﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void PhysicsScene2D_ToString_mACA22EF575F7544176360B16D431F2BB1DEBB307 (void);
extern void PhysicsScene2D_op_Equality_mF7A3D7BF3F4E48D862EE1981941BD580C6AB741A (void);
extern void PhysicsScene2D_op_Inequality_m87F2AA2476AE980B057FE5A31E2432A673107C17 (void);
extern void PhysicsScene2D_GetHashCode_mD45B3437D088C66A35AE20066AD632D1D0858B1E (void);
extern void PhysicsScene2D_Equals_m4A19DE0675BD596A1B5AC0F7138A9A6F4D6029B3 (void);
extern void PhysicsScene2D_Equals_mA7C243A71CFDBFA905F057CE3E9C5E61B34216FB (void);
extern void PhysicsScene2D_IsValid_m3C273A4AC45E6E9994DCBC66D11E003200FAF50C (void);
extern void PhysicsScene2D_IsValid_Internal_m50BDBEA5D97EE63E1E1732BD753DAD9C1B625D90 (void);
extern void PhysicsScene2D_IsEmpty_m62DBEF907153F3158A43768639668054F8B82465 (void);
extern void PhysicsScene2D_IsEmpty_Internal_m8ACC80B5923701D097E862ECCD5DEB68DBF4B078 (void);
extern void PhysicsScene2D_Simulate_m2210AE79B5D4713DA5BEFD4EB5857778651DCE62 (void);
extern void PhysicsScene2D_Linecast_mF2BCE1D6E939D9E3B0289FE40FC6F8618706B33D (void);
extern void PhysicsScene2D_Linecast_mAAC2C53143BDB79CCE8666C2E1432FB4E75FD915 (void);
extern void PhysicsScene2D_Linecast_Internal_m89344966CB23C79B1A9819FCB5F5B98CD4F3C304 (void);
extern void PhysicsScene2D_Linecast_m5DEB3199C07DB52BB9E1C890D58A53726DBD24C9 (void);
extern void PhysicsScene2D_Linecast_mEEB64412838171C7AA92974E8E923645C2AED7DD (void);
extern void PhysicsScene2D_LinecastArray_Internal_m57E2A7ABE8671BDF15C8120123BB948E41E117CA (void);
extern void PhysicsScene2D_Linecast_m68E48116A78255DCADA115102C4093A6B79612C3 (void);
extern void PhysicsScene2D_LinecastNonAllocList_Internal_m4A8361A19EB72590EB7B1E40223C3A1536C82239 (void);
extern void PhysicsScene2D_Raycast_m5A2D66F6E7E8F34B6CF5B82099EFA4F69155F25D (void);
extern void PhysicsScene2D_Raycast_m74A71D9DBCC2CCD7454240AE784CEE5720E55EA0 (void);
extern void PhysicsScene2D_Raycast_Internal_m39487CD184C451A0932E85CF899B768B0AF04424 (void);
extern void PhysicsScene2D_Raycast_mDA9E6E04FC3117D6819BD757347A886AAF6024CD (void);
extern void PhysicsScene2D_Raycast_m004884696543F60917C1ED72374C1528207229C3 (void);
extern void PhysicsScene2D_RaycastArray_Internal_m78B0093DCC7AD643AE641D65D9A616FBAAF93D08 (void);
extern void PhysicsScene2D_Raycast_m541841D244633BA234ED72B01204161686D6B3B9 (void);
extern void PhysicsScene2D_RaycastList_Internal_m8F8D0331D48A0A5F19238FB3FE48DF1007C078DD (void);
extern void PhysicsScene2D_CircleCast_m**************************************** (void);
extern void PhysicsScene2D_CircleCast_m**************************************** (void);
extern void PhysicsScene2D_CircleCast_Internal_m**************************************** (void);
extern void PhysicsScene2D_CircleCast_m**************************************** (void);
extern void PhysicsScene2D_CircleCast_m**************************************** (void);
extern void PhysicsScene2D_CircleCastArray_Internal_m**************************************** (void);
extern void PhysicsScene2D_CircleCast_m**************************************** (void);
extern void PhysicsScene2D_CircleCastList_Internal_m**************************************** (void);
extern void PhysicsScene2D_BoxCast_m87BBB9AE3A51D80D32CF4351D1BCB24D230C547F (void);
extern void PhysicsScene2D_BoxCast_m2BE649FF276A4DA721F359D60991E20B6EC1390E (void);
extern void PhysicsScene2D_BoxCast_Internal_m14C241D05B6FC564CE812B144AC09EEC47AAF62D (void);
extern void PhysicsScene2D_BoxCast_m303C253EFABCC5FFA582706B5263CFF703BAE31A (void);
extern void PhysicsScene2D_BoxCast_m5959F7A39896649972E94A16FD852470321077A9 (void);
extern void PhysicsScene2D_BoxCastArray_Internal_m876C291873AC8A96BE306E5EC8152C6696654AC6 (void);
extern void PhysicsScene2D_BoxCast_mB3593FE8701D4482F74886089D69087CA38515CA (void);
extern void PhysicsScene2D_BoxCastList_Internal_m21A492B93014AFCCA33EE98F1475C2BF1EE90D56 (void);
extern void PhysicsScene2D_CapsuleCast_m63BFCD6264B727CFFBDE288E3BAF56395A9BB3C1 (void);
extern void PhysicsScene2D_CapsuleCast_m06AE8E8E4E64B35464B27F5AE236B0D81D3E72A1 (void);
extern void PhysicsScene2D_CapsuleCast_Internal_mEF949F75F6FAFEBD4535E9CB0DB56E614F7C2B65 (void);
extern void PhysicsScene2D_CapsuleCast_mFF5DE18E5B463E9A9435A4F73BDA4CCD87D63E49 (void);
extern void PhysicsScene2D_CapsuleCast_mF141441734E1357C94F1B51A3065DCD8BB8A5F99 (void);
extern void PhysicsScene2D_CapsuleCastArray_Internal_m0BBB183B4404914052E1B952020CABA6C94C3886 (void);
extern void PhysicsScene2D_CapsuleCast_m0D02AFE5BB9D55F18864E158CE3FC6792F180E9A (void);
extern void PhysicsScene2D_CapsuleCastList_Internal_mBBE9E328C138E6DFEB4CE0A1D33FFE3F3E3FEFBE (void);
extern void PhysicsScene2D_GetRayIntersection_m92BF0BF919D8BB704EC93D45EE7E2DB2EB176943 (void);
extern void PhysicsScene2D_GetRayIntersection_Internal_m8F523A4E443EC8F78C38CADEB6A2721EA45980F1 (void);
extern void PhysicsScene2D_GetRayIntersection_mF3E0EC0D4F5A4B8C063E735979C851ED5B4B4C2E (void);
extern void PhysicsScene2D_GetRayIntersectionArray_Internal_m5E84231D490515C4B9D39733E3CB11EDDF979B20 (void);
extern void PhysicsScene2D_GetRayIntersectionList_Internal_m8B4CE07AC10BC1B25C32DF3BC55B52D582513340 (void);
extern void PhysicsScene2D_OverlapPoint_mBD3BD6137CC642F63968D6058E75467C1098BBCE (void);
extern void PhysicsScene2D_OverlapPoint_mC2ED0510F29807B334351227C118F74CBC278227 (void);
extern void PhysicsScene2D_OverlapPoint_Internal_m266CFB424DEC17E370C78E519F65DF60E01C256E (void);
extern void PhysicsScene2D_OverlapPoint_m3BEA82B608B1338872A28CE4A7A8BCB4A45A7F07 (void);
extern void PhysicsScene2D_OverlapPoint_m19614587E2F3CD617B899513DDA8299F455960A1 (void);
extern void PhysicsScene2D_OverlapPointArray_Internal_m6A106F4AC8D3B42A6604FA6C3C7A6A540094D486 (void);
extern void PhysicsScene2D_OverlapPoint_m3C062202164C34E05357D6D53864132CB4DF3325 (void);
extern void PhysicsScene2D_OverlapPointList_Internal_mAF5F43FF546546BA7BDEDC14E964D8B79AD6F067 (void);
extern void PhysicsScene2D_OverlapCircle_m**************************************** (void);
extern void PhysicsScene2D_OverlapCircle_m**************************************** (void);
extern void PhysicsScene2D_OverlapCircle_Internal_m**************************************** (void);
extern void PhysicsScene2D_OverlapCircle_m**************************************** (void);
extern void PhysicsScene2D_OverlapCircle_m**************************************** (void);
extern void PhysicsScene2D_OverlapCircleArray_Internal_m**************************************** (void);
extern void PhysicsScene2D_OverlapCircle_m**************************************** (void);
extern void PhysicsScene2D_OverlapCircleList_Internal_m**************************************** (void);
extern void PhysicsScene2D_OverlapBox_m92B0E7E2244148FB38CB5EC0782C55E4CE5D41C6 (void);
extern void PhysicsScene2D_OverlapBox_m335088F6FBADF523759D88B56B4CE8B351BACEBF (void);
extern void PhysicsScene2D_OverlapBox_Internal_m03820B585C51BD0FA5CA4ECC4AA7786986767A46 (void);
extern void PhysicsScene2D_OverlapBox_m822F0FBB0CB890B8B3DCF1B513EDD45686325D58 (void);
extern void PhysicsScene2D_OverlapBox_mE4BBC0516865E1BFB5CC23E85FCC6985EE1F1673 (void);
extern void PhysicsScene2D_OverlapBoxArray_Internal_m35D9309858C2006A0E3B453780F7201064FDBCFE (void);
extern void PhysicsScene2D_OverlapBox_m87AB321755E4631C49A58FD12E85A6046F78A20B (void);
extern void PhysicsScene2D_OverlapBoxList_Internal_m077144CB6A138D9A1B2F6AABBD58C0BA46B35CF4 (void);
extern void PhysicsScene2D_OverlapArea_mFD58E3BBEEE4357EB0BBA9A96EBB58012E7270A0 (void);
extern void PhysicsScene2D_OverlapArea_m4D8FAFD9E7A299823649C9043C70456FF63F0655 (void);
extern void PhysicsScene2D_OverlapAreaToBoxArray_Internal_m330E73DB6C450B36501A02801C5C77A4BD1F1B19 (void);
extern void PhysicsScene2D_OverlapArea_m00070511C0F182CFDAC6BC57A057ADDCC671B1F3 (void);
extern void PhysicsScene2D_OverlapArea_mD7B8AA443BB67D29957C8F0AA14DD4AC99F93F2B (void);
extern void PhysicsScene2D_OverlapAreaToBoxArray_Internal_m81756D2895F9044EA86F0B21A47A16FB3F2AD592 (void);
extern void PhysicsScene2D_OverlapArea_m1DBB709EB60C3BCBF2064C98E247360DCE93C1FB (void);
extern void PhysicsScene2D_OverlapAreaToBoxList_Internal_m9D3425E6BC3D06B023D05F38ED4F50E1E178459E (void);
extern void PhysicsScene2D_OverlapCapsule_mB9EED3130F064B14CB4895858D6A25B7FE9CAF47 (void);
extern void PhysicsScene2D_OverlapCapsule_mB521741943F713EB037454DA5EB61CF4E405B364 (void);
extern void PhysicsScene2D_OverlapCapsule_Internal_m7DA985D5CD4C14C797DFD7ABCAEDC7745E7E628C (void);
extern void PhysicsScene2D_OverlapCapsule_m98A5052A0721B060EF518A167268F1384F36565B (void);
extern void PhysicsScene2D_OverlapCapsule_m950A00AE483884975510112A38DDAC9ED30358EA (void);
extern void PhysicsScene2D_OverlapCapsuleArray_Internal_mA9CF4CE10F2783774909BD5AC4C7DBD111EB9719 (void);
extern void PhysicsScene2D_OverlapCapsule_mD621AE5E0F3095B5E7A9B6662DD4D55B1C0FAA31 (void);
extern void PhysicsScene2D_OverlapCapsuleList_Internal_mD941BC678A00DCF5E31D14460BA93DEB6679EA43 (void);
extern void PhysicsScene2D_OverlapCollider_mC87E2704140649F1E70F1E9318BB035ADF3D2EE5 (void);
extern void PhysicsScene2D_OverlapCollider_m0B119DC3BC74F23E89EA78CFB55C000F0C2CDAF8 (void);
extern void PhysicsScene2D_OverlapColliderArray_Internal_mC132864949D60EA73C432B0BABCFA408AA95B7FB (void);
extern void PhysicsScene2D_OverlapCollider_m99B77F1452FB414901F59C8821CC762E7026B145 (void);
extern void PhysicsScene2D_OverlapColliderList_Internal_mE0D02729C317959A00C350783548689556EBB1FB (void);
extern void PhysicsScene2D_IsValid_Internal_Injected_mAE8FB599C34DF82B10243CCF39338DB1D15D8E68 (void);
extern void PhysicsScene2D_IsEmpty_Internal_Injected_m1C6A83C6A9A2C291DEEB7FA20AF50268A1CDC53C (void);
extern void PhysicsScene2D_Linecast_Internal_Injected_mEC7F06BBE9C0F88ACFC6C14A39C0CF2BD0E26B31 (void);
extern void PhysicsScene2D_LinecastArray_Internal_Injected_mBB18F5F30F62B826F6E36B67F07F04AA33B88BC2 (void);
extern void PhysicsScene2D_LinecastNonAllocList_Internal_Injected_mC97B61467AAA4071B245080436C25AAA85DBFB2B (void);
extern void PhysicsScene2D_Raycast_Internal_Injected_mFDE2EC874A7E78DF64283FD6396B8C3F9B69659E (void);
extern void PhysicsScene2D_RaycastArray_Internal_Injected_m554BD34B09F598A0B0F264957AB06ADD1285582F (void);
extern void PhysicsScene2D_RaycastList_Internal_Injected_m6B018025FABC15C19B02E005C87B5228C4D8A917 (void);
extern void PhysicsScene2D_CircleCast_Internal_Injected_m**************************************** (void);
extern void PhysicsScene2D_CircleCastArray_Internal_Injected_m**************************************** (void);
extern void PhysicsScene2D_CircleCastList_Internal_Injected_m**************************************** (void);
extern void PhysicsScene2D_BoxCast_Internal_Injected_mD5EC44BC06AD72A7F28A0D893C12587AA915C66C (void);
extern void PhysicsScene2D_BoxCastArray_Internal_Injected_m0A82E92F906E96AD1CA2BDA40EF88529F22DB55D (void);
extern void PhysicsScene2D_BoxCastList_Internal_Injected_mC7F5B6CDAFBB5544F2E72EDC439A0E9BDF7A1255 (void);
extern void PhysicsScene2D_CapsuleCast_Internal_Injected_m1D49EDE7754153AC8DBEA696246EF90D923819FC (void);
extern void PhysicsScene2D_CapsuleCastArray_Internal_Injected_mE0AAE4E084F585E6FD9390C8FC5C4D1F2A4DABED (void);
extern void PhysicsScene2D_CapsuleCastList_Internal_Injected_m5F273A0C0940A7449B80DEA8C4125AB489F464D4 (void);
extern void PhysicsScene2D_GetRayIntersection_Internal_Injected_mFFE9F51858E5DECF785638E8B28BFF941D8D6329 (void);
extern void PhysicsScene2D_GetRayIntersectionArray_Internal_Injected_mAC45B2CE0A6FC10A72166EFEA0D04151C2C579A2 (void);
extern void PhysicsScene2D_GetRayIntersectionList_Internal_Injected_mA97723484F986E9309B873BBE94578AFC7EFB35C (void);
extern void PhysicsScene2D_OverlapPoint_Internal_Injected_mB9BD7F80DF31309E93592E8E269A19C0AE49D5E4 (void);
extern void PhysicsScene2D_OverlapPointArray_Internal_Injected_m33139CEC7C63B21E6E59CF5236147ADE777B36F7 (void);
extern void PhysicsScene2D_OverlapPointList_Internal_Injected_m95F0DABB5312FF43DDB08D0CA0A7DF7CE28BEF4B (void);
extern void PhysicsScene2D_OverlapCircle_Internal_Injected_m**************************************** (void);
extern void PhysicsScene2D_OverlapCircleArray_Internal_Injected_m**************************************** (void);
extern void PhysicsScene2D_OverlapCircleList_Internal_Injected_m**************************************** (void);
extern void PhysicsScene2D_OverlapBox_Internal_Injected_mD8A62BB2F0705ECC9C0A9681C61A0CDF51DFAB6D (void);
extern void PhysicsScene2D_OverlapBoxArray_Internal_Injected_m3F11205B29560348C8C51381BFA99EA231B34253 (void);
extern void PhysicsScene2D_OverlapBoxList_Internal_Injected_mB5B02C0FAB82B54BEA4E1177E6D2DD149B64A46A (void);
extern void PhysicsScene2D_OverlapCapsule_Internal_Injected_m262C55DC8E5C007DE8BA4CC650DDE0A4BD01B132 (void);
extern void PhysicsScene2D_OverlapCapsuleArray_Internal_Injected_m9BB43F807179B143FE80C8E5CE1EA6842007CC6E (void);
extern void PhysicsScene2D_OverlapCapsuleList_Internal_Injected_mD0A0C3D0F9C7754CFD627DAFF8A313C82C81C4A4 (void);
extern void PhysicsScene2D_OverlapColliderArray_Internal_Injected_m976AEFD47D42223D2BA7E51449E64019B3B7E512 (void);
extern void PhysicsScene2D_OverlapColliderList_Internal_Injected_m52D29A43675AB3CE412D5C19F5CEF23DCDDCAC93 (void);
extern void PhysicsSceneExtensions2D_GetPhysicsScene2D_m71554BCD0560589D6D33391A74CA2E7B22FE913D (void);
extern void PhysicsSceneExtensions2D_GetPhysicsScene_Internal_m1BD5268413273AF3C855D2510C6829E64AD0A65A (void);
extern void PhysicsSceneExtensions2D_GetPhysicsScene_Internal_Injected_mA01F7804FC031AAC9E242C4F21B93F6CBEC6F569 (void);
extern void Physics2D_get_defaultPhysicsScene_m688935D2C81F56703A1F0E3C8712604A42CB2804 (void);
extern void Physics2D_get_velocityIterations_m0DBAC36A23AAC57B98DE40B8344C2D0A48F9063A (void);
extern void Physics2D_set_velocityIterations_mC23055A566AE6C78DE1A35D9936A9782DAE6A3C6 (void);
extern void Physics2D_get_positionIterations_mE0736556D30AC5E0D77DFEE6687BC9A1B5F4EAC1 (void);
extern void Physics2D_set_positionIterations_mA31F95FC57C52A9B6FF770077B3D26D8D5306011 (void);
extern void Physics2D_get_gravity_mDF7A391FC0C21253EB763436B3CD612CC52A59BE (void);
extern void Physics2D_set_gravity_m1BBB6F368E12983D5978054A41A0ECA5579CD620 (void);
extern void Physics2D_get_queriesHitTriggers_m2652ECB55DB31ADA6E1BD62DFB9792860A7B5FE8 (void);
extern void Physics2D_set_queriesHitTriggers_mE288FA67C553C015CF4D5E2DA6C435F2F9ED9F4E (void);
extern void Physics2D_get_queriesStartInColliders_mECA14AD1A3E14F64AFDA4ABB5C83A54872DDF09E (void);
extern void Physics2D_set_queriesStartInColliders_m71EDD323AF2BA23D40914047B8C272D5480667F4 (void);
extern void Physics2D_get_callbacksOnDisable_m08B5CB44B5E54D74AE7B0A6A50CD8A8FA188B3D7 (void);
extern void Physics2D_set_callbacksOnDisable_m2CA8941C90A6CF849ADF9D75BDD61B2F1478CD91 (void);
extern void Physics2D_get_reuseCollisionCallbacks_m40CC62BF72BF4209B9D12EBC57D99445E25416FC (void);
extern void Physics2D_set_reuseCollisionCallbacks_m5C6A1526FD02CDA3A4E7B043E6D71BBA6F317E4C (void);
extern void Physics2D_get_autoSyncTransforms_mE856967A9F58E7000D162549616789A401763BD6 (void);
extern void Physics2D_set_autoSyncTransforms_mC437FCA611E64BB043B1491A88DAAF53FD8FA88B (void);
extern void Physics2D_get_simulationMode_m0AC8A54856FA71E5C7EF3EA9EBE6BCAD5E5F769C (void);
extern void Physics2D_set_simulationMode_mD9C9CC25B0DE231C766DF5153C483B689F6EA8E6 (void);
extern void Physics2D_get_jobOptions_mD32D093B3E3E90AC5E9BE1952AA33497F75C57D0 (void);
extern void Physics2D_set_jobOptions_mD93E79D72A0FE2DC8942C1DAB33883811CDF47AD (void);
extern void Physics2D_get_velocityThreshold_mE0461DC1C6A79BA7E9C39F667A1BBB5C87B7E5BB (void);
extern void Physics2D_set_velocityThreshold_mEB1B56B1F09F921C5055B8A3FE2693491E38A6E5 (void);
extern void Physics2D_get_maxLinearCorrection_mB2799A7F1268585461433C83C2A4651466E6502A (void);
extern void Physics2D_set_maxLinearCorrection_mA5C33DB6B77E2F524C3D14DB5A0139DBF0E60438 (void);
extern void Physics2D_get_maxAngularCorrection_m4CB26CE23F5EB1984644E6D9BE0C628FC635E8FE (void);
extern void Physics2D_set_maxAngularCorrection_m0866A481CA14424DFCEB2C3DA649C1442498E6C2 (void);
extern void Physics2D_get_maxTranslationSpeed_m689DFA51413C049FDAA7A164AEFA9A36B1AD0B14 (void);
extern void Physics2D_set_maxTranslationSpeed_mE4E03BF9C68FD6CA8EBD49635D15EB6D757DC52A (void);
extern void Physics2D_get_maxRotationSpeed_m048E62223C543630FD8323F056B96D49E780BA7C (void);
extern void Physics2D_set_maxRotationSpeed_mF0313C41EF5435DAC1396DAB7FE65468284C499C (void);
extern void Physics2D_get_defaultContactOffset_m559AF335B2376428A33F6C906EAA92CE60F75FED (void);
extern void Physics2D_set_defaultContactOffset_m3E50E7936947179342211F83B856A297DD2A8B4C (void);
extern void Physics2D_get_baumgarteScale_m1FD6E6AF2C4B0D0C19CFEEB0C25923BB5EAF0A3B (void);
extern void Physics2D_set_baumgarteScale_m616E4309A00189E8808797E3D9A5D848C5CF7730 (void);
extern void Physics2D_get_baumgarteTOIScale_mE99D516EB998776F53498C7871FFC987A456BB49 (void);
extern void Physics2D_set_baumgarteTOIScale_mDAFAC60A5EB7E9363D6AD9F4DBAA23DFFEB8DB12 (void);
extern void Physics2D_get_timeToSleep_m94A3FF08BB18525CA524B193760DF22F70C3CCB5 (void);
extern void Physics2D_set_timeToSleep_m69B326351764C17EC969653495413D24558A1A34 (void);
extern void Physics2D_get_linearSleepTolerance_m3432708A180F03F8EA215A77CA94604E02EE01BA (void);
extern void Physics2D_set_linearSleepTolerance_m9343E4E0837A04D69D894823B4B133160901DD33 (void);
extern void Physics2D_get_angularSleepTolerance_m965EEE3B419C350D62866C001CBEC10B07595CE2 (void);
extern void Physics2D_set_angularSleepTolerance_mE1FC13314F95D5443AB615758DAED38E4717E1ED (void);
extern void Physics2D_Simulate_m1DAEEF3777BA7B2F63E5CC230B44B9D2B71793A8 (void);
extern void Physics2D_Simulate_Internal_m82630D89CBA33438D0123D0AB47996035435EBA4 (void);
extern void Physics2D_SyncTransforms_mF936634793253A203EF6632454731E44509256D8 (void);
extern void Physics2D_IgnoreCollision_mFE023CDD902A5068236266648DAB8E9FD8EE387F (void);
extern void Physics2D_IgnoreCollision_mA05835421D23AACF69E76082124F4F983B9E2BDD (void);
extern void Physics2D_GetIgnoreCollision_m30747D1CA595AF08842285CE07CE5940CDC3A1F0 (void);
extern void Physics2D_IgnoreLayerCollision_mCA0530AA3A008D092F23E6954CD97447191EB6CD (void);
extern void Physics2D_IgnoreLayerCollision_mCCDA76292A31204335084A5BCD87B0BCAB117500 (void);
extern void Physics2D_IgnoreLayerCollision_Internal_m2749D17AA72083A27379C7F0493F51FD103EE12C (void);
extern void Physics2D_GetIgnoreLayerCollision_m0F138C898A8ABF58D86DE9B3B192E1BCD17A6D95 (void);
extern void Physics2D_GetIgnoreLayerCollision_Internal_m4E2C29E3869F52DD60C1CB3B6B9FC04734F2CFAF (void);
extern void Physics2D_SetLayerCollisionMask_mD54B799499738E8C48B439E863D98196DC338ABD (void);
extern void Physics2D_SetLayerCollisionMask_Internal_m42B77EAF051667C2CC62D0562E90A154FC251A20 (void);
extern void Physics2D_GetLayerCollisionMask_mAFE4ED4289B7845D7922AC8FBA4A68AD3518B504 (void);
extern void Physics2D_GetLayerCollisionMask_Internal_m45BEE83C0CF1C70E780F4B794B4C488C51820049 (void);
extern void Physics2D_IsTouching_mA4729AD62E4452BD16770E41CCE17CF9E7E1B53F (void);
extern void Physics2D_IsTouching_m664FE711127076D22DDAFF3682AE14104EADB84D (void);
extern void Physics2D_IsTouching_TwoCollidersWithFilter_mA71FAA16798C8063C022907D45F4BD7CBDBCA465 (void);
extern void Physics2D_IsTouching_m2A5DF0BA1C6F3B1446F9E1479754B57FCC39C005 (void);
extern void Physics2D_IsTouching_SingleColliderWithFilter_m9DBB106FBE15A9F6BC167ACF2F96F02B65F08484 (void);
extern void Physics2D_IsTouchingLayers_m5038718C989DEEAD1233F0AE9661657D0782AC90 (void);
extern void Physics2D_IsTouchingLayers_mFA5DE2043BBC865BA5A8B0DCF3E780C7C36DF51B (void);
extern void Physics2D_Distance_mA0261660F0B40F9A6C721E3FE212AF35AA5AC5C5 (void);
extern void Physics2D_Distance_Internal_m88733C0CEEAEC293168910C54F587CBB554C5652 (void);
extern void Physics2D_ClosestPoint_m8BF5092BAC57633EAE27294541B511C572F84970 (void);
extern void Physics2D_ClosestPoint_m8A374A5EFFC9F9D21CB7B37427199FD8F22B82D1 (void);
extern void Physics2D_ClosestPoint_Collider_mE2FD8744A1402685CFC793D6A88DFEFFE5FAC77B (void);
extern void Physics2D_ClosestPoint_Rigidbody_mB92515591D0C50A1F153AEBCFB3EEE3A7E9F4976 (void);
extern void Physics2D_Linecast_mA5E8A23A679483445FFF3E44D1D7CCAB2E40B207 (void);
extern void Physics2D_Linecast_m9812FA76F939EF9A8DA87B6C27F2C91C751533B3 (void);
extern void Physics2D_Linecast_m319E2AFFF606C8B96831853B7A9FFA4D001F1B07 (void);
extern void Physics2D_Linecast_m3D8375EDEBA4C03A5453642276B4D0435C6DD492 (void);
extern void Physics2D_Linecast_m02BC41305491AC0B252045B4EB6B8126321B77B4 (void);
extern void Physics2D_Linecast_m7E560BE72AD0AAB3163CB5E2CB32D0F513F9F3EA (void);
extern void Physics2D_LinecastAll_mF21599922C6E613E2BFAB46BD154532872031D77 (void);
extern void Physics2D_LinecastAll_mE2D3E20FC1F01416E5BCCEA7F7A3337C89C4548B (void);
extern void Physics2D_LinecastAll_m766BC2927DB5A58DA5501B482E3C10F10DCBD8D6 (void);
extern void Physics2D_LinecastAll_mCF8DCCB1666C15750FD126E4B47E570FD1586861 (void);
extern void Physics2D_LinecastAll_Internal_mAA71200FC94A40A5479C62713946053F4EB2DB6A (void);
extern void Physics2D_LinecastNonAlloc_m167932B702ECF563FE545AD33B7AD65BD2703523 (void);
extern void Physics2D_LinecastNonAlloc_m8D36A38624CAE318F45B089A70D57B3473D957C1 (void);
extern void Physics2D_LinecastNonAlloc_mB5FFAA42F1888263A7439D8DAC329C954EAB8C21 (void);
extern void Physics2D_LinecastNonAlloc_m4A80485AAC646575CD8A6D89C8DBD4F92AA06CA0 (void);
extern void Physics2D_Raycast_mBECD869F0788D0B0E665BBA3611362E6D5CD2947 (void);
extern void Physics2D_Raycast_m758FB450001D6EA88A3C51FA2E93D98988B7F630 (void);
extern void Physics2D_Raycast_m5BF2A59AAEE0B488FB3ECD1D3AF3537FD7789E7F (void);
extern void Physics2D_Raycast_mBB6A989AFAFE4B32BEC77543F3121C4ED38A1710 (void);
extern void Physics2D_Raycast_m03D33CAF9BCCAE7DC2863E263FB8CEFAD87D5E27 (void);
extern void Physics2D_Raycast_m56E5CBDA49BD64A3A775F4850F18F66A07D1085B (void);
extern void Physics2D_Raycast_m65A222170C18F173E06309A784D736E5C6EB32D6 (void);
extern void Physics2D_Raycast_mB49B869989A276E025785C0FB53443551B3C8B17 (void);
extern void Physics2D_RaycastNonAlloc_mBF9F41881E335AAEEB6F712BE2213E261DD6683A (void);
extern void Physics2D_RaycastNonAlloc_m4AA81066CF537047CC101AF544EA4D5EDDE09357 (void);
extern void Physics2D_RaycastNonAlloc_mA080DB8D4E20A5454F34F4EFE103898178453DFB (void);
extern void Physics2D_RaycastNonAlloc_m71DBE02A3555080CC7F1CB53B4DD198C92B5E10B (void);
extern void Physics2D_RaycastNonAlloc_m8A8688A5F6704404E1F168AFB0613CACBEF13264 (void);
extern void Physics2D_RaycastAll_mA3C18E34CE2F30B92DC336174962C021D437F6C1 (void);
extern void Physics2D_RaycastAll_m7C461F55BBEF18894404B12851E6C1646A5837A8 (void);
extern void Physics2D_RaycastAll_m06B06279D4E05F05198F57F4E35DEED4A5CF37E6 (void);
extern void Physics2D_RaycastAll_mF3F78F9C0D00A0A6C065D59BFEF00125A4282061 (void);
extern void Physics2D_RaycastAll_mBDA7D58DCA9982DA2C2C0D130C86AF41A8FC42F5 (void);
extern void Physics2D_RaycastAll_Internal_m784DCB1C06F42FD24B62917ADB7DBFD5B108F82F (void);
extern void Physics2D_CircleCast_m**************************************** (void);
extern void Physics2D_CircleCast_m**************************************** (void);
extern void Physics2D_CircleCast_m**************************************** (void);
extern void Physics2D_CircleCast_m**************************************** (void);
extern void Physics2D_CircleCast_m**************************************** (void);
extern void Physics2D_CircleCast_m**************************************** (void);
extern void Physics2D_CircleCast_m**************************************** (void);
extern void Physics2D_CircleCast_m**************************************** (void);
extern void Physics2D_CircleCastAll_m**************************************** (void);
extern void Physics2D_CircleCastAll_m**************************************** (void);
extern void Physics2D_CircleCastAll_m**************************************** (void);
extern void Physics2D_CircleCastAll_m**************************************** (void);
extern void Physics2D_CircleCastAll_m**************************************** (void);
extern void Physics2D_CircleCastAll_Internal_m**************************************** (void);
extern void Physics2D_CircleCastNonAlloc_m**************************************** (void);
extern void Physics2D_CircleCastNonAlloc_m**************************************** (void);
extern void Physics2D_CircleCastNonAlloc_m**************************************** (void);
extern void Physics2D_CircleCastNonAlloc_m**************************************** (void);
extern void Physics2D_CircleCastNonAlloc_m**************************************** (void);
extern void Physics2D_BoxCast_mFF053711F19B6D267D681DCC4D566C19895AAC2A (void);
extern void Physics2D_BoxCast_mCAF4A9373C2C4DBFC1D5AE82E8A386F3EA846DD7 (void);
extern void Physics2D_BoxCast_mC7CAEAB9C10CE3C4EDEAC492568382EC03B17E92 (void);
extern void Physics2D_BoxCast_m718BCE5F1787711D626C24A2BF0B2131D7231283 (void);
extern void Physics2D_BoxCast_mB0A00171A19055FA17E132FCB701242057DAF77E (void);
extern void Physics2D_BoxCast_m98CE5227E43693832B864B0FEF488074F15EB3D2 (void);
extern void Physics2D_BoxCast_m2B8A2BB3C95A0D2713C4BC194E6BDC188F1A94F2 (void);
extern void Physics2D_BoxCast_mB2D3953BD1F7C7A2E70FFF6AA88A73CAD2F4C9D5 (void);
extern void Physics2D_BoxCastAll_m19E98B67C5C914A2268285648735E9768FEA89E4 (void);
extern void Physics2D_BoxCastAll_mAD5C6041843578921B3BC72D3DDD6786AE3CE39B (void);
extern void Physics2D_BoxCastAll_m233581366644868226FED3D087A6384E2F940CD2 (void);
extern void Physics2D_BoxCastAll_m255158E41AE1A490780844CBFDE5DD581A3B35B8 (void);
extern void Physics2D_BoxCastAll_m0189B37197E856FC6FE9F1EB385308AFF15455BB (void);
extern void Physics2D_BoxCastAll_Internal_mDDA9D066D4DB710D00C1384A959526E6FEEAC253 (void);
extern void Physics2D_BoxCastNonAlloc_m9118D3D07CA9044A98E53FF6A94602666C269409 (void);
extern void Physics2D_BoxCastNonAlloc_mA01B4DCBC7F877BD2DA5590243DFC579E1BED9DB (void);
extern void Physics2D_BoxCastNonAlloc_mA5B3A8436F2B07F18C026090C6DC3B57BF1B6D1D (void);
extern void Physics2D_BoxCastNonAlloc_mACB3729249096165C4E8AA31C2017678DFF0FF07 (void);
extern void Physics2D_BoxCastNonAlloc_mDBD3A56B7CA41F552E3CD8FD53EA46C73D4AC9CF (void);
extern void Physics2D_CapsuleCast_m40CCD83C1D8401344A74ACB045BF621A75E00F5E (void);
extern void Physics2D_CapsuleCast_m53E848EB0B4648F516AAFD24F271B394807B71F6 (void);
extern void Physics2D_CapsuleCast_m4EA377ADA6FB4E6BA3B8509DD78C00762B2D2FFB (void);
extern void Physics2D_CapsuleCast_m2A1BCB8678F6CBD7F3A3C5B3D26C308BC61AE62D (void);
extern void Physics2D_CapsuleCast_mD7FB26D9F6F98B638B70119B3E2C64FC2243A765 (void);
extern void Physics2D_CapsuleCast_mAC4D4ECDACDDAFBE6AE15CDC69D056A2FF84487F (void);
extern void Physics2D_CapsuleCast_m658DB6DD45C901FB1FB3056DE4AAE1F7CBAABB99 (void);
extern void Physics2D_CapsuleCast_m46826AC10598A4ACF34EE72B81DEB5D30B98E6E3 (void);
extern void Physics2D_CapsuleCastAll_mE8B151D8BF0ABB897D4AAFFE75170C1CA3B87EDB (void);
extern void Physics2D_CapsuleCastAll_m69E9D32B6EF809660F76F26F9AE537A710E921AD (void);
extern void Physics2D_CapsuleCastAll_Internal_m96529ACEF33B04023052163C82C9B419244F830D (void);
extern void Physics2D_CapsuleCastAll_m3A1ED2A6CE679A240FE85778F6DCECE3D16A9C44 (void);
extern void Physics2D_CapsuleCastAll_m0C52E18D6E29D019E72838A59A8FD4998B50117C (void);
extern void Physics2D_CapsuleCastAll_m91C99656898E4DB2FDD91175DF46153769B5ABDD (void);
extern void Physics2D_CapsuleCastNonAlloc_mC6E8B9CC75C13CD99496100ECA65CA061BC18EF2 (void);
extern void Physics2D_CapsuleCastNonAlloc_mD3359A29468AA60CAF5CA245D2A70AF76268E2C3 (void);
extern void Physics2D_CapsuleCastNonAlloc_m1CD299E07810C0FA6E30370A784B7962934613A6 (void);
extern void Physics2D_CapsuleCastNonAlloc_mA27E94A2A1599AB7319A78BD24433336EB5A159F (void);
extern void Physics2D_CapsuleCastNonAlloc_mCAF127FCCF26E9D62809C0B4106F63A60FC52D89 (void);
extern void Physics2D_GetRayIntersection_m14FE964631FC806EC3C7D6EBCA461315961492C4 (void);
extern void Physics2D_GetRayIntersection_m31BD5D61E0472511E9A93CA4613A0BBB6328D8F5 (void);
extern void Physics2D_GetRayIntersection_m9246D0A6EE267271AC1AD49928BDDF7FB6FB76E8 (void);
extern void Physics2D_GetRayIntersectionAll_mE44882D00E63761758A1C10D8579F5AD5A027C14 (void);
extern void Physics2D_GetRayIntersectionAll_m71F010CB1DF9881A6AEC32123FFD7BFDE32A59EA (void);
extern void Physics2D_GetRayIntersectionAll_m1584C9C6ABD1AAEB6235830DC16D05C4566EB80D (void);
extern void Physics2D_GetRayIntersectionAll_Internal_m98C9407CC390AA4F560D4BAFE3D35FE06DD3400C (void);
extern void Physics2D_GetRayIntersectionNonAlloc_mC17430C3F478EAB0F15D96D10F25CE5E42579A5C (void);
extern void Physics2D_GetRayIntersectionNonAlloc_m77BF66F763DE34E2BA96789FC7AC5C2797037228 (void);
extern void Physics2D_GetRayIntersectionNonAlloc_mB7942B73C8B86F369262FC3B87F080132E7A369C (void);
extern void Physics2D_OverlapPoint_mA63A2BD632AACFB5C5F1DF950D8F1A3F268B023D (void);
extern void Physics2D_OverlapPoint_mCCA9CD3A57E00F9673E1B93B16A3CEB0D46147D1 (void);
extern void Physics2D_OverlapPoint_mCEB41A7E99B39E756C8B1AA87E530872F04BF0E4 (void);
extern void Physics2D_OverlapPoint_mEDB00DAC341DA164AC1F8A39F8B0CD2F18F447C7 (void);
extern void Physics2D_OverlapPoint_m7DE14703086A7AD5F061A5E915CF0047532C23D7 (void);
extern void Physics2D_OverlapPoint_m89EA7884794307B5D66A1211397E2AB7DB13478A (void);
extern void Physics2D_OverlapPointAll_mB5B72FB2FC0B6EDC86429F1D577DC3AC2BC2249C (void);
extern void Physics2D_OverlapPointAll_mCA182087E2277E6796549FB73291D52365ED917B (void);
extern void Physics2D_OverlapPointAll_m429BB7AEF1D48AA66C6839618B1B8577A960EADB (void);
extern void Physics2D_OverlapPointAll_m9A6E2368C993844FD70E86D120FE52713427B7C8 (void);
extern void Physics2D_OverlapPointAll_Internal_m6F8CBFEEDEDC77F01667C3EFF1738E1DA5320FBE (void);
extern void Physics2D_OverlapPointNonAlloc_m7C642222932C98F44C99419FED236181B807040C (void);
extern void Physics2D_OverlapPointNonAlloc_m39B60A0A527550466575AFD0CF7B1149DCB849FB (void);
extern void Physics2D_OverlapPointNonAlloc_m518ADBCAC4203558497F28CB046111EC002078BC (void);
extern void Physics2D_OverlapPointNonAlloc_m8F88E8CE4FC130F37102880632741EC86D863D3D (void);
extern void Physics2D_OverlapCircle_m**************************************** (void);
extern void Physics2D_OverlapCircle_m**************************************** (void);
extern void Physics2D_OverlapCircle_m**************************************** (void);
extern void Physics2D_OverlapCircle_m**************************************** (void);
extern void Physics2D_OverlapCircle_m**************************************** (void);
extern void Physics2D_OverlapCircle_m**************************************** (void);
extern void Physics2D_OverlapCircleAll_m**************************************** (void);
extern void Physics2D_OverlapCircleAll_m**************************************** (void);
extern void Physics2D_OverlapCircleAll_m**************************************** (void);
extern void Physics2D_OverlapCircleAll_m**************************************** (void);
extern void Physics2D_OverlapCircleAll_Internal_m**************************************** (void);
extern void Physics2D_OverlapCircleNonAlloc_m**************************************** (void);
extern void Physics2D_OverlapCircleNonAlloc_m**************************************** (void);
extern void Physics2D_OverlapCircleNonAlloc_m**************************************** (void);
extern void Physics2D_OverlapCircleNonAlloc_m**************************************** (void);
extern void Physics2D_OverlapBox_m474622F2441EA274E257DA3066691831D59BF3B4 (void);
extern void Physics2D_OverlapBox_mF4F43BA71C46C138C460ACFA8EB479DE73A5E4A7 (void);
extern void Physics2D_OverlapBox_mAAB6CD150472EA9C82A11EC76A6CA8D47AC16F37 (void);
extern void Physics2D_OverlapBox_m65C687F8DAFAA971FC8339137DF9EB06FC9C51B9 (void);
extern void Physics2D_OverlapBox_m31F327A72F057ABBE6086524C31E958E94A04612 (void);
extern void Physics2D_OverlapBox_mFAA9F46A660117E853BE92A40B31AC48D009A5C1 (void);
extern void Physics2D_OverlapBoxAll_mBE79BB1EA100B859488D14DE01EA77E68DBD434D (void);
extern void Physics2D_OverlapBoxAll_mF62C6ED3122825ECA752E05B1E5993A478FB4C3D (void);
extern void Physics2D_OverlapBoxAll_mD1DE6E108996B1C3AFDA73C970DB9EC40D1820FF (void);
extern void Physics2D_OverlapBoxAll_m429463A5A1E1782352997776E587344E1BFC92A5 (void);
extern void Physics2D_OverlapBoxAll_Internal_mF51992B7CCD72EF309445E0F221BFE318A8F05CC (void);
extern void Physics2D_OverlapBoxNonAlloc_m25D8546A388FE55FA0BDA8E2FF5F1372173B3B2F (void);
extern void Physics2D_OverlapBoxNonAlloc_m97F2CE20703167B8259C4F3A81F3531842748C9A (void);
extern void Physics2D_OverlapBoxNonAlloc_m8C840AB888C9B4BD26C34200EC6F53003A605ABF (void);
extern void Physics2D_OverlapBoxNonAlloc_m2655E21E15E61CD2EF6634C3CCDBC291810ABF89 (void);
extern void Physics2D_OverlapArea_mF07C31CAE51943CD56FF6E5BF15C5D393CF95328 (void);
extern void Physics2D_OverlapArea_m034DDE7D6BA13FBCB10A16DF67EFE20D03BDA777 (void);
extern void Physics2D_OverlapArea_mD036DDDA70E0C950E197852A137F7A6F9AF6D1AA (void);
extern void Physics2D_OverlapArea_m935EC644C187656307D1A6B1A32C764271AD7402 (void);
extern void Physics2D_OverlapArea_mABBFB9CBC8A7520E1266213D84C42166219FD98B (void);
extern void Physics2D_OverlapArea_m6A41E0B588F7E93C3EC0EFA0D636C22F5BF4F399 (void);
extern void Physics2D_OverlapAreaAll_m1D2FA40228FF1939EF996CBC8994892D9E929CDF (void);
extern void Physics2D_OverlapAreaAll_m9FB58BC96BAAD2EBF0FE587CD7EEB17590CBCCFD (void);
extern void Physics2D_OverlapAreaAll_mC02FD248A920265B031EEC7BBC3738BF383E2BED (void);
extern void Physics2D_OverlapAreaAll_mB3020CD320BC666CB8BBE6A0C788F40BB86B36A2 (void);
extern void Physics2D_OverlapAreaAllToBox_Internal_m4C49DE7CC580FFD9ED580A59587A406DB521C79F (void);
extern void Physics2D_OverlapAreaNonAlloc_mF21DE46F2BBEC159063217E1EDE3D8A13FBAD31B (void);
extern void Physics2D_OverlapAreaNonAlloc_mB284FE5776D69957D309DBF794AE892082E08B11 (void);
extern void Physics2D_OverlapAreaNonAlloc_mA0E00FA95378B98A34B5F487BDC23A6651D64A80 (void);
extern void Physics2D_OverlapAreaNonAlloc_mCB0B793D41CEFC260D0938494D2517F615916B30 (void);
extern void Physics2D_OverlapCapsule_m2394A5E5C40D85F26A3133AF81D3F9BD3E4B40FB (void);
extern void Physics2D_OverlapCapsule_m6E149086FD94CB6905896124DCA380A1F3C98E96 (void);
extern void Physics2D_OverlapCapsule_m15829D75998836AC524BC62E4505D4F2AC3BF1CB (void);
extern void Physics2D_OverlapCapsule_mEB399B39BBCC721E62FE86C8FD497EF848C7D3B5 (void);
extern void Physics2D_OverlapCapsule_mE71B76CDE08A6FF5A2C509C61B67FE90AD8C2AB8 (void);
extern void Physics2D_OverlapCapsule_mB5F1C97565B77DA9ECF6557E3C7E6C9B4EF1C788 (void);
extern void Physics2D_OverlapCapsuleAll_m45455FD00AC8AC585755C3E58BEBE8C777291A2E (void);
extern void Physics2D_OverlapCapsuleAll_mE92E2E79B1291ED0FD646AAE4AF899D5CB6458C6 (void);
extern void Physics2D_OverlapCapsuleAll_m61B1DB059607624936D23CDD59A158B20B4828B1 (void);
extern void Physics2D_OverlapCapsuleAll_m1AFC997F280375813086E41C03ECB9FA9E494504 (void);
extern void Physics2D_OverlapCapsuleAll_Internal_m4623153689BB29553BB982B062E53FAD9126139D (void);
extern void Physics2D_OverlapCapsuleNonAlloc_m1722967A255A89F5DA62742470198B0A2B934AE7 (void);
extern void Physics2D_OverlapCapsuleNonAlloc_mC1363E3F30356B01114CA74EB85FFA9FF57D60A2 (void);
extern void Physics2D_OverlapCapsuleNonAlloc_mD4136A633CE3349C79F29EC959573346E2237003 (void);
extern void Physics2D_OverlapCapsuleNonAlloc_m93FF0F86CBC10231DB9A45DEFDA1B708F21AFA6B (void);
extern void Physics2D_OverlapCollider_mA9A14648005436C84C1821E950F463978858C7B2 (void);
extern void Physics2D_OverlapCollider_m4BEEEB54D16532DA655C11956D2841BF7E3421AC (void);
extern void Physics2D_GetContacts_mC28E1C5334B88FA5BE7C8B27A2E26E80C8E63F52 (void);
extern void Physics2D_GetContacts_m2D814C5FD2160C4E2A3809183570776E668D275A (void);
extern void Physics2D_GetContacts_m96D00E493F40C12BDB168B35A654F1E49710B5B2 (void);
extern void Physics2D_GetContacts_m00D61EC0E43AAF0C5F80E9E1F21F711D30851316 (void);
extern void Physics2D_GetContacts_mE77325CF26F36079539C0BA68F7938A26B722902 (void);
extern void Physics2D_GetContacts_mE80C6FC68BC4FE23A1196A351AA71923EA52CA6D (void);
extern void Physics2D_GetContacts_mCC273E69CBC88D69BB3B669B6671A66995FF4B0F (void);
extern void Physics2D_GetContacts_mB92E218B5A4E2DA48FCE1E61AC0D6D4C500ABFEA (void);
extern void Physics2D_GetContacts_mC7A1E5C26E431B6A483D4DEF31ACDD173E7B86F7 (void);
extern void Physics2D_GetColliderContactsArray_m4894B8F334AE9C31C90415A820AF18E8C43C4096 (void);
extern void Physics2D_GetColliderColliderContactsArray_m90A3D3C4E41F45DABAFCF0156C67B4F3ED094CAB (void);
extern void Physics2D_GetRigidbodyContactsArray_m9707D359D2487188FEF996214020A4D4DDFFAF68 (void);
extern void Physics2D_GetColliderContactsCollidersOnlyArray_m488172E725F99C2C6151514799C721166600DA28 (void);
extern void Physics2D_GetRigidbodyContactsCollidersOnlyArray_mD6F4BA1D7C82BEFB0CAAAE32C41D3718BE3FB63F (void);
extern void Physics2D_GetContacts_mB21ECEECC22EA9146DF2D4C3024979E42783AE5B (void);
extern void Physics2D_GetContacts_m6BCCBA4FA3F55DDBA5F106EC56DA9CC80A73ED4E (void);
extern void Physics2D_GetContacts_mD20884841609334E50BFAEFA1040D835066909F3 (void);
extern void Physics2D_GetContacts_m854C1FF9470AC8BA7A956022921BF1B346370F48 (void);
extern void Physics2D_GetContacts_m47063EDFD77367A3E6B4FE6FC467C9B882954D4B (void);
extern void Physics2D_GetContacts_m3E764DB49BFD901D34E638010D55E7048595445C (void);
extern void Physics2D_GetContacts_m66385E3A1AA7FB4C5EA0E3579F70328340E4D9BD (void);
extern void Physics2D_GetContacts_mA95305B79AAFBB9EE9DCEAD67A8D06115B2523BC (void);
extern void Physics2D_GetContacts_m89ADB0CEABE85E8847EF36C5BB84F13FA2551C91 (void);
extern void Physics2D_GetColliderContactsList_mE5F6778AF9A6123893EF1CA4EF48FAB44E4FB413 (void);
extern void Physics2D_GetColliderColliderContactsList_m01EB37B797D150CCC61A82158B02249BFA65978C (void);
extern void Physics2D_GetRigidbodyContactsList_m70C689D385F9CF99EB0AFC317D318C6953C951C8 (void);
extern void Physics2D_GetColliderContactsCollidersOnlyList_m38C8049BCC72A6EA96EE505DF7DDF58F3F3E14A1 (void);
extern void Physics2D_GetRigidbodyContactsCollidersOnlyList_m67DD0E78B0906C48016AA48BA9230C9D4ECD5CCF (void);
extern void Physics2D_SetEditorDragMovement_m7B8EB685E7830AB9721C31D74A711CC48B5A7A22 (void);
extern void Physics2D_get_raycastsHitTriggers_m56BA522C9120E23322BE89DE20DBA325E12C6CB4 (void);
extern void Physics2D_set_raycastsHitTriggers_mDF85DDCDB251F10A2EF7DEFB070618FACDEA66B4 (void);
extern void Physics2D_get_raycastsStartInColliders_mB7D1D6FB74A7648B884F4E65738FC578ADC7F6DB (void);
extern void Physics2D_set_raycastsStartInColliders_m1F6EB7AF3426F9A6A6618002D76A7BCD0B72E0B9 (void);
extern void Physics2D_get_deleteStopsCallbacks_m49F1DD7065027A26D2F2DBD73CCC1D5B4C75182D (void);
extern void Physics2D_set_deleteStopsCallbacks_m3CB360D093CB4DB105E4716F0A6B89E4A2BE32E5 (void);
extern void Physics2D_get_changeStopsCallbacks_mDA399A3FBBA1F9F766A12EFAB4E0935A161F706C (void);
extern void Physics2D_set_changeStopsCallbacks_mFD72443014BF9B3993C5B7E578F97948E2086BEB (void);
extern void Physics2D_get_minPenetrationForPenalty_mB3F7F0B059508F78E0DDA2AAB63C6D2105F0E396 (void);
extern void Physics2D_set_minPenetrationForPenalty_mA27EC4966871C676181B654D4CB8051A01E61841 (void);
extern void Physics2D_get_autoSimulation_m4ABB50F5A390E486E94D90FD63EC7233DC6FDA82 (void);
extern void Physics2D_set_autoSimulation_mD141D9115EE951A2C26BBDBF435A9027489C9A38 (void);
extern void Physics2D_get_colliderAwakeColor_m715372199F8E0B7F973CC40D9B37677717BAE318 (void);
extern void Physics2D_set_colliderAwakeColor_m2713A7A8672BB861CCD48DF4EC875822250FB0F7 (void);
extern void Physics2D_get_colliderAsleepColor_m59D094D33BD3097B1B3850F3ECB1FC176D5445FC (void);
extern void Physics2D_set_colliderAsleepColor_mEA547865DED2389BE4528471D781C55B9C0DB8BD (void);
extern void Physics2D_get_colliderContactColor_mFA2E931E0542A0EED863EE5114860F95396F3975 (void);
extern void Physics2D_set_colliderContactColor_m04A364BC0392FF2E356B801EBE90A3E5A7D851DF (void);
extern void Physics2D_get_colliderAABBColor_mB19972F3384073A8166A7BEFEA07E049868550A4 (void);
extern void Physics2D_set_colliderAABBColor_m69BEE817232DABBC5734A6E6144E6F28AF84A69F (void);
extern void Physics2D_get_contactArrowScale_mE9B35D01157FAB0C49687861F974DE1D1D3DEEF9 (void);
extern void Physics2D_set_contactArrowScale_mBC30967682451703774E41801222B3A1BDD9CA7A (void);
extern void Physics2D_get_alwaysShowColliders_mB4C5BAFF6B257A8E6A4028F84F22A285E2553451 (void);
extern void Physics2D_set_alwaysShowColliders_m5EF9CA97CBB3198C422BB202862C4262E4DB6E7A (void);
extern void Physics2D_get_showCollidersFilled_mC6C9DC4B6E6BDFB448C40C4B2940DB34344C9254 (void);
extern void Physics2D_set_showCollidersFilled_mF725908801E37C5695863FA89B2E3E9632502D92 (void);
extern void Physics2D_get_showColliderSleep_m67A835AD227455DCB3C2CE334E4FDD115E8AE6AB (void);
extern void Physics2D_set_showColliderSleep_m349A0BEA05993D9C016F29D315127ED2A5B56387 (void);
extern void Physics2D_get_showColliderContacts_mA16C0F429F639E791B907C3BB70ABEE56AB889EC (void);
extern void Physics2D_set_showColliderContacts_m047A8AB0FCC4EEB45E8705CA4B440D870C243E03 (void);
extern void Physics2D_get_showColliderAABB_mDFAEFC85A5019DA4A2F60727E4DF369975CA700A (void);
extern void Physics2D_set_showColliderAABB_mEFE6CF5CC5E99EB11B0C7A300980DFD21CE94DA0 (void);
extern void Physics2D__ctor_m326F06BF43B7C08A452B13A6387B32E0029DC59B (void);
extern void Physics2D__cctor_m7B7A8EEEE744CE27534A7ADF12F31A4E376544E8 (void);
extern void Physics2D_get_gravity_Injected_mC06A234085280AC3F4EFB7272A1F7E6AF32D83BC (void);
extern void Physics2D_set_gravity_Injected_mF2B40E2CBAAE94177E8F6957ACDC13438E771CFA (void);
extern void Physics2D_get_jobOptions_Injected_m8B233F4735F412EF41672FC07918C9E7E95F5035 (void);
extern void Physics2D_set_jobOptions_Injected_mE55E5DE977E172058CA9EC0B85D84DE4B7E437EB (void);
extern void Physics2D_Simulate_Internal_Injected_mD608121626C6B72256029D3A0963FD752A33E1C7 (void);
extern void Physics2D_IsTouching_TwoCollidersWithFilter_Injected_m08E621C249D1F097084B8623DEC4817710D0B96A (void);
extern void Physics2D_IsTouching_SingleColliderWithFilter_Injected_mBCD1C50AF15F47AEC1C106F6D768ACCF7703670C (void);
extern void Physics2D_Distance_Internal_Injected_m5449AC9FD4E93CB6A402D77138CFBDFA646543EA (void);
extern void Physics2D_ClosestPoint_Collider_Injected_m181F9BEFE025A075E604504319A267A1765E91FB (void);
extern void Physics2D_ClosestPoint_Rigidbody_Injected_m1FC6618B4620DA09FCC0CD069A7626E241623987 (void);
extern void Physics2D_LinecastAll_Internal_Injected_mE6A9EFD2C20707F308CD36D1322E8D1298F41452 (void);
extern void Physics2D_RaycastAll_Internal_Injected_m5F20D58815E964575E7A0B36C54B735B0CFA7311 (void);
extern void Physics2D_CircleCastAll_Internal_Injected_m**************************************** (void);
extern void Physics2D_BoxCastAll_Internal_Injected_m8AF2ACB63B37E176B3F238FFF8C26B13D2D2D053 (void);
extern void Physics2D_CapsuleCastAll_Internal_Injected_m7EE5B9B4F312D94871622E8C5DB664DFA8A3C917 (void);
extern void Physics2D_GetRayIntersectionAll_Internal_Injected_mB7BCACD0A0B90CCD4CD4041764BD19B95D553E2B (void);
extern void Physics2D_OverlapPointAll_Internal_Injected_m48F78E5620E3B443935527EAFB362DB6E335F311 (void);
extern void Physics2D_OverlapCircleAll_Internal_Injected_m**************************************** (void);
extern void Physics2D_OverlapBoxAll_Internal_Injected_m96F44850F41131D44FC98FCC01519679CBFFFC1D (void);
extern void Physics2D_OverlapCapsuleAll_Internal_Injected_m27C748FD129A31651276D0BE2F2EB712A33DDFE8 (void);
extern void Physics2D_GetColliderContactsArray_Injected_m8586CD4E52A4B4240F61C205EA21043764EB9C35 (void);
extern void Physics2D_GetColliderColliderContactsArray_Injected_m48D0291EAB3C30118FAD5CF3AB5E5080FAE4B9BE (void);
extern void Physics2D_GetRigidbodyContactsArray_Injected_mE733D6DB0550EEE6C30F4F247890A99A250D3CA6 (void);
extern void Physics2D_GetColliderContactsCollidersOnlyArray_Injected_mFFD7CD1234F631CB63E04F9CEC06F9B3FEECDAC0 (void);
extern void Physics2D_GetRigidbodyContactsCollidersOnlyArray_Injected_mDF4F1FB68646571D136002565F3442CB98E64ECE (void);
extern void Physics2D_GetColliderContactsList_Injected_m5D0FBB67A27D3BDEB48CC4A24B6CC5B43F800497 (void);
extern void Physics2D_GetColliderColliderContactsList_Injected_m7BE022C3A68676CCE5C2B61E4C39EBD056357825 (void);
extern void Physics2D_GetRigidbodyContactsList_Injected_mB9EA4EE75ACE9C8C326A38E467389D0BC7ED1DD9 (void);
extern void Physics2D_GetColliderContactsCollidersOnlyList_Injected_m7A4092C37BA2EA3F26C65FF517302F0AA06974FA (void);
extern void Physics2D_GetRigidbodyContactsCollidersOnlyList_Injected_m876A84E6515A77A68CDC21E6F6B264444DDB0A1E (void);
extern void PhysicsShape2D_get_shapeType_mBA55638729252854535E3E9279F7A006B610B01F (void);
extern void PhysicsShape2D_set_shapeType_m8A00238C6DFD5ABF398700C86492296DB9DC6D9B (void);
extern void PhysicsShape2D_get_radius_m4300D92A9F9523277976419369F0A14DC75AEF8B (void);
extern void PhysicsShape2D_set_radius_mF96363A2D7B8112F2994D38A1113C8FC441FF864 (void);
extern void PhysicsShape2D_get_vertexStartIndex_m23782C197FC0521A6DC3A4E0F115DB477042EC40 (void);
extern void PhysicsShape2D_set_vertexStartIndex_m289E3881B77BEBA99E28B698AEEBFFC0F4A5DD00 (void);
extern void PhysicsShape2D_get_vertexCount_mBB09936295C475647B8E92C1464F4C2F3CA7A8D2 (void);
extern void PhysicsShape2D_set_vertexCount_m982167474864E44AA5DC6ED6CFD854E757B6702A (void);
extern void PhysicsShape2D_get_useAdjacentStart_mF9EE25B1A542E807AA8AFB722F79D7CE0937271B (void);
extern void PhysicsShape2D_set_useAdjacentStart_mF419418F7F43E9F30BA926E373730A3D4DE68D4E (void);
extern void PhysicsShape2D_get_useAdjacentEnd_mE04E765CF666B95B6C49E34A38ED2F056B9E4CEA (void);
extern void PhysicsShape2D_set_useAdjacentEnd_mDC730349B1B72E2B095BC5A2858BC34C2EF18D2B (void);
extern void PhysicsShape2D_get_adjacentStart_mC99F496D28E98CD8D59425D4815275530414F94E (void);
extern void PhysicsShape2D_set_adjacentStart_m6EDB2AD4B54FD968C0E6E3C783045FA121B5D081 (void);
extern void PhysicsShape2D_get_adjacentEnd_m8C5EA386C240C0A123C12C849892922D8D1F11DA (void);
extern void PhysicsShape2D_set_adjacentEnd_m6646CBEFCA64FF1FA84B2E01B6C18AD6F351CEC1 (void);
extern void PhysicsShapeGroup2D_get_groupVertices_mECFB54F79371D17E73D1B49F38140C0D604177C8 (void);
extern void PhysicsShapeGroup2D_get_groupShapes_mD42F5717D5EEE161C0B9A75335FA0B06D57DDA9F (void);
extern void PhysicsShapeGroup2D_get_shapeCount_m238BE168C9D3579FEFAD12AA54EA212ABC1D0279 (void);
extern void PhysicsShapeGroup2D_get_vertexCount_mC63C729E4B4DFE1C3E6F0233792A4DD71DB1EEEE (void);
extern void PhysicsShapeGroup2D_get_localToWorldMatrix_mC66CC5F526DA0099C161B8EF715B92F214BA837D (void);
extern void PhysicsShapeGroup2D_set_localToWorldMatrix_mACC64B52039118923499B33AA6626518DE88211B (void);
extern void PhysicsShapeGroup2D__ctor_mDD9B2EE22BB914D23767D8BDEF5517ACA05A7ED8 (void);
extern void PhysicsShapeGroup2D_Clear_mD762D71D19D0D0166E69214F1E291566C6F8B3F7 (void);
extern void PhysicsShapeGroup2D_Add_mCC29A1C9F197B06990FB247B9395EC14B4153F31 (void);
extern void PhysicsShapeGroup2D_GetShapeData_mAC27C534537278D2CDD957396AF28016D3D11B17 (void);
extern void PhysicsShapeGroup2D_GetShapeData_m1D6FB486CBDEFFAFF8B9744F9918C12F659589CA (void);
extern void PhysicsShapeGroup2D_GetShapeVertices_mB9897694633E9F85AFBFA7E50C195071682D7491 (void);
extern void PhysicsShapeGroup2D_GetShapeVertex_mDF62F80AF6E5638C9A80CC9838A4D96DC28EF29B (void);
extern void PhysicsShapeGroup2D_SetShapeVertex_m03F4350C37785C0EBAE6550D8408643B66B75FBF (void);
extern void PhysicsShapeGroup2D_SetShapeRadius_mC4439610949D4B0BCB2BBB5EA95A7C48EDE4053B (void);
extern void PhysicsShapeGroup2D_SetShapeAdjacentVertices_m76B2A13301956C154FBFA36B3D7CAAB557FCD266 (void);
extern void PhysicsShapeGroup2D_DeleteShape_mB1ABBA70CEB414E665A3EF5482D548B09C6EA968 (void);
extern void PhysicsShapeGroup2D_GetShape_m4B5E6DB4EC57A2FCACA24D750B08CDA81B4C06AC (void);
extern void PhysicsShapeGroup2D_AddCircle_m**************************************** (void);
extern void PhysicsShapeGroup2D_AddCapsule_m6F1AE5239142A78E8919F0EA2EE9D8B5D851721A (void);
extern void PhysicsShapeGroup2D_AddBox_mC3C8A7197164A468BD5CE7FF1051CB80FB075D6F (void);
extern void PhysicsShapeGroup2D_AddPolygon_mB6FFD23403215D884BB0B8C71A7E0A55E5D8BCDB (void);
extern void PhysicsShapeGroup2D_AddEdges_m296E86FB14123762B2ECAD3074486BC3174E3A68 (void);
extern void PhysicsShapeGroup2D_AddEdges_mB9C431A763B4F67E63CDE229D9B52D6FA13A41F1 (void);
extern void PhysicsShapeGroup2D_U3CAddBoxU3Eg__RotateU7C28_0_mE6CE94A5D2B98F0673D32F74457E3B963143D502 (void);
extern void GroupState_ClearGeometry_m4A1E29A3DFE71E4426BD2F02D7067C8E32BEBE8F (void);
extern void ColliderDistance2D_get_pointA_m55571F64432B8539AF715F91AE9F01E039B1BD3B (void);
extern void ColliderDistance2D_set_pointA_m4334AB6925BF3CA93F3993E260CE986702C0AC8B (void);
extern void ColliderDistance2D_get_pointB_m8C42A60CF6DFD3257CA1EF994BAE5DE2956F732A (void);
extern void ColliderDistance2D_set_pointB_mF53E3F9EA595B627B64344411163212631DF42D0 (void);
extern void ColliderDistance2D_get_normal_m751A946B91F2A85A5F37376D4C4BC6BD42B437ED (void);
extern void ColliderDistance2D_get_distance_m6754976414E63C5F8B9493FDECA2210F5339F3C7 (void);
extern void ColliderDistance2D_set_distance_m1CFF30A40C19AE4F4ACB11B0B157B1FDE8DD56E4 (void);
extern void ColliderDistance2D_get_isOverlapped_mEFF81A407A9EF37A3800E1050B9D9C789DF3F0D3 (void);
extern void ColliderDistance2D_get_isValid_m044D48E09BD8ADC4A01708B1D9478E43098EDF93 (void);
extern void ColliderDistance2D_set_isValid_m356B0AEAF9421D48DD98304D8460893FE44C82DB (void);
extern void ContactFilter2D_NoFilter_m15980FED0667A151078679DF555638BEEA7C01DE (void);
extern void ContactFilter2D_CheckConsistency_mD918F11F977EA35E87CF491F7AE8794F5D01DF72 (void);
extern void ContactFilter2D_ClearLayerMask_m86FA33B78DAAC6E04A4ED62F73CDD3D34B0DF68A (void);
extern void ContactFilter2D_SetLayerMask_mC3FBC2D806C1A3ACB2D060CE48F8157505E42F9B (void);
extern void ContactFilter2D_ClearDepth_m6CBAEF48B84E53079CC628E186EC6DD940C3151E (void);
extern void ContactFilter2D_SetDepth_mE614DDDDAEA489D150E61D2DF8104F9292236F18 (void);
extern void ContactFilter2D_ClearNormalAngle_m7C7A157AF9CF67AF3D1094770B6B910BFEDC23D3 (void);
extern void ContactFilter2D_SetNormalAngle_m9A737032D43865398C15881FF1764694D0601B33 (void);
extern void ContactFilter2D_get_isFiltering_m993424DE9FBBA01394167C8FC546E4526C564A74 (void);
extern void ContactFilter2D_IsFilteringTrigger_m48921B2B2B5254FE3033757321224C14E7AB9884 (void);
extern void ContactFilter2D_IsFilteringLayerMask_m353947849DAF100E9125B0D92349B4763C76139A (void);
extern void ContactFilter2D_IsFilteringDepth_m455CB30F384EFEB68951CFEA42F26D2F15BC8886 (void);
extern void ContactFilter2D_IsFilteringNormalAngle_m67548CED7AB9D9029B88C405A23F67307E95EA8F (void);
extern void ContactFilter2D_IsFilteringNormalAngle_mCBCAD097FC2B202E4C54A8A2EC13883EEE652C64 (void);
extern void ContactFilter2D_IsFilteringNormalAngleUsingAngle_m30C52106865DBBAD20B012FD86699324E7BA2CC6 (void);
extern void ContactFilter2D_CreateLegacyFilter_m7DF755B13D055FA510CB7F57A2CA5B45EAD161E2 (void);
extern void ContactFilter2D_CheckConsistency_Injected_mE330C47C2583E5057F24B9E7B2D8F4B63B2B7A7C (void);
extern void ContactFilter2D_IsFilteringNormalAngle_Injected_m01CFD4929268D12F47285658298A1B470E985732 (void);
extern void ContactFilter2D_IsFilteringNormalAngleUsingAngle_Injected_mFFAFE8C92A8A6FE67AED76023A5859B2F8D657B5 (void);
extern void Collision2D_GetContacts_Internal_m2DEB43D363533FD6E88C889F7C3B914636EB2643 (void);
extern void Collision2D_get_collider_m90FA98F6619E9F1E2EFAE8132EDB6ECA1A2C4F37 (void);
extern void Collision2D_get_otherCollider_mE83E8BC80234672284EB1934AE9A70BDD52A9C4B (void);
extern void Collision2D_get_rigidbody_mD763F2D56BF538A94AD62379D22E42335A67B60D (void);
extern void Collision2D_get_otherRigidbody_mD6A52043DACEC341356C618452FA48B95786CABE (void);
extern void Collision2D_get_transform_mC59737F246B2DAFF2AB4F6322664C87B28605CC7 (void);
extern void Collision2D_get_gameObject_mE4B3D56F3477F7D2D6D7B217DF5488DA1D13204C (void);
extern void Collision2D_get_relativeVelocity_m1F0BB90BC73FB0A0EA27212D832BB3F26D4C004A (void);
extern void Collision2D_get_enabled_mBA3D8BA274E40F606E8356C64A78896B6D2D77B6 (void);
extern void Collision2D_get_contacts_mA4A1EDCC2D82407E30EC63689C7858C684462E68 (void);
extern void Collision2D_get_contactCount_m7656255B45A9D6E37F4EE4258CB33C9F6C6FE231 (void);
extern void Collision2D_GetContact_mB48B0E46D9183FAE6635D3C7BDB09F11ED1B2C95 (void);
extern void Collision2D_GetContacts_m7E7B9C9FDCEB63196B8A10EFF2536272EC69758D (void);
extern void Collision2D_GetContacts_mD93B838E59707D5AD96A18CDCE0A6468AB068E23 (void);
extern void Collision2D__ctor_mC61096E6215EB1B820F55F1163D92CBBCF78FEA4 (void);
extern void ContactPoint2D_get_point_mFF9B7395F63E748507C85166F3EDC218B8740396 (void);
extern void ContactPoint2D_get_normal_m421147AFFC1A029B4DEC775C6B9197919ED93D21 (void);
extern void ContactPoint2D_get_separation_m70174AAA4EC5B1857607115B25BED77BA142EA5E (void);
extern void ContactPoint2D_get_normalImpulse_m601808AD6F4E1F81E4C9E53401556C465D161288 (void);
extern void ContactPoint2D_get_tangentImpulse_mC45ADFB72CA45EE4C430598511DB6534AF3F5CB4 (void);
extern void ContactPoint2D_get_relativeVelocity_m0DAD8E66E82BE6A3133618EFCB2CA579FD0F5D7D (void);
extern void ContactPoint2D_get_collider_mCEC4BBE3C9CF0977C3EC5D529C2D5B664180768F (void);
extern void ContactPoint2D_get_otherCollider_m1892E5E5AA0032610E8252FC371654E4198A7779 (void);
extern void ContactPoint2D_get_rigidbody_m28CDDD12EB3F7C06D05126C1ECA3AEA9594E1FF3 (void);
extern void ContactPoint2D_get_otherRigidbody_mAE4893B039030B7AF7B645D2EEA0BD0F142CE6D9 (void);
extern void ContactPoint2D_get_enabled_m1AC0022C616EBDD9012C25A6C9FD21766E87686C (void);
extern void JointAngleLimits2D_get_min_mABF1DD59D9AF0093C3A6C3EA039B2CE7B3F0AC1A (void);
extern void JointAngleLimits2D_set_min_m9EBCBBF3B7D4126158DB2405A2C2333DFFE48B29 (void);
extern void JointAngleLimits2D_get_max_m2673C23C93D2802B61332C9BA58BD64E3A76BCC8 (void);
extern void JointAngleLimits2D_set_max_m8264B45D23F709BA60BEF19645569D105A623D6F (void);
extern void JointTranslationLimits2D_get_min_m76573A6341DBDA6DA716A910851A19DBA7C6F773 (void);
extern void JointTranslationLimits2D_set_min_m68279D0A2371102C82CC0511E4A8AEE9D697EDD4 (void);
extern void JointTranslationLimits2D_get_max_mA2360BA3CD1A35E2E6C297D1EE478FD60757A699 (void);
extern void JointTranslationLimits2D_set_max_m29EC807D57EC6702250B59CA07A207971E6D164B (void);
extern void JointMotor2D_get_motorSpeed_mDD7B3F1E134AB8367191EAFEA683B82AC4952163 (void);
extern void JointMotor2D_set_motorSpeed_m080F930D6EC3A5BE6348C409B9115664E464B480 (void);
extern void JointMotor2D_get_maxMotorTorque_m911E081510303C1B7763686891ACFCEF6748C6EB (void);
extern void JointMotor2D_set_maxMotorTorque_mFB41FE9052B411B7C43C29DE4978E04AD9C748AC (void);
extern void JointSuspension2D_get_dampingRatio_m436AF1D3DE8C46C1F548E08AF83A0C0C546CFD25 (void);
extern void JointSuspension2D_set_dampingRatio_m6E706B4991620D9F8E54014779A788DF55A45DD4 (void);
extern void JointSuspension2D_get_frequency_m62ACA94DE93973E8874544AF88BFC5C56AE7F0F3 (void);
extern void JointSuspension2D_set_frequency_mE8B115A66AD8FADFC3C035075A3662C0C16E89B6 (void);
extern void JointSuspension2D_get_angle_mF95F89C72EFF15D5CC37CFFD93FAF8BD8437C0E1 (void);
extern void JointSuspension2D_set_angle_m27413F565F50EEF07F617ED60539527175B73BB5 (void);
extern void RaycastHit2D_get_centroid_mEA7A6ACCFE6C0E7566B0C177A54A750EC554C704 (void);
extern void RaycastHit2D_set_centroid_mFB5F56330BAA9C8CE547AFE75648FD8E426776D8 (void);
extern void RaycastHit2D_get_point_mB35E988E9E04328EFE926228A18334326721A36B (void);
extern void RaycastHit2D_set_point_m13D917ABD5F8FEB291FED13D2DB2A2481E085FAC (void);
extern void RaycastHit2D_get_normal_m75F1EBDE347BACEB5A6A6AA72543C740806AB5F2 (void);
extern void RaycastHit2D_set_normal_m36B4A488824FD1E4D0F47AE13211C4D773FE6799 (void);
extern void RaycastHit2D_get_distance_mD0FE1482E2768CF587AFB65488459697EAB64613 (void);
extern void RaycastHit2D_set_distance_m652B2B50F02583A4FAD019190AEFA1D402B0FA33 (void);
extern void RaycastHit2D_get_fraction_m9BF416582F5C4D5FC8D93E5DA57B4CDC64E030BE (void);
extern void RaycastHit2D_set_fraction_m3FE49691CA64CB6EB030AE87C4FAEA28285C1062 (void);
extern void RaycastHit2D_get_collider_mB56DFCD16B708852EEBDBB490BC8665DBF7487FD (void);
extern void RaycastHit2D_get_rigidbody_mA7C26ACF22912C14CC6C8B1D7C50F38BCF5096B8 (void);
extern void RaycastHit2D_get_transform_mA5E3F8DC9914E79D3C9F6F3F2515B49EEBB4564A (void);
extern void RaycastHit2D_op_Implicit_mBEF99A746116664D68B1398D58CA247550980A11 (void);
extern void RaycastHit2D_CompareTo_mF4665ADD9F8B212987443C052ADE73E8C0DF2612 (void);
extern void PhysicsJobOptions2D_get_useMultithreading_m8967EDBBDC51011BE1C0FB7FC437643F8E0E8DFA (void);
extern void PhysicsJobOptions2D_set_useMultithreading_mF475BF5CDDBBEA27D50F2B60E00CC9BCAFA016A9 (void);
extern void PhysicsJobOptions2D_get_useConsistencySorting_m583424FC6BACAB1E6161F3BDEBAE102978630CDA (void);
extern void PhysicsJobOptions2D_set_useConsistencySorting_m4190351CA61E03766E72435E437DCB23CE1408CC (void);
extern void PhysicsJobOptions2D_get_interpolationPosesPerJob_m7F3C3C05C4AC1770E342EA6D6F63B709A610DFB8 (void);
extern void PhysicsJobOptions2D_set_interpolationPosesPerJob_mA764F435978E7BD6005F1B560516B7B9AE2A123E (void);
extern void PhysicsJobOptions2D_get_newContactsPerJob_mCA477A50F149B5DADC5E2A772E48B746638DC761 (void);
extern void PhysicsJobOptions2D_set_newContactsPerJob_mA2C104942792A9188ADE151D5221FF18F3EF052B (void);
extern void PhysicsJobOptions2D_get_collideContactsPerJob_mC27C3A2243ED724C4D147C210595716AAECDC217 (void);
extern void PhysicsJobOptions2D_set_collideContactsPerJob_m199A8D06195B95564503429B109C7E32B3E42138 (void);
extern void PhysicsJobOptions2D_get_clearFlagsPerJob_m0F1299FC15FDAE5A030772D1D1A01E9FA522AC94 (void);
extern void PhysicsJobOptions2D_set_clearFlagsPerJob_mD198700A6FEA3696DC7A8F54E043A337B18D97B3 (void);
extern void PhysicsJobOptions2D_get_clearBodyForcesPerJob_mCA5DC9F62EC9BCB44D9AD7C50F5D72DEA79CB560 (void);
extern void PhysicsJobOptions2D_set_clearBodyForcesPerJob_m85773E07D803A633C58928F66588ED04FB8EE78F (void);
extern void PhysicsJobOptions2D_get_syncDiscreteFixturesPerJob_m44F03D367416DB932C675A23B30BA8CE81DED94E (void);
extern void PhysicsJobOptions2D_set_syncDiscreteFixturesPerJob_m59988BD56ADE10BF7D55B1E3BB6A505EE5290DEB (void);
extern void PhysicsJobOptions2D_get_syncContinuousFixturesPerJob_m3AA97386BBD0EE4BE60232DA0940D1C0B00F6549 (void);
extern void PhysicsJobOptions2D_set_syncContinuousFixturesPerJob_m8F99FB132D69D00C86FFA1788CF8B084E929DD65 (void);
extern void PhysicsJobOptions2D_get_findNearestContactsPerJob_m989A3ACBF42B19E2EA50E2414A8EF3A876B1C7D9 (void);
extern void PhysicsJobOptions2D_set_findNearestContactsPerJob_m7A2EC665BD757F95E86BC950E91BA37B1AC2588A (void);
extern void PhysicsJobOptions2D_get_updateTriggerContactsPerJob_m5756767F8BA939BF8834E3F3F244BD0AFFC3E8CA (void);
extern void PhysicsJobOptions2D_set_updateTriggerContactsPerJob_m200A1E3BC34797C4C40B6F375AE0E492892CBD66 (void);
extern void PhysicsJobOptions2D_get_islandSolverCostThreshold_m7214005F53C4136C65DDBB7BF40352CF5C69F307 (void);
extern void PhysicsJobOptions2D_set_islandSolverCostThreshold_m08171184FFAF1C0C8B268BC15FAA961719906D35 (void);
extern void PhysicsJobOptions2D_get_islandSolverBodyCostScale_mBB11308FF9BB281DD21B800FD02701F9AFC5E881 (void);
extern void PhysicsJobOptions2D_set_islandSolverBodyCostScale_m0F5568713C8A7D056614EAECA50F1EC42DDA6E4C (void);
extern void PhysicsJobOptions2D_get_islandSolverContactCostScale_mB24A44A346D134D75B93D8F35477D12F2D5E29F8 (void);
extern void PhysicsJobOptions2D_set_islandSolverContactCostScale_mB1848684FA85C5F324896437840B4712CC57B162 (void);
extern void PhysicsJobOptions2D_get_islandSolverJointCostScale_mD23FABF4C84E1ED3456E8E9AF0E4EF2902E769C6 (void);
extern void PhysicsJobOptions2D_set_islandSolverJointCostScale_m85293EF48F13BABF165CA4C1B04960F0C0C70A38 (void);
extern void PhysicsJobOptions2D_get_islandSolverBodiesPerJob_mBC375DF0FB397948AAB54BC4ED7593377F989A97 (void);
extern void PhysicsJobOptions2D_set_islandSolverBodiesPerJob_m927451FE8439EBCE47532EB6B0BFC1546AD63294 (void);
extern void PhysicsJobOptions2D_get_islandSolverContactsPerJob_mD484AFA46C160C184837E58C462CA9E19E28D5D3 (void);
extern void PhysicsJobOptions2D_set_islandSolverContactsPerJob_mF162C21E0D8466EFCE0AD8D4ECDF4646497DDDD3 (void);
extern void Rigidbody2D_get_position_m07070C4416DFE2229070F95B349E411AE4869276 (void);
extern void Rigidbody2D_set_position_m03C92F26F561D48050FBA840754F584AA7F415EF (void);
extern void Rigidbody2D_get_rotation_m3F6D0437733C0D9E4A3DF4196F80D66B20E563AB (void);
extern void Rigidbody2D_set_rotation_m2B5A1F0A973B47E2CFED94B3C01CA523938BF8A0 (void);
extern void Rigidbody2D_SetRotation_mC4D1C67247B4A738E97FA0A0CB581D575652E786 (void);
extern void Rigidbody2D_SetRotation_Angle_mA98FB4786535AFD6FA57A86F7379464064C79860 (void);
extern void Rigidbody2D_SetRotation_mEA618D515380273683B77825C078D83C52E34D1B (void);
extern void Rigidbody2D_SetRotation_Quaternion_mF272CFDF0963EFA7C7BC3DB3C767BB4972750D53 (void);
extern void Rigidbody2D_MovePosition_m7F24879BB78DA0587168B257C56DCFD248A90895 (void);
extern void Rigidbody2D_MoveRotation_m33BC3C46E2584EB32C6E440AA4E9E58BEEEE8256 (void);
extern void Rigidbody2D_MoveRotation_Angle_m067E8C2931E2C4BF400FC012A2D86A459D18E76E (void);
extern void Rigidbody2D_MoveRotation_m5AE2B8CE8F63C2417F584F4D13A745492E61732B (void);
extern void Rigidbody2D_MoveRotation_Quaternion_m47F8E2E6EF8086B3FB5D94A709D96DA25EAE7CC7 (void);
extern void Rigidbody2D_get_velocity_mBD8AC6F93F0E24CC41D2361BCEF74F81303720EF (void);
extern void Rigidbody2D_set_velocity_m9335C5883B218F6FCDF7E229AC96232FCBAC4CE6 (void);
extern void Rigidbody2D_get_angularVelocity_mAD2505FB1F8C9E1A66D1EA8F8680D14380BFC58D (void);
extern void Rigidbody2D_set_angularVelocity_mFC06FB14E69DD4847F27E614900D22317AA5A390 (void);
extern void Rigidbody2D_get_useAutoMass_m793637DCAFA651C48E5ED6755E55E8A07492CF76 (void);
extern void Rigidbody2D_set_useAutoMass_m13EA3EF3D9FBA4972B7E5FDBF601AE03AC644C69 (void);
extern void Rigidbody2D_get_mass_mC8854F0E26585A11D4420B9F5570AB4E75192AE1 (void);
extern void Rigidbody2D_set_mass_mC2E292A62264D605120FC65C713BB35503CE35B1 (void);
extern void Rigidbody2D_get_sharedMaterial_m6C6FB14A03720BF280C2AD7C22A4419F5EFBF966 (void);
extern void Rigidbody2D_set_sharedMaterial_m14C05C4028C6EAD8BC8EF3C75B5EADD858161285 (void);
extern void Rigidbody2D_get_centerOfMass_mE0719CD36314804E9BF2D0924B7A2C3076917F4C (void);
extern void Rigidbody2D_set_centerOfMass_mF30D18DD82C9093A2961FFB0A7D64396012B6238 (void);
extern void Rigidbody2D_get_worldCenterOfMass_m31238BC9455BE4EC3CDFF599B3CB571EFC59CD6F (void);
extern void Rigidbody2D_get_inertia_m6A5C0D11B5DE93AFA0C0A5D076448BFE64776BCA (void);
extern void Rigidbody2D_set_inertia_mC85358688D8203A76801CDCD428247BB17C5F8E6 (void);
extern void Rigidbody2D_get_drag_m0C65BDB12E059C56C2B5E1A9C0906F5A6AE0E92B (void);
extern void Rigidbody2D_set_drag_m42A0E181D64FD374A047E8ABE351207847B324D0 (void);
extern void Rigidbody2D_get_angularDrag_m017C7872E21D165A492C49BEE89CCBC9DF249B54 (void);
extern void Rigidbody2D_set_angularDrag_mF93603D37CFA437F6899EE53F711A3A7B5D0B599 (void);
extern void Rigidbody2D_get_gravityScale_mCFA8E159F51B876E16EEF634A63415F7051AFF44 (void);
extern void Rigidbody2D_set_gravityScale_mAFD1A72661304467D20971BBCAA7E04B418F80FD (void);
extern void Rigidbody2D_get_bodyType_m20709275F3D8215592B2B89736AA8DDD2BF44ED1 (void);
extern void Rigidbody2D_set_bodyType_mE2FAC2D78B06B445BD2AD58F87AC5B1865B23248 (void);
extern void Rigidbody2D_SetDragBehaviour_m39034E718F4E73405D03F5A3D56911D1673B96CF (void);
extern void Rigidbody2D_get_useFullKinematicContacts_m6F5EA23D8B1927F12FEAB156C4E80A8A53D98844 (void);
extern void Rigidbody2D_set_useFullKinematicContacts_m2D8164631CE3B48CA401DF389F49902F272063CD (void);
extern void Rigidbody2D_get_isKinematic_m41BBC60A072047F850097C0391A002935DD277CB (void);
extern void Rigidbody2D_set_isKinematic_m7C68AB4CFB6D301F0EDF0BFF66FB121ED3CC7853 (void);
extern void Rigidbody2D_get_fixedAngle_m768B00A05AB762B707C2596A945D2C55986CC907 (void);
extern void Rigidbody2D_set_fixedAngle_m000844F6B9A77865D80757A5A63F908F97934254 (void);
extern void Rigidbody2D_get_freezeRotation_mDC5E450309F120AA15C1D47DF23AF025E7218E05 (void);
extern void Rigidbody2D_set_freezeRotation_mFD0A56A3ED021B714BE05CE0EB22E954ED74E9B9 (void);
extern void Rigidbody2D_get_constraints_m5699129452DF94C5E15E67BCB2497C91A5E9FD42 (void);
extern void Rigidbody2D_set_constraints_mBF02A56E20BD497E3D291931E0AABB850952B238 (void);
extern void Rigidbody2D_IsSleeping_m349F570CFCF33C356D621D1D23A8336CF12AF99B (void);
extern void Rigidbody2D_IsAwake_mA27A80CB26CC0D38F7A4EB6B441E0C9DC3BDB6DE (void);
extern void Rigidbody2D_Sleep_mD0A38FE95ECB010C95AD99DC9C29308273186D30 (void);
extern void Rigidbody2D_WakeUp_m9660AAE58AC940973098BDABD1F54178EDE91753 (void);
extern void Rigidbody2D_get_simulated_mA8CC81D52F0F1ED08A88D82A808C27905909127F (void);
extern void Rigidbody2D_set_simulated_m38E0BD6581E907DD87059034C4B2E8D47BBFE71D (void);
extern void Rigidbody2D_get_interpolation_m3A85873C44DB8123E68DB38B1CC3DCF3FD2CD083 (void);
extern void Rigidbody2D_set_interpolation_m4914262B161A76DD061969667C0D412A8C93A994 (void);
extern void Rigidbody2D_get_sleepMode_mA66CE7D1CF938D72CBE0C7CD007F85563DE3CC71 (void);
extern void Rigidbody2D_set_sleepMode_m4E949F941B1D0950E9F30E93298ADF1B464A2362 (void);
extern void Rigidbody2D_get_collisionDetectionMode_m2FECB466D3B088FA62C593840164053FE662464D (void);
extern void Rigidbody2D_set_collisionDetectionMode_m87EC5B73B166FFA59434D7418643ADCBAD33C609 (void);
extern void Rigidbody2D_get_attachedColliderCount_m3CA3496C4A5740059D36AEB2B64455778E03898C (void);
extern void Rigidbody2D_get_totalForce_m8C196D829C0A3B1C2E652A2ABF6970EFD267377B (void);
extern void Rigidbody2D_set_totalForce_m201E1FA6506059ACFE21D5C1298BC93DA8E8A4B7 (void);
extern void Rigidbody2D_get_totalTorque_m0EDFDA6948579789111F370580D02B4679C137F4 (void);
extern void Rigidbody2D_set_totalTorque_mA7B05135AE11D9D4AF692A5F36BE3E7B15BBE0B0 (void);
extern void Rigidbody2D_get_excludeLayers_m257E2A564E576213DEDE8F3CEB8724B6A401C957 (void);
extern void Rigidbody2D_set_excludeLayers_mFF591036C53B929848E18808909CFD4E775370DA (void);
extern void Rigidbody2D_get_includeLayers_m2A16EF9EC595A508C30198B8A3F2801C6BDF544F (void);
extern void Rigidbody2D_set_includeLayers_m98752BFE99ABC921C4A7A7D824021312B34DF7B2 (void);
extern void Rigidbody2D_IsTouching_mC291AF31EE1712C3AC589A9C974044E6E6329453 (void);
extern void Rigidbody2D_IsTouching_mBA38654AD27DF4E2C813467BA7594E0322B54232 (void);
extern void Rigidbody2D_IsTouching_OtherColliderWithFilter_Internal_mE1718D4825E6D0899F8E00C494537AA21DBADFCC (void);
extern void Rigidbody2D_IsTouching_mA69870E78416F4DC0B381E5D862BEB5778FE6361 (void);
extern void Rigidbody2D_IsTouching_AnyColliderWithFilter_Internal_m5EA344EEC304D5C007495E561D3106CEE23A74EC (void);
extern void Rigidbody2D_IsTouchingLayers_m7EB57DFAE8657E011F7A48BFF71FE33787463600 (void);
extern void Rigidbody2D_IsTouchingLayers_m44E19521A4706926F284EE884221AF03814D197E (void);
extern void Rigidbody2D_OverlapPoint_m68E05AB2FF577198CEA583D385328353771BBB4A (void);
extern void Rigidbody2D_Distance_m4CC5A9540203C73E5A06A1AEDE4BA31A5082EC46 (void);
extern void Rigidbody2D_Distance_Internal_m4E0EC9AE7150FF9015642CFD88DA069C97E63BB1 (void);
extern void Rigidbody2D_ClosestPoint_mCBD378272B44E1ABDCF8642D646DCCE5777AB1DC (void);
extern void Rigidbody2D_AddForce_mC635C76F94D56891007700CA0E653EB269E955CB (void);
extern void Rigidbody2D_AddForce_mDD5CAE0137A42660C2D585B090D7E24496976E1B (void);
extern void Rigidbody2D_AddRelativeForce_m1C478BC5716586B81D014884CDFEC9AA0B8FCC9C (void);
extern void Rigidbody2D_AddRelativeForce_m503B7B6867FC6B2D95EECAAB5619F802669EBB16 (void);
extern void Rigidbody2D_AddForceAtPosition_m36B89D72D5C6C5B6D65A62E1F08FAE74CFC0356F (void);
extern void Rigidbody2D_AddForceAtPosition_mB561295A966E534307C1AE0D2122B40C49FEB15E (void);
extern void Rigidbody2D_AddTorque_mB126101CF0ECA5CC8C284ED06132B24FD8885029 (void);
extern void Rigidbody2D_AddTorque_m6B732A4BBCC6DFE0A5A8160FAF4DD292C76AC4D2 (void);
extern void Rigidbody2D_GetPoint_mEB7724F124CDA5B16967A910B14E08E09359B311 (void);
extern void Rigidbody2D_GetRelativePoint_m46BD12038126AC8080C12E60ECE6B21C55B9B39D (void);
extern void Rigidbody2D_GetVector_m65F4AA27A0B63570EAA17EFEED21F0581F3D5821 (void);
extern void Rigidbody2D_GetRelativeVector_m4F74ACE65537B062816EF1850F35B336C52A7855 (void);
extern void Rigidbody2D_GetPointVelocity_m29EB9C7A3665AA3E5EC4A591DA69CEDD1AA3F7CF (void);
extern void Rigidbody2D_GetRelativePointVelocity_m07BF714FE7CE38F014C5C64C9F41BCC566126FFD (void);
extern void Rigidbody2D_OverlapCollider_mCEDD6B47DB09AAF0CD0CFB6C98240D833CEBF2A2 (void);
extern void Rigidbody2D_OverlapColliderArray_Internal_m556DCF27FB3A24C052FDE39916498EE91775B31C (void);
extern void Rigidbody2D_OverlapCollider_m10FD5DF939CE46845BC85AF84D2375CDD2D6970A (void);
extern void Rigidbody2D_OverlapColliderList_Internal_mAB0ACE9BFBD56A76CD9D376EB904CCF15E0CA043 (void);
extern void Rigidbody2D_GetContacts_m24AFB88D473299B6208DC632B7599C447AEDEE73 (void);
extern void Rigidbody2D_GetContacts_mCDFA9FC88E26BC13C75F42C967FF7F140A700F31 (void);
extern void Rigidbody2D_GetContacts_mBB0F8AFD57488656888CA682F46ACC1BD9D888D0 (void);
extern void Rigidbody2D_GetContacts_m044ACCAD7854D1D891DB7A8F4C529BA34F21B42C (void);
extern void Rigidbody2D_GetContacts_mF99B9F51D0B327518F12AC03510E9D256F1170E6 (void);
extern void Rigidbody2D_GetContacts_m1F0CCF4E1F3C76937617A211A72F56A67C26A9FB (void);
extern void Rigidbody2D_GetContacts_m5F96E4804EA0DCBF73CE59D88DF0AC33979DF4C5 (void);
extern void Rigidbody2D_GetContacts_m912229FE8EAE8FBD1361F05CD2EE658466745D40 (void);
extern void Rigidbody2D_GetAttachedColliders_m8BD4BF172EE5A599BF0E7403AA3D22BCA962717A (void);
extern void Rigidbody2D_GetAttachedCollidersArray_Internal_mD914FAE2B26B3096327EC53EE858AD301A437226 (void);
extern void Rigidbody2D_GetAttachedColliders_m586FC445ABB6A3494BE2D6C627CD1F9807A4CA5D (void);
extern void Rigidbody2D_GetAttachedCollidersList_Internal_mE26A705EA2F8EC753BFD932DFAB5C21358DEC4CC (void);
extern void Rigidbody2D_Cast_mC74A661B42ADF8A8C092D7649B100C84B6864E2B (void);
extern void Rigidbody2D_Cast_m341A313BDC032BFF8729FF6670453EF03F7EEBF6 (void);
extern void Rigidbody2D_CastArray_Internal_m85128B34488B21E7CBDF6721B3DB675C97D19026 (void);
extern void Rigidbody2D_Cast_m8DEA46350BE3DD467C74A30EFEAA41570D35AB49 (void);
extern void Rigidbody2D_CastList_Internal_mCD69FF2B5B2A5E2CEBDD1567BBB1D622F1D5538B (void);
extern void Rigidbody2D_Cast_m73389995AD310431157636CD2E699FE47067F1CF (void);
extern void Rigidbody2D_Cast_m69B2C35D7B642F20ECC6E398D603D26FC86093EF (void);
extern void Rigidbody2D_CastFilteredArray_Internal_m5901CFF3A7ABF4E9FD5F370308BD754F796083B5 (void);
extern void Rigidbody2D_Cast_mAAB462833699748EDD641A5E7AD2747E5C054DCA (void);
extern void Rigidbody2D_CastFilteredList_Internal_mB7FD54658D3A65F5BAE773DC59821CBD26EFD8D7 (void);
extern void Rigidbody2D_GetShapes_m0FFC1DBDC8AB92DE13CB09AA19A6B36E81A777AE (void);
extern void Rigidbody2D_GetShapes_Internal_m7FFABE86C41C8F19BEDC651925A45FF55C707AF2 (void);
extern void Rigidbody2D__ctor_mFF16B8ADAAE2FFD5FD4FBE3F412FC9E8FBBDBC88 (void);
extern void Rigidbody2D_get_position_Injected_m89CBCD6C0EDACABB4A20B2B22958CEDAE030DC44 (void);
extern void Rigidbody2D_set_position_Injected_m5D2AC2F0F4D41BA17BCA48FF18263F7728C6871C (void);
extern void Rigidbody2D_SetRotation_Quaternion_Injected_m971873F0BE792FDC6C831E25DA9BF081A4504FF7 (void);
extern void Rigidbody2D_MovePosition_Injected_m7B6D07CFCE2E864C008AB5CED9EF1C8231D95386 (void);
extern void Rigidbody2D_MoveRotation_Quaternion_Injected_m788825A6966DFF17EC4B1C4E803774CCF90F1F4F (void);
extern void Rigidbody2D_get_velocity_Injected_m980E2BA9FA750BA922DD2F79CEEA1CFF0B9B5D08 (void);
extern void Rigidbody2D_set_velocity_Injected_m060C7F62A3A0280CC02594A15E7DE4486D1BE05E (void);
extern void Rigidbody2D_get_centerOfMass_Injected_m99936508EBABB64A9DB6E0D23925A768E5D26D69 (void);
extern void Rigidbody2D_set_centerOfMass_Injected_m4162FEE8D97CFF692A183D60989B2EE8A9F79A4E (void);
extern void Rigidbody2D_get_worldCenterOfMass_Injected_mE27AE31806EF79698842007398C1B5831F80624D (void);
extern void Rigidbody2D_get_totalForce_Injected_m80F16D9AC46423B95EC5B5E87E8BC6D17EE5108F (void);
extern void Rigidbody2D_set_totalForce_Injected_mF6493C6AAC11B174A730DCB07E94BFCA1DCE06E2 (void);
extern void Rigidbody2D_get_excludeLayers_Injected_m6776097C2A478AABFD41BB73404ED08B592A5669 (void);
extern void Rigidbody2D_set_excludeLayers_Injected_m539D7516D0B8D5A5AF4511E1A694D824E870B115 (void);
extern void Rigidbody2D_get_includeLayers_Injected_mDCBC9ECE347B684587CDD8C27A7B30E35714D3ED (void);
extern void Rigidbody2D_set_includeLayers_Injected_mBE55DD1394A095A39B8608E3BDD3C06C046561B4 (void);
extern void Rigidbody2D_IsTouching_OtherColliderWithFilter_Internal_Injected_m388045548275D1A8A971789C4123BD3B7E2C8210 (void);
extern void Rigidbody2D_IsTouching_AnyColliderWithFilter_Internal_Injected_m6BD055990388B22713244BDD68728BD793572363 (void);
extern void Rigidbody2D_OverlapPoint_Injected_m8FA2204DEB195C42266DD4883B0ADFF3970B6EDD (void);
extern void Rigidbody2D_Distance_Internal_Injected_m1EE4FCDEFDC648580F4F556A462E6FAB3C592BB1 (void);
extern void Rigidbody2D_AddForce_Injected_mC5372C179362B25CA579A94DBB11C5719F16452F (void);
extern void Rigidbody2D_AddRelativeForce_Injected_mFBEA1322EBD1A85F6303E9ED7BDB93FBCD648DC5 (void);
extern void Rigidbody2D_AddForceAtPosition_Injected_mEC7444E8A4872CA31FFD2266B617009E96DEB578 (void);
extern void Rigidbody2D_GetPoint_Injected_m4496261439CBF26686464DA7A98F91A92CA51514 (void);
extern void Rigidbody2D_GetRelativePoint_Injected_m679228AEC36B0BBF16FA6C11DE35398DBBD6A52C (void);
extern void Rigidbody2D_GetVector_Injected_m4679D65590A481B46C0698C6D1B8BB1F0E9E9D09 (void);
extern void Rigidbody2D_GetRelativeVector_Injected_m55E23C646C1B353EEFCF285639254A7E02E8C0F3 (void);
extern void Rigidbody2D_GetPointVelocity_Injected_mE632AEDC51E0504F55FFCA5350FFB05F6677357A (void);
extern void Rigidbody2D_GetRelativePointVelocity_Injected_mF988A20BE146E39F58B7E04155B6D6BC44365747 (void);
extern void Rigidbody2D_OverlapColliderArray_Internal_Injected_m52B03B8DB47F83A78693E0A36166BEB41B61F2BA (void);
extern void Rigidbody2D_OverlapColliderList_Internal_Injected_mB184F057EA967BF48A558C01FC094AF0D9DAD2A1 (void);
extern void Rigidbody2D_CastArray_Internal_Injected_m697ED544F860E74AE02BA4BC4202768D463ACFE2 (void);
extern void Rigidbody2D_CastList_Internal_Injected_m70C40BD3846AA86F5831F7D6EF9C8BA6E2C233C5 (void);
extern void Rigidbody2D_CastFilteredArray_Internal_Injected_mAD8FB97A72F6434AE9A331F4946C50B5B3B970E3 (void);
extern void Rigidbody2D_CastFilteredList_Internal_Injected_mFDC8AE2BA9AA99B5EF57DDA5451B0E91FC7E3442 (void);
extern void Collider2D_get_density_m1782EAE58518A69E8CBB0C9E30153D1522A90DB5 (void);
extern void Collider2D_set_density_m4D7FD660AAC3D3D08329C9A74BA2415A3C02A833 (void);
extern void Collider2D_get_isTrigger_m982A3441480D505432B26A5B3DF6D0B34342EEE7 (void);
extern void Collider2D_set_isTrigger_m19D5227BAB5D41F212D515C1E2CA433C2FEF7A48 (void);
extern void Collider2D_get_usedByEffector_m416E00D82F05E0608809AD495967D5605AA22DCE (void);
extern void Collider2D_set_usedByEffector_mEF3DE09BBDE852262DAA80F4229CAB6BB42CC8DB (void);
extern void Collider2D_get_usedByComposite_mFBCEE0F0F650485222B16C5F39A5604A20F89256 (void);
extern void Collider2D_set_usedByComposite_mBE188F38DA28CEFCEDAD84D8966D520222F749CA (void);
extern void Collider2D_get_composite_m1E64147888D6886748478E9CD2BB1035DDA9B1BB (void);
extern void Collider2D_get_offset_m6DC45B352DDE28C7B08607BFA3BECEC6E5F31914 (void);
extern void Collider2D_set_offset_m416A5FDD11A7E07322418D1869AEFF9F1295913F (void);
extern void Collider2D_get_attachedRigidbody_m76D718444A94C258228DD98102DCF81C91CF9654 (void);
extern void Collider2D_get_shapeCount_m2020E832FCAC46E2F702DF0132561B8C1A28D31B (void);
extern void Collider2D_CreateMesh_m3652DA9D992D70C69586B2CF76CC1B294434D487 (void);
extern void Collider2D_GetShapeHash_m21660247FB3386589F9CB5EADA20A3566DCECB6D (void);
extern void Collider2D_GetShapes_mB57DDDF72DD1734177B93CED52D1C2D0E43E67E8 (void);
extern void Collider2D_GetShapes_m2F072DA7FA4239FCD2D04A15863B357CFA0FF8C4 (void);
extern void Collider2D_GetShapes_Internal_mAA13A485C136388B87323C54CB3CE4909AA565EF (void);
extern void Collider2D_get_bounds_m74F65CE702BA9D9EED05B870325B4FE3B2401B5E (void);
extern void Collider2D_get_errorState_mB3E1140DDAA1883DC1EBB787F55D94CB69B85184 (void);
extern void Collider2D_get_compositeCapable_m3B5C595E4F0129625B839F9BD5D36B4A53B48691 (void);
extern void Collider2D_get_sharedMaterial_m04E36DA08D9C5494729A062A922233E875BA9D79 (void);
extern void Collider2D_set_sharedMaterial_m3CA0689BCC35C5601FEF7FD60ECA3F8A47C7FC8B (void);
extern void Collider2D_get_layerOverridePriority_m1135D5F56C107F238E49BB134C4EBD1F146FE10A (void);
extern void Collider2D_set_layerOverridePriority_mC896C54A19FD4C61357F73A5A141DA2919DC53CE (void);
extern void Collider2D_get_excludeLayers_m5BF604D30339A807AADC601130E55254A7C9BA34 (void);
extern void Collider2D_set_excludeLayers_mAD3BE7B8E564BC4381075A90FD6933338FADD588 (void);
extern void Collider2D_get_includeLayers_mFCE637602E68E599BF5CA4FA120FFFD0CAE3B241 (void);
extern void Collider2D_set_includeLayers_mFAAC8BABE19C6582CFF371D460204B68A52D46F8 (void);
extern void Collider2D_get_forceSendLayers_m2B5F4DA83B4DC4680DA1DE07E66F79E1139BA540 (void);
extern void Collider2D_set_forceSendLayers_m025690AB9952F77D6B8604C5884809D412A641D5 (void);
extern void Collider2D_get_forceReceiveLayers_m1CCD8BB08D0DD397A6DFFAF0BB8B76ABE66166ED (void);
extern void Collider2D_set_forceReceiveLayers_m1F69E3C231DBF10C7EE0626D6E89BB0D838CE7BD (void);
extern void Collider2D_get_contactCaptureLayers_m1576EF70121155CBE1918CFC5258A3B8C903B337 (void);
extern void Collider2D_set_contactCaptureLayers_m81E13E156F8361A0BB4D785C74BC951F02E1B5D8 (void);
extern void Collider2D_get_callbackLayers_m6AAC2DE43797453D12FB4F31D750FC25DB14E6AF (void);
extern void Collider2D_set_callbackLayers_m78D0B4A901432F51A35F43051955A880A77A4AD3 (void);
extern void Collider2D_get_friction_mC146D9227596E9EA351873D2910BE377BAA27973 (void);
extern void Collider2D_get_bounciness_m974564B81E5965061CC70018EB714E632B81B744 (void);
extern void Collider2D_IsTouching_mEE1EF81028F933A33E4585AF49E85FDE2AEDF7B5 (void);
extern void Collider2D_IsTouching_m582DC22EAD6086950A2058051D9AF2734B2CFFC0 (void);
extern void Collider2D_IsTouching_OtherColliderWithFilter_mF295A9DC2AA1211BDFBD2B0A968C6DA066D54F53 (void);
extern void Collider2D_IsTouching_m51C3D665075EB83DB191FF7BB5FA301930B8A16A (void);
extern void Collider2D_IsTouching_AnyColliderWithFilter_mFFB143430EB88ABAA13546F2F88AC779D28275F9 (void);
extern void Collider2D_IsTouchingLayers_m658C6283458728B7135860245E656338DD05A4C2 (void);
extern void Collider2D_IsTouchingLayers_mD0EA867624BAA3F4E5AD48F812FF1962A67F21B9 (void);
extern void Collider2D_OverlapPoint_mF04F862E1CDA270589C82BBD72E71F0B60B6B883 (void);
extern void Collider2D_Distance_m6649DB4DFFAD1992575F7FEFFA698D98BAC65690 (void);
extern void Collider2D_OverlapCollider_m1E8170E34B70AC18A9B441298E2E2F7A83D29C7D (void);
extern void Collider2D_OverlapCollider_m02DEE1A9596975452BC43CFC08EF5071426D51CC (void);
extern void Collider2D_GetContacts_m05C07D64E1AA1C3454079A0A1C1B64FB6914A12A (void);
extern void Collider2D_GetContacts_mABB8B706AE58C6AF10F5E0CD289D6CC003231D6E (void);
extern void Collider2D_GetContacts_mC776A58C20C9E8708E422F9D953FC82D13CA1458 (void);
extern void Collider2D_GetContacts_m776A35312006D8A02A22DA97037CAF1B9830F990 (void);
extern void Collider2D_GetContacts_mE16E31F0B44A237425EFBBFDF4F86E9EFEDA79E3 (void);
extern void Collider2D_GetContacts_mCD2A0B2C18808210431228DD5F1FE36F16348C92 (void);
extern void Collider2D_GetContacts_m21C0EFF59210FD998DA626008F67605C949AEA4B (void);
extern void Collider2D_GetContacts_mFC2ABBD9C0F59CC96840E08A41BB7C065552D022 (void);
extern void Collider2D_Cast_mD860ECCB9A4BB99401D9A4EFFB10D24192AFB2ED (void);
extern void Collider2D_Cast_m8C793587EFC6A1E3AB9A0BF3E26D4F296886E616 (void);
extern void Collider2D_Cast_m5E46950578F28D8317E8FE20755794FEA8EA6905 (void);
extern void Collider2D_Cast_m747197AA981A98E250B71619A1C5C4FE632D5868 (void);
extern void Collider2D_Cast_mDAA044B3EF1C2C8A428435FEB5CB77C25E01F258 (void);
extern void Collider2D_Cast_mF0BCE0B2A8FA4F909F166D891E54F5D620486EB9 (void);
extern void Collider2D_CastArray_Internal_m33D435FE40D0366CE73A6B096B7D7619D094777F (void);
extern void Collider2D_Cast_mDC994B95845B2E48B1D10C1DD40FD771B80B17AF (void);
extern void Collider2D_CastList_Internal_mDAE8E54A9D7350609D4A8DDD2BACD7F479ABEB2D (void);
extern void Collider2D_Raycast_m0DE730565A97717C045409262FEBE328107DA9D4 (void);
extern void Collider2D_Raycast_m2582FDC8A2732FBB9C41E677A4715CBB3996322D (void);
extern void Collider2D_Raycast_mF335AB5CBA08A85B8D3D2FB5C381A115C300DF9B (void);
extern void Collider2D_Raycast_m3B2BBDB5B9A4CD0E49DD4D69C1F62483133E5775 (void);
extern void Collider2D_Raycast_m74E2DAF7AE315E048190314392CC596A536E375E (void);
extern void Collider2D_Raycast_m3A055CBB341EA841839BD0A741F07C1E99138A21 (void);
extern void Collider2D_Raycast_m6460363104AE140968CD2B1BFD437BCA678038C9 (void);
extern void Collider2D_RaycastArray_Internal_mF50416ACA1F64D52A3C8DA6EDD84E7AF66D25E1B (void);
extern void Collider2D_Raycast_m843FA86B2BA1A2F18A9183A83ACA4EC80D69B59D (void);
extern void Collider2D_RaycastList_Internal_m3ECA2E13BBD3600633F3F5BDF18912681E87040A (void);
extern void Collider2D_ClosestPoint_m2530361BC16191D6CD3AED28A8ACA8830D8EA453 (void);
extern void Collider2D__ctor_mC4E4C5F6A2093B4902A09B312D1E832F12DE0B4B (void);
extern void Collider2D_get_offset_Injected_mDB21472D27E7E40EBB270CB307959BEEA9E55783 (void);
extern void Collider2D_set_offset_Injected_m9C48213A56B5D2DA5DC8873D650E602ECBCFCC96 (void);
extern void Collider2D_get_bounds_Injected_mB3F24D39428D3C8E314CC6452878BD9A62C216E4 (void);
extern void Collider2D_get_excludeLayers_Injected_mAFC71003E7AD6E1DB9D96953DC20AEA58FE802F6 (void);
extern void Collider2D_set_excludeLayers_Injected_m2E642F5F74DC22E05796F4022041C07A88F0B0C5 (void);
extern void Collider2D_get_includeLayers_Injected_m1ABE2B7CB9087904F0135B8EDEE70A38E277B0D0 (void);
extern void Collider2D_set_includeLayers_Injected_m339CA67F10ECF84ECF6A6944A4158D6A62C4F5C8 (void);
extern void Collider2D_get_forceSendLayers_Injected_m54E754FAFAB7A9EC93958CD862DE988F4A75A0C8 (void);
extern void Collider2D_set_forceSendLayers_Injected_m6D82229AFBFEB463005F035B607F88363701CF56 (void);
extern void Collider2D_get_forceReceiveLayers_Injected_m48AC1667732F147B602C1818BE26DAFE9D8C5226 (void);
extern void Collider2D_set_forceReceiveLayers_Injected_mFC94C90579A452C674726389820BF45ECF54CFBB (void);
extern void Collider2D_get_contactCaptureLayers_Injected_mA14C87269F5E13F91ECDF58C8C51998E7B7773EE (void);
extern void Collider2D_set_contactCaptureLayers_Injected_m9D9205A8A2FB666F8A72315FC618BFC9A0FE666C (void);
extern void Collider2D_get_callbackLayers_Injected_m93A27AAA96FD13ED894EAC175D91BB6C915DAC17 (void);
extern void Collider2D_set_callbackLayers_Injected_mE833BB6DE418280745FEDBCD15780FFAED32A228 (void);
extern void Collider2D_IsTouching_OtherColliderWithFilter_Injected_mB652C7ED62801745BA24E486E95021BAA71CC3B5 (void);
extern void Collider2D_IsTouching_AnyColliderWithFilter_Injected_m3B8165E3F368D1DCD1E7281415460A5BAD7645A9 (void);
extern void Collider2D_OverlapPoint_Injected_mCD54F37FD8E95A7EF693F7502435017DAD640188 (void);
extern void Collider2D_CastArray_Internal_Injected_mBB0C68E31C9EC06E54FA1581D4DB75A3AD0DE90F (void);
extern void Collider2D_CastList_Internal_Injected_mB1691F0D6709312632D07F10F4A612437609F2D2 (void);
extern void Collider2D_RaycastArray_Internal_Injected_m44B30BACAB3AA19FE3440DD7039CEE4BD838913C (void);
extern void Collider2D_RaycastList_Internal_Injected_m7356F4477061C1E8E4DB9B797448547DC14CEF40 (void);
extern void CustomCollider2D_get_customShapeCount_mBE08207173380CA20F2D4A5F2BE15C76518A6AD9 (void);
extern void CustomCollider2D_get_customVertexCount_m616476A7CF2DFDD444E6F2CCBADEBBAAC7BFCAA1 (void);
extern void CustomCollider2D_GetCustomShapes_mB463C46A0DCE53C960F2E7FB2793DF5D954B9CB2 (void);
extern void CustomCollider2D_GetCustomShapes_m16067B5F0A4F7867F5417E69D6F4EB386689A59B (void);
extern void CustomCollider2D_GetCustomShapes_Internal_mA50CF23D1E4B1564D28F35066C4F97D4B65B5DAC (void);
extern void CustomCollider2D_GetCustomShapes_m6260CB829EFA2BCDE5CE36C3E67C1B46E9338A86 (void);
extern void CustomCollider2D_GetCustomShapesNative_Internal_m15A9E7191AD0020E69918F0B63EBEB43CD98DAEE (void);
extern void CustomCollider2D_SetCustomShapes_m48A46BEE8DB24CC02B6586BCF1E8853585912BDF (void);
extern void CustomCollider2D_SetCustomShapesAll_Internal_m011E83F4A029CF73B838EAB46680DB9B763B263A (void);
extern void CustomCollider2D_SetCustomShapes_mDCA6F07FFA86D76296FAC08AC3BA20B410E541C1 (void);
extern void CustomCollider2D_SetCustomShapesNative_Internal_m0D77378650BA801B075CCB26E5C66E57FC426C48 (void);
extern void CustomCollider2D_SetCustomShape_m83346477A05C387179AFE2A5F2DF137E8FD0A340 (void);
extern void CustomCollider2D_SetCustomShape_Internal_m883ED32933CE2616345530FB5C56C2FCFF0ADDEE (void);
extern void CustomCollider2D_SetCustomShape_mE113626A12BC4BE71192214FB1572D679A3ACED6 (void);
extern void CustomCollider2D_SetCustomShapeNative_Internal_mCB051D503B85CAE181771E5054FE02751E0C680A (void);
extern void CustomCollider2D_ClearCustomShapes_m2F8C02BCA08EFBA0AB5168C232ED832E1D37E4DB (void);
extern void CustomCollider2D_ClearCustomShapes_Internal_m88B5F47E59C6AA438CD6C4C6C59440CCF0EC1AFE (void);
extern void CustomCollider2D_ClearCustomShapes_mCAD0E68EA53C59096DCEF947FB1617AED0A5EB73 (void);
extern void CustomCollider2D__ctor_m1B625DE24BC3434A692A58906B3DA4FFBEA9D10C (void);
extern void CircleCollider2D_get_radius_m**************************************** (void);
extern void CircleCollider2D_set_radius_m**************************************** (void);
extern void CircleCollider2D_get_center_m**************************************** (void);
extern void CircleCollider2D_set_center_m**************************************** (void);
extern void CircleCollider2D__ctor_m**************************************** (void);
extern void CapsuleCollider2D_get_size_m665189C0E2EE06B144D595F92AF8A4F03C23E70C (void);
extern void CapsuleCollider2D_set_size_mF81DEA4CAED765717A0B17DBB71C4E9392E84FCE (void);
extern void CapsuleCollider2D_get_direction_m6243634C67D44970DE58F69EF0A8218681606A0F (void);
extern void CapsuleCollider2D_set_direction_mD3BCE4D9E5187AB0F180550BB57F89E4D8327E0A (void);
extern void CapsuleCollider2D__ctor_mC4E295CCB3FCF7B83A2AF2418D86F0F0C0C46ABF (void);
extern void CapsuleCollider2D_get_size_Injected_m54E90669EF16ADDDFD85EA774E8A004E0C3CE176 (void);
extern void CapsuleCollider2D_set_size_Injected_m4F5747DB855E8F2C506DCC8A6DB8F03D45E1B3A9 (void);
extern void EdgeCollider2D_Reset_mD8E1DDE82D7922AF780049B5AA232030D9EF9E5F (void);
extern void EdgeCollider2D_get_edgeRadius_m99516FF70D54BEA0C84DA64205A8EA5E35EDB7BC (void);
extern void EdgeCollider2D_set_edgeRadius_m98F70E3A7976E606C4E003C4D740DAA0F60DC8B6 (void);
extern void EdgeCollider2D_get_edgeCount_mBC6D001B5F41935FDA2E08B6FD282410758BC5B8 (void);
extern void EdgeCollider2D_get_pointCount_m55AE0598CD0D4981F7818CAD2F20B65A34418517 (void);
extern void EdgeCollider2D_get_points_m51D877F93275BA12BD02873E5623ED9F468D968A (void);
extern void EdgeCollider2D_set_points_mFF49E39867F376E313D50F57531C28A1F715D02F (void);
extern void EdgeCollider2D_GetPoints_mB42014C3FC7BFF5A26C1FFB7300EFE8C1E166D2B (void);
extern void EdgeCollider2D_SetPoints_m214E8660F2539B7A50AFAD92206F359D1418BEA8 (void);
extern void EdgeCollider2D_get_useAdjacentStartPoint_m8E074BB055459F469DBB7DCD2B2985122AC0296B (void);
extern void EdgeCollider2D_set_useAdjacentStartPoint_m0C5F481510036106A76E8E8969E6863EF20BCB5B (void);
extern void EdgeCollider2D_get_useAdjacentEndPoint_mE8ABBEDC875AB4494D54076925297CA5E5B441F8 (void);
extern void EdgeCollider2D_set_useAdjacentEndPoint_m6F67425587CEAA63DEE11538EC7CAF4E4C53AECC (void);
extern void EdgeCollider2D_get_adjacentStartPoint_mA4E289E1CD02ECE21644A45EC3788B8C38BA0D3A (void);
extern void EdgeCollider2D_set_adjacentStartPoint_m726EEB10BFA51B3B8C8BB33BD15ADD3BE01D298F (void);
extern void EdgeCollider2D_get_adjacentEndPoint_m9DBDC3A1A6836927E832E567CBFC8051DD7A5397 (void);
extern void EdgeCollider2D_set_adjacentEndPoint_m0BA538CF2876C2D06778FB5C7A351CAB1908E1BE (void);
extern void EdgeCollider2D__ctor_mEFFC70C11EFA43456B9A41CEFFACD61A4D1B71DE (void);
extern void EdgeCollider2D_get_adjacentStartPoint_Injected_mFAD79CFAE0636F04E57520FD3BC4B8FDE5AE4C4D (void);
extern void EdgeCollider2D_set_adjacentStartPoint_Injected_mC60962AFC4F92DA6ABDC37BB6391A23F6FB035DC (void);
extern void EdgeCollider2D_get_adjacentEndPoint_Injected_m46B51237229818B0107B431EE3906FFFBA548DE9 (void);
extern void EdgeCollider2D_set_adjacentEndPoint_Injected_m38BB09D7EEBD288F39DA97AC7393D09630B17E7A (void);
extern void BoxCollider2D_get_size_mBB657ADFC58A79CDFDB7478956BBD9032E41D3D1 (void);
extern void BoxCollider2D_set_size_mA69E48F639FFB614B5FC083D3FEED3DF78A9FF46 (void);
extern void BoxCollider2D_get_edgeRadius_mC992345491CE535A20E0C2209D6DAC9E156FB5C5 (void);
extern void BoxCollider2D_set_edgeRadius_m3B65453E01419336458097C9CFF2F1A164F7A015 (void);
extern void BoxCollider2D_get_autoTiling_m9DF61AB62FB56245A6B289CC90FB9905A3E2E479 (void);
extern void BoxCollider2D_set_autoTiling_mBCE5DCEFB409AA9F890424B4EC767C373DF62596 (void);
extern void BoxCollider2D_get_center_m8CDA86C60BC5E368F3F2C669246B5DB6C6239709 (void);
extern void BoxCollider2D_set_center_m0B2352C472856F235FEB94F7DDC75F6E1B751CFE (void);
extern void BoxCollider2D__ctor_mF153AB4CCB1C1F59176D3EE252E14B983FB557CF (void);
extern void BoxCollider2D_get_size_Injected_m6F9DD21D59E2B6D2B202DA657590DDA51A5B3EBF (void);
extern void BoxCollider2D_set_size_Injected_mE40FB02D46FAF468524BBC5BDF7BE468E7F85B9E (void);
extern void PolygonCollider2D_get_useDelaunayMesh_m44FB6ABFDC7F3C40468FE6A2142393BE01BEC3A1 (void);
extern void PolygonCollider2D_set_useDelaunayMesh_mC145E47AB94701F9546B41102ABFB7D6B5324453 (void);
extern void PolygonCollider2D_get_autoTiling_mE48F5926C8BB2516CCD7A35E7E55000D25960250 (void);
extern void PolygonCollider2D_set_autoTiling_m4DDF7E85942C8CF14312EAB86CA1AEBB5430BB24 (void);
extern void PolygonCollider2D_GetTotalPointCount_mDD5625F1A07C11D06D9F35757BFDF22B3ABD943A (void);
extern void PolygonCollider2D_get_points_m44182B587ECD765B7CF23A3A4BB289CDE95D421E (void);
extern void PolygonCollider2D_set_points_m74A433CEBC5A6A460EC2852CDEDEED2D4E261462 (void);
extern void PolygonCollider2D_get_pathCount_m2F7EA6C9D0D7E579741DD3CB26BD1B2320570CC3 (void);
extern void PolygonCollider2D_set_pathCount_m088370F58AC70DE6D28029AB0F2443D6A9B87721 (void);
extern void PolygonCollider2D_GetPath_mE9D53D83FBB110EAC748BA535A1659C262B50F50 (void);
extern void PolygonCollider2D_GetPath_Internal_mEF39269E7021D37741567FE0D6001305DCE49A69 (void);
extern void PolygonCollider2D_SetPath_mDF03B6FDAE81E25C985F9BA6D372D949A6D9A1C1 (void);
extern void PolygonCollider2D_SetPath_Internal_m868D93E9467A88558DD0E5D66797186B9FA82C4D (void);
extern void PolygonCollider2D_GetPath_mB98011C4334D40CE11F4C382144F5369D0B22D70 (void);
extern void PolygonCollider2D_GetPathList_Internal_mA52E40B227DFCC61DA4899AA99BC17A17FE3F22D (void);
extern void PolygonCollider2D_SetPath_mF606659E4198753710AE7C4D032AC9A65972BBAB (void);
extern void PolygonCollider2D_SetPathList_Internal_m5D71A68788B250DFFE8BFA0A94152D6D797A56A2 (void);
extern void PolygonCollider2D_CreatePrimitive_m2782BFD076EDE0A4D6EF7E2904EF12285D3FAF76 (void);
extern void PolygonCollider2D_CreatePrimitive_m8F71C78DA75D43277273B1C4570BA1669919944E (void);
extern void PolygonCollider2D_CreatePrimitive_m8557092739042CF265ECB129A6AF3C4BBF3AC1B5 (void);
extern void PolygonCollider2D_CreatePrimitive_Internal_m55AE07F15581E5D428327B9D6297BA13515F518F (void);
extern void PolygonCollider2D__ctor_mC2255D56CD93945AD9E72E196BF5168F7A13A538 (void);
extern void PolygonCollider2D_CreatePrimitive_Internal_Injected_m4EB71AD721B0C99D2EA874A435B0097514A3CE99 (void);
extern void CompositeCollider2D_get_geometryType_m7A11D31CB35A73AE64AE08C781ADE9CDC9C2E78C (void);
extern void CompositeCollider2D_set_geometryType_mC171C34D43B63BD504568160B722D11CF097004C (void);
extern void CompositeCollider2D_get_generationType_m5450B70EE23AB8591530592BA5504DAFAC903E08 (void);
extern void CompositeCollider2D_set_generationType_m4F4412F0193FB1D19CEFD5394FBE3DA4848367C5 (void);
extern void CompositeCollider2D_get_useDelaunayMesh_m78F653A5B228DD97C5AF526010FB31611BF4F0A2 (void);
extern void CompositeCollider2D_set_useDelaunayMesh_mACE1EBBDF2D6AC5961DCC8D48FA6FC4BFBF3AE75 (void);
extern void CompositeCollider2D_get_vertexDistance_mBF6A3438CF3F9193B2CB0253EDF9FBEF471B9AA8 (void);
extern void CompositeCollider2D_set_vertexDistance_m29AA51E389B699EAD62633E94C5FCA6EF901A15F (void);
extern void CompositeCollider2D_get_edgeRadius_mDDA470560B4FD6708F508DE4014D34B212D10E7A (void);
extern void CompositeCollider2D_set_edgeRadius_m541E32B74C9A215897DED9029CD3D0FC51D968FF (void);
extern void CompositeCollider2D_get_offsetDistance_m8E64B6936B78CD64FD1449CAAA190E110C6C68D1 (void);
extern void CompositeCollider2D_set_offsetDistance_m49947EEC717EF5DFB07F9CEE1EFE27F3A1A334B3 (void);
extern void CompositeCollider2D_GenerateGeometry_mBC5EBC336F93E580B37A8FCD1CCF70D6258FB898 (void);
extern void CompositeCollider2D_GetPathPointCount_m227236C21461B9D259075429C5D72A9117D833F4 (void);
extern void CompositeCollider2D_GetPathPointCount_Internal_mC0B352A52B4102AB6B60421116F9A12DDBAE17DB (void);
extern void CompositeCollider2D_get_pathCount_mFCE509BFF1DE4F0AB2C242DB9665CCF16BC2D0DC (void);
extern void CompositeCollider2D_get_pointCount_m2D67EA1A290944246376496948C259B03F06D459 (void);
extern void CompositeCollider2D_GetPath_m048E471625A3AC46864D74EA7D17077F8348FC09 (void);
extern void CompositeCollider2D_GetPathArray_Internal_m04FD906BAC09301C13ACD6BBA2479913D8CF5920 (void);
extern void CompositeCollider2D_GetPath_m5106FB56C8618315FEB28F630B1A14A4A1475BFD (void);
extern void CompositeCollider2D_GetPathList_Internal_m613178445832F4BF0F7EB8D5DAF2B2BDEF08DCAD (void);
extern void CompositeCollider2D__ctor_m701B74C8EF6FEA37C8D3C2204C80D4F2A9695503 (void);
extern void Joint2D_get_attachedRigidbody_mF11B3881B599F4358539E99AA11AAF86E7DBF24F (void);
extern void Joint2D_get_connectedBody_m2ACC7B59AFFF74F080B96DBBC42866B15F6EA125 (void);
extern void Joint2D_set_connectedBody_m96BC3C64A153EAB29F7C4BE4435A3E15B9FE6DED (void);
extern void Joint2D_get_enableCollision_m51773F6581CD59450F90D350B0749139FFE9AE8E (void);
extern void Joint2D_set_enableCollision_m21290BA0369A21B5C992556B4236BC576D11944D (void);
extern void Joint2D_get_breakForce_mB07CFACCF8173013AE59639635067991C67AA1F3 (void);
extern void Joint2D_set_breakForce_m0D386E87920FB5FFA4C62E5B01656E66D6111E64 (void);
extern void Joint2D_get_breakTorque_m03E456D986A250828053D33391C5792BBD464F4A (void);
extern void Joint2D_set_breakTorque_m351CBC2E1EB2708B1737A6BD3EE3622C5D490729 (void);
extern void Joint2D_get_breakAction_mDB645B376DC417816122306B8D35AABF21138EE4 (void);
extern void Joint2D_set_breakAction_mD730EA23498B75D977C8657E00C0FBD44EB58FFF (void);
extern void Joint2D_get_reactionForce_m89418F3DB76BAA9FBD8695B496B139C8F091D26F (void);
extern void Joint2D_get_reactionTorque_m1A3E7969A0627E174968D2A4BBAB230ED131ED85 (void);
extern void Joint2D_GetReactionForce_m276772CC174D4128AD2CE184EAFAF59E39E97536 (void);
extern void Joint2D_GetReactionTorque_m20D83BE56E786184AFC39774AC1A260CE8D022BB (void);
extern void Joint2D_get_collideConnected_m306A0F8E8153441C0B49FC868DAE543F3B07E2DD (void);
extern void Joint2D_set_collideConnected_m1381BD6784F1F30BC8D9B5B90D881A931EDA87DC (void);
extern void Joint2D__ctor_mAB58E264E8D3ADD1A2185274A517E7F39E7ED853 (void);
extern void Joint2D_get_reactionForce_Injected_m2A4E8C2CB88769FCC8340DAFBE18717420621FBF (void);
extern void Joint2D_GetReactionForce_Injected_m538BCA18E0BF821BE8DAAB01915B89E1C6A95EC4 (void);
extern void AnchoredJoint2D_get_anchor_m517BE8FCD419F6F6521E5BD690CCCEE0D1FD5E46 (void);
extern void AnchoredJoint2D_set_anchor_mD6316CB3E4BAA0F443F9CC7A109361FA34059C0F (void);
extern void AnchoredJoint2D_get_connectedAnchor_mCE5550E89AA68AC0C17DA51A7B6F11F43457FC83 (void);
extern void AnchoredJoint2D_set_connectedAnchor_mB07C956CA00DD5DE7DC59DFD075A415120E2803D (void);
extern void AnchoredJoint2D_get_autoConfigureConnectedAnchor_m5A3E2045B025F176969BD4FCA399F64CC9DF1582 (void);
extern void AnchoredJoint2D_set_autoConfigureConnectedAnchor_mCCEA6080EDA9BE6A77064AF4A193B69B4AE80713 (void);
extern void AnchoredJoint2D__ctor_mF490146220001E0121B7CFDF4D2A6C53F1633CB7 (void);
extern void AnchoredJoint2D_get_anchor_Injected_mB4A11D61BA7A6FAEAC5B23CCF0C94A8021327633 (void);
extern void AnchoredJoint2D_set_anchor_Injected_mB73F28EC4B855C01458D81E5EF1B19114FBEDE5A (void);
extern void AnchoredJoint2D_get_connectedAnchor_Injected_m5A960B4774F642CB34B009D69776ED9A10F735F7 (void);
extern void AnchoredJoint2D_set_connectedAnchor_Injected_m14F1FCD64FE2008CEE123577D95C61D5033B3318 (void);
extern void SpringJoint2D_get_autoConfigureDistance_mADCA9647C42631046FD828C04F9E17ABCE002F7E (void);
extern void SpringJoint2D_set_autoConfigureDistance_m92C05ECB7DD1357B31C892EA8D95A7129DEC2231 (void);
extern void SpringJoint2D_get_distance_m0E08665FC9D23D51AD8F24B423BAAB3B2912FDA1 (void);
extern void SpringJoint2D_set_distance_m9D72DBFBA3FBAE8DA5B95DEDE3F0FE5D3D5AFA16 (void);
extern void SpringJoint2D_get_dampingRatio_mC708BE53071C2C24B56E77BBB187BC68CD7A20B0 (void);
extern void SpringJoint2D_set_dampingRatio_mCE35C68410871890FF81E70380295869684CFA68 (void);
extern void SpringJoint2D_get_frequency_m34C8251937CC875D4765E0057DF108A860EBD0F1 (void);
extern void SpringJoint2D_set_frequency_m41AED17894EA7D360BF1089B1CD8A793F21256CE (void);
extern void SpringJoint2D__ctor_m204288D5BD581C49C2793E013786B82DE1B48086 (void);
extern void DistanceJoint2D_get_autoConfigureDistance_m545EFD3FA2225303E6EA184DF653EC7AF3BEB669 (void);
extern void DistanceJoint2D_set_autoConfigureDistance_m3C72E98ECF71C7F6606B6D6BF72BDFBC131C3D8A (void);
extern void DistanceJoint2D_get_distance_mBA1BA4522675FFBB05FAB6C9B5DAF42679661496 (void);
extern void DistanceJoint2D_set_distance_m1A6C0D1337B10103691F7074A48BD9759AB1E39C (void);
extern void DistanceJoint2D_get_maxDistanceOnly_mCA41EB66D0738AE850849BBC552DE0A7F8218F94 (void);
extern void DistanceJoint2D_set_maxDistanceOnly_m83A8A1EE7BF8DB2C3093F6282B468A394C097238 (void);
extern void DistanceJoint2D__ctor_mC0F54EA481C7FE50939B6745397750195EEE77F5 (void);
extern void FrictionJoint2D_get_maxForce_m74BFABD977B928CDF8B191EEC7144993AC160F3B (void);
extern void FrictionJoint2D_set_maxForce_m4CF4D5668E9C77937DF53B1E09DE94682B287812 (void);
extern void FrictionJoint2D_get_maxTorque_mE7F679A4FC5C905C8884EEE1820B44FE551FBAB8 (void);
extern void FrictionJoint2D_set_maxTorque_m6E252578D83DE1D7830CB55082397D1ED1B73E5B (void);
extern void FrictionJoint2D__ctor_mEB80266F7F8557396DB2C68BA5DAB82AE89CB0F3 (void);
extern void HingeJoint2D_get_useMotor_mEC951A49C02E6D3D6B2CDF044F375087BDF293A9 (void);
extern void HingeJoint2D_set_useMotor_m01AE6EB9DDDB4D8123369965B27A953DE9453DBF (void);
extern void HingeJoint2D_get_useLimits_m88D5E5FF1E6DB96106569E311488550983942612 (void);
extern void HingeJoint2D_set_useLimits_m8E1A4EA6EE6850F7960C939DBFD0F8ED1AECF086 (void);
extern void HingeJoint2D_get_motor_m78B13D793D036DECF09AB508D80A7043CA03868D (void);
extern void HingeJoint2D_set_motor_m1C296741B49BCB6A4B8F4AD347B04BC90E61A863 (void);
extern void HingeJoint2D_get_limits_m6CAA2912070CED97CF8E724171D5FB944D45FA3F (void);
extern void HingeJoint2D_set_limits_mD948CC50F20045FA0A5CBAE57B38D05E984FE3BB (void);
extern void HingeJoint2D_get_limitState_m86781EE771B838A633556CA3E1F6AFA0082626B7 (void);
extern void HingeJoint2D_get_referenceAngle_mFF58B70690BC7C5312D4D5C5ABF5DC969FEDE6BA (void);
extern void HingeJoint2D_get_jointAngle_m3E13F9ACA9E55A3B8207C91AFA9EBD10217C31DD (void);
extern void HingeJoint2D_get_jointSpeed_mEE116CCA1DBC3891A9060FF36DD0C903FA1982FE (void);
extern void HingeJoint2D_GetMotorTorque_m1C00273D08CA85992E4059D078207EE24818AA9D (void);
extern void HingeJoint2D__ctor_m01217F17792A684CDEB294242B39A891D2AD406A (void);
extern void HingeJoint2D_get_motor_Injected_m790FC34275C2EE27B4AE737BF55555CC88173B49 (void);
extern void HingeJoint2D_set_motor_Injected_mAC48171AD7FDB9C8B2208455B703CC2EE95FEF41 (void);
extern void HingeJoint2D_get_limits_Injected_mDCABF905EC94D567652061DCCFFDF5E65A3D6A29 (void);
extern void HingeJoint2D_set_limits_Injected_mC074612E730D7159523FDFE834908EADAD4D3AF1 (void);
extern void RelativeJoint2D_get_maxForce_mB5849E930A13FB75645574A0D12F323C9909C78D (void);
extern void RelativeJoint2D_set_maxForce_mA0B6126B89F825CE62CD511D426766C119A03015 (void);
extern void RelativeJoint2D_get_maxTorque_m0178245D8239AF5819EC3B65C9E421CDD542F066 (void);
extern void RelativeJoint2D_set_maxTorque_m9FC0275EE218098920ED03EE33250E145AD64235 (void);
extern void RelativeJoint2D_get_correctionScale_m2F60047934BAF9F80FB446E1125F84D8F500F2DB (void);
extern void RelativeJoint2D_set_correctionScale_mE63FB6369FA233980D26A15382DB4B6D75DAAA70 (void);
extern void RelativeJoint2D_get_autoConfigureOffset_m6D476F05275E9540C1BEFC5DB062B68F815BF62B (void);
extern void RelativeJoint2D_set_autoConfigureOffset_m127C028E09B0C6806EFCF0E460E3B6A9BCFE4407 (void);
extern void RelativeJoint2D_get_linearOffset_mF7715E30094E81F7723CD857D27A7379BF97F299 (void);
extern void RelativeJoint2D_set_linearOffset_m8CED88F5DFCDE7A9D5798EE4ABCCE8BDFFD6451B (void);
extern void RelativeJoint2D_get_angularOffset_m16C173D3378524CF1D9C1D07523A82979F85E52F (void);
extern void RelativeJoint2D_set_angularOffset_mD3E7127AEB1CECE54BE0A765E732C54FD9230E0F (void);
extern void RelativeJoint2D_get_target_m8832090D139AED4965EE61BD5061595A051D0574 (void);
extern void RelativeJoint2D__ctor_m8419E8F544B315CE97B67FA81DA223E97C947224 (void);
extern void RelativeJoint2D_get_linearOffset_Injected_m5ECF5CDA9247BBAE742C40A5D3F7F8E27AECF1B4 (void);
extern void RelativeJoint2D_set_linearOffset_Injected_mFABE2353127C6C430FC6D34061E591532FCD6831 (void);
extern void RelativeJoint2D_get_target_Injected_m7919A553A63EF992A0AF68520014483926F902B1 (void);
extern void SliderJoint2D_get_autoConfigureAngle_m714AADA636855A0FD909876D57C8A9217F714DB9 (void);
extern void SliderJoint2D_set_autoConfigureAngle_m05D23AD054ACB910D0810332B9C4294AEAD2AFCB (void);
extern void SliderJoint2D_get_angle_m3E0B29DA81EDD4C329F569C5CBD93B35A2286B71 (void);
extern void SliderJoint2D_set_angle_m8FA204E7AF77C39AF71BDC90A6C2153D1C5CF56C (void);
extern void SliderJoint2D_get_useMotor_m5CCB1993650B387E65ABAD83461A81C3018F90F1 (void);
extern void SliderJoint2D_set_useMotor_mEBE4B53AE419F3D35431BAA6A079FC15157FE45A (void);
extern void SliderJoint2D_get_useLimits_m59C8E06171DEF67290B75BB3FEFBA5EED8E6EB87 (void);
extern void SliderJoint2D_set_useLimits_m4FD93475AB26925FCF0933A6B34F346104ABB9B9 (void);
extern void SliderJoint2D_get_motor_m3F48592DF38BE0BF1DE4A6E0B457EA179E9AE0D6 (void);
extern void SliderJoint2D_set_motor_m8F2FE7E30420B9B3445D70EE619EE74C2122B242 (void);
extern void SliderJoint2D_get_limits_m22F67068000901CB7494EC6CE50AC2171F5DA6D0 (void);
extern void SliderJoint2D_set_limits_mA06CC8C7743403D6D39BFE3A92A5DA6712A0FB9A (void);
extern void SliderJoint2D_get_limitState_mA4796AEAB481E470E3A63B721D9B005519444EC9 (void);
extern void SliderJoint2D_get_referenceAngle_m34D8C706D1F5BDDEE680F126AA37135CA91EF3DD (void);
extern void SliderJoint2D_get_jointTranslation_mC0CAD9EF2E642B4829879AACAF10A372A09EC474 (void);
extern void SliderJoint2D_get_jointSpeed_m163F1CE86F82B6D87CB731DB78E8D1038583D7C6 (void);
extern void SliderJoint2D_GetMotorForce_m960C166D4E952DE8E8E839DA7A40C5F298006693 (void);
extern void SliderJoint2D__ctor_m01A4D65377A5ED9EDEE8B58E836C9AD301E6EE32 (void);
extern void SliderJoint2D_get_motor_Injected_m0797A36062932F62251CC79CB1E40C7CE7056FF2 (void);
extern void SliderJoint2D_set_motor_Injected_m94648F7C6629B35822E3DB59E388D2B27F2C7131 (void);
extern void SliderJoint2D_get_limits_Injected_m223390A434238C146212D28BBD2E1B05E9F2E561 (void);
extern void SliderJoint2D_set_limits_Injected_m225CBB9B79C65340E28313C5F41F3FF71E3B0F33 (void);
extern void TargetJoint2D_get_anchor_mB394F84A67089FBEE8F835BA8209A5A214D0A34D (void);
extern void TargetJoint2D_set_anchor_mB390CD2FB4C6DA84722DEAD8E22DEF3F36E4D473 (void);
extern void TargetJoint2D_get_target_m6C8FFEB47352FC7891F593C1DAF9D45037578DDD (void);
extern void TargetJoint2D_set_target_m1E3C256EA0BC2E7F84DF06D78B27CEC47427ED37 (void);
extern void TargetJoint2D_get_autoConfigureTarget_m4A01339DD58AD8DF6A8987145AD768F8516977BE (void);
extern void TargetJoint2D_set_autoConfigureTarget_m29D827A36F7AF5EF2CCA59C751895A8E954FDD8B (void);
extern void TargetJoint2D_get_maxForce_m587596A8099EDFEC1042010450698F5CA079B4D0 (void);
extern void TargetJoint2D_set_maxForce_mE30B5AE87F32BF350A7E42F4806C3789F5D4400F (void);
extern void TargetJoint2D_get_dampingRatio_mFE0FE1BC25599BE2ABC2A39CC173D1CA7B9C453B (void);
extern void TargetJoint2D_set_dampingRatio_m911A0C3FDD91AE5FD38AD6DFF82B10BF0DC14864 (void);
extern void TargetJoint2D_get_frequency_mB77855831937FA57ECD21946B6DFAD5F7CD6D583 (void);
extern void TargetJoint2D_set_frequency_m80F76AA40FBEF1587316E66300F55FD9E696D846 (void);
extern void TargetJoint2D__ctor_m4811DEC4CC456CF217BEF93010A17504DBAED16E (void);
extern void TargetJoint2D_get_anchor_Injected_m1901CD146F9DCD4E854A35C1FAA56E01389C7EF7 (void);
extern void TargetJoint2D_set_anchor_Injected_m137388BB690E19F23D17FDD89E8B311809B17C1D (void);
extern void TargetJoint2D_get_target_Injected_m8BC74137C8027CD774D5E31E8B9D1ED7C8C3160C (void);
extern void TargetJoint2D_set_target_Injected_m1B017BEABA58FCEA63DC806B50147000E3EDDB54 (void);
extern void FixedJoint2D_get_dampingRatio_m4FA14CC70E255303EEBBF9B326549AD49C8627EF (void);
extern void FixedJoint2D_set_dampingRatio_mAD96C575C3AEF211A9233629D2019D75F8522F15 (void);
extern void FixedJoint2D_get_frequency_m60C536DA1C5668165152B4F7C416FD667738D6A3 (void);
extern void FixedJoint2D_set_frequency_mE1A9139F2A0DCF63FC581656770EB8663F1F9F54 (void);
extern void FixedJoint2D_get_referenceAngle_mE09E2ED6D40D10F3063B06BF5D994407EC79F8BC (void);
extern void FixedJoint2D__ctor_m4D3E49EE19F2387170FF67C63333EC844DDCF00C (void);
extern void WheelJoint2D_get_suspension_m63BBA2E8C9F3BA23D350DFAE2664DB08E980BD0C (void);
extern void WheelJoint2D_set_suspension_m017C1A68D1411841D1FFC0AF2C7291B361B4E554 (void);
extern void WheelJoint2D_get_useMotor_m5DE6900CEC08ECBB1C07DF3BD041494D35706D1B (void);
extern void WheelJoint2D_set_useMotor_m97D63CBBDC65194E6B3116ACB68A89EF5019D1AA (void);
extern void WheelJoint2D_get_motor_m7B8A4EE9688A96BF43C1B90AB767EF0BA14449F0 (void);
extern void WheelJoint2D_set_motor_m0A772ED5C1DA9925825F97D871E9D2320599702F (void);
extern void WheelJoint2D_get_jointTranslation_mF48618BC2FD3490420FE6CD9A9E14D7C1C73BD5A (void);
extern void WheelJoint2D_get_jointLinearSpeed_m8330007F345E79F7C22D9186271A10019CCE229F (void);
extern void WheelJoint2D_get_jointSpeed_m2D04C5B38D68E5735A9E40E48E9AA13938F7EEB8 (void);
extern void WheelJoint2D_get_jointAngle_mC8C64E8FC6C8A7107751E88D8B5F542E442265DA (void);
extern void WheelJoint2D_GetMotorTorque_mC639162E42BE1E32B68E7F0615AB34B8C50DB450 (void);
extern void WheelJoint2D__ctor_m0F13BEE677ABE27A2625A72B7C52F2527535CDC7 (void);
extern void WheelJoint2D_get_suspension_Injected_m33FF82B5B58DD6CB38286625BD9D28A37138C5BD (void);
extern void WheelJoint2D_set_suspension_Injected_m6D13E64D7C74AA48DAD394094BB9EAA37540676D (void);
extern void WheelJoint2D_get_motor_Injected_m19BA887304608C8669E3FE5321B94AC69F505B98 (void);
extern void WheelJoint2D_set_motor_Injected_mF5B66AD79991882EB09C669C39CA74DFA20ECD8E (void);
extern void Effector2D_get_useColliderMask_m4D61092DC2E3778C69A588E5F7701893BDB0A27B (void);
extern void Effector2D_set_useColliderMask_mFCF8B69C11E793E06FE33077EB3F869010C16FB6 (void);
extern void Effector2D_get_colliderMask_m1431067B61EF032D539C9DEF6505CDF1C6EB3E7A (void);
extern void Effector2D_set_colliderMask_m5C7E34CA1611177A59ADBB8D1B6C9B177CF87BED (void);
extern void Effector2D_get_requiresCollider_m3228CE8BBF0AF919690B6D9E008B788B675EFD15 (void);
extern void Effector2D_get_designedForTrigger_m5AC214DC32256874F5527177C62FE65CD0715C70 (void);
extern void Effector2D_get_designedForNonTrigger_m7A523D9D98E9B110D34166124FDA11621373809F (void);
extern void Effector2D__ctor_m80D08310829C700C31BA2E4B6B9109EFE31B628F (void);
extern void AreaEffector2D_get_forceAngle_mAE1EBAB8BF86F48AC5C4C1BFCC4E67891494CA33 (void);
extern void AreaEffector2D_set_forceAngle_m510FAD4CCB55EFE1B4223A06D308FEBE30AF3A17 (void);
extern void AreaEffector2D_get_useGlobalAngle_m39787D8C929799E6A19B4687E4000678609A1213 (void);
extern void AreaEffector2D_set_useGlobalAngle_m89179FAD2B3AB6C796912678AC9178D5A8515260 (void);
extern void AreaEffector2D_get_forceMagnitude_mA572C7297BE28EC01F21A37EBD44EF220C0A7FF1 (void);
extern void AreaEffector2D_set_forceMagnitude_m9EAD9F9D51FA54B20B16EC2D97300AF27FC08D5A (void);
extern void AreaEffector2D_get_forceVariation_m12B634EC144DB72AD635B252F7BFE95C9E48E8A7 (void);
extern void AreaEffector2D_set_forceVariation_mADD42B7A5103CD6AB9685CA2E146ACACFEE09E0E (void);
extern void AreaEffector2D_get_drag_mC6A0D5642E5C83847059A48E01E6A3942A7B361E (void);
extern void AreaEffector2D_set_drag_m62106C7DD6CA073C7E64226E8E9F6D9BDE553F7A (void);
extern void AreaEffector2D_get_angularDrag_mEE4E64259BEA089D2CF593279FC8289E3B9FB081 (void);
extern void AreaEffector2D_set_angularDrag_mA2B133C3010CF8EAA76A3222BE642674AFBE68A6 (void);
extern void AreaEffector2D_get_forceTarget_mEE0289B4C59E66100F760455BAA86C1DCB3B6F26 (void);
extern void AreaEffector2D_set_forceTarget_mA57C89968F72C2B879D09A6930C0C50056C1AF1D (void);
extern void AreaEffector2D_get_forceDirection_m5DAD95A723D9C9F3717336ADA66D676AC19F7EFC (void);
extern void AreaEffector2D_set_forceDirection_m8415D2BBF72C728B1404CD9DCFCFFC6B3B5ECEDA (void);
extern void AreaEffector2D__ctor_mEFDF77D391F5686D81BC1183535D701C9543E910 (void);
extern void BuoyancyEffector2D_get_surfaceLevel_mEEEF2AFD2E7FA30814A710F087D8EF4EA2EE4D48 (void);
extern void BuoyancyEffector2D_set_surfaceLevel_mA2E66865E258E6EDDB4B6453A9BD4681B259BE5F (void);
extern void BuoyancyEffector2D_get_density_m719EA866A0FDB2935E66A5ADCEC6006C72C82479 (void);
extern void BuoyancyEffector2D_set_density_m42D85A902F814C94C3ACEBC996204126FC0C0DD8 (void);
extern void BuoyancyEffector2D_get_linearDrag_m04B19A94E8B35B9446B966974D1775BC49B271C6 (void);
extern void BuoyancyEffector2D_set_linearDrag_m3124502851A2E9DF6A855715344BEA28877577C5 (void);
extern void BuoyancyEffector2D_get_angularDrag_m4C295A65D2C24DDA95A48381A8B4C9FC985AE83C (void);
extern void BuoyancyEffector2D_set_angularDrag_m49DDC2592EB4D390217490CA0E27138E4641B2D8 (void);
extern void BuoyancyEffector2D_get_flowAngle_m49C504276945133550F71635BFAE9899B553F902 (void);
extern void BuoyancyEffector2D_set_flowAngle_m9FE1F4F7ADFFA96629EC7105853943CB5C62A73A (void);
extern void BuoyancyEffector2D_get_flowMagnitude_m78C60720AEE4D8866BF69E94BEDD8987CC5D7AC4 (void);
extern void BuoyancyEffector2D_set_flowMagnitude_mE61117AF92B595C8EE982FDAF217A6C665DF8E17 (void);
extern void BuoyancyEffector2D_get_flowVariation_m5D2E6042CD1ADEDBEA838D36AE32D8A92BBF9DCA (void);
extern void BuoyancyEffector2D_set_flowVariation_mFE3C11B6C7349F10569119B1C12C15D32B449E59 (void);
extern void BuoyancyEffector2D__ctor_m2CD5DDD34EBDD376475F7E7187DC7542AD49A443 (void);
extern void PointEffector2D_get_forceMagnitude_m8AE19EF7174FEC1ABB8DB89BBE5B565DE927CB73 (void);
extern void PointEffector2D_set_forceMagnitude_mB644B6A9AD8D1EC1BE18B0E05E82B0B58FDE7B29 (void);
extern void PointEffector2D_get_forceVariation_mDDD35E4BB76EDF4154D8E64E371B464C33FC4A7A (void);
extern void PointEffector2D_set_forceVariation_mF42136F7DF9936E9FC5CE9F6705FCE7890E290C7 (void);
extern void PointEffector2D_get_distanceScale_m0307CB80A13876DEF7FC28A82F612A0C4D1B4CAE (void);
extern void PointEffector2D_set_distanceScale_m278A83859C595BF578D1729F8185FE576AECD68F (void);
extern void PointEffector2D_get_drag_m1131CE8CBF238AA14800ACE1E2776BD26FFAD4B8 (void);
extern void PointEffector2D_set_drag_m935AEB52672F2E2087AF736E1E54ABAC18AE1BAD (void);
extern void PointEffector2D_get_angularDrag_mFE905AED6B896C4B347F244C170634F4A4BD047A (void);
extern void PointEffector2D_set_angularDrag_mA1616BBF3B03A4B6384394F93218E2B03C22157F (void);
extern void PointEffector2D_get_forceSource_m30C09EE515C8FEB6F890DAF2011BEED84353FB3B (void);
extern void PointEffector2D_set_forceSource_mFDAB020692945EF0BD98B1297E08695E9EDC3FB5 (void);
extern void PointEffector2D_get_forceTarget_mCC012819DBE8A10AD2A13F02128EE6670E4D3370 (void);
extern void PointEffector2D_set_forceTarget_m7BB63ED280BE4F9CBFED63BE8FE342525EFD2630 (void);
extern void PointEffector2D_get_forceMode_m74268A10C0C5E2004F54C58817EE066F5FD472D5 (void);
extern void PointEffector2D_set_forceMode_mAE06AED24A08DA5C70762B2BE4A48C1A0C9A48C9 (void);
extern void PointEffector2D__ctor_m8018BAC1D6AA2DA1BD766A8F91444BEA576B5F76 (void);
extern void PlatformEffector2D_get_useOneWay_mC26DC37058BE73DC73437BA6CEEEDE0AFC0D271C (void);
extern void PlatformEffector2D_set_useOneWay_m471563E9B6A5AB0400EA32D3DAC27A9718FE375F (void);
extern void PlatformEffector2D_get_useOneWayGrouping_m7E56908FFD48BDBCB6D9439219114A025BCBB4A4 (void);
extern void PlatformEffector2D_set_useOneWayGrouping_m855B415A02D912F57CB5B8432C71F37E4A4BD27F (void);
extern void PlatformEffector2D_get_useSideFriction_m4907F73EA14E0A300FD655225023B95FF38D8FCE (void);
extern void PlatformEffector2D_set_useSideFriction_mD507BADE509F5035EB9C1413C0ECE672AD259164 (void);
extern void PlatformEffector2D_get_useSideBounce_mD181AFDEAEE11F426EE12580E4BA29B4F484BCF5 (void);
extern void PlatformEffector2D_set_useSideBounce_m3FFE929FB82C7E12199491C272629CA014FCA041 (void);
extern void PlatformEffector2D_get_surfaceArc_mDA96473824CB409259BDAD94CC2CF71A6E78176A (void);
extern void PlatformEffector2D_set_surfaceArc_mE0F36334C5F0A2C7CCCF8BAFDA8DD022B1C05440 (void);
extern void PlatformEffector2D_get_sideArc_mBE83C37F4B7EE06F5C6555AFAFA96351BA162DFB (void);
extern void PlatformEffector2D_set_sideArc_mD485DCEE35A405505B013BED7236398A2352DAF3 (void);
extern void PlatformEffector2D_get_rotationalOffset_mBA9E5BB3030CB92A5A2A8935779C6F6E7719420B (void);
extern void PlatformEffector2D_set_rotationalOffset_m620906016495D1C544165EFCB6B2EA5D6663FB20 (void);
extern void PlatformEffector2D_get_oneWay_mF380935A8F455C6E21E781CBAA0696B952FE22BA (void);
extern void PlatformEffector2D_set_oneWay_mAD1F53570E23E0FDB7BC563B8B4EF2FB5896010C (void);
extern void PlatformEffector2D_get_sideFriction_mFE5A77D74F1FFC57F9E92421FFF7229CE83E6D54 (void);
extern void PlatformEffector2D_set_sideFriction_mFB1E3C2E782BA38E0EB7EC87219FA2B2EE7B36FC (void);
extern void PlatformEffector2D_get_sideBounce_m763FF228A17647081A6560C33B969688CD6652E1 (void);
extern void PlatformEffector2D_set_sideBounce_m97051D7F33E006C2C019A67B76CEF97F4F6F9662 (void);
extern void PlatformEffector2D_get_sideAngleVariance_m49B9E0619398F5C1D0A23E7BB4B3FD844962AC4F (void);
extern void PlatformEffector2D_set_sideAngleVariance_m0B39B0A7B35200C70D6884B07D5BF3F8E16618AB (void);
extern void PlatformEffector2D__ctor_mC1BE6B1195E71193CCBD774D231B91A5C975C35D (void);
extern void SurfaceEffector2D_get_speed_m81585B003E24D6F0DD8D2641BA0A7400499A06AB (void);
extern void SurfaceEffector2D_set_speed_m1A7D40BEF98A32FBB2F7988508F20672F51771CF (void);
extern void SurfaceEffector2D_get_speedVariation_m9A768A56601E264DE6961B1A711C235723F93751 (void);
extern void SurfaceEffector2D_set_speedVariation_m55EC471E13EF2F2C7BDF6108CE6C575C35230E00 (void);
extern void SurfaceEffector2D_get_forceScale_m754C6A8B464D725E4CEDF62E93B71BAA282E7F50 (void);
extern void SurfaceEffector2D_set_forceScale_mF90C47A72B2E34ACD53D0ECE21751E9338BC4426 (void);
extern void SurfaceEffector2D_get_useContactForce_m49DB6D8470D7D015486481015AC84DE967D36D7E (void);
extern void SurfaceEffector2D_set_useContactForce_m0F4DFA5ABEE68E9FE6163E7F90A62F8409E433CC (void);
extern void SurfaceEffector2D_get_useFriction_m2DFD492E5E07241B7025AC80E194DCBA9581956F (void);
extern void SurfaceEffector2D_set_useFriction_m6FD3DFDEE2D37944B230D1B0AB5E7A07E28902FE (void);
extern void SurfaceEffector2D_get_useBounce_m7965EA0142BCF92C20EA41EF1FE7E7EAEC49253B (void);
extern void SurfaceEffector2D_set_useBounce_m8EB99E31FF37C7F993454645EC8E0E71441BFBED (void);
extern void SurfaceEffector2D__ctor_m2FFB309942DADE93910221DE31E39B40ED89CCE0 (void);
extern void PhysicsUpdateBehaviour2D__ctor_m4A73D789ED84FDE97B95556E88768E15DD374DAF (void);
extern void ConstantForce2D_get_force_m1ADDB7691F9480ED2D0485BD71A09333E819D551 (void);
extern void ConstantForce2D_set_force_mD46C591006E52563509C70CB0C081646C96992CC (void);
extern void ConstantForce2D_get_relativeForce_mF22945A4F60ED1A5A400DFF14E1F98BD4CDD7C2E (void);
extern void ConstantForce2D_set_relativeForce_mB4E6779432559DA2C1799359EF5ABA8F15D169A4 (void);
extern void ConstantForce2D_get_torque_mAC6CD6297DF65AAF72BA7413CF9B1794C7699537 (void);
extern void ConstantForce2D_set_torque_m4E12D622FECC185B792804DCB4192DACC3CA2CE0 (void);
extern void ConstantForce2D__ctor_m68361431387716BD7FE885457DA8779A73005C8A (void);
extern void ConstantForce2D_get_force_Injected_m73816BC3B23C63F5EC9672A1195660AA83C9BB10 (void);
extern void ConstantForce2D_set_force_Injected_mBA8F3FC1D8825C3F7FC3B52E11B3E54C6F1E0FD2 (void);
extern void ConstantForce2D_get_relativeForce_Injected_m34B20DCB7998FA663F7D493F6A9C27BFD0233E7F (void);
extern void ConstantForce2D_set_relativeForce_Injected_mB05692C69E184236F2A44B84287DB3F0080F53B0 (void);
extern void PhysicsMaterial2D__ctor_mFF418E70441EFCA8BBED3B95860CF60CB19E96FB (void);
extern void PhysicsMaterial2D__ctor_m6B7BF5BCE02CFE0F96C480B653B29E24C90C2DC9 (void);
extern void PhysicsMaterial2D_Create_Internal_m3DB6A6CEEB4078BE664C982F61B5BA6A54FE6F81 (void);
extern void PhysicsMaterial2D_get_bounciness_m30F25ED9C256B1D48B2F26AA2D1795FA321C3E46 (void);
extern void PhysicsMaterial2D_set_bounciness_mBA08748E3304284A251E7B3E343EF548AB82DE91 (void);
extern void PhysicsMaterial2D_get_friction_m1972CF3539B219537E5517A35C912940FCF51C5A (void);
extern void PhysicsMaterial2D_set_friction_m30157AED9C44D312E40B4E1C735197EF7A17B000 (void);
static Il2CppMethodPointer s_methodPointers[1271] = 
{
	PhysicsScene2D_ToString_mACA22EF575F7544176360B16D431F2BB1DEBB307,
	PhysicsScene2D_op_Equality_mF7A3D7BF3F4E48D862EE1981941BD580C6AB741A,
	PhysicsScene2D_op_Inequality_m87F2AA2476AE980B057FE5A31E2432A673107C17,
	PhysicsScene2D_GetHashCode_mD45B3437D088C66A35AE20066AD632D1D0858B1E,
	PhysicsScene2D_Equals_m4A19DE0675BD596A1B5AC0F7138A9A6F4D6029B3,
	PhysicsScene2D_Equals_mA7C243A71CFDBFA905F057CE3E9C5E61B34216FB,
	PhysicsScene2D_IsValid_m3C273A4AC45E6E9994DCBC66D11E003200FAF50C,
	PhysicsScene2D_IsValid_Internal_m50BDBEA5D97EE63E1E1732BD753DAD9C1B625D90,
	PhysicsScene2D_IsEmpty_m62DBEF907153F3158A43768639668054F8B82465,
	PhysicsScene2D_IsEmpty_Internal_m8ACC80B5923701D097E862ECCD5DEB68DBF4B078,
	PhysicsScene2D_Simulate_m2210AE79B5D4713DA5BEFD4EB5857778651DCE62,
	PhysicsScene2D_Linecast_mF2BCE1D6E939D9E3B0289FE40FC6F8618706B33D,
	PhysicsScene2D_Linecast_mAAC2C53143BDB79CCE8666C2E1432FB4E75FD915,
	PhysicsScene2D_Linecast_Internal_m89344966CB23C79B1A9819FCB5F5B98CD4F3C304,
	PhysicsScene2D_Linecast_m5DEB3199C07DB52BB9E1C890D58A53726DBD24C9,
	PhysicsScene2D_Linecast_mEEB64412838171C7AA92974E8E923645C2AED7DD,
	PhysicsScene2D_LinecastArray_Internal_m57E2A7ABE8671BDF15C8120123BB948E41E117CA,
	PhysicsScene2D_Linecast_m68E48116A78255DCADA115102C4093A6B79612C3,
	PhysicsScene2D_LinecastNonAllocList_Internal_m4A8361A19EB72590EB7B1E40223C3A1536C82239,
	PhysicsScene2D_Raycast_m5A2D66F6E7E8F34B6CF5B82099EFA4F69155F25D,
	PhysicsScene2D_Raycast_m74A71D9DBCC2CCD7454240AE784CEE5720E55EA0,
	PhysicsScene2D_Raycast_Internal_m39487CD184C451A0932E85CF899B768B0AF04424,
	PhysicsScene2D_Raycast_mDA9E6E04FC3117D6819BD757347A886AAF6024CD,
	PhysicsScene2D_Raycast_m004884696543F60917C1ED72374C1528207229C3,
	PhysicsScene2D_RaycastArray_Internal_m78B0093DCC7AD643AE641D65D9A616FBAAF93D08,
	PhysicsScene2D_Raycast_m541841D244633BA234ED72B01204161686D6B3B9,
	PhysicsScene2D_RaycastList_Internal_m8F8D0331D48A0A5F19238FB3FE48DF1007C078DD,
	PhysicsScene2D_CircleCast_m****************************************,
	PhysicsScene2D_CircleCast_m****************************************,
	PhysicsScene2D_CircleCast_Internal_m****************************************,
	PhysicsScene2D_CircleCast_m****************************************,
	PhysicsScene2D_CircleCast_m****************************************,
	PhysicsScene2D_CircleCastArray_Internal_m****************************************,
	PhysicsScene2D_CircleCast_m****************************************,
	PhysicsScene2D_CircleCastList_Internal_m****************************************,
	PhysicsScene2D_BoxCast_m87BBB9AE3A51D80D32CF4351D1BCB24D230C547F,
	PhysicsScene2D_BoxCast_m2BE649FF276A4DA721F359D60991E20B6EC1390E,
	PhysicsScene2D_BoxCast_Internal_m14C241D05B6FC564CE812B144AC09EEC47AAF62D,
	PhysicsScene2D_BoxCast_m303C253EFABCC5FFA582706B5263CFF703BAE31A,
	PhysicsScene2D_BoxCast_m5959F7A39896649972E94A16FD852470321077A9,
	PhysicsScene2D_BoxCastArray_Internal_m876C291873AC8A96BE306E5EC8152C6696654AC6,
	PhysicsScene2D_BoxCast_mB3593FE8701D4482F74886089D69087CA38515CA,
	PhysicsScene2D_BoxCastList_Internal_m21A492B93014AFCCA33EE98F1475C2BF1EE90D56,
	PhysicsScene2D_CapsuleCast_m63BFCD6264B727CFFBDE288E3BAF56395A9BB3C1,
	PhysicsScene2D_CapsuleCast_m06AE8E8E4E64B35464B27F5AE236B0D81D3E72A1,
	PhysicsScene2D_CapsuleCast_Internal_mEF949F75F6FAFEBD4535E9CB0DB56E614F7C2B65,
	PhysicsScene2D_CapsuleCast_mFF5DE18E5B463E9A9435A4F73BDA4CCD87D63E49,
	PhysicsScene2D_CapsuleCast_mF141441734E1357C94F1B51A3065DCD8BB8A5F99,
	PhysicsScene2D_CapsuleCastArray_Internal_m0BBB183B4404914052E1B952020CABA6C94C3886,
	PhysicsScene2D_CapsuleCast_m0D02AFE5BB9D55F18864E158CE3FC6792F180E9A,
	PhysicsScene2D_CapsuleCastList_Internal_mBBE9E328C138E6DFEB4CE0A1D33FFE3F3E3FEFBE,
	PhysicsScene2D_GetRayIntersection_m92BF0BF919D8BB704EC93D45EE7E2DB2EB176943,
	PhysicsScene2D_GetRayIntersection_Internal_m8F523A4E443EC8F78C38CADEB6A2721EA45980F1,
	PhysicsScene2D_GetRayIntersection_mF3E0EC0D4F5A4B8C063E735979C851ED5B4B4C2E,
	PhysicsScene2D_GetRayIntersectionArray_Internal_m5E84231D490515C4B9D39733E3CB11EDDF979B20,
	PhysicsScene2D_GetRayIntersectionList_Internal_m8B4CE07AC10BC1B25C32DF3BC55B52D582513340,
	PhysicsScene2D_OverlapPoint_mBD3BD6137CC642F63968D6058E75467C1098BBCE,
	PhysicsScene2D_OverlapPoint_mC2ED0510F29807B334351227C118F74CBC278227,
	PhysicsScene2D_OverlapPoint_Internal_m266CFB424DEC17E370C78E519F65DF60E01C256E,
	PhysicsScene2D_OverlapPoint_m3BEA82B608B1338872A28CE4A7A8BCB4A45A7F07,
	PhysicsScene2D_OverlapPoint_m19614587E2F3CD617B899513DDA8299F455960A1,
	PhysicsScene2D_OverlapPointArray_Internal_m6A106F4AC8D3B42A6604FA6C3C7A6A540094D486,
	PhysicsScene2D_OverlapPoint_m3C062202164C34E05357D6D53864132CB4DF3325,
	PhysicsScene2D_OverlapPointList_Internal_mAF5F43FF546546BA7BDEDC14E964D8B79AD6F067,
	PhysicsScene2D_OverlapCircle_m****************************************,
	PhysicsScene2D_OverlapCircle_m****************************************,
	PhysicsScene2D_OverlapCircle_Internal_m****************************************,
	PhysicsScene2D_OverlapCircle_m****************************************,
	PhysicsScene2D_OverlapCircle_m****************************************,
	PhysicsScene2D_OverlapCircleArray_Internal_m****************************************,
	PhysicsScene2D_OverlapCircle_m****************************************,
	PhysicsScene2D_OverlapCircleList_Internal_m****************************************,
	PhysicsScene2D_OverlapBox_m92B0E7E2244148FB38CB5EC0782C55E4CE5D41C6,
	PhysicsScene2D_OverlapBox_m335088F6FBADF523759D88B56B4CE8B351BACEBF,
	PhysicsScene2D_OverlapBox_Internal_m03820B585C51BD0FA5CA4ECC4AA7786986767A46,
	PhysicsScene2D_OverlapBox_m822F0FBB0CB890B8B3DCF1B513EDD45686325D58,
	PhysicsScene2D_OverlapBox_mE4BBC0516865E1BFB5CC23E85FCC6985EE1F1673,
	PhysicsScene2D_OverlapBoxArray_Internal_m35D9309858C2006A0E3B453780F7201064FDBCFE,
	PhysicsScene2D_OverlapBox_m87AB321755E4631C49A58FD12E85A6046F78A20B,
	PhysicsScene2D_OverlapBoxList_Internal_m077144CB6A138D9A1B2F6AABBD58C0BA46B35CF4,
	PhysicsScene2D_OverlapArea_mFD58E3BBEEE4357EB0BBA9A96EBB58012E7270A0,
	PhysicsScene2D_OverlapArea_m4D8FAFD9E7A299823649C9043C70456FF63F0655,
	PhysicsScene2D_OverlapAreaToBoxArray_Internal_m330E73DB6C450B36501A02801C5C77A4BD1F1B19,
	PhysicsScene2D_OverlapArea_m00070511C0F182CFDAC6BC57A057ADDCC671B1F3,
	PhysicsScene2D_OverlapArea_mD7B8AA443BB67D29957C8F0AA14DD4AC99F93F2B,
	PhysicsScene2D_OverlapAreaToBoxArray_Internal_m81756D2895F9044EA86F0B21A47A16FB3F2AD592,
	PhysicsScene2D_OverlapArea_m1DBB709EB60C3BCBF2064C98E247360DCE93C1FB,
	PhysicsScene2D_OverlapAreaToBoxList_Internal_m9D3425E6BC3D06B023D05F38ED4F50E1E178459E,
	PhysicsScene2D_OverlapCapsule_mB9EED3130F064B14CB4895858D6A25B7FE9CAF47,
	PhysicsScene2D_OverlapCapsule_mB521741943F713EB037454DA5EB61CF4E405B364,
	PhysicsScene2D_OverlapCapsule_Internal_m7DA985D5CD4C14C797DFD7ABCAEDC7745E7E628C,
	PhysicsScene2D_OverlapCapsule_m98A5052A0721B060EF518A167268F1384F36565B,
	PhysicsScene2D_OverlapCapsule_m950A00AE483884975510112A38DDAC9ED30358EA,
	PhysicsScene2D_OverlapCapsuleArray_Internal_mA9CF4CE10F2783774909BD5AC4C7DBD111EB9719,
	PhysicsScene2D_OverlapCapsule_mD621AE5E0F3095B5E7A9B6662DD4D55B1C0FAA31,
	PhysicsScene2D_OverlapCapsuleList_Internal_mD941BC678A00DCF5E31D14460BA93DEB6679EA43,
	PhysicsScene2D_OverlapCollider_mC87E2704140649F1E70F1E9318BB035ADF3D2EE5,
	PhysicsScene2D_OverlapCollider_m0B119DC3BC74F23E89EA78CFB55C000F0C2CDAF8,
	PhysicsScene2D_OverlapColliderArray_Internal_mC132864949D60EA73C432B0BABCFA408AA95B7FB,
	PhysicsScene2D_OverlapCollider_m99B77F1452FB414901F59C8821CC762E7026B145,
	PhysicsScene2D_OverlapColliderList_Internal_mE0D02729C317959A00C350783548689556EBB1FB,
	PhysicsScene2D_IsValid_Internal_Injected_mAE8FB599C34DF82B10243CCF39338DB1D15D8E68,
	PhysicsScene2D_IsEmpty_Internal_Injected_m1C6A83C6A9A2C291DEEB7FA20AF50268A1CDC53C,
	PhysicsScene2D_Linecast_Internal_Injected_mEC7F06BBE9C0F88ACFC6C14A39C0CF2BD0E26B31,
	PhysicsScene2D_LinecastArray_Internal_Injected_mBB18F5F30F62B826F6E36B67F07F04AA33B88BC2,
	PhysicsScene2D_LinecastNonAllocList_Internal_Injected_mC97B61467AAA4071B245080436C25AAA85DBFB2B,
	PhysicsScene2D_Raycast_Internal_Injected_mFDE2EC874A7E78DF64283FD6396B8C3F9B69659E,
	PhysicsScene2D_RaycastArray_Internal_Injected_m554BD34B09F598A0B0F264957AB06ADD1285582F,
	PhysicsScene2D_RaycastList_Internal_Injected_m6B018025FABC15C19B02E005C87B5228C4D8A917,
	PhysicsScene2D_CircleCast_Internal_Injected_m****************************************,
	PhysicsScene2D_CircleCastArray_Internal_Injected_m****************************************,
	PhysicsScene2D_CircleCastList_Internal_Injected_m****************************************,
	PhysicsScene2D_BoxCast_Internal_Injected_mD5EC44BC06AD72A7F28A0D893C12587AA915C66C,
	PhysicsScene2D_BoxCastArray_Internal_Injected_m0A82E92F906E96AD1CA2BDA40EF88529F22DB55D,
	PhysicsScene2D_BoxCastList_Internal_Injected_mC7F5B6CDAFBB5544F2E72EDC439A0E9BDF7A1255,
	PhysicsScene2D_CapsuleCast_Internal_Injected_m1D49EDE7754153AC8DBEA696246EF90D923819FC,
	PhysicsScene2D_CapsuleCastArray_Internal_Injected_mE0AAE4E084F585E6FD9390C8FC5C4D1F2A4DABED,
	PhysicsScene2D_CapsuleCastList_Internal_Injected_m5F273A0C0940A7449B80DEA8C4125AB489F464D4,
	PhysicsScene2D_GetRayIntersection_Internal_Injected_mFFE9F51858E5DECF785638E8B28BFF941D8D6329,
	PhysicsScene2D_GetRayIntersectionArray_Internal_Injected_mAC45B2CE0A6FC10A72166EFEA0D04151C2C579A2,
	PhysicsScene2D_GetRayIntersectionList_Internal_Injected_mA97723484F986E9309B873BBE94578AFC7EFB35C,
	PhysicsScene2D_OverlapPoint_Internal_Injected_mB9BD7F80DF31309E93592E8E269A19C0AE49D5E4,
	PhysicsScene2D_OverlapPointArray_Internal_Injected_m33139CEC7C63B21E6E59CF5236147ADE777B36F7,
	PhysicsScene2D_OverlapPointList_Internal_Injected_m95F0DABB5312FF43DDB08D0CA0A7DF7CE28BEF4B,
	PhysicsScene2D_OverlapCircle_Internal_Injected_m****************************************,
	PhysicsScene2D_OverlapCircleArray_Internal_Injected_m****************************************,
	PhysicsScene2D_OverlapCircleList_Internal_Injected_m****************************************,
	PhysicsScene2D_OverlapBox_Internal_Injected_mD8A62BB2F0705ECC9C0A9681C61A0CDF51DFAB6D,
	PhysicsScene2D_OverlapBoxArray_Internal_Injected_m3F11205B29560348C8C51381BFA99EA231B34253,
	PhysicsScene2D_OverlapBoxList_Internal_Injected_mB5B02C0FAB82B54BEA4E1177E6D2DD149B64A46A,
	PhysicsScene2D_OverlapCapsule_Internal_Injected_m262C55DC8E5C007DE8BA4CC650DDE0A4BD01B132,
	PhysicsScene2D_OverlapCapsuleArray_Internal_Injected_m9BB43F807179B143FE80C8E5CE1EA6842007CC6E,
	PhysicsScene2D_OverlapCapsuleList_Internal_Injected_mD0A0C3D0F9C7754CFD627DAFF8A313C82C81C4A4,
	PhysicsScene2D_OverlapColliderArray_Internal_Injected_m976AEFD47D42223D2BA7E51449E64019B3B7E512,
	PhysicsScene2D_OverlapColliderList_Internal_Injected_m52D29A43675AB3CE412D5C19F5CEF23DCDDCAC93,
	PhysicsSceneExtensions2D_GetPhysicsScene2D_m71554BCD0560589D6D33391A74CA2E7B22FE913D,
	PhysicsSceneExtensions2D_GetPhysicsScene_Internal_m1BD5268413273AF3C855D2510C6829E64AD0A65A,
	PhysicsSceneExtensions2D_GetPhysicsScene_Internal_Injected_mA01F7804FC031AAC9E242C4F21B93F6CBEC6F569,
	Physics2D_get_defaultPhysicsScene_m688935D2C81F56703A1F0E3C8712604A42CB2804,
	Physics2D_get_velocityIterations_m0DBAC36A23AAC57B98DE40B8344C2D0A48F9063A,
	Physics2D_set_velocityIterations_mC23055A566AE6C78DE1A35D9936A9782DAE6A3C6,
	Physics2D_get_positionIterations_mE0736556D30AC5E0D77DFEE6687BC9A1B5F4EAC1,
	Physics2D_set_positionIterations_mA31F95FC57C52A9B6FF770077B3D26D8D5306011,
	Physics2D_get_gravity_mDF7A391FC0C21253EB763436B3CD612CC52A59BE,
	Physics2D_set_gravity_m1BBB6F368E12983D5978054A41A0ECA5579CD620,
	Physics2D_get_queriesHitTriggers_m2652ECB55DB31ADA6E1BD62DFB9792860A7B5FE8,
	Physics2D_set_queriesHitTriggers_mE288FA67C553C015CF4D5E2DA6C435F2F9ED9F4E,
	Physics2D_get_queriesStartInColliders_mECA14AD1A3E14F64AFDA4ABB5C83A54872DDF09E,
	Physics2D_set_queriesStartInColliders_m71EDD323AF2BA23D40914047B8C272D5480667F4,
	Physics2D_get_callbacksOnDisable_m08B5CB44B5E54D74AE7B0A6A50CD8A8FA188B3D7,
	Physics2D_set_callbacksOnDisable_m2CA8941C90A6CF849ADF9D75BDD61B2F1478CD91,
	Physics2D_get_reuseCollisionCallbacks_m40CC62BF72BF4209B9D12EBC57D99445E25416FC,
	Physics2D_set_reuseCollisionCallbacks_m5C6A1526FD02CDA3A4E7B043E6D71BBA6F317E4C,
	Physics2D_get_autoSyncTransforms_mE856967A9F58E7000D162549616789A401763BD6,
	Physics2D_set_autoSyncTransforms_mC437FCA611E64BB043B1491A88DAAF53FD8FA88B,
	Physics2D_get_simulationMode_m0AC8A54856FA71E5C7EF3EA9EBE6BCAD5E5F769C,
	Physics2D_set_simulationMode_mD9C9CC25B0DE231C766DF5153C483B689F6EA8E6,
	Physics2D_get_jobOptions_mD32D093B3E3E90AC5E9BE1952AA33497F75C57D0,
	Physics2D_set_jobOptions_mD93E79D72A0FE2DC8942C1DAB33883811CDF47AD,
	Physics2D_get_velocityThreshold_mE0461DC1C6A79BA7E9C39F667A1BBB5C87B7E5BB,
	Physics2D_set_velocityThreshold_mEB1B56B1F09F921C5055B8A3FE2693491E38A6E5,
	Physics2D_get_maxLinearCorrection_mB2799A7F1268585461433C83C2A4651466E6502A,
	Physics2D_set_maxLinearCorrection_mA5C33DB6B77E2F524C3D14DB5A0139DBF0E60438,
	Physics2D_get_maxAngularCorrection_m4CB26CE23F5EB1984644E6D9BE0C628FC635E8FE,
	Physics2D_set_maxAngularCorrection_m0866A481CA14424DFCEB2C3DA649C1442498E6C2,
	Physics2D_get_maxTranslationSpeed_m689DFA51413C049FDAA7A164AEFA9A36B1AD0B14,
	Physics2D_set_maxTranslationSpeed_mE4E03BF9C68FD6CA8EBD49635D15EB6D757DC52A,
	Physics2D_get_maxRotationSpeed_m048E62223C543630FD8323F056B96D49E780BA7C,
	Physics2D_set_maxRotationSpeed_mF0313C41EF5435DAC1396DAB7FE65468284C499C,
	Physics2D_get_defaultContactOffset_m559AF335B2376428A33F6C906EAA92CE60F75FED,
	Physics2D_set_defaultContactOffset_m3E50E7936947179342211F83B856A297DD2A8B4C,
	Physics2D_get_baumgarteScale_m1FD6E6AF2C4B0D0C19CFEEB0C25923BB5EAF0A3B,
	Physics2D_set_baumgarteScale_m616E4309A00189E8808797E3D9A5D848C5CF7730,
	Physics2D_get_baumgarteTOIScale_mE99D516EB998776F53498C7871FFC987A456BB49,
	Physics2D_set_baumgarteTOIScale_mDAFAC60A5EB7E9363D6AD9F4DBAA23DFFEB8DB12,
	Physics2D_get_timeToSleep_m94A3FF08BB18525CA524B193760DF22F70C3CCB5,
	Physics2D_set_timeToSleep_m69B326351764C17EC969653495413D24558A1A34,
	Physics2D_get_linearSleepTolerance_m3432708A180F03F8EA215A77CA94604E02EE01BA,
	Physics2D_set_linearSleepTolerance_m9343E4E0837A04D69D894823B4B133160901DD33,
	Physics2D_get_angularSleepTolerance_m965EEE3B419C350D62866C001CBEC10B07595CE2,
	Physics2D_set_angularSleepTolerance_mE1FC13314F95D5443AB615758DAED38E4717E1ED,
	Physics2D_Simulate_m1DAEEF3777BA7B2F63E5CC230B44B9D2B71793A8,
	Physics2D_Simulate_Internal_m82630D89CBA33438D0123D0AB47996035435EBA4,
	Physics2D_SyncTransforms_mF936634793253A203EF6632454731E44509256D8,
	Physics2D_IgnoreCollision_mFE023CDD902A5068236266648DAB8E9FD8EE387F,
	Physics2D_IgnoreCollision_mA05835421D23AACF69E76082124F4F983B9E2BDD,
	Physics2D_GetIgnoreCollision_m30747D1CA595AF08842285CE07CE5940CDC3A1F0,
	Physics2D_IgnoreLayerCollision_mCA0530AA3A008D092F23E6954CD97447191EB6CD,
	Physics2D_IgnoreLayerCollision_mCCDA76292A31204335084A5BCD87B0BCAB117500,
	Physics2D_IgnoreLayerCollision_Internal_m2749D17AA72083A27379C7F0493F51FD103EE12C,
	Physics2D_GetIgnoreLayerCollision_m0F138C898A8ABF58D86DE9B3B192E1BCD17A6D95,
	Physics2D_GetIgnoreLayerCollision_Internal_m4E2C29E3869F52DD60C1CB3B6B9FC04734F2CFAF,
	Physics2D_SetLayerCollisionMask_mD54B799499738E8C48B439E863D98196DC338ABD,
	Physics2D_SetLayerCollisionMask_Internal_m42B77EAF051667C2CC62D0562E90A154FC251A20,
	Physics2D_GetLayerCollisionMask_mAFE4ED4289B7845D7922AC8FBA4A68AD3518B504,
	Physics2D_GetLayerCollisionMask_Internal_m45BEE83C0CF1C70E780F4B794B4C488C51820049,
	Physics2D_IsTouching_mA4729AD62E4452BD16770E41CCE17CF9E7E1B53F,
	Physics2D_IsTouching_m664FE711127076D22DDAFF3682AE14104EADB84D,
	Physics2D_IsTouching_TwoCollidersWithFilter_mA71FAA16798C8063C022907D45F4BD7CBDBCA465,
	Physics2D_IsTouching_m2A5DF0BA1C6F3B1446F9E1479754B57FCC39C005,
	Physics2D_IsTouching_SingleColliderWithFilter_m9DBB106FBE15A9F6BC167ACF2F96F02B65F08484,
	Physics2D_IsTouchingLayers_m5038718C989DEEAD1233F0AE9661657D0782AC90,
	Physics2D_IsTouchingLayers_mFA5DE2043BBC865BA5A8B0DCF3E780C7C36DF51B,
	Physics2D_Distance_mA0261660F0B40F9A6C721E3FE212AF35AA5AC5C5,
	Physics2D_Distance_Internal_m88733C0CEEAEC293168910C54F587CBB554C5652,
	Physics2D_ClosestPoint_m8BF5092BAC57633EAE27294541B511C572F84970,
	Physics2D_ClosestPoint_m8A374A5EFFC9F9D21CB7B37427199FD8F22B82D1,
	Physics2D_ClosestPoint_Collider_mE2FD8744A1402685CFC793D6A88DFEFFE5FAC77B,
	Physics2D_ClosestPoint_Rigidbody_mB92515591D0C50A1F153AEBCFB3EEE3A7E9F4976,
	Physics2D_Linecast_mA5E8A23A679483445FFF3E44D1D7CCAB2E40B207,
	Physics2D_Linecast_m9812FA76F939EF9A8DA87B6C27F2C91C751533B3,
	Physics2D_Linecast_m319E2AFFF606C8B96831853B7A9FFA4D001F1B07,
	Physics2D_Linecast_m3D8375EDEBA4C03A5453642276B4D0435C6DD492,
	Physics2D_Linecast_m02BC41305491AC0B252045B4EB6B8126321B77B4,
	Physics2D_Linecast_m7E560BE72AD0AAB3163CB5E2CB32D0F513F9F3EA,
	Physics2D_LinecastAll_mF21599922C6E613E2BFAB46BD154532872031D77,
	Physics2D_LinecastAll_mE2D3E20FC1F01416E5BCCEA7F7A3337C89C4548B,
	Physics2D_LinecastAll_m766BC2927DB5A58DA5501B482E3C10F10DCBD8D6,
	Physics2D_LinecastAll_mCF8DCCB1666C15750FD126E4B47E570FD1586861,
	Physics2D_LinecastAll_Internal_mAA71200FC94A40A5479C62713946053F4EB2DB6A,
	Physics2D_LinecastNonAlloc_m167932B702ECF563FE545AD33B7AD65BD2703523,
	Physics2D_LinecastNonAlloc_m8D36A38624CAE318F45B089A70D57B3473D957C1,
	Physics2D_LinecastNonAlloc_mB5FFAA42F1888263A7439D8DAC329C954EAB8C21,
	Physics2D_LinecastNonAlloc_m4A80485AAC646575CD8A6D89C8DBD4F92AA06CA0,
	Physics2D_Raycast_mBECD869F0788D0B0E665BBA3611362E6D5CD2947,
	Physics2D_Raycast_m758FB450001D6EA88A3C51FA2E93D98988B7F630,
	Physics2D_Raycast_m5BF2A59AAEE0B488FB3ECD1D3AF3537FD7789E7F,
	Physics2D_Raycast_mBB6A989AFAFE4B32BEC77543F3121C4ED38A1710,
	Physics2D_Raycast_m03D33CAF9BCCAE7DC2863E263FB8CEFAD87D5E27,
	Physics2D_Raycast_m56E5CBDA49BD64A3A775F4850F18F66A07D1085B,
	Physics2D_Raycast_m65A222170C18F173E06309A784D736E5C6EB32D6,
	Physics2D_Raycast_mB49B869989A276E025785C0FB53443551B3C8B17,
	Physics2D_RaycastNonAlloc_mBF9F41881E335AAEEB6F712BE2213E261DD6683A,
	Physics2D_RaycastNonAlloc_m4AA81066CF537047CC101AF544EA4D5EDDE09357,
	Physics2D_RaycastNonAlloc_mA080DB8D4E20A5454F34F4EFE103898178453DFB,
	Physics2D_RaycastNonAlloc_m71DBE02A3555080CC7F1CB53B4DD198C92B5E10B,
	Physics2D_RaycastNonAlloc_m8A8688A5F6704404E1F168AFB0613CACBEF13264,
	Physics2D_RaycastAll_mA3C18E34CE2F30B92DC336174962C021D437F6C1,
	Physics2D_RaycastAll_m7C461F55BBEF18894404B12851E6C1646A5837A8,
	Physics2D_RaycastAll_m06B06279D4E05F05198F57F4E35DEED4A5CF37E6,
	Physics2D_RaycastAll_mF3F78F9C0D00A0A6C065D59BFEF00125A4282061,
	Physics2D_RaycastAll_mBDA7D58DCA9982DA2C2C0D130C86AF41A8FC42F5,
	Physics2D_RaycastAll_Internal_m784DCB1C06F42FD24B62917ADB7DBFD5B108F82F,
	Physics2D_CircleCast_m****************************************,
	Physics2D_CircleCast_m****************************************,
	Physics2D_CircleCast_m****************************************,
	Physics2D_CircleCast_m****************************************,
	Physics2D_CircleCast_m****************************************,
	Physics2D_CircleCast_m****************************************,
	Physics2D_CircleCast_m****************************************,
	Physics2D_CircleCast_m****************************************,
	Physics2D_CircleCastAll_m****************************************,
	Physics2D_CircleCastAll_m****************************************,
	Physics2D_CircleCastAll_m****************************************,
	Physics2D_CircleCastAll_m****************************************,
	Physics2D_CircleCastAll_m****************************************,
	Physics2D_CircleCastAll_Internal_m****************************************,
	Physics2D_CircleCastNonAlloc_m****************************************,
	Physics2D_CircleCastNonAlloc_m****************************************,
	Physics2D_CircleCastNonAlloc_m****************************************,
	Physics2D_CircleCastNonAlloc_m****************************************,
	Physics2D_CircleCastNonAlloc_m****************************************,
	Physics2D_BoxCast_mFF053711F19B6D267D681DCC4D566C19895AAC2A,
	Physics2D_BoxCast_mCAF4A9373C2C4DBFC1D5AE82E8A386F3EA846DD7,
	Physics2D_BoxCast_mC7CAEAB9C10CE3C4EDEAC492568382EC03B17E92,
	Physics2D_BoxCast_m718BCE5F1787711D626C24A2BF0B2131D7231283,
	Physics2D_BoxCast_mB0A00171A19055FA17E132FCB701242057DAF77E,
	Physics2D_BoxCast_m98CE5227E43693832B864B0FEF488074F15EB3D2,
	Physics2D_BoxCast_m2B8A2BB3C95A0D2713C4BC194E6BDC188F1A94F2,
	Physics2D_BoxCast_mB2D3953BD1F7C7A2E70FFF6AA88A73CAD2F4C9D5,
	Physics2D_BoxCastAll_m19E98B67C5C914A2268285648735E9768FEA89E4,
	Physics2D_BoxCastAll_mAD5C6041843578921B3BC72D3DDD6786AE3CE39B,
	Physics2D_BoxCastAll_m233581366644868226FED3D087A6384E2F940CD2,
	Physics2D_BoxCastAll_m255158E41AE1A490780844CBFDE5DD581A3B35B8,
	Physics2D_BoxCastAll_m0189B37197E856FC6FE9F1EB385308AFF15455BB,
	Physics2D_BoxCastAll_Internal_mDDA9D066D4DB710D00C1384A959526E6FEEAC253,
	Physics2D_BoxCastNonAlloc_m9118D3D07CA9044A98E53FF6A94602666C269409,
	Physics2D_BoxCastNonAlloc_mA01B4DCBC7F877BD2DA5590243DFC579E1BED9DB,
	Physics2D_BoxCastNonAlloc_mA5B3A8436F2B07F18C026090C6DC3B57BF1B6D1D,
	Physics2D_BoxCastNonAlloc_mACB3729249096165C4E8AA31C2017678DFF0FF07,
	Physics2D_BoxCastNonAlloc_mDBD3A56B7CA41F552E3CD8FD53EA46C73D4AC9CF,
	Physics2D_CapsuleCast_m40CCD83C1D8401344A74ACB045BF621A75E00F5E,
	Physics2D_CapsuleCast_m53E848EB0B4648F516AAFD24F271B394807B71F6,
	Physics2D_CapsuleCast_m4EA377ADA6FB4E6BA3B8509DD78C00762B2D2FFB,
	Physics2D_CapsuleCast_m2A1BCB8678F6CBD7F3A3C5B3D26C308BC61AE62D,
	Physics2D_CapsuleCast_mD7FB26D9F6F98B638B70119B3E2C64FC2243A765,
	Physics2D_CapsuleCast_mAC4D4ECDACDDAFBE6AE15CDC69D056A2FF84487F,
	Physics2D_CapsuleCast_m658DB6DD45C901FB1FB3056DE4AAE1F7CBAABB99,
	Physics2D_CapsuleCast_m46826AC10598A4ACF34EE72B81DEB5D30B98E6E3,
	Physics2D_CapsuleCastAll_mE8B151D8BF0ABB897D4AAFFE75170C1CA3B87EDB,
	Physics2D_CapsuleCastAll_m69E9D32B6EF809660F76F26F9AE537A710E921AD,
	Physics2D_CapsuleCastAll_Internal_m96529ACEF33B04023052163C82C9B419244F830D,
	Physics2D_CapsuleCastAll_m3A1ED2A6CE679A240FE85778F6DCECE3D16A9C44,
	Physics2D_CapsuleCastAll_m0C52E18D6E29D019E72838A59A8FD4998B50117C,
	Physics2D_CapsuleCastAll_m91C99656898E4DB2FDD91175DF46153769B5ABDD,
	Physics2D_CapsuleCastNonAlloc_mC6E8B9CC75C13CD99496100ECA65CA061BC18EF2,
	Physics2D_CapsuleCastNonAlloc_mD3359A29468AA60CAF5CA245D2A70AF76268E2C3,
	Physics2D_CapsuleCastNonAlloc_m1CD299E07810C0FA6E30370A784B7962934613A6,
	Physics2D_CapsuleCastNonAlloc_mA27E94A2A1599AB7319A78BD24433336EB5A159F,
	Physics2D_CapsuleCastNonAlloc_mCAF127FCCF26E9D62809C0B4106F63A60FC52D89,
	Physics2D_GetRayIntersection_m14FE964631FC806EC3C7D6EBCA461315961492C4,
	Physics2D_GetRayIntersection_m31BD5D61E0472511E9A93CA4613A0BBB6328D8F5,
	Physics2D_GetRayIntersection_m9246D0A6EE267271AC1AD49928BDDF7FB6FB76E8,
	Physics2D_GetRayIntersectionAll_mE44882D00E63761758A1C10D8579F5AD5A027C14,
	Physics2D_GetRayIntersectionAll_m71F010CB1DF9881A6AEC32123FFD7BFDE32A59EA,
	Physics2D_GetRayIntersectionAll_m1584C9C6ABD1AAEB6235830DC16D05C4566EB80D,
	Physics2D_GetRayIntersectionAll_Internal_m98C9407CC390AA4F560D4BAFE3D35FE06DD3400C,
	Physics2D_GetRayIntersectionNonAlloc_mC17430C3F478EAB0F15D96D10F25CE5E42579A5C,
	Physics2D_GetRayIntersectionNonAlloc_m77BF66F763DE34E2BA96789FC7AC5C2797037228,
	Physics2D_GetRayIntersectionNonAlloc_mB7942B73C8B86F369262FC3B87F080132E7A369C,
	Physics2D_OverlapPoint_mA63A2BD632AACFB5C5F1DF950D8F1A3F268B023D,
	Physics2D_OverlapPoint_mCCA9CD3A57E00F9673E1B93B16A3CEB0D46147D1,
	Physics2D_OverlapPoint_mCEB41A7E99B39E756C8B1AA87E530872F04BF0E4,
	Physics2D_OverlapPoint_mEDB00DAC341DA164AC1F8A39F8B0CD2F18F447C7,
	Physics2D_OverlapPoint_m7DE14703086A7AD5F061A5E915CF0047532C23D7,
	Physics2D_OverlapPoint_m89EA7884794307B5D66A1211397E2AB7DB13478A,
	Physics2D_OverlapPointAll_mB5B72FB2FC0B6EDC86429F1D577DC3AC2BC2249C,
	Physics2D_OverlapPointAll_mCA182087E2277E6796549FB73291D52365ED917B,
	Physics2D_OverlapPointAll_m429BB7AEF1D48AA66C6839618B1B8577A960EADB,
	Physics2D_OverlapPointAll_m9A6E2368C993844FD70E86D120FE52713427B7C8,
	Physics2D_OverlapPointAll_Internal_m6F8CBFEEDEDC77F01667C3EFF1738E1DA5320FBE,
	Physics2D_OverlapPointNonAlloc_m7C642222932C98F44C99419FED236181B807040C,
	Physics2D_OverlapPointNonAlloc_m39B60A0A527550466575AFD0CF7B1149DCB849FB,
	Physics2D_OverlapPointNonAlloc_m518ADBCAC4203558497F28CB046111EC002078BC,
	Physics2D_OverlapPointNonAlloc_m8F88E8CE4FC130F37102880632741EC86D863D3D,
	Physics2D_OverlapCircle_m****************************************,
	Physics2D_OverlapCircle_m****************************************,
	Physics2D_OverlapCircle_m****************************************,
	Physics2D_OverlapCircle_m****************************************,
	Physics2D_OverlapCircle_m****************************************,
	Physics2D_OverlapCircle_m****************************************,
	Physics2D_OverlapCircleAll_m****************************************,
	Physics2D_OverlapCircleAll_m****************************************,
	Physics2D_OverlapCircleAll_m****************************************,
	Physics2D_OverlapCircleAll_m****************************************,
	Physics2D_OverlapCircleAll_Internal_m****************************************,
	Physics2D_OverlapCircleNonAlloc_m****************************************,
	Physics2D_OverlapCircleNonAlloc_m****************************************,
	Physics2D_OverlapCircleNonAlloc_m****************************************,
	Physics2D_OverlapCircleNonAlloc_m****************************************,
	Physics2D_OverlapBox_m474622F2441EA274E257DA3066691831D59BF3B4,
	Physics2D_OverlapBox_mF4F43BA71C46C138C460ACFA8EB479DE73A5E4A7,
	Physics2D_OverlapBox_mAAB6CD150472EA9C82A11EC76A6CA8D47AC16F37,
	Physics2D_OverlapBox_m65C687F8DAFAA971FC8339137DF9EB06FC9C51B9,
	Physics2D_OverlapBox_m31F327A72F057ABBE6086524C31E958E94A04612,
	Physics2D_OverlapBox_mFAA9F46A660117E853BE92A40B31AC48D009A5C1,
	Physics2D_OverlapBoxAll_mBE79BB1EA100B859488D14DE01EA77E68DBD434D,
	Physics2D_OverlapBoxAll_mF62C6ED3122825ECA752E05B1E5993A478FB4C3D,
	Physics2D_OverlapBoxAll_mD1DE6E108996B1C3AFDA73C970DB9EC40D1820FF,
	Physics2D_OverlapBoxAll_m429463A5A1E1782352997776E587344E1BFC92A5,
	Physics2D_OverlapBoxAll_Internal_mF51992B7CCD72EF309445E0F221BFE318A8F05CC,
	Physics2D_OverlapBoxNonAlloc_m25D8546A388FE55FA0BDA8E2FF5F1372173B3B2F,
	Physics2D_OverlapBoxNonAlloc_m97F2CE20703167B8259C4F3A81F3531842748C9A,
	Physics2D_OverlapBoxNonAlloc_m8C840AB888C9B4BD26C34200EC6F53003A605ABF,
	Physics2D_OverlapBoxNonAlloc_m2655E21E15E61CD2EF6634C3CCDBC291810ABF89,
	Physics2D_OverlapArea_mF07C31CAE51943CD56FF6E5BF15C5D393CF95328,
	Physics2D_OverlapArea_m034DDE7D6BA13FBCB10A16DF67EFE20D03BDA777,
	Physics2D_OverlapArea_mD036DDDA70E0C950E197852A137F7A6F9AF6D1AA,
	Physics2D_OverlapArea_m935EC644C187656307D1A6B1A32C764271AD7402,
	Physics2D_OverlapArea_mABBFB9CBC8A7520E1266213D84C42166219FD98B,
	Physics2D_OverlapArea_m6A41E0B588F7E93C3EC0EFA0D636C22F5BF4F399,
	Physics2D_OverlapAreaAll_m1D2FA40228FF1939EF996CBC8994892D9E929CDF,
	Physics2D_OverlapAreaAll_m9FB58BC96BAAD2EBF0FE587CD7EEB17590CBCCFD,
	Physics2D_OverlapAreaAll_mC02FD248A920265B031EEC7BBC3738BF383E2BED,
	Physics2D_OverlapAreaAll_mB3020CD320BC666CB8BBE6A0C788F40BB86B36A2,
	Physics2D_OverlapAreaAllToBox_Internal_m4C49DE7CC580FFD9ED580A59587A406DB521C79F,
	Physics2D_OverlapAreaNonAlloc_mF21DE46F2BBEC159063217E1EDE3D8A13FBAD31B,
	Physics2D_OverlapAreaNonAlloc_mB284FE5776D69957D309DBF794AE892082E08B11,
	Physics2D_OverlapAreaNonAlloc_mA0E00FA95378B98A34B5F487BDC23A6651D64A80,
	Physics2D_OverlapAreaNonAlloc_mCB0B793D41CEFC260D0938494D2517F615916B30,
	Physics2D_OverlapCapsule_m2394A5E5C40D85F26A3133AF81D3F9BD3E4B40FB,
	Physics2D_OverlapCapsule_m6E149086FD94CB6905896124DCA380A1F3C98E96,
	Physics2D_OverlapCapsule_m15829D75998836AC524BC62E4505D4F2AC3BF1CB,
	Physics2D_OverlapCapsule_mEB399B39BBCC721E62FE86C8FD497EF848C7D3B5,
	Physics2D_OverlapCapsule_mE71B76CDE08A6FF5A2C509C61B67FE90AD8C2AB8,
	Physics2D_OverlapCapsule_mB5F1C97565B77DA9ECF6557E3C7E6C9B4EF1C788,
	Physics2D_OverlapCapsuleAll_m45455FD00AC8AC585755C3E58BEBE8C777291A2E,
	Physics2D_OverlapCapsuleAll_mE92E2E79B1291ED0FD646AAE4AF899D5CB6458C6,
	Physics2D_OverlapCapsuleAll_m61B1DB059607624936D23CDD59A158B20B4828B1,
	Physics2D_OverlapCapsuleAll_m1AFC997F280375813086E41C03ECB9FA9E494504,
	Physics2D_OverlapCapsuleAll_Internal_m4623153689BB29553BB982B062E53FAD9126139D,
	Physics2D_OverlapCapsuleNonAlloc_m1722967A255A89F5DA62742470198B0A2B934AE7,
	Physics2D_OverlapCapsuleNonAlloc_mC1363E3F30356B01114CA74EB85FFA9FF57D60A2,
	Physics2D_OverlapCapsuleNonAlloc_mD4136A633CE3349C79F29EC959573346E2237003,
	Physics2D_OverlapCapsuleNonAlloc_m93FF0F86CBC10231DB9A45DEFDA1B708F21AFA6B,
	Physics2D_OverlapCollider_mA9A14648005436C84C1821E950F463978858C7B2,
	Physics2D_OverlapCollider_m4BEEEB54D16532DA655C11956D2841BF7E3421AC,
	Physics2D_GetContacts_mC28E1C5334B88FA5BE7C8B27A2E26E80C8E63F52,
	Physics2D_GetContacts_m2D814C5FD2160C4E2A3809183570776E668D275A,
	Physics2D_GetContacts_m96D00E493F40C12BDB168B35A654F1E49710B5B2,
	Physics2D_GetContacts_m00D61EC0E43AAF0C5F80E9E1F21F711D30851316,
	Physics2D_GetContacts_mE77325CF26F36079539C0BA68F7938A26B722902,
	Physics2D_GetContacts_mE80C6FC68BC4FE23A1196A351AA71923EA52CA6D,
	Physics2D_GetContacts_mCC273E69CBC88D69BB3B669B6671A66995FF4B0F,
	Physics2D_GetContacts_mB92E218B5A4E2DA48FCE1E61AC0D6D4C500ABFEA,
	Physics2D_GetContacts_mC7A1E5C26E431B6A483D4DEF31ACDD173E7B86F7,
	Physics2D_GetColliderContactsArray_m4894B8F334AE9C31C90415A820AF18E8C43C4096,
	Physics2D_GetColliderColliderContactsArray_m90A3D3C4E41F45DABAFCF0156C67B4F3ED094CAB,
	Physics2D_GetRigidbodyContactsArray_m9707D359D2487188FEF996214020A4D4DDFFAF68,
	Physics2D_GetColliderContactsCollidersOnlyArray_m488172E725F99C2C6151514799C721166600DA28,
	Physics2D_GetRigidbodyContactsCollidersOnlyArray_mD6F4BA1D7C82BEFB0CAAAE32C41D3718BE3FB63F,
	Physics2D_GetContacts_mB21ECEECC22EA9146DF2D4C3024979E42783AE5B,
	Physics2D_GetContacts_m6BCCBA4FA3F55DDBA5F106EC56DA9CC80A73ED4E,
	Physics2D_GetContacts_mD20884841609334E50BFAEFA1040D835066909F3,
	Physics2D_GetContacts_m854C1FF9470AC8BA7A956022921BF1B346370F48,
	Physics2D_GetContacts_m47063EDFD77367A3E6B4FE6FC467C9B882954D4B,
	Physics2D_GetContacts_m3E764DB49BFD901D34E638010D55E7048595445C,
	Physics2D_GetContacts_m66385E3A1AA7FB4C5EA0E3579F70328340E4D9BD,
	Physics2D_GetContacts_mA95305B79AAFBB9EE9DCEAD67A8D06115B2523BC,
	Physics2D_GetContacts_m89ADB0CEABE85E8847EF36C5BB84F13FA2551C91,
	Physics2D_GetColliderContactsList_mE5F6778AF9A6123893EF1CA4EF48FAB44E4FB413,
	Physics2D_GetColliderColliderContactsList_m01EB37B797D150CCC61A82158B02249BFA65978C,
	Physics2D_GetRigidbodyContactsList_m70C689D385F9CF99EB0AFC317D318C6953C951C8,
	Physics2D_GetColliderContactsCollidersOnlyList_m38C8049BCC72A6EA96EE505DF7DDF58F3F3E14A1,
	Physics2D_GetRigidbodyContactsCollidersOnlyList_m67DD0E78B0906C48016AA48BA9230C9D4ECD5CCF,
	Physics2D_SetEditorDragMovement_m7B8EB685E7830AB9721C31D74A711CC48B5A7A22,
	Physics2D_get_raycastsHitTriggers_m56BA522C9120E23322BE89DE20DBA325E12C6CB4,
	Physics2D_set_raycastsHitTriggers_mDF85DDCDB251F10A2EF7DEFB070618FACDEA66B4,
	Physics2D_get_raycastsStartInColliders_mB7D1D6FB74A7648B884F4E65738FC578ADC7F6DB,
	Physics2D_set_raycastsStartInColliders_m1F6EB7AF3426F9A6A6618002D76A7BCD0B72E0B9,
	Physics2D_get_deleteStopsCallbacks_m49F1DD7065027A26D2F2DBD73CCC1D5B4C75182D,
	Physics2D_set_deleteStopsCallbacks_m3CB360D093CB4DB105E4716F0A6B89E4A2BE32E5,
	Physics2D_get_changeStopsCallbacks_mDA399A3FBBA1F9F766A12EFAB4E0935A161F706C,
	Physics2D_set_changeStopsCallbacks_mFD72443014BF9B3993C5B7E578F97948E2086BEB,
	Physics2D_get_minPenetrationForPenalty_mB3F7F0B059508F78E0DDA2AAB63C6D2105F0E396,
	Physics2D_set_minPenetrationForPenalty_mA27EC4966871C676181B654D4CB8051A01E61841,
	Physics2D_get_autoSimulation_m4ABB50F5A390E486E94D90FD63EC7233DC6FDA82,
	Physics2D_set_autoSimulation_mD141D9115EE951A2C26BBDBF435A9027489C9A38,
	Physics2D_get_colliderAwakeColor_m715372199F8E0B7F973CC40D9B37677717BAE318,
	Physics2D_set_colliderAwakeColor_m2713A7A8672BB861CCD48DF4EC875822250FB0F7,
	Physics2D_get_colliderAsleepColor_m59D094D33BD3097B1B3850F3ECB1FC176D5445FC,
	Physics2D_set_colliderAsleepColor_mEA547865DED2389BE4528471D781C55B9C0DB8BD,
	Physics2D_get_colliderContactColor_mFA2E931E0542A0EED863EE5114860F95396F3975,
	Physics2D_set_colliderContactColor_m04A364BC0392FF2E356B801EBE90A3E5A7D851DF,
	Physics2D_get_colliderAABBColor_mB19972F3384073A8166A7BEFEA07E049868550A4,
	Physics2D_set_colliderAABBColor_m69BEE817232DABBC5734A6E6144E6F28AF84A69F,
	Physics2D_get_contactArrowScale_mE9B35D01157FAB0C49687861F974DE1D1D3DEEF9,
	Physics2D_set_contactArrowScale_mBC30967682451703774E41801222B3A1BDD9CA7A,
	Physics2D_get_alwaysShowColliders_mB4C5BAFF6B257A8E6A4028F84F22A285E2553451,
	Physics2D_set_alwaysShowColliders_m5EF9CA97CBB3198C422BB202862C4262E4DB6E7A,
	Physics2D_get_showCollidersFilled_mC6C9DC4B6E6BDFB448C40C4B2940DB34344C9254,
	Physics2D_set_showCollidersFilled_mF725908801E37C5695863FA89B2E3E9632502D92,
	Physics2D_get_showColliderSleep_m67A835AD227455DCB3C2CE334E4FDD115E8AE6AB,
	Physics2D_set_showColliderSleep_m349A0BEA05993D9C016F29D315127ED2A5B56387,
	Physics2D_get_showColliderContacts_mA16C0F429F639E791B907C3BB70ABEE56AB889EC,
	Physics2D_set_showColliderContacts_m047A8AB0FCC4EEB45E8705CA4B440D870C243E03,
	Physics2D_get_showColliderAABB_mDFAEFC85A5019DA4A2F60727E4DF369975CA700A,
	Physics2D_set_showColliderAABB_mEFE6CF5CC5E99EB11B0C7A300980DFD21CE94DA0,
	Physics2D__ctor_m326F06BF43B7C08A452B13A6387B32E0029DC59B,
	Physics2D__cctor_m7B7A8EEEE744CE27534A7ADF12F31A4E376544E8,
	Physics2D_get_gravity_Injected_mC06A234085280AC3F4EFB7272A1F7E6AF32D83BC,
	Physics2D_set_gravity_Injected_mF2B40E2CBAAE94177E8F6957ACDC13438E771CFA,
	Physics2D_get_jobOptions_Injected_m8B233F4735F412EF41672FC07918C9E7E95F5035,
	Physics2D_set_jobOptions_Injected_mE55E5DE977E172058CA9EC0B85D84DE4B7E437EB,
	Physics2D_Simulate_Internal_Injected_mD608121626C6B72256029D3A0963FD752A33E1C7,
	Physics2D_IsTouching_TwoCollidersWithFilter_Injected_m08E621C249D1F097084B8623DEC4817710D0B96A,
	Physics2D_IsTouching_SingleColliderWithFilter_Injected_mBCD1C50AF15F47AEC1C106F6D768ACCF7703670C,
	Physics2D_Distance_Internal_Injected_m5449AC9FD4E93CB6A402D77138CFBDFA646543EA,
	Physics2D_ClosestPoint_Collider_Injected_m181F9BEFE025A075E604504319A267A1765E91FB,
	Physics2D_ClosestPoint_Rigidbody_Injected_m1FC6618B4620DA09FCC0CD069A7626E241623987,
	Physics2D_LinecastAll_Internal_Injected_mE6A9EFD2C20707F308CD36D1322E8D1298F41452,
	Physics2D_RaycastAll_Internal_Injected_m5F20D58815E964575E7A0B36C54B735B0CFA7311,
	Physics2D_CircleCastAll_Internal_Injected_m****************************************,
	Physics2D_BoxCastAll_Internal_Injected_m8AF2ACB63B37E176B3F238FFF8C26B13D2D2D053,
	Physics2D_CapsuleCastAll_Internal_Injected_m7EE5B9B4F312D94871622E8C5DB664DFA8A3C917,
	Physics2D_GetRayIntersectionAll_Internal_Injected_mB7BCACD0A0B90CCD4CD4041764BD19B95D553E2B,
	Physics2D_OverlapPointAll_Internal_Injected_m48F78E5620E3B443935527EAFB362DB6E335F311,
	Physics2D_OverlapCircleAll_Internal_Injected_m****************************************,
	Physics2D_OverlapBoxAll_Internal_Injected_m96F44850F41131D44FC98FCC01519679CBFFFC1D,
	Physics2D_OverlapCapsuleAll_Internal_Injected_m27C748FD129A31651276D0BE2F2EB712A33DDFE8,
	Physics2D_GetColliderContactsArray_Injected_m8586CD4E52A4B4240F61C205EA21043764EB9C35,
	Physics2D_GetColliderColliderContactsArray_Injected_m48D0291EAB3C30118FAD5CF3AB5E5080FAE4B9BE,
	Physics2D_GetRigidbodyContactsArray_Injected_mE733D6DB0550EEE6C30F4F247890A99A250D3CA6,
	Physics2D_GetColliderContactsCollidersOnlyArray_Injected_mFFD7CD1234F631CB63E04F9CEC06F9B3FEECDAC0,
	Physics2D_GetRigidbodyContactsCollidersOnlyArray_Injected_mDF4F1FB68646571D136002565F3442CB98E64ECE,
	Physics2D_GetColliderContactsList_Injected_m5D0FBB67A27D3BDEB48CC4A24B6CC5B43F800497,
	Physics2D_GetColliderColliderContactsList_Injected_m7BE022C3A68676CCE5C2B61E4C39EBD056357825,
	Physics2D_GetRigidbodyContactsList_Injected_mB9EA4EE75ACE9C8C326A38E467389D0BC7ED1DD9,
	Physics2D_GetColliderContactsCollidersOnlyList_Injected_m7A4092C37BA2EA3F26C65FF517302F0AA06974FA,
	Physics2D_GetRigidbodyContactsCollidersOnlyList_Injected_m876A84E6515A77A68CDC21E6F6B264444DDB0A1E,
	PhysicsShape2D_get_shapeType_mBA55638729252854535E3E9279F7A006B610B01F,
	PhysicsShape2D_set_shapeType_m8A00238C6DFD5ABF398700C86492296DB9DC6D9B,
	PhysicsShape2D_get_radius_m4300D92A9F9523277976419369F0A14DC75AEF8B,
	PhysicsShape2D_set_radius_mF96363A2D7B8112F2994D38A1113C8FC441FF864,
	PhysicsShape2D_get_vertexStartIndex_m23782C197FC0521A6DC3A4E0F115DB477042EC40,
	PhysicsShape2D_set_vertexStartIndex_m289E3881B77BEBA99E28B698AEEBFFC0F4A5DD00,
	PhysicsShape2D_get_vertexCount_mBB09936295C475647B8E92C1464F4C2F3CA7A8D2,
	PhysicsShape2D_set_vertexCount_m982167474864E44AA5DC6ED6CFD854E757B6702A,
	PhysicsShape2D_get_useAdjacentStart_mF9EE25B1A542E807AA8AFB722F79D7CE0937271B,
	PhysicsShape2D_set_useAdjacentStart_mF419418F7F43E9F30BA926E373730A3D4DE68D4E,
	PhysicsShape2D_get_useAdjacentEnd_mE04E765CF666B95B6C49E34A38ED2F056B9E4CEA,
	PhysicsShape2D_set_useAdjacentEnd_mDC730349B1B72E2B095BC5A2858BC34C2EF18D2B,
	PhysicsShape2D_get_adjacentStart_mC99F496D28E98CD8D59425D4815275530414F94E,
	PhysicsShape2D_set_adjacentStart_m6EDB2AD4B54FD968C0E6E3C783045FA121B5D081,
	PhysicsShape2D_get_adjacentEnd_m8C5EA386C240C0A123C12C849892922D8D1F11DA,
	PhysicsShape2D_set_adjacentEnd_m6646CBEFCA64FF1FA84B2E01B6C18AD6F351CEC1,
	PhysicsShapeGroup2D_get_groupVertices_mECFB54F79371D17E73D1B49F38140C0D604177C8,
	PhysicsShapeGroup2D_get_groupShapes_mD42F5717D5EEE161C0B9A75335FA0B06D57DDA9F,
	PhysicsShapeGroup2D_get_shapeCount_m238BE168C9D3579FEFAD12AA54EA212ABC1D0279,
	PhysicsShapeGroup2D_get_vertexCount_mC63C729E4B4DFE1C3E6F0233792A4DD71DB1EEEE,
	PhysicsShapeGroup2D_get_localToWorldMatrix_mC66CC5F526DA0099C161B8EF715B92F214BA837D,
	PhysicsShapeGroup2D_set_localToWorldMatrix_mACC64B52039118923499B33AA6626518DE88211B,
	PhysicsShapeGroup2D__ctor_mDD9B2EE22BB914D23767D8BDEF5517ACA05A7ED8,
	PhysicsShapeGroup2D_Clear_mD762D71D19D0D0166E69214F1E291566C6F8B3F7,
	PhysicsShapeGroup2D_Add_mCC29A1C9F197B06990FB247B9395EC14B4153F31,
	PhysicsShapeGroup2D_GetShapeData_mAC27C534537278D2CDD957396AF28016D3D11B17,
	PhysicsShapeGroup2D_GetShapeData_m1D6FB486CBDEFFAFF8B9744F9918C12F659589CA,
	PhysicsShapeGroup2D_GetShapeVertices_mB9897694633E9F85AFBFA7E50C195071682D7491,
	PhysicsShapeGroup2D_GetShapeVertex_mDF62F80AF6E5638C9A80CC9838A4D96DC28EF29B,
	PhysicsShapeGroup2D_SetShapeVertex_m03F4350C37785C0EBAE6550D8408643B66B75FBF,
	PhysicsShapeGroup2D_SetShapeRadius_mC4439610949D4B0BCB2BBB5EA95A7C48EDE4053B,
	PhysicsShapeGroup2D_SetShapeAdjacentVertices_m76B2A13301956C154FBFA36B3D7CAAB557FCD266,
	PhysicsShapeGroup2D_DeleteShape_mB1ABBA70CEB414E665A3EF5482D548B09C6EA968,
	PhysicsShapeGroup2D_GetShape_m4B5E6DB4EC57A2FCACA24D750B08CDA81B4C06AC,
	PhysicsShapeGroup2D_AddCircle_m****************************************,
	PhysicsShapeGroup2D_AddCapsule_m6F1AE5239142A78E8919F0EA2EE9D8B5D851721A,
	PhysicsShapeGroup2D_AddBox_mC3C8A7197164A468BD5CE7FF1051CB80FB075D6F,
	PhysicsShapeGroup2D_AddPolygon_mB6FFD23403215D884BB0B8C71A7E0A55E5D8BCDB,
	PhysicsShapeGroup2D_AddEdges_m296E86FB14123762B2ECAD3074486BC3174E3A68,
	PhysicsShapeGroup2D_AddEdges_mB9C431A763B4F67E63CDE229D9B52D6FA13A41F1,
	PhysicsShapeGroup2D_U3CAddBoxU3Eg__RotateU7C28_0_mE6CE94A5D2B98F0673D32F74457E3B963143D502,
	GroupState_ClearGeometry_m4A1E29A3DFE71E4426BD2F02D7067C8E32BEBE8F,
	ColliderDistance2D_get_pointA_m55571F64432B8539AF715F91AE9F01E039B1BD3B,
	ColliderDistance2D_set_pointA_m4334AB6925BF3CA93F3993E260CE986702C0AC8B,
	ColliderDistance2D_get_pointB_m8C42A60CF6DFD3257CA1EF994BAE5DE2956F732A,
	ColliderDistance2D_set_pointB_mF53E3F9EA595B627B64344411163212631DF42D0,
	ColliderDistance2D_get_normal_m751A946B91F2A85A5F37376D4C4BC6BD42B437ED,
	ColliderDistance2D_get_distance_m6754976414E63C5F8B9493FDECA2210F5339F3C7,
	ColliderDistance2D_set_distance_m1CFF30A40C19AE4F4ACB11B0B157B1FDE8DD56E4,
	ColliderDistance2D_get_isOverlapped_mEFF81A407A9EF37A3800E1050B9D9C789DF3F0D3,
	ColliderDistance2D_get_isValid_m044D48E09BD8ADC4A01708B1D9478E43098EDF93,
	ColliderDistance2D_set_isValid_m356B0AEAF9421D48DD98304D8460893FE44C82DB,
	ContactFilter2D_NoFilter_m15980FED0667A151078679DF555638BEEA7C01DE,
	ContactFilter2D_CheckConsistency_mD918F11F977EA35E87CF491F7AE8794F5D01DF72,
	ContactFilter2D_ClearLayerMask_m86FA33B78DAAC6E04A4ED62F73CDD3D34B0DF68A,
	ContactFilter2D_SetLayerMask_mC3FBC2D806C1A3ACB2D060CE48F8157505E42F9B,
	ContactFilter2D_ClearDepth_m6CBAEF48B84E53079CC628E186EC6DD940C3151E,
	ContactFilter2D_SetDepth_mE614DDDDAEA489D150E61D2DF8104F9292236F18,
	ContactFilter2D_ClearNormalAngle_m7C7A157AF9CF67AF3D1094770B6B910BFEDC23D3,
	ContactFilter2D_SetNormalAngle_m9A737032D43865398C15881FF1764694D0601B33,
	ContactFilter2D_get_isFiltering_m993424DE9FBBA01394167C8FC546E4526C564A74,
	ContactFilter2D_IsFilteringTrigger_m48921B2B2B5254FE3033757321224C14E7AB9884,
	ContactFilter2D_IsFilteringLayerMask_m353947849DAF100E9125B0D92349B4763C76139A,
	ContactFilter2D_IsFilteringDepth_m455CB30F384EFEB68951CFEA42F26D2F15BC8886,
	ContactFilter2D_IsFilteringNormalAngle_m67548CED7AB9D9029B88C405A23F67307E95EA8F,
	ContactFilter2D_IsFilteringNormalAngle_mCBCAD097FC2B202E4C54A8A2EC13883EEE652C64,
	ContactFilter2D_IsFilteringNormalAngleUsingAngle_m30C52106865DBBAD20B012FD86699324E7BA2CC6,
	ContactFilter2D_CreateLegacyFilter_m7DF755B13D055FA510CB7F57A2CA5B45EAD161E2,
	ContactFilter2D_CheckConsistency_Injected_mE330C47C2583E5057F24B9E7B2D8F4B63B2B7A7C,
	ContactFilter2D_IsFilteringNormalAngle_Injected_m01CFD4929268D12F47285658298A1B470E985732,
	ContactFilter2D_IsFilteringNormalAngleUsingAngle_Injected_mFFAFE8C92A8A6FE67AED76023A5859B2F8D657B5,
	Collision2D_GetContacts_Internal_m2DEB43D363533FD6E88C889F7C3B914636EB2643,
	Collision2D_get_collider_m90FA98F6619E9F1E2EFAE8132EDB6ECA1A2C4F37,
	Collision2D_get_otherCollider_mE83E8BC80234672284EB1934AE9A70BDD52A9C4B,
	Collision2D_get_rigidbody_mD763F2D56BF538A94AD62379D22E42335A67B60D,
	Collision2D_get_otherRigidbody_mD6A52043DACEC341356C618452FA48B95786CABE,
	Collision2D_get_transform_mC59737F246B2DAFF2AB4F6322664C87B28605CC7,
	Collision2D_get_gameObject_mE4B3D56F3477F7D2D6D7B217DF5488DA1D13204C,
	Collision2D_get_relativeVelocity_m1F0BB90BC73FB0A0EA27212D832BB3F26D4C004A,
	Collision2D_get_enabled_mBA3D8BA274E40F606E8356C64A78896B6D2D77B6,
	Collision2D_get_contacts_mA4A1EDCC2D82407E30EC63689C7858C684462E68,
	Collision2D_get_contactCount_m7656255B45A9D6E37F4EE4258CB33C9F6C6FE231,
	Collision2D_GetContact_mB48B0E46D9183FAE6635D3C7BDB09F11ED1B2C95,
	Collision2D_GetContacts_m7E7B9C9FDCEB63196B8A10EFF2536272EC69758D,
	Collision2D_GetContacts_mD93B838E59707D5AD96A18CDCE0A6468AB068E23,
	Collision2D__ctor_mC61096E6215EB1B820F55F1163D92CBBCF78FEA4,
	ContactPoint2D_get_point_mFF9B7395F63E748507C85166F3EDC218B8740396,
	ContactPoint2D_get_normal_m421147AFFC1A029B4DEC775C6B9197919ED93D21,
	ContactPoint2D_get_separation_m70174AAA4EC5B1857607115B25BED77BA142EA5E,
	ContactPoint2D_get_normalImpulse_m601808AD6F4E1F81E4C9E53401556C465D161288,
	ContactPoint2D_get_tangentImpulse_mC45ADFB72CA45EE4C430598511DB6534AF3F5CB4,
	ContactPoint2D_get_relativeVelocity_m0DAD8E66E82BE6A3133618EFCB2CA579FD0F5D7D,
	ContactPoint2D_get_collider_mCEC4BBE3C9CF0977C3EC5D529C2D5B664180768F,
	ContactPoint2D_get_otherCollider_m1892E5E5AA0032610E8252FC371654E4198A7779,
	ContactPoint2D_get_rigidbody_m28CDDD12EB3F7C06D05126C1ECA3AEA9594E1FF3,
	ContactPoint2D_get_otherRigidbody_mAE4893B039030B7AF7B645D2EEA0BD0F142CE6D9,
	ContactPoint2D_get_enabled_m1AC0022C616EBDD9012C25A6C9FD21766E87686C,
	JointAngleLimits2D_get_min_mABF1DD59D9AF0093C3A6C3EA039B2CE7B3F0AC1A,
	JointAngleLimits2D_set_min_m9EBCBBF3B7D4126158DB2405A2C2333DFFE48B29,
	JointAngleLimits2D_get_max_m2673C23C93D2802B61332C9BA58BD64E3A76BCC8,
	JointAngleLimits2D_set_max_m8264B45D23F709BA60BEF19645569D105A623D6F,
	JointTranslationLimits2D_get_min_m76573A6341DBDA6DA716A910851A19DBA7C6F773,
	JointTranslationLimits2D_set_min_m68279D0A2371102C82CC0511E4A8AEE9D697EDD4,
	JointTranslationLimits2D_get_max_mA2360BA3CD1A35E2E6C297D1EE478FD60757A699,
	JointTranslationLimits2D_set_max_m29EC807D57EC6702250B59CA07A207971E6D164B,
	JointMotor2D_get_motorSpeed_mDD7B3F1E134AB8367191EAFEA683B82AC4952163,
	JointMotor2D_set_motorSpeed_m080F930D6EC3A5BE6348C409B9115664E464B480,
	JointMotor2D_get_maxMotorTorque_m911E081510303C1B7763686891ACFCEF6748C6EB,
	JointMotor2D_set_maxMotorTorque_mFB41FE9052B411B7C43C29DE4978E04AD9C748AC,
	JointSuspension2D_get_dampingRatio_m436AF1D3DE8C46C1F548E08AF83A0C0C546CFD25,
	JointSuspension2D_set_dampingRatio_m6E706B4991620D9F8E54014779A788DF55A45DD4,
	JointSuspension2D_get_frequency_m62ACA94DE93973E8874544AF88BFC5C56AE7F0F3,
	JointSuspension2D_set_frequency_mE8B115A66AD8FADFC3C035075A3662C0C16E89B6,
	JointSuspension2D_get_angle_mF95F89C72EFF15D5CC37CFFD93FAF8BD8437C0E1,
	JointSuspension2D_set_angle_m27413F565F50EEF07F617ED60539527175B73BB5,
	RaycastHit2D_get_centroid_mEA7A6ACCFE6C0E7566B0C177A54A750EC554C704,
	RaycastHit2D_set_centroid_mFB5F56330BAA9C8CE547AFE75648FD8E426776D8,
	RaycastHit2D_get_point_mB35E988E9E04328EFE926228A18334326721A36B,
	RaycastHit2D_set_point_m13D917ABD5F8FEB291FED13D2DB2A2481E085FAC,
	RaycastHit2D_get_normal_m75F1EBDE347BACEB5A6A6AA72543C740806AB5F2,
	RaycastHit2D_set_normal_m36B4A488824FD1E4D0F47AE13211C4D773FE6799,
	RaycastHit2D_get_distance_mD0FE1482E2768CF587AFB65488459697EAB64613,
	RaycastHit2D_set_distance_m652B2B50F02583A4FAD019190AEFA1D402B0FA33,
	RaycastHit2D_get_fraction_m9BF416582F5C4D5FC8D93E5DA57B4CDC64E030BE,
	RaycastHit2D_set_fraction_m3FE49691CA64CB6EB030AE87C4FAEA28285C1062,
	RaycastHit2D_get_collider_mB56DFCD16B708852EEBDBB490BC8665DBF7487FD,
	RaycastHit2D_get_rigidbody_mA7C26ACF22912C14CC6C8B1D7C50F38BCF5096B8,
	RaycastHit2D_get_transform_mA5E3F8DC9914E79D3C9F6F3F2515B49EEBB4564A,
	RaycastHit2D_op_Implicit_mBEF99A746116664D68B1398D58CA247550980A11,
	RaycastHit2D_CompareTo_mF4665ADD9F8B212987443C052ADE73E8C0DF2612,
	PhysicsJobOptions2D_get_useMultithreading_m8967EDBBDC51011BE1C0FB7FC437643F8E0E8DFA,
	PhysicsJobOptions2D_set_useMultithreading_mF475BF5CDDBBEA27D50F2B60E00CC9BCAFA016A9,
	PhysicsJobOptions2D_get_useConsistencySorting_m583424FC6BACAB1E6161F3BDEBAE102978630CDA,
	PhysicsJobOptions2D_set_useConsistencySorting_m4190351CA61E03766E72435E437DCB23CE1408CC,
	PhysicsJobOptions2D_get_interpolationPosesPerJob_m7F3C3C05C4AC1770E342EA6D6F63B709A610DFB8,
	PhysicsJobOptions2D_set_interpolationPosesPerJob_mA764F435978E7BD6005F1B560516B7B9AE2A123E,
	PhysicsJobOptions2D_get_newContactsPerJob_mCA477A50F149B5DADC5E2A772E48B746638DC761,
	PhysicsJobOptions2D_set_newContactsPerJob_mA2C104942792A9188ADE151D5221FF18F3EF052B,
	PhysicsJobOptions2D_get_collideContactsPerJob_mC27C3A2243ED724C4D147C210595716AAECDC217,
	PhysicsJobOptions2D_set_collideContactsPerJob_m199A8D06195B95564503429B109C7E32B3E42138,
	PhysicsJobOptions2D_get_clearFlagsPerJob_m0F1299FC15FDAE5A030772D1D1A01E9FA522AC94,
	PhysicsJobOptions2D_set_clearFlagsPerJob_mD198700A6FEA3696DC7A8F54E043A337B18D97B3,
	PhysicsJobOptions2D_get_clearBodyForcesPerJob_mCA5DC9F62EC9BCB44D9AD7C50F5D72DEA79CB560,
	PhysicsJobOptions2D_set_clearBodyForcesPerJob_m85773E07D803A633C58928F66588ED04FB8EE78F,
	PhysicsJobOptions2D_get_syncDiscreteFixturesPerJob_m44F03D367416DB932C675A23B30BA8CE81DED94E,
	PhysicsJobOptions2D_set_syncDiscreteFixturesPerJob_m59988BD56ADE10BF7D55B1E3BB6A505EE5290DEB,
	PhysicsJobOptions2D_get_syncContinuousFixturesPerJob_m3AA97386BBD0EE4BE60232DA0940D1C0B00F6549,
	PhysicsJobOptions2D_set_syncContinuousFixturesPerJob_m8F99FB132D69D00C86FFA1788CF8B084E929DD65,
	PhysicsJobOptions2D_get_findNearestContactsPerJob_m989A3ACBF42B19E2EA50E2414A8EF3A876B1C7D9,
	PhysicsJobOptions2D_set_findNearestContactsPerJob_m7A2EC665BD757F95E86BC950E91BA37B1AC2588A,
	PhysicsJobOptions2D_get_updateTriggerContactsPerJob_m5756767F8BA939BF8834E3F3F244BD0AFFC3E8CA,
	PhysicsJobOptions2D_set_updateTriggerContactsPerJob_m200A1E3BC34797C4C40B6F375AE0E492892CBD66,
	PhysicsJobOptions2D_get_islandSolverCostThreshold_m7214005F53C4136C65DDBB7BF40352CF5C69F307,
	PhysicsJobOptions2D_set_islandSolverCostThreshold_m08171184FFAF1C0C8B268BC15FAA961719906D35,
	PhysicsJobOptions2D_get_islandSolverBodyCostScale_mBB11308FF9BB281DD21B800FD02701F9AFC5E881,
	PhysicsJobOptions2D_set_islandSolverBodyCostScale_m0F5568713C8A7D056614EAECA50F1EC42DDA6E4C,
	PhysicsJobOptions2D_get_islandSolverContactCostScale_mB24A44A346D134D75B93D8F35477D12F2D5E29F8,
	PhysicsJobOptions2D_set_islandSolverContactCostScale_mB1848684FA85C5F324896437840B4712CC57B162,
	PhysicsJobOptions2D_get_islandSolverJointCostScale_mD23FABF4C84E1ED3456E8E9AF0E4EF2902E769C6,
	PhysicsJobOptions2D_set_islandSolverJointCostScale_m85293EF48F13BABF165CA4C1B04960F0C0C70A38,
	PhysicsJobOptions2D_get_islandSolverBodiesPerJob_mBC375DF0FB397948AAB54BC4ED7593377F989A97,
	PhysicsJobOptions2D_set_islandSolverBodiesPerJob_m927451FE8439EBCE47532EB6B0BFC1546AD63294,
	PhysicsJobOptions2D_get_islandSolverContactsPerJob_mD484AFA46C160C184837E58C462CA9E19E28D5D3,
	PhysicsJobOptions2D_set_islandSolverContactsPerJob_mF162C21E0D8466EFCE0AD8D4ECDF4646497DDDD3,
	Rigidbody2D_get_position_m07070C4416DFE2229070F95B349E411AE4869276,
	Rigidbody2D_set_position_m03C92F26F561D48050FBA840754F584AA7F415EF,
	Rigidbody2D_get_rotation_m3F6D0437733C0D9E4A3DF4196F80D66B20E563AB,
	Rigidbody2D_set_rotation_m2B5A1F0A973B47E2CFED94B3C01CA523938BF8A0,
	Rigidbody2D_SetRotation_mC4D1C67247B4A738E97FA0A0CB581D575652E786,
	Rigidbody2D_SetRotation_Angle_mA98FB4786535AFD6FA57A86F7379464064C79860,
	Rigidbody2D_SetRotation_mEA618D515380273683B77825C078D83C52E34D1B,
	Rigidbody2D_SetRotation_Quaternion_mF272CFDF0963EFA7C7BC3DB3C767BB4972750D53,
	Rigidbody2D_MovePosition_m7F24879BB78DA0587168B257C56DCFD248A90895,
	Rigidbody2D_MoveRotation_m33BC3C46E2584EB32C6E440AA4E9E58BEEEE8256,
	Rigidbody2D_MoveRotation_Angle_m067E8C2931E2C4BF400FC012A2D86A459D18E76E,
	Rigidbody2D_MoveRotation_m5AE2B8CE8F63C2417F584F4D13A745492E61732B,
	Rigidbody2D_MoveRotation_Quaternion_m47F8E2E6EF8086B3FB5D94A709D96DA25EAE7CC7,
	Rigidbody2D_get_velocity_mBD8AC6F93F0E24CC41D2361BCEF74F81303720EF,
	Rigidbody2D_set_velocity_m9335C5883B218F6FCDF7E229AC96232FCBAC4CE6,
	Rigidbody2D_get_angularVelocity_mAD2505FB1F8C9E1A66D1EA8F8680D14380BFC58D,
	Rigidbody2D_set_angularVelocity_mFC06FB14E69DD4847F27E614900D22317AA5A390,
	Rigidbody2D_get_useAutoMass_m793637DCAFA651C48E5ED6755E55E8A07492CF76,
	Rigidbody2D_set_useAutoMass_m13EA3EF3D9FBA4972B7E5FDBF601AE03AC644C69,
	Rigidbody2D_get_mass_mC8854F0E26585A11D4420B9F5570AB4E75192AE1,
	Rigidbody2D_set_mass_mC2E292A62264D605120FC65C713BB35503CE35B1,
	Rigidbody2D_get_sharedMaterial_m6C6FB14A03720BF280C2AD7C22A4419F5EFBF966,
	Rigidbody2D_set_sharedMaterial_m14C05C4028C6EAD8BC8EF3C75B5EADD858161285,
	Rigidbody2D_get_centerOfMass_mE0719CD36314804E9BF2D0924B7A2C3076917F4C,
	Rigidbody2D_set_centerOfMass_mF30D18DD82C9093A2961FFB0A7D64396012B6238,
	Rigidbody2D_get_worldCenterOfMass_m31238BC9455BE4EC3CDFF599B3CB571EFC59CD6F,
	Rigidbody2D_get_inertia_m6A5C0D11B5DE93AFA0C0A5D076448BFE64776BCA,
	Rigidbody2D_set_inertia_mC85358688D8203A76801CDCD428247BB17C5F8E6,
	Rigidbody2D_get_drag_m0C65BDB12E059C56C2B5E1A9C0906F5A6AE0E92B,
	Rigidbody2D_set_drag_m42A0E181D64FD374A047E8ABE351207847B324D0,
	Rigidbody2D_get_angularDrag_m017C7872E21D165A492C49BEE89CCBC9DF249B54,
	Rigidbody2D_set_angularDrag_mF93603D37CFA437F6899EE53F711A3A7B5D0B599,
	Rigidbody2D_get_gravityScale_mCFA8E159F51B876E16EEF634A63415F7051AFF44,
	Rigidbody2D_set_gravityScale_mAFD1A72661304467D20971BBCAA7E04B418F80FD,
	Rigidbody2D_get_bodyType_m20709275F3D8215592B2B89736AA8DDD2BF44ED1,
	Rigidbody2D_set_bodyType_mE2FAC2D78B06B445BD2AD58F87AC5B1865B23248,
	Rigidbody2D_SetDragBehaviour_m39034E718F4E73405D03F5A3D56911D1673B96CF,
	Rigidbody2D_get_useFullKinematicContacts_m6F5EA23D8B1927F12FEAB156C4E80A8A53D98844,
	Rigidbody2D_set_useFullKinematicContacts_m2D8164631CE3B48CA401DF389F49902F272063CD,
	Rigidbody2D_get_isKinematic_m41BBC60A072047F850097C0391A002935DD277CB,
	Rigidbody2D_set_isKinematic_m7C68AB4CFB6D301F0EDF0BFF66FB121ED3CC7853,
	Rigidbody2D_get_fixedAngle_m768B00A05AB762B707C2596A945D2C55986CC907,
	Rigidbody2D_set_fixedAngle_m000844F6B9A77865D80757A5A63F908F97934254,
	Rigidbody2D_get_freezeRotation_mDC5E450309F120AA15C1D47DF23AF025E7218E05,
	Rigidbody2D_set_freezeRotation_mFD0A56A3ED021B714BE05CE0EB22E954ED74E9B9,
	Rigidbody2D_get_constraints_m5699129452DF94C5E15E67BCB2497C91A5E9FD42,
	Rigidbody2D_set_constraints_mBF02A56E20BD497E3D291931E0AABB850952B238,
	Rigidbody2D_IsSleeping_m349F570CFCF33C356D621D1D23A8336CF12AF99B,
	Rigidbody2D_IsAwake_mA27A80CB26CC0D38F7A4EB6B441E0C9DC3BDB6DE,
	Rigidbody2D_Sleep_mD0A38FE95ECB010C95AD99DC9C29308273186D30,
	Rigidbody2D_WakeUp_m9660AAE58AC940973098BDABD1F54178EDE91753,
	Rigidbody2D_get_simulated_mA8CC81D52F0F1ED08A88D82A808C27905909127F,
	Rigidbody2D_set_simulated_m38E0BD6581E907DD87059034C4B2E8D47BBFE71D,
	Rigidbody2D_get_interpolation_m3A85873C44DB8123E68DB38B1CC3DCF3FD2CD083,
	Rigidbody2D_set_interpolation_m4914262B161A76DD061969667C0D412A8C93A994,
	Rigidbody2D_get_sleepMode_mA66CE7D1CF938D72CBE0C7CD007F85563DE3CC71,
	Rigidbody2D_set_sleepMode_m4E949F941B1D0950E9F30E93298ADF1B464A2362,
	Rigidbody2D_get_collisionDetectionMode_m2FECB466D3B088FA62C593840164053FE662464D,
	Rigidbody2D_set_collisionDetectionMode_m87EC5B73B166FFA59434D7418643ADCBAD33C609,
	Rigidbody2D_get_attachedColliderCount_m3CA3496C4A5740059D36AEB2B64455778E03898C,
	Rigidbody2D_get_totalForce_m8C196D829C0A3B1C2E652A2ABF6970EFD267377B,
	Rigidbody2D_set_totalForce_m201E1FA6506059ACFE21D5C1298BC93DA8E8A4B7,
	Rigidbody2D_get_totalTorque_m0EDFDA6948579789111F370580D02B4679C137F4,
	Rigidbody2D_set_totalTorque_mA7B05135AE11D9D4AF692A5F36BE3E7B15BBE0B0,
	Rigidbody2D_get_excludeLayers_m257E2A564E576213DEDE8F3CEB8724B6A401C957,
	Rigidbody2D_set_excludeLayers_mFF591036C53B929848E18808909CFD4E775370DA,
	Rigidbody2D_get_includeLayers_m2A16EF9EC595A508C30198B8A3F2801C6BDF544F,
	Rigidbody2D_set_includeLayers_m98752BFE99ABC921C4A7A7D824021312B34DF7B2,
	Rigidbody2D_IsTouching_mC291AF31EE1712C3AC589A9C974044E6E6329453,
	Rigidbody2D_IsTouching_mBA38654AD27DF4E2C813467BA7594E0322B54232,
	Rigidbody2D_IsTouching_OtherColliderWithFilter_Internal_mE1718D4825E6D0899F8E00C494537AA21DBADFCC,
	Rigidbody2D_IsTouching_mA69870E78416F4DC0B381E5D862BEB5778FE6361,
	Rigidbody2D_IsTouching_AnyColliderWithFilter_Internal_m5EA344EEC304D5C007495E561D3106CEE23A74EC,
	Rigidbody2D_IsTouchingLayers_m7EB57DFAE8657E011F7A48BFF71FE33787463600,
	Rigidbody2D_IsTouchingLayers_m44E19521A4706926F284EE884221AF03814D197E,
	Rigidbody2D_OverlapPoint_m68E05AB2FF577198CEA583D385328353771BBB4A,
	Rigidbody2D_Distance_m4CC5A9540203C73E5A06A1AEDE4BA31A5082EC46,
	Rigidbody2D_Distance_Internal_m4E0EC9AE7150FF9015642CFD88DA069C97E63BB1,
	Rigidbody2D_ClosestPoint_mCBD378272B44E1ABDCF8642D646DCCE5777AB1DC,
	Rigidbody2D_AddForce_mC635C76F94D56891007700CA0E653EB269E955CB,
	Rigidbody2D_AddForce_mDD5CAE0137A42660C2D585B090D7E24496976E1B,
	Rigidbody2D_AddRelativeForce_m1C478BC5716586B81D014884CDFEC9AA0B8FCC9C,
	Rigidbody2D_AddRelativeForce_m503B7B6867FC6B2D95EECAAB5619F802669EBB16,
	Rigidbody2D_AddForceAtPosition_m36B89D72D5C6C5B6D65A62E1F08FAE74CFC0356F,
	Rigidbody2D_AddForceAtPosition_mB561295A966E534307C1AE0D2122B40C49FEB15E,
	Rigidbody2D_AddTorque_mB126101CF0ECA5CC8C284ED06132B24FD8885029,
	Rigidbody2D_AddTorque_m6B732A4BBCC6DFE0A5A8160FAF4DD292C76AC4D2,
	Rigidbody2D_GetPoint_mEB7724F124CDA5B16967A910B14E08E09359B311,
	Rigidbody2D_GetRelativePoint_m46BD12038126AC8080C12E60ECE6B21C55B9B39D,
	Rigidbody2D_GetVector_m65F4AA27A0B63570EAA17EFEED21F0581F3D5821,
	Rigidbody2D_GetRelativeVector_m4F74ACE65537B062816EF1850F35B336C52A7855,
	Rigidbody2D_GetPointVelocity_m29EB9C7A3665AA3E5EC4A591DA69CEDD1AA3F7CF,
	Rigidbody2D_GetRelativePointVelocity_m07BF714FE7CE38F014C5C64C9F41BCC566126FFD,
	Rigidbody2D_OverlapCollider_mCEDD6B47DB09AAF0CD0CFB6C98240D833CEBF2A2,
	Rigidbody2D_OverlapColliderArray_Internal_m556DCF27FB3A24C052FDE39916498EE91775B31C,
	Rigidbody2D_OverlapCollider_m10FD5DF939CE46845BC85AF84D2375CDD2D6970A,
	Rigidbody2D_OverlapColliderList_Internal_mAB0ACE9BFBD56A76CD9D376EB904CCF15E0CA043,
	Rigidbody2D_GetContacts_m24AFB88D473299B6208DC632B7599C447AEDEE73,
	Rigidbody2D_GetContacts_mCDFA9FC88E26BC13C75F42C967FF7F140A700F31,
	Rigidbody2D_GetContacts_mBB0F8AFD57488656888CA682F46ACC1BD9D888D0,
	Rigidbody2D_GetContacts_m044ACCAD7854D1D891DB7A8F4C529BA34F21B42C,
	Rigidbody2D_GetContacts_mF99B9F51D0B327518F12AC03510E9D256F1170E6,
	Rigidbody2D_GetContacts_m1F0CCF4E1F3C76937617A211A72F56A67C26A9FB,
	Rigidbody2D_GetContacts_m5F96E4804EA0DCBF73CE59D88DF0AC33979DF4C5,
	Rigidbody2D_GetContacts_m912229FE8EAE8FBD1361F05CD2EE658466745D40,
	Rigidbody2D_GetAttachedColliders_m8BD4BF172EE5A599BF0E7403AA3D22BCA962717A,
	Rigidbody2D_GetAttachedCollidersArray_Internal_mD914FAE2B26B3096327EC53EE858AD301A437226,
	Rigidbody2D_GetAttachedColliders_m586FC445ABB6A3494BE2D6C627CD1F9807A4CA5D,
	Rigidbody2D_GetAttachedCollidersList_Internal_mE26A705EA2F8EC753BFD932DFAB5C21358DEC4CC,
	Rigidbody2D_Cast_mC74A661B42ADF8A8C092D7649B100C84B6864E2B,
	Rigidbody2D_Cast_m341A313BDC032BFF8729FF6670453EF03F7EEBF6,
	Rigidbody2D_CastArray_Internal_m85128B34488B21E7CBDF6721B3DB675C97D19026,
	Rigidbody2D_Cast_m8DEA46350BE3DD467C74A30EFEAA41570D35AB49,
	Rigidbody2D_CastList_Internal_mCD69FF2B5B2A5E2CEBDD1567BBB1D622F1D5538B,
	Rigidbody2D_Cast_m73389995AD310431157636CD2E699FE47067F1CF,
	Rigidbody2D_Cast_m69B2C35D7B642F20ECC6E398D603D26FC86093EF,
	Rigidbody2D_CastFilteredArray_Internal_m5901CFF3A7ABF4E9FD5F370308BD754F796083B5,
	Rigidbody2D_Cast_mAAB462833699748EDD641A5E7AD2747E5C054DCA,
	Rigidbody2D_CastFilteredList_Internal_mB7FD54658D3A65F5BAE773DC59821CBD26EFD8D7,
	Rigidbody2D_GetShapes_m0FFC1DBDC8AB92DE13CB09AA19A6B36E81A777AE,
	Rigidbody2D_GetShapes_Internal_m7FFABE86C41C8F19BEDC651925A45FF55C707AF2,
	Rigidbody2D__ctor_mFF16B8ADAAE2FFD5FD4FBE3F412FC9E8FBBDBC88,
	Rigidbody2D_get_position_Injected_m89CBCD6C0EDACABB4A20B2B22958CEDAE030DC44,
	Rigidbody2D_set_position_Injected_m5D2AC2F0F4D41BA17BCA48FF18263F7728C6871C,
	Rigidbody2D_SetRotation_Quaternion_Injected_m971873F0BE792FDC6C831E25DA9BF081A4504FF7,
	Rigidbody2D_MovePosition_Injected_m7B6D07CFCE2E864C008AB5CED9EF1C8231D95386,
	Rigidbody2D_MoveRotation_Quaternion_Injected_m788825A6966DFF17EC4B1C4E803774CCF90F1F4F,
	Rigidbody2D_get_velocity_Injected_m980E2BA9FA750BA922DD2F79CEEA1CFF0B9B5D08,
	Rigidbody2D_set_velocity_Injected_m060C7F62A3A0280CC02594A15E7DE4486D1BE05E,
	Rigidbody2D_get_centerOfMass_Injected_m99936508EBABB64A9DB6E0D23925A768E5D26D69,
	Rigidbody2D_set_centerOfMass_Injected_m4162FEE8D97CFF692A183D60989B2EE8A9F79A4E,
	Rigidbody2D_get_worldCenterOfMass_Injected_mE27AE31806EF79698842007398C1B5831F80624D,
	Rigidbody2D_get_totalForce_Injected_m80F16D9AC46423B95EC5B5E87E8BC6D17EE5108F,
	Rigidbody2D_set_totalForce_Injected_mF6493C6AAC11B174A730DCB07E94BFCA1DCE06E2,
	Rigidbody2D_get_excludeLayers_Injected_m6776097C2A478AABFD41BB73404ED08B592A5669,
	Rigidbody2D_set_excludeLayers_Injected_m539D7516D0B8D5A5AF4511E1A694D824E870B115,
	Rigidbody2D_get_includeLayers_Injected_mDCBC9ECE347B684587CDD8C27A7B30E35714D3ED,
	Rigidbody2D_set_includeLayers_Injected_mBE55DD1394A095A39B8608E3BDD3C06C046561B4,
	Rigidbody2D_IsTouching_OtherColliderWithFilter_Internal_Injected_m388045548275D1A8A971789C4123BD3B7E2C8210,
	Rigidbody2D_IsTouching_AnyColliderWithFilter_Internal_Injected_m6BD055990388B22713244BDD68728BD793572363,
	Rigidbody2D_OverlapPoint_Injected_m8FA2204DEB195C42266DD4883B0ADFF3970B6EDD,
	Rigidbody2D_Distance_Internal_Injected_m1EE4FCDEFDC648580F4F556A462E6FAB3C592BB1,
	Rigidbody2D_AddForce_Injected_mC5372C179362B25CA579A94DBB11C5719F16452F,
	Rigidbody2D_AddRelativeForce_Injected_mFBEA1322EBD1A85F6303E9ED7BDB93FBCD648DC5,
	Rigidbody2D_AddForceAtPosition_Injected_mEC7444E8A4872CA31FFD2266B617009E96DEB578,
	Rigidbody2D_GetPoint_Injected_m4496261439CBF26686464DA7A98F91A92CA51514,
	Rigidbody2D_GetRelativePoint_Injected_m679228AEC36B0BBF16FA6C11DE35398DBBD6A52C,
	Rigidbody2D_GetVector_Injected_m4679D65590A481B46C0698C6D1B8BB1F0E9E9D09,
	Rigidbody2D_GetRelativeVector_Injected_m55E23C646C1B353EEFCF285639254A7E02E8C0F3,
	Rigidbody2D_GetPointVelocity_Injected_mE632AEDC51E0504F55FFCA5350FFB05F6677357A,
	Rigidbody2D_GetRelativePointVelocity_Injected_mF988A20BE146E39F58B7E04155B6D6BC44365747,
	Rigidbody2D_OverlapColliderArray_Internal_Injected_m52B03B8DB47F83A78693E0A36166BEB41B61F2BA,
	Rigidbody2D_OverlapColliderList_Internal_Injected_mB184F057EA967BF48A558C01FC094AF0D9DAD2A1,
	Rigidbody2D_CastArray_Internal_Injected_m697ED544F860E74AE02BA4BC4202768D463ACFE2,
	Rigidbody2D_CastList_Internal_Injected_m70C40BD3846AA86F5831F7D6EF9C8BA6E2C233C5,
	Rigidbody2D_CastFilteredArray_Internal_Injected_mAD8FB97A72F6434AE9A331F4946C50B5B3B970E3,
	Rigidbody2D_CastFilteredList_Internal_Injected_mFDC8AE2BA9AA99B5EF57DDA5451B0E91FC7E3442,
	Collider2D_get_density_m1782EAE58518A69E8CBB0C9E30153D1522A90DB5,
	Collider2D_set_density_m4D7FD660AAC3D3D08329C9A74BA2415A3C02A833,
	Collider2D_get_isTrigger_m982A3441480D505432B26A5B3DF6D0B34342EEE7,
	Collider2D_set_isTrigger_m19D5227BAB5D41F212D515C1E2CA433C2FEF7A48,
	Collider2D_get_usedByEffector_m416E00D82F05E0608809AD495967D5605AA22DCE,
	Collider2D_set_usedByEffector_mEF3DE09BBDE852262DAA80F4229CAB6BB42CC8DB,
	Collider2D_get_usedByComposite_mFBCEE0F0F650485222B16C5F39A5604A20F89256,
	Collider2D_set_usedByComposite_mBE188F38DA28CEFCEDAD84D8966D520222F749CA,
	Collider2D_get_composite_m1E64147888D6886748478E9CD2BB1035DDA9B1BB,
	Collider2D_get_offset_m6DC45B352DDE28C7B08607BFA3BECEC6E5F31914,
	Collider2D_set_offset_m416A5FDD11A7E07322418D1869AEFF9F1295913F,
	Collider2D_get_attachedRigidbody_m76D718444A94C258228DD98102DCF81C91CF9654,
	Collider2D_get_shapeCount_m2020E832FCAC46E2F702DF0132561B8C1A28D31B,
	Collider2D_CreateMesh_m3652DA9D992D70C69586B2CF76CC1B294434D487,
	Collider2D_GetShapeHash_m21660247FB3386589F9CB5EADA20A3566DCECB6D,
	Collider2D_GetShapes_mB57DDDF72DD1734177B93CED52D1C2D0E43E67E8,
	Collider2D_GetShapes_m2F072DA7FA4239FCD2D04A15863B357CFA0FF8C4,
	Collider2D_GetShapes_Internal_mAA13A485C136388B87323C54CB3CE4909AA565EF,
	Collider2D_get_bounds_m74F65CE702BA9D9EED05B870325B4FE3B2401B5E,
	Collider2D_get_errorState_mB3E1140DDAA1883DC1EBB787F55D94CB69B85184,
	Collider2D_get_compositeCapable_m3B5C595E4F0129625B839F9BD5D36B4A53B48691,
	Collider2D_get_sharedMaterial_m04E36DA08D9C5494729A062A922233E875BA9D79,
	Collider2D_set_sharedMaterial_m3CA0689BCC35C5601FEF7FD60ECA3F8A47C7FC8B,
	Collider2D_get_layerOverridePriority_m1135D5F56C107F238E49BB134C4EBD1F146FE10A,
	Collider2D_set_layerOverridePriority_mC896C54A19FD4C61357F73A5A141DA2919DC53CE,
	Collider2D_get_excludeLayers_m5BF604D30339A807AADC601130E55254A7C9BA34,
	Collider2D_set_excludeLayers_mAD3BE7B8E564BC4381075A90FD6933338FADD588,
	Collider2D_get_includeLayers_mFCE637602E68E599BF5CA4FA120FFFD0CAE3B241,
	Collider2D_set_includeLayers_mFAAC8BABE19C6582CFF371D460204B68A52D46F8,
	Collider2D_get_forceSendLayers_m2B5F4DA83B4DC4680DA1DE07E66F79E1139BA540,
	Collider2D_set_forceSendLayers_m025690AB9952F77D6B8604C5884809D412A641D5,
	Collider2D_get_forceReceiveLayers_m1CCD8BB08D0DD397A6DFFAF0BB8B76ABE66166ED,
	Collider2D_set_forceReceiveLayers_m1F69E3C231DBF10C7EE0626D6E89BB0D838CE7BD,
	Collider2D_get_contactCaptureLayers_m1576EF70121155CBE1918CFC5258A3B8C903B337,
	Collider2D_set_contactCaptureLayers_m81E13E156F8361A0BB4D785C74BC951F02E1B5D8,
	Collider2D_get_callbackLayers_m6AAC2DE43797453D12FB4F31D750FC25DB14E6AF,
	Collider2D_set_callbackLayers_m78D0B4A901432F51A35F43051955A880A77A4AD3,
	Collider2D_get_friction_mC146D9227596E9EA351873D2910BE377BAA27973,
	Collider2D_get_bounciness_m974564B81E5965061CC70018EB714E632B81B744,
	Collider2D_IsTouching_mEE1EF81028F933A33E4585AF49E85FDE2AEDF7B5,
	Collider2D_IsTouching_m582DC22EAD6086950A2058051D9AF2734B2CFFC0,
	Collider2D_IsTouching_OtherColliderWithFilter_mF295A9DC2AA1211BDFBD2B0A968C6DA066D54F53,
	Collider2D_IsTouching_m51C3D665075EB83DB191FF7BB5FA301930B8A16A,
	Collider2D_IsTouching_AnyColliderWithFilter_mFFB143430EB88ABAA13546F2F88AC779D28275F9,
	Collider2D_IsTouchingLayers_m658C6283458728B7135860245E656338DD05A4C2,
	Collider2D_IsTouchingLayers_mD0EA867624BAA3F4E5AD48F812FF1962A67F21B9,
	Collider2D_OverlapPoint_mF04F862E1CDA270589C82BBD72E71F0B60B6B883,
	Collider2D_Distance_m6649DB4DFFAD1992575F7FEFFA698D98BAC65690,
	Collider2D_OverlapCollider_m1E8170E34B70AC18A9B441298E2E2F7A83D29C7D,
	Collider2D_OverlapCollider_m02DEE1A9596975452BC43CFC08EF5071426D51CC,
	Collider2D_GetContacts_m05C07D64E1AA1C3454079A0A1C1B64FB6914A12A,
	Collider2D_GetContacts_mABB8B706AE58C6AF10F5E0CD289D6CC003231D6E,
	Collider2D_GetContacts_mC776A58C20C9E8708E422F9D953FC82D13CA1458,
	Collider2D_GetContacts_m776A35312006D8A02A22DA97037CAF1B9830F990,
	Collider2D_GetContacts_mE16E31F0B44A237425EFBBFDF4F86E9EFEDA79E3,
	Collider2D_GetContacts_mCD2A0B2C18808210431228DD5F1FE36F16348C92,
	Collider2D_GetContacts_m21C0EFF59210FD998DA626008F67605C949AEA4B,
	Collider2D_GetContacts_mFC2ABBD9C0F59CC96840E08A41BB7C065552D022,
	Collider2D_Cast_mD860ECCB9A4BB99401D9A4EFFB10D24192AFB2ED,
	Collider2D_Cast_m8C793587EFC6A1E3AB9A0BF3E26D4F296886E616,
	Collider2D_Cast_m5E46950578F28D8317E8FE20755794FEA8EA6905,
	Collider2D_Cast_m747197AA981A98E250B71619A1C5C4FE632D5868,
	Collider2D_Cast_mDAA044B3EF1C2C8A428435FEB5CB77C25E01F258,
	Collider2D_Cast_mF0BCE0B2A8FA4F909F166D891E54F5D620486EB9,
	Collider2D_CastArray_Internal_m33D435FE40D0366CE73A6B096B7D7619D094777F,
	Collider2D_Cast_mDC994B95845B2E48B1D10C1DD40FD771B80B17AF,
	Collider2D_CastList_Internal_mDAE8E54A9D7350609D4A8DDD2BACD7F479ABEB2D,
	Collider2D_Raycast_m0DE730565A97717C045409262FEBE328107DA9D4,
	Collider2D_Raycast_m2582FDC8A2732FBB9C41E677A4715CBB3996322D,
	Collider2D_Raycast_mF335AB5CBA08A85B8D3D2FB5C381A115C300DF9B,
	Collider2D_Raycast_m3B2BBDB5B9A4CD0E49DD4D69C1F62483133E5775,
	Collider2D_Raycast_m74E2DAF7AE315E048190314392CC596A536E375E,
	Collider2D_Raycast_m3A055CBB341EA841839BD0A741F07C1E99138A21,
	Collider2D_Raycast_m6460363104AE140968CD2B1BFD437BCA678038C9,
	Collider2D_RaycastArray_Internal_mF50416ACA1F64D52A3C8DA6EDD84E7AF66D25E1B,
	Collider2D_Raycast_m843FA86B2BA1A2F18A9183A83ACA4EC80D69B59D,
	Collider2D_RaycastList_Internal_m3ECA2E13BBD3600633F3F5BDF18912681E87040A,
	Collider2D_ClosestPoint_m2530361BC16191D6CD3AED28A8ACA8830D8EA453,
	Collider2D__ctor_mC4E4C5F6A2093B4902A09B312D1E832F12DE0B4B,
	Collider2D_get_offset_Injected_mDB21472D27E7E40EBB270CB307959BEEA9E55783,
	Collider2D_set_offset_Injected_m9C48213A56B5D2DA5DC8873D650E602ECBCFCC96,
	Collider2D_get_bounds_Injected_mB3F24D39428D3C8E314CC6452878BD9A62C216E4,
	Collider2D_get_excludeLayers_Injected_mAFC71003E7AD6E1DB9D96953DC20AEA58FE802F6,
	Collider2D_set_excludeLayers_Injected_m2E642F5F74DC22E05796F4022041C07A88F0B0C5,
	Collider2D_get_includeLayers_Injected_m1ABE2B7CB9087904F0135B8EDEE70A38E277B0D0,
	Collider2D_set_includeLayers_Injected_m339CA67F10ECF84ECF6A6944A4158D6A62C4F5C8,
	Collider2D_get_forceSendLayers_Injected_m54E754FAFAB7A9EC93958CD862DE988F4A75A0C8,
	Collider2D_set_forceSendLayers_Injected_m6D82229AFBFEB463005F035B607F88363701CF56,
	Collider2D_get_forceReceiveLayers_Injected_m48AC1667732F147B602C1818BE26DAFE9D8C5226,
	Collider2D_set_forceReceiveLayers_Injected_mFC94C90579A452C674726389820BF45ECF54CFBB,
	Collider2D_get_contactCaptureLayers_Injected_mA14C87269F5E13F91ECDF58C8C51998E7B7773EE,
	Collider2D_set_contactCaptureLayers_Injected_m9D9205A8A2FB666F8A72315FC618BFC9A0FE666C,
	Collider2D_get_callbackLayers_Injected_m93A27AAA96FD13ED894EAC175D91BB6C915DAC17,
	Collider2D_set_callbackLayers_Injected_mE833BB6DE418280745FEDBCD15780FFAED32A228,
	Collider2D_IsTouching_OtherColliderWithFilter_Injected_mB652C7ED62801745BA24E486E95021BAA71CC3B5,
	Collider2D_IsTouching_AnyColliderWithFilter_Injected_m3B8165E3F368D1DCD1E7281415460A5BAD7645A9,
	Collider2D_OverlapPoint_Injected_mCD54F37FD8E95A7EF693F7502435017DAD640188,
	Collider2D_CastArray_Internal_Injected_mBB0C68E31C9EC06E54FA1581D4DB75A3AD0DE90F,
	Collider2D_CastList_Internal_Injected_mB1691F0D6709312632D07F10F4A612437609F2D2,
	Collider2D_RaycastArray_Internal_Injected_m44B30BACAB3AA19FE3440DD7039CEE4BD838913C,
	Collider2D_RaycastList_Internal_Injected_m7356F4477061C1E8E4DB9B797448547DC14CEF40,
	CustomCollider2D_get_customShapeCount_mBE08207173380CA20F2D4A5F2BE15C76518A6AD9,
	CustomCollider2D_get_customVertexCount_m616476A7CF2DFDD444E6F2CCBADEBBAAC7BFCAA1,
	CustomCollider2D_GetCustomShapes_mB463C46A0DCE53C960F2E7FB2793DF5D954B9CB2,
	CustomCollider2D_GetCustomShapes_m16067B5F0A4F7867F5417E69D6F4EB386689A59B,
	CustomCollider2D_GetCustomShapes_Internal_mA50CF23D1E4B1564D28F35066C4F97D4B65B5DAC,
	CustomCollider2D_GetCustomShapes_m6260CB829EFA2BCDE5CE36C3E67C1B46E9338A86,
	CustomCollider2D_GetCustomShapesNative_Internal_m15A9E7191AD0020E69918F0B63EBEB43CD98DAEE,
	CustomCollider2D_SetCustomShapes_m48A46BEE8DB24CC02B6586BCF1E8853585912BDF,
	CustomCollider2D_SetCustomShapesAll_Internal_m011E83F4A029CF73B838EAB46680DB9B763B263A,
	CustomCollider2D_SetCustomShapes_mDCA6F07FFA86D76296FAC08AC3BA20B410E541C1,
	CustomCollider2D_SetCustomShapesNative_Internal_m0D77378650BA801B075CCB26E5C66E57FC426C48,
	CustomCollider2D_SetCustomShape_m83346477A05C387179AFE2A5F2DF137E8FD0A340,
	CustomCollider2D_SetCustomShape_Internal_m883ED32933CE2616345530FB5C56C2FCFF0ADDEE,
	CustomCollider2D_SetCustomShape_mE113626A12BC4BE71192214FB1572D679A3ACED6,
	CustomCollider2D_SetCustomShapeNative_Internal_mCB051D503B85CAE181771E5054FE02751E0C680A,
	CustomCollider2D_ClearCustomShapes_m2F8C02BCA08EFBA0AB5168C232ED832E1D37E4DB,
	CustomCollider2D_ClearCustomShapes_Internal_m88B5F47E59C6AA438CD6C4C6C59440CCF0EC1AFE,
	CustomCollider2D_ClearCustomShapes_mCAD0E68EA53C59096DCEF947FB1617AED0A5EB73,
	CustomCollider2D__ctor_m1B625DE24BC3434A692A58906B3DA4FFBEA9D10C,
	CircleCollider2D_get_radius_m****************************************,
	CircleCollider2D_set_radius_m****************************************,
	CircleCollider2D_get_center_m****************************************,
	CircleCollider2D_set_center_m****************************************,
	CircleCollider2D__ctor_m****************************************,
	CapsuleCollider2D_get_size_m665189C0E2EE06B144D595F92AF8A4F03C23E70C,
	CapsuleCollider2D_set_size_mF81DEA4CAED765717A0B17DBB71C4E9392E84FCE,
	CapsuleCollider2D_get_direction_m6243634C67D44970DE58F69EF0A8218681606A0F,
	CapsuleCollider2D_set_direction_mD3BCE4D9E5187AB0F180550BB57F89E4D8327E0A,
	CapsuleCollider2D__ctor_mC4E295CCB3FCF7B83A2AF2418D86F0F0C0C46ABF,
	CapsuleCollider2D_get_size_Injected_m54E90669EF16ADDDFD85EA774E8A004E0C3CE176,
	CapsuleCollider2D_set_size_Injected_m4F5747DB855E8F2C506DCC8A6DB8F03D45E1B3A9,
	EdgeCollider2D_Reset_mD8E1DDE82D7922AF780049B5AA232030D9EF9E5F,
	EdgeCollider2D_get_edgeRadius_m99516FF70D54BEA0C84DA64205A8EA5E35EDB7BC,
	EdgeCollider2D_set_edgeRadius_m98F70E3A7976E606C4E003C4D740DAA0F60DC8B6,
	EdgeCollider2D_get_edgeCount_mBC6D001B5F41935FDA2E08B6FD282410758BC5B8,
	EdgeCollider2D_get_pointCount_m55AE0598CD0D4981F7818CAD2F20B65A34418517,
	EdgeCollider2D_get_points_m51D877F93275BA12BD02873E5623ED9F468D968A,
	EdgeCollider2D_set_points_mFF49E39867F376E313D50F57531C28A1F715D02F,
	EdgeCollider2D_GetPoints_mB42014C3FC7BFF5A26C1FFB7300EFE8C1E166D2B,
	EdgeCollider2D_SetPoints_m214E8660F2539B7A50AFAD92206F359D1418BEA8,
	EdgeCollider2D_get_useAdjacentStartPoint_m8E074BB055459F469DBB7DCD2B2985122AC0296B,
	EdgeCollider2D_set_useAdjacentStartPoint_m0C5F481510036106A76E8E8969E6863EF20BCB5B,
	EdgeCollider2D_get_useAdjacentEndPoint_mE8ABBEDC875AB4494D54076925297CA5E5B441F8,
	EdgeCollider2D_set_useAdjacentEndPoint_m6F67425587CEAA63DEE11538EC7CAF4E4C53AECC,
	EdgeCollider2D_get_adjacentStartPoint_mA4E289E1CD02ECE21644A45EC3788B8C38BA0D3A,
	EdgeCollider2D_set_adjacentStartPoint_m726EEB10BFA51B3B8C8BB33BD15ADD3BE01D298F,
	EdgeCollider2D_get_adjacentEndPoint_m9DBDC3A1A6836927E832E567CBFC8051DD7A5397,
	EdgeCollider2D_set_adjacentEndPoint_m0BA538CF2876C2D06778FB5C7A351CAB1908E1BE,
	EdgeCollider2D__ctor_mEFFC70C11EFA43456B9A41CEFFACD61A4D1B71DE,
	EdgeCollider2D_get_adjacentStartPoint_Injected_mFAD79CFAE0636F04E57520FD3BC4B8FDE5AE4C4D,
	EdgeCollider2D_set_adjacentStartPoint_Injected_mC60962AFC4F92DA6ABDC37BB6391A23F6FB035DC,
	EdgeCollider2D_get_adjacentEndPoint_Injected_m46B51237229818B0107B431EE3906FFFBA548DE9,
	EdgeCollider2D_set_adjacentEndPoint_Injected_m38BB09D7EEBD288F39DA97AC7393D09630B17E7A,
	BoxCollider2D_get_size_mBB657ADFC58A79CDFDB7478956BBD9032E41D3D1,
	BoxCollider2D_set_size_mA69E48F639FFB614B5FC083D3FEED3DF78A9FF46,
	BoxCollider2D_get_edgeRadius_mC992345491CE535A20E0C2209D6DAC9E156FB5C5,
	BoxCollider2D_set_edgeRadius_m3B65453E01419336458097C9CFF2F1A164F7A015,
	BoxCollider2D_get_autoTiling_m9DF61AB62FB56245A6B289CC90FB9905A3E2E479,
	BoxCollider2D_set_autoTiling_mBCE5DCEFB409AA9F890424B4EC767C373DF62596,
	BoxCollider2D_get_center_m8CDA86C60BC5E368F3F2C669246B5DB6C6239709,
	BoxCollider2D_set_center_m0B2352C472856F235FEB94F7DDC75F6E1B751CFE,
	BoxCollider2D__ctor_mF153AB4CCB1C1F59176D3EE252E14B983FB557CF,
	BoxCollider2D_get_size_Injected_m6F9DD21D59E2B6D2B202DA657590DDA51A5B3EBF,
	BoxCollider2D_set_size_Injected_mE40FB02D46FAF468524BBC5BDF7BE468E7F85B9E,
	PolygonCollider2D_get_useDelaunayMesh_m44FB6ABFDC7F3C40468FE6A2142393BE01BEC3A1,
	PolygonCollider2D_set_useDelaunayMesh_mC145E47AB94701F9546B41102ABFB7D6B5324453,
	PolygonCollider2D_get_autoTiling_mE48F5926C8BB2516CCD7A35E7E55000D25960250,
	PolygonCollider2D_set_autoTiling_m4DDF7E85942C8CF14312EAB86CA1AEBB5430BB24,
	PolygonCollider2D_GetTotalPointCount_mDD5625F1A07C11D06D9F35757BFDF22B3ABD943A,
	PolygonCollider2D_get_points_m44182B587ECD765B7CF23A3A4BB289CDE95D421E,
	PolygonCollider2D_set_points_m74A433CEBC5A6A460EC2852CDEDEED2D4E261462,
	PolygonCollider2D_get_pathCount_m2F7EA6C9D0D7E579741DD3CB26BD1B2320570CC3,
	PolygonCollider2D_set_pathCount_m088370F58AC70DE6D28029AB0F2443D6A9B87721,
	PolygonCollider2D_GetPath_mE9D53D83FBB110EAC748BA535A1659C262B50F50,
	PolygonCollider2D_GetPath_Internal_mEF39269E7021D37741567FE0D6001305DCE49A69,
	PolygonCollider2D_SetPath_mDF03B6FDAE81E25C985F9BA6D372D949A6D9A1C1,
	PolygonCollider2D_SetPath_Internal_m868D93E9467A88558DD0E5D66797186B9FA82C4D,
	PolygonCollider2D_GetPath_mB98011C4334D40CE11F4C382144F5369D0B22D70,
	PolygonCollider2D_GetPathList_Internal_mA52E40B227DFCC61DA4899AA99BC17A17FE3F22D,
	PolygonCollider2D_SetPath_mF606659E4198753710AE7C4D032AC9A65972BBAB,
	PolygonCollider2D_SetPathList_Internal_m5D71A68788B250DFFE8BFA0A94152D6D797A56A2,
	PolygonCollider2D_CreatePrimitive_m2782BFD076EDE0A4D6EF7E2904EF12285D3FAF76,
	PolygonCollider2D_CreatePrimitive_m8F71C78DA75D43277273B1C4570BA1669919944E,
	PolygonCollider2D_CreatePrimitive_m8557092739042CF265ECB129A6AF3C4BBF3AC1B5,
	PolygonCollider2D_CreatePrimitive_Internal_m55AE07F15581E5D428327B9D6297BA13515F518F,
	PolygonCollider2D__ctor_mC2255D56CD93945AD9E72E196BF5168F7A13A538,
	PolygonCollider2D_CreatePrimitive_Internal_Injected_m4EB71AD721B0C99D2EA874A435B0097514A3CE99,
	CompositeCollider2D_get_geometryType_m7A11D31CB35A73AE64AE08C781ADE9CDC9C2E78C,
	CompositeCollider2D_set_geometryType_mC171C34D43B63BD504568160B722D11CF097004C,
	CompositeCollider2D_get_generationType_m5450B70EE23AB8591530592BA5504DAFAC903E08,
	CompositeCollider2D_set_generationType_m4F4412F0193FB1D19CEFD5394FBE3DA4848367C5,
	CompositeCollider2D_get_useDelaunayMesh_m78F653A5B228DD97C5AF526010FB31611BF4F0A2,
	CompositeCollider2D_set_useDelaunayMesh_mACE1EBBDF2D6AC5961DCC8D48FA6FC4BFBF3AE75,
	CompositeCollider2D_get_vertexDistance_mBF6A3438CF3F9193B2CB0253EDF9FBEF471B9AA8,
	CompositeCollider2D_set_vertexDistance_m29AA51E389B699EAD62633E94C5FCA6EF901A15F,
	CompositeCollider2D_get_edgeRadius_mDDA470560B4FD6708F508DE4014D34B212D10E7A,
	CompositeCollider2D_set_edgeRadius_m541E32B74C9A215897DED9029CD3D0FC51D968FF,
	CompositeCollider2D_get_offsetDistance_m8E64B6936B78CD64FD1449CAAA190E110C6C68D1,
	CompositeCollider2D_set_offsetDistance_m49947EEC717EF5DFB07F9CEE1EFE27F3A1A334B3,
	CompositeCollider2D_GenerateGeometry_mBC5EBC336F93E580B37A8FCD1CCF70D6258FB898,
	CompositeCollider2D_GetPathPointCount_m227236C21461B9D259075429C5D72A9117D833F4,
	CompositeCollider2D_GetPathPointCount_Internal_mC0B352A52B4102AB6B60421116F9A12DDBAE17DB,
	CompositeCollider2D_get_pathCount_mFCE509BFF1DE4F0AB2C242DB9665CCF16BC2D0DC,
	CompositeCollider2D_get_pointCount_m2D67EA1A290944246376496948C259B03F06D459,
	CompositeCollider2D_GetPath_m048E471625A3AC46864D74EA7D17077F8348FC09,
	CompositeCollider2D_GetPathArray_Internal_m04FD906BAC09301C13ACD6BBA2479913D8CF5920,
	CompositeCollider2D_GetPath_m5106FB56C8618315FEB28F630B1A14A4A1475BFD,
	CompositeCollider2D_GetPathList_Internal_m613178445832F4BF0F7EB8D5DAF2B2BDEF08DCAD,
	CompositeCollider2D__ctor_m701B74C8EF6FEA37C8D3C2204C80D4F2A9695503,
	Joint2D_get_attachedRigidbody_mF11B3881B599F4358539E99AA11AAF86E7DBF24F,
	Joint2D_get_connectedBody_m2ACC7B59AFFF74F080B96DBBC42866B15F6EA125,
	Joint2D_set_connectedBody_m96BC3C64A153EAB29F7C4BE4435A3E15B9FE6DED,
	Joint2D_get_enableCollision_m51773F6581CD59450F90D350B0749139FFE9AE8E,
	Joint2D_set_enableCollision_m21290BA0369A21B5C992556B4236BC576D11944D,
	Joint2D_get_breakForce_mB07CFACCF8173013AE59639635067991C67AA1F3,
	Joint2D_set_breakForce_m0D386E87920FB5FFA4C62E5B01656E66D6111E64,
	Joint2D_get_breakTorque_m03E456D986A250828053D33391C5792BBD464F4A,
	Joint2D_set_breakTorque_m351CBC2E1EB2708B1737A6BD3EE3622C5D490729,
	Joint2D_get_breakAction_mDB645B376DC417816122306B8D35AABF21138EE4,
	Joint2D_set_breakAction_mD730EA23498B75D977C8657E00C0FBD44EB58FFF,
	Joint2D_get_reactionForce_m89418F3DB76BAA9FBD8695B496B139C8F091D26F,
	Joint2D_get_reactionTorque_m1A3E7969A0627E174968D2A4BBAB230ED131ED85,
	Joint2D_GetReactionForce_m276772CC174D4128AD2CE184EAFAF59E39E97536,
	Joint2D_GetReactionTorque_m20D83BE56E786184AFC39774AC1A260CE8D022BB,
	Joint2D_get_collideConnected_m306A0F8E8153441C0B49FC868DAE543F3B07E2DD,
	Joint2D_set_collideConnected_m1381BD6784F1F30BC8D9B5B90D881A931EDA87DC,
	Joint2D__ctor_mAB58E264E8D3ADD1A2185274A517E7F39E7ED853,
	Joint2D_get_reactionForce_Injected_m2A4E8C2CB88769FCC8340DAFBE18717420621FBF,
	Joint2D_GetReactionForce_Injected_m538BCA18E0BF821BE8DAAB01915B89E1C6A95EC4,
	AnchoredJoint2D_get_anchor_m517BE8FCD419F6F6521E5BD690CCCEE0D1FD5E46,
	AnchoredJoint2D_set_anchor_mD6316CB3E4BAA0F443F9CC7A109361FA34059C0F,
	AnchoredJoint2D_get_connectedAnchor_mCE5550E89AA68AC0C17DA51A7B6F11F43457FC83,
	AnchoredJoint2D_set_connectedAnchor_mB07C956CA00DD5DE7DC59DFD075A415120E2803D,
	AnchoredJoint2D_get_autoConfigureConnectedAnchor_m5A3E2045B025F176969BD4FCA399F64CC9DF1582,
	AnchoredJoint2D_set_autoConfigureConnectedAnchor_mCCEA6080EDA9BE6A77064AF4A193B69B4AE80713,
	AnchoredJoint2D__ctor_mF490146220001E0121B7CFDF4D2A6C53F1633CB7,
	AnchoredJoint2D_get_anchor_Injected_mB4A11D61BA7A6FAEAC5B23CCF0C94A8021327633,
	AnchoredJoint2D_set_anchor_Injected_mB73F28EC4B855C01458D81E5EF1B19114FBEDE5A,
	AnchoredJoint2D_get_connectedAnchor_Injected_m5A960B4774F642CB34B009D69776ED9A10F735F7,
	AnchoredJoint2D_set_connectedAnchor_Injected_m14F1FCD64FE2008CEE123577D95C61D5033B3318,
	SpringJoint2D_get_autoConfigureDistance_mADCA9647C42631046FD828C04F9E17ABCE002F7E,
	SpringJoint2D_set_autoConfigureDistance_m92C05ECB7DD1357B31C892EA8D95A7129DEC2231,
	SpringJoint2D_get_distance_m0E08665FC9D23D51AD8F24B423BAAB3B2912FDA1,
	SpringJoint2D_set_distance_m9D72DBFBA3FBAE8DA5B95DEDE3F0FE5D3D5AFA16,
	SpringJoint2D_get_dampingRatio_mC708BE53071C2C24B56E77BBB187BC68CD7A20B0,
	SpringJoint2D_set_dampingRatio_mCE35C68410871890FF81E70380295869684CFA68,
	SpringJoint2D_get_frequency_m34C8251937CC875D4765E0057DF108A860EBD0F1,
	SpringJoint2D_set_frequency_m41AED17894EA7D360BF1089B1CD8A793F21256CE,
	SpringJoint2D__ctor_m204288D5BD581C49C2793E013786B82DE1B48086,
	DistanceJoint2D_get_autoConfigureDistance_m545EFD3FA2225303E6EA184DF653EC7AF3BEB669,
	DistanceJoint2D_set_autoConfigureDistance_m3C72E98ECF71C7F6606B6D6BF72BDFBC131C3D8A,
	DistanceJoint2D_get_distance_mBA1BA4522675FFBB05FAB6C9B5DAF42679661496,
	DistanceJoint2D_set_distance_m1A6C0D1337B10103691F7074A48BD9759AB1E39C,
	DistanceJoint2D_get_maxDistanceOnly_mCA41EB66D0738AE850849BBC552DE0A7F8218F94,
	DistanceJoint2D_set_maxDistanceOnly_m83A8A1EE7BF8DB2C3093F6282B468A394C097238,
	DistanceJoint2D__ctor_mC0F54EA481C7FE50939B6745397750195EEE77F5,
	FrictionJoint2D_get_maxForce_m74BFABD977B928CDF8B191EEC7144993AC160F3B,
	FrictionJoint2D_set_maxForce_m4CF4D5668E9C77937DF53B1E09DE94682B287812,
	FrictionJoint2D_get_maxTorque_mE7F679A4FC5C905C8884EEE1820B44FE551FBAB8,
	FrictionJoint2D_set_maxTorque_m6E252578D83DE1D7830CB55082397D1ED1B73E5B,
	FrictionJoint2D__ctor_mEB80266F7F8557396DB2C68BA5DAB82AE89CB0F3,
	HingeJoint2D_get_useMotor_mEC951A49C02E6D3D6B2CDF044F375087BDF293A9,
	HingeJoint2D_set_useMotor_m01AE6EB9DDDB4D8123369965B27A953DE9453DBF,
	HingeJoint2D_get_useLimits_m88D5E5FF1E6DB96106569E311488550983942612,
	HingeJoint2D_set_useLimits_m8E1A4EA6EE6850F7960C939DBFD0F8ED1AECF086,
	HingeJoint2D_get_motor_m78B13D793D036DECF09AB508D80A7043CA03868D,
	HingeJoint2D_set_motor_m1C296741B49BCB6A4B8F4AD347B04BC90E61A863,
	HingeJoint2D_get_limits_m6CAA2912070CED97CF8E724171D5FB944D45FA3F,
	HingeJoint2D_set_limits_mD948CC50F20045FA0A5CBAE57B38D05E984FE3BB,
	HingeJoint2D_get_limitState_m86781EE771B838A633556CA3E1F6AFA0082626B7,
	HingeJoint2D_get_referenceAngle_mFF58B70690BC7C5312D4D5C5ABF5DC969FEDE6BA,
	HingeJoint2D_get_jointAngle_m3E13F9ACA9E55A3B8207C91AFA9EBD10217C31DD,
	HingeJoint2D_get_jointSpeed_mEE116CCA1DBC3891A9060FF36DD0C903FA1982FE,
	HingeJoint2D_GetMotorTorque_m1C00273D08CA85992E4059D078207EE24818AA9D,
	HingeJoint2D__ctor_m01217F17792A684CDEB294242B39A891D2AD406A,
	HingeJoint2D_get_motor_Injected_m790FC34275C2EE27B4AE737BF55555CC88173B49,
	HingeJoint2D_set_motor_Injected_mAC48171AD7FDB9C8B2208455B703CC2EE95FEF41,
	HingeJoint2D_get_limits_Injected_mDCABF905EC94D567652061DCCFFDF5E65A3D6A29,
	HingeJoint2D_set_limits_Injected_mC074612E730D7159523FDFE834908EADAD4D3AF1,
	RelativeJoint2D_get_maxForce_mB5849E930A13FB75645574A0D12F323C9909C78D,
	RelativeJoint2D_set_maxForce_mA0B6126B89F825CE62CD511D426766C119A03015,
	RelativeJoint2D_get_maxTorque_m0178245D8239AF5819EC3B65C9E421CDD542F066,
	RelativeJoint2D_set_maxTorque_m9FC0275EE218098920ED03EE33250E145AD64235,
	RelativeJoint2D_get_correctionScale_m2F60047934BAF9F80FB446E1125F84D8F500F2DB,
	RelativeJoint2D_set_correctionScale_mE63FB6369FA233980D26A15382DB4B6D75DAAA70,
	RelativeJoint2D_get_autoConfigureOffset_m6D476F05275E9540C1BEFC5DB062B68F815BF62B,
	RelativeJoint2D_set_autoConfigureOffset_m127C028E09B0C6806EFCF0E460E3B6A9BCFE4407,
	RelativeJoint2D_get_linearOffset_mF7715E30094E81F7723CD857D27A7379BF97F299,
	RelativeJoint2D_set_linearOffset_m8CED88F5DFCDE7A9D5798EE4ABCCE8BDFFD6451B,
	RelativeJoint2D_get_angularOffset_m16C173D3378524CF1D9C1D07523A82979F85E52F,
	RelativeJoint2D_set_angularOffset_mD3E7127AEB1CECE54BE0A765E732C54FD9230E0F,
	RelativeJoint2D_get_target_m8832090D139AED4965EE61BD5061595A051D0574,
	RelativeJoint2D__ctor_m8419E8F544B315CE97B67FA81DA223E97C947224,
	RelativeJoint2D_get_linearOffset_Injected_m5ECF5CDA9247BBAE742C40A5D3F7F8E27AECF1B4,
	RelativeJoint2D_set_linearOffset_Injected_mFABE2353127C6C430FC6D34061E591532FCD6831,
	RelativeJoint2D_get_target_Injected_m7919A553A63EF992A0AF68520014483926F902B1,
	SliderJoint2D_get_autoConfigureAngle_m714AADA636855A0FD909876D57C8A9217F714DB9,
	SliderJoint2D_set_autoConfigureAngle_m05D23AD054ACB910D0810332B9C4294AEAD2AFCB,
	SliderJoint2D_get_angle_m3E0B29DA81EDD4C329F569C5CBD93B35A2286B71,
	SliderJoint2D_set_angle_m8FA204E7AF77C39AF71BDC90A6C2153D1C5CF56C,
	SliderJoint2D_get_useMotor_m5CCB1993650B387E65ABAD83461A81C3018F90F1,
	SliderJoint2D_set_useMotor_mEBE4B53AE419F3D35431BAA6A079FC15157FE45A,
	SliderJoint2D_get_useLimits_m59C8E06171DEF67290B75BB3FEFBA5EED8E6EB87,
	SliderJoint2D_set_useLimits_m4FD93475AB26925FCF0933A6B34F346104ABB9B9,
	SliderJoint2D_get_motor_m3F48592DF38BE0BF1DE4A6E0B457EA179E9AE0D6,
	SliderJoint2D_set_motor_m8F2FE7E30420B9B3445D70EE619EE74C2122B242,
	SliderJoint2D_get_limits_m22F67068000901CB7494EC6CE50AC2171F5DA6D0,
	SliderJoint2D_set_limits_mA06CC8C7743403D6D39BFE3A92A5DA6712A0FB9A,
	SliderJoint2D_get_limitState_mA4796AEAB481E470E3A63B721D9B005519444EC9,
	SliderJoint2D_get_referenceAngle_m34D8C706D1F5BDDEE680F126AA37135CA91EF3DD,
	SliderJoint2D_get_jointTranslation_mC0CAD9EF2E642B4829879AACAF10A372A09EC474,
	SliderJoint2D_get_jointSpeed_m163F1CE86F82B6D87CB731DB78E8D1038583D7C6,
	SliderJoint2D_GetMotorForce_m960C166D4E952DE8E8E839DA7A40C5F298006693,
	SliderJoint2D__ctor_m01A4D65377A5ED9EDEE8B58E836C9AD301E6EE32,
	SliderJoint2D_get_motor_Injected_m0797A36062932F62251CC79CB1E40C7CE7056FF2,
	SliderJoint2D_set_motor_Injected_m94648F7C6629B35822E3DB59E388D2B27F2C7131,
	SliderJoint2D_get_limits_Injected_m223390A434238C146212D28BBD2E1B05E9F2E561,
	SliderJoint2D_set_limits_Injected_m225CBB9B79C65340E28313C5F41F3FF71E3B0F33,
	TargetJoint2D_get_anchor_mB394F84A67089FBEE8F835BA8209A5A214D0A34D,
	TargetJoint2D_set_anchor_mB390CD2FB4C6DA84722DEAD8E22DEF3F36E4D473,
	TargetJoint2D_get_target_m6C8FFEB47352FC7891F593C1DAF9D45037578DDD,
	TargetJoint2D_set_target_m1E3C256EA0BC2E7F84DF06D78B27CEC47427ED37,
	TargetJoint2D_get_autoConfigureTarget_m4A01339DD58AD8DF6A8987145AD768F8516977BE,
	TargetJoint2D_set_autoConfigureTarget_m29D827A36F7AF5EF2CCA59C751895A8E954FDD8B,
	TargetJoint2D_get_maxForce_m587596A8099EDFEC1042010450698F5CA079B4D0,
	TargetJoint2D_set_maxForce_mE30B5AE87F32BF350A7E42F4806C3789F5D4400F,
	TargetJoint2D_get_dampingRatio_mFE0FE1BC25599BE2ABC2A39CC173D1CA7B9C453B,
	TargetJoint2D_set_dampingRatio_m911A0C3FDD91AE5FD38AD6DFF82B10BF0DC14864,
	TargetJoint2D_get_frequency_mB77855831937FA57ECD21946B6DFAD5F7CD6D583,
	TargetJoint2D_set_frequency_m80F76AA40FBEF1587316E66300F55FD9E696D846,
	TargetJoint2D__ctor_m4811DEC4CC456CF217BEF93010A17504DBAED16E,
	TargetJoint2D_get_anchor_Injected_m1901CD146F9DCD4E854A35C1FAA56E01389C7EF7,
	TargetJoint2D_set_anchor_Injected_m137388BB690E19F23D17FDD89E8B311809B17C1D,
	TargetJoint2D_get_target_Injected_m8BC74137C8027CD774D5E31E8B9D1ED7C8C3160C,
	TargetJoint2D_set_target_Injected_m1B017BEABA58FCEA63DC806B50147000E3EDDB54,
	FixedJoint2D_get_dampingRatio_m4FA14CC70E255303EEBBF9B326549AD49C8627EF,
	FixedJoint2D_set_dampingRatio_mAD96C575C3AEF211A9233629D2019D75F8522F15,
	FixedJoint2D_get_frequency_m60C536DA1C5668165152B4F7C416FD667738D6A3,
	FixedJoint2D_set_frequency_mE1A9139F2A0DCF63FC581656770EB8663F1F9F54,
	FixedJoint2D_get_referenceAngle_mE09E2ED6D40D10F3063B06BF5D994407EC79F8BC,
	FixedJoint2D__ctor_m4D3E49EE19F2387170FF67C63333EC844DDCF00C,
	WheelJoint2D_get_suspension_m63BBA2E8C9F3BA23D350DFAE2664DB08E980BD0C,
	WheelJoint2D_set_suspension_m017C1A68D1411841D1FFC0AF2C7291B361B4E554,
	WheelJoint2D_get_useMotor_m5DE6900CEC08ECBB1C07DF3BD041494D35706D1B,
	WheelJoint2D_set_useMotor_m97D63CBBDC65194E6B3116ACB68A89EF5019D1AA,
	WheelJoint2D_get_motor_m7B8A4EE9688A96BF43C1B90AB767EF0BA14449F0,
	WheelJoint2D_set_motor_m0A772ED5C1DA9925825F97D871E9D2320599702F,
	WheelJoint2D_get_jointTranslation_mF48618BC2FD3490420FE6CD9A9E14D7C1C73BD5A,
	WheelJoint2D_get_jointLinearSpeed_m8330007F345E79F7C22D9186271A10019CCE229F,
	WheelJoint2D_get_jointSpeed_m2D04C5B38D68E5735A9E40E48E9AA13938F7EEB8,
	WheelJoint2D_get_jointAngle_mC8C64E8FC6C8A7107751E88D8B5F542E442265DA,
	WheelJoint2D_GetMotorTorque_mC639162E42BE1E32B68E7F0615AB34B8C50DB450,
	WheelJoint2D__ctor_m0F13BEE677ABE27A2625A72B7C52F2527535CDC7,
	WheelJoint2D_get_suspension_Injected_m33FF82B5B58DD6CB38286625BD9D28A37138C5BD,
	WheelJoint2D_set_suspension_Injected_m6D13E64D7C74AA48DAD394094BB9EAA37540676D,
	WheelJoint2D_get_motor_Injected_m19BA887304608C8669E3FE5321B94AC69F505B98,
	WheelJoint2D_set_motor_Injected_mF5B66AD79991882EB09C669C39CA74DFA20ECD8E,
	Effector2D_get_useColliderMask_m4D61092DC2E3778C69A588E5F7701893BDB0A27B,
	Effector2D_set_useColliderMask_mFCF8B69C11E793E06FE33077EB3F869010C16FB6,
	Effector2D_get_colliderMask_m1431067B61EF032D539C9DEF6505CDF1C6EB3E7A,
	Effector2D_set_colliderMask_m5C7E34CA1611177A59ADBB8D1B6C9B177CF87BED,
	Effector2D_get_requiresCollider_m3228CE8BBF0AF919690B6D9E008B788B675EFD15,
	Effector2D_get_designedForTrigger_m5AC214DC32256874F5527177C62FE65CD0715C70,
	Effector2D_get_designedForNonTrigger_m7A523D9D98E9B110D34166124FDA11621373809F,
	Effector2D__ctor_m80D08310829C700C31BA2E4B6B9109EFE31B628F,
	AreaEffector2D_get_forceAngle_mAE1EBAB8BF86F48AC5C4C1BFCC4E67891494CA33,
	AreaEffector2D_set_forceAngle_m510FAD4CCB55EFE1B4223A06D308FEBE30AF3A17,
	AreaEffector2D_get_useGlobalAngle_m39787D8C929799E6A19B4687E4000678609A1213,
	AreaEffector2D_set_useGlobalAngle_m89179FAD2B3AB6C796912678AC9178D5A8515260,
	AreaEffector2D_get_forceMagnitude_mA572C7297BE28EC01F21A37EBD44EF220C0A7FF1,
	AreaEffector2D_set_forceMagnitude_m9EAD9F9D51FA54B20B16EC2D97300AF27FC08D5A,
	AreaEffector2D_get_forceVariation_m12B634EC144DB72AD635B252F7BFE95C9E48E8A7,
	AreaEffector2D_set_forceVariation_mADD42B7A5103CD6AB9685CA2E146ACACFEE09E0E,
	AreaEffector2D_get_drag_mC6A0D5642E5C83847059A48E01E6A3942A7B361E,
	AreaEffector2D_set_drag_m62106C7DD6CA073C7E64226E8E9F6D9BDE553F7A,
	AreaEffector2D_get_angularDrag_mEE4E64259BEA089D2CF593279FC8289E3B9FB081,
	AreaEffector2D_set_angularDrag_mA2B133C3010CF8EAA76A3222BE642674AFBE68A6,
	AreaEffector2D_get_forceTarget_mEE0289B4C59E66100F760455BAA86C1DCB3B6F26,
	AreaEffector2D_set_forceTarget_mA57C89968F72C2B879D09A6930C0C50056C1AF1D,
	AreaEffector2D_get_forceDirection_m5DAD95A723D9C9F3717336ADA66D676AC19F7EFC,
	AreaEffector2D_set_forceDirection_m8415D2BBF72C728B1404CD9DCFCFFC6B3B5ECEDA,
	AreaEffector2D__ctor_mEFDF77D391F5686D81BC1183535D701C9543E910,
	BuoyancyEffector2D_get_surfaceLevel_mEEEF2AFD2E7FA30814A710F087D8EF4EA2EE4D48,
	BuoyancyEffector2D_set_surfaceLevel_mA2E66865E258E6EDDB4B6453A9BD4681B259BE5F,
	BuoyancyEffector2D_get_density_m719EA866A0FDB2935E66A5ADCEC6006C72C82479,
	BuoyancyEffector2D_set_density_m42D85A902F814C94C3ACEBC996204126FC0C0DD8,
	BuoyancyEffector2D_get_linearDrag_m04B19A94E8B35B9446B966974D1775BC49B271C6,
	BuoyancyEffector2D_set_linearDrag_m3124502851A2E9DF6A855715344BEA28877577C5,
	BuoyancyEffector2D_get_angularDrag_m4C295A65D2C24DDA95A48381A8B4C9FC985AE83C,
	BuoyancyEffector2D_set_angularDrag_m49DDC2592EB4D390217490CA0E27138E4641B2D8,
	BuoyancyEffector2D_get_flowAngle_m49C504276945133550F71635BFAE9899B553F902,
	BuoyancyEffector2D_set_flowAngle_m9FE1F4F7ADFFA96629EC7105853943CB5C62A73A,
	BuoyancyEffector2D_get_flowMagnitude_m78C60720AEE4D8866BF69E94BEDD8987CC5D7AC4,
	BuoyancyEffector2D_set_flowMagnitude_mE61117AF92B595C8EE982FDAF217A6C665DF8E17,
	BuoyancyEffector2D_get_flowVariation_m5D2E6042CD1ADEDBEA838D36AE32D8A92BBF9DCA,
	BuoyancyEffector2D_set_flowVariation_mFE3C11B6C7349F10569119B1C12C15D32B449E59,
	BuoyancyEffector2D__ctor_m2CD5DDD34EBDD376475F7E7187DC7542AD49A443,
	PointEffector2D_get_forceMagnitude_m8AE19EF7174FEC1ABB8DB89BBE5B565DE927CB73,
	PointEffector2D_set_forceMagnitude_mB644B6A9AD8D1EC1BE18B0E05E82B0B58FDE7B29,
	PointEffector2D_get_forceVariation_mDDD35E4BB76EDF4154D8E64E371B464C33FC4A7A,
	PointEffector2D_set_forceVariation_mF42136F7DF9936E9FC5CE9F6705FCE7890E290C7,
	PointEffector2D_get_distanceScale_m0307CB80A13876DEF7FC28A82F612A0C4D1B4CAE,
	PointEffector2D_set_distanceScale_m278A83859C595BF578D1729F8185FE576AECD68F,
	PointEffector2D_get_drag_m1131CE8CBF238AA14800ACE1E2776BD26FFAD4B8,
	PointEffector2D_set_drag_m935AEB52672F2E2087AF736E1E54ABAC18AE1BAD,
	PointEffector2D_get_angularDrag_mFE905AED6B896C4B347F244C170634F4A4BD047A,
	PointEffector2D_set_angularDrag_mA1616BBF3B03A4B6384394F93218E2B03C22157F,
	PointEffector2D_get_forceSource_m30C09EE515C8FEB6F890DAF2011BEED84353FB3B,
	PointEffector2D_set_forceSource_mFDAB020692945EF0BD98B1297E08695E9EDC3FB5,
	PointEffector2D_get_forceTarget_mCC012819DBE8A10AD2A13F02128EE6670E4D3370,
	PointEffector2D_set_forceTarget_m7BB63ED280BE4F9CBFED63BE8FE342525EFD2630,
	PointEffector2D_get_forceMode_m74268A10C0C5E2004F54C58817EE066F5FD472D5,
	PointEffector2D_set_forceMode_mAE06AED24A08DA5C70762B2BE4A48C1A0C9A48C9,
	PointEffector2D__ctor_m8018BAC1D6AA2DA1BD766A8F91444BEA576B5F76,
	PlatformEffector2D_get_useOneWay_mC26DC37058BE73DC73437BA6CEEEDE0AFC0D271C,
	PlatformEffector2D_set_useOneWay_m471563E9B6A5AB0400EA32D3DAC27A9718FE375F,
	PlatformEffector2D_get_useOneWayGrouping_m7E56908FFD48BDBCB6D9439219114A025BCBB4A4,
	PlatformEffector2D_set_useOneWayGrouping_m855B415A02D912F57CB5B8432C71F37E4A4BD27F,
	PlatformEffector2D_get_useSideFriction_m4907F73EA14E0A300FD655225023B95FF38D8FCE,
	PlatformEffector2D_set_useSideFriction_mD507BADE509F5035EB9C1413C0ECE672AD259164,
	PlatformEffector2D_get_useSideBounce_mD181AFDEAEE11F426EE12580E4BA29B4F484BCF5,
	PlatformEffector2D_set_useSideBounce_m3FFE929FB82C7E12199491C272629CA014FCA041,
	PlatformEffector2D_get_surfaceArc_mDA96473824CB409259BDAD94CC2CF71A6E78176A,
	PlatformEffector2D_set_surfaceArc_mE0F36334C5F0A2C7CCCF8BAFDA8DD022B1C05440,
	PlatformEffector2D_get_sideArc_mBE83C37F4B7EE06F5C6555AFAFA96351BA162DFB,
	PlatformEffector2D_set_sideArc_mD485DCEE35A405505B013BED7236398A2352DAF3,
	PlatformEffector2D_get_rotationalOffset_mBA9E5BB3030CB92A5A2A8935779C6F6E7719420B,
	PlatformEffector2D_set_rotationalOffset_m620906016495D1C544165EFCB6B2EA5D6663FB20,
	PlatformEffector2D_get_oneWay_mF380935A8F455C6E21E781CBAA0696B952FE22BA,
	PlatformEffector2D_set_oneWay_mAD1F53570E23E0FDB7BC563B8B4EF2FB5896010C,
	PlatformEffector2D_get_sideFriction_mFE5A77D74F1FFC57F9E92421FFF7229CE83E6D54,
	PlatformEffector2D_set_sideFriction_mFB1E3C2E782BA38E0EB7EC87219FA2B2EE7B36FC,
	PlatformEffector2D_get_sideBounce_m763FF228A17647081A6560C33B969688CD6652E1,
	PlatformEffector2D_set_sideBounce_m97051D7F33E006C2C019A67B76CEF97F4F6F9662,
	PlatformEffector2D_get_sideAngleVariance_m49B9E0619398F5C1D0A23E7BB4B3FD844962AC4F,
	PlatformEffector2D_set_sideAngleVariance_m0B39B0A7B35200C70D6884B07D5BF3F8E16618AB,
	PlatformEffector2D__ctor_mC1BE6B1195E71193CCBD774D231B91A5C975C35D,
	SurfaceEffector2D_get_speed_m81585B003E24D6F0DD8D2641BA0A7400499A06AB,
	SurfaceEffector2D_set_speed_m1A7D40BEF98A32FBB2F7988508F20672F51771CF,
	SurfaceEffector2D_get_speedVariation_m9A768A56601E264DE6961B1A711C235723F93751,
	SurfaceEffector2D_set_speedVariation_m55EC471E13EF2F2C7BDF6108CE6C575C35230E00,
	SurfaceEffector2D_get_forceScale_m754C6A8B464D725E4CEDF62E93B71BAA282E7F50,
	SurfaceEffector2D_set_forceScale_mF90C47A72B2E34ACD53D0ECE21751E9338BC4426,
	SurfaceEffector2D_get_useContactForce_m49DB6D8470D7D015486481015AC84DE967D36D7E,
	SurfaceEffector2D_set_useContactForce_m0F4DFA5ABEE68E9FE6163E7F90A62F8409E433CC,
	SurfaceEffector2D_get_useFriction_m2DFD492E5E07241B7025AC80E194DCBA9581956F,
	SurfaceEffector2D_set_useFriction_m6FD3DFDEE2D37944B230D1B0AB5E7A07E28902FE,
	SurfaceEffector2D_get_useBounce_m7965EA0142BCF92C20EA41EF1FE7E7EAEC49253B,
	SurfaceEffector2D_set_useBounce_m8EB99E31FF37C7F993454645EC8E0E71441BFBED,
	SurfaceEffector2D__ctor_m2FFB309942DADE93910221DE31E39B40ED89CCE0,
	PhysicsUpdateBehaviour2D__ctor_m4A73D789ED84FDE97B95556E88768E15DD374DAF,
	ConstantForce2D_get_force_m1ADDB7691F9480ED2D0485BD71A09333E819D551,
	ConstantForce2D_set_force_mD46C591006E52563509C70CB0C081646C96992CC,
	ConstantForce2D_get_relativeForce_mF22945A4F60ED1A5A400DFF14E1F98BD4CDD7C2E,
	ConstantForce2D_set_relativeForce_mB4E6779432559DA2C1799359EF5ABA8F15D169A4,
	ConstantForce2D_get_torque_mAC6CD6297DF65AAF72BA7413CF9B1794C7699537,
	ConstantForce2D_set_torque_m4E12D622FECC185B792804DCB4192DACC3CA2CE0,
	ConstantForce2D__ctor_m68361431387716BD7FE885457DA8779A73005C8A,
	ConstantForce2D_get_force_Injected_m73816BC3B23C63F5EC9672A1195660AA83C9BB10,
	ConstantForce2D_set_force_Injected_mBA8F3FC1D8825C3F7FC3B52E11B3E54C6F1E0FD2,
	ConstantForce2D_get_relativeForce_Injected_m34B20DCB7998FA663F7D493F6A9C27BFD0233E7F,
	ConstantForce2D_set_relativeForce_Injected_mB05692C69E184236F2A44B84287DB3F0080F53B0,
	PhysicsMaterial2D__ctor_mFF418E70441EFCA8BBED3B95860CF60CB19E96FB,
	PhysicsMaterial2D__ctor_m6B7BF5BCE02CFE0F96C480B653B29E24C90C2DC9,
	PhysicsMaterial2D_Create_Internal_m3DB6A6CEEB4078BE664C982F61B5BA6A54FE6F81,
	PhysicsMaterial2D_get_bounciness_m30F25ED9C256B1D48B2F26AA2D1795FA321C3E46,
	PhysicsMaterial2D_set_bounciness_mBA08748E3304284A251E7B3E343EF548AB82DE91,
	PhysicsMaterial2D_get_friction_m1972CF3539B219537E5517A35C912940FCF51C5A,
	PhysicsMaterial2D_set_friction_m30157AED9C44D312E40B4E1C735197EF7A17B000,
};
extern void PhysicsScene2D_ToString_mACA22EF575F7544176360B16D431F2BB1DEBB307_AdjustorThunk (void);
extern void PhysicsScene2D_GetHashCode_mD45B3437D088C66A35AE20066AD632D1D0858B1E_AdjustorThunk (void);
extern void PhysicsScene2D_Equals_m4A19DE0675BD596A1B5AC0F7138A9A6F4D6029B3_AdjustorThunk (void);
extern void PhysicsScene2D_Equals_mA7C243A71CFDBFA905F057CE3E9C5E61B34216FB_AdjustorThunk (void);
extern void PhysicsScene2D_IsValid_m3C273A4AC45E6E9994DCBC66D11E003200FAF50C_AdjustorThunk (void);
extern void PhysicsScene2D_IsEmpty_m62DBEF907153F3158A43768639668054F8B82465_AdjustorThunk (void);
extern void PhysicsScene2D_Simulate_m2210AE79B5D4713DA5BEFD4EB5857778651DCE62_AdjustorThunk (void);
extern void PhysicsScene2D_Linecast_mF2BCE1D6E939D9E3B0289FE40FC6F8618706B33D_AdjustorThunk (void);
extern void PhysicsScene2D_Linecast_mAAC2C53143BDB79CCE8666C2E1432FB4E75FD915_AdjustorThunk (void);
extern void PhysicsScene2D_Linecast_m5DEB3199C07DB52BB9E1C890D58A53726DBD24C9_AdjustorThunk (void);
extern void PhysicsScene2D_Linecast_mEEB64412838171C7AA92974E8E923645C2AED7DD_AdjustorThunk (void);
extern void PhysicsScene2D_Linecast_m68E48116A78255DCADA115102C4093A6B79612C3_AdjustorThunk (void);
extern void PhysicsScene2D_Raycast_m5A2D66F6E7E8F34B6CF5B82099EFA4F69155F25D_AdjustorThunk (void);
extern void PhysicsScene2D_Raycast_m74A71D9DBCC2CCD7454240AE784CEE5720E55EA0_AdjustorThunk (void);
extern void PhysicsScene2D_Raycast_mDA9E6E04FC3117D6819BD757347A886AAF6024CD_AdjustorThunk (void);
extern void PhysicsScene2D_Raycast_m004884696543F60917C1ED72374C1528207229C3_AdjustorThunk (void);
extern void PhysicsScene2D_Raycast_m541841D244633BA234ED72B01204161686D6B3B9_AdjustorThunk (void);
extern void PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_BoxCast_m87BBB9AE3A51D80D32CF4351D1BCB24D230C547F_AdjustorThunk (void);
extern void PhysicsScene2D_BoxCast_m2BE649FF276A4DA721F359D60991E20B6EC1390E_AdjustorThunk (void);
extern void PhysicsScene2D_BoxCast_m303C253EFABCC5FFA582706B5263CFF703BAE31A_AdjustorThunk (void);
extern void PhysicsScene2D_BoxCast_m5959F7A39896649972E94A16FD852470321077A9_AdjustorThunk (void);
extern void PhysicsScene2D_BoxCast_mB3593FE8701D4482F74886089D69087CA38515CA_AdjustorThunk (void);
extern void PhysicsScene2D_CapsuleCast_m63BFCD6264B727CFFBDE288E3BAF56395A9BB3C1_AdjustorThunk (void);
extern void PhysicsScene2D_CapsuleCast_m06AE8E8E4E64B35464B27F5AE236B0D81D3E72A1_AdjustorThunk (void);
extern void PhysicsScene2D_CapsuleCast_mFF5DE18E5B463E9A9435A4F73BDA4CCD87D63E49_AdjustorThunk (void);
extern void PhysicsScene2D_CapsuleCast_mF141441734E1357C94F1B51A3065DCD8BB8A5F99_AdjustorThunk (void);
extern void PhysicsScene2D_CapsuleCast_m0D02AFE5BB9D55F18864E158CE3FC6792F180E9A_AdjustorThunk (void);
extern void PhysicsScene2D_GetRayIntersection_m92BF0BF919D8BB704EC93D45EE7E2DB2EB176943_AdjustorThunk (void);
extern void PhysicsScene2D_GetRayIntersection_mF3E0EC0D4F5A4B8C063E735979C851ED5B4B4C2E_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapPoint_mBD3BD6137CC642F63968D6058E75467C1098BBCE_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapPoint_mC2ED0510F29807B334351227C118F74CBC278227_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapPoint_m3BEA82B608B1338872A28CE4A7A8BCB4A45A7F07_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapPoint_m19614587E2F3CD617B899513DDA8299F455960A1_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapPoint_m3C062202164C34E05357D6D53864132CB4DF3325_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapBox_m92B0E7E2244148FB38CB5EC0782C55E4CE5D41C6_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapBox_m335088F6FBADF523759D88B56B4CE8B351BACEBF_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapBox_m822F0FBB0CB890B8B3DCF1B513EDD45686325D58_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapBox_mE4BBC0516865E1BFB5CC23E85FCC6985EE1F1673_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapBox_m87AB321755E4631C49A58FD12E85A6046F78A20B_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapArea_mFD58E3BBEEE4357EB0BBA9A96EBB58012E7270A0_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapArea_m4D8FAFD9E7A299823649C9043C70456FF63F0655_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapAreaToBoxArray_Internal_m330E73DB6C450B36501A02801C5C77A4BD1F1B19_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapArea_m00070511C0F182CFDAC6BC57A057ADDCC671B1F3_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapArea_mD7B8AA443BB67D29957C8F0AA14DD4AC99F93F2B_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapAreaToBoxArray_Internal_m81756D2895F9044EA86F0B21A47A16FB3F2AD592_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapArea_m1DBB709EB60C3BCBF2064C98E247360DCE93C1FB_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapAreaToBoxList_Internal_m9D3425E6BC3D06B023D05F38ED4F50E1E178459E_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCapsule_mB9EED3130F064B14CB4895858D6A25B7FE9CAF47_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCapsule_mB521741943F713EB037454DA5EB61CF4E405B364_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCapsule_m98A5052A0721B060EF518A167268F1384F36565B_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCapsule_m950A00AE483884975510112A38DDAC9ED30358EA_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapCapsule_mD621AE5E0F3095B5E7A9B6662DD4D55B1C0FAA31_AdjustorThunk (void);
extern void PhysicsShape2D_get_shapeType_mBA55638729252854535E3E9279F7A006B610B01F_AdjustorThunk (void);
extern void PhysicsShape2D_set_shapeType_m8A00238C6DFD5ABF398700C86492296DB9DC6D9B_AdjustorThunk (void);
extern void PhysicsShape2D_get_radius_m4300D92A9F9523277976419369F0A14DC75AEF8B_AdjustorThunk (void);
extern void PhysicsShape2D_set_radius_mF96363A2D7B8112F2994D38A1113C8FC441FF864_AdjustorThunk (void);
extern void PhysicsShape2D_get_vertexStartIndex_m23782C197FC0521A6DC3A4E0F115DB477042EC40_AdjustorThunk (void);
extern void PhysicsShape2D_set_vertexStartIndex_m289E3881B77BEBA99E28B698AEEBFFC0F4A5DD00_AdjustorThunk (void);
extern void PhysicsShape2D_get_vertexCount_mBB09936295C475647B8E92C1464F4C2F3CA7A8D2_AdjustorThunk (void);
extern void PhysicsShape2D_set_vertexCount_m982167474864E44AA5DC6ED6CFD854E757B6702A_AdjustorThunk (void);
extern void PhysicsShape2D_get_useAdjacentStart_mF9EE25B1A542E807AA8AFB722F79D7CE0937271B_AdjustorThunk (void);
extern void PhysicsShape2D_set_useAdjacentStart_mF419418F7F43E9F30BA926E373730A3D4DE68D4E_AdjustorThunk (void);
extern void PhysicsShape2D_get_useAdjacentEnd_mE04E765CF666B95B6C49E34A38ED2F056B9E4CEA_AdjustorThunk (void);
extern void PhysicsShape2D_set_useAdjacentEnd_mDC730349B1B72E2B095BC5A2858BC34C2EF18D2B_AdjustorThunk (void);
extern void PhysicsShape2D_get_adjacentStart_mC99F496D28E98CD8D59425D4815275530414F94E_AdjustorThunk (void);
extern void PhysicsShape2D_set_adjacentStart_m6EDB2AD4B54FD968C0E6E3C783045FA121B5D081_AdjustorThunk (void);
extern void PhysicsShape2D_get_adjacentEnd_m8C5EA386C240C0A123C12C849892922D8D1F11DA_AdjustorThunk (void);
extern void PhysicsShape2D_set_adjacentEnd_m6646CBEFCA64FF1FA84B2E01B6C18AD6F351CEC1_AdjustorThunk (void);
extern void GroupState_ClearGeometry_m4A1E29A3DFE71E4426BD2F02D7067C8E32BEBE8F_AdjustorThunk (void);
extern void ColliderDistance2D_get_pointA_m55571F64432B8539AF715F91AE9F01E039B1BD3B_AdjustorThunk (void);
extern void ColliderDistance2D_set_pointA_m4334AB6925BF3CA93F3993E260CE986702C0AC8B_AdjustorThunk (void);
extern void ColliderDistance2D_get_pointB_m8C42A60CF6DFD3257CA1EF994BAE5DE2956F732A_AdjustorThunk (void);
extern void ColliderDistance2D_set_pointB_mF53E3F9EA595B627B64344411163212631DF42D0_AdjustorThunk (void);
extern void ColliderDistance2D_get_normal_m751A946B91F2A85A5F37376D4C4BC6BD42B437ED_AdjustorThunk (void);
extern void ColliderDistance2D_get_distance_m6754976414E63C5F8B9493FDECA2210F5339F3C7_AdjustorThunk (void);
extern void ColliderDistance2D_set_distance_m1CFF30A40C19AE4F4ACB11B0B157B1FDE8DD56E4_AdjustorThunk (void);
extern void ColliderDistance2D_get_isOverlapped_mEFF81A407A9EF37A3800E1050B9D9C789DF3F0D3_AdjustorThunk (void);
extern void ColliderDistance2D_get_isValid_m044D48E09BD8ADC4A01708B1D9478E43098EDF93_AdjustorThunk (void);
extern void ColliderDistance2D_set_isValid_m356B0AEAF9421D48DD98304D8460893FE44C82DB_AdjustorThunk (void);
extern void ContactFilter2D_NoFilter_m15980FED0667A151078679DF555638BEEA7C01DE_AdjustorThunk (void);
extern void ContactFilter2D_CheckConsistency_mD918F11F977EA35E87CF491F7AE8794F5D01DF72_AdjustorThunk (void);
extern void ContactFilter2D_ClearLayerMask_m86FA33B78DAAC6E04A4ED62F73CDD3D34B0DF68A_AdjustorThunk (void);
extern void ContactFilter2D_SetLayerMask_mC3FBC2D806C1A3ACB2D060CE48F8157505E42F9B_AdjustorThunk (void);
extern void ContactFilter2D_ClearDepth_m6CBAEF48B84E53079CC628E186EC6DD940C3151E_AdjustorThunk (void);
extern void ContactFilter2D_SetDepth_mE614DDDDAEA489D150E61D2DF8104F9292236F18_AdjustorThunk (void);
extern void ContactFilter2D_ClearNormalAngle_m7C7A157AF9CF67AF3D1094770B6B910BFEDC23D3_AdjustorThunk (void);
extern void ContactFilter2D_SetNormalAngle_m9A737032D43865398C15881FF1764694D0601B33_AdjustorThunk (void);
extern void ContactFilter2D_get_isFiltering_m993424DE9FBBA01394167C8FC546E4526C564A74_AdjustorThunk (void);
extern void ContactFilter2D_IsFilteringTrigger_m48921B2B2B5254FE3033757321224C14E7AB9884_AdjustorThunk (void);
extern void ContactFilter2D_IsFilteringLayerMask_m353947849DAF100E9125B0D92349B4763C76139A_AdjustorThunk (void);
extern void ContactFilter2D_IsFilteringDepth_m455CB30F384EFEB68951CFEA42F26D2F15BC8886_AdjustorThunk (void);
extern void ContactFilter2D_IsFilteringNormalAngle_m67548CED7AB9D9029B88C405A23F67307E95EA8F_AdjustorThunk (void);
extern void ContactFilter2D_IsFilteringNormalAngle_mCBCAD097FC2B202E4C54A8A2EC13883EEE652C64_AdjustorThunk (void);
extern void ContactFilter2D_IsFilteringNormalAngleUsingAngle_m30C52106865DBBAD20B012FD86699324E7BA2CC6_AdjustorThunk (void);
extern void ContactPoint2D_get_point_mFF9B7395F63E748507C85166F3EDC218B8740396_AdjustorThunk (void);
extern void ContactPoint2D_get_normal_m421147AFFC1A029B4DEC775C6B9197919ED93D21_AdjustorThunk (void);
extern void ContactPoint2D_get_separation_m70174AAA4EC5B1857607115B25BED77BA142EA5E_AdjustorThunk (void);
extern void ContactPoint2D_get_normalImpulse_m601808AD6F4E1F81E4C9E53401556C465D161288_AdjustorThunk (void);
extern void ContactPoint2D_get_tangentImpulse_mC45ADFB72CA45EE4C430598511DB6534AF3F5CB4_AdjustorThunk (void);
extern void ContactPoint2D_get_relativeVelocity_m0DAD8E66E82BE6A3133618EFCB2CA579FD0F5D7D_AdjustorThunk (void);
extern void ContactPoint2D_get_collider_mCEC4BBE3C9CF0977C3EC5D529C2D5B664180768F_AdjustorThunk (void);
extern void ContactPoint2D_get_otherCollider_m1892E5E5AA0032610E8252FC371654E4198A7779_AdjustorThunk (void);
extern void ContactPoint2D_get_rigidbody_m28CDDD12EB3F7C06D05126C1ECA3AEA9594E1FF3_AdjustorThunk (void);
extern void ContactPoint2D_get_otherRigidbody_mAE4893B039030B7AF7B645D2EEA0BD0F142CE6D9_AdjustorThunk (void);
extern void ContactPoint2D_get_enabled_m1AC0022C616EBDD9012C25A6C9FD21766E87686C_AdjustorThunk (void);
extern void JointAngleLimits2D_get_min_mABF1DD59D9AF0093C3A6C3EA039B2CE7B3F0AC1A_AdjustorThunk (void);
extern void JointAngleLimits2D_set_min_m9EBCBBF3B7D4126158DB2405A2C2333DFFE48B29_AdjustorThunk (void);
extern void JointAngleLimits2D_get_max_m2673C23C93D2802B61332C9BA58BD64E3A76BCC8_AdjustorThunk (void);
extern void JointAngleLimits2D_set_max_m8264B45D23F709BA60BEF19645569D105A623D6F_AdjustorThunk (void);
extern void JointTranslationLimits2D_get_min_m76573A6341DBDA6DA716A910851A19DBA7C6F773_AdjustorThunk (void);
extern void JointTranslationLimits2D_set_min_m68279D0A2371102C82CC0511E4A8AEE9D697EDD4_AdjustorThunk (void);
extern void JointTranslationLimits2D_get_max_mA2360BA3CD1A35E2E6C297D1EE478FD60757A699_AdjustorThunk (void);
extern void JointTranslationLimits2D_set_max_m29EC807D57EC6702250B59CA07A207971E6D164B_AdjustorThunk (void);
extern void JointMotor2D_get_motorSpeed_mDD7B3F1E134AB8367191EAFEA683B82AC4952163_AdjustorThunk (void);
extern void JointMotor2D_set_motorSpeed_m080F930D6EC3A5BE6348C409B9115664E464B480_AdjustorThunk (void);
extern void JointMotor2D_get_maxMotorTorque_m911E081510303C1B7763686891ACFCEF6748C6EB_AdjustorThunk (void);
extern void JointMotor2D_set_maxMotorTorque_mFB41FE9052B411B7C43C29DE4978E04AD9C748AC_AdjustorThunk (void);
extern void JointSuspension2D_get_dampingRatio_m436AF1D3DE8C46C1F548E08AF83A0C0C546CFD25_AdjustorThunk (void);
extern void JointSuspension2D_set_dampingRatio_m6E706B4991620D9F8E54014779A788DF55A45DD4_AdjustorThunk (void);
extern void JointSuspension2D_get_frequency_m62ACA94DE93973E8874544AF88BFC5C56AE7F0F3_AdjustorThunk (void);
extern void JointSuspension2D_set_frequency_mE8B115A66AD8FADFC3C035075A3662C0C16E89B6_AdjustorThunk (void);
extern void JointSuspension2D_get_angle_mF95F89C72EFF15D5CC37CFFD93FAF8BD8437C0E1_AdjustorThunk (void);
extern void JointSuspension2D_set_angle_m27413F565F50EEF07F617ED60539527175B73BB5_AdjustorThunk (void);
extern void RaycastHit2D_get_centroid_mEA7A6ACCFE6C0E7566B0C177A54A750EC554C704_AdjustorThunk (void);
extern void RaycastHit2D_set_centroid_mFB5F56330BAA9C8CE547AFE75648FD8E426776D8_AdjustorThunk (void);
extern void RaycastHit2D_get_point_mB35E988E9E04328EFE926228A18334326721A36B_AdjustorThunk (void);
extern void RaycastHit2D_set_point_m13D917ABD5F8FEB291FED13D2DB2A2481E085FAC_AdjustorThunk (void);
extern void RaycastHit2D_get_normal_m75F1EBDE347BACEB5A6A6AA72543C740806AB5F2_AdjustorThunk (void);
extern void RaycastHit2D_set_normal_m36B4A488824FD1E4D0F47AE13211C4D773FE6799_AdjustorThunk (void);
extern void RaycastHit2D_get_distance_mD0FE1482E2768CF587AFB65488459697EAB64613_AdjustorThunk (void);
extern void RaycastHit2D_set_distance_m652B2B50F02583A4FAD019190AEFA1D402B0FA33_AdjustorThunk (void);
extern void RaycastHit2D_get_fraction_m9BF416582F5C4D5FC8D93E5DA57B4CDC64E030BE_AdjustorThunk (void);
extern void RaycastHit2D_set_fraction_m3FE49691CA64CB6EB030AE87C4FAEA28285C1062_AdjustorThunk (void);
extern void RaycastHit2D_get_collider_mB56DFCD16B708852EEBDBB490BC8665DBF7487FD_AdjustorThunk (void);
extern void RaycastHit2D_get_rigidbody_mA7C26ACF22912C14CC6C8B1D7C50F38BCF5096B8_AdjustorThunk (void);
extern void RaycastHit2D_get_transform_mA5E3F8DC9914E79D3C9F6F3F2515B49EEBB4564A_AdjustorThunk (void);
extern void RaycastHit2D_CompareTo_mF4665ADD9F8B212987443C052ADE73E8C0DF2612_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_useMultithreading_m8967EDBBDC51011BE1C0FB7FC437643F8E0E8DFA_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_useMultithreading_mF475BF5CDDBBEA27D50F2B60E00CC9BCAFA016A9_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_useConsistencySorting_m583424FC6BACAB1E6161F3BDEBAE102978630CDA_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_useConsistencySorting_m4190351CA61E03766E72435E437DCB23CE1408CC_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_interpolationPosesPerJob_m7F3C3C05C4AC1770E342EA6D6F63B709A610DFB8_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_interpolationPosesPerJob_mA764F435978E7BD6005F1B560516B7B9AE2A123E_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_newContactsPerJob_mCA477A50F149B5DADC5E2A772E48B746638DC761_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_newContactsPerJob_mA2C104942792A9188ADE151D5221FF18F3EF052B_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_collideContactsPerJob_mC27C3A2243ED724C4D147C210595716AAECDC217_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_collideContactsPerJob_m199A8D06195B95564503429B109C7E32B3E42138_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_clearFlagsPerJob_m0F1299FC15FDAE5A030772D1D1A01E9FA522AC94_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_clearFlagsPerJob_mD198700A6FEA3696DC7A8F54E043A337B18D97B3_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_clearBodyForcesPerJob_mCA5DC9F62EC9BCB44D9AD7C50F5D72DEA79CB560_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_clearBodyForcesPerJob_m85773E07D803A633C58928F66588ED04FB8EE78F_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_syncDiscreteFixturesPerJob_m44F03D367416DB932C675A23B30BA8CE81DED94E_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_syncDiscreteFixturesPerJob_m59988BD56ADE10BF7D55B1E3BB6A505EE5290DEB_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_syncContinuousFixturesPerJob_m3AA97386BBD0EE4BE60232DA0940D1C0B00F6549_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_syncContinuousFixturesPerJob_m8F99FB132D69D00C86FFA1788CF8B084E929DD65_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_findNearestContactsPerJob_m989A3ACBF42B19E2EA50E2414A8EF3A876B1C7D9_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_findNearestContactsPerJob_m7A2EC665BD757F95E86BC950E91BA37B1AC2588A_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_updateTriggerContactsPerJob_m5756767F8BA939BF8834E3F3F244BD0AFFC3E8CA_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_updateTriggerContactsPerJob_m200A1E3BC34797C4C40B6F375AE0E492892CBD66_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_islandSolverCostThreshold_m7214005F53C4136C65DDBB7BF40352CF5C69F307_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_islandSolverCostThreshold_m08171184FFAF1C0C8B268BC15FAA961719906D35_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_islandSolverBodyCostScale_mBB11308FF9BB281DD21B800FD02701F9AFC5E881_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_islandSolverBodyCostScale_m0F5568713C8A7D056614EAECA50F1EC42DDA6E4C_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_islandSolverContactCostScale_mB24A44A346D134D75B93D8F35477D12F2D5E29F8_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_islandSolverContactCostScale_mB1848684FA85C5F324896437840B4712CC57B162_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_islandSolverJointCostScale_mD23FABF4C84E1ED3456E8E9AF0E4EF2902E769C6_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_islandSolverJointCostScale_m85293EF48F13BABF165CA4C1B04960F0C0C70A38_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_islandSolverBodiesPerJob_mBC375DF0FB397948AAB54BC4ED7593377F989A97_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_islandSolverBodiesPerJob_m927451FE8439EBCE47532EB6B0BFC1546AD63294_AdjustorThunk (void);
extern void PhysicsJobOptions2D_get_islandSolverContactsPerJob_mD484AFA46C160C184837E58C462CA9E19E28D5D3_AdjustorThunk (void);
extern void PhysicsJobOptions2D_set_islandSolverContactsPerJob_mF162C21E0D8466EFCE0AD8D4ECDF4646497DDDD3_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[181] = 
{
	{ 0x06000001, PhysicsScene2D_ToString_mACA22EF575F7544176360B16D431F2BB1DEBB307_AdjustorThunk },
	{ 0x06000004, PhysicsScene2D_GetHashCode_mD45B3437D088C66A35AE20066AD632D1D0858B1E_AdjustorThunk },
	{ 0x06000005, PhysicsScene2D_Equals_m4A19DE0675BD596A1B5AC0F7138A9A6F4D6029B3_AdjustorThunk },
	{ 0x06000006, PhysicsScene2D_Equals_mA7C243A71CFDBFA905F057CE3E9C5E61B34216FB_AdjustorThunk },
	{ 0x06000007, PhysicsScene2D_IsValid_m3C273A4AC45E6E9994DCBC66D11E003200FAF50C_AdjustorThunk },
	{ 0x06000009, PhysicsScene2D_IsEmpty_m62DBEF907153F3158A43768639668054F8B82465_AdjustorThunk },
	{ 0x0600000B, PhysicsScene2D_Simulate_m2210AE79B5D4713DA5BEFD4EB5857778651DCE62_AdjustorThunk },
	{ 0x0600000C, PhysicsScene2D_Linecast_mF2BCE1D6E939D9E3B0289FE40FC6F8618706B33D_AdjustorThunk },
	{ 0x0600000D, PhysicsScene2D_Linecast_mAAC2C53143BDB79CCE8666C2E1432FB4E75FD915_AdjustorThunk },
	{ 0x0600000F, PhysicsScene2D_Linecast_m5DEB3199C07DB52BB9E1C890D58A53726DBD24C9_AdjustorThunk },
	{ 0x06000010, PhysicsScene2D_Linecast_mEEB64412838171C7AA92974E8E923645C2AED7DD_AdjustorThunk },
	{ 0x06000012, PhysicsScene2D_Linecast_m68E48116A78255DCADA115102C4093A6B79612C3_AdjustorThunk },
	{ 0x06000014, PhysicsScene2D_Raycast_m5A2D66F6E7E8F34B6CF5B82099EFA4F69155F25D_AdjustorThunk },
	{ 0x06000015, PhysicsScene2D_Raycast_m74A71D9DBCC2CCD7454240AE784CEE5720E55EA0_AdjustorThunk },
	{ 0x06000017, PhysicsScene2D_Raycast_mDA9E6E04FC3117D6819BD757347A886AAF6024CD_AdjustorThunk },
	{ 0x06000018, PhysicsScene2D_Raycast_m004884696543F60917C1ED72374C1528207229C3_AdjustorThunk },
	{ 0x0600001A, PhysicsScene2D_Raycast_m541841D244633BA234ED72B01204161686D6B3B9_AdjustorThunk },
	{ 0x0600001C, PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk },
	{ 0x0600001D, PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk },
	{ 0x0600001F, PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk },
	{ 0x06000020, PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk },
	{ 0x06000022, PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk },
	{ 0x06000024, PhysicsScene2D_BoxCast_m87BBB9AE3A51D80D32CF4351D1BCB24D230C547F_AdjustorThunk },
	{ 0x06000025, PhysicsScene2D_BoxCast_m2BE649FF276A4DA721F359D60991E20B6EC1390E_AdjustorThunk },
	{ 0x06000027, PhysicsScene2D_BoxCast_m303C253EFABCC5FFA582706B5263CFF703BAE31A_AdjustorThunk },
	{ 0x06000028, PhysicsScene2D_BoxCast_m5959F7A39896649972E94A16FD852470321077A9_AdjustorThunk },
	{ 0x0600002A, PhysicsScene2D_BoxCast_mB3593FE8701D4482F74886089D69087CA38515CA_AdjustorThunk },
	{ 0x0600002C, PhysicsScene2D_CapsuleCast_m63BFCD6264B727CFFBDE288E3BAF56395A9BB3C1_AdjustorThunk },
	{ 0x0600002D, PhysicsScene2D_CapsuleCast_m06AE8E8E4E64B35464B27F5AE236B0D81D3E72A1_AdjustorThunk },
	{ 0x0600002F, PhysicsScene2D_CapsuleCast_mFF5DE18E5B463E9A9435A4F73BDA4CCD87D63E49_AdjustorThunk },
	{ 0x06000030, PhysicsScene2D_CapsuleCast_mF141441734E1357C94F1B51A3065DCD8BB8A5F99_AdjustorThunk },
	{ 0x06000032, PhysicsScene2D_CapsuleCast_m0D02AFE5BB9D55F18864E158CE3FC6792F180E9A_AdjustorThunk },
	{ 0x06000034, PhysicsScene2D_GetRayIntersection_m92BF0BF919D8BB704EC93D45EE7E2DB2EB176943_AdjustorThunk },
	{ 0x06000036, PhysicsScene2D_GetRayIntersection_mF3E0EC0D4F5A4B8C063E735979C851ED5B4B4C2E_AdjustorThunk },
	{ 0x06000039, PhysicsScene2D_OverlapPoint_mBD3BD6137CC642F63968D6058E75467C1098BBCE_AdjustorThunk },
	{ 0x0600003A, PhysicsScene2D_OverlapPoint_mC2ED0510F29807B334351227C118F74CBC278227_AdjustorThunk },
	{ 0x0600003C, PhysicsScene2D_OverlapPoint_m3BEA82B608B1338872A28CE4A7A8BCB4A45A7F07_AdjustorThunk },
	{ 0x0600003D, PhysicsScene2D_OverlapPoint_m19614587E2F3CD617B899513DDA8299F455960A1_AdjustorThunk },
	{ 0x0600003F, PhysicsScene2D_OverlapPoint_m3C062202164C34E05357D6D53864132CB4DF3325_AdjustorThunk },
	{ 0x06000041, PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk },
	{ 0x06000042, PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk },
	{ 0x06000044, PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk },
	{ 0x06000045, PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk },
	{ 0x06000047, PhysicsScene2D_OverlapCircle_m****************************************_AdjustorThunk },
	{ 0x06000049, PhysicsScene2D_OverlapBox_m92B0E7E2244148FB38CB5EC0782C55E4CE5D41C6_AdjustorThunk },
	{ 0x0600004A, PhysicsScene2D_OverlapBox_m335088F6FBADF523759D88B56B4CE8B351BACEBF_AdjustorThunk },
	{ 0x0600004C, PhysicsScene2D_OverlapBox_m822F0FBB0CB890B8B3DCF1B513EDD45686325D58_AdjustorThunk },
	{ 0x0600004D, PhysicsScene2D_OverlapBox_mE4BBC0516865E1BFB5CC23E85FCC6985EE1F1673_AdjustorThunk },
	{ 0x0600004F, PhysicsScene2D_OverlapBox_m87AB321755E4631C49A58FD12E85A6046F78A20B_AdjustorThunk },
	{ 0x06000051, PhysicsScene2D_OverlapArea_mFD58E3BBEEE4357EB0BBA9A96EBB58012E7270A0_AdjustorThunk },
	{ 0x06000052, PhysicsScene2D_OverlapArea_m4D8FAFD9E7A299823649C9043C70456FF63F0655_AdjustorThunk },
	{ 0x06000053, PhysicsScene2D_OverlapAreaToBoxArray_Internal_m330E73DB6C450B36501A02801C5C77A4BD1F1B19_AdjustorThunk },
	{ 0x06000054, PhysicsScene2D_OverlapArea_m00070511C0F182CFDAC6BC57A057ADDCC671B1F3_AdjustorThunk },
	{ 0x06000055, PhysicsScene2D_OverlapArea_mD7B8AA443BB67D29957C8F0AA14DD4AC99F93F2B_AdjustorThunk },
	{ 0x06000056, PhysicsScene2D_OverlapAreaToBoxArray_Internal_m81756D2895F9044EA86F0B21A47A16FB3F2AD592_AdjustorThunk },
	{ 0x06000057, PhysicsScene2D_OverlapArea_m1DBB709EB60C3BCBF2064C98E247360DCE93C1FB_AdjustorThunk },
	{ 0x06000058, PhysicsScene2D_OverlapAreaToBoxList_Internal_m9D3425E6BC3D06B023D05F38ED4F50E1E178459E_AdjustorThunk },
	{ 0x06000059, PhysicsScene2D_OverlapCapsule_mB9EED3130F064B14CB4895858D6A25B7FE9CAF47_AdjustorThunk },
	{ 0x0600005A, PhysicsScene2D_OverlapCapsule_mB521741943F713EB037454DA5EB61CF4E405B364_AdjustorThunk },
	{ 0x0600005C, PhysicsScene2D_OverlapCapsule_m98A5052A0721B060EF518A167268F1384F36565B_AdjustorThunk },
	{ 0x0600005D, PhysicsScene2D_OverlapCapsule_m950A00AE483884975510112A38DDAC9ED30358EA_AdjustorThunk },
	{ 0x0600005F, PhysicsScene2D_OverlapCapsule_mD621AE5E0F3095B5E7A9B6662DD4D55B1C0FAA31_AdjustorThunk },
	{ 0x060001E1, PhysicsShape2D_get_shapeType_mBA55638729252854535E3E9279F7A006B610B01F_AdjustorThunk },
	{ 0x060001E2, PhysicsShape2D_set_shapeType_m8A00238C6DFD5ABF398700C86492296DB9DC6D9B_AdjustorThunk },
	{ 0x060001E3, PhysicsShape2D_get_radius_m4300D92A9F9523277976419369F0A14DC75AEF8B_AdjustorThunk },
	{ 0x060001E4, PhysicsShape2D_set_radius_mF96363A2D7B8112F2994D38A1113C8FC441FF864_AdjustorThunk },
	{ 0x060001E5, PhysicsShape2D_get_vertexStartIndex_m23782C197FC0521A6DC3A4E0F115DB477042EC40_AdjustorThunk },
	{ 0x060001E6, PhysicsShape2D_set_vertexStartIndex_m289E3881B77BEBA99E28B698AEEBFFC0F4A5DD00_AdjustorThunk },
	{ 0x060001E7, PhysicsShape2D_get_vertexCount_mBB09936295C475647B8E92C1464F4C2F3CA7A8D2_AdjustorThunk },
	{ 0x060001E8, PhysicsShape2D_set_vertexCount_m982167474864E44AA5DC6ED6CFD854E757B6702A_AdjustorThunk },
	{ 0x060001E9, PhysicsShape2D_get_useAdjacentStart_mF9EE25B1A542E807AA8AFB722F79D7CE0937271B_AdjustorThunk },
	{ 0x060001EA, PhysicsShape2D_set_useAdjacentStart_mF419418F7F43E9F30BA926E373730A3D4DE68D4E_AdjustorThunk },
	{ 0x060001EB, PhysicsShape2D_get_useAdjacentEnd_mE04E765CF666B95B6C49E34A38ED2F056B9E4CEA_AdjustorThunk },
	{ 0x060001EC, PhysicsShape2D_set_useAdjacentEnd_mDC730349B1B72E2B095BC5A2858BC34C2EF18D2B_AdjustorThunk },
	{ 0x060001ED, PhysicsShape2D_get_adjacentStart_mC99F496D28E98CD8D59425D4815275530414F94E_AdjustorThunk },
	{ 0x060001EE, PhysicsShape2D_set_adjacentStart_m6EDB2AD4B54FD968C0E6E3C783045FA121B5D081_AdjustorThunk },
	{ 0x060001EF, PhysicsShape2D_get_adjacentEnd_m8C5EA386C240C0A123C12C849892922D8D1F11DA_AdjustorThunk },
	{ 0x060001F0, PhysicsShape2D_set_adjacentEnd_m6646CBEFCA64FF1FA84B2E01B6C18AD6F351CEC1_AdjustorThunk },
	{ 0x0600020A, GroupState_ClearGeometry_m4A1E29A3DFE71E4426BD2F02D7067C8E32BEBE8F_AdjustorThunk },
	{ 0x0600020B, ColliderDistance2D_get_pointA_m55571F64432B8539AF715F91AE9F01E039B1BD3B_AdjustorThunk },
	{ 0x0600020C, ColliderDistance2D_set_pointA_m4334AB6925BF3CA93F3993E260CE986702C0AC8B_AdjustorThunk },
	{ 0x0600020D, ColliderDistance2D_get_pointB_m8C42A60CF6DFD3257CA1EF994BAE5DE2956F732A_AdjustorThunk },
	{ 0x0600020E, ColliderDistance2D_set_pointB_mF53E3F9EA595B627B64344411163212631DF42D0_AdjustorThunk },
	{ 0x0600020F, ColliderDistance2D_get_normal_m751A946B91F2A85A5F37376D4C4BC6BD42B437ED_AdjustorThunk },
	{ 0x06000210, ColliderDistance2D_get_distance_m6754976414E63C5F8B9493FDECA2210F5339F3C7_AdjustorThunk },
	{ 0x06000211, ColliderDistance2D_set_distance_m1CFF30A40C19AE4F4ACB11B0B157B1FDE8DD56E4_AdjustorThunk },
	{ 0x06000212, ColliderDistance2D_get_isOverlapped_mEFF81A407A9EF37A3800E1050B9D9C789DF3F0D3_AdjustorThunk },
	{ 0x06000213, ColliderDistance2D_get_isValid_m044D48E09BD8ADC4A01708B1D9478E43098EDF93_AdjustorThunk },
	{ 0x06000214, ColliderDistance2D_set_isValid_m356B0AEAF9421D48DD98304D8460893FE44C82DB_AdjustorThunk },
	{ 0x06000215, ContactFilter2D_NoFilter_m15980FED0667A151078679DF555638BEEA7C01DE_AdjustorThunk },
	{ 0x06000216, ContactFilter2D_CheckConsistency_mD918F11F977EA35E87CF491F7AE8794F5D01DF72_AdjustorThunk },
	{ 0x06000217, ContactFilter2D_ClearLayerMask_m86FA33B78DAAC6E04A4ED62F73CDD3D34B0DF68A_AdjustorThunk },
	{ 0x06000218, ContactFilter2D_SetLayerMask_mC3FBC2D806C1A3ACB2D060CE48F8157505E42F9B_AdjustorThunk },
	{ 0x06000219, ContactFilter2D_ClearDepth_m6CBAEF48B84E53079CC628E186EC6DD940C3151E_AdjustorThunk },
	{ 0x0600021A, ContactFilter2D_SetDepth_mE614DDDDAEA489D150E61D2DF8104F9292236F18_AdjustorThunk },
	{ 0x0600021B, ContactFilter2D_ClearNormalAngle_m7C7A157AF9CF67AF3D1094770B6B910BFEDC23D3_AdjustorThunk },
	{ 0x0600021C, ContactFilter2D_SetNormalAngle_m9A737032D43865398C15881FF1764694D0601B33_AdjustorThunk },
	{ 0x0600021D, ContactFilter2D_get_isFiltering_m993424DE9FBBA01394167C8FC546E4526C564A74_AdjustorThunk },
	{ 0x0600021E, ContactFilter2D_IsFilteringTrigger_m48921B2B2B5254FE3033757321224C14E7AB9884_AdjustorThunk },
	{ 0x0600021F, ContactFilter2D_IsFilteringLayerMask_m353947849DAF100E9125B0D92349B4763C76139A_AdjustorThunk },
	{ 0x06000220, ContactFilter2D_IsFilteringDepth_m455CB30F384EFEB68951CFEA42F26D2F15BC8886_AdjustorThunk },
	{ 0x06000221, ContactFilter2D_IsFilteringNormalAngle_m67548CED7AB9D9029B88C405A23F67307E95EA8F_AdjustorThunk },
	{ 0x06000222, ContactFilter2D_IsFilteringNormalAngle_mCBCAD097FC2B202E4C54A8A2EC13883EEE652C64_AdjustorThunk },
	{ 0x06000223, ContactFilter2D_IsFilteringNormalAngleUsingAngle_m30C52106865DBBAD20B012FD86699324E7BA2CC6_AdjustorThunk },
	{ 0x06000237, ContactPoint2D_get_point_mFF9B7395F63E748507C85166F3EDC218B8740396_AdjustorThunk },
	{ 0x06000238, ContactPoint2D_get_normal_m421147AFFC1A029B4DEC775C6B9197919ED93D21_AdjustorThunk },
	{ 0x06000239, ContactPoint2D_get_separation_m70174AAA4EC5B1857607115B25BED77BA142EA5E_AdjustorThunk },
	{ 0x0600023A, ContactPoint2D_get_normalImpulse_m601808AD6F4E1F81E4C9E53401556C465D161288_AdjustorThunk },
	{ 0x0600023B, ContactPoint2D_get_tangentImpulse_mC45ADFB72CA45EE4C430598511DB6534AF3F5CB4_AdjustorThunk },
	{ 0x0600023C, ContactPoint2D_get_relativeVelocity_m0DAD8E66E82BE6A3133618EFCB2CA579FD0F5D7D_AdjustorThunk },
	{ 0x0600023D, ContactPoint2D_get_collider_mCEC4BBE3C9CF0977C3EC5D529C2D5B664180768F_AdjustorThunk },
	{ 0x0600023E, ContactPoint2D_get_otherCollider_m1892E5E5AA0032610E8252FC371654E4198A7779_AdjustorThunk },
	{ 0x0600023F, ContactPoint2D_get_rigidbody_m28CDDD12EB3F7C06D05126C1ECA3AEA9594E1FF3_AdjustorThunk },
	{ 0x06000240, ContactPoint2D_get_otherRigidbody_mAE4893B039030B7AF7B645D2EEA0BD0F142CE6D9_AdjustorThunk },
	{ 0x06000241, ContactPoint2D_get_enabled_m1AC0022C616EBDD9012C25A6C9FD21766E87686C_AdjustorThunk },
	{ 0x06000242, JointAngleLimits2D_get_min_mABF1DD59D9AF0093C3A6C3EA039B2CE7B3F0AC1A_AdjustorThunk },
	{ 0x06000243, JointAngleLimits2D_set_min_m9EBCBBF3B7D4126158DB2405A2C2333DFFE48B29_AdjustorThunk },
	{ 0x06000244, JointAngleLimits2D_get_max_m2673C23C93D2802B61332C9BA58BD64E3A76BCC8_AdjustorThunk },
	{ 0x06000245, JointAngleLimits2D_set_max_m8264B45D23F709BA60BEF19645569D105A623D6F_AdjustorThunk },
	{ 0x06000246, JointTranslationLimits2D_get_min_m76573A6341DBDA6DA716A910851A19DBA7C6F773_AdjustorThunk },
	{ 0x06000247, JointTranslationLimits2D_set_min_m68279D0A2371102C82CC0511E4A8AEE9D697EDD4_AdjustorThunk },
	{ 0x06000248, JointTranslationLimits2D_get_max_mA2360BA3CD1A35E2E6C297D1EE478FD60757A699_AdjustorThunk },
	{ 0x06000249, JointTranslationLimits2D_set_max_m29EC807D57EC6702250B59CA07A207971E6D164B_AdjustorThunk },
	{ 0x0600024A, JointMotor2D_get_motorSpeed_mDD7B3F1E134AB8367191EAFEA683B82AC4952163_AdjustorThunk },
	{ 0x0600024B, JointMotor2D_set_motorSpeed_m080F930D6EC3A5BE6348C409B9115664E464B480_AdjustorThunk },
	{ 0x0600024C, JointMotor2D_get_maxMotorTorque_m911E081510303C1B7763686891ACFCEF6748C6EB_AdjustorThunk },
	{ 0x0600024D, JointMotor2D_set_maxMotorTorque_mFB41FE9052B411B7C43C29DE4978E04AD9C748AC_AdjustorThunk },
	{ 0x0600024E, JointSuspension2D_get_dampingRatio_m436AF1D3DE8C46C1F548E08AF83A0C0C546CFD25_AdjustorThunk },
	{ 0x0600024F, JointSuspension2D_set_dampingRatio_m6E706B4991620D9F8E54014779A788DF55A45DD4_AdjustorThunk },
	{ 0x06000250, JointSuspension2D_get_frequency_m62ACA94DE93973E8874544AF88BFC5C56AE7F0F3_AdjustorThunk },
	{ 0x06000251, JointSuspension2D_set_frequency_mE8B115A66AD8FADFC3C035075A3662C0C16E89B6_AdjustorThunk },
	{ 0x06000252, JointSuspension2D_get_angle_mF95F89C72EFF15D5CC37CFFD93FAF8BD8437C0E1_AdjustorThunk },
	{ 0x06000253, JointSuspension2D_set_angle_m27413F565F50EEF07F617ED60539527175B73BB5_AdjustorThunk },
	{ 0x06000254, RaycastHit2D_get_centroid_mEA7A6ACCFE6C0E7566B0C177A54A750EC554C704_AdjustorThunk },
	{ 0x06000255, RaycastHit2D_set_centroid_mFB5F56330BAA9C8CE547AFE75648FD8E426776D8_AdjustorThunk },
	{ 0x06000256, RaycastHit2D_get_point_mB35E988E9E04328EFE926228A18334326721A36B_AdjustorThunk },
	{ 0x06000257, RaycastHit2D_set_point_m13D917ABD5F8FEB291FED13D2DB2A2481E085FAC_AdjustorThunk },
	{ 0x06000258, RaycastHit2D_get_normal_m75F1EBDE347BACEB5A6A6AA72543C740806AB5F2_AdjustorThunk },
	{ 0x06000259, RaycastHit2D_set_normal_m36B4A488824FD1E4D0F47AE13211C4D773FE6799_AdjustorThunk },
	{ 0x0600025A, RaycastHit2D_get_distance_mD0FE1482E2768CF587AFB65488459697EAB64613_AdjustorThunk },
	{ 0x0600025B, RaycastHit2D_set_distance_m652B2B50F02583A4FAD019190AEFA1D402B0FA33_AdjustorThunk },
	{ 0x0600025C, RaycastHit2D_get_fraction_m9BF416582F5C4D5FC8D93E5DA57B4CDC64E030BE_AdjustorThunk },
	{ 0x0600025D, RaycastHit2D_set_fraction_m3FE49691CA64CB6EB030AE87C4FAEA28285C1062_AdjustorThunk },
	{ 0x0600025E, RaycastHit2D_get_collider_mB56DFCD16B708852EEBDBB490BC8665DBF7487FD_AdjustorThunk },
	{ 0x0600025F, RaycastHit2D_get_rigidbody_mA7C26ACF22912C14CC6C8B1D7C50F38BCF5096B8_AdjustorThunk },
	{ 0x06000260, RaycastHit2D_get_transform_mA5E3F8DC9914E79D3C9F6F3F2515B49EEBB4564A_AdjustorThunk },
	{ 0x06000262, RaycastHit2D_CompareTo_mF4665ADD9F8B212987443C052ADE73E8C0DF2612_AdjustorThunk },
	{ 0x06000263, PhysicsJobOptions2D_get_useMultithreading_m8967EDBBDC51011BE1C0FB7FC437643F8E0E8DFA_AdjustorThunk },
	{ 0x06000264, PhysicsJobOptions2D_set_useMultithreading_mF475BF5CDDBBEA27D50F2B60E00CC9BCAFA016A9_AdjustorThunk },
	{ 0x06000265, PhysicsJobOptions2D_get_useConsistencySorting_m583424FC6BACAB1E6161F3BDEBAE102978630CDA_AdjustorThunk },
	{ 0x06000266, PhysicsJobOptions2D_set_useConsistencySorting_m4190351CA61E03766E72435E437DCB23CE1408CC_AdjustorThunk },
	{ 0x06000267, PhysicsJobOptions2D_get_interpolationPosesPerJob_m7F3C3C05C4AC1770E342EA6D6F63B709A610DFB8_AdjustorThunk },
	{ 0x06000268, PhysicsJobOptions2D_set_interpolationPosesPerJob_mA764F435978E7BD6005F1B560516B7B9AE2A123E_AdjustorThunk },
	{ 0x06000269, PhysicsJobOptions2D_get_newContactsPerJob_mCA477A50F149B5DADC5E2A772E48B746638DC761_AdjustorThunk },
	{ 0x0600026A, PhysicsJobOptions2D_set_newContactsPerJob_mA2C104942792A9188ADE151D5221FF18F3EF052B_AdjustorThunk },
	{ 0x0600026B, PhysicsJobOptions2D_get_collideContactsPerJob_mC27C3A2243ED724C4D147C210595716AAECDC217_AdjustorThunk },
	{ 0x0600026C, PhysicsJobOptions2D_set_collideContactsPerJob_m199A8D06195B95564503429B109C7E32B3E42138_AdjustorThunk },
	{ 0x0600026D, PhysicsJobOptions2D_get_clearFlagsPerJob_m0F1299FC15FDAE5A030772D1D1A01E9FA522AC94_AdjustorThunk },
	{ 0x0600026E, PhysicsJobOptions2D_set_clearFlagsPerJob_mD198700A6FEA3696DC7A8F54E043A337B18D97B3_AdjustorThunk },
	{ 0x0600026F, PhysicsJobOptions2D_get_clearBodyForcesPerJob_mCA5DC9F62EC9BCB44D9AD7C50F5D72DEA79CB560_AdjustorThunk },
	{ 0x06000270, PhysicsJobOptions2D_set_clearBodyForcesPerJob_m85773E07D803A633C58928F66588ED04FB8EE78F_AdjustorThunk },
	{ 0x06000271, PhysicsJobOptions2D_get_syncDiscreteFixturesPerJob_m44F03D367416DB932C675A23B30BA8CE81DED94E_AdjustorThunk },
	{ 0x06000272, PhysicsJobOptions2D_set_syncDiscreteFixturesPerJob_m59988BD56ADE10BF7D55B1E3BB6A505EE5290DEB_AdjustorThunk },
	{ 0x06000273, PhysicsJobOptions2D_get_syncContinuousFixturesPerJob_m3AA97386BBD0EE4BE60232DA0940D1C0B00F6549_AdjustorThunk },
	{ 0x06000274, PhysicsJobOptions2D_set_syncContinuousFixturesPerJob_m8F99FB132D69D00C86FFA1788CF8B084E929DD65_AdjustorThunk },
	{ 0x06000275, PhysicsJobOptions2D_get_findNearestContactsPerJob_m989A3ACBF42B19E2EA50E2414A8EF3A876B1C7D9_AdjustorThunk },
	{ 0x06000276, PhysicsJobOptions2D_set_findNearestContactsPerJob_m7A2EC665BD757F95E86BC950E91BA37B1AC2588A_AdjustorThunk },
	{ 0x06000277, PhysicsJobOptions2D_get_updateTriggerContactsPerJob_m5756767F8BA939BF8834E3F3F244BD0AFFC3E8CA_AdjustorThunk },
	{ 0x06000278, PhysicsJobOptions2D_set_updateTriggerContactsPerJob_m200A1E3BC34797C4C40B6F375AE0E492892CBD66_AdjustorThunk },
	{ 0x06000279, PhysicsJobOptions2D_get_islandSolverCostThreshold_m7214005F53C4136C65DDBB7BF40352CF5C69F307_AdjustorThunk },
	{ 0x0600027A, PhysicsJobOptions2D_set_islandSolverCostThreshold_m08171184FFAF1C0C8B268BC15FAA961719906D35_AdjustorThunk },
	{ 0x0600027B, PhysicsJobOptions2D_get_islandSolverBodyCostScale_mBB11308FF9BB281DD21B800FD02701F9AFC5E881_AdjustorThunk },
	{ 0x0600027C, PhysicsJobOptions2D_set_islandSolverBodyCostScale_m0F5568713C8A7D056614EAECA50F1EC42DDA6E4C_AdjustorThunk },
	{ 0x0600027D, PhysicsJobOptions2D_get_islandSolverContactCostScale_mB24A44A346D134D75B93D8F35477D12F2D5E29F8_AdjustorThunk },
	{ 0x0600027E, PhysicsJobOptions2D_set_islandSolverContactCostScale_mB1848684FA85C5F324896437840B4712CC57B162_AdjustorThunk },
	{ 0x0600027F, PhysicsJobOptions2D_get_islandSolverJointCostScale_mD23FABF4C84E1ED3456E8E9AF0E4EF2902E769C6_AdjustorThunk },
	{ 0x06000280, PhysicsJobOptions2D_set_islandSolverJointCostScale_m85293EF48F13BABF165CA4C1B04960F0C0C70A38_AdjustorThunk },
	{ 0x06000281, PhysicsJobOptions2D_get_islandSolverBodiesPerJob_mBC375DF0FB397948AAB54BC4ED7593377F989A97_AdjustorThunk },
	{ 0x06000282, PhysicsJobOptions2D_set_islandSolverBodiesPerJob_m927451FE8439EBCE47532EB6B0BFC1546AD63294_AdjustorThunk },
	{ 0x06000283, PhysicsJobOptions2D_get_islandSolverContactsPerJob_mD484AFA46C160C184837E58C462CA9E19E28D5D3_AdjustorThunk },
	{ 0x06000284, PhysicsJobOptions2D_set_islandSolverContactsPerJob_mF162C21E0D8466EFCE0AD8D4ECDF4646497DDDD3_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1271] = 
{
	4250,
	7270,
	7270,
	4216,
	3185,
	3191,
	4168,
	8222,
	4168,
	8222,
	3229,
	1838,
	1837,
	6014,
	1147,
	1146,
	5281,
	1146,
	5281,
	1250,
	1249,
	5415,
	747,
	746,
	4951,
	746,
	4951,
	816,
	815,
	5035,
	464,
	463,
	4758,
	463,
	4758,
	512,
	511,
	4813,
	288,
	287,
	4631,
	287,
	4631,
	308,
	307,
	4662,
	170,
	169,
	4549,
	169,
	4549,
	1836,
	5416,
	1138,
	4952,
	4952,
	2543,
	2542,
	6709,
	1736,
	1734,
	5833,
	1734,
	5833,
	1822,
	1821,
	5978,
	1145,
	1144,
	5280,
	1144,
	5280,
	1242,
	1241,
	5391,
	747,
	746,
	4951,
	746,
	4951,
	1824,
	1823,
	1823,
	1147,
	1146,
	1146,
	1146,
	1146,
	813,
	812,
	5023,
	466,
	465,
	4759,
	465,
	4759,
	6537,
	6522,
	6522,
	6522,
	6522,
	8200,
	8200,
	5456,
	5245,
	5245,
	5066,
	4927,
	4927,
	4830,
	4745,
	4745,
	4669,
	4623,
	4623,
	4571,
	4538,
	4538,
	5067,
	4928,
	4928,
	6620,
	5776,
	5776,
	5893,
	5247,
	5247,
	5323,
	4927,
	4927,
	4978,
	4742,
	4742,
	6519,
	6519,
	8538,
	8538,
	7894,
	9036,
	9018,
	8880,
	9018,
	8880,
	9084,
	8907,
	8993,
	8868,
	8993,
	8868,
	8993,
	8868,
	8993,
	8868,
	8993,
	8868,
	9018,
	8880,
	9034,
	8888,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	8233,
	7271,
	9089,
	7975,
	6980,
	7261,
	7928,
	6899,
	6899,
	7226,
	7226,
	7928,
	7928,
	8395,
	8395,
	7261,
	6367,
	6367,
	7256,
	7256,
	8220,
	7259,
	7380,
	7380,
	7847,
	7847,
	7847,
	7847,
	7700,
	6756,
	6016,
	5418,
	5845,
	5845,
	7670,
	6731,
	5996,
	5401,
	5979,
	6555,
	5846,
	5293,
	4961,
	7700,
	6757,
	6017,
	5420,
	5038,
	5845,
	5291,
	5291,
	6555,
	5847,
	5294,
	4962,
	4765,
	7670,
	6732,
	5998,
	5403,
	5030,
	5391,
	6755,
	6015,
	5417,
	5036,
	4814,
	5289,
	4956,
	4956,
	6730,
	5995,
	5399,
	5027,
	4806,
	5022,
	5844,
	5290,
	4957,
	4761,
	4632,
	6018,
	5421,
	5039,
	4816,
	4664,
	4964,
	4767,
	4767,
	5999,
	5404,
	5031,
	4809,
	4660,
	4803,
	5297,
	4965,
	4768,
	4636,
	4551,
	5419,
	5037,
	4815,
	4663,
	4567,
	4763,
	4634,
	4634,
	5402,
	5029,
	4658,
	4808,
	4659,
	4565,
	4960,
	4764,
	4635,
	4550,
	4498,
	8579,
	7699,
	6754,
	8507,
	7646,
	6711,
	5392,
	7495,
	6546,
	5835,
	8524,
	7667,
	6728,
	5993,
	6552,
	6552,
	8524,
	7667,
	6728,
	5993,
	6709,
	7509,
	6553,
	5841,
	5287,
	7669,
	6729,
	5994,
	5398,
	5842,
	5842,
	7669,
	6729,
	5994,
	5398,
	5978,
	6554,
	5843,
	5288,
	4955,
	6732,
	5998,
	5403,
	5030,
	5295,
	5295,
	6732,
	5998,
	5403,
	5030,
	5391,
	5848,
	5296,
	4963,
	4766,
	7670,
	6731,
	5996,
	5401,
	5845,
	5845,
	7670,
	6731,
	5996,
	5401,
	5401,
	6555,
	5846,
	5293,
	4961,
	5996,
	5400,
	5028,
	4807,
	4958,
	4958,
	5996,
	5400,
	5028,
	4807,
	5023,
	5292,
	4959,
	4762,
	4633,
	6522,
	6522,
	5822,
	7489,
	6522,
	7489,
	6522,
	7489,
	6522,
	7489,
	6522,
	6522,
	5822,
	6522,
	6522,
	6522,
	5822,
	7489,
	6522,
	7489,
	6522,
	7489,
	6522,
	7489,
	6522,
	6522,
	5822,
	6522,
	6522,
	6522,
	7914,
	8993,
	8868,
	8993,
	8868,
	8993,
	8868,
	8993,
	8868,
	9064,
	8899,
	8993,
	8868,
	8996,
	8871,
	8996,
	8871,
	8996,
	8871,
	8996,
	8871,
	9064,
	8899,
	8993,
	8868,
	8993,
	8868,
	8993,
	8868,
	8993,
	8868,
	8993,
	8868,
	4364,
	9089,
	8867,
	8867,
	8867,
	8867,
	7146,
	6365,
	7252,
	6979,
	6867,
	6867,
	5892,
	5323,
	4980,
	4773,
	4642,
	5324,
	6620,
	5893,
	5323,
	4978,
	6519,
	5819,
	6519,
	6519,
	6519,
	6519,
	5819,
	6519,
	6519,
	6519,
	4216,
	3852,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4168,
	3807,
	4168,
	3807,
	4356,
	3978,
	4356,
	3978,
	4250,
	4250,
	4216,
	4216,
	4240,
	3875,
	2734,
	4364,
	3881,
	2802,
	2639,
	2739,
	2614,
	1979,
	2745,
	858,
	3852,
	3536,
	2453,
	1740,
	1148,
	3419,
	2437,
	453,
	6825,
	4364,
	4356,
	3978,
	4356,
	3978,
	4356,
	4298,
	3928,
	4168,
	4168,
	3807,
	4178,
	4364,
	4364,
	3865,
	4364,
	2850,
	4364,
	2850,
	4168,
	3185,
	3185,
	3185,
	3277,
	3229,
	3229,
	6428,
	8867,
	7140,
	7146,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4356,
	4168,
	4250,
	4216,
	3338,
	3419,
	3419,
	4364,
	4356,
	4356,
	4298,
	4298,
	4298,
	4356,
	4250,
	4250,
	4250,
	4250,
	4168,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4356,
	3978,
	4356,
	3978,
	4356,
	3978,
	4298,
	3928,
	4298,
	3928,
	4250,
	4250,
	4250,
	8228,
	3421,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4356,
	3978,
	4298,
	3928,
	3928,
	3928,
	3896,
	3896,
	3978,
	3928,
	3928,
	3896,
	3896,
	4356,
	3978,
	4298,
	3928,
	4168,
	3807,
	4298,
	3928,
	4250,
	3881,
	4356,
	3978,
	4356,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4168,
	4168,
	4364,
	4364,
	4168,
	3807,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	4356,
	3978,
	4298,
	3928,
	4230,
	3865,
	4230,
	3865,
	3185,
	2317,
	2317,
	3126,
	3126,
	4168,
	3166,
	3277,
	3310,
	3310,
	3662,
	3978,
	2884,
	3978,
	2884,
	2887,
	2171,
	3928,
	2848,
	3662,
	3662,
	3662,
	3662,
	3662,
	3662,
	2419,
	2419,
	2419,
	2419,
	3419,
	3419,
	2419,
	2419,
	3419,
	3419,
	2419,
	2419,
	3419,
	3419,
	3419,
	3419,
	2452,
	1737,
	1739,
	1737,
	1739,
	1734,
	1141,
	1144,
	1141,
	1144,
	3419,
	3397,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	2315,
	3079,
	3079,
	2787,
	2659,
	2659,
	1906,
	2657,
	2657,
	2657,
	2657,
	2657,
	2657,
	2414,
	2414,
	1696,
	1696,
	1109,
	1109,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4250,
	4356,
	3978,
	4250,
	4216,
	2491,
	4350,
	3419,
	1724,
	1690,
	4166,
	4216,
	4168,
	4250,
	3881,
	4216,
	3852,
	4230,
	3865,
	4230,
	3865,
	4230,
	3865,
	4230,
	3865,
	4230,
	3865,
	4230,
	3865,
	4298,
	4298,
	3185,
	2317,
	2317,
	3126,
	3126,
	4168,
	3166,
	3277,
	3310,
	2419,
	2419,
	3419,
	3419,
	2419,
	2419,
	3419,
	3419,
	2419,
	2419,
	2452,
	1737,
	1142,
	1734,
	1141,
	743,
	745,
	743,
	745,
	2452,
	1737,
	1143,
	744,
	462,
	1734,
	1141,
	1144,
	1141,
	1144,
	3662,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	2315,
	3079,
	3079,
	720,
	720,
	1109,
	1109,
	4216,
	4216,
	3419,
	1724,
	1690,
	2406,
	1119,
	3881,
	3788,
	2639,
	1378,
	2053,
	1912,
	1277,
	558,
	2734,
	2734,
	4364,
	4364,
	4298,
	3928,
	4356,
	3978,
	4364,
	4356,
	3978,
	4216,
	3852,
	4364,
	3788,
	3788,
	4364,
	4298,
	3928,
	4216,
	4216,
	4250,
	3881,
	3419,
	3185,
	4168,
	3807,
	4168,
	3807,
	4356,
	3978,
	4356,
	3978,
	4364,
	3788,
	3788,
	3788,
	3788,
	4356,
	3978,
	4298,
	3928,
	4168,
	3807,
	4356,
	3978,
	4364,
	3788,
	3788,
	4168,
	3807,
	4168,
	3807,
	4216,
	4250,
	3881,
	4216,
	3852,
	3515,
	3515,
	2739,
	2739,
	2425,
	2425,
	2739,
	2739,
	3852,
	2750,
	2001,
	1368,
	4364,
	1331,
	4216,
	3852,
	4216,
	3852,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
	3415,
	3415,
	4216,
	4216,
	2425,
	2425,
	2425,
	2425,
	4364,
	4250,
	4250,
	3881,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4356,
	4298,
	3661,
	3586,
	4168,
	3807,
	4364,
	3788,
	2845,
	4356,
	3978,
	4356,
	3978,
	4168,
	3807,
	4364,
	3788,
	3788,
	3788,
	3788,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
	4168,
	3807,
	4298,
	3928,
	4168,
	3807,
	4364,
	4298,
	3928,
	4298,
	3928,
	4364,
	4168,
	3807,
	4168,
	3807,
	4225,
	3860,
	4221,
	3856,
	4216,
	4298,
	4298,
	4298,
	3586,
	4364,
	3788,
	3788,
	3788,
	3788,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4356,
	3978,
	4298,
	3928,
	4356,
	4364,
	3788,
	3788,
	3788,
	4168,
	3807,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4225,
	3860,
	4228,
	3863,
	4216,
	4298,
	4298,
	4298,
	3586,
	4364,
	3788,
	3788,
	3788,
	3788,
	4356,
	3978,
	4356,
	3978,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
	3788,
	3788,
	3788,
	3788,
	4298,
	3928,
	4298,
	3928,
	4298,
	4364,
	4227,
	3862,
	4168,
	3807,
	4225,
	3860,
	4298,
	4298,
	4298,
	4298,
	3586,
	4364,
	3788,
	3788,
	3788,
	3788,
	4168,
	3807,
	4216,
	3852,
	4168,
	4168,
	4168,
	4364,
	4298,
	3928,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4298,
	3928,
	4364,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4364,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4298,
	3928,
	4364,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4364,
	4364,
	4356,
	3978,
	4356,
	3978,
	4298,
	3928,
	4364,
	3788,
	3788,
	3788,
	3788,
	4364,
	3881,
	7975,
	4298,
	3928,
	4298,
	3928,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_Physics2DModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_Physics2DModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_Physics2DModule_CodeGenModule = 
{
	"UnityEngine.Physics2DModule.dll",
	1271,
	s_methodPointers,
	181,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_Physics2DModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
