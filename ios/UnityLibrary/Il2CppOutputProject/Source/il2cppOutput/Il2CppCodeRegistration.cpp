﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif







IL2CPP_EXTERN_C const Il2CppMethodPointer g_ReversePInvokeWrapperPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_Il2CppGenericMethodPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_Il2CppGenericAdjustorThunks[];
IL2CPP_EXTERN_C const InvokerMethod g_Il2CppInvokerPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_UnresolvedVirtualMethodPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_UnresolvedInstanceMethodPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_UnresolvedStaticMethodPointers[];
IL2CPP_EXTERN_C Il2CppInteropData g_Il2CppInteropData[];
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AssemblyU2DCSharpU2Dfirstpass_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_IngameDebugConsole_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Microsoft_Bcl_TimeProvider_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Mono_Data_Sqlite_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Mono_Security_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Newtonsoft_Json_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_R3_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_R3_Unity_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Configuration_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Core_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Data_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Drawing_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Numerics_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Runtime_CompilerServices_Unsafe_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Runtime_Serialization_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Transactions_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Xml_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Xml_Linq_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UniTask_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UniTask_Linq_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ARModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AccessibilityModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AndroidJNIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AnimationModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AssetBundleModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AudioModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ClothModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ContentLoadModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_CoreModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_CrashReportingModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_DSPGraphModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_DirectorModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_GIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_GameCenterModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_GridModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_HotReloadModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_IMGUIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ImageConversionModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_InputModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_JSONSerializeModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_LocalizationModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ParticleSystemModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_PerformanceReportingModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_Physics2DModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ProfilerModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_PropertiesModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_RuntimeInitializeOnLoadManagerInitializerModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ScreenCaptureModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SharedInternalsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SpriteMaskModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SpriteShapeModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_StreamingModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SubstanceModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SubsystemsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TLSModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TerrainModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TerrainPhysicsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TextCoreFontEngineModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TextCoreTextEngineModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TilemapModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UIElementsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UI_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UmbraModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsCommonModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityConnectModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityCurlModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityTestProtocolModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestAssetBundleModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestAudioModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestTextureModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_VFXModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_VRModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_VehiclesModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_VideoModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_WindModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Burst_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Burst_Unsafe_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Collections_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Mathematics_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_TextMeshPro_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_VisualScripting_Core_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g___Generated_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_mscorlib_CodeGenModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule* g_CodeGenModules[];
const Il2CppCodeGenModule* g_CodeGenModules[95] = 
{
	(&g_AssemblyU2DCSharpU2Dfirstpass_CodeGenModule),
	(&g_AssemblyU2DCSharp_CodeGenModule),
	(&g_IngameDebugConsole_Runtime_CodeGenModule),
	(&g_Microsoft_Bcl_TimeProvider_CodeGenModule),
	(&g_Mono_Data_Sqlite_CodeGenModule),
	(&g_Mono_Security_CodeGenModule),
	(&g_Newtonsoft_Json_CodeGenModule),
	(&g_R3_CodeGenModule),
	(&g_R3_Unity_CodeGenModule),
	(&g_System_CodeGenModule),
	(&g_System_Configuration_CodeGenModule),
	(&g_System_Core_CodeGenModule),
	(&g_System_Data_CodeGenModule),
	(&g_System_Drawing_CodeGenModule),
	(&g_System_Numerics_CodeGenModule),
	(&g_System_Runtime_CompilerServices_Unsafe_CodeGenModule),
	(&g_System_Runtime_Serialization_CodeGenModule),
	(&g_System_Transactions_CodeGenModule),
	(&g_System_Xml_CodeGenModule),
	(&g_System_Xml_Linq_CodeGenModule),
	(&g_UniTask_CodeGenModule),
	(&g_UniTask_Linq_CodeGenModule),
	(&g_UnityEngine_AIModule_CodeGenModule),
	(&g_UnityEngine_ARModule_CodeGenModule),
	(&g_UnityEngine_AccessibilityModule_CodeGenModule),
	(&g_UnityEngine_AndroidJNIModule_CodeGenModule),
	(&g_UnityEngine_AnimationModule_CodeGenModule),
	(&g_UnityEngine_AssetBundleModule_CodeGenModule),
	(&g_UnityEngine_AudioModule_CodeGenModule),
	(&g_UnityEngine_ClothModule_CodeGenModule),
	(&g_UnityEngine_CodeGenModule),
	(&g_UnityEngine_ContentLoadModule_CodeGenModule),
	(&g_UnityEngine_CoreModule_CodeGenModule),
	(&g_UnityEngine_CrashReportingModule_CodeGenModule),
	(&g_UnityEngine_DSPGraphModule_CodeGenModule),
	(&g_UnityEngine_DirectorModule_CodeGenModule),
	(&g_UnityEngine_GIModule_CodeGenModule),
	(&g_UnityEngine_GameCenterModule_CodeGenModule),
	(&g_UnityEngine_GridModule_CodeGenModule),
	(&g_UnityEngine_HotReloadModule_CodeGenModule),
	(&g_UnityEngine_IMGUIModule_CodeGenModule),
	(&g_UnityEngine_ImageConversionModule_CodeGenModule),
	(&g_UnityEngine_InputLegacyModule_CodeGenModule),
	(&g_UnityEngine_InputModule_CodeGenModule),
	(&g_UnityEngine_JSONSerializeModule_CodeGenModule),
	(&g_UnityEngine_LocalizationModule_CodeGenModule),
	(&g_UnityEngine_ParticleSystemModule_CodeGenModule),
	(&g_UnityEngine_PerformanceReportingModule_CodeGenModule),
	(&g_UnityEngine_Physics2DModule_CodeGenModule),
	(&g_UnityEngine_PhysicsModule_CodeGenModule),
	(&g_UnityEngine_ProfilerModule_CodeGenModule),
	(&g_UnityEngine_PropertiesModule_CodeGenModule),
	(&g_UnityEngine_RuntimeInitializeOnLoadManagerInitializerModule_CodeGenModule),
	(&g_UnityEngine_ScreenCaptureModule_CodeGenModule),
	(&g_UnityEngine_SharedInternalsModule_CodeGenModule),
	(&g_UnityEngine_SpriteMaskModule_CodeGenModule),
	(&g_UnityEngine_SpriteShapeModule_CodeGenModule),
	(&g_UnityEngine_StreamingModule_CodeGenModule),
	(&g_UnityEngine_SubstanceModule_CodeGenModule),
	(&g_UnityEngine_SubsystemsModule_CodeGenModule),
	(&g_UnityEngine_TLSModule_CodeGenModule),
	(&g_UnityEngine_TerrainModule_CodeGenModule),
	(&g_UnityEngine_TerrainPhysicsModule_CodeGenModule),
	(&g_UnityEngine_TextCoreFontEngineModule_CodeGenModule),
	(&g_UnityEngine_TextCoreTextEngineModule_CodeGenModule),
	(&g_UnityEngine_TextRenderingModule_CodeGenModule),
	(&g_UnityEngine_TilemapModule_CodeGenModule),
	(&g_UnityEngine_UIElementsModule_CodeGenModule),
	(&g_UnityEngine_UIModule_CodeGenModule),
	(&g_UnityEngine_UI_CodeGenModule),
	(&g_UnityEngine_UmbraModule_CodeGenModule),
	(&g_UnityEngine_UnityAnalyticsCommonModule_CodeGenModule),
	(&g_UnityEngine_UnityAnalyticsModule_CodeGenModule),
	(&g_UnityEngine_UnityConnectModule_CodeGenModule),
	(&g_UnityEngine_UnityCurlModule_CodeGenModule),
	(&g_UnityEngine_UnityTestProtocolModule_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestAssetBundleModule_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestAudioModule_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestModule_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestTextureModule_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule),
	(&g_UnityEngine_VFXModule_CodeGenModule),
	(&g_UnityEngine_VRModule_CodeGenModule),
	(&g_UnityEngine_VehiclesModule_CodeGenModule),
	(&g_UnityEngine_VideoModule_CodeGenModule),
	(&g_UnityEngine_WindModule_CodeGenModule),
	(&g_UnityEngine_XRModule_CodeGenModule),
	(&g_Unity_Burst_CodeGenModule),
	(&g_Unity_Burst_Unsafe_CodeGenModule),
	(&g_Unity_Collections_CodeGenModule),
	(&g_Unity_Mathematics_CodeGenModule),
	(&g_Unity_TextMeshPro_CodeGenModule),
	(&g_Unity_VisualScripting_Core_CodeGenModule),
	(&g___Generated_CodeGenModule),
	(&g_mscorlib_CodeGenModule),
};
IL2CPP_EXTERN_C const Il2CppCodeRegistration g_CodeRegistration;
const Il2CppCodeRegistration g_CodeRegistration = 
{
	29,
	g_ReversePInvokeWrapperPointers,
	8948,
	g_Il2CppGenericMethodPointers,
	g_Il2CppGenericAdjustorThunks,
	9110,
	g_Il2CppInvokerPointers,
	847,
	g_UnresolvedVirtualMethodPointers,
	g_UnresolvedInstanceMethodPointers,
	g_UnresolvedStaticMethodPointers,
	785,
	g_Il2CppInteropData,
	0,
	NULL,
	95,
	g_CodeGenModules,
};
IL2CPP_EXTERN_C_CONST Il2CppMetadataRegistration g_MetadataRegistration;
static const Il2CppCodeGenOptions s_Il2CppCodeGenOptions = 
{
	true,
	0,
	0,
};
void s_Il2CppCodegenRegistration()
{
	il2cpp_codegen_register (&g_CodeRegistration, &g_MetadataRegistration, &s_Il2CppCodeGenOptions);
	#if IL2CPP_MONO_DEBUGGER
	il2cpp_codegen_register_debugger_data(NULL);
	#endif
}
#if RUNTIME_IL2CPP
typedef void (*CodegenRegistrationFunction)();
CodegenRegistrationFunction g_CodegenRegistration = s_Il2CppCodegenRegistration;
#endif
