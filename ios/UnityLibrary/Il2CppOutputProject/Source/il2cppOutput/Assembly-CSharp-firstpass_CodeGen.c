﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mC7CA174A23290C34424DF6D2733D5E64B92E5977 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m3C9D09F94200334DD5FA29A465481C7848AF4549 (void);
extern void ExtensionMethods_CTToTitleCase_mD8912604139364DA9F23864F1C881719C2406B0A (void);
extern void ExtensionMethods_CTReverse_m5B3CA05B6C3B8467983E996DB1DE3494953B1827 (void);
extern void ExtensionMethods_CTReplace_m09CC2E70AD6303C9E585600D1F79FF275FB9DCCB (void);
extern void ExtensionMethods_CTRemoveChars_m79B9B564B884EF3F675A7355255B611BA4983FD6 (void);
extern void ExtensionMethods_CTEquals_m57623DA37059652671F9B8BB4439D80F13CCC7BF (void);
extern void ExtensionMethods_CTContains_m2176C444D13298F8E63508431E9EE81F18B2C683 (void);
extern void ExtensionMethods_CTContainsAny_m96517DB3012A304D2BBCF44CF2267C81434C687A (void);
extern void ExtensionMethods_CTContainsAll_m1E27935C322EB3A9DD60158DE5FB64C6EE40C894 (void);
extern void ExtensionMethods_CTRemoveNewLines_mDA5C259756BF8DA7839A6D161FD95C944097AA6E (void);
extern void ExtensionMethods_CTAddNewLines_m006EE15C14BB34AD570AF5D2A6E3D267379E7E4D (void);
extern void ExtensionMethods_CTisNumeric_m3F984C7DE8A06F5F991678070157B56008F01C5F (void);
extern void ExtensionMethods_CTIsNumeric_m061DE6DA5CB082AB19F517B1602EDA3D20FE45E1 (void);
extern void ExtensionMethods_CTisInteger_m8A68507F188DD45A7B1C09DFA149B121B1E0CD09 (void);
extern void ExtensionMethods_CTIsInteger_m7D0070D58630EBD39F21743AD2F530BFB3FE9CD3 (void);
extern void ExtensionMethods_CTisEmail_m4E12D93FFF0CED3EAE7665FF56B5BFAC82931F67 (void);
extern void ExtensionMethods_CTIsEmail_m36A7472EF384288223DBC36A66595F695AA52C6A (void);
extern void ExtensionMethods_CTisWebsite_m4FD21F26D0BD084510D8C64AB941F270EA0759EF (void);
extern void ExtensionMethods_CTIsWebsite_m49D83E20FC464D94E02A5E6C4233DB8E516A8B9E (void);
extern void ExtensionMethods_CTisCreditcard_mEE8347CC6DC2EADA53531D4ED777BAC9B6E58569 (void);
extern void ExtensionMethods_CTIsCreditcard_m07FD11EE8C29E701EF34E9447260073D76D1CADA (void);
extern void ExtensionMethods_CTisIPv4_mC8724363392E74CB91B8C6AD3FB0C07BA5179F3F (void);
extern void ExtensionMethods_CTIsIPv4_m5C7BFBDC80406EA4F4F40E01BAB1BDB32961DBCD (void);
extern void ExtensionMethods_CTisAlphanumeric_mA50033DB9BEF56B2BF6DBC3307F478CCDCF536D1 (void);
extern void ExtensionMethods_CTIsAlphanumeric_m278B2D880099C39F51977474FCFF6EAB6EFA27C5 (void);
extern void ExtensionMethods_CThasLineEndings_mA23A8D573B0133FA0E5268E76C57FD73BA0BE94A (void);
extern void ExtensionMethods_CTHasLineEndings_m83F9E4A20C1E840F6E0DBB2CBDC5E9AF585F1A5D (void);
extern void ExtensionMethods_CThasInvalidChars_mFE6FDEC1F0D6C3CC791B37F3B4CD56D44DC888B9 (void);
extern void ExtensionMethods_CTHasInvalidChars_m35B090986495AB8B3CC4DA3485CCA4A8E45296AC (void);
extern void ExtensionMethods_CTStartsWith_mB5C1E02A6876078E0393429E756D4BA4DD012B68 (void);
extern void ExtensionMethods_CTEndsWith_mBB9B06D1DB2ED41CDD982F06ED82124EB6445F61 (void);
extern void ExtensionMethods_CTLastIndexOf_m8B27A3F1C9445BD3C4E2A8FC877360045772D137 (void);
extern void ExtensionMethods_CTIndexOf_m139B5B05D1ADC84292DDCD3D3969B50EAEDB7933 (void);
extern void ExtensionMethods_CTIndexOf_m5AF50B1587AE2A73EE5A98B32D3E0E7B39920F24 (void);
extern void ExtensionMethods_CTToBase64_mF8C11A7EA5FE9EA353685C6AFE4B8FB5ED8C6BC3 (void);
extern void ExtensionMethods_CTFromBase64_m2706806E212E51B45ADB848D33C759F6F8572DCD (void);
extern void ExtensionMethods_CTFromBase64ToByteArray_m05BC21F1365F157CF245D86B7F22E5E67105C150 (void);
extern void ExtensionMethods_CTToHex_m701CF03766BAED3D030ED5A28C43FCD9EF7DC830 (void);
extern void ExtensionMethods_CTHexToString_mEA705524570C1EF7E68AE2189F987A75CEF0592B (void);
extern void ExtensionMethods_CTHexToColor32_m67A37401CA2F20FB255FF5FE167CA7C0053EFC4C (void);
extern void ExtensionMethods_CTHexToColor_mD29375F7F7410F04AD2F2ADAA0F3BBD4EE7FF4CC (void);
extern void ExtensionMethods_CTToByteArray_m5A296D4C151916841471C6884EBA49F7CBE1FACF (void);
extern void ExtensionMethods_CTClearTags_m740802566AAE746B44C5B782899ACBD00188BC73 (void);
extern void ExtensionMethods_CTClearSpaces_m13549AD4DB227E1E47F73974B85FC07F2735AD29 (void);
extern void ExtensionMethods_CTClearLineEndings_m558AE9859A7C848C57965E229434A833DA1AD282 (void);
extern void ExtensionMethods_CTDump_m33BA123C4CB74D30140EA4C1F2FE058F705B3C8F (void);
extern void ExtensionMethods_CTDump_m9A2A7DEEE10BB172934AD05120DBF4ED44E7C4A7 (void);
extern void ExtensionMethods_CTDump_m58539EDF5F2615458FB365708F9966A577D5DF39 (void);
extern void ExtensionMethods_CTDump_mFA7BDAD692E4846225C46AC6E37CB5EB754924E4 (void);
extern void ExtensionMethods_CTToFloatArray_m891661184D3D207B0848B8C9291C661AC7373E29 (void);
extern void ExtensionMethods_CTToByteArray_m212D7235B30775847C6A084AFDA8008935A582DA (void);
extern void ExtensionMethods_CTToTexture_mE682A1A13F47800A784BFA620041CB42D986CBE2 (void);
extern void ExtensionMethods_CTToSprite_mA7AC50E776C7FA1DD109BAC4CC9BD25A41EA93E9 (void);
extern void ExtensionMethods_CTToString_m17444A55B2906DDA25F6B7E687BF9916B9A4FAD0 (void);
extern void ExtensionMethods_CTToBase64_mA97C676582E34CC7E2D60BE8EF8125B86C5662F6 (void);
extern void ExtensionMethods_CTDump_m525436E01A81EF235D49C193F7992059EE29374A (void);
extern void ExtensionMethods_CTDump_mCAE6C04C0E956A29E5E3A1125BE8D764C66319F1 (void);
extern void ExtensionMethods_CTDump_mC1623EF9B6EB40753A6E231B5261C4EB3B29E389 (void);
extern void ExtensionMethods_CTDump_m6D4D18B0D7783E1126F49FFEF81E30A06E88AE66 (void);
extern void ExtensionMethods_CTReadFully_m031567CEC3FEA9A1A14A182A202B84DEC93BB319 (void);
extern void ExtensionMethods_CTToHexRGB_m8650DC53667BE5B556F1A15508F4AFC649347436 (void);
extern void ExtensionMethods_CTToHexRGB_mDBDBCFE45C532B61F83144868801917689EE00AC (void);
extern void ExtensionMethods_CTToHexRGBA_mCA21EA03112D7120832E1D02CC774719903AE9BD (void);
extern void ExtensionMethods_CTToHexRGBA_mB751C2239DA565330255FE544D3B2B0CBAE650AA (void);
extern void ExtensionMethods_CTVector3_m6984F5E80C78644CBFC89A7E150F8FDD659C366B (void);
extern void ExtensionMethods_CTVector3_mCA08E9B0C4D023B26CA9951F2008204248EFE8A1 (void);
extern void ExtensionMethods_CTVector4_mC433A2A8586C7CFFE023CC0180C1A5CF299C51D6 (void);
extern void ExtensionMethods_CTVector4_m94FA7FD1625532E6B9AB8DB7844CE7AFDF2E2290 (void);
extern void ExtensionMethods_CTMultiply_mA2F42D4C9CF55521B62EF00E469CC9054AE17233 (void);
extern void ExtensionMethods_CTMultiply_m47E9FE5C1995BC250E028A6B2B50DCF1C495EA7A (void);
extern void ExtensionMethods_CTFlatten_mE5CE5C37BC01C82A62AB0BED58E26EDFCB085D1E (void);
extern void ExtensionMethods_CTQuaternion_m66BB51F40A9B9484F8ED80FA595117B1A813EBEE (void);
extern void ExtensionMethods_CTColorRGB_mA073E49F24A2AE374233812AF266E9C871486FF8 (void);
extern void ExtensionMethods_CTMultiply_m2DF7247C533B4C0886596C28DA09CFC48B26678D (void);
extern void ExtensionMethods_CTQuaternion_mA7899B04DAB8CBA52492D8FB88EC5B817BCF5535 (void);
extern void ExtensionMethods_CTColorRGBA_m038AC6A633F2046346A162FD36396DF65469A8DC (void);
extern void ExtensionMethods_CTVector3_mF3194515228664D7E197C6FBCFE54E144576D650 (void);
extern void ExtensionMethods_CTVector4_mAB92762CA8F1434ABA2179AA41946237D3FA80DA (void);
extern void ExtensionMethods_CTCorrectLossyScale_mC4072308CAA143928D1408952889A2A66B02A7F3 (void);
extern void ExtensionMethods_CTGetLocalCorners_mA372D1D02C5BE19E6F0CEB6A330D82BA07D79EE2 (void);
extern void ExtensionMethods_CTGetLocalCorners_mBA6966F4073433AA984CD5380D105805ABB6707F (void);
extern void ExtensionMethods_CTGetScreenCorners_mBCFFD2F22172A69C14F25904FE5BB31325EE2FC4 (void);
extern void ExtensionMethods_CTGetScreenCorners_m4D098A3EA0C0BFE53890B2DA6AAD6502FCDB867F (void);
extern void ExtensionMethods_CTGetBounds_mE054FBA3493D294A9585E5F21F4CB1072584DD87 (void);
extern void ExtensionMethods_CTSetLeft_mBF644B51D466D22A2086D8CAE02E6B01C8F885B8 (void);
extern void ExtensionMethods_CTSetRight_m7131AD8CAAFBF45AE1658DEB57606D1B1D983548 (void);
extern void ExtensionMethods_CTSetTop_m8BD3AF804B9AD0FA9963A37CB853F545A8C67ED5 (void);
extern void ExtensionMethods_CTSetBottom_m3E21BA1C64BEA3C0529D8A8D0B727DB0A18A8D64 (void);
extern void ExtensionMethods_CTGetLeft_m1F7A7EFC5144E05C577287019F8E106BF5EBD25A (void);
extern void ExtensionMethods_CTGetRight_m6C5DC7576CBB006B14E0EE74D9A35C7916C746C7 (void);
extern void ExtensionMethods_CTGetTop_m0B2B5C0550AD6D371ED57184322F50DBF8482D7F (void);
extern void ExtensionMethods_CTGetBottom_m6886413E7810A58964FB484598452D158637BECA (void);
extern void ExtensionMethods_CTGetLRTB_m62E826D5D45215DA343E4A5FEAC916C594CE214E (void);
extern void ExtensionMethods_CTSetLRTB_mD249898FB93D80FB310C0B84E895A2515740C3BB (void);
extern void ExtensionMethods_CTFindAll_m3F09DDE73A2F34E305125CD46B01ED77DABA6DAA (void);
extern void ExtensionMethods_CTFind_mCE8BDA6860A8DC20FB76C28AA2A740C05864081F (void);
extern void ExtensionMethods_CTFind_m72E0ADC0584A713817A622D0990A01C6E58308FC (void);
extern void ExtensionMethods_CTGetBounds_m7B45B92EC4CE0CB25E921D76B8B71D7685D67A4F (void);
extern void ExtensionMethods_CTFind_mF22ABB62C9BD2D1FE9A3AE7F73FCD9C3CE649910 (void);
extern void ExtensionMethods_CTToPNG_m7925B0CCA4C0AA0783D6EBED66C563D98D2D3488 (void);
extern void ExtensionMethods_CTToJPG_mA3DCC23368F459B7724D293019D34DB011BBB194 (void);
extern void ExtensionMethods_CTToTGA_m9927C4527CB30AE926CC6E44EBF1D618215017D9 (void);
extern void ExtensionMethods_CTToEXR_m1D2D1FB9F326AAEF32CDEAAAD546FEF9932A6081 (void);
extern void ExtensionMethods_CTToPNG_mA3EB11A3BF1E34381D6EB232794EAB9175957D33 (void);
extern void ExtensionMethods_CTToJPG_mBE203A9FC46E3135E2E9932EA8079871467FF1D4 (void);
extern void ExtensionMethods_CTToTGA_mCCB852CB61F0858B16310280778397C2FB1444EC (void);
extern void ExtensionMethods_CTToEXR_mA8042DDE5562CC8BD8D8ECB26803D8CF0438A8B1 (void);
extern void ExtensionMethods_CTToSprite_m3ABAA4556FB10799C4223DECD91D902DC5E82C97 (void);
extern void ExtensionMethods_CTRotate90_mF562A74FD57789291885A96594DABDD8E8A6C8B3 (void);
extern void ExtensionMethods_CTRotate180_m66FAE50C47AE4AF674FB3937B76AB606730313B8 (void);
extern void ExtensionMethods_CTRotate270_m01644DA8EAC808924B6BE118416F56F07512B22A (void);
extern void ExtensionMethods_CTToTexture2D_mC238A56F92FEBCA8EF40237A11725BBB081DEFDF (void);
extern void ExtensionMethods_CTFlipHorizontal_m5583811F36F6B0048EE4FE6BF3681EDDF702B072 (void);
extern void ExtensionMethods_CTFlipVertical_mC3BA983B9DA317188DEEEE38C8F33B71AA84DB29 (void);
extern void ExtensionMethods_CTHasActiveClip_m30AB2E82E44A8A7A050C9BCA88DC5E9B7D905F41 (void);
extern void ExtensionMethods_CTAbort_m859D3FAB9CDDC74D820B2280A7DD449FDDE394CA (void);
extern void ExtensionMethods_CTIsVisibleFrom_mBB44E44CDCE6DD2743DFF4420E3EEA9A77E48099 (void);
extern void ExtensionMethods_deepSearch_mE32B3041D29AF43E45B6C12D623259F4CC908606 (void);
extern void ExtensionMethods_getAllChildren_m5BB79ACD232B7D23ED6E1CFE4180EEC0ADEB28E2 (void);
extern void ExtensionMethods_bytesToFloat_m872E8618D666788FEAD0D45D25CE2ABAF552A44D (void);
extern void ExtensionMethods__cctor_mFAA2B849830649E4C1784B6264974824B5822230 (void);
extern void U3CU3Ec__cctor_mD288B072253BDDB0CD12F1B503B6680AC3ECAC1F (void);
extern void U3CU3Ec__ctor_mBD6933056C92750D8ADD53CC3E55BCAA9AB4BB9C (void);
extern void U3CU3Ec_U3CCTRemoveCharsU3Eb__4_0_mAE272EF6AC71D863124B9994A31E5C3B34E4B469 (void);
extern void U3CU3Ec__DisplayClass104_0__ctor_m1FFEACA8F7D79C21D7C6A9AC31948D994E5710C5 (void);
extern void U3CU3Ec__DisplayClass104_0_U3CCTFindAllU3Eb__0_m4A2523AF0F3549BB45613553B4BEC978AA6A703D (void);
extern void U3CU3Ec__DisplayClass7_0__ctor_m4763A1BF292F6722B879BB8718B356F9F118FD05 (void);
extern void U3CU3Ec__DisplayClass7_0_U3CCTContainsAnyU3Eb__0_m21909545A4B252BAFDC4573C61CE31B4A4BA59E1 (void);
extern void U3CU3Ec__DisplayClass8_0__ctor_mEF1CF6AA384AD605764CB165048CF8A2A568002F (void);
extern void U3CU3Ec__DisplayClass8_0_U3CCTContainsAllU3Eb__0_m88F4E8D91191A1DB1206CCA655E467EFAAD709AA (void);
extern void U3CU3Ec__DisplayClass93_0__ctor_m7AD688D5105B53A4EC958ABF1D03FFF66E814773 (void);
extern void U3CU3Ec__DisplayClass93_0_U3CCTGetBoundsU3Eb__0_m331B037ED9422C50C8F4E9E7CD27E1AC7D06FC8F (void);
extern void FileBrowser_get_CustomWrapper_m7035DDE90019A4706F23DE08335574D46F75EC77 (void);
extern void FileBrowser_set_CustomWrapper_mC691E573CCA1AA6C60DEEBE290B2E09D4E46B65E (void);
extern void FileBrowser_get_CustomMode_mE1C7500D89175E703B432D99576A7A9BABF68C26 (void);
extern void FileBrowser_set_CustomMode_mE6E73DD4EC288D37F8C939D930D10C4578CCEB53 (void);
extern void FileBrowser_get_LegacyFolderBrowser_mDD51971EF86032B639433CA45F8AAB5592C140EB (void);
extern void FileBrowser_set_LegacyFolderBrowser_mEC49B096B7D267232FD9C91D899B7174BA39970E (void);
extern void FileBrowser_get_AskOverwriteFile_m98337CF5E0E3B99195849CD68813B406208C84C0 (void);
extern void FileBrowser_set_AskOverwriteFile_m6F776FD6B46D24D11760C489ED27375D48C690F7 (void);
extern void FileBrowser_get_AllowSyncCalls_m7DB5CA4CC797ABC46DE42F8259D1C943617DF018 (void);
extern void FileBrowser_set_AllowSyncCalls_m604AF88F4E608C812CE577DA1687AA5378F3136E (void);
extern void FileBrowser_get_AlwaysReadFile_mB4F9E00908EFC29430569294E448AA37DFD13C5B (void);
extern void FileBrowser_set_AlwaysReadFile_m3A2E188CC3F96B6448D3495729BF62A5CD8C2C4C (void);
extern void FileBrowser_get_TitleOpenFile_mEB8D7F2637C945EC6E184A10BFD2F3986ABDC0B5 (void);
extern void FileBrowser_set_TitleOpenFile_m5F25F9851BAD1283A39EEF8E56EFD98664D7D90C (void);
extern void FileBrowser_get_TitleOpenFiles_m28F00A2A9F996411465A656A36C0662870C1330D (void);
extern void FileBrowser_set_TitleOpenFiles_mD7CAD8B9A7D6FA5021CC0A551DB009843B9C7AAC (void);
extern void FileBrowser_get_TitleOpenFolder_m93FD609CB44A33A488E115B8B4626932DAD10383 (void);
extern void FileBrowser_set_TitleOpenFolder_mC7204057BF659B0BD98A9549964B05CAC00D2B82 (void);
extern void FileBrowser_get_TitleOpenFolders_m0D1488048759CD7807371DF425011A2E102C0E29 (void);
extern void FileBrowser_set_TitleOpenFolders_mD1DC90AE25FB742BD9BF76BE6BC7A943C4BE7837 (void);
extern void FileBrowser_get_TitleSaveFile_mC23847399734489907FDC4275C662CE533EB65DB (void);
extern void FileBrowser_set_TitleSaveFile_m044563D1491A32B3D7857E49F3AAAF976A645DA0 (void);
extern void FileBrowser_get_TextAllFiles_m3A67267F08E2BD38C322ABB04AC1085E27958772 (void);
extern void FileBrowser_set_TextAllFiles_m3FB087A13F5A67183DB55D9B650C67A9902E7702 (void);
extern void FileBrowser_get_NameSaveFile_m66339F2E8487CCBE6C7A69E1E365C30A44E78293 (void);
extern void FileBrowser_set_NameSaveFile_m443803DA77553F650562B3B38BE76FC60FD8F5B4 (void);
extern void FileBrowser_get_CurrentOpenSingleFile_m70A07B45BE40B6A3F7E4CF499E3D7B6ED05DECAD (void);
extern void FileBrowser_set_CurrentOpenSingleFile_m9187B46E72609E89B127B9D2E707396E657475D6 (void);
extern void FileBrowser_get_CurrentOpenSingleFileName_m9DC9537E830E134E73DB64D63395B088FE1A42C1 (void);
extern void FileBrowser_get_CurrentOpenFiles_m6A6D6675C024D37E22F4A953A6C4ACD91FAB27BB (void);
extern void FileBrowser_set_CurrentOpenFiles_m63B8FB67D199BB2CA7222A90A4BC13E1066283EA (void);
extern void FileBrowser_get_CurrentOpenSingleFolder_m0AFD4036909A77A1DB93DDD543DA9FB087AF0683 (void);
extern void FileBrowser_set_CurrentOpenSingleFolder_m3769F4B8FDBAF39D4698F253055DB8E78CF959B0 (void);
extern void FileBrowser_get_CurrentOpenSingleFolderName_mCE7A6ED1D551E14AFE5F715F430983B5C2B21AE0 (void);
extern void FileBrowser_get_CurrentOpenFolders_m52C1EEBCF183212BF9A928E26542CB6CF60EC532 (void);
extern void FileBrowser_set_CurrentOpenFolders_m33A389448BE1A56C619FC27E011A46B06829DFE1 (void);
extern void FileBrowser_get_CurrentSaveFile_m516A0128D5FF33E15D563466E74FD3A950D5B0FD (void);
extern void FileBrowser_set_CurrentSaveFile_mB1C49D1666E40903F9792F93CF08DD16FF9C18F1 (void);
extern void FileBrowser_get_CurrentSaveFileName_m805B762EC01004A806D0FD590E2026E2DA89ECA7 (void);
extern void FileBrowser_get_CurrentOpenSingleFileData_mA2ABEE811416E73104BC8DA94FCCAEE88504CA15 (void);
extern void FileBrowser_get_CurrentSaveFileData_mE99022552B4895C9184D772D20264E64B1FE795F (void);
extern void FileBrowser_set_CurrentSaveFileData_m0A447E4CFC3C53D4F503608E1EDDDB89B86CD475 (void);
extern void FileBrowser_get_canOpenFile_mBB47344C7DB7C69FA16987BA5BBBC49C20058608 (void);
extern void FileBrowser_get_canOpenFolder_m799F397C8E6E91D5DE54D56792FD1E43D4401D58 (void);
extern void FileBrowser_get_canSaveFile_mFB05C4681864077E8034A39C16E6E1C5698FBF36 (void);
extern void FileBrowser_get_canOpenMultipleFiles_m41CD39F12AAE7E6A32BF56141D95D99715E53A17 (void);
extern void FileBrowser_get_canOpenMultipleFolders_mE044739E91DE2A8796CFFCC7430CDB785AADCEFA (void);
extern void FileBrowser_get_isPlatformSupported_m66C89550B19BE8A0A2671841961A54E68E8D7638 (void);
extern void FileBrowser_get_isWorkingInEditor_mA1BEBCCA7DCBECF1A7B5D44363C785048094106B (void);
extern void FileBrowser_add_OnOpenFilesStart_m63265AD8FFD7393079F84E140CB530446E4CE59F (void);
extern void FileBrowser_remove_OnOpenFilesStart_mB351023E651C20F18F1F2C34185E84410AE8F968 (void);
extern void FileBrowser_add_OnOpenFilesComplete_m2E7E4C76116ED9F69DACC579B3D379A6C5835CAE (void);
extern void FileBrowser_remove_OnOpenFilesComplete_m972101F15E4B068894F975D100B6B4C35FCB73C3 (void);
extern void FileBrowser_add_OnOpenFoldersStart_m0B2568E9D3EF9CD337B049CFD2CCB5938E5660A1 (void);
extern void FileBrowser_remove_OnOpenFoldersStart_mB4037BD6B6C3C0C96AC9B19C21CA4A331B776BE7 (void);
extern void FileBrowser_add_OnOpenFoldersComplete_m4A684318DCAE880EA4409E1D438A501A83B26CD6 (void);
extern void FileBrowser_remove_OnOpenFoldersComplete_mFB4F9BAC647A262734D9850F059563B8535CA762 (void);
extern void FileBrowser_add_OnSaveFileStart_m97638984DA9160725CD1EE8F3309FE20FF8207CC (void);
extern void FileBrowser_remove_OnSaveFileStart_mADA05E2EAC70EC67952F194C3B2966C43EE43A93 (void);
extern void FileBrowser_add_OnSaveFileComplete_m0B44CEF35C6BFDEEB605BB0270651A0816E2ECB4 (void);
extern void FileBrowser_remove_OnSaveFileComplete_m924380030F5ACA3F861F912F326781AC34EA465C (void);
extern void FileBrowser_Awake_m0C55B70CD400914EDAD990B1603A49AE782319E1 (void);
extern void FileBrowser_Update_m688DDCB7E6D344B0E1D2FC88DAD4535991142B42 (void);
extern void FileBrowser_OpenSingleFile_m271FEDB8960C234251570178DAEF9ED798783A42 (void);
extern void FileBrowser_OpenSingleFile_m9FB1778A2D84F1A93FF513FA637569BDB28ACB2B (void);
extern void FileBrowser_OpenSingleFile_m46907BDD6B9BACE3DB39471EDF90BEB57F725E08 (void);
extern void FileBrowser_OpenFiles_mC968B0AF62611941203A727D00057115237BAF04 (void);
extern void FileBrowser_OpenFiles_m400DA1C0922061E1A9F20C1E5AB21E5DDA2A322F (void);
extern void FileBrowser_OpenFiles_mBA8D694D98A8ABC495284B54875AF32CC14E1ECD (void);
extern void FileBrowser_OpenSingleFolder_m76D444D586D3C21B9BC9A925D20FD91D9595D93B (void);
extern void FileBrowser_OpenSingleFolder_m7005511E45434D22525C7588BBB2028C61FDDDAF (void);
extern void FileBrowser_OpenFolders_m7937351EE33ECBF5DEE3143AB0D24AB50A1A9C4C (void);
extern void FileBrowser_OpenFolders_mD475E46197E6D76D1B0BA7FE0EF24BF85F49EC23 (void);
extern void FileBrowser_SaveFile_mB499171FFE4F91137432DF27D40B474F84DB6B6E (void);
extern void FileBrowser_SaveFile_m17CBA229D0F828482995BB8A42ADA9DE756778D7 (void);
extern void FileBrowser_SaveFile_m602494EAD6B448371C77799A18FB585F6ACFFAB9 (void);
extern void FileBrowser_OpenSingleFileAsync_m989C1E5891B70A88FFEC9EC2CB12EC26F4C8D4D5 (void);
extern void FileBrowser_OpenSingleFileAsync_mD1FCE6B40E1723332D3EE5B2C9C563246F4408A2 (void);
extern void FileBrowser_OpenSingleFileAsync_m1E4007761BE6D1BF82B3E57628249B15DEB369F0 (void);
extern void FileBrowser_OpenFilesAsync_mA7A7325EDBA2BFCA5D6A764D9B828A3699E3FD60 (void);
extern void FileBrowser_OpenFilesAsync_m24A593E3B87436D27E666164E5A248285BAD389C (void);
extern void FileBrowser_OpenFilesAsync_m70057253DFC237F0A050D3AB9D9E1E069FA6409E (void);
extern void FileBrowser_OpenSingleFolderAsync_m3CCD538330933A5D1A29A76DD1BE1BBA1FDC007A (void);
extern void FileBrowser_OpenSingleFolderAsync_m955B095A110D650E2444DF0A927DA09C3F357345 (void);
extern void FileBrowser_OpenFoldersAsync_m948C0EA35EC516BFA7ECF11FAE36F3C9F7674F27 (void);
extern void FileBrowser_OpenFoldersAsync_mCCA6038EBD1383CA5B0E3044935B45706E799FEE (void);
extern void FileBrowser_SaveFileAsync_m0C241479ED41E01A7BF26A24363585E090738637 (void);
extern void FileBrowser_SaveFileAsync_m4DA120079DD137C66D60D82FF33C4875431EF227 (void);
extern void FileBrowser_SaveFileAsync_mA0F8E8B7D86A22CD55BD56F87FAF0778968D82F8 (void);
extern void FileBrowser_GetFiles_m2EAAC001705F9210299F0F7F10A240A85E00BFE7 (void);
extern void FileBrowser_GetFiles_mAD04240594585FA2966BEABD78D7FE910C7BF643 (void);
extern void FileBrowser_GetFolders_m9E0F0A31AB9211C25F44551F69FE274F7267E132 (void);
extern void FileBrowser_GetDrives_m0D69F7761BF1A822116F4ED7BB2FE7441B26F4CB (void);
extern void FileBrowser_CopyFile_m6865B3515E72FA3F7E15DAA9AD8BF91695B31DFD (void);
extern void FileBrowser_CopyFolder_m5AC0DDB049CC3584A23B2D1CF8D4C7C38AC1D2FA (void);
extern void FileBrowser_ShowFile_mF86AC413DC97CB49C0B40A08E4FF2BEFBF7AB021 (void);
extern void FileBrowser_ShowFolder_m5101EFEF9E730045973C4D3148FBA131FEB81EA0 (void);
extern void FileBrowser_OpenFile_m7CE5710DF0D83BBB314DEA7A50ACF94C4307368E (void);
extern void FileBrowser_OpenFilesAsync_m7DF1D8E5292CEF430EAFC0716C1DFBFD055E5C0C (void);
extern void FileBrowser_OpenFilesAsync_m92C232A250D44BC169D5B0D40FB4017C83E2FDFE (void);
extern void FileBrowser_OpenFilesAsync_mFFFCC2999342DA08393BDEF4999CFDD384830720 (void);
extern void FileBrowser_OpenFoldersAsync_m1487314A40A29ABEFD7C852733CA4728756AC51A (void);
extern void FileBrowser_OpenFoldersAsync_mAB2E25370ABF3BA053641DC95CDA33F5ACFCDCA8 (void);
extern void FileBrowser_SaveFileAsync_m7B6735A4AAEBA13694CB9E50D4B3617240790A93 (void);
extern void FileBrowser_SaveFileAsync_m04B914AFAC6BEAFD9CB7A8CD9EB65C63345C15E9 (void);
extern void FileBrowser_SaveFileAsync_mE8A491D2447AAA38DCCEFA90406D40A2A194897E (void);
extern void FileBrowser_getNameFromPath_m104D794AF62DDBCF5334A4BEDEC07F7CB30387E8 (void);
extern void FileBrowser_resetOpenFiles_mD6C0656C98BC7363AF7B3275DEADE19D6B7EF2A7 (void);
extern void FileBrowser_resetOpenFolders_mE71A885DEE258A0B60FB15D4F968FF2925F3F03C (void);
extern void FileBrowser_resetSaveFile_m1F348CCF471E60D4F5065C278BFE070A3DC1ACA2 (void);
extern void FileBrowser_getFilter_m75A5BA2CD5DC2C23F952E4506736C29CE5B2E4B6 (void);
extern void FileBrowser_onOpenFilesStart_m7B04C2CFA940EA3A398CFCE1CCC5879D48E9E8CE (void);
extern void FileBrowser_onOpenFilesComplete_m6566A9F25508766E64FAD7A6169C3306FE6FB1ED (void);
extern void FileBrowser_onOpenFoldersStart_mC62FD28A4A96E377D08DD4ED3721C12A566815CE (void);
extern void FileBrowser_onOpenFoldersComplete_mEACB5AF67318C306430817DEFF2483EDEC7A0A57 (void);
extern void FileBrowser_onSaveFileStart_m9DDE72BB3C417CE43A14F16F8D92F0DA995C9D65 (void);
extern void FileBrowser_onSaveFileComplete_m279D743A9070AE0164692E723775DC8944733B61 (void);
extern void FileBrowser__ctor_m6EC08DD43BE1D52B0743DDECC158907B7B6E0F83 (void);
extern void FileBrowser_U3CSaveFileAsyncU3Eb__152_0_m0ABD507CECEAC8A5BE7BB1EE1A69C16CC0A1393B (void);
extern void OpenFilesStart__ctor_m269F31AF20504DF4E3C4FA47B9F0D4E8F524D20A (void);
extern void OpenFilesStart_Invoke_mABA3E22812CFA24B3F7C03240BAB3F340CB043BF (void);
extern void OpenFilesStart_BeginInvoke_mBA2A9088AA292C734982AAD8A2A39EB9D1010CBC (void);
extern void OpenFilesStart_EndInvoke_m0ADB1CCB8C417A8BE9B97ED174E0816AEA260CB3 (void);
extern void OpenFilesComplete__ctor_mC2E8638E8E3BF114A5A38F2098ADB1C62B6C639F (void);
extern void OpenFilesComplete_Invoke_mAB4C84E1F643DEF0430167220ADEBB16227EAD5E (void);
extern void OpenFilesComplete_BeginInvoke_mECBF4DAEA5A9C54B4639FF90ADA391E10B19BF3B (void);
extern void OpenFilesComplete_EndInvoke_mE95FACF76D8751CC3CD15582B91FD3C424F8CF7B (void);
extern void OpenFoldersStart__ctor_m8577C7E508DFB52D70EFF88D1CB82B6DB6BC51E5 (void);
extern void OpenFoldersStart_Invoke_m5376A5BC20D6BD6B7C44B9D7F7D1177819FD3A44 (void);
extern void OpenFoldersStart_BeginInvoke_m343B642BE5EDCCEFC3655D065F69B04E70A7E78B (void);
extern void OpenFoldersStart_EndInvoke_m4EE4FC459289E3666C5CA748ED9A598F89A3EFAC (void);
extern void OpenFoldersComplete__ctor_mF29E519A99F938321867DCBDFECA24129515379F (void);
extern void OpenFoldersComplete_Invoke_m0884DBFFB284CCEBC268EAEF5D533D5D932105CD (void);
extern void OpenFoldersComplete_BeginInvoke_mDC78427A7CBFCE35096301D63327BE23A813630B (void);
extern void OpenFoldersComplete_EndInvoke_m93C6EEDC7A2206113A7B5AE9BD3934061C3FDAB2 (void);
extern void SaveFileStart__ctor_m2AE477C2EFFE89E32B604455D8064ECBC2CD88C5 (void);
extern void SaveFileStart_Invoke_mD2C20D53D95910238B8289B1BDD2DC2DED3263D8 (void);
extern void SaveFileStart_BeginInvoke_m909B6236153C78D0849E926ACCABEB4CA3592C3A (void);
extern void SaveFileStart_EndInvoke_m179F0EDCA6D83BA8CC2C1AF1D18EC3AF85CFBF8A (void);
extern void SaveFileComplete__ctor_mA95A218B90D213D151264BE5E5DF722B73D8399A (void);
extern void SaveFileComplete_Invoke_m5865A528393961DAABAC585941F2A0F2EDFAED7E (void);
extern void SaveFileComplete_BeginInvoke_m3BD1F7A18905B24306056CE6E17DAEA02A51CFA5 (void);
extern void SaveFileComplete_EndInvoke_m8B7283DEAF3192CC5813966EF3A74B08C9AF4332 (void);
extern void U3CU3Ec__cctor_m1CEC811BED1B657CB27ED15BBC0FBDA8F2606C93 (void);
extern void U3CU3Ec__ctor_m842821DE32285C90E2CABC5DE628A644EF5C964D (void);
extern void U3CU3Ec_U3CGetFilesU3Eb__153_0_mE9DECAD29897720DB98A4D869A52362AF3283E6F (void);
extern void ExtensionFilter__ctor_m84B8C8B2F6885054D6DDE7E2431B6B12231B792D (void);
extern void ExtensionFilter_ToString_mE7AF0C4F85E9BBF382F2EF65787CBAF3086D6041 (void);
extern void OnOpenFilesCompleted__ctor_mB136C19C9D4F4ACCC3E7294480A7A04AE5B9256E (void);
extern void OnOpenFoldersCompleted__ctor_m38EB333C4BEE6CB657397016B09866F256DAEB4C (void);
extern void OnSaveFileCompleted__ctor_mDC0566EAA6683D91EFF8F97D5A3709209422920D (void);
extern void WrapperHolder_get_PlatformWrapper_m9EFACA1B78C9DD5F60BA16571AC5884B6A1F5380 (void);
extern void WrapperHolder_set_PlatformWrapper_m85148B94C0D83B1CDDDBDE350AE2AA754B8D4AC2 (void);
extern void WrapperHolder__ctor_m4C71D1B7E5DD2566FC044E7A0CD3018DF99CAE75 (void);
extern void WrapperExample_get_canOpenFile_mB1991D1713D677650766E3FF0A28638E3E0007EC (void);
extern void WrapperExample_get_canOpenFolder_mB7F208B4313D1FA173B83BCED6B94656F33D8B8C (void);
extern void WrapperExample_get_canSaveFile_m4228750A88B0F59E8BFA8D3143D3BD39E3A8BB32 (void);
extern void WrapperExample_get_canOpenMultipleFiles_m28AC1E8BE63DB8F0AF72D1DA3E54C0139E7BA67E (void);
extern void WrapperExample_get_canOpenMultipleFolders_mBBD9153718163EC371FC7492954D39AEF0BBBD7D (void);
extern void WrapperExample_get_isPlatformSupported_m3E855489A03AF218D5498E471BE6C65237ACED43 (void);
extern void WrapperExample_get_isWorkingInEditor_mFDBA5C9B49ECA94CF7492715B8711011C78D9546 (void);
extern void WrapperExample_get_CurrentOpenSingleFile_m6479D99F5EAB43FA952D728902F5B3EAB1B1D5B3 (void);
extern void WrapperExample_set_CurrentOpenSingleFile_mB52CDC9FBD52F878EA40E959D24AE98D33631D0F (void);
extern void WrapperExample_get_CurrentOpenFiles_m0AA5904A4E76F8E32D0C21C4CB15598273609874 (void);
extern void WrapperExample_set_CurrentOpenFiles_m43AE8BB189AAC4DEAC09762327CD2606C93CA37F (void);
extern void WrapperExample_get_CurrentOpenSingleFolder_mFB082A8E66A0566060DD30F73637710CC7FF612D (void);
extern void WrapperExample_set_CurrentOpenSingleFolder_m7267A23F1F0D3CD13B7AC9F58699B97240BE68FA (void);
extern void WrapperExample_get_CurrentOpenFolders_m99E205D4630EBE7C5BAD813A7A1B9C04A91C7475 (void);
extern void WrapperExample_set_CurrentOpenFolders_m6B0B31D3E48E4159A30439C50AE6543EEF735774 (void);
extern void WrapperExample_get_CurrentSaveFile_mAFFEB524833D0B5B36BBF73DD424E22032EEA054 (void);
extern void WrapperExample_set_CurrentSaveFile_mFA09F2F8709985A80872BBA6816407D5FE50DA74 (void);
extern void WrapperExample_OpenFiles_m4BF3A0E2448959A9BE5F9A750048651220BD90A6 (void);
extern void WrapperExample_OpenFolders_m70C2251D80B7771FEF2CE1D84616481EAFE176F7 (void);
extern void WrapperExample_SaveFile_m332BD2E3B72CF22A658EED3C89BA4EB0B6BBB603 (void);
extern void WrapperExample_OpenFilesAsync_m0040B47885BC71A80D4B749AC4149E22C21EB69F (void);
extern void WrapperExample_OpenFoldersAsync_m692EFAD86E1D4B8D0A6A2D53CB0A618BE2033E2E (void);
extern void WrapperExample_SaveFileAsync_mC9814B3A0AE7C133D6B5B58F4D7180B3CEFE2E0C (void);
extern void WrapperExample__ctor_mF6AF492FC2A369C1462D0012685CF189B5F41470 (void);
extern void BaseCustomFileBrowser_get_CurrentOpenSingleFileData_m1C8C323436CB5B9D7A94E9E9184CB02E1BB926F8 (void);
extern void BaseCustomFileBrowser_get_CurrentSaveFileData_m305402F6C4258EFA356E4C556DDA677108EC8997 (void);
extern void BaseCustomFileBrowser_set_CurrentSaveFileData_mC804D946B02394311196D317635EDC01E425DAEA (void);
extern void BaseCustomFileBrowser_OpenSingleFile_m01BE09078C45E273DA0B473D743AE008A6060926 (void);
extern void BaseCustomFileBrowser_OpenSingleFolder_m9FE0EFFBF7FF1586BE700907097107869C3533DB (void);
extern void BaseCustomFileBrowser__ctor_mFB400179DB16BD77EB6AD2F2663C2D6F899F5100 (void);
extern void BaseFileBrowser_get_CurrentOpenSingleFile_mA8E56B81A51C77AF2C0AAEA6FC724710DADFDC11 (void);
extern void BaseFileBrowser_set_CurrentOpenSingleFile_mDC5D46D6EB6212F549B65A0158F3CAA9F59A03ED (void);
extern void BaseFileBrowser_get_CurrentOpenFiles_mC975C202477FD1C1FF9EA54C9CDF5C3DCC246441 (void);
extern void BaseFileBrowser_set_CurrentOpenFiles_m42D3EE8EE3E6EA1FF74C97A27481ABCE7DFD0727 (void);
extern void BaseFileBrowser_get_CurrentOpenSingleFolder_m71F2A523F0E93A7C579C800B9AD3194B1BB98CF5 (void);
extern void BaseFileBrowser_set_CurrentOpenSingleFolder_m2D88DB910FC052CBDCCB86547F10D05E2AE300D7 (void);
extern void BaseFileBrowser_get_CurrentOpenFolders_m8DF535A9E969C8DF081A21C70A3C6B648C180C61 (void);
extern void BaseFileBrowser_set_CurrentOpenFolders_m186C6D8C8F469F740A298A3AC8F000534277DB56 (void);
extern void BaseFileBrowser_get_CurrentSaveFile_m86E0E41F1A5FD35A7369B24FBA1518A9A3B33D4C (void);
extern void BaseFileBrowser_set_CurrentSaveFile_m49FF3E6D92E7870804A4FFA01F7BAD0880A3707E (void);
extern void BaseFileBrowser_get_CurrentOpenSingleFileData_m9CF4D65ED107E170129BBFDF154FCA2BDFF71F83 (void);
extern void BaseFileBrowser_get_CurrentSaveFileData_m370910541D1BDCE021E212AA10D91A18984DF7FB (void);
extern void BaseFileBrowser_set_CurrentSaveFileData_m11499902206AEA80D70F73B5B58B69C39444FB01 (void);
extern void BaseFileBrowser_OpenSingleFile_m6C1C44799548A0E19F8C6C6E35040C9C6F531B20 (void);
extern void BaseFileBrowser_OpenSingleFolder_m6B40416A178C5B754A37A3C698AB3BE3C024F368 (void);
extern void BaseFileBrowser__ctor_m880702D7BC6E1CEC4D705A62912AE88713B822DF (void);
extern void FileBrowserGeneric_get_canOpenFile_m6323A459FB7A842782EFBF3CB543A9EB26D38611 (void);
extern void FileBrowserGeneric_get_canOpenFolder_m4A0081F69BCE27AEB05AF1F5D0E32FB5A998B5D8 (void);
extern void FileBrowserGeneric_get_canSaveFile_mF6F54B100775424920A596450538E98B52D07E26 (void);
extern void FileBrowserGeneric_get_canOpenMultipleFiles_m2C7A9434B176E820C325B09EA78C8097CED88CC1 (void);
extern void FileBrowserGeneric_get_canOpenMultipleFolders_m1D399D6A0ED8774F88EF6E3F97C6A55CBBD4AA36 (void);
extern void FileBrowserGeneric_get_isPlatformSupported_m64EF152FFEF3CC47C56A2BCDDF7781DE828B7DD5 (void);
extern void FileBrowserGeneric_get_isWorkingInEditor_m19641AA6F898FDF8BD59862936557554031E3CB6 (void);
extern void FileBrowserGeneric_OpenFiles_m70B610A772CFB09C50F09CF0B6DB28D49A98A06A (void);
extern void FileBrowserGeneric_OpenFolders_m7ADA786F7763E8A76DC184CC2E44224F9B352E91 (void);
extern void FileBrowserGeneric_SaveFile_m9293EBE5A4B02E6299B36976D54ED7960154D23B (void);
extern void FileBrowserGeneric_OpenFilesAsync_m9BA4D14AB796AEC63A4F93C2C4A49A533F4CC2BC (void);
extern void FileBrowserGeneric_OpenFoldersAsync_mFF3923CF026BC2FBC928DB207F01380AA073ED60 (void);
extern void FileBrowserGeneric_SaveFileAsync_mC1402DBBB8CCC4718B82A7D33C144E064A4D3697 (void);
extern void FileBrowserGeneric__ctor_mF67FC0514415DE0876B29C0500310EC29DE4871E (void);
extern void Config__cctor_m1188DFD8E36AF35CE0B45054968F33D5906C6071 (void);
extern void Constants__ctor_m47820FA773DAA6EB73431A0C1B625EC0277ABD0E (void);
extern void Constants__cctor_mC48C5010D3505FAC4DD677BA9B9D1094B43321FD (void);
extern void Helper__ctor_mAA1279D4D127915CCA30F9C912621DF6C99E8D88 (void);
extern void SetupProject__cctor_m5A4651D19375A4F3A57ACE1142B6D94EF64869C7 (void);
extern void SetupProject_setup_mE1740E5D5C820B4E5328D81F3A422FA555F84BF8 (void);
extern void SetupProject__ctor_m35200C2F6B5E918EE47BF20D7F7BDB00E3534F60 (void);
extern void PlatformWrapper_Start_m40F2259EC3BD89FBE90FA1710078A3B77C832590 (void);
extern void PlatformWrapper__ctor_m3C5C48B764AB23CA097737EAD41520E83ED06AC9 (void);
extern void PlatformWrapperTuple__ctor_m09E3D0680BF0A725DE755C78BCB22AEA9E3218AA (void);
extern void Social_Facebook_mB6EFD5AE09A27AD61962FCE46CFC481148045247 (void);
extern void Social_Twitter_m332CDCBC0BBFDA1059EF2FE63DFA515B922C4856 (void);
extern void Social_LinkedIn_m805308826E46C9A877C256EA9305601D95FB9D17 (void);
extern void Social_Youtube_m0EBDD98C8A0C69722120F3E0C8A389B7A5E24C7E (void);
extern void Social_Discord_m0615BFDBC446EE4648E907767DCA336C6462CB34 (void);
extern void Social__ctor_mB5D378C5D8DF36448BE1AE6809B8DC37D3CB4D75 (void);
extern void StaticManager_Quit_m6D555E8D5F5802AC267908A337E9307C19DFB75B (void);
extern void StaticManager_OpenCrosstales_m5256A5B66EB1B378DD503AC8599362D80B197B9A (void);
extern void StaticManager_OpenAssetstore_mCD778E258F518F0C9E49A032C63320A1CAB53E4F (void);
extern void StaticManager__ctor_m078E43C016A75F6E66205A8352D75714A22F82C4 (void);
extern void UIDrag_Start_m0435CD76BCC26C145071D9EBCB105B1CC8C0D1A7 (void);
extern void UIDrag_BeginDrag_m4275C044D4C9AF36EE7296F582283B41D4B7822A (void);
extern void UIDrag_OnDrag_m68EBC4D74CFF6B1CF579607B2F6921AC1A56EA5A (void);
extern void UIDrag__ctor_mE1075AA38FDD5880BB6E70DF7C852FC310E51DC2 (void);
extern void UIFocus_Start_mC4E3E1938ABDA026B785A0BFFEE9960A097F8556 (void);
extern void UIFocus_Awake_m0378F5AE39CBA5110D32255F2E6F71F39BD69498 (void);
extern void UIFocus_OnPanelEnter_m9B508D2E48268D8B4FC9849D4D7D11079B7E38D9 (void);
extern void UIFocus__ctor_m4F5317F5D8615C33932E1D042ED4F5C6FBD28A78 (void);
extern void UIHint_Start_m360C418159B33F27265E70A87988E5D318B3C17A (void);
extern void UIHint_FadeUp_mAE411B3A12D1E198096DB003F1285CF124BE645D (void);
extern void UIHint_FadeDown_mF5F4EEE1F8E68CF5101A8888B5E52F6422DFC462 (void);
extern void UIHint_lerpAlphaDown_m10B13A338538D98C917D74A77A46CDFD7BD9CC49 (void);
extern void UIHint_lerpAlphaUp_mCDA3FEBE2CE63AB96D0CF9ED4B9CD6E88619711D (void);
extern void UIHint__ctor_m68908B27685F720F7EEA813F8CDAC0306E55EA43 (void);
extern void U3ClerpAlphaDownU3Ed__8__ctor_m87A8888EC5B30789B05A9C4F3AEE2BE8489FD0CE (void);
extern void U3ClerpAlphaDownU3Ed__8_System_IDisposable_Dispose_m6EE7D774484AAB03CA8EF6926B1D58007806C9E3 (void);
extern void U3ClerpAlphaDownU3Ed__8_MoveNext_m0336AC69FC1B3D8AC810B7025A8490266AF84DE4 (void);
extern void U3ClerpAlphaDownU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0B6D0A9E26CED0E0A0E3D009F94A3977A77B7980 (void);
extern void U3ClerpAlphaDownU3Ed__8_System_Collections_IEnumerator_Reset_m176138FFEBA8D7DF62B716850069385744F60A87 (void);
extern void U3ClerpAlphaDownU3Ed__8_System_Collections_IEnumerator_get_Current_mAE1A0545FEEA11939E271E4015E2B1278BD91016 (void);
extern void U3ClerpAlphaUpU3Ed__9__ctor_m9C9915519829C6B031EE4F711F0397C4BB06F3F8 (void);
extern void U3ClerpAlphaUpU3Ed__9_System_IDisposable_Dispose_m7DCC07528A9EA29A9D14FCDBF1F1939ACF6BF99C (void);
extern void U3ClerpAlphaUpU3Ed__9_MoveNext_m82AFD51CAF5A7770B8A74C4821E41F18C3E85E41 (void);
extern void U3ClerpAlphaUpU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m97D68BBA7EF81BC61AB312A6BDBE3283485F5EE4 (void);
extern void U3ClerpAlphaUpU3Ed__9_System_Collections_IEnumerator_Reset_m0C8466BE799D085F9D4368777F08B3B9D4D22E25 (void);
extern void U3ClerpAlphaUpU3Ed__9_System_Collections_IEnumerator_get_Current_m9D3FFED2B213FC0B99870A2F0D41EC33A0C46CCC (void);
extern void UIResize_Awake_mDFC491394A4DCF88E201D60AD1CB8ABD6876665E (void);
extern void UIResize_OnPointerDown_m6F5B0E6306EAA1D48946D8BB9AA4D60871382A8A (void);
extern void UIResize_OnDrag_mEE0AB78FC4A81231404FE0287074FC8C695AA784 (void);
extern void UIResize__ctor_mCEE3EB310C39FD74D95DF0CE839B665B2962D837 (void);
extern void UIWindowManager_Start_m8747026FD93C4B764A5D98D496CDD3139BA2A81D (void);
extern void UIWindowManager_ChangeState_m4A737975179903881F1E05DFF834B7C3E46EBE6C (void);
extern void UIWindowManager__ctor_m1FBDB5269AC46C6C58E2FD785487009A61C49355 (void);
extern void WindowManager_Start_mF3F824593A86DD631B1BE1049E4C056BBBC38998 (void);
extern void WindowManager_Update_m0C861D409D7A5714DC121183FC67220804723916 (void);
extern void WindowManager_SwitchPanel_mFD8271108536BEC37B96B3A50A13CF63185E398E (void);
extern void WindowManager_OpenPanel_mDC8542DF0B09CA5FD804585CD261A92943739636 (void);
extern void WindowManager_ClosePanel_m83F532BCDDAA57B1877945DEF16B8E8E9CCC5427 (void);
extern void WindowManager__ctor_mD3E74D01ECA28E8DC1F0E470CAD18A24218C9B6A (void);
extern void FPSDisplay_Update_m23B314DD7E9FEB385B9508814FF67D23BE713684 (void);
extern void FPSDisplay__ctor_mC025AB1A519D9A3A9EA7DEE149146A78E600F8F3 (void);
extern void ScrollRectHandler_Start_mC7D30A08C004BD6AB1CF6AAB1D766A1EB0FDCCB5 (void);
extern void ScrollRectHandler__ctor_mD140DF64EB23E01B14B98A24125761568346767A (void);
extern void AudioFilterController_Start_m122D61E6B167AC10B0F5F6D863EC0E4907947AAD (void);
extern void AudioFilterController_Update_mBDAE5A26E6EEC74DEF27AA58073A343D8A6288D7 (void);
extern void AudioFilterController_FindAllAudioFilters_m0776059AF142EE9AE96FD2CB45F82B45E6EB7367 (void);
extern void AudioFilterController_ResetAudioFilters_m320F14E6D4289EA716976093E2E59F4623862251 (void);
extern void AudioFilterController_ReverbFilterDropdownChanged_mA0BD30EB1678279853C84CD96903C779782F5B2D (void);
extern void AudioFilterController_ChorusFilterEnabled_m30074CB15E40676157DBFB1F49EC5EAE9332C0E2 (void);
extern void AudioFilterController_EchoFilterEnabled_m66A5127D84C7AF8B622C412A4C81131F26A8ABE8 (void);
extern void AudioFilterController_DistortionFilterEnabled_m7F650E043151A07FDF7DBE2F798B3E151F75978F (void);
extern void AudioFilterController_DistortionFilterChanged_m81D7749FCBC0FADF38A714DD11922A87DB164E43 (void);
extern void AudioFilterController_LowPassFilterEnabled_mEAB9B0A54B33580419F195AC4EE1FB4B445E863C (void);
extern void AudioFilterController_LowPassFilterChanged_m3AD53E334723BC078B0F881C24F4F3E78FD1FC88 (void);
extern void AudioFilterController_HighPassFilterEnabled_m5E79D534C0C28431DAD304BBE43C31FC70FAB26B (void);
extern void AudioFilterController_HighPassFilterChanged_m13C72BEAF411FF249E9CA54ED11496BDA07BB704 (void);
extern void AudioFilterController__ctor_mD57C57F4F8E5E1EC46358703E0BF3C4CEB1266DA (void);
extern void AudioSourceController_Update_mA643A1AC634A0D187FAE689D48A626848DDC0C42 (void);
extern void AudioSourceController_FindAllAudioSources_mCF727A4EF6516DCE23F961CD197BDCD04DA14218 (void);
extern void AudioSourceController_ResetAllAudioSources_mB6A657F9C04803E7DDC32F25363EBAB4F0EE85FD (void);
extern void AudioSourceController_MuteEnabled_m1E305445A2796F6D3B82C54E7B3F96FBC3FA1ECE (void);
extern void AudioSourceController_LoopEnabled_mB9DB113B12B824459D3CF3680B89177950ECA635 (void);
extern void AudioSourceController_VolumeChanged_mCA230C43CCAD908A8C35C0C20B8A0CC6290ED991 (void);
extern void AudioSourceController_PitchChanged_mA43B85D60DABD051C0D837BF48F1D8ED0E1B1C9C (void);
extern void AudioSourceController_StereoPanChanged_mF76BCD92DB4B2C6DF351A153FE966B803A5F8B0F (void);
extern void AudioSourceController__ctor_m79B97F5F3DCEA56DB08BA824EB4B97F9870FB8C6 (void);
extern void PlatformController_Awake_m4BFE7AED53051130D7360E61BDB90D1CD33A43EA (void);
extern void PlatformController_Start_m09B2AEE2DECA75C4DBD2DCDA8B5F690B0C6322F1 (void);
extern void PlatformController_selectPlatform_m790A5987CCB6543D9D84D11138C3544870DF11BE (void);
extern void PlatformController_activateGameObjects_m68C3F5FE26328C42FADAA10687C7847A3A34CA4D (void);
extern void PlatformController_activateScripts_mC7A7D44FA2D784406985C60CE961433A308B3C69 (void);
extern void PlatformController__ctor_m2CDB1DF23E7B037E711CFEBCF468C484808BEB09 (void);
extern void U3CU3Ec__cctor_m6B2ED5EA66D9D351337142911257EEC99F77ACBD (void);
extern void U3CU3Ec__ctor_m1AB5DFE32B5606486284950C79ED6690F6C5A636 (void);
extern void U3CU3Ec_U3CactivateGameObjectsU3Eb__8_0_mFC5B40FDFBBC993CF6113F7451F6E9041FE34ACE (void);
extern void U3CU3Ec_U3CactivateScriptsU3Eb__9_0_m96784A141293B155B63D0B712DF5D157E4B45497 (void);
extern void RandomColor_Start_mBC02A5377DF1B658FDF67BB79634BCEB80EF668C (void);
extern void RandomColor_Update_m160C2752BBEB9027B5492AE7AD73FBD54C6F839E (void);
extern void RandomColor__ctor_mB848008AC36078D1B409DE414AFC7366AFF2B45D (void);
extern void RandomColor__cctor_m2E546DB611A44CBC71639A16D93E7DFD49EDF971 (void);
extern void RandomRotator_Start_m11B1016E728F6B495A7E30CB78706FB7B2AB80AD (void);
extern void RandomRotator_Update_mC6FAEB79BF508A634B13B07D311588513ECB3C21 (void);
extern void RandomRotator__ctor_m4E3EB11E5D0D083DECA8155B4D29C8327E94E6D7 (void);
extern void RandomScaler_Start_m6474CEC8B705AC8972747CA7586F9EDD07E2664E (void);
extern void RandomScaler_Update_mD4F58422932A0560E9C709213475C8020BA25A5F (void);
extern void RandomScaler__ctor_m35498353C9232FD91815D62E44FBA106843751ED (void);
extern void BaseConstants_get_REGEX_LINEENDINGS_m4895158A97275415A9CDA1F4FDB28CFE7BE1C1CC (void);
extern void BaseConstants_get_REGEX_EMAIL_m11E13FB104D9F9F23B3F46D6AC0C4B9A69E576F2 (void);
extern void BaseConstants_get_REGEX_CREDITCARD_m168A0AE6C2E16EBE5973596FCA26F467B3D552A4 (void);
extern void BaseConstants_get_REGEX_URL_WEB_mA3CDE79C3F9AD941C1E48DC9FA10B82D124F9133 (void);
extern void BaseConstants_get_REGEX_IP_ADDRESS_m10F4DC2E0133434EBA20EB08ACF864F607E5AC2B (void);
extern void BaseConstants_get_REGEX_INVALID_CHARS_mFFFD6DC6EBCF4609CBFCC9982794794AC5A188A6 (void);
extern void BaseConstants_get_REGEX_ALPHANUMERIC_m9A147D06657BDCD72A1302D5E8FE74920FFBE8EA (void);
extern void BaseConstants_get_REGEX_CLEAN_SPACES_mB9EBDBAE7C5E0AC070DC65B04118176B1296F844 (void);
extern void BaseConstants_get_REGEX_CLEAN_TAGS_mD80739E4DC413929F0187DE4D757F72082D1DD87 (void);
extern void BaseConstants_get_REGEX_DRIVE_LETTERS_m6ACB12B39FC3809746479C41B97A83486BCFAA79 (void);
extern void BaseConstants_get_REGEX_FILE_m2986C4CFD5969827CB430D98C0AF5D4782BEEF0C (void);
extern void BaseConstants_get_PREFIX_FILE_m779562D3343FE486BDE16A8714EE83845E824FED (void);
extern void BaseConstants_get_APPLICATION_PATH_m21E5602DC8ADDBBF22422547AAB4952711A6073A (void);
extern void BaseConstants__ctor_m6FB36758DECB7A60CCCB673CBADF04B3E589CB9B (void);
extern void BaseConstants__cctor_m3BC60EBE74C95A1515819FB69A801D73BA5D5793 (void);
extern void BaseHelper_get_BaseCulture_mE9548227E0243318E13BAAE4559EF4BE60CFE063 (void);
extern void BaseHelper_get_isEditorMode_m93820E6B36D0DC625BB6AF66EB6573BF9545429D (void);
extern void BaseHelper_get_isIL2CPP_m5E8DD65CC8EA3B79EFD45063BD7800A21297C4A6 (void);
extern void BaseHelper_get_CurrentPlatform_m011B7604F1DCAA00DE56FD479329743B95B30AEB (void);
extern void BaseHelper_get_AndroidAPILevel_m50D618CB7E5843EC03C529E949FA3972B788B855 (void);
extern void BaseHelper_get_isWindowsPlatform_mF6650920E73E7618C663F21719213BCA1B5A4547 (void);
extern void BaseHelper_get_isMacOSPlatform_m7AD99C320FBA37A303B07F60441FC792B455CC18 (void);
extern void BaseHelper_get_isLinuxPlatform_m24C0E418C4D62D16A1B8036A939E55CB65DDE1B7 (void);
extern void BaseHelper_get_isStandalonePlatform_mA221CF4663B33F6195DD647FB601B7E08A5E80FF (void);
extern void BaseHelper_get_isAndroidPlatform_m60BCD91055339C43D865EF752A7427EE5D9C8497 (void);
extern void BaseHelper_get_isIOSPlatform_m33E9A93F4FF88F818CE0820AA8881BECC5DDC178 (void);
extern void BaseHelper_get_isTvOSPlatform_m9B4C1DA5432ABA5C24372B11FFFF79EC448EA832 (void);
extern void BaseHelper_get_isWSAPlatform_mDA854DBB616C3AD5D86ADC87B870DF2ECBA2898E (void);
extern void BaseHelper_get_isXboxOnePlatform_m51652CA1A243014DF9994A96DEA7D5C133C3391E (void);
extern void BaseHelper_get_isPS4Platform_m8E1EF945740B3F677125C17B7EFDA9AEDE7822CA (void);
extern void BaseHelper_get_isWebGLPlatform_m59C13ECC0AD3202D20F178F208D9EB9E7BCCC5D5 (void);
extern void BaseHelper_get_isWebPlatform_mF107B2B636CE641CE7DC75A8B3CE7C2797E59276 (void);
extern void BaseHelper_get_isWindowsBasedPlatform_m6ECB1D7F700D20B24E338A3A846579A203CCD25F (void);
extern void BaseHelper_get_isWSABasedPlatform_mBF4CBFC9EDE348991DB9D513533E32ACF2843975 (void);
extern void BaseHelper_get_isAppleBasedPlatform_m5BA8B256C8C85EE5B7150D22D39D1619385C0ED6 (void);
extern void BaseHelper_get_isIOSBasedPlatform_mD19DA9D2EA0A9C2A22DF130F598CA5B4EB457910 (void);
extern void BaseHelper_get_isMobilePlatform_m6B4FD01CAB09C208BED4F4CEC7B00D5839A3F1AA (void);
extern void BaseHelper_get_isEditor_m57508F5FC6D0EB0E2BCED1222922C5B68D320366 (void);
extern void BaseHelper_get_isWindowsEditor_m905DF54FC1AF9310CCF8181FC9A81BF55BAC9908 (void);
extern void BaseHelper_get_isMacOSEditor_mB498128F3EC26AEDF84A10A10BD11DBB5B80F1A6 (void);
extern void BaseHelper_get_isLinuxEditor_m7C9E24B618CE8DA56D8D3BE44BDBB994CA992EA5 (void);
extern void BaseHelper_initialize_m24180A216AC99C4E53360CEE025B5937F45846F2 (void);
extern void BaseHelper_CreateString_m709214EF42D31EAC474860A1FCA6686E4B3789B3 (void);
extern void BaseHelper_SplitStringToLines_m9B1E45952E0C80637566E23583FFFE4CC17FEBE9 (void);
extern void BaseHelper_FormatBytesToHRF_m24850B10A003B532B7969E3B489A4F112046E0DF (void);
extern void BaseHelper_FormatSecondsToHRF_m12B5F2AFE7A9547380B8FFF382BE717EBCD2FE4D (void);
extern void BaseHelper_HSVToRGB_m4B3F56F11F4FD20FA22674978B18B7E2E246ABC6 (void);
extern void BaseHelper_GenerateLoremIpsum_mE7177C87CBDA42B4F1408E602B86D9A3AB584B11 (void);
extern void BaseHelper_LanguageToISO639_mDEEA536D80A59AE7C0F0B6182B7A24F63CBEBF08 (void);
extern void BaseHelper_ISO639ToLanguage_m9988DAA63DC827726DE04ECF072DD5D941CEB903 (void);
extern void BaseHelper_InvokeMethod_m84455B77143CAF274883570A1ACF308239C73DB6 (void);
extern void BaseHelper_GetArgument_mEDEC1E1165EE0FD38AF8A8CAE91C4BB37E17B77D (void);
extern void BaseHelper_GetArguments_m3FA2CC168907A333A7F8D8727945A8DF18119E88 (void);
extern void BaseHelper_ParseJSON_mA68E7226C88F4BD7EF318A4A474D9E26456A1204 (void);
extern void BaseHelper_addLeadingZero_m62D3D7E8D0FF078D396AB289F7C7C42C8BBC88FA (void);
extern void BaseHelper__ctor_m5AD5466ACE6506E28F582A8C2A0677BE2C0F2C8A (void);
extern void BaseHelper__cctor_m433B4EBC3FD33028F3FEE16FA7FC4AF3DDA92FC2 (void);
extern void U3CU3Ec__cctor_m3741615C27DE290FF33E48F48B843444F0913B0B (void);
extern void U3CU3Ec__ctor_mA2C9478E04DAD48F8CEBB5AABB02F90956488D81 (void);
extern void U3CU3Ec_U3CInvokeMethodU3Eb__65_0_m55245EEE357D66278B0AA7476DA66E67BAA45F44 (void);
extern void CTPlayerPrefs_HasKey_m9AD6D23286FF2023A63253AD5334D4A24C34A9E9 (void);
extern void CTPlayerPrefs_DeleteAll_mD0E3A067E798064A7D1281B9857B268B7FE4E3AD (void);
extern void CTPlayerPrefs_DeleteKey_mACBA36703D8A439D84F8E6B3C57E292BFA4F885B (void);
extern void CTPlayerPrefs_Save_m47E2F80CA8FFDF86E1EA511D414C37649A0F2C5F (void);
extern void CTPlayerPrefs_GetString_m12727ED53ECCAD1B73F88AC9A7674C885BD30D55 (void);
extern void CTPlayerPrefs_GetFloat_mEAD28CB2B18BCE46A7EF58EC3F9CE376738B3044 (void);
extern void CTPlayerPrefs_GetInt_mD647BFA32DEBC1C4ACD48780E39DD07F09B627ED (void);
extern void CTPlayerPrefs_GetBool_m4CBF0C9AC39E864E3F6F7D640914F9C662933B4A (void);
extern void CTPlayerPrefs_GetDate_m5CE10A4BC2766D240151FC17D4DBDF748A52B44A (void);
extern void CTPlayerPrefs_GetVector2_m23926CCD9C81ED0C0EC27D3CCE7C9C3D13F6C19B (void);
extern void CTPlayerPrefs_GetVector3_mA8B8E177E44FBD6113248834A76DC6A11ACF7EE7 (void);
extern void CTPlayerPrefs_GetVector4_m00D1C89760D6AC2CBFF6C47414C526AA2A7F195C (void);
extern void CTPlayerPrefs_GetQuaternion_m549402F340532796DC886A6C7027524697098584 (void);
extern void CTPlayerPrefs_GetColor_m97C3A4BA84133EC3E625406B82E77F730290727C (void);
extern void CTPlayerPrefs_GetLanguage_mFDF588F7342529CED0933F3EDDF70BD958928C23 (void);
extern void CTPlayerPrefs_SetString_mF005A07CA71D1691BBFFCD98D5C8D4D7689B7B6F (void);
extern void CTPlayerPrefs_SetFloat_m4C3B4EF17E8209DCC5109FB32DDF217DA7348A1D (void);
extern void CTPlayerPrefs_SetInt_m25BEC0CD693AD8BD5790841E2EADB83D9DE7C2CF (void);
extern void CTPlayerPrefs_SetBool_m601FDB02E533E25A43763DE0092CC922979EE4FA (void);
extern void CTPlayerPrefs_SetDate_m3310C63319E8CDD03056D039B904FA6F04C341B9 (void);
extern void CTPlayerPrefs_SetVector2_m54A82443A547AD5CDFA9EFA92C14E6E8ED360BD2 (void);
extern void CTPlayerPrefs_SetVector3_m09ACBFDE30CA4191F773EADC113390F67D1081DF (void);
extern void CTPlayerPrefs_SetVector4_m86B9D955BBA3EED4F4F325E4B7CBF5C37B90D290 (void);
extern void CTPlayerPrefs_SetQuaternion_m8D8ADE8BD164EFDCF05EF6B2B1A22D00B4161F1C (void);
extern void CTPlayerPrefs_SetColor_m1BDAFB6A6B0DBBB206A5B3E8D2111159AADD9983 (void);
extern void CTPlayerPrefs_SetLanguage_m1AB3EEAE2235BAE4C0635553664A2562454BB52C (void);
extern void CTPlayerPrefs__ctor_mA0DC3BF75693639A0D5BCA1FBE84E6C75944A6B3 (void);
extern void CTWebClient_get_Timeout_m29AD981FE9C6DBFB095C22B65424ECDF95E0AD13 (void);
extern void CTWebClient_set_Timeout_m67E4FCFA6D8A1DFFF08AD167A78FF51FA1305700 (void);
extern void CTWebClient_get_ConnectionLimit_mCCE99B9E354DF17F195E268CBFB306496CDFD632 (void);
extern void CTWebClient_set_ConnectionLimit_mEC7194BE6A1FF9248E6ADF2A894797FC9BD47441 (void);
extern void CTWebClient__ctor_m3E19D61BF5ED3B2D41519909800B27B3035039BD (void);
extern void CTWebClient__ctor_m43F26AF90987B5E3C0D2A0788CC189BE70103498 (void);
extern void CTWebClient_CTGetWebRequest_mBEA02A60043A32644CA65D1DA04FCC225A58F121 (void);
extern void CTWebClient_GetWebRequest_m6023851CBE1E6047E6406247C66C7092D7460BFE (void);
extern void FileHelper_get_StreamingAssetsPath_mDECDD6689B9F2ADF5B46DE2780DCD4340F6AD86E (void);
extern void FileHelper_get_ApplicationDataPath_mCE1046DE6B6B76DFA649A27A72FA9B615B7591AA (void);
extern void FileHelper_get_ApplicationTempPath_mEF98EA2181BAA7771710EE107E139FC0BE016435 (void);
extern void FileHelper_get_ApplicationPersistentPath_m6ACBCE3B15052DE1C34542FB75CA7813F09A0A57 (void);
extern void FileHelper_get_TempFile_m62F38837E8E86916EE766A6067A5B39FFF16DDD6 (void);
extern void FileHelper_get_TempPath_mC759997ABF565E53649E92D7256B37CAE7EFAE05 (void);
extern void FileHelper_initialize_m427913F233E067FD9B4B9E1216497F4FCDCCDE2E (void);
extern void FileHelper_isUnixPath_m321A823E2734A199165441064BD8E5A5C0A7241E (void);
extern void FileHelper_isWindowsPath_m937C9F4646C800091366484F1F28DD82D629023D (void);
extern void FileHelper_isUNCPath_m78FA462C8166855A39D499209EA49BDF44A71C63 (void);
extern void FileHelper_isURL_m4FD2F46E1364281A170172B1C8047FF89DEBF0A4 (void);
extern void FileHelper_ValidatePath_mACFA02E9B4A8206927F891EAE09CF10AF8D640A2 (void);
extern void FileHelper_ValidateFile_m5A9FCA99866FF9F85C1E39218D32DB03173CFC73 (void);
extern void FileHelper_HasPathInvalidChars_mAE898A71FAAF4EABEEB72DF67D51F776D9F9CBB5 (void);
extern void FileHelper_HasFileInvalidChars_m39A50114F5C237520378FC4868CD6F0D57F22615 (void);
extern void FileHelper_GetFilesForName_mFED614111399C62FD54A25D61F3EE0BBDD35EC93 (void);
extern void FileHelper_GetFiles_m1CEB56C25CBA4812A7C97942CEBD59E323555DE5 (void);
extern void FileHelper_GetDirectories_m6161C09E3578F47138692DE77EEBC6D9BF717B03 (void);
extern void FileHelper_GetDrives_m1366D5B76C7A5E6F3249180F2A71014B46857AB3 (void);
extern void FileHelper_CopyDirectory_m0A7CD5EC34752970F3945FF8F6C12B6EECDFEF5B (void);
extern void FileHelper_CopyFile_mF4BA88FDC8EF621F84F2C71C50336692D386380E (void);
extern void FileHelper_MoveDirectory_m70AF77FA199702A1C21EBCB68BB03B28F767731F (void);
extern void FileHelper_MoveFile_m8ED843D6126DD420FA659BCAE64BFEC38E37CEB9 (void);
extern void FileHelper_RenameDirectory_m51BC586C931EEB34DA795D2EACE087691FB5035B (void);
extern void FileHelper_RenameFile_m4B200DC9925B540A6870D61DA367014A3E95C953 (void);
extern void FileHelper_DeleteFile_mC964F4286E26EA9BEB51C77AC60BBC92F1BD2BC6 (void);
extern void FileHelper_DeleteDirectory_m6F53630B02E4A7A32E7068FC496ED3204C1E5FED (void);
extern void FileHelper_ExistsFile_mDD1EE6FDDA998E11C16D7E78F2FF18D07FE16CC8 (void);
extern void FileHelper_ExistsDirectory_mE6F80D97C3CE2A893B03D702A14DEA02689F7824 (void);
extern void FileHelper_CreateDirectory_m536CBD951B36BC4896CAE229B0B2A0FA81748CB1 (void);
extern void FileHelper_CreateDirectory_m7C8E6DE249262226334AF52948AB676B12C825F7 (void);
extern void FileHelper_CreateFile_m545C9F8F338478EA3B528BDF96783BD51BA2048E (void);
extern void FileHelper_CreateFile_mD8A476FE171BB18DCECA9AE4FF612F412A9559DF (void);
extern void FileHelper_isDirectory_m6C974C437BDB81B8E6EFD0F31566A96DF1767687 (void);
extern void FileHelper_isFile_mCC17C15FE4EBFB924E4E24D0D0203884C3110B48 (void);
extern void FileHelper_isRoot_m19E2AD9ACE114D8EFAFEB2379E001260547BFE35 (void);
extern void FileHelper_GetFileName_mEFB6927ADF71FD971C21A962723F784B3D6D05BD (void);
extern void FileHelper_GetCurrentDirectoryName_m2E4A4AA7B5C1CEDBACF6F513BC2FA4AEA8FC550D (void);
extern void FileHelper_GetDirectoryName_mACC8EF60572CB1BC91590C75435320C2121EFCF0 (void);
extern void FileHelper_GetFilesize_m0BD380442205C6D5FE0C72864A66B3A9CD98E755 (void);
extern void FileHelper_GetExtension_m2A5BD174C1C6160AA444CF5DCCFEDCD6BE18828E (void);
extern void FileHelper_GetLastModifiedDate_mD1E8E2D558FB822F680148613E9A03C30818BE6A (void);
extern void FileHelper_ReadAllText_m1BDC643B71D311559362C2CECA3E9D6384FC39C3 (void);
extern void FileHelper_ReadAllLines_m36BF56D13E7AE44970C8135333009C570207274B (void);
extern void FileHelper_ReadAllBytes_mD1C7B7246C1C19077487660DD3C3221971B76527 (void);
extern void FileHelper_WriteAllText_mA07DBC392F91AF5E81E20BB538D6D56BCD98C95D (void);
extern void FileHelper_WriteAllLines_m4B8B25B85F7D91E2CC5C178B61B0E3E4D6A393AE (void);
extern void FileHelper_WriteAllBytes_m372A15AC31B472D9875AA8E95CC610BD948BE86F (void);
extern void FileHelper_ShowPath_mBC4F03866FDC199ED3C4057221FC95E633E481DD (void);
extern void FileHelper_ShowFolder_mE7989350E32514D784A532669B94C4117B49E302 (void);
extern void FileHelper_ShowFile_mE6DB795241FC96886C2A6F75252B7D58F2F439BA (void);
extern void FileHelper_OpenFile_mFE82392D943CDEF4C18335AD071DD4D8DC075189 (void);
extern void FileHelper_PathHasInvalidChars_m7616568B94FC4D42F1D6F94EC5866D350B697035 (void);
extern void FileHelper_FileHasInvalidChars_m87E4554CBF54579F8AA9986888AAB95E60E9EFBD (void);
extern void FileHelper_CopyPath_m419260BD6D2C259D4B104FB08F38BB1EFE1E6859 (void);
extern void FileHelper_MovePath_m54689F38044A61BD7D25C9934B63D4C5CBA28B47 (void);
extern void FileHelper_copyAll_m6ED688D279B24071F8B2076BA4A6696095098216 (void);
extern void FileHelper__cctor_mD11FBF5879D14DD231BDCACFF291B94F4C0ECD6C (void);
extern void U3CU3Ec__cctor_m6B55495B50BFD63B1A008F7E7E97FB6A56D3A6BE (void);
extern void U3CU3Ec__ctor_mF51F1EB3F7E679DFBEB5D83A24FE7C032189CE72 (void);
extern void U3CU3Ec_U3CGetFilesForNameU3Eb__26_1_m2F439F77BF0EDCA30CB241FE5779B78FBD4093ED (void);
extern void U3CU3Ec_U3CGetFilesForNameU3Eb__26_0_mB872B5CAE8BF07B797E7D4859D4BBE0AEA80834C (void);
extern void MemoryCacheStream__ctor_m8E0C1F9B38FC1A9D08AA9ACA8E3E4CBF3564083A (void);
extern void MemoryCacheStream_get_CanRead_m7D84EBB6415907ECA472FD9FC0A31B339CCC6816 (void);
extern void MemoryCacheStream_get_CanSeek_mA880179D3CC23475F7A1967AB9C80BA5E489B698 (void);
extern void MemoryCacheStream_get_CanWrite_mE044619204D0D924CAA670C879ACD2171C7EC805 (void);
extern void MemoryCacheStream_get_Position_m315433FE042F6245119E590A04A92E77BB15D8EE (void);
extern void MemoryCacheStream_set_Position_m8459EB52E1758ABCE8DF1EDCACEE7DE4C2EE425F (void);
extern void MemoryCacheStream_get_Length_mB066A2C332DA421165B84E2F015B93D1954AAEF9 (void);
extern void MemoryCacheStream_Flush_m296440ADDBDD52B4DDF6E0300CCE0B7079373EA3 (void);
extern void MemoryCacheStream_Seek_mB153AF093A8B6D9B3745648446E1C156C731BA97 (void);
extern void MemoryCacheStream_SetLength_mB630B9F8ECCDC3B484FA5702B9F953E68B25A35A (void);
extern void MemoryCacheStream_Read_m81FC91487491264D770BD407574358935E05214D (void);
extern void MemoryCacheStream_Write_m2108334BDDFEBC5BB3043DB6A4A46E93797F675D (void);
extern void MemoryCacheStream_read_m17108F23BBFCFE4563E07E77A7324B90ED99E4D2 (void);
extern void MemoryCacheStream_write_m1F62D98081AB5DA6E5996BEB70F3D78FCB8A627F (void);
extern void MemoryCacheStream_createCache_m56DA619544477CBBAA812729BCD43B70209F14D9 (void);
extern void NetworkHelper_get_isInternetAvailable_mB56ED69D2521A0DBC691EEEACB44A801BBD83FC7 (void);
extern void NetworkHelper_OpenURL_mD626F648DEBF117489E9D09E4340BCBCF279FAB0 (void);
extern void NetworkHelper_RemoteCertificateValidationCallback_mDD135E68534F72382893EF6984832F4D394B9B12 (void);
extern void NetworkHelper_GetURLFromFile_m7D2A5CBE772129EEFF30A8492DCA6BA7AFB327C9 (void);
extern void NetworkHelper_ValidateURL_m790A6F1803977B049A7B999C180EC72B880B3AED (void);
extern void NetworkHelper_isURL_mA0BB19DF9F3D0820F52B6FE7798BFFA3019630E8 (void);
extern void NetworkHelper_isIPv4_m2450BB793284B254B816B73D280631D6E5F339CF (void);
extern void NetworkHelper_GetIP_mD31011B2D78DB42267CAF9E9B5BE94EBB62C5F4F (void);
extern void NetworkHelper_ValidURLFromFilePath_m7CD1047F3ADE69A6732309DF7AC5F11DD363702F (void);
extern void NetworkHelper_CleanUrl_mC550DC8791D4E46F079E2F9EE88CDC57715F307A (void);
extern void NetworkHelper_isValidURL_mEF4AD8F9A4B73DD6B929B4BC6FF416D24F6DD60D (void);
extern void NetworkHelper_openURL_m9742A5098EF65CC8E4EF96D10170A2ED2820F901 (void);
extern void NetworkHelper__ctor_m3A4148FAF2141C2A6A8C7FBD81BFF4AAAABBD720 (void);
extern void U3CU3Ec__cctor_mBDD92949CB9646A1D1D28C7D7405834685D67C81 (void);
extern void U3CU3Ec__ctor_m634B8F157D93EF8BE0487FEDCD64AC4D0679CDF9 (void);
extern void U3CU3Ec_U3CRemoteCertificateValidationCallbackU3Eb__4_0_mF715B54D8898D2D96F30D5FE8A93B90BF5EAB511 (void);
extern void SingletonHelper_get_isQuitting_m1112F111E7850294FC34B43E7A2D498B082A9CF0 (void);
extern void SingletonHelper_set_isQuitting_mD423D90445BBDD7FF9072CEC350598AAD5DB625A (void);
extern void SingletonHelper__cctor_m23B1D46710FE149612CBC68F1C795662433B0556 (void);
extern void SingletonHelper_initialize_m0E957A50EA220DE7332F3FCDE7E9E59B1D238D69 (void);
extern void SingletonHelper_onSceneLoaded_mB71D3AB83D2A69B57CDF7C4D488A65D15D63B6E9 (void);
extern void SingletonHelper_onQuitting_mE1B5B1153F80CDF37C6C076C672ACA8357546151 (void);
extern void SingletonHelper__ctor_m89942F0B520E1C0BC400413A1670F93BA21FC4B9 (void);
extern void XmlHelper__ctor_mADBA03B84D780D0E14C297600C1A47BE6A42E57E (void);
extern void U3CPrivateImplementationDetailsU3E_ComputeStringHash_mB4C632184660364C404321BFCA05E1ECF45A5047 (void);
static Il2CppMethodPointer s_methodPointers[755] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mC7CA174A23290C34424DF6D2733D5E64B92E5977,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m3C9D09F94200334DD5FA29A465481C7848AF4549,
	ExtensionMethods_CTToTitleCase_mD8912604139364DA9F23864F1C881719C2406B0A,
	ExtensionMethods_CTReverse_m5B3CA05B6C3B8467983E996DB1DE3494953B1827,
	ExtensionMethods_CTReplace_m09CC2E70AD6303C9E585600D1F79FF275FB9DCCB,
	ExtensionMethods_CTRemoveChars_m79B9B564B884EF3F675A7355255B611BA4983FD6,
	ExtensionMethods_CTEquals_m57623DA37059652671F9B8BB4439D80F13CCC7BF,
	ExtensionMethods_CTContains_m2176C444D13298F8E63508431E9EE81F18B2C683,
	ExtensionMethods_CTContainsAny_m96517DB3012A304D2BBCF44CF2267C81434C687A,
	ExtensionMethods_CTContainsAll_m1E27935C322EB3A9DD60158DE5FB64C6EE40C894,
	ExtensionMethods_CTRemoveNewLines_mDA5C259756BF8DA7839A6D161FD95C944097AA6E,
	ExtensionMethods_CTAddNewLines_m006EE15C14BB34AD570AF5D2A6E3D267379E7E4D,
	ExtensionMethods_CTisNumeric_m3F984C7DE8A06F5F991678070157B56008F01C5F,
	ExtensionMethods_CTIsNumeric_m061DE6DA5CB082AB19F517B1602EDA3D20FE45E1,
	ExtensionMethods_CTisInteger_m8A68507F188DD45A7B1C09DFA149B121B1E0CD09,
	ExtensionMethods_CTIsInteger_m7D0070D58630EBD39F21743AD2F530BFB3FE9CD3,
	ExtensionMethods_CTisEmail_m4E12D93FFF0CED3EAE7665FF56B5BFAC82931F67,
	ExtensionMethods_CTIsEmail_m36A7472EF384288223DBC36A66595F695AA52C6A,
	ExtensionMethods_CTisWebsite_m4FD21F26D0BD084510D8C64AB941F270EA0759EF,
	ExtensionMethods_CTIsWebsite_m49D83E20FC464D94E02A5E6C4233DB8E516A8B9E,
	ExtensionMethods_CTisCreditcard_mEE8347CC6DC2EADA53531D4ED777BAC9B6E58569,
	ExtensionMethods_CTIsCreditcard_m07FD11EE8C29E701EF34E9447260073D76D1CADA,
	ExtensionMethods_CTisIPv4_mC8724363392E74CB91B8C6AD3FB0C07BA5179F3F,
	ExtensionMethods_CTIsIPv4_m5C7BFBDC80406EA4F4F40E01BAB1BDB32961DBCD,
	ExtensionMethods_CTisAlphanumeric_mA50033DB9BEF56B2BF6DBC3307F478CCDCF536D1,
	ExtensionMethods_CTIsAlphanumeric_m278B2D880099C39F51977474FCFF6EAB6EFA27C5,
	ExtensionMethods_CThasLineEndings_mA23A8D573B0133FA0E5268E76C57FD73BA0BE94A,
	ExtensionMethods_CTHasLineEndings_m83F9E4A20C1E840F6E0DBB2CBDC5E9AF585F1A5D,
	ExtensionMethods_CThasInvalidChars_mFE6FDEC1F0D6C3CC791B37F3B4CD56D44DC888B9,
	ExtensionMethods_CTHasInvalidChars_m35B090986495AB8B3CC4DA3485CCA4A8E45296AC,
	ExtensionMethods_CTStartsWith_mB5C1E02A6876078E0393429E756D4BA4DD012B68,
	ExtensionMethods_CTEndsWith_mBB9B06D1DB2ED41CDD982F06ED82124EB6445F61,
	ExtensionMethods_CTLastIndexOf_m8B27A3F1C9445BD3C4E2A8FC877360045772D137,
	ExtensionMethods_CTIndexOf_m139B5B05D1ADC84292DDCD3D3969B50EAEDB7933,
	ExtensionMethods_CTIndexOf_m5AF50B1587AE2A73EE5A98B32D3E0E7B39920F24,
	ExtensionMethods_CTToBase64_mF8C11A7EA5FE9EA353685C6AFE4B8FB5ED8C6BC3,
	ExtensionMethods_CTFromBase64_m2706806E212E51B45ADB848D33C759F6F8572DCD,
	ExtensionMethods_CTFromBase64ToByteArray_m05BC21F1365F157CF245D86B7F22E5E67105C150,
	ExtensionMethods_CTToHex_m701CF03766BAED3D030ED5A28C43FCD9EF7DC830,
	ExtensionMethods_CTHexToString_mEA705524570C1EF7E68AE2189F987A75CEF0592B,
	ExtensionMethods_CTHexToColor32_m67A37401CA2F20FB255FF5FE167CA7C0053EFC4C,
	ExtensionMethods_CTHexToColor_mD29375F7F7410F04AD2F2ADAA0F3BBD4EE7FF4CC,
	ExtensionMethods_CTToByteArray_m5A296D4C151916841471C6884EBA49F7CBE1FACF,
	ExtensionMethods_CTClearTags_m740802566AAE746B44C5B782899ACBD00188BC73,
	ExtensionMethods_CTClearSpaces_m13549AD4DB227E1E47F73974B85FC07F2735AD29,
	ExtensionMethods_CTClearLineEndings_m558AE9859A7C848C57965E229434A833DA1AD282,
	NULL,
	NULL,
	ExtensionMethods_CTDump_m33BA123C4CB74D30140EA4C1F2FE058F705B3C8F,
	ExtensionMethods_CTDump_m9A2A7DEEE10BB172934AD05120DBF4ED44E7C4A7,
	ExtensionMethods_CTDump_m58539EDF5F2615458FB365708F9966A577D5DF39,
	ExtensionMethods_CTDump_mFA7BDAD692E4846225C46AC6E37CB5EB754924E4,
	NULL,
	ExtensionMethods_CTToFloatArray_m891661184D3D207B0848B8C9291C661AC7373E29,
	ExtensionMethods_CTToByteArray_m212D7235B30775847C6A084AFDA8008935A582DA,
	ExtensionMethods_CTToTexture_mE682A1A13F47800A784BFA620041CB42D986CBE2,
	ExtensionMethods_CTToSprite_mA7AC50E776C7FA1DD109BAC4CC9BD25A41EA93E9,
	ExtensionMethods_CTToString_m17444A55B2906DDA25F6B7E687BF9916B9A4FAD0,
	ExtensionMethods_CTToBase64_mA97C676582E34CC7E2D60BE8EF8125B86C5662F6,
	NULL,
	NULL,
	NULL,
	NULL,
	ExtensionMethods_CTDump_m525436E01A81EF235D49C193F7992059EE29374A,
	ExtensionMethods_CTDump_mCAE6C04C0E956A29E5E3A1125BE8D764C66319F1,
	ExtensionMethods_CTDump_mC1623EF9B6EB40753A6E231B5261C4EB3B29E389,
	ExtensionMethods_CTDump_m6D4D18B0D7783E1126F49FFEF81E30A06E88AE66,
	NULL,
	NULL,
	NULL,
	ExtensionMethods_CTReadFully_m031567CEC3FEA9A1A14A182A202B84DEC93BB319,
	ExtensionMethods_CTToHexRGB_m8650DC53667BE5B556F1A15508F4AFC649347436,
	ExtensionMethods_CTToHexRGB_mDBDBCFE45C532B61F83144868801917689EE00AC,
	ExtensionMethods_CTToHexRGBA_mCA21EA03112D7120832E1D02CC774719903AE9BD,
	ExtensionMethods_CTToHexRGBA_mB751C2239DA565330255FE544D3B2B0CBAE650AA,
	ExtensionMethods_CTVector3_m6984F5E80C78644CBFC89A7E150F8FDD659C366B,
	ExtensionMethods_CTVector3_mCA08E9B0C4D023B26CA9951F2008204248EFE8A1,
	ExtensionMethods_CTVector4_mC433A2A8586C7CFFE023CC0180C1A5CF299C51D6,
	ExtensionMethods_CTVector4_m94FA7FD1625532E6B9AB8DB7844CE7AFDF2E2290,
	ExtensionMethods_CTMultiply_mA2F42D4C9CF55521B62EF00E469CC9054AE17233,
	ExtensionMethods_CTMultiply_m47E9FE5C1995BC250E028A6B2B50DCF1C495EA7A,
	ExtensionMethods_CTFlatten_mE5CE5C37BC01C82A62AB0BED58E26EDFCB085D1E,
	ExtensionMethods_CTQuaternion_m66BB51F40A9B9484F8ED80FA595117B1A813EBEE,
	ExtensionMethods_CTColorRGB_mA073E49F24A2AE374233812AF266E9C871486FF8,
	ExtensionMethods_CTMultiply_m2DF7247C533B4C0886596C28DA09CFC48B26678D,
	ExtensionMethods_CTQuaternion_mA7899B04DAB8CBA52492D8FB88EC5B817BCF5535,
	ExtensionMethods_CTColorRGBA_m038AC6A633F2046346A162FD36396DF65469A8DC,
	ExtensionMethods_CTVector3_mF3194515228664D7E197C6FBCFE54E144576D650,
	ExtensionMethods_CTVector4_mAB92762CA8F1434ABA2179AA41946237D3FA80DA,
	ExtensionMethods_CTCorrectLossyScale_mC4072308CAA143928D1408952889A2A66B02A7F3,
	ExtensionMethods_CTGetLocalCorners_mA372D1D02C5BE19E6F0CEB6A330D82BA07D79EE2,
	ExtensionMethods_CTGetLocalCorners_mBA6966F4073433AA984CD5380D105805ABB6707F,
	ExtensionMethods_CTGetScreenCorners_mBCFFD2F22172A69C14F25904FE5BB31325EE2FC4,
	ExtensionMethods_CTGetScreenCorners_m4D098A3EA0C0BFE53890B2DA6AAD6502FCDB867F,
	ExtensionMethods_CTGetBounds_mE054FBA3493D294A9585E5F21F4CB1072584DD87,
	ExtensionMethods_CTSetLeft_mBF644B51D466D22A2086D8CAE02E6B01C8F885B8,
	ExtensionMethods_CTSetRight_m7131AD8CAAFBF45AE1658DEB57606D1B1D983548,
	ExtensionMethods_CTSetTop_m8BD3AF804B9AD0FA9963A37CB853F545A8C67ED5,
	ExtensionMethods_CTSetBottom_m3E21BA1C64BEA3C0529D8A8D0B727DB0A18A8D64,
	ExtensionMethods_CTGetLeft_m1F7A7EFC5144E05C577287019F8E106BF5EBD25A,
	ExtensionMethods_CTGetRight_m6C5DC7576CBB006B14E0EE74D9A35C7916C746C7,
	ExtensionMethods_CTGetTop_m0B2B5C0550AD6D371ED57184322F50DBF8482D7F,
	ExtensionMethods_CTGetBottom_m6886413E7810A58964FB484598452D158637BECA,
	ExtensionMethods_CTGetLRTB_m62E826D5D45215DA343E4A5FEAC916C594CE214E,
	ExtensionMethods_CTSetLRTB_mD249898FB93D80FB310C0B84E895A2515740C3BB,
	ExtensionMethods_CTFindAll_m3F09DDE73A2F34E305125CD46B01ED77DABA6DAA,
	NULL,
	ExtensionMethods_CTFind_mCE8BDA6860A8DC20FB76C28AA2A740C05864081F,
	NULL,
	ExtensionMethods_CTFind_m72E0ADC0584A713817A622D0990A01C6E58308FC,
	NULL,
	ExtensionMethods_CTGetBounds_m7B45B92EC4CE0CB25E921D76B8B71D7685D67A4F,
	ExtensionMethods_CTFind_mF22ABB62C9BD2D1FE9A3AE7F73FCD9C3CE649910,
	NULL,
	ExtensionMethods_CTToPNG_m7925B0CCA4C0AA0783D6EBED66C563D98D2D3488,
	ExtensionMethods_CTToJPG_mA3DCC23368F459B7724D293019D34DB011BBB194,
	ExtensionMethods_CTToTGA_m9927C4527CB30AE926CC6E44EBF1D618215017D9,
	ExtensionMethods_CTToEXR_m1D2D1FB9F326AAEF32CDEAAAD546FEF9932A6081,
	ExtensionMethods_CTToPNG_mA3EB11A3BF1E34381D6EB232794EAB9175957D33,
	ExtensionMethods_CTToJPG_mBE203A9FC46E3135E2E9932EA8079871467FF1D4,
	ExtensionMethods_CTToTGA_mCCB852CB61F0858B16310280778397C2FB1444EC,
	ExtensionMethods_CTToEXR_mA8042DDE5562CC8BD8D8ECB26803D8CF0438A8B1,
	ExtensionMethods_CTToSprite_m3ABAA4556FB10799C4223DECD91D902DC5E82C97,
	ExtensionMethods_CTRotate90_mF562A74FD57789291885A96594DABDD8E8A6C8B3,
	ExtensionMethods_CTRotate180_m66FAE50C47AE4AF674FB3937B76AB606730313B8,
	ExtensionMethods_CTRotate270_m01644DA8EAC808924B6BE118416F56F07512B22A,
	ExtensionMethods_CTToTexture2D_mC238A56F92FEBCA8EF40237A11725BBB081DEFDF,
	ExtensionMethods_CTFlipHorizontal_m5583811F36F6B0048EE4FE6BF3681EDDF702B072,
	ExtensionMethods_CTFlipVertical_mC3BA983B9DA317188DEEEE38C8F33B71AA84DB29,
	ExtensionMethods_CTHasActiveClip_m30AB2E82E44A8A7A050C9BCA88DC5E9B7D905F41,
	ExtensionMethods_CTAbort_m859D3FAB9CDDC74D820B2280A7DD449FDDE394CA,
	ExtensionMethods_CTIsVisibleFrom_mBB44E44CDCE6DD2743DFF4420E3EEA9A77E48099,
	ExtensionMethods_deepSearch_mE32B3041D29AF43E45B6C12D623259F4CC908606,
	ExtensionMethods_getAllChildren_m5BB79ACD232B7D23ED6E1CFE4180EEC0ADEB28E2,
	ExtensionMethods_bytesToFloat_m872E8618D666788FEAD0D45D25CE2ABAF552A44D,
	ExtensionMethods__cctor_mFAA2B849830649E4C1784B6264974824B5822230,
	U3CU3Ec__cctor_mD288B072253BDDB0CD12F1B503B6680AC3ECAC1F,
	U3CU3Ec__ctor_mBD6933056C92750D8ADD53CC3E55BCAA9AB4BB9C,
	U3CU3Ec_U3CCTRemoveCharsU3Eb__4_0_mAE272EF6AC71D863124B9994A31E5C3B34E4B469,
	NULL,
	NULL,
	NULL,
	U3CU3Ec__DisplayClass104_0__ctor_m1FFEACA8F7D79C21D7C6A9AC31948D994E5710C5,
	U3CU3Ec__DisplayClass104_0_U3CCTFindAllU3Eb__0_m4A2523AF0F3549BB45613553B4BEC978AA6A703D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	U3CU3Ec__DisplayClass7_0__ctor_m4763A1BF292F6722B879BB8718B356F9F118FD05,
	U3CU3Ec__DisplayClass7_0_U3CCTContainsAnyU3Eb__0_m21909545A4B252BAFDC4573C61CE31B4A4BA59E1,
	U3CU3Ec__DisplayClass8_0__ctor_mEF1CF6AA384AD605764CB165048CF8A2A568002F,
	U3CU3Ec__DisplayClass8_0_U3CCTContainsAllU3Eb__0_m88F4E8D91191A1DB1206CCA655E467EFAAD709AA,
	U3CU3Ec__DisplayClass93_0__ctor_m7AD688D5105B53A4EC958ABF1D03FFF66E814773,
	U3CU3Ec__DisplayClass93_0_U3CCTGetBoundsU3Eb__0_m331B037ED9422C50C8F4E9E7CD27E1AC7D06FC8F,
	FileBrowser_get_CustomWrapper_m7035DDE90019A4706F23DE08335574D46F75EC77,
	FileBrowser_set_CustomWrapper_mC691E573CCA1AA6C60DEEBE290B2E09D4E46B65E,
	FileBrowser_get_CustomMode_mE1C7500D89175E703B432D99576A7A9BABF68C26,
	FileBrowser_set_CustomMode_mE6E73DD4EC288D37F8C939D930D10C4578CCEB53,
	FileBrowser_get_LegacyFolderBrowser_mDD51971EF86032B639433CA45F8AAB5592C140EB,
	FileBrowser_set_LegacyFolderBrowser_mEC49B096B7D267232FD9C91D899B7174BA39970E,
	FileBrowser_get_AskOverwriteFile_m98337CF5E0E3B99195849CD68813B406208C84C0,
	FileBrowser_set_AskOverwriteFile_m6F776FD6B46D24D11760C489ED27375D48C690F7,
	FileBrowser_get_AllowSyncCalls_m7DB5CA4CC797ABC46DE42F8259D1C943617DF018,
	FileBrowser_set_AllowSyncCalls_m604AF88F4E608C812CE577DA1687AA5378F3136E,
	FileBrowser_get_AlwaysReadFile_mB4F9E00908EFC29430569294E448AA37DFD13C5B,
	FileBrowser_set_AlwaysReadFile_m3A2E188CC3F96B6448D3495729BF62A5CD8C2C4C,
	FileBrowser_get_TitleOpenFile_mEB8D7F2637C945EC6E184A10BFD2F3986ABDC0B5,
	FileBrowser_set_TitleOpenFile_m5F25F9851BAD1283A39EEF8E56EFD98664D7D90C,
	FileBrowser_get_TitleOpenFiles_m28F00A2A9F996411465A656A36C0662870C1330D,
	FileBrowser_set_TitleOpenFiles_mD7CAD8B9A7D6FA5021CC0A551DB009843B9C7AAC,
	FileBrowser_get_TitleOpenFolder_m93FD609CB44A33A488E115B8B4626932DAD10383,
	FileBrowser_set_TitleOpenFolder_mC7204057BF659B0BD98A9549964B05CAC00D2B82,
	FileBrowser_get_TitleOpenFolders_m0D1488048759CD7807371DF425011A2E102C0E29,
	FileBrowser_set_TitleOpenFolders_mD1DC90AE25FB742BD9BF76BE6BC7A943C4BE7837,
	FileBrowser_get_TitleSaveFile_mC23847399734489907FDC4275C662CE533EB65DB,
	FileBrowser_set_TitleSaveFile_m044563D1491A32B3D7857E49F3AAAF976A645DA0,
	FileBrowser_get_TextAllFiles_m3A67267F08E2BD38C322ABB04AC1085E27958772,
	FileBrowser_set_TextAllFiles_m3FB087A13F5A67183DB55D9B650C67A9902E7702,
	FileBrowser_get_NameSaveFile_m66339F2E8487CCBE6C7A69E1E365C30A44E78293,
	FileBrowser_set_NameSaveFile_m443803DA77553F650562B3B38BE76FC60FD8F5B4,
	FileBrowser_get_CurrentOpenSingleFile_m70A07B45BE40B6A3F7E4CF499E3D7B6ED05DECAD,
	FileBrowser_set_CurrentOpenSingleFile_m9187B46E72609E89B127B9D2E707396E657475D6,
	FileBrowser_get_CurrentOpenSingleFileName_m9DC9537E830E134E73DB64D63395B088FE1A42C1,
	FileBrowser_get_CurrentOpenFiles_m6A6D6675C024D37E22F4A953A6C4ACD91FAB27BB,
	FileBrowser_set_CurrentOpenFiles_m63B8FB67D199BB2CA7222A90A4BC13E1066283EA,
	FileBrowser_get_CurrentOpenSingleFolder_m0AFD4036909A77A1DB93DDD543DA9FB087AF0683,
	FileBrowser_set_CurrentOpenSingleFolder_m3769F4B8FDBAF39D4698F253055DB8E78CF959B0,
	FileBrowser_get_CurrentOpenSingleFolderName_mCE7A6ED1D551E14AFE5F715F430983B5C2B21AE0,
	FileBrowser_get_CurrentOpenFolders_m52C1EEBCF183212BF9A928E26542CB6CF60EC532,
	FileBrowser_set_CurrentOpenFolders_m33A389448BE1A56C619FC27E011A46B06829DFE1,
	FileBrowser_get_CurrentSaveFile_m516A0128D5FF33E15D563466E74FD3A950D5B0FD,
	FileBrowser_set_CurrentSaveFile_mB1C49D1666E40903F9792F93CF08DD16FF9C18F1,
	FileBrowser_get_CurrentSaveFileName_m805B762EC01004A806D0FD590E2026E2DA89ECA7,
	FileBrowser_get_CurrentOpenSingleFileData_mA2ABEE811416E73104BC8DA94FCCAEE88504CA15,
	FileBrowser_get_CurrentSaveFileData_mE99022552B4895C9184D772D20264E64B1FE795F,
	FileBrowser_set_CurrentSaveFileData_m0A447E4CFC3C53D4F503608E1EDDDB89B86CD475,
	FileBrowser_get_canOpenFile_mBB47344C7DB7C69FA16987BA5BBBC49C20058608,
	FileBrowser_get_canOpenFolder_m799F397C8E6E91D5DE54D56792FD1E43D4401D58,
	FileBrowser_get_canSaveFile_mFB05C4681864077E8034A39C16E6E1C5698FBF36,
	FileBrowser_get_canOpenMultipleFiles_m41CD39F12AAE7E6A32BF56141D95D99715E53A17,
	FileBrowser_get_canOpenMultipleFolders_mE044739E91DE2A8796CFFCC7430CDB785AADCEFA,
	FileBrowser_get_isPlatformSupported_m66C89550B19BE8A0A2671841961A54E68E8D7638,
	FileBrowser_get_isWorkingInEditor_mA1BEBCCA7DCBECF1A7B5D44363C785048094106B,
	FileBrowser_add_OnOpenFilesStart_m63265AD8FFD7393079F84E140CB530446E4CE59F,
	FileBrowser_remove_OnOpenFilesStart_mB351023E651C20F18F1F2C34185E84410AE8F968,
	FileBrowser_add_OnOpenFilesComplete_m2E7E4C76116ED9F69DACC579B3D379A6C5835CAE,
	FileBrowser_remove_OnOpenFilesComplete_m972101F15E4B068894F975D100B6B4C35FCB73C3,
	FileBrowser_add_OnOpenFoldersStart_m0B2568E9D3EF9CD337B049CFD2CCB5938E5660A1,
	FileBrowser_remove_OnOpenFoldersStart_mB4037BD6B6C3C0C96AC9B19C21CA4A331B776BE7,
	FileBrowser_add_OnOpenFoldersComplete_m4A684318DCAE880EA4409E1D438A501A83B26CD6,
	FileBrowser_remove_OnOpenFoldersComplete_mFB4F9BAC647A262734D9850F059563B8535CA762,
	FileBrowser_add_OnSaveFileStart_m97638984DA9160725CD1EE8F3309FE20FF8207CC,
	FileBrowser_remove_OnSaveFileStart_mADA05E2EAC70EC67952F194C3B2966C43EE43A93,
	FileBrowser_add_OnSaveFileComplete_m0B44CEF35C6BFDEEB605BB0270651A0816E2ECB4,
	FileBrowser_remove_OnSaveFileComplete_m924380030F5ACA3F861F912F326781AC34EA465C,
	FileBrowser_Awake_m0C55B70CD400914EDAD990B1603A49AE782319E1,
	FileBrowser_Update_m688DDCB7E6D344B0E1D2FC88DAD4535991142B42,
	FileBrowser_OpenSingleFile_m271FEDB8960C234251570178DAEF9ED798783A42,
	FileBrowser_OpenSingleFile_m9FB1778A2D84F1A93FF513FA637569BDB28ACB2B,
	FileBrowser_OpenSingleFile_m46907BDD6B9BACE3DB39471EDF90BEB57F725E08,
	FileBrowser_OpenFiles_mC968B0AF62611941203A727D00057115237BAF04,
	FileBrowser_OpenFiles_m400DA1C0922061E1A9F20C1E5AB21E5DDA2A322F,
	FileBrowser_OpenFiles_mBA8D694D98A8ABC495284B54875AF32CC14E1ECD,
	FileBrowser_OpenSingleFolder_m76D444D586D3C21B9BC9A925D20FD91D9595D93B,
	FileBrowser_OpenSingleFolder_m7005511E45434D22525C7588BBB2028C61FDDDAF,
	FileBrowser_OpenFolders_m7937351EE33ECBF5DEE3143AB0D24AB50A1A9C4C,
	FileBrowser_OpenFolders_mD475E46197E6D76D1B0BA7FE0EF24BF85F49EC23,
	FileBrowser_SaveFile_mB499171FFE4F91137432DF27D40B474F84DB6B6E,
	FileBrowser_SaveFile_m17CBA229D0F828482995BB8A42ADA9DE756778D7,
	FileBrowser_SaveFile_m602494EAD6B448371C77799A18FB585F6ACFFAB9,
	FileBrowser_OpenSingleFileAsync_m989C1E5891B70A88FFEC9EC2CB12EC26F4C8D4D5,
	FileBrowser_OpenSingleFileAsync_mD1FCE6B40E1723332D3EE5B2C9C563246F4408A2,
	FileBrowser_OpenSingleFileAsync_m1E4007761BE6D1BF82B3E57628249B15DEB369F0,
	FileBrowser_OpenFilesAsync_mA7A7325EDBA2BFCA5D6A764D9B828A3699E3FD60,
	FileBrowser_OpenFilesAsync_m24A593E3B87436D27E666164E5A248285BAD389C,
	FileBrowser_OpenFilesAsync_m70057253DFC237F0A050D3AB9D9E1E069FA6409E,
	FileBrowser_OpenSingleFolderAsync_m3CCD538330933A5D1A29A76DD1BE1BBA1FDC007A,
	FileBrowser_OpenSingleFolderAsync_m955B095A110D650E2444DF0A927DA09C3F357345,
	FileBrowser_OpenFoldersAsync_m948C0EA35EC516BFA7ECF11FAE36F3C9F7674F27,
	FileBrowser_OpenFoldersAsync_mCCA6038EBD1383CA5B0E3044935B45706E799FEE,
	FileBrowser_SaveFileAsync_m0C241479ED41E01A7BF26A24363585E090738637,
	FileBrowser_SaveFileAsync_m4DA120079DD137C66D60D82FF33C4875431EF227,
	FileBrowser_SaveFileAsync_mA0F8E8B7D86A22CD55BD56F87FAF0778968D82F8,
	FileBrowser_GetFiles_m2EAAC001705F9210299F0F7F10A240A85E00BFE7,
	FileBrowser_GetFiles_mAD04240594585FA2966BEABD78D7FE910C7BF643,
	FileBrowser_GetFolders_m9E0F0A31AB9211C25F44551F69FE274F7267E132,
	FileBrowser_GetDrives_m0D69F7761BF1A822116F4ED7BB2FE7441B26F4CB,
	FileBrowser_CopyFile_m6865B3515E72FA3F7E15DAA9AD8BF91695B31DFD,
	FileBrowser_CopyFolder_m5AC0DDB049CC3584A23B2D1CF8D4C7C38AC1D2FA,
	FileBrowser_ShowFile_mF86AC413DC97CB49C0B40A08E4FF2BEFBF7AB021,
	FileBrowser_ShowFolder_m5101EFEF9E730045973C4D3148FBA131FEB81EA0,
	FileBrowser_OpenFile_m7CE5710DF0D83BBB314DEA7A50ACF94C4307368E,
	FileBrowser_OpenFilesAsync_m7DF1D8E5292CEF430EAFC0716C1DFBFD055E5C0C,
	FileBrowser_OpenFilesAsync_m92C232A250D44BC169D5B0D40FB4017C83E2FDFE,
	FileBrowser_OpenFilesAsync_mFFFCC2999342DA08393BDEF4999CFDD384830720,
	FileBrowser_OpenFoldersAsync_m1487314A40A29ABEFD7C852733CA4728756AC51A,
	FileBrowser_OpenFoldersAsync_mAB2E25370ABF3BA053641DC95CDA33F5ACFCDCA8,
	FileBrowser_SaveFileAsync_m7B6735A4AAEBA13694CB9E50D4B3617240790A93,
	FileBrowser_SaveFileAsync_m04B914AFAC6BEAFD9CB7A8CD9EB65C63345C15E9,
	FileBrowser_SaveFileAsync_mE8A491D2447AAA38DCCEFA90406D40A2A194897E,
	FileBrowser_getNameFromPath_m104D794AF62DDBCF5334A4BEDEC07F7CB30387E8,
	FileBrowser_resetOpenFiles_mD6C0656C98BC7363AF7B3275DEADE19D6B7EF2A7,
	FileBrowser_resetOpenFolders_mE71A885DEE258A0B60FB15D4F968FF2925F3F03C,
	FileBrowser_resetSaveFile_m1F348CCF471E60D4F5065C278BFE070A3DC1ACA2,
	FileBrowser_getFilter_m75A5BA2CD5DC2C23F952E4506736C29CE5B2E4B6,
	FileBrowser_onOpenFilesStart_m7B04C2CFA940EA3A398CFCE1CCC5879D48E9E8CE,
	FileBrowser_onOpenFilesComplete_m6566A9F25508766E64FAD7A6169C3306FE6FB1ED,
	FileBrowser_onOpenFoldersStart_mC62FD28A4A96E377D08DD4ED3721C12A566815CE,
	FileBrowser_onOpenFoldersComplete_mEACB5AF67318C306430817DEFF2483EDEC7A0A57,
	FileBrowser_onSaveFileStart_m9DDE72BB3C417CE43A14F16F8D92F0DA995C9D65,
	FileBrowser_onSaveFileComplete_m279D743A9070AE0164692E723775DC8944733B61,
	FileBrowser__ctor_m6EC08DD43BE1D52B0743DDECC158907B7B6E0F83,
	FileBrowser_U3CSaveFileAsyncU3Eb__152_0_m0ABD507CECEAC8A5BE7BB1EE1A69C16CC0A1393B,
	OpenFilesStart__ctor_m269F31AF20504DF4E3C4FA47B9F0D4E8F524D20A,
	OpenFilesStart_Invoke_mABA3E22812CFA24B3F7C03240BAB3F340CB043BF,
	OpenFilesStart_BeginInvoke_mBA2A9088AA292C734982AAD8A2A39EB9D1010CBC,
	OpenFilesStart_EndInvoke_m0ADB1CCB8C417A8BE9B97ED174E0816AEA260CB3,
	OpenFilesComplete__ctor_mC2E8638E8E3BF114A5A38F2098ADB1C62B6C639F,
	OpenFilesComplete_Invoke_mAB4C84E1F643DEF0430167220ADEBB16227EAD5E,
	OpenFilesComplete_BeginInvoke_mECBF4DAEA5A9C54B4639FF90ADA391E10B19BF3B,
	OpenFilesComplete_EndInvoke_mE95FACF76D8751CC3CD15582B91FD3C424F8CF7B,
	OpenFoldersStart__ctor_m8577C7E508DFB52D70EFF88D1CB82B6DB6BC51E5,
	OpenFoldersStart_Invoke_m5376A5BC20D6BD6B7C44B9D7F7D1177819FD3A44,
	OpenFoldersStart_BeginInvoke_m343B642BE5EDCCEFC3655D065F69B04E70A7E78B,
	OpenFoldersStart_EndInvoke_m4EE4FC459289E3666C5CA748ED9A598F89A3EFAC,
	OpenFoldersComplete__ctor_mF29E519A99F938321867DCBDFECA24129515379F,
	OpenFoldersComplete_Invoke_m0884DBFFB284CCEBC268EAEF5D533D5D932105CD,
	OpenFoldersComplete_BeginInvoke_mDC78427A7CBFCE35096301D63327BE23A813630B,
	OpenFoldersComplete_EndInvoke_m93C6EEDC7A2206113A7B5AE9BD3934061C3FDAB2,
	SaveFileStart__ctor_m2AE477C2EFFE89E32B604455D8064ECBC2CD88C5,
	SaveFileStart_Invoke_mD2C20D53D95910238B8289B1BDD2DC2DED3263D8,
	SaveFileStart_BeginInvoke_m909B6236153C78D0849E926ACCABEB4CA3592C3A,
	SaveFileStart_EndInvoke_m179F0EDCA6D83BA8CC2C1AF1D18EC3AF85CFBF8A,
	SaveFileComplete__ctor_mA95A218B90D213D151264BE5E5DF722B73D8399A,
	SaveFileComplete_Invoke_m5865A528393961DAABAC585941F2A0F2EDFAED7E,
	SaveFileComplete_BeginInvoke_m3BD1F7A18905B24306056CE6E17DAEA02A51CFA5,
	SaveFileComplete_EndInvoke_m8B7283DEAF3192CC5813966EF3A74B08C9AF4332,
	U3CU3Ec__cctor_m1CEC811BED1B657CB27ED15BBC0FBDA8F2606C93,
	U3CU3Ec__ctor_m842821DE32285C90E2CABC5DE628A644EF5C964D,
	U3CU3Ec_U3CGetFilesU3Eb__153_0_mE9DECAD29897720DB98A4D869A52362AF3283E6F,
	ExtensionFilter__ctor_m84B8C8B2F6885054D6DDE7E2431B6B12231B792D,
	ExtensionFilter_ToString_mE7AF0C4F85E9BBF382F2EF65787CBAF3086D6041,
	OnOpenFilesCompleted__ctor_mB136C19C9D4F4ACCC3E7294480A7A04AE5B9256E,
	OnOpenFoldersCompleted__ctor_m38EB333C4BEE6CB657397016B09866F256DAEB4C,
	OnSaveFileCompleted__ctor_mDC0566EAA6683D91EFF8F97D5A3709209422920D,
	WrapperHolder_get_PlatformWrapper_m9EFACA1B78C9DD5F60BA16571AC5884B6A1F5380,
	WrapperHolder_set_PlatformWrapper_m85148B94C0D83B1CDDDBDE350AE2AA754B8D4AC2,
	WrapperHolder__ctor_m4C71D1B7E5DD2566FC044E7A0CD3018DF99CAE75,
	WrapperExample_get_canOpenFile_mB1991D1713D677650766E3FF0A28638E3E0007EC,
	WrapperExample_get_canOpenFolder_mB7F208B4313D1FA173B83BCED6B94656F33D8B8C,
	WrapperExample_get_canSaveFile_m4228750A88B0F59E8BFA8D3143D3BD39E3A8BB32,
	WrapperExample_get_canOpenMultipleFiles_m28AC1E8BE63DB8F0AF72D1DA3E54C0139E7BA67E,
	WrapperExample_get_canOpenMultipleFolders_mBBD9153718163EC371FC7492954D39AEF0BBBD7D,
	WrapperExample_get_isPlatformSupported_m3E855489A03AF218D5498E471BE6C65237ACED43,
	WrapperExample_get_isWorkingInEditor_mFDBA5C9B49ECA94CF7492715B8711011C78D9546,
	WrapperExample_get_CurrentOpenSingleFile_m6479D99F5EAB43FA952D728902F5B3EAB1B1D5B3,
	WrapperExample_set_CurrentOpenSingleFile_mB52CDC9FBD52F878EA40E959D24AE98D33631D0F,
	WrapperExample_get_CurrentOpenFiles_m0AA5904A4E76F8E32D0C21C4CB15598273609874,
	WrapperExample_set_CurrentOpenFiles_m43AE8BB189AAC4DEAC09762327CD2606C93CA37F,
	WrapperExample_get_CurrentOpenSingleFolder_mFB082A8E66A0566060DD30F73637710CC7FF612D,
	WrapperExample_set_CurrentOpenSingleFolder_m7267A23F1F0D3CD13B7AC9F58699B97240BE68FA,
	WrapperExample_get_CurrentOpenFolders_m99E205D4630EBE7C5BAD813A7A1B9C04A91C7475,
	WrapperExample_set_CurrentOpenFolders_m6B0B31D3E48E4159A30439C50AE6543EEF735774,
	WrapperExample_get_CurrentSaveFile_mAFFEB524833D0B5B36BBF73DD424E22032EEA054,
	WrapperExample_set_CurrentSaveFile_mFA09F2F8709985A80872BBA6816407D5FE50DA74,
	WrapperExample_OpenFiles_m4BF3A0E2448959A9BE5F9A750048651220BD90A6,
	WrapperExample_OpenFolders_m70C2251D80B7771FEF2CE1D84616481EAFE176F7,
	WrapperExample_SaveFile_m332BD2E3B72CF22A658EED3C89BA4EB0B6BBB603,
	WrapperExample_OpenFilesAsync_m0040B47885BC71A80D4B749AC4149E22C21EB69F,
	WrapperExample_OpenFoldersAsync_m692EFAD86E1D4B8D0A6A2D53CB0A618BE2033E2E,
	WrapperExample_SaveFileAsync_mC9814B3A0AE7C133D6B5B58F4D7180B3CEFE2E0C,
	WrapperExample__ctor_mF6AF492FC2A369C1462D0012685CF189B5F41470,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	BaseCustomFileBrowser_get_CurrentOpenSingleFileData_m1C8C323436CB5B9D7A94E9E9184CB02E1BB926F8,
	BaseCustomFileBrowser_get_CurrentSaveFileData_m305402F6C4258EFA356E4C556DDA677108EC8997,
	BaseCustomFileBrowser_set_CurrentSaveFileData_mC804D946B02394311196D317635EDC01E425DAEA,
	BaseCustomFileBrowser_OpenSingleFile_m01BE09078C45E273DA0B473D743AE008A6060926,
	NULL,
	BaseCustomFileBrowser_OpenSingleFolder_m9FE0EFFBF7FF1586BE700907097107869C3533DB,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	BaseCustomFileBrowser__ctor_mFB400179DB16BD77EB6AD2F2663C2D6F899F5100,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	BaseFileBrowser_get_CurrentOpenSingleFile_mA8E56B81A51C77AF2C0AAEA6FC724710DADFDC11,
	BaseFileBrowser_set_CurrentOpenSingleFile_mDC5D46D6EB6212F549B65A0158F3CAA9F59A03ED,
	BaseFileBrowser_get_CurrentOpenFiles_mC975C202477FD1C1FF9EA54C9CDF5C3DCC246441,
	BaseFileBrowser_set_CurrentOpenFiles_m42D3EE8EE3E6EA1FF74C97A27481ABCE7DFD0727,
	BaseFileBrowser_get_CurrentOpenSingleFolder_m71F2A523F0E93A7C579C800B9AD3194B1BB98CF5,
	BaseFileBrowser_set_CurrentOpenSingleFolder_m2D88DB910FC052CBDCCB86547F10D05E2AE300D7,
	BaseFileBrowser_get_CurrentOpenFolders_m8DF535A9E969C8DF081A21C70A3C6B648C180C61,
	BaseFileBrowser_set_CurrentOpenFolders_m186C6D8C8F469F740A298A3AC8F000534277DB56,
	BaseFileBrowser_get_CurrentSaveFile_m86E0E41F1A5FD35A7369B24FBA1518A9A3B33D4C,
	BaseFileBrowser_set_CurrentSaveFile_m49FF3E6D92E7870804A4FFA01F7BAD0880A3707E,
	BaseFileBrowser_get_CurrentOpenSingleFileData_m9CF4D65ED107E170129BBFDF154FCA2BDFF71F83,
	BaseFileBrowser_get_CurrentSaveFileData_m370910541D1BDCE021E212AA10D91A18984DF7FB,
	BaseFileBrowser_set_CurrentSaveFileData_m11499902206AEA80D70F73B5B58B69C39444FB01,
	BaseFileBrowser_OpenSingleFile_m6C1C44799548A0E19F8C6C6E35040C9C6F531B20,
	NULL,
	BaseFileBrowser_OpenSingleFolder_m6B40416A178C5B754A37A3C698AB3BE3C024F368,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	BaseFileBrowser__ctor_m880702D7BC6E1CEC4D705A62912AE88713B822DF,
	FileBrowserGeneric_get_canOpenFile_m6323A459FB7A842782EFBF3CB543A9EB26D38611,
	FileBrowserGeneric_get_canOpenFolder_m4A0081F69BCE27AEB05AF1F5D0E32FB5A998B5D8,
	FileBrowserGeneric_get_canSaveFile_mF6F54B100775424920A596450538E98B52D07E26,
	FileBrowserGeneric_get_canOpenMultipleFiles_m2C7A9434B176E820C325B09EA78C8097CED88CC1,
	FileBrowserGeneric_get_canOpenMultipleFolders_m1D399D6A0ED8774F88EF6E3F97C6A55CBBD4AA36,
	FileBrowserGeneric_get_isPlatformSupported_m64EF152FFEF3CC47C56A2BCDDF7781DE828B7DD5,
	FileBrowserGeneric_get_isWorkingInEditor_m19641AA6F898FDF8BD59862936557554031E3CB6,
	FileBrowserGeneric_OpenFiles_m70B610A772CFB09C50F09CF0B6DB28D49A98A06A,
	FileBrowserGeneric_OpenFolders_m7ADA786F7763E8A76DC184CC2E44224F9B352E91,
	FileBrowserGeneric_SaveFile_m9293EBE5A4B02E6299B36976D54ED7960154D23B,
	FileBrowserGeneric_OpenFilesAsync_m9BA4D14AB796AEC63A4F93C2C4A49A533F4CC2BC,
	FileBrowserGeneric_OpenFoldersAsync_mFF3923CF026BC2FBC928DB207F01380AA073ED60,
	FileBrowserGeneric_SaveFileAsync_mC1402DBBB8CCC4718B82A7D33C144E064A4D3697,
	FileBrowserGeneric__ctor_mF67FC0514415DE0876B29C0500310EC29DE4871E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Config__cctor_m1188DFD8E36AF35CE0B45054968F33D5906C6071,
	Constants__ctor_m47820FA773DAA6EB73431A0C1B625EC0277ABD0E,
	Constants__cctor_mC48C5010D3505FAC4DD677BA9B9D1094B43321FD,
	Helper__ctor_mAA1279D4D127915CCA30F9C912621DF6C99E8D88,
	SetupProject__cctor_m5A4651D19375A4F3A57ACE1142B6D94EF64869C7,
	SetupProject_setup_mE1740E5D5C820B4E5328D81F3A422FA555F84BF8,
	SetupProject__ctor_m35200C2F6B5E918EE47BF20D7F7BDB00E3534F60,
	PlatformWrapper_Start_m40F2259EC3BD89FBE90FA1710078A3B77C832590,
	PlatformWrapper__ctor_m3C5C48B764AB23CA097737EAD41520E83ED06AC9,
	PlatformWrapperTuple__ctor_m09E3D0680BF0A725DE755C78BCB22AEA9E3218AA,
	Social_Facebook_mB6EFD5AE09A27AD61962FCE46CFC481148045247,
	Social_Twitter_m332CDCBC0BBFDA1059EF2FE63DFA515B922C4856,
	Social_LinkedIn_m805308826E46C9A877C256EA9305601D95FB9D17,
	Social_Youtube_m0EBDD98C8A0C69722120F3E0C8A389B7A5E24C7E,
	Social_Discord_m0615BFDBC446EE4648E907767DCA336C6462CB34,
	Social__ctor_mB5D378C5D8DF36448BE1AE6809B8DC37D3CB4D75,
	StaticManager_Quit_m6D555E8D5F5802AC267908A337E9307C19DFB75B,
	StaticManager_OpenCrosstales_m5256A5B66EB1B378DD503AC8599362D80B197B9A,
	StaticManager_OpenAssetstore_mCD778E258F518F0C9E49A032C63320A1CAB53E4F,
	StaticManager__ctor_m078E43C016A75F6E66205A8352D75714A22F82C4,
	UIDrag_Start_m0435CD76BCC26C145071D9EBCB105B1CC8C0D1A7,
	UIDrag_BeginDrag_m4275C044D4C9AF36EE7296F582283B41D4B7822A,
	UIDrag_OnDrag_m68EBC4D74CFF6B1CF579607B2F6921AC1A56EA5A,
	UIDrag__ctor_mE1075AA38FDD5880BB6E70DF7C852FC310E51DC2,
	UIFocus_Start_mC4E3E1938ABDA026B785A0BFFEE9960A097F8556,
	UIFocus_Awake_m0378F5AE39CBA5110D32255F2E6F71F39BD69498,
	UIFocus_OnPanelEnter_m9B508D2E48268D8B4FC9849D4D7D11079B7E38D9,
	UIFocus__ctor_m4F5317F5D8615C33932E1D042ED4F5C6FBD28A78,
	UIHint_Start_m360C418159B33F27265E70A87988E5D318B3C17A,
	UIHint_FadeUp_mAE411B3A12D1E198096DB003F1285CF124BE645D,
	UIHint_FadeDown_mF5F4EEE1F8E68CF5101A8888B5E52F6422DFC462,
	UIHint_lerpAlphaDown_m10B13A338538D98C917D74A77A46CDFD7BD9CC49,
	UIHint_lerpAlphaUp_mCDA3FEBE2CE63AB96D0CF9ED4B9CD6E88619711D,
	UIHint__ctor_m68908B27685F720F7EEA813F8CDAC0306E55EA43,
	U3ClerpAlphaDownU3Ed__8__ctor_m87A8888EC5B30789B05A9C4F3AEE2BE8489FD0CE,
	U3ClerpAlphaDownU3Ed__8_System_IDisposable_Dispose_m6EE7D774484AAB03CA8EF6926B1D58007806C9E3,
	U3ClerpAlphaDownU3Ed__8_MoveNext_m0336AC69FC1B3D8AC810B7025A8490266AF84DE4,
	U3ClerpAlphaDownU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0B6D0A9E26CED0E0A0E3D009F94A3977A77B7980,
	U3ClerpAlphaDownU3Ed__8_System_Collections_IEnumerator_Reset_m176138FFEBA8D7DF62B716850069385744F60A87,
	U3ClerpAlphaDownU3Ed__8_System_Collections_IEnumerator_get_Current_mAE1A0545FEEA11939E271E4015E2B1278BD91016,
	U3ClerpAlphaUpU3Ed__9__ctor_m9C9915519829C6B031EE4F711F0397C4BB06F3F8,
	U3ClerpAlphaUpU3Ed__9_System_IDisposable_Dispose_m7DCC07528A9EA29A9D14FCDBF1F1939ACF6BF99C,
	U3ClerpAlphaUpU3Ed__9_MoveNext_m82AFD51CAF5A7770B8A74C4821E41F18C3E85E41,
	U3ClerpAlphaUpU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m97D68BBA7EF81BC61AB312A6BDBE3283485F5EE4,
	U3ClerpAlphaUpU3Ed__9_System_Collections_IEnumerator_Reset_m0C8466BE799D085F9D4368777F08B3B9D4D22E25,
	U3ClerpAlphaUpU3Ed__9_System_Collections_IEnumerator_get_Current_m9D3FFED2B213FC0B99870A2F0D41EC33A0C46CCC,
	UIResize_Awake_mDFC491394A4DCF88E201D60AD1CB8ABD6876665E,
	UIResize_OnPointerDown_m6F5B0E6306EAA1D48946D8BB9AA4D60871382A8A,
	UIResize_OnDrag_mEE0AB78FC4A81231404FE0287074FC8C695AA784,
	UIResize__ctor_mCEE3EB310C39FD74D95DF0CE839B665B2962D837,
	UIWindowManager_Start_m8747026FD93C4B764A5D98D496CDD3139BA2A81D,
	UIWindowManager_ChangeState_m4A737975179903881F1E05DFF834B7C3E46EBE6C,
	UIWindowManager__ctor_m1FBDB5269AC46C6C58E2FD785487009A61C49355,
	WindowManager_Start_mF3F824593A86DD631B1BE1049E4C056BBBC38998,
	WindowManager_Update_m0C861D409D7A5714DC121183FC67220804723916,
	WindowManager_SwitchPanel_mFD8271108536BEC37B96B3A50A13CF63185E398E,
	WindowManager_OpenPanel_mDC8542DF0B09CA5FD804585CD261A92943739636,
	WindowManager_ClosePanel_m83F532BCDDAA57B1877945DEF16B8E8E9CCC5427,
	WindowManager__ctor_mD3E74D01ECA28E8DC1F0E470CAD18A24218C9B6A,
	FPSDisplay_Update_m23B314DD7E9FEB385B9508814FF67D23BE713684,
	FPSDisplay__ctor_mC025AB1A519D9A3A9EA7DEE149146A78E600F8F3,
	ScrollRectHandler_Start_mC7D30A08C004BD6AB1CF6AAB1D766A1EB0FDCCB5,
	ScrollRectHandler__ctor_mD140DF64EB23E01B14B98A24125761568346767A,
	AudioFilterController_Start_m122D61E6B167AC10B0F5F6D863EC0E4907947AAD,
	AudioFilterController_Update_mBDAE5A26E6EEC74DEF27AA58073A343D8A6288D7,
	AudioFilterController_FindAllAudioFilters_m0776059AF142EE9AE96FD2CB45F82B45E6EB7367,
	AudioFilterController_ResetAudioFilters_m320F14E6D4289EA716976093E2E59F4623862251,
	AudioFilterController_ReverbFilterDropdownChanged_mA0BD30EB1678279853C84CD96903C779782F5B2D,
	AudioFilterController_ChorusFilterEnabled_m30074CB15E40676157DBFB1F49EC5EAE9332C0E2,
	AudioFilterController_EchoFilterEnabled_m66A5127D84C7AF8B622C412A4C81131F26A8ABE8,
	AudioFilterController_DistortionFilterEnabled_m7F650E043151A07FDF7DBE2F798B3E151F75978F,
	AudioFilterController_DistortionFilterChanged_m81D7749FCBC0FADF38A714DD11922A87DB164E43,
	AudioFilterController_LowPassFilterEnabled_mEAB9B0A54B33580419F195AC4EE1FB4B445E863C,
	AudioFilterController_LowPassFilterChanged_m3AD53E334723BC078B0F881C24F4F3E78FD1FC88,
	AudioFilterController_HighPassFilterEnabled_m5E79D534C0C28431DAD304BBE43C31FC70FAB26B,
	AudioFilterController_HighPassFilterChanged_m13C72BEAF411FF249E9CA54ED11496BDA07BB704,
	AudioFilterController__ctor_mD57C57F4F8E5E1EC46358703E0BF3C4CEB1266DA,
	AudioSourceController_Update_mA643A1AC634A0D187FAE689D48A626848DDC0C42,
	AudioSourceController_FindAllAudioSources_mCF727A4EF6516DCE23F961CD197BDCD04DA14218,
	AudioSourceController_ResetAllAudioSources_mB6A657F9C04803E7DDC32F25363EBAB4F0EE85FD,
	AudioSourceController_MuteEnabled_m1E305445A2796F6D3B82C54E7B3F96FBC3FA1ECE,
	AudioSourceController_LoopEnabled_mB9DB113B12B824459D3CF3680B89177950ECA635,
	AudioSourceController_VolumeChanged_mCA230C43CCAD908A8C35C0C20B8A0CC6290ED991,
	AudioSourceController_PitchChanged_mA43B85D60DABD051C0D837BF48F1D8ED0E1B1C9C,
	AudioSourceController_StereoPanChanged_mF76BCD92DB4B2C6DF351A153FE966B803A5F8B0F,
	AudioSourceController__ctor_m79B97F5F3DCEA56DB08BA824EB4B97F9870FB8C6,
	PlatformController_Awake_m4BFE7AED53051130D7360E61BDB90D1CD33A43EA,
	PlatformController_Start_m09B2AEE2DECA75C4DBD2DCDA8B5F690B0C6322F1,
	PlatformController_selectPlatform_m790A5987CCB6543D9D84D11138C3544870DF11BE,
	PlatformController_activateGameObjects_m68C3F5FE26328C42FADAA10687C7847A3A34CA4D,
	PlatformController_activateScripts_mC7A7D44FA2D784406985C60CE961433A308B3C69,
	PlatformController__ctor_m2CDB1DF23E7B037E711CFEBCF468C484808BEB09,
	U3CU3Ec__cctor_m6B2ED5EA66D9D351337142911257EEC99F77ACBD,
	U3CU3Ec__ctor_m1AB5DFE32B5606486284950C79ED6690F6C5A636,
	U3CU3Ec_U3CactivateGameObjectsU3Eb__8_0_mFC5B40FDFBBC993CF6113F7451F6E9041FE34ACE,
	U3CU3Ec_U3CactivateScriptsU3Eb__9_0_m96784A141293B155B63D0B712DF5D157E4B45497,
	RandomColor_Start_mBC02A5377DF1B658FDF67BB79634BCEB80EF668C,
	RandomColor_Update_m160C2752BBEB9027B5492AE7AD73FBD54C6F839E,
	RandomColor__ctor_mB848008AC36078D1B409DE414AFC7366AFF2B45D,
	RandomColor__cctor_m2E546DB611A44CBC71639A16D93E7DFD49EDF971,
	RandomRotator_Start_m11B1016E728F6B495A7E30CB78706FB7B2AB80AD,
	RandomRotator_Update_mC6FAEB79BF508A634B13B07D311588513ECB3C21,
	RandomRotator__ctor_m4E3EB11E5D0D083DECA8155B4D29C8327E94E6D7,
	RandomScaler_Start_m6474CEC8B705AC8972747CA7586F9EDD07E2664E,
	RandomScaler_Update_mD4F58422932A0560E9C709213475C8020BA25A5F,
	RandomScaler__ctor_m35498353C9232FD91815D62E44FBA106843751ED,
	BaseConstants_get_REGEX_LINEENDINGS_m4895158A97275415A9CDA1F4FDB28CFE7BE1C1CC,
	BaseConstants_get_REGEX_EMAIL_m11E13FB104D9F9F23B3F46D6AC0C4B9A69E576F2,
	BaseConstants_get_REGEX_CREDITCARD_m168A0AE6C2E16EBE5973596FCA26F467B3D552A4,
	BaseConstants_get_REGEX_URL_WEB_mA3CDE79C3F9AD941C1E48DC9FA10B82D124F9133,
	BaseConstants_get_REGEX_IP_ADDRESS_m10F4DC2E0133434EBA20EB08ACF864F607E5AC2B,
	BaseConstants_get_REGEX_INVALID_CHARS_mFFFD6DC6EBCF4609CBFCC9982794794AC5A188A6,
	BaseConstants_get_REGEX_ALPHANUMERIC_m9A147D06657BDCD72A1302D5E8FE74920FFBE8EA,
	BaseConstants_get_REGEX_CLEAN_SPACES_mB9EBDBAE7C5E0AC070DC65B04118176B1296F844,
	BaseConstants_get_REGEX_CLEAN_TAGS_mD80739E4DC413929F0187DE4D757F72082D1DD87,
	BaseConstants_get_REGEX_DRIVE_LETTERS_m6ACB12B39FC3809746479C41B97A83486BCFAA79,
	BaseConstants_get_REGEX_FILE_m2986C4CFD5969827CB430D98C0AF5D4782BEEF0C,
	BaseConstants_get_PREFIX_FILE_m779562D3343FE486BDE16A8714EE83845E824FED,
	BaseConstants_get_APPLICATION_PATH_m21E5602DC8ADDBBF22422547AAB4952711A6073A,
	BaseConstants__ctor_m6FB36758DECB7A60CCCB673CBADF04B3E589CB9B,
	BaseConstants__cctor_m3BC60EBE74C95A1515819FB69A801D73BA5D5793,
	BaseHelper_get_BaseCulture_mE9548227E0243318E13BAAE4559EF4BE60CFE063,
	BaseHelper_get_isEditorMode_m93820E6B36D0DC625BB6AF66EB6573BF9545429D,
	BaseHelper_get_isIL2CPP_m5E8DD65CC8EA3B79EFD45063BD7800A21297C4A6,
	BaseHelper_get_CurrentPlatform_m011B7604F1DCAA00DE56FD479329743B95B30AEB,
	BaseHelper_get_AndroidAPILevel_m50D618CB7E5843EC03C529E949FA3972B788B855,
	BaseHelper_get_isWindowsPlatform_mF6650920E73E7618C663F21719213BCA1B5A4547,
	BaseHelper_get_isMacOSPlatform_m7AD99C320FBA37A303B07F60441FC792B455CC18,
	BaseHelper_get_isLinuxPlatform_m24C0E418C4D62D16A1B8036A939E55CB65DDE1B7,
	BaseHelper_get_isStandalonePlatform_mA221CF4663B33F6195DD647FB601B7E08A5E80FF,
	BaseHelper_get_isAndroidPlatform_m60BCD91055339C43D865EF752A7427EE5D9C8497,
	BaseHelper_get_isIOSPlatform_m33E9A93F4FF88F818CE0820AA8881BECC5DDC178,
	BaseHelper_get_isTvOSPlatform_m9B4C1DA5432ABA5C24372B11FFFF79EC448EA832,
	BaseHelper_get_isWSAPlatform_mDA854DBB616C3AD5D86ADC87B870DF2ECBA2898E,
	BaseHelper_get_isXboxOnePlatform_m51652CA1A243014DF9994A96DEA7D5C133C3391E,
	BaseHelper_get_isPS4Platform_m8E1EF945740B3F677125C17B7EFDA9AEDE7822CA,
	BaseHelper_get_isWebGLPlatform_m59C13ECC0AD3202D20F178F208D9EB9E7BCCC5D5,
	BaseHelper_get_isWebPlatform_mF107B2B636CE641CE7DC75A8B3CE7C2797E59276,
	BaseHelper_get_isWindowsBasedPlatform_m6ECB1D7F700D20B24E338A3A846579A203CCD25F,
	BaseHelper_get_isWSABasedPlatform_mBF4CBFC9EDE348991DB9D513533E32ACF2843975,
	BaseHelper_get_isAppleBasedPlatform_m5BA8B256C8C85EE5B7150D22D39D1619385C0ED6,
	BaseHelper_get_isIOSBasedPlatform_mD19DA9D2EA0A9C2A22DF130F598CA5B4EB457910,
	BaseHelper_get_isMobilePlatform_m6B4FD01CAB09C208BED4F4CEC7B00D5839A3F1AA,
	BaseHelper_get_isEditor_m57508F5FC6D0EB0E2BCED1222922C5B68D320366,
	BaseHelper_get_isWindowsEditor_m905DF54FC1AF9310CCF8181FC9A81BF55BAC9908,
	BaseHelper_get_isMacOSEditor_mB498128F3EC26AEDF84A10A10BD11DBB5B80F1A6,
	BaseHelper_get_isLinuxEditor_m7C9E24B618CE8DA56D8D3BE44BDBB994CA992EA5,
	BaseHelper_initialize_m24180A216AC99C4E53360CEE025B5937F45846F2,
	BaseHelper_CreateString_m709214EF42D31EAC474860A1FCA6686E4B3789B3,
	BaseHelper_SplitStringToLines_m9B1E45952E0C80637566E23583FFFE4CC17FEBE9,
	BaseHelper_FormatBytesToHRF_m24850B10A003B532B7969E3B489A4F112046E0DF,
	BaseHelper_FormatSecondsToHRF_m12B5F2AFE7A9547380B8FFF382BE717EBCD2FE4D,
	BaseHelper_HSVToRGB_m4B3F56F11F4FD20FA22674978B18B7E2E246ABC6,
	BaseHelper_GenerateLoremIpsum_mE7177C87CBDA42B4F1408E602B86D9A3AB584B11,
	BaseHelper_LanguageToISO639_mDEEA536D80A59AE7C0F0B6182B7A24F63CBEBF08,
	BaseHelper_ISO639ToLanguage_m9988DAA63DC827726DE04ECF072DD5D941CEB903,
	BaseHelper_InvokeMethod_m84455B77143CAF274883570A1ACF308239C73DB6,
	BaseHelper_GetArgument_mEDEC1E1165EE0FD38AF8A8CAE91C4BB37E17B77D,
	BaseHelper_GetArguments_m3FA2CC168907A333A7F8D8727945A8DF18119E88,
	BaseHelper_ParseJSON_mA68E7226C88F4BD7EF318A4A474D9E26456A1204,
	BaseHelper_addLeadingZero_m62D3D7E8D0FF078D396AB289F7C7C42C8BBC88FA,
	BaseHelper__ctor_m5AD5466ACE6506E28F582A8C2A0677BE2C0F2C8A,
	BaseHelper__cctor_m433B4EBC3FD33028F3FEE16FA7FC4AF3DDA92FC2,
	U3CU3Ec__cctor_m3741615C27DE290FF33E48F48B843444F0913B0B,
	U3CU3Ec__ctor_mA2C9478E04DAD48F8CEBB5AABB02F90956488D81,
	U3CU3Ec_U3CInvokeMethodU3Eb__65_0_m55245EEE357D66278B0AA7476DA66E67BAA45F44,
	CTPlayerPrefs_HasKey_m9AD6D23286FF2023A63253AD5334D4A24C34A9E9,
	CTPlayerPrefs_DeleteAll_mD0E3A067E798064A7D1281B9857B268B7FE4E3AD,
	CTPlayerPrefs_DeleteKey_mACBA36703D8A439D84F8E6B3C57E292BFA4F885B,
	CTPlayerPrefs_Save_m47E2F80CA8FFDF86E1EA511D414C37649A0F2C5F,
	CTPlayerPrefs_GetString_m12727ED53ECCAD1B73F88AC9A7674C885BD30D55,
	CTPlayerPrefs_GetFloat_mEAD28CB2B18BCE46A7EF58EC3F9CE376738B3044,
	CTPlayerPrefs_GetInt_mD647BFA32DEBC1C4ACD48780E39DD07F09B627ED,
	CTPlayerPrefs_GetBool_m4CBF0C9AC39E864E3F6F7D640914F9C662933B4A,
	CTPlayerPrefs_GetDate_m5CE10A4BC2766D240151FC17D4DBDF748A52B44A,
	CTPlayerPrefs_GetVector2_m23926CCD9C81ED0C0EC27D3CCE7C9C3D13F6C19B,
	CTPlayerPrefs_GetVector3_mA8B8E177E44FBD6113248834A76DC6A11ACF7EE7,
	CTPlayerPrefs_GetVector4_m00D1C89760D6AC2CBFF6C47414C526AA2A7F195C,
	CTPlayerPrefs_GetQuaternion_m549402F340532796DC886A6C7027524697098584,
	CTPlayerPrefs_GetColor_m97C3A4BA84133EC3E625406B82E77F730290727C,
	CTPlayerPrefs_GetLanguage_mFDF588F7342529CED0933F3EDDF70BD958928C23,
	CTPlayerPrefs_SetString_mF005A07CA71D1691BBFFCD98D5C8D4D7689B7B6F,
	CTPlayerPrefs_SetFloat_m4C3B4EF17E8209DCC5109FB32DDF217DA7348A1D,
	CTPlayerPrefs_SetInt_m25BEC0CD693AD8BD5790841E2EADB83D9DE7C2CF,
	CTPlayerPrefs_SetBool_m601FDB02E533E25A43763DE0092CC922979EE4FA,
	CTPlayerPrefs_SetDate_m3310C63319E8CDD03056D039B904FA6F04C341B9,
	CTPlayerPrefs_SetVector2_m54A82443A547AD5CDFA9EFA92C14E6E8ED360BD2,
	CTPlayerPrefs_SetVector3_m09ACBFDE30CA4191F773EADC113390F67D1081DF,
	CTPlayerPrefs_SetVector4_m86B9D955BBA3EED4F4F325E4B7CBF5C37B90D290,
	CTPlayerPrefs_SetQuaternion_m8D8ADE8BD164EFDCF05EF6B2B1A22D00B4161F1C,
	CTPlayerPrefs_SetColor_m1BDAFB6A6B0DBBB206A5B3E8D2111159AADD9983,
	CTPlayerPrefs_SetLanguage_m1AB3EEAE2235BAE4C0635553664A2562454BB52C,
	CTPlayerPrefs__ctor_mA0DC3BF75693639A0D5BCA1FBE84E6C75944A6B3,
	CTWebClient_get_Timeout_m29AD981FE9C6DBFB095C22B65424ECDF95E0AD13,
	CTWebClient_set_Timeout_m67E4FCFA6D8A1DFFF08AD167A78FF51FA1305700,
	CTWebClient_get_ConnectionLimit_mCCE99B9E354DF17F195E268CBFB306496CDFD632,
	CTWebClient_set_ConnectionLimit_mEC7194BE6A1FF9248E6ADF2A894797FC9BD47441,
	CTWebClient__ctor_m3E19D61BF5ED3B2D41519909800B27B3035039BD,
	CTWebClient__ctor_m43F26AF90987B5E3C0D2A0788CC189BE70103498,
	CTWebClient_CTGetWebRequest_mBEA02A60043A32644CA65D1DA04FCC225A58F121,
	CTWebClient_GetWebRequest_m6023851CBE1E6047E6406247C66C7092D7460BFE,
	FileHelper_get_StreamingAssetsPath_mDECDD6689B9F2ADF5B46DE2780DCD4340F6AD86E,
	FileHelper_get_ApplicationDataPath_mCE1046DE6B6B76DFA649A27A72FA9B615B7591AA,
	FileHelper_get_ApplicationTempPath_mEF98EA2181BAA7771710EE107E139FC0BE016435,
	FileHelper_get_ApplicationPersistentPath_m6ACBCE3B15052DE1C34542FB75CA7813F09A0A57,
	FileHelper_get_TempFile_m62F38837E8E86916EE766A6067A5B39FFF16DDD6,
	FileHelper_get_TempPath_mC759997ABF565E53649E92D7256B37CAE7EFAE05,
	FileHelper_initialize_m427913F233E067FD9B4B9E1216497F4FCDCCDE2E,
	FileHelper_isUnixPath_m321A823E2734A199165441064BD8E5A5C0A7241E,
	FileHelper_isWindowsPath_m937C9F4646C800091366484F1F28DD82D629023D,
	FileHelper_isUNCPath_m78FA462C8166855A39D499209EA49BDF44A71C63,
	FileHelper_isURL_m4FD2F46E1364281A170172B1C8047FF89DEBF0A4,
	FileHelper_ValidatePath_mACFA02E9B4A8206927F891EAE09CF10AF8D640A2,
	FileHelper_ValidateFile_m5A9FCA99866FF9F85C1E39218D32DB03173CFC73,
	FileHelper_HasPathInvalidChars_mAE898A71FAAF4EABEEB72DF67D51F776D9F9CBB5,
	FileHelper_HasFileInvalidChars_m39A50114F5C237520378FC4868CD6F0D57F22615,
	FileHelper_GetFilesForName_mFED614111399C62FD54A25D61F3EE0BBDD35EC93,
	FileHelper_GetFiles_m1CEB56C25CBA4812A7C97942CEBD59E323555DE5,
	FileHelper_GetDirectories_m6161C09E3578F47138692DE77EEBC6D9BF717B03,
	FileHelper_GetDrives_m1366D5B76C7A5E6F3249180F2A71014B46857AB3,
	FileHelper_CopyDirectory_m0A7CD5EC34752970F3945FF8F6C12B6EECDFEF5B,
	FileHelper_CopyFile_mF4BA88FDC8EF621F84F2C71C50336692D386380E,
	FileHelper_MoveDirectory_m70AF77FA199702A1C21EBCB68BB03B28F767731F,
	FileHelper_MoveFile_m8ED843D6126DD420FA659BCAE64BFEC38E37CEB9,
	FileHelper_RenameDirectory_m51BC586C931EEB34DA795D2EACE087691FB5035B,
	FileHelper_RenameFile_m4B200DC9925B540A6870D61DA367014A3E95C953,
	FileHelper_DeleteFile_mC964F4286E26EA9BEB51C77AC60BBC92F1BD2BC6,
	FileHelper_DeleteDirectory_m6F53630B02E4A7A32E7068FC496ED3204C1E5FED,
	FileHelper_ExistsFile_mDD1EE6FDDA998E11C16D7E78F2FF18D07FE16CC8,
	FileHelper_ExistsDirectory_mE6F80D97C3CE2A893B03D702A14DEA02689F7824,
	FileHelper_CreateDirectory_m536CBD951B36BC4896CAE229B0B2A0FA81748CB1,
	FileHelper_CreateDirectory_m7C8E6DE249262226334AF52948AB676B12C825F7,
	FileHelper_CreateFile_m545C9F8F338478EA3B528BDF96783BD51BA2048E,
	FileHelper_CreateFile_mD8A476FE171BB18DCECA9AE4FF612F412A9559DF,
	FileHelper_isDirectory_m6C974C437BDB81B8E6EFD0F31566A96DF1767687,
	FileHelper_isFile_mCC17C15FE4EBFB924E4E24D0D0203884C3110B48,
	FileHelper_isRoot_m19E2AD9ACE114D8EFAFEB2379E001260547BFE35,
	FileHelper_GetFileName_mEFB6927ADF71FD971C21A962723F784B3D6D05BD,
	FileHelper_GetCurrentDirectoryName_m2E4A4AA7B5C1CEDBACF6F513BC2FA4AEA8FC550D,
	FileHelper_GetDirectoryName_mACC8EF60572CB1BC91590C75435320C2121EFCF0,
	FileHelper_GetFilesize_m0BD380442205C6D5FE0C72864A66B3A9CD98E755,
	FileHelper_GetExtension_m2A5BD174C1C6160AA444CF5DCCFEDCD6BE18828E,
	FileHelper_GetLastModifiedDate_mD1E8E2D558FB822F680148613E9A03C30818BE6A,
	FileHelper_ReadAllText_m1BDC643B71D311559362C2CECA3E9D6384FC39C3,
	FileHelper_ReadAllLines_m36BF56D13E7AE44970C8135333009C570207274B,
	FileHelper_ReadAllBytes_mD1C7B7246C1C19077487660DD3C3221971B76527,
	FileHelper_WriteAllText_mA07DBC392F91AF5E81E20BB538D6D56BCD98C95D,
	FileHelper_WriteAllLines_m4B8B25B85F7D91E2CC5C178B61B0E3E4D6A393AE,
	FileHelper_WriteAllBytes_m372A15AC31B472D9875AA8E95CC610BD948BE86F,
	FileHelper_ShowPath_mBC4F03866FDC199ED3C4057221FC95E633E481DD,
	FileHelper_ShowFolder_mE7989350E32514D784A532669B94C4117B49E302,
	FileHelper_ShowFile_mE6DB795241FC96886C2A6F75252B7D58F2F439BA,
	FileHelper_OpenFile_mFE82392D943CDEF4C18335AD071DD4D8DC075189,
	FileHelper_PathHasInvalidChars_m7616568B94FC4D42F1D6F94EC5866D350B697035,
	FileHelper_FileHasInvalidChars_m87E4554CBF54579F8AA9986888AAB95E60E9EFBD,
	FileHelper_CopyPath_m419260BD6D2C259D4B104FB08F38BB1EFE1E6859,
	FileHelper_MovePath_m54689F38044A61BD7D25C9934B63D4C5CBA28B47,
	FileHelper_copyAll_m6ED688D279B24071F8B2076BA4A6696095098216,
	FileHelper__cctor_mD11FBF5879D14DD231BDCACFF291B94F4C0ECD6C,
	U3CU3Ec__cctor_m6B55495B50BFD63B1A008F7E7E97FB6A56D3A6BE,
	U3CU3Ec__ctor_mF51F1EB3F7E679DFBEB5D83A24FE7C032189CE72,
	U3CU3Ec_U3CGetFilesForNameU3Eb__26_1_m2F439F77BF0EDCA30CB241FE5779B78FBD4093ED,
	U3CU3Ec_U3CGetFilesForNameU3Eb__26_0_mB872B5CAE8BF07B797E7D4859D4BBE0AEA80834C,
	MemoryCacheStream__ctor_m8E0C1F9B38FC1A9D08AA9ACA8E3E4CBF3564083A,
	MemoryCacheStream_get_CanRead_m7D84EBB6415907ECA472FD9FC0A31B339CCC6816,
	MemoryCacheStream_get_CanSeek_mA880179D3CC23475F7A1967AB9C80BA5E489B698,
	MemoryCacheStream_get_CanWrite_mE044619204D0D924CAA670C879ACD2171C7EC805,
	MemoryCacheStream_get_Position_m315433FE042F6245119E590A04A92E77BB15D8EE,
	MemoryCacheStream_set_Position_m8459EB52E1758ABCE8DF1EDCACEE7DE4C2EE425F,
	MemoryCacheStream_get_Length_mB066A2C332DA421165B84E2F015B93D1954AAEF9,
	MemoryCacheStream_Flush_m296440ADDBDD52B4DDF6E0300CCE0B7079373EA3,
	MemoryCacheStream_Seek_mB153AF093A8B6D9B3745648446E1C156C731BA97,
	MemoryCacheStream_SetLength_mB630B9F8ECCDC3B484FA5702B9F953E68B25A35A,
	MemoryCacheStream_Read_m81FC91487491264D770BD407574358935E05214D,
	MemoryCacheStream_Write_m2108334BDDFEBC5BB3043DB6A4A46E93797F675D,
	MemoryCacheStream_read_m17108F23BBFCFE4563E07E77A7324B90ED99E4D2,
	MemoryCacheStream_write_m1F62D98081AB5DA6E5996BEB70F3D78FCB8A627F,
	MemoryCacheStream_createCache_m56DA619544477CBBAA812729BCD43B70209F14D9,
	NetworkHelper_get_isInternetAvailable_mB56ED69D2521A0DBC691EEEACB44A801BBD83FC7,
	NetworkHelper_OpenURL_mD626F648DEBF117489E9D09E4340BCBCF279FAB0,
	NetworkHelper_RemoteCertificateValidationCallback_mDD135E68534F72382893EF6984832F4D394B9B12,
	NetworkHelper_GetURLFromFile_m7D2A5CBE772129EEFF30A8492DCA6BA7AFB327C9,
	NetworkHelper_ValidateURL_m790A6F1803977B049A7B999C180EC72B880B3AED,
	NetworkHelper_isURL_mA0BB19DF9F3D0820F52B6FE7798BFFA3019630E8,
	NetworkHelper_isIPv4_m2450BB793284B254B816B73D280631D6E5F339CF,
	NetworkHelper_GetIP_mD31011B2D78DB42267CAF9E9B5BE94EBB62C5F4F,
	NetworkHelper_ValidURLFromFilePath_m7CD1047F3ADE69A6732309DF7AC5F11DD363702F,
	NetworkHelper_CleanUrl_mC550DC8791D4E46F079E2F9EE88CDC57715F307A,
	NetworkHelper_isValidURL_mEF4AD8F9A4B73DD6B929B4BC6FF416D24F6DD60D,
	NetworkHelper_openURL_m9742A5098EF65CC8E4EF96D10170A2ED2820F901,
	NetworkHelper__ctor_m3A4148FAF2141C2A6A8C7FBD81BFF4AAAABBD720,
	U3CU3Ec__cctor_mBDD92949CB9646A1D1D28C7D7405834685D67C81,
	U3CU3Ec__ctor_m634B8F157D93EF8BE0487FEDCD64AC4D0679CDF9,
	U3CU3Ec_U3CRemoteCertificateValidationCallbackU3Eb__4_0_mF715B54D8898D2D96F30D5FE8A93B90BF5EAB511,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SingletonHelper_get_isQuitting_m1112F111E7850294FC34B43E7A2D498B082A9CF0,
	SingletonHelper_set_isQuitting_mD423D90445BBDD7FF9072CEC350598AAD5DB625A,
	SingletonHelper__cctor_m23B1D46710FE149612CBC68F1C795662433B0556,
	SingletonHelper_initialize_m0E957A50EA220DE7332F3FCDE7E9E59B1D238D69,
	SingletonHelper_onSceneLoaded_mB71D3AB83D2A69B57CDF7C4D488A65D15D63B6E9,
	SingletonHelper_onQuitting_mE1B5B1153F80CDF37C6C076C672ACA8357546151,
	SingletonHelper__ctor_m89942F0B520E1C0BC400413A1670F93BA21FC4B9,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	XmlHelper__ctor_mADBA03B84D780D0E14C297600C1A47BE6A42E57E,
	U3CPrivateImplementationDetailsU3E_ComputeStringHash_mB4C632184660364C404321BFCA05E1ECF45A5047,
};
extern void ExtensionFilter__ctor_m84B8C8B2F6885054D6DDE7E2431B6B12231B792D_AdjustorThunk (void);
extern void ExtensionFilter_ToString_mE7AF0C4F85E9BBF382F2EF65787CBAF3086D6041_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[2] = 
{
	{ 0x0600012F, ExtensionFilter__ctor_m84B8C8B2F6885054D6DDE7E2431B6B12231B792D_AdjustorThunk },
	{ 0x06000130, ExtensionFilter_ToString_mE7AF0C4F85E9BBF382F2EF65787CBAF3086D6041_AdjustorThunk },
};
static const int32_t s_InvokerIndices[755] = 
{
	9105,
	4364,
	8505,
	8505,
	5958,
	7631,
	6370,
	6370,
	6373,
	6373,
	6693,
	6693,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	6370,
	6370,
	6537,
	6537,
	5823,
	7631,
	7631,
	8505,
	7616,
	8505,
	8265,
	8258,
	7631,
	8505,
	8505,
	8505,
	0,
	0,
	8505,
	8505,
	8505,
	8505,
	0,
	7626,
	7626,
	7631,
	7631,
	7631,
	8505,
	0,
	0,
	0,
	0,
	8505,
	8505,
	8505,
	8505,
	0,
	0,
	0,
	8505,
	8491,
	8490,
	8491,
	8490,
	8833,
	8832,
	8851,
	8850,
	7850,
	7870,
	8843,
	8577,
	7386,
	7878,
	8578,
	8260,
	8839,
	8854,
	8838,
	5574,
	5960,
	5574,
	5960,
	7122,
	7983,
	7983,
	7983,
	7983,
	8626,
	8626,
	8626,
	8626,
	8853,
	7992,
	6692,
	0,
	7631,
	0,
	7631,
	0,
	8195,
	7631,
	0,
	8505,
	8505,
	8505,
	8505,
	8505,
	8505,
	8505,
	8505,
	7636,
	8505,
	8505,
	8505,
	8505,
	8505,
	8505,
	8220,
	7963,
	7261,
	7631,
	5942,
	7730,
	9089,
	9089,
	4364,
	2526,
	0,
	0,
	0,
	4364,
	3185,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	3185,
	4364,
	3185,
	4364,
	3051,
	4250,
	3881,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	4250,
	3881,
	4250,
	3881,
	4250,
	4250,
	3881,
	4250,
	3881,
	4250,
	4250,
	4250,
	3881,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	4364,
	4364,
	3518,
	1210,
	1210,
	3518,
	1210,
	1210,
	4250,
	2524,
	4250,
	2524,
	2524,
	1210,
	1210,
	3881,
	1458,
	1458,
	2691,
	964,
	964,
	4364,
	2802,
	3807,
	2077,
	2802,
	1458,
	1458,
	1787,
	1787,
	2518,
	4250,
	6980,
	6980,
	8887,
	8887,
	8887,
	2028,
	621,
	621,
	2788,
	1454,
	2084,
	971,
	971,
	3518,
	3881,
	3881,
	3881,
	3518,
	4364,
	1936,
	4364,
	1936,
	4364,
	2691,
	4364,
	3881,
	2798,
	4364,
	2524,
	3881,
	2798,
	1936,
	754,
	3881,
	2798,
	4364,
	2524,
	3881,
	2798,
	1936,
	754,
	3881,
	2798,
	4364,
	2524,
	3881,
	2798,
	2691,
	1165,
	3881,
	9089,
	4364,
	3511,
	2802,
	4250,
	4364,
	4364,
	4364,
	4250,
	3881,
	4364,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	788,
	1796,
	1210,
	614,
	1440,
	971,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	4250,
	3881,
	1210,
	0,
	2524,
	0,
	0,
	0,
	0,
	0,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	4250,
	3881,
	1210,
	0,
	2524,
	0,
	0,
	0,
	0,
	0,
	4364,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	788,
	1796,
	1210,
	614,
	1440,
	971,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9089,
	4364,
	9089,
	4364,
	9089,
	9089,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	806,
	806,
	4364,
	3852,
	4364,
	4168,
	4250,
	4364,
	4250,
	3852,
	4364,
	4168,
	4250,
	4364,
	4250,
	4364,
	3881,
	3881,
	4364,
	4364,
	3881,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	3852,
	3807,
	3807,
	3807,
	3928,
	3807,
	3928,
	3807,
	3928,
	4364,
	4364,
	4364,
	4364,
	3807,
	3807,
	3928,
	3928,
	3928,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	9089,
	4364,
	3185,
	3185,
	4364,
	4364,
	4364,
	9089,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	4364,
	9089,
	9031,
	8993,
	8993,
	9018,
	9018,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	8993,
	9089,
	7626,
	5927,
	7606,
	8497,
	5745,
	5337,
	8501,
	8399,
	5953,
	8505,
	9031,
	8505,
	8501,
	4364,
	9089,
	9089,
	4364,
	3518,
	8220,
	9089,
	8887,
	9089,
	8505,
	8626,
	8399,
	8220,
	8297,
	8822,
	8838,
	8853,
	8575,
	8258,
	8399,
	7975,
	7983,
	7969,
	7963,
	7966,
	7990,
	7991,
	7992,
	7976,
	7965,
	7969,
	4364,
	4216,
	3852,
	4216,
	3852,
	4364,
	2734,
	3518,
	3518,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9089,
	8220,
	8220,
	8220,
	8220,
	5924,
	7616,
	7254,
	7254,
	6675,
	6675,
	7616,
	9031,
	5693,
	5693,
	7261,
	7261,
	7631,
	7631,
	8220,
	8220,
	8220,
	8220,
	7631,
	8220,
	7631,
	8220,
	7254,
	7254,
	8220,
	7616,
	8505,
	8505,
	8425,
	8505,
	8297,
	7631,
	7631,
	8505,
	6372,
	6372,
	7261,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	6366,
	7261,
	7975,
	9089,
	9089,
	4364,
	3185,
	3518,
	2734,
	4168,
	4168,
	4168,
	4217,
	3853,
	4217,
	4364,
	2467,
	3853,
	1724,
	2053,
	1724,
	2053,
	4364,
	8993,
	8220,
	5701,
	8505,
	5924,
	8220,
	8220,
	8505,
	8505,
	5924,
	8220,
	8887,
	4364,
	9089,
	4364,
	3288,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8993,
	8868,
	9089,
	9089,
	8007,
	9089,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	8787,
};
static const Il2CppTokenRangePair s_rgctxIndices[26] = 
{
	{ 0x02000006, { 90, 5 } },
	{ 0x02000008, { 95, 2 } },
	{ 0x02000009, { 97, 3 } },
	{ 0x0200000A, { 100, 3 } },
	{ 0x02000045, { 103, 12 } },
	{ 0x0600002F, { 0, 2 } },
	{ 0x06000030, { 2, 2 } },
	{ 0x06000035, { 4, 3 } },
	{ 0x0600003C, { 7, 10 } },
	{ 0x0600003D, { 17, 10 } },
	{ 0x0600003E, { 27, 6 } },
	{ 0x0600003F, { 33, 6 } },
	{ 0x06000044, { 39, 10 } },
	{ 0x06000045, { 49, 11 } },
	{ 0x06000046, { 60, 13 } },
	{ 0x0600006B, { 73, 11 } },
	{ 0x0600006D, { 84, 2 } },
	{ 0x0600006F, { 86, 2 } },
	{ 0x06000072, { 88, 2 } },
	{ 0x060002EB, { 115, 2 } },
	{ 0x060002EC, { 117, 2 } },
	{ 0x060002ED, { 119, 2 } },
	{ 0x060002EE, { 121, 2 } },
	{ 0x060002EF, { 123, 2 } },
	{ 0x060002F0, { 125, 2 } },
	{ 0x060002F1, { 127, 2 } },
};
extern const uint32_t g_rgctx_TU5BU5D_tE7144531ED09C1A94D87F0246AB0D26364575F44;
extern const uint32_t g_rgctx_T_t7B1661CD861D4BEC249DCF3A6AA73F017AC1209C;
extern const uint32_t g_rgctx_TU5BU5D_tA3EFBE9953982686B3F33D9ECE306F636DCC4702;
extern const uint32_t g_rgctx_T_tF17F15733E4CEB28AAE48995F429A616BE236978;
extern const uint32_t g_rgctx_TU5BU5D_t20A0DB33FA0E81C9B21D877CB6E038474A21B5C1;
extern const uint32_t g_rgctx_T_t1EB70875D53D80BB9E1B9C03DE03B55D69EF2FAE;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t1EB70875D53D80BB9E1B9C03DE03B55D69EF2FAE_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass58_0_1_t897E0D4321282B43FFC70B5038860FA08FC8082B;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass58_0_1__ctor_m1C45C2958F277DC529FC4836C40E4A040E16DF57;
extern const uint32_t g_rgctx_TU5BU2CU5D_tAAF8F5F81BCE48404E61F7FB76654F3CAF4A312C;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass58_0_1_U3CGetColumnU3Eb__0_mF21CF6E8478F00A058DD09CA079EE4BF5A5B7950;
extern const uint32_t g_rgctx_Func_2_t66F0A15B20E3274BD451713C1CF0B3055698BF0C;
extern const uint32_t g_rgctx_Func_2__ctor_mE5DD8A786AB56FDF05608C6862CD41983A85E6DE;
extern const uint32_t g_rgctx_Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisT_t3DE34081C0FD07C58C84E1004BD0367CAD5DDC6A_m8F0533DAD93DE655C78E5E08AB5B27FE3FB9FEF2;
extern const uint32_t g_rgctx_IEnumerable_1_t6E79BCE94FD49EF9B1C7AE5F9FDBCBDDDF83EA1D;
extern const uint32_t g_rgctx_Enumerable_ToArray_TisT_t3DE34081C0FD07C58C84E1004BD0367CAD5DDC6A_m01867FB489EDAEB5B9007A1D737AF57952784CB6;
extern const uint32_t g_rgctx_TU5BU5D_t1AC45025EA5783076A4F0227386DE0541F83E1CB;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass59_0_1_tFC2E7717A8C2164FABF6B01D8CF91BA42236B3AF;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass59_0_1__ctor_m89D8476F33440034A92F9D17AA0F27020F0B4717;
extern const uint32_t g_rgctx_TU5BU2CU5D_t2AD10AF8A8B2758F76EC7642612C66C590CA7B90;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass59_0_1_U3CGetRowU3Eb__0_m365E777299F80AAFF9268243546CAB69AB783C99;
extern const uint32_t g_rgctx_Func_2_tC0BC082A37E7C0023BA6E82C64CA4964D3D60D0B;
extern const uint32_t g_rgctx_Func_2__ctor_m5F057096B1B5D347E7FE5D5838FA164108050C45;
extern const uint32_t g_rgctx_Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisT_tCDF0AB3B7667E2883D9578C37DAF020893591E3A_mCE8757E80C16EE4044F356E83F35F6FDB9E73267;
extern const uint32_t g_rgctx_IEnumerable_1_t128928191F542875E3FD49C51B2355DBA8FF4998;
extern const uint32_t g_rgctx_Enumerable_ToArray_TisT_tCDF0AB3B7667E2883D9578C37DAF020893591E3A_m2A4DA313925806EDCB54732EBA2BCCA7C9AD03D3;
extern const uint32_t g_rgctx_TU5BU5D_t495B5741F9BEE7835A8F6E2CC89399535140BD88;
extern const uint32_t g_rgctx_IList_1_t7749FF5036DE776876990C7C8DC80865A7F6D6DA;
extern const uint32_t g_rgctx_ICollection_1_t762DD6EC15A6A9BA49FFFADB3D073DCA22644004;
extern const uint32_t g_rgctx_ICollection_1_get_Count_mA2B777CB78B5570B708DE71B38655EB1B4C3745D;
extern const uint32_t g_rgctx_IList_1_get_Item_m194C28C78E5FE0321CE43B54114ABAEFFE0889EE;
extern const uint32_t g_rgctx_T_tDDB9222DDFB4B252817E0942CC33E36DD3676F42;
extern const uint32_t g_rgctx_IList_1_set_Item_mFA269A3E102B31D3B11C24111770ED059EABCCEC;
extern const uint32_t g_rgctx_IList_1_t69E8B31D73ECF1D126D73472D596BBE25D7409A6;
extern const uint32_t g_rgctx_IEnumerable_1_tD67F3A90C68F99D8311315B374122B0B9474ADFD;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_mC89ABB45245AEAEE8F92D833B62397FE99B1CBB7;
extern const uint32_t g_rgctx_IEnumerator_1_tB71CA70F3691A9675CAD2F46C2B3EADCDABEA4A5;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m2105962AF73C01DFFB236B284ACA3FF1D9C27808;
extern const uint32_t g_rgctx_T_t25F15BB1FDE544502B5E517DA9D1C91308903F2D;
extern const uint32_t g_rgctx_IList_1_t54E39A175C5D701D936C517B3433B5F16DA59B27;
extern const uint32_t g_rgctx_ICollection_1_tFC63A4C9790DD35301524C5183EEE60D2B2F630B;
extern const uint32_t g_rgctx_ICollection_1_get_Count_mC045A9AC03B4386DB48B3B602D61AFCBC3562912;
extern const uint32_t g_rgctx_U3CU3Ec__66_1_tA03CD9E893B3551108F94F4FF1BBE37D9385FBA4;
extern const uint32_t g_rgctx_Func_2_t0294AF5582090756C0C52922C55A3BC296501B47;
extern const uint32_t g_rgctx_U3CU3Ec__66_1_tA03CD9E893B3551108F94F4FF1BBE37D9385FBA4;
extern const uint32_t g_rgctx_U3CU3Ec__66_1_U3CCTToStringU3Eb__66_0_m2FAB340A35364CB187AB00C9481BD24B9A278A97;
extern const uint32_t g_rgctx_Func_2__ctor_mC2AEF920E2F324C8C04666CA67BD5B2B27FA1045;
extern const uint32_t g_rgctx_Enumerable_Select_TisT_tE552E3F92AAED1F9FB4A99FF00D29D68BF7208A2_TisString_t_m8925681C3F616A554E88D460FD8DEFA25D5B1F7E;
extern const uint32_t g_rgctx_IEnumerable_1_t325B799B1C54C96D67D0113EA5A0BCC6BA1A123B;
extern const uint32_t g_rgctx_IDictionary_2_tA4EB1CC7CFF14754A13896F6631DF9A8E109C3FC;
extern const uint32_t g_rgctx_IEnumerable_1_tA848EE41A9235F54D13E8616919795185BF15BC7;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m922AFC899BCBD8D9980B8BF47801EC88A4E60D8D;
extern const uint32_t g_rgctx_IEnumerator_1_t18DDA39E6D23945E9989BAD1B8B38D32170D9DF5;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m62793EAAB93D817E853E100106DCEF3004BA4A47;
extern const uint32_t g_rgctx_KeyValuePair_2_t72309AECFCD5A29532FA160877E10C1FBEC827CE;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_mA176CA48EE725C4C51BD1E6EE308BD9CC2FFC09D;
extern const uint32_t g_rgctx_KeyValuePair_2_t72309AECFCD5A29532FA160877E10C1FBEC827CE;
extern const uint32_t g_rgctx_K_t05A7E03A4DBC1D78EE0EA9FE71F6107921E07774;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_mB0A45DF9BD09690531D21A6DB2B938A6ADFE5557;
extern const uint32_t g_rgctx_V_t4118E1CEC738C8C6C8503979E5677AF5A5979CE1;
extern const uint32_t g_rgctx_IDictionary_2_t00A5A7B829FABDEC3C14C41FF94BAE9C1A24047B;
extern const uint32_t g_rgctx_IEnumerable_1_t2C703531A92F4E4DDE0959BE6109E4FFF8B52B2A;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m70766CDAF98FD3A6B9280C64112FAAAB8E10292F;
extern const uint32_t g_rgctx_IEnumerator_1_t4C63F740EDD164744FF0A13DB71F3BBF1831F190;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m1DB3D1C5098F15D768D140F1F5DF8B34F1975CB5;
extern const uint32_t g_rgctx_KeyValuePair_2_t4BD26E729B6230CFEE99DF9666618FC6D1CD9DA6;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_m79CDCC87DA4DDE4C41C255B5DFE8C424EF6E8891;
extern const uint32_t g_rgctx_KeyValuePair_2_t4BD26E729B6230CFEE99DF9666618FC6D1CD9DA6;
extern const uint32_t g_rgctx_K_tABDE9A5B9F2A10C975C2100079DE1BB098EA6820;
extern const uint32_t g_rgctx_IDictionary_2_ContainsKey_m947EC0E28F0CED07E2E46BC45025E847218D2BE9;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_mA0FDAA7A7A0026D68AF37A9A843AF1CD76B9DC95;
extern const uint32_t g_rgctx_V_tF664217BF85491A6C5B4AB9CB44F4581B36311CA;
extern const uint32_t g_rgctx_IDictionary_2_Add_mDDF03C858BB9D220940FE980BC5E3E0B1E74E02E;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass105_0_1_tE32F9DAA226E637A908A18AE411EBA0FFB76EFCD;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass105_0_1__ctor_m3D8D8F2D035982C9AE17C2DBF4638F4139042DBD;
extern const uint32_t g_rgctx_Component_GetComponentsInChildren_TisT_tA2A9CABBE1C161AA05B5B573D4700B36624C9BF2_m6A05D49DC6DD08A8A52DF096C24916B4CEDCC830;
extern const uint32_t g_rgctx_TU5BU5D_t7781B9D361516B81126B8DA869E5DE16E6B40A1D;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass105_0_1_U3CCTFindAllU3Eb__0_m13707439470BAF1AE1D06C74C6C72EF1E4B79FDB;
extern const uint32_t g_rgctx_Func_2_tF817F3477F9BDE6ADDDB8B4313DB8CD59B9E1598;
extern const uint32_t g_rgctx_Func_2__ctor_m5A8FDA5E997A624466774803B0CDAE4518F06CDE;
extern const uint32_t g_rgctx_Enumerable_Where_TisT_tA2A9CABBE1C161AA05B5B573D4700B36624C9BF2_m67B0344B75958BDD0CBFDB74ECF5B04E5EAE8D33;
extern const uint32_t g_rgctx_IEnumerable_1_t9B16D7080CDB02FBDB0BCB558D291B8A4D52AC59;
extern const uint32_t g_rgctx_Enumerable_ToList_TisT_tA2A9CABBE1C161AA05B5B573D4700B36624C9BF2_mFBEF78B8BE694A786003913D82EF1A03561B7F77;
extern const uint32_t g_rgctx_List_1_tBD06887103099B718BDBFA7CA990B844A65F0E27;
extern const uint32_t g_rgctx_ExtensionMethods_CTFind_TisT_t72AE9FBB42E1D4CC6C6936C2E9310C3464FE0B4B_mE6EF8E4F44D0411F58C05CF00620D98256A8A863;
extern const uint32_t g_rgctx_T_t72AE9FBB42E1D4CC6C6936C2E9310C3464FE0B4B;
extern const uint32_t g_rgctx_ExtensionMethods_CTFind_TisT_tD9A65E6EC2FD03B5530C1D3CDB67C539CB516429_m7D0E18DCEA2D8E5788F58ADACFA73654EB2CBE7B;
extern const uint32_t g_rgctx_T_tD9A65E6EC2FD03B5530C1D3CDB67C539CB516429;
extern const uint32_t g_rgctx_T_t44878F6023CCA570E60635FDBAEB1269FB01526A;
extern const uint32_t g_rgctx_GameObject_GetComponent_TisT_t44878F6023CCA570E60635FDBAEB1269FB01526A_m4F4699E81CB3598C8EAC6F1BA71D72CA5C14C82A;
extern const uint32_t g_rgctx_U3CU3Ec__66_1_t79F151B20559D685A2E9FFB3385A3243678B80B4;
extern const uint32_t g_rgctx_U3CU3Ec__66_1__ctor_m21A5AD9C0CC95BAA305A99AC2D66176D1A7D0743;
extern const uint32_t g_rgctx_U3CU3Ec__66_1_t79F151B20559D685A2E9FFB3385A3243678B80B4;
extern const uint32_t g_rgctx_T_t6CD15D01737F134D140C4E53275ECC5C7D170ADA;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t6CD15D01737F134D140C4E53275ECC5C7D170ADA_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F;
extern const uint32_t g_rgctx_T_tF455E5D55D97B7B1E98F889051E5FCC346A3330E;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass105_0_1_t584639D3756CAB78B36D133218145B56B2890026;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass58_0_1_t16E0DBE6B68E6B56C601CD01BDFC6965A03283E6;
extern const uint32_t g_rgctx_TU5BU2CU5D_tDC8B9C9707BC872B843BF8992113FB0785E9A571;
extern const uint32_t g_rgctx_T_t3943A77A48CAF76F185D2265C9329FA014B9931D;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass59_0_1_tC366106156F5D147C3AECB8E77382BDE296CD45C;
extern const uint32_t g_rgctx_TU5BU2CU5D_t2FC41E0C71B3788D7DF2DCE5C04D5DAD3F01012E;
extern const uint32_t g_rgctx_T_tF48F894EE07191D47DB66C9D4CF85FD936BA47E1;
extern const uint32_t g_rgctx_T_t3E747912A6ABA118483EF29012F034AEA4361EE1;
extern const uint32_t g_rgctx_Singleton_1_tE564D4796FB759320AEE3354586572E71094270C;
extern const uint32_t g_rgctx_T_t3E747912A6ABA118483EF29012F034AEA4361EE1;
extern const uint32_t g_rgctx_Singleton_1_tE564D4796FB759320AEE3354586572E71094270C;
extern const uint32_t g_rgctx_Singleton_1_CreateInstance_mBCC3FA3543C8F2FD5C50916081D185DB2757975D;
extern const uint32_t g_rgctx_Component_GetComponent_TisT_t3E747912A6ABA118483EF29012F034AEA4361EE1_mE303627DE3DD4C341F7D88F238180E4D6739279F;
extern const uint32_t g_rgctx_Singleton_1_set_Instance_mDF3AF2FDE0595D488B9B6B6A1A9BC153EF73B25B;
extern const uint32_t g_rgctx_Singleton_1_DeleteInstance_m467C653B0317FFA2C1768CA725D4C869793FD824;
extern const uint32_t g_rgctx_Resources_Load_TisT_t3E747912A6ABA118483EF29012F034AEA4361EE1_mDEFBC94719B9BD32AD232C5BC296977E5EEA147C;
extern const uint32_t g_rgctx_Object_Instantiate_TisT_t3E747912A6ABA118483EF29012F034AEA4361EE1_m0A5D3AC503B860E3D644463648F76FA53F5F31E8;
extern const uint32_t g_rgctx_Singleton_1_get_Instance_mBF1F1BCDD73B0197163AFF086B1C68ED67281444;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_t3E747912A6ABA118483EF29012F034AEA4361EE1_mDE91D523526F1C293FD12F1AC7650F02F199E29A;
extern const uint32_t g_rgctx_T_tB2C18C56F439F66CC8D8C04C52E8C0F71D2C88A8;
extern const uint32_t g_rgctx_XmlHelper_SerializeToString_TisT_tB2C18C56F439F66CC8D8C04C52E8C0F71D2C88A8_mA1E285A7A2EFFF024427453ACED173B15C41488F;
extern const uint32_t g_rgctx_T_t14B651CD1EFCBD2D73540D09B307F2F0F32381AB;
extern const uint32_t g_rgctx_XmlHelper_SerializeToByteArray_TisT_t14B651CD1EFCBD2D73540D09B307F2F0F32381AB_m8B5152786A2E8682D3ABA06E54BA7977C4E0427B;
extern const uint32_t g_rgctx_T_tFE9DD0709FEF65C9D8C953C4FB2D17610A4FA4AD;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tFE9DD0709FEF65C9D8C953C4FB2D17610A4FA4AD_Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3;
extern const uint32_t g_rgctx_XmlHelper_DeserializeFromString_TisT_t1C1E7F6CC3D9D9A2D77F7822D73D5CB0EAD0EDE3_mA0DD9DBBF73D6F1CAB8A10A727359A2E3285DDFF;
extern const uint32_t g_rgctx_T_t1C1E7F6CC3D9D9A2D77F7822D73D5CB0EAD0EDE3;
extern const uint32_t g_rgctx_T_tFFD946D8AD1F8E4484ED4FBE3C9B5882396C72F0;
extern const uint32_t g_rgctx_T_tFFD946D8AD1F8E4484ED4FBE3C9B5882396C72F0;
extern const uint32_t g_rgctx_T_t2AD9B81D6A3A8A0D5B94B281AC7F955D62B27868;
extern const uint32_t g_rgctx_T_t2AD9B81D6A3A8A0D5B94B281AC7F955D62B27868;
extern const uint32_t g_rgctx_T_tA306A02BD60F4C2188DFDC79E8CA1CBE6B821FCA;
extern const uint32_t g_rgctx_XmlHelper_DeserializeFromString_TisT_tA306A02BD60F4C2188DFDC79E8CA1CBE6B821FCA_mFDE2926ED93E4EF09BDED4A8AA70DC58B99B4D3A;
static const Il2CppRGCTXDefinition s_rgctxValues[129] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tE7144531ED09C1A94D87F0246AB0D26364575F44 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7B1661CD861D4BEC249DCF3A6AA73F017AC1209C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tA3EFBE9953982686B3F33D9ECE306F636DCC4702 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF17F15733E4CEB28AAE48995F429A616BE236978 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t20A0DB33FA0E81C9B21D877CB6E038474A21B5C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1EB70875D53D80BB9E1B9C03DE03B55D69EF2FAE },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t1EB70875D53D80BB9E1B9C03DE03B55D69EF2FAE_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass58_0_1_t897E0D4321282B43FFC70B5038860FA08FC8082B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass58_0_1__ctor_m1C45C2958F277DC529FC4836C40E4A040E16DF57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU2CU5D_tAAF8F5F81BCE48404E61F7FB76654F3CAF4A312C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass58_0_1_U3CGetColumnU3Eb__0_mF21CF6E8478F00A058DD09CA079EE4BF5A5B7950 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t66F0A15B20E3274BD451713C1CF0B3055698BF0C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_mE5DD8A786AB56FDF05608C6862CD41983A85E6DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisT_t3DE34081C0FD07C58C84E1004BD0367CAD5DDC6A_m8F0533DAD93DE655C78E5E08AB5B27FE3FB9FEF2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t6E79BCE94FD49EF9B1C7AE5F9FDBCBDDDF83EA1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_ToArray_TisT_t3DE34081C0FD07C58C84E1004BD0367CAD5DDC6A_m01867FB489EDAEB5B9007A1D737AF57952784CB6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t1AC45025EA5783076A4F0227386DE0541F83E1CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass59_0_1_tFC2E7717A8C2164FABF6B01D8CF91BA42236B3AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass59_0_1__ctor_m89D8476F33440034A92F9D17AA0F27020F0B4717 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU2CU5D_t2AD10AF8A8B2758F76EC7642612C66C590CA7B90 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass59_0_1_U3CGetRowU3Eb__0_m365E777299F80AAFF9268243546CAB69AB783C99 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tC0BC082A37E7C0023BA6E82C64CA4964D3D60D0B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m5F057096B1B5D347E7FE5D5838FA164108050C45 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisT_tCDF0AB3B7667E2883D9578C37DAF020893591E3A_mCE8757E80C16EE4044F356E83F35F6FDB9E73267 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t128928191F542875E3FD49C51B2355DBA8FF4998 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_ToArray_TisT_tCDF0AB3B7667E2883D9578C37DAF020893591E3A_m2A4DA313925806EDCB54732EBA2BCCA7C9AD03D3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t495B5741F9BEE7835A8F6E2CC89399535140BD88 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_t7749FF5036DE776876990C7C8DC80865A7F6D6DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t762DD6EC15A6A9BA49FFFADB3D073DCA22644004 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_mA2B777CB78B5570B708DE71B38655EB1B4C3745D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IList_1_get_Item_m194C28C78E5FE0321CE43B54114ABAEFFE0889EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDDB9222DDFB4B252817E0942CC33E36DD3676F42 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IList_1_set_Item_mFA269A3E102B31D3B11C24111770ED059EABCCEC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_t69E8B31D73ECF1D126D73472D596BBE25D7409A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tD67F3A90C68F99D8311315B374122B0B9474ADFD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_mC89ABB45245AEAEE8F92D833B62397FE99B1CBB7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tB71CA70F3691A9675CAD2F46C2B3EADCDABEA4A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m2105962AF73C01DFFB236B284ACA3FF1D9C27808 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t25F15BB1FDE544502B5E517DA9D1C91308903F2D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_t54E39A175C5D701D936C517B3433B5F16DA59B27 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_tFC63A4C9790DD35301524C5183EEE60D2B2F630B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_mC045A9AC03B4386DB48B3B602D61AFCBC3562912 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__66_1_tA03CD9E893B3551108F94F4FF1BBE37D9385FBA4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t0294AF5582090756C0C52922C55A3BC296501B47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__66_1_tA03CD9E893B3551108F94F4FF1BBE37D9385FBA4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__66_1_U3CCTToStringU3Eb__66_0_m2FAB340A35364CB187AB00C9481BD24B9A278A97 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_mC2AEF920E2F324C8C04666CA67BD5B2B27FA1045 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Select_TisT_tE552E3F92AAED1F9FB4A99FF00D29D68BF7208A2_TisString_t_m8925681C3F616A554E88D460FD8DEFA25D5B1F7E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t325B799B1C54C96D67D0113EA5A0BCC6BA1A123B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IDictionary_2_tA4EB1CC7CFF14754A13896F6631DF9A8E109C3FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tA848EE41A9235F54D13E8616919795185BF15BC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m922AFC899BCBD8D9980B8BF47801EC88A4E60D8D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t18DDA39E6D23945E9989BAD1B8B38D32170D9DF5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m62793EAAB93D817E853E100106DCEF3004BA4A47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t72309AECFCD5A29532FA160877E10C1FBEC827CE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_mA176CA48EE725C4C51BD1E6EE308BD9CC2FFC09D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t72309AECFCD5A29532FA160877E10C1FBEC827CE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_K_t05A7E03A4DBC1D78EE0EA9FE71F6107921E07774 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_mB0A45DF9BD09690531D21A6DB2B938A6ADFE5557 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_V_t4118E1CEC738C8C6C8503979E5677AF5A5979CE1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IDictionary_2_t00A5A7B829FABDEC3C14C41FF94BAE9C1A24047B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t2C703531A92F4E4DDE0959BE6109E4FFF8B52B2A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m70766CDAF98FD3A6B9280C64112FAAAB8E10292F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t4C63F740EDD164744FF0A13DB71F3BBF1831F190 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m1DB3D1C5098F15D768D140F1F5DF8B34F1975CB5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t4BD26E729B6230CFEE99DF9666618FC6D1CD9DA6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_m79CDCC87DA4DDE4C41C255B5DFE8C424EF6E8891 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t4BD26E729B6230CFEE99DF9666618FC6D1CD9DA6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_K_tABDE9A5B9F2A10C975C2100079DE1BB098EA6820 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_ContainsKey_m947EC0E28F0CED07E2E46BC45025E847218D2BE9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_mA0FDAA7A7A0026D68AF37A9A843AF1CD76B9DC95 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_V_tF664217BF85491A6C5B4AB9CB44F4581B36311CA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_Add_mDDF03C858BB9D220940FE980BC5E3E0B1E74E02E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass105_0_1_tE32F9DAA226E637A908A18AE411EBA0FFB76EFCD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass105_0_1__ctor_m3D8D8F2D035982C9AE17C2DBF4638F4139042DBD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponentsInChildren_TisT_tA2A9CABBE1C161AA05B5B573D4700B36624C9BF2_m6A05D49DC6DD08A8A52DF096C24916B4CEDCC830 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t7781B9D361516B81126B8DA869E5DE16E6B40A1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass105_0_1_U3CCTFindAllU3Eb__0_m13707439470BAF1AE1D06C74C6C72EF1E4B79FDB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tF817F3477F9BDE6ADDDB8B4313DB8CD59B9E1598 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m5A8FDA5E997A624466774803B0CDAE4518F06CDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Where_TisT_tA2A9CABBE1C161AA05B5B573D4700B36624C9BF2_m67B0344B75958BDD0CBFDB74ECF5B04E5EAE8D33 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t9B16D7080CDB02FBDB0BCB558D291B8A4D52AC59 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_ToList_TisT_tA2A9CABBE1C161AA05B5B573D4700B36624C9BF2_mFBEF78B8BE694A786003913D82EF1A03561B7F77 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tBD06887103099B718BDBFA7CA990B844A65F0E27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExtensionMethods_CTFind_TisT_t72AE9FBB42E1D4CC6C6936C2E9310C3464FE0B4B_mE6EF8E4F44D0411F58C05CF00620D98256A8A863 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t72AE9FBB42E1D4CC6C6936C2E9310C3464FE0B4B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExtensionMethods_CTFind_TisT_tD9A65E6EC2FD03B5530C1D3CDB67C539CB516429_m7D0E18DCEA2D8E5788F58ADACFA73654EB2CBE7B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD9A65E6EC2FD03B5530C1D3CDB67C539CB516429 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t44878F6023CCA570E60635FDBAEB1269FB01526A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponent_TisT_t44878F6023CCA570E60635FDBAEB1269FB01526A_m4F4699E81CB3598C8EAC6F1BA71D72CA5C14C82A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__66_1_t79F151B20559D685A2E9FFB3385A3243678B80B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__66_1__ctor_m21A5AD9C0CC95BAA305A99AC2D66176D1A7D0743 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__66_1_t79F151B20559D685A2E9FFB3385A3243678B80B4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6CD15D01737F134D140C4E53275ECC5C7D170ADA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t6CD15D01737F134D140C4E53275ECC5C7D170ADA_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF455E5D55D97B7B1E98F889051E5FCC346A3330E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass105_0_1_t584639D3756CAB78B36D133218145B56B2890026 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass58_0_1_t16E0DBE6B68E6B56C601CD01BDFC6965A03283E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU2CU5D_tDC8B9C9707BC872B843BF8992113FB0785E9A571 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3943A77A48CAF76F185D2265C9329FA014B9931D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass59_0_1_tC366106156F5D147C3AECB8E77382BDE296CD45C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU2CU5D_t2FC41E0C71B3788D7DF2DCE5C04D5DAD3F01012E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF48F894EE07191D47DB66C9D4CF85FD936BA47E1 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t3E747912A6ABA118483EF29012F034AEA4361EE1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Singleton_1_tE564D4796FB759320AEE3354586572E71094270C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3E747912A6ABA118483EF29012F034AEA4361EE1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Singleton_1_tE564D4796FB759320AEE3354586572E71094270C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_CreateInstance_mBCC3FA3543C8F2FD5C50916081D185DB2757975D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisT_t3E747912A6ABA118483EF29012F034AEA4361EE1_mE303627DE3DD4C341F7D88F238180E4D6739279F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_set_Instance_mDF3AF2FDE0595D488B9B6B6A1A9BC153EF73B25B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_DeleteInstance_m467C653B0317FFA2C1768CA725D4C869793FD824 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Resources_Load_TisT_t3E747912A6ABA118483EF29012F034AEA4361EE1_mDEFBC94719B9BD32AD232C5BC296977E5EEA147C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_Instantiate_TisT_t3E747912A6ABA118483EF29012F034AEA4361EE1_m0A5D3AC503B860E3D644463648F76FA53F5F31E8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_get_Instance_mBF1F1BCDD73B0197163AFF086B1C68ED67281444 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_t3E747912A6ABA118483EF29012F034AEA4361EE1_mDE91D523526F1C293FD12F1AC7650F02F199E29A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB2C18C56F439F66CC8D8C04C52E8C0F71D2C88A8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XmlHelper_SerializeToString_TisT_tB2C18C56F439F66CC8D8C04C52E8C0F71D2C88A8_mA1E285A7A2EFFF024427453ACED173B15C41488F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t14B651CD1EFCBD2D73540D09B307F2F0F32381AB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XmlHelper_SerializeToByteArray_TisT_t14B651CD1EFCBD2D73540D09B307F2F0F32381AB_m8B5152786A2E8682D3ABA06E54BA7977C4E0427B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFE9DD0709FEF65C9D8C953C4FB2D17610A4FA4AD },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tFE9DD0709FEF65C9D8C953C4FB2D17610A4FA4AD_Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XmlHelper_DeserializeFromString_TisT_t1C1E7F6CC3D9D9A2D77F7822D73D5CB0EAD0EDE3_mA0DD9DBBF73D6F1CAB8A10A727359A2E3285DDFF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1C1E7F6CC3D9D9A2D77F7822D73D5CB0EAD0EDE3 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tFFD946D8AD1F8E4484ED4FBE3C9B5882396C72F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFFD946D8AD1F8E4484ED4FBE3C9B5882396C72F0 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t2AD9B81D6A3A8A0D5B94B281AC7F955D62B27868 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2AD9B81D6A3A8A0D5B94B281AC7F955D62B27868 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA306A02BD60F4C2188DFDC79E8CA1CBE6B821FCA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XmlHelper_DeserializeFromString_TisT_tA306A02BD60F4C2188DFDC79E8CA1CBE6B821FCA_mFDE2926ED93E4EF09BDED4A8AA70DC58B99B4D3A },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationAssemblyU2DCSharpU2Dfirstpass;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharpU2Dfirstpass_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharpU2Dfirstpass_CodeGenModule = 
{
	"Assembly-CSharp-firstpass.dll",
	755,
	s_methodPointers,
	2,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	26,
	s_rgctxIndices,
	129,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationAssemblyU2DCSharpU2Dfirstpass,
	NULL,
	NULL,
	NULL,
	NULL,
};
