﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

struct Action_1_tA5EFEB887DFB52B21189CCF158EFA7BEA6E91D0A;
struct Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6;
struct Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99;
struct Action_2_tA1976F05055FFB43DE1C61397E93BBBFCD77E97E;
struct CircularBuffer_1_t6F1B957F4957913743FE1DFC2F4F0B05E471128A;
struct Comparison_1_t9FCAC8C8CE160A96C5AAD2DE1D353DCE8A2FEEFC;
struct Dictionary_2_tD2A01FF7682654DF38C267D638BAB8AE0491A568;
struct DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4;
struct DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA;
struct DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF;
struct DynamicCircularBuffer_1_tBF936640484CF43115B5EB2600C146DACC75D965;
struct DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669;
struct List_1_tA5BDE435C735A082941CD33D212F97F4AE9FA55F;
struct List_1_t77A2CF97AA1B05C81783D84584405B0B9A8484F4;
struct List_1_tF2FE88545EFEC788CAAE6C74EC2F78E937FCCAC3;
struct List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B;
struct List_1_tE6BB71ABF15905EFA2BE92C38A2716547AEADB19;
struct List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73;
struct List_1_tF2BD894424997346355BE3D3F0A728DBF10DCF48;
struct Predicate_1_t2D3DB1B2F8687CD481B0FE1CD42006398EFAB34E;
struct Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC;
struct Predicate_1_t81499D2838AC2641B3FA14CD4DBF7E1594A9E107;
struct Stack_1_t5DB813F9C669005201F1DE46996A668CCA70397C;
struct Stack_1_t45460CCBC43223EC301AD4CD87BD53CCA2F47FC8;
struct TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4;
struct UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C;
struct UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669;
struct UnityAction_1_tC5C168260FDBBEDACD1D1996850C58AA3E9C2259;
struct UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A;
struct UnityAction_2_t742C43FA6EAABE0458C753DFE15FDDFAE01EA73F;
struct UnityEvent_1_t9A868DD8EBFC0D9D8134D903A170ECBDEE567932;
struct UnityEvent_1_t2A2840A12C919146C6D21E86D7DAA8E3DAB74030;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DebugLogEntryU5BU5D_tD7A340701CFF6219ED046F42D1A4B726AFBB6192;
struct DebugLogEntryTimestampU5BU5D_t8E5BB7892CC61C2FD0065B78005D54E21811CDA8;
struct DebugLogItemU5BU5D_t19E47B1D4545D24FB08E77DD70D765296117597E;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
struct __Il2CppFullySharedGenericTypeU5BU5D_tCAB6D060972DD49223A834B7EEFEB9FE2D003BEC;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C;
struct BaseEventData_tE03A848325C0AE8E76C6CA15FD86395EBF83364F;
struct BaseInputModule_tF3B7C22AF1419B2AC9ECE6589357DC1B88ED96B1;
struct BaseRaycaster_t7DC8158FD3CA0193455344379DD5FF7CD5F1F832;
struct Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26;
struct CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094;
struct CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860;
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
struct DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD;
struct DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230;
struct DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8;
struct DebugLogPopup_t521468752FCF53CE80E2E8989D7A42A10D8E216B;
struct DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2;
struct DebugLogResizeListener_tAEB6FDAE1D37AD856CBD82204674E6AC4C1764B5;
struct DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707;
struct EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E;
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F;
struct IAsyncResult_t7B9B5A0ECB35DCEC31B8A8122C37D687369253B5;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA;
struct Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E;
struct InputField_tABEA115F23FBD374EBE80D4FAC1D15BD6E37A140;
struct InputFieldWarningsFixer_tE2789F5BD5EE03FE010196301F77663D483ABC90;
struct InvokableCallList_t309E1C8C7CE885A0D2F98C84CEA77A8935688382;
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3;
struct Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4;
struct MethodInfo_t;
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71;
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct PersistentCallGroup_tB826EDF15DC80F71BCBCD8E410FD959A04C33F25;
struct PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB;
struct RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670;
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E;
struct Scrollbar_t7CDC9B956698D9385A11E4C12964CD51477072C3;
struct Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99;
struct String_t;
struct StringBuilder_t;
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62;
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4;
struct UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7;
struct VertexHelper_tB905FCB02AE67CBEE5F265FE37A5938FC5D136FE;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7;
struct CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8;
struct ReapplyDrivenProperties_t3482EA130A01FF7EE2EEFE37F66A5215D08CFE24;
struct ScrollRectEvent_t812C011901E6101F2A0FFC34C66AC5F65C0DEC26;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsIngameDebugConsole_Runtime[];
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_CoreModule[];
IL2CPP_EXTERN_C RuntimeClass* Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponentInParent_TisScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E_mD5D105BD6034A3131A91D7B6534D7E74834D72AD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogManager_PoolLogItem_m50869AD2E15FB447682B47A83D7B06DED349B736_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_Awake_mB17543BD466BD4A736547268AAC2680CC75BC5B3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_CalculateSelectedLogEntryHeight_mE78B718B6A980A95F3E9D538AB67D6075F253D44_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_ColorLogItem_mC1A80473D95AFCE3DC87E38DCABEB90096C1D2A7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_DeselectSelectedLogItem_m04DC7746AB16AF9D1B7E9D9747ACA6B70787BF63_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_FindIndexOfLogEntryInReverseDirection_m2638A20C51B02F596001C11B22890147D26D7B27_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_GetLogItemAtIndex_m701E9237DDEC23011F430F2B47D52B573DC34BAD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_Initialize_m9AB919674122BE9855B08B8DC0E6BA736DFAAAD2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_OnCollapsedLogEntryAtIndexUpdated_m83CA66274FC922C662D9A56A137F308EC39F1DDB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_OnLogEntriesRemoved_m76BF83BC5683B794E130C669C9089505FD8A6437_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_OnLogEntriesUpdated_m31B75B0BEFE49F0A6C735132A24575ED537259F1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_OnLogItemClickedInternal_m9C3AA7CA2C2C65BC15F72C0AA4AA9310F724707E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_OnLogItemClicked_m884D345CF8F6ACB003D53075D9A8B6F8B4BB19B2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_OnViewportHeightChanged_mC257005C0AC4AAB23CAB212ABC2B3CD3311E2484_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_OnViewportWidthChanged_mC15FBC039660C721E52590BFA3E6A08320AF6C4D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_RefreshCollapsedLogEntryCounts_m9DD4F6E26D5AD4913B7D572BDC6C1A0A1712241D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_RepositionLogItem_m6A7A36C93FBA0C44560F9D9556C343F5EA052072_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_SelectAndFocusOnLogItemAtIndex_m76E2D97A52AD52976B1A9ACCEBDC1253B9A07BC9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_SetCollapseMode_m8B9F13CBAB315B685DBFBFFBD9FF546DC3AEAFC2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_ShouldRemoveLogItem_m88B27AD516FEC0744C63EA1AE7ECDD4B4C086124_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_U3CAwakeU3Eb__25_0_m46F3DED084709344721E369E0D3AC052735594A5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView__ctor_m187BC9A3FCE5156FCDA97401BF5431EB4BAC164A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_get_ItemHeight_m4FBFACEF0C31D99C4745209391D410234EAE6A80_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogRecycledListView_get_SelectedItemHeight_m55C399776D3C681426A1E7BB96D17799AAF17358_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogResizeListener_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mA23891E13947BF5553F8E9463958B5733DCDAEAF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogResizeListener_UnityEngine_EventSystems_IDragHandler_OnDrag_m34D99B83889FBD661DE942A7C59522A8ABB0AD3A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugLogResizeListener__ctor_m9891E2D11D53F3900EB79C88301C937AD4FB8B33_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugsOnScrollListener_OnBeginDrag_mDA693E6910C47EFFFB1C9E749BACF843BE7680DB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugsOnScrollListener_OnEndDrag_m73CCC8AC42E0FC185F490DE8829D51554E59968E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugsOnScrollListener_OnScroll_m12A82295EEFBA395210BFDF1C80BA59B7438D8F2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugsOnScrollListener_OnScrollbarDragEnd_mB867BC08242DE9B5EDD968AB6A01DBA5F96626F9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugsOnScrollListener_OnScrollbarDragStart_mC24D5BD9F61D0BC49F91808A5D9B20E59A13D30E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DebugsOnScrollListener__ctor_mC599E1B72DE41CF49476C9F2C121DDAE4B9F4B1F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_AddFirst_m75C4229800AC7F9A99A7781E3DAC12B2FD0C75B3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_Add_mB7CBE0E63E213E436C2F09949491E47182AB0DEE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_RemoveAll_mCC751454842A18019068E7B7D9C1A00CED8BF54F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_TrimEnd_mFB28BA268BCFAFE28246145B6EA96820BFAE3BA4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1__ctor_mBE7966474BC269763705CA36F37661885024A65A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventSystemHandler_ActivateEventSystemIfNeeded_m162A2AECC9C9CB151849DD89B25485FAFC30313B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventSystemHandler_OnDisable_mEDA7BEC448D249FC070F53363E935967AF5FB97F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventSystemHandler_OnEnable_mBE644BBFCA0529C37CB7C4BDEAA166858EF1545E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventSystemHandler__ctor_m2FD41F48C7ED26C15E12B40EDFBAAC64C9A40448_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InputFieldWarningsFixer__ctor_mA0352FB9DB9785DF7FBDE9CD4E1D2A94718F9C6C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Mathf_Max_m7FA442918DE37E3A00106D1F2E789D65829792B8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1__ctor_m745BF2FAC35C508F889804F26D66321FE3785685_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CMoveToPosAnimationU3Ed__25_MoveNext_mEB365A0C3236ACF671867DD1EFAA97C531BFEA59_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CMoveToPosAnimationU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1EB8FC262CCF5BA2744BE265E81C2FE5362E2C59_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_Reset_m27A880D1F617C814C12D25EC9516E99F05D8B24D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_get_Current_m0D3D86F36CF20F84ADDB433D3D1101DB84DECD15_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CMoveToPosAnimationU3Ed__25_System_IDisposable_Dispose_m6B852D6BCF4CEDF266A8A62352A62334072D12C1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CMoveToPosAnimationU3Ed__25__ctor_mC8A1388E86D07D17D4D9B6402EA77B701899C169_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UnityEvent_1_AddListener_m2B74313C91E347D6AD24CE5B036E190E77E70851_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector2_Lerp_m1A36103F7967F653A929556E26E6D052C298C00C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* DebugLogResizeListener_tAEB6FDAE1D37AD856CBD82204674E6AC4C1764B5_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* DynamicCircularBuffer_1_tB13F054EA429529FE1B22D4E3992D968A9379DDF_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4  : public RuntimeObject
{
	DebugLogEntryU5BU5D_tD7A340701CFF6219ED046F42D1A4B726AFBB6192* ___array;
	int32_t ___startIndex;
	int32_t ___U3CCountU3Ek__BackingField;
};
struct DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA  : public RuntimeObject
{
	DebugLogEntryTimestampU5BU5D_t8E5BB7892CC61C2FD0065B78005D54E21811CDA8* ___array;
	int32_t ___startIndex;
	int32_t ___U3CCountU3Ek__BackingField;
};
struct DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF  : public RuntimeObject
{
	DebugLogItemU5BU5D_t19E47B1D4545D24FB08E77DD70D765296117597E* ___array;
	int32_t ___startIndex;
	int32_t ___U3CCountU3Ek__BackingField;
};
struct DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669  : public RuntimeObject
{
	__Il2CppFullySharedGenericTypeU5BU5D_tCAB6D060972DD49223A834B7EEFEB9FE2D003BEC* ___array;
	int32_t ___startIndex;
	int32_t ___U3CCountU3Ek__BackingField;
};
struct U3CPrivateImplementationDetailsU3E_t1ECF1A9E3F556408EC04BFACA61EAE2314C16DF1  : public RuntimeObject
{
};
struct AbstractEventData_tAE1A127ED657117548181D29FFE4B1B14D8E67F7  : public RuntimeObject
{
	bool ___m_Used;
};
struct DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD  : public RuntimeObject
{
	String_t* ___logString;
	String_t* ___stackTrace;
	String_t* ___completeLog;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___logTypeSpriteRepresentation;
	int32_t ___count;
	int32_t ___collapsedIndex;
	int32_t ___hashValue;
};
struct UnityEventBase_t4968A4C72559F35C0923E4BD9C042C3A842E1DB8  : public RuntimeObject
{
	InvokableCallList_t309E1C8C7CE885A0D2F98C84CEA77A8935688382* ___m_Calls;
	PersistentCallGroup_tB826EDF15DC80F71BCBCD8E410FD959A04C33F25* ___m_PersistentCalls;
	bool ___m_CallsDirty;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
typedef Il2CppFullySharedGenericStruct Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339;
struct UnityEvent_1_t9A868DD8EBFC0D9D8134D903A170ECBDEE567932  : public UnityEventBase_t4968A4C72559F35C0923E4BD9C042C3A842E1DB8
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___m_InvokeArray;
};
struct BaseEventData_tE03A848325C0AE8E76C6CA15FD86395EBF83364F  : public AbstractEventData_tAE1A127ED657117548181D29FFE4B1B14D8E67F7
{
	EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707* ___m_EventSystem;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D 
{
	uint64_t ____dateData;
};
struct DrivenRectTransformTracker_tFB0706C933E3C68E4F377C204FCEEF091F1EE0B1 
{
	union
	{
		struct
		{
		};
		uint8_t DrivenRectTransformTracker_tFB0706C933E3C68E4F377C204FCEEF091F1EE0B1__padding[1];
	};
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D 
{
	float ___m_XMin;
	float ___m_YMin;
	float ___m_Width;
	float ___m_Height;
};
struct Scene_tA1DC762B79745EB5140F054C884855B922318356 
{
	int32_t ___m_Handle;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A 
{
	int64_t ____ticks;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D736_t1BF8B4CCF6DF77B5BADA6209C80EABA56EC2F52E 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D736_t1BF8B4CCF6DF77B5BADA6209C80EABA56EC2F52E__padding[736];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D861_tB78F9C6E53BD8400DB767717D3BF8FAB8D6A03D1 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D861_tB78F9C6E53BD8400DB767717D3BF8FAB8D6A03D1__padding[861];
	};
};
#pragma pack(pop, tp)
struct UIToolkitOverrideConfig_t4E6B4528E38BCA7DA72C45424634806200A50182 
{
	EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707* ___activeEventSystem;
	bool ___sendEvents;
	bool ___createPanelGameObjectsOnStart;
};
struct UIToolkitOverrideConfig_t4E6B4528E38BCA7DA72C45424634806200A50182_marshaled_pinvoke
{
	EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707* ___activeEventSystem;
	int32_t ___sendEvents;
	int32_t ___createPanelGameObjectsOnStart;
};
struct UIToolkitOverrideConfig_t4E6B4528E38BCA7DA72C45424634806200A50182_marshaled_com
{
	EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707* ___activeEventSystem;
	int32_t ___sendEvents;
	int32_t ___createPanelGameObjectsOnStart;
};
struct Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Center;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Extents;
};
struct DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 
{
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___dateTime;
	float ___elapsedSeconds;
	int32_t ___frameCount;
};
struct DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97_marshaled_pinvoke
{
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___dateTime;
	float ___elapsedSeconds;
	int32_t ___frameCount;
};
struct DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97_marshaled_com
{
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___dateTime;
	float ___elapsedSeconds;
	int32_t ___frameCount;
};
struct DebugLogFilter_t176E0D1D099B26C4BF6E395F35ECE3B95446D218 
{
	int32_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct KeyCode_t75B9ECCC26D858F55040DDFF9523681E996D17E9 
{
	int32_t ___value__;
};
struct LoadSceneMode_t3E17ADA25A3C4F14ECF6026741219437DA054963 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct PenStatus_tCAD6543115EF443E17410B52D37EC67BCC88ABB8 
{
	int32_t ___value__;
};
struct PopupVisibility_t7278D4D7AE2BE3FD12590E8A11C48485B852E975 
{
	int32_t ___value__;
};
struct RaycastResult_tEC6A7B7CABA99C386F054F01E498AEC426CF8023 
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_GameObject;
	BaseRaycaster_t7DC8158FD3CA0193455344379DD5FF7CD5F1F832* ___module;
	float ___distance;
	float ___index;
	int32_t ___depth;
	int32_t ___sortingGroupID;
	int32_t ___sortingGroupOrder;
	int32_t ___sortingLayer;
	int32_t ___sortingOrder;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___worldPosition;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___worldNormal;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___screenPosition;
	int32_t ___displayIndex;
};
struct RaycastResult_tEC6A7B7CABA99C386F054F01E498AEC426CF8023_marshaled_pinvoke
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_GameObject;
	BaseRaycaster_t7DC8158FD3CA0193455344379DD5FF7CD5F1F832* ___module;
	float ___distance;
	float ___index;
	int32_t ___depth;
	int32_t ___sortingGroupID;
	int32_t ___sortingGroupOrder;
	int32_t ___sortingLayer;
	int32_t ___sortingOrder;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___worldPosition;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___worldNormal;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___screenPosition;
	int32_t ___displayIndex;
};
struct RaycastResult_tEC6A7B7CABA99C386F054F01E498AEC426CF8023_marshaled_com
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_GameObject;
	BaseRaycaster_t7DC8158FD3CA0193455344379DD5FF7CD5F1F832* ___module;
	float ___distance;
	float ___index;
	int32_t ___depth;
	int32_t ___sortingGroupID;
	int32_t ___sortingGroupOrder;
	int32_t ___sortingLayer;
	int32_t ___sortingOrder;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___worldPosition;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___worldNormal;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___screenPosition;
	int32_t ___displayIndex;
};
struct U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___targetPos;
	DebugLogPopup_t521468752FCF53CE80E2E8989D7A42A10D8E216B* ___U3CU3E4__this;
	float ___U3CmodifierU3E5__1;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CinitialPosU3E5__2;
};
struct FillMethod_t36837ED12068DF1582CC20489D571B0BCAA7AD19 
{
	int32_t ___value__;
};
struct Type_t81D6F138C2FC745112D5247CD91BD483EDFFC041 
{
	int32_t ___value__;
};
struct InputButton_t7F40241CC7C406EBD574D426F736CB744DE86CDA 
{
	int32_t ___value__;
};
struct MovementType_t35B76DF2E479A4C67D7768854404EFB47BF1BBB6 
{
	int32_t ___value__;
};
struct ScrollRectEvent_t812C011901E6101F2A0FFC34C66AC5F65C0DEC26  : public UnityEvent_1_t9A868DD8EBFC0D9D8134D903A170ECBDEE567932
{
};
struct ScrollbarVisibility_t04A8B197CECE292E71BBB9145B1CA95BD450383E 
{
	int32_t ___value__;
};
struct Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 
{
	bool ___hasValue;
	DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 ___value;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB  : public BaseEventData_tE03A848325C0AE8E76C6CA15FD86395EBF83364F
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___U3CpointerEnterU3Ek__BackingField;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_PointerPress;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___U3ClastPressU3Ek__BackingField;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___U3CrawPointerPressU3Ek__BackingField;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___U3CpointerDragU3Ek__BackingField;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___U3CpointerClickU3Ek__BackingField;
	RaycastResult_tEC6A7B7CABA99C386F054F01E498AEC426CF8023 ___U3CpointerCurrentRaycastU3Ek__BackingField;
	RaycastResult_tEC6A7B7CABA99C386F054F01E498AEC426CF8023 ___U3CpointerPressRaycastU3Ek__BackingField;
	List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* ___hovered;
	bool ___U3CeligibleForClickU3Ek__BackingField;
	int32_t ___U3CdisplayIndexU3Ek__BackingField;
	int32_t ___U3CpointerIdU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CpositionU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CdeltaU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CpressPositionU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CworldPositionU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CworldNormalU3Ek__BackingField;
	float ___U3CclickTimeU3Ek__BackingField;
	int32_t ___U3CclickCountU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CscrollDeltaU3Ek__BackingField;
	bool ___U3CuseDragThresholdU3Ek__BackingField;
	bool ___U3CdraggingU3Ek__BackingField;
	int32_t ___U3CbuttonU3Ek__BackingField;
	float ___U3CpressureU3Ek__BackingField;
	float ___U3CtangentialPressureU3Ek__BackingField;
	float ___U3CaltitudeAngleU3Ek__BackingField;
	float ___U3CazimuthAngleU3Ek__BackingField;
	float ___U3CtwistU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CtiltU3Ek__BackingField;
	int32_t ___U3CpenStatusU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CradiusU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CradiusVarianceU3Ek__BackingField;
	bool ___U3CfullyExitedU3Ek__BackingField;
	bool ___U3CreenteredU3Ek__BackingField;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6  : public MulticastDelegate_t
{
};
struct Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99  : public MulticastDelegate_t
{
};
struct Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC  : public MulticastDelegate_t
{
};
struct UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C  : public MulticastDelegate_t
{
};
struct UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669  : public MulticastDelegate_t
{
};
struct UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A  : public MulticastDelegate_t
{
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5  : public Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1
{
};
struct DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___transformComponent;
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___imageComponent;
	CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* ___canvasGroupComponent;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___logText;
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___logTypeImage;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___logCountParent;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___logCountText;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___copyLogButton;
	DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* ___logEntry;
	Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 ___logEntryTimestamp;
	int32_t ___Index;
	bool ___isExpanded;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___logTextOriginalPosition;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___logTextOriginalSize;
	float ___copyLogButtonHeight;
	DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* ___listView;
};
struct DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	bool ___singleton;
	float ___minimumHeight;
	bool ___enableHorizontalResizing;
	bool ___resizeFromRight;
	float ___minimumWidth;
	float ___logWindowOpacity;
	float ___popupOpacity;
	int32_t ___popupVisibility;
	int32_t ___popupVisibilityLogFilter;
	bool ___startMinimized;
	bool ___toggleWithKey;
	int32_t ___toggleKey;
	bool ___enableSearchbar;
	float ___topSearchbarMinWidth;
	bool ___receiveLogsWhileInactive;
	bool ___receiveInfoLogs;
	bool ___receiveWarningLogs;
	bool ___receiveErrorLogs;
	bool ___receiveExceptionLogs;
	bool ___captureLogTimestamps;
	bool ___alwaysDisplayTimestamps;
	int32_t ___maxLogCount;
	int32_t ___logsToRemoveAfterMaxLogCount;
	int32_t ___queuedLogLimit;
	bool ___clearCommandAfterExecution;
	int32_t ___commandHistorySize;
	bool ___showCommandSuggestions;
	bool ___receiveLogcatLogsInAndroid;
	String_t* ___logcatArguments;
	bool ___avoidScreenCutout;
	bool ___popupAvoidsScreenCutout;
	int32_t ___maxLogLength;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___logItemPrefab;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___commandSuggestionPrefab;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___infoLog;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___warningLog;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___errorLog;
	SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___logSpriteRepresentations;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___resizeIconAllDirections;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___resizeIconVerticalOnly;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___collapseButtonNormalColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___collapseButtonSelectedColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___filterButtonsNormalColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___filterButtonsSelectedColor;
	String_t* ___commandSuggestionHighlightStart;
	String_t* ___commandSuggestionHighlightEnd;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___logWindowTR;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___canvasTR;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___logItemsContainer;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___commandSuggestionsContainer;
	InputField_tABEA115F23FBD374EBE80D4FAC1D15BD6E37A140* ___commandInputField;
	Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* ___hideButton;
	Button_t6786514A57F7AFDEE5431112FEA0CAB24F5AE098* ___clearButton;
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___collapseButton;
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___filterInfoButton;
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___filterWarningButton;
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___filterErrorButton;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___infoEntryCountText;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___warningEntryCountText;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___errorEntryCountText;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___searchbar;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___searchbarSlotTop;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___searchbarSlotBottom;
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___resizeButton;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___snapToBottomButton;
	CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* ___logWindowCanvasGroup;
	DebugLogPopup_t521468752FCF53CE80E2E8989D7A42A10D8E216B* ___popupManager;
	ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* ___logItemsScrollRect;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___logItemsScrollRectTR;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___logItemsScrollRectOriginalSize;
	DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* ___recycledListView;
	bool ___isLogWindowVisible;
	bool ___screenDimensionsChanged;
	float ___logWindowPreviousWidth;
	int32_t ___infoEntryCount;
	int32_t ___warningEntryCount;
	int32_t ___errorEntryCount;
	bool ___entryCountTextsDirty;
	int32_t ___newInfoEntryCount;
	int32_t ___newWarningEntryCount;
	int32_t ___newErrorEntryCount;
	bool ___isCollapseOn;
	int32_t ___logFilter;
	String_t* ___searchTerm;
	bool ___isInSearchMode;
	bool ___SnapToBottom;
	DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* ___collapsedLogEntries;
	DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* ___collapsedLogEntriesTimestamps;
	Dictionary_2_tD2A01FF7682654DF38C267D638BAB8AE0491A568* ___collapsedLogEntriesMap;
	DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* ___uncollapsedLogEntries;
	DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* ___uncollapsedLogEntriesTimestamps;
	DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* ___logEntriesToShow;
	DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* ___timestampsOfLogEntriesToShow;
	int32_t ___indexOfLogEntryToSelectAndFocus;
	bool ___shouldUpdateRecycledListView;
	DynamicCircularBuffer_1_tBF936640484CF43115B5EB2600C146DACC75D965* ___queuedLogEntries;
	DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* ___queuedLogEntriesTimestamps;
	RuntimeObject* ___logEntriesLock;
	int32_t ___pendingLogToAutoExpand;
	List_1_tF2BD894424997346355BE3D3F0A728DBF10DCF48* ___commandSuggestionInstances;
	int32_t ___visibleCommandSuggestionInstances;
	List_1_t77A2CF97AA1B05C81783D84584405B0B9A8484F4* ___matchingCommandSuggestions;
	List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* ___commandCaretIndexIncrements;
	String_t* ___commandInputFieldPrevCommand;
	String_t* ___commandInputFieldPrevCommandName;
	int32_t ___commandInputFieldPrevParamCount;
	int32_t ___commandInputFieldPrevCaretPos;
	int32_t ___commandInputFieldPrevCaretArgumentIndex;
	String_t* ___commandInputFieldAutoCompleteBase;
	bool ___commandInputFieldAutoCompletedNow;
	Stack_1_t5DB813F9C669005201F1DE46996A668CCA70397C* ___pooledLogEntries;
	Stack_1_t45460CCBC43223EC301AD4CD87BD53CCA2F47FC8* ___pooledLogItems;
	bool ___anyCollapsedLogRemoved;
	int32_t ___removedLogEntriesToShowCount;
	CircularBuffer_1_t6F1B957F4957913743FE1DFC2F4F0B05E471128A* ___commandHistory;
	int32_t ___commandHistoryIndex;
	String_t* ___unfinishedCommand;
	StringBuilder_t* ___sharedStringBuilder;
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___localTimeUtcOffset;
	float ___lastElapsedSeconds;
	int32_t ___lastFrameCount;
	DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 ___dummyLogEntryTimestamp;
	PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB* ___nullPointerEventData;
	Action_1_tA5EFEB887DFB52B21189CCF158EFA7BEA6E91D0A* ___poolLogEntryAction;
	Action_1_tA5EFEB887DFB52B21189CCF158EFA7BEA6E91D0A* ___removeUncollapsedLogEntryAction;
	Predicate_1_t2D3DB1B2F8687CD481B0FE1CD42006398EFAB34E* ___shouldRemoveCollapsedLogEntryPredicate;
	Predicate_1_t2D3DB1B2F8687CD481B0FE1CD42006398EFAB34E* ___shouldRemoveLogEntryToShowPredicate;
	Action_2_tA1976F05055FFB43DE1C61397E93BBBFCD77E97E* ___updateLogEntryCollapsedIndexAction;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnLogWindowShown;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnLogWindowHidden;
};
struct DebugLogPopup_t521468752FCF53CE80E2E8989D7A42A10D8E216B  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___popupTransform;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___halfSize;
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___backgroundImage;
	CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* ___canvasGroup;
	DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* ___debugManager;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___newInfoCountText;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___newWarningCountText;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___newErrorCountText;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___alertColorInfo;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___alertColorWarning;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___alertColorError;
	int32_t ___newInfoCount;
	int32_t ___newWarningCount;
	int32_t ___newErrorCount;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___normalColor;
	bool ___isPopupBeingDragged;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___normalizedPosition;
	RuntimeObject* ___moveToPosCoroutine;
	bool ___U3CIsVisibleU3Ek__BackingField;
};
struct DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___transformComponent;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___viewportTransform;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___logItemNormalColor1;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___logItemNormalColor2;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___logItemSelectedColor;
	DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* ___manager;
	ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* ___scrollView;
	float ___logItemHeight;
	DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* ___entriesToShow;
	DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* ___timestampsOfEntriesToShow;
	DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* ___selectedLogEntry;
	int32_t ___indexOfSelectedLogEntry;
	float ___heightOfSelectedLogEntry;
	DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* ___visibleLogItems;
	bool ___isCollapseOn;
	int32_t ___currentTopIndex;
	int32_t ___currentBottomIndex;
	Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC* ___shouldRemoveLogItemPredicate;
	Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* ___poolLogItemAction;
};
struct DebugLogResizeListener_tAEB6FDAE1D37AD856CBD82204674E6AC4C1764B5  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* ___debugManager;
};
struct DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* ___debugsScrollRect;
	DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* ___debugLogManager;
};
struct EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___embeddedEventSystem;
};
struct InputFieldWarningsFixer_tE2789F5BD5EE03FE010196301F77663D483ABC90  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	List_1_tA5BDE435C735A082941CD33D212F97F4AE9FA55F* ___m_SystemInputModules;
	BaseInputModule_tF3B7C22AF1419B2AC9ECE6589357DC1B88ED96B1* ___m_CurrentInputModule;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_FirstSelected;
	bool ___m_sendNavigationEvents;
	int32_t ___m_DragThreshold;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_CurrentSelected;
	bool ___m_HasFocus;
	bool ___m_SelectionGuard;
	BaseEventData_tE03A848325C0AE8E76C6CA15FD86395EBF83364F* ___m_DummyData;
	bool ___m_Started;
	bool ___m_IsTrackingUIToolkitPanels;
};
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_Material;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_Color;
	bool ___m_SkipLayoutUpdate;
	bool ___m_SkipMaterialUpdate;
	bool ___m_RaycastTarget;
	bool ___m_RaycastTargetCache;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___m_RaycastPadding;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_RectTransform;
	CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860* ___m_CanvasRenderer;
	Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26* ___m_Canvas;
	bool ___m_VertsDirty;
	bool ___m_MaterialDirty;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyLayoutCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyVertsCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyMaterialCallback;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___m_CachedMesh;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___m_CachedUvs;
	TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4* ___m_ColorTweenRunner;
	bool ___U3CuseLegacyMeshGenerationU3Ek__BackingField;
};
struct ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_Content;
	bool ___m_Horizontal;
	bool ___m_Vertical;
	int32_t ___m_MovementType;
	float ___m_Elasticity;
	bool ___m_Inertia;
	float ___m_DecelerationRate;
	float ___m_ScrollSensitivity;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_Viewport;
	Scrollbar_t7CDC9B956698D9385A11E4C12964CD51477072C3* ___m_HorizontalScrollbar;
	Scrollbar_t7CDC9B956698D9385A11E4C12964CD51477072C3* ___m_VerticalScrollbar;
	int32_t ___m_HorizontalScrollbarVisibility;
	int32_t ___m_VerticalScrollbarVisibility;
	float ___m_HorizontalScrollbarSpacing;
	float ___m_VerticalScrollbarSpacing;
	ScrollRectEvent_t812C011901E6101F2A0FFC34C66AC5F65C0DEC26* ___m_OnValueChanged;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_PointerStartLocalCursor;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_ContentStartPosition;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_ViewRect;
	Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 ___m_ContentBounds;
	Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 ___m_ViewBounds;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_Velocity;
	bool ___m_Dragging;
	bool ___m_Scrolling;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_PrevPosition;
	Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 ___m_PrevContentBounds;
	Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 ___m_PrevViewBounds;
	bool ___m_HasRebuiltLayout;
	bool ___m_HSliderExpand;
	bool ___m_VSliderExpand;
	float ___m_HSliderHeight;
	float ___m_VSliderWidth;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_Rect;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_HorizontalScrollbarRect;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_VerticalScrollbarRect;
	DrivenRectTransformTracker_tFB0706C933E3C68E4F377C204FCEEF091F1EE0B1 ___m_Tracker;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___m_Corners;
};
struct MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E  : public Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931
{
	bool ___m_ShouldRecalculateStencil;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_MaskMaterial;
	RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670* ___m_ParentMask;
	bool ___m_Maskable;
	bool ___m_IsMaskingGraphic;
	bool ___m_IncludeForMasking;
	CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8* ___m_OnCullStateChanged;
	bool ___m_ShouldRecalculate;
	int32_t ___m_StencilValue;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___m_Corners;
};
struct Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E  : public MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E
{
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_Sprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_OverrideSprite;
	int32_t ___m_Type;
	bool ___m_PreserveAspect;
	bool ___m_FillCenter;
	int32_t ___m_FillMethod;
	float ___m_FillAmount;
	bool ___m_FillClockwise;
	int32_t ___m_FillOrigin;
	float ___m_AlphaHitTestMinimumThreshold;
	bool ___m_Tracked;
	bool ___m_UseSpriteMesh;
	float ___m_PixelsPerUnitMultiplier;
	float ___m_CachedReferencePixelsPerUnit;
};
struct U3CPrivateImplementationDetailsU3E_t1ECF1A9E3F556408EC04BFACA61EAE2314C16DF1_StaticFields
{
	__StaticArrayInitTypeSizeU3D736_t1BF8B4CCF6DF77B5BADA6209C80EABA56EC2F52E ___05D2DB40060C4D81D47C328F3BB631529195C815071487F0258AA4B4B7565EF0;
	__StaticArrayInitTypeSizeU3D861_tB78F9C6E53BD8400DB767717D3BF8FAB8D6A03D1 ___815417081B42D965DAFECD51D9C2F4D92F92761E59E0135CCBDA6EBDAF491FDE;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5_StaticFields
{
	ReapplyDrivenProperties_t3482EA130A01FF7EE2EEFE37F66A5215D08CFE24* ___reapplyDrivenProperties;
};
struct DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8_StaticFields
{
	DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* ___U3CInstanceU3Ek__BackingField;
};
struct EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707_StaticFields
{
	List_1_tF2FE88545EFEC788CAAE6C74EC2F78E937FCCAC3* ___m_EventSystems;
	Comparison_1_t9FCAC8C8CE160A96C5AAD2DE1D353DCE8A2FEEFC* ___s_RaycastComparer;
	UIToolkitOverrideConfig_t4E6B4528E38BCA7DA72C45424634806200A50182 ___s_UIToolkitOverride;
};
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_StaticFields
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___s_DefaultUI;
	Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___s_WhiteTexture;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___s_Mesh;
	VertexHelper_tB905FCB02AE67CBEE5F265FE37A5938FC5D136FE* ___s_VertexHelper;
};
struct Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E_StaticFields
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___s_ETC1DefaultUI;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___s_VertScratch;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___s_UVScratch;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___s_Xy;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___s_Uv;
	List_1_tE6BB71ABF15905EFA2BE92C38A2716547AEADB19* ___m_TrackedTexturelessImages;
	bool ___s_Initialized;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Component_GetComponentInParent_TisIl2CppFullySharedGenericAny_mC623E57DF1C1113E52B35DF8F5130A698B7174A5_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, Il2CppFullySharedGenericAny* il2cppRetVal, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityAction_1__ctor_m5CDE58421961A2EE0BCD97B9A4F3602910C2CE29_gshared (UnityAction_1_tC5C168260FDBBEDACD1D1996850C58AA3E9C2259* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityEvent_1_AddListener_mCF6F788014D8853604DCD25C8AFB5C342F032F9D_gshared (UnityEvent_1_t2A2840A12C919146C6D21E86D7DAA8E3DAB74030* __this, UnityAction_1_tC5C168260FDBBEDACD1D1996850C58AA3E9C2259* ___0_call, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Predicate_1__ctor_m2B0279AF2950764FCBFEF3BC1BF616854B3EE3AC_gshared (Predicate_1_t81499D2838AC2641B3FA14CD4DBF7E1594A9E107* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m685A441EC9FAC9D554B26FA83A08F4BEF96DFF0E_gshared (Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DynamicCircularBuffer_1_get_Item_m140B474938EA004A9FA0E87A5EB4F32875E9BFFF_gshared (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669* __this, int32_t ___0_index, Il2CppFullySharedGenericAny* il2cppRetVal, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t DynamicCircularBuffer_1_get_Count_mE68C71C8EE6AA5DF7358E2043BD662DBE83EC476_gshared_inline (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DynamicCircularBuffer_1_TrimStart_m2BF68C2D14776714BD1EDF4E6AC260130CFDF056_gshared (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669* __this, int32_t ___0_trimCount, Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99* ___1_perElementCallback, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DynamicCircularBuffer_1_RemoveAll_m0254D09C98FC13B65019B4043333B087E054EE01_gshared (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669* __this, Predicate_1_t81499D2838AC2641B3FA14CD4DBF7E1594A9E107* ___0_shouldRemoveElement, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_m5A038831CEB84A7E374FE59D43444412629F833F_gshared_inline (Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99* __this, Il2CppFullySharedGenericAny ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Nullable_1__ctor_m4257D7FF23A495D1B204F20330FBDED58248E4CC_gshared (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339* __this, Il2CppFullySharedGenericStruct ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DynamicCircularBuffer_1_Add_mA91FB0B2E7A25E0D18506393C5DA9B377107207C_gshared (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669* __this, Il2CppFullySharedGenericAny ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DynamicCircularBuffer_1_TrimEnd_m1F3B1B62574C452EEB2867ED66F9FC369CB76EB8_gshared (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669* __this, int32_t ___0_trimCount, Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99* ___1_perElementCallback, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DynamicCircularBuffer_1_AddFirst_m11395A0AE45D1016EBDCE365F6417B3E9AE25FD9_gshared (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669* __this, Il2CppFullySharedGenericAny ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DynamicCircularBuffer_1__ctor_mD6DF67250A152C79CFDD7785CC458D5F8AC5F8AF_gshared (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669* __this, int32_t ___0_initialCapacity, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityAction_2__ctor_m17203366119014F4963976DF6B8E83DE49274252_gshared (UnityAction_2_t742C43FA6EAABE0458C753DFE15FDDFAE01EA73F* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 RectTransform_get_anchoredPosition_m38F25A4253B0905BB058BE73DBF43C7172CE0680 (RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_unscaledDeltaTime_mF057EECA857E5C0F90A3F910D26D3EE59F27C4B5 (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_Lerp_m1A36103F7967F653A929556E26E6D052C298C00C_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_b, float ___2_t, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectTransform_set_anchoredPosition_mF903ACE04F6959B1CD67E2B94FABC0263068F965 (RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* __this, const RuntimeMethod* method) ;
inline ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* Component_GetComponentInParent_TisScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E_mD5D105BD6034A3131A91D7B6534D7E74834D72AD (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* il2cppRetVal;
	((  void (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, Il2CppFullySharedGenericAny*, const RuntimeMethod*))Component_GetComponentInParent_TisIl2CppFullySharedGenericAny_mC623E57DF1C1113E52B35DF8F5130A698B7174A5_gshared)((Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*)__this, (Il2CppFullySharedGenericAny*)&il2cppRetVal, method);
	return il2cppRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ScrollRectEvent_t812C011901E6101F2A0FFC34C66AC5F65C0DEC26* ScrollRect_get_onValueChanged_mA6AF3832A97E82D31BB8C20BCD6E87A300E56C05 (ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* __this, const RuntimeMethod* method) ;
inline void UnityAction_1__ctor_m71C125B79EE35648CFC416CA7DBBC27C8DBAD45A (UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669*, RuntimeObject*, intptr_t, const RuntimeMethod*))UnityAction_1__ctor_m5CDE58421961A2EE0BCD97B9A4F3602910C2CE29_gshared)(__this, ___0_object, ___1_method, method);
}
inline void UnityEvent_1_AddListener_m2B74313C91E347D6AD24CE5B036E190E77E70851 (UnityEvent_1_t9A868DD8EBFC0D9D8134D903A170ECBDEE567932* __this, UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669* ___0_call, const RuntimeMethod* method)
{
	((  void (*) (UnityEvent_1_t9A868DD8EBFC0D9D8134D903A170ECBDEE567932*, UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669*, const RuntimeMethod*))UnityEvent_1_AddListener_mCF6F788014D8853604DCD25C8AFB5C342F032F9D_gshared)(__this, ___0_call, method);
}
inline void Predicate_1__ctor_m1D033D098E719D8745BF5D7D4D5302E92968F489 (Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC*, RuntimeObject*, intptr_t, const RuntimeMethod*))Predicate_1__ctor_m2B0279AF2950764FCBFEF3BC1BF616854B3EE3AC_gshared)(__this, ___0_object, ___1_method, method);
}
inline void Action_1__ctor_m8BE57CCFAF1B86FDC404B17AB6DCAA0A6DCD5EED (Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m685A441EC9FAC9D554B26FA83A08F4BEF96DFF0E_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_OnLogItemClickedInternal_m9C3AA7CA2C2C65BC15F72C0AA4AA9310F724707E (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, int32_t ___0_itemIndex, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___1_referenceItem, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D RectTransform_get_rect_mC82A60F8C3805ED9833508CCC233689641207488 (RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 RectTransform_get_sizeDelta_m822A8493F2035677384F1540A2E9E5ACE63010BB (RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScrollRect_set_verticalNormalizedPosition_m4AF461113925E6710BF04F46A49CF1F856F7738C (ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D_inline (float ___0_a, float ___1_b, float ___2_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_DeselectSelectedLogItem_m04DC7746AB16AF9D1B7E9D9747ACA6B70787BF63 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) ;
inline DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4 (DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* il2cppRetVal;
	((  void (*) (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*, int32_t, Il2CppFullySharedGenericAny*, const RuntimeMethod*))DynamicCircularBuffer_1_get_Item_m140B474938EA004A9FA0E87A5EB4F32875E9BFFF_gshared)((DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*)__this, ___0_index, (Il2CppFullySharedGenericAny*)&il2cppRetVal, method);
	return il2cppRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_CalculateSelectedLogEntryHeight_mE78B718B6A980A95F3E9D538AB67D6075F253D44 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_referenceItem, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, bool ___0_updateAllVisibleItemContents, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogManager_ValidateScrollPosition_m2A11C6555ED058C996C5116F208E953848D479FF (DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* DebugLogRecycledListView_GetLogItemAtIndex_m701E9237DDEC23011F430F2B47D52B573DC34BAD (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogItem_ShowCount_m370DD836F4F51DE3090FBA4E46F0D5DAE4F6E7A4 (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* __this, const RuntimeMethod* method) ;
inline DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94 (DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 il2cppRetVal;
	((  void (*) (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*, int32_t, Il2CppFullySharedGenericAny*, const RuntimeMethod*))DynamicCircularBuffer_1_get_Item_m140B474938EA004A9FA0E87A5EB4F32875E9BFFF_gshared)((DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*)__this, ___0_index, (Il2CppFullySharedGenericAny*)&il2cppRetVal, method);
	return il2cppRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogItem_UpdateTimestamp_m727217AF90E45BF3F269237C89C9477D0EC3CEB0 (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* __this, DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 ___0_timestamp, const RuntimeMethod* method) ;
inline DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79 (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* il2cppRetVal;
	((  void (*) (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*, int32_t, Il2CppFullySharedGenericAny*, const RuntimeMethod*))DynamicCircularBuffer_1_get_Item_m140B474938EA004A9FA0E87A5EB4F32875E9BFFF_gshared)((DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*)__this, ___0_index, (Il2CppFullySharedGenericAny*)&il2cppRetVal, method);
	return il2cppRetVal;
}
inline int32_t DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF*, const RuntimeMethod*))DynamicCircularBuffer_1_get_Count_mE68C71C8EE6AA5DF7358E2043BD662DBE83EC476_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DebugLogRecycledListView_FindIndexOfLogEntryInReverseDirection_m2638A20C51B02F596001C11B22890147D26D7B27 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* ___0_logEntry, int32_t ___1_startIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool DebugLogManager_get_IsLogWindowVisible_mA4654B1326D4234C4754F811A845C9C57E7F8F54 (DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* __this, const RuntimeMethod* method) ;
inline void DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08 (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* __this, int32_t ___0_trimCount, Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* ___1_perElementCallback, const RuntimeMethod* method)
{
	((  void (*) (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF*, int32_t, Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6*, const RuntimeMethod*))DynamicCircularBuffer_1_TrimStart_m2BF68C2D14776714BD1EDF4E6AC260130CFDF056_gshared)(__this, ___0_trimCount, ___1_perElementCallback, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_inline (int32_t ___0_value, int32_t ___1_min, int32_t ___2_max, const RuntimeMethod* method) ;
inline int32_t DynamicCircularBuffer_1_RemoveAll_mCC751454842A18019068E7B7D9C1A00CED8BF54F (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* __this, Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC* ___0_shouldRemoveElement, const RuntimeMethod* method)
{
	return ((  int32_t (*) (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF*, Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC*, const RuntimeMethod*))DynamicCircularBuffer_1_RemoveAll_m0254D09C98FC13B65019B4043333B087E054EE01_gshared)(__this, ___0_shouldRemoveElement, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* DebugLogItem_get_Entry_mA632E3B925BF0ED661A1FC9BE52E87AE1ADC30D1 (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Max_m7FA442918DE37E3A00106D1F2E789D65829792B8_inline (int32_t ___0_a, int32_t ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* DebugLogItem_get_Transform_mBAA5D6E01F83683FF29CE440C3D2F4D5616E6466 (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_RepositionLogItem_m6A7A36C93FBA0C44560F9D9556C343F5EA052072 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_logItem, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_ColorLogItem_mC1A80473D95AFCE3DC87E38DCABEB90096C1D2A7 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_logItem, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline (float ___0_a, float ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) ;
inline void Action_1_Invoke_m76B68BEAE6460AB4050CA2CC03792086EBE54EE2_inline (Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99*, Il2CppFullySharedGenericAny, const RuntimeMethod*))Action_1_Invoke_m5A038831CEB84A7E374FE59D43444412629F833F_gshared_inline)((Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99*)__this, (Il2CppFullySharedGenericAny)___0_obj, method);
}
inline int32_t DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_inline (DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4*, const RuntimeMethod*))DynamicCircularBuffer_1_get_Count_mE68C71C8EE6AA5DF7358E2043BD662DBE83EC476_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_inline (int32_t ___0_a, int32_t ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectTransform_set_sizeDelta_mC9A980EA6036E6725EF24CEDF3EE80A9B2B50EE5 (RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_exists, const RuntimeMethod* method) ;
inline void Nullable_1__ctor_m745BF2FAC35C508F889804F26D66321FE3785685 (Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41* __this, DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 ___0_value, const RuntimeMethod* method)
{
	((  void (*) (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*, Il2CppFullySharedGenericStruct, const RuntimeMethod*))Nullable_1__ctor_m4257D7FF23A495D1B204F20330FBDED58248E4CC_gshared)((Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*)__this, (Il2CppFullySharedGenericStruct)&___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float DebugLogItem_CalculateExpandedHeight_m55309AF097A86F270D520E266A7C3ACA00BA0EFC (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* __this, DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* ___0_logEntry, Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 ___1_logEntryTimestamp, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* DebugLogManager_PopLogItem_m854ED51B251AD048C1097A5BF3F6E8518504E0EA (DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* __this, const RuntimeMethod* method) ;
inline void DynamicCircularBuffer_1_Add_mB7CBE0E63E213E436C2F09949491E47182AB0DEE (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_value, const RuntimeMethod* method)
{
	((  void (*) (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*, Il2CppFullySharedGenericAny, const RuntimeMethod*))DynamicCircularBuffer_1_Add_mA91FB0B2E7A25E0D18506393C5DA9B377107207C_gshared)((DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*)__this, (Il2CppFullySharedGenericAny)___0_value, method);
}
inline void DynamicCircularBuffer_1_TrimEnd_mFB28BA268BCFAFE28246145B6EA96820BFAE3BA4 (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* __this, int32_t ___0_trimCount, Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* ___1_perElementCallback, const RuntimeMethod* method)
{
	((  void (*) (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF*, int32_t, Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6*, const RuntimeMethod*))DynamicCircularBuffer_1_TrimEnd_m1F3B1B62574C452EEB2867ED66F9FC369CB76EB8_gshared)(__this, ___0_trimCount, ___1_perElementCallback, method);
}
inline void DynamicCircularBuffer_1_AddFirst_m75C4229800AC7F9A99A7781E3DAC12B2FD0C75B3 (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_value, const RuntimeMethod* method)
{
	((  void (*) (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*, Il2CppFullySharedGenericAny, const RuntimeMethod*))DynamicCircularBuffer_1_AddFirst_m11395A0AE45D1016EBDCE365F6417B3E9AE25FD9_gshared)((DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669*)__this, (Il2CppFullySharedGenericAny)___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, int32_t ___0_topIndex, int32_t ___1_bottomIndex, int32_t ___2_logItemOffset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogItem_SetContent_mAA060346F58906FAA676D025BF7B440DEBA1C7E3 (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* __this, DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* ___0_logEntry, Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 ___1_logEntryTimestamp, int32_t ___2_entryIndex, bool ___3_isExpanded, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogItem_HideCount_m74445C719C5941D4DD597C50988526E5FAF7DBD2 (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* DebugLogItem_get_Image_m19E45CDC961A393E891617E0AE8196EEC60827E2 (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* __this, const RuntimeMethod* method) ;
inline void DynamicCircularBuffer_1__ctor_mBE7966474BC269763705CA36F37661885024A65A (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* __this, int32_t ___0_initialCapacity, const RuntimeMethod* method)
{
	((  void (*) (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF*, int32_t, const RuntimeMethod*))DynamicCircularBuffer_1__ctor_mD6DF67250A152C79CFDD7785CC458D5F8AC5F8AF_gshared)(__this, ___0_initialCapacity, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogManager_Resize_m0F28C37B4111332354C18770A29BD3D3277DD0E4 (DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* __this, PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB* ___0_eventData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D (DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ScrollRect_get_verticalNormalizedPosition_m4FE766F04272C1805FDE2A4B72D80F6190841FA1 (ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* __this, const RuntimeMethod* method) ;
inline void UnityAction_2__ctor_m0E0C01B7056EB1CB1E6C6F4FC457EBCA3F6B0041 (UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A*, RuntimeObject*, intptr_t, const RuntimeMethod*))UnityAction_2__ctor_m17203366119014F4963976DF6B8E83DE49274252_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SceneManager_remove_sceneLoaded_m72A7C2A1B8EF1C21A208A9A015375577768B3978 (UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SceneManager_add_sceneLoaded_m14BEBCC5E4A8DD2C806A48D79A4773315CB434C6 (UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A* ___0_value, const RuntimeMethod* method) ;
inline void UnityAction_1__ctor_m3D196ADE59DE13B9FDC5D827B1A6D00CBEF1F6DF (UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C*, RuntimeObject*, intptr_t, const RuntimeMethod*))UnityAction_1__ctor_m5CDE58421961A2EE0BCD97B9A4F3602910C2CE29_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SceneManager_remove_sceneUnloaded_m2CACDB3F47DED2C92E6AA1912906F7E2C61424EB (UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SceneManager_add_sceneUnloaded_mC3BAE77FFFA0DBA3F6EE3303CA78400A3932F029 (UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventSystemHandler_ActivateEventSystemIfNeeded_m162A2AECC9C9CB151849DD89B25485FAFC30313B (EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8 (EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707* EventSystem_get_current_mC87C69FB418563DC2A571A10E2F9DB59A6785016 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, bool ___0_value, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CMoveToPosAnimationU3Ed__25__ctor_mC8A1388E86D07D17D4D9B6402EA77B701899C169 (U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CMoveToPosAnimationU3Ed__25__ctor_mC8A1388E86D07D17D4D9B6402EA77B701899C169_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CMoveToPosAnimationU3Ed__25__ctor_mC8A1388E86D07D17D4D9B6402EA77B701899C169_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CMoveToPosAnimationU3Ed__25_System_IDisposable_Dispose_m6B852D6BCF4CEDF266A8A62352A62334072D12C1 (U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CMoveToPosAnimationU3Ed__25_System_IDisposable_Dispose_m6B852D6BCF4CEDF266A8A62352A62334072D12C1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CMoveToPosAnimationU3Ed__25_System_IDisposable_Dispose_m6B852D6BCF4CEDF266A8A62352A62334072D12C1_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CMoveToPosAnimationU3Ed__25_MoveNext_mEB365A0C3236ACF671867DD1EFAA97C531BFEA59 (U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CMoveToPosAnimationU3Ed__25_MoveNext_mEB365A0C3236ACF671867DD1EFAA97C531BFEA59_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CMoveToPosAnimationU3Ed__25_MoveNext_mEB365A0C3236ACF671867DD1EFAA97C531BFEA59_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5376));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5377));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5378));
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		int32_t L_1 = V_0;
		if (!L_1)
		{
			goto IL_0012;
		}
	}
	{
		goto IL_000c;
	}

IL_000c:
	{
		int32_t L_2 = V_0;
		if ((((int32_t)L_2) == ((int32_t)1)))
		{
			goto IL_0014;
		}
	}
	{
		goto IL_0016;
	}

IL_0012:
	{
		goto IL_0018;
	}

IL_0014:
	{
		goto IL_0094;
	}

IL_0016:
	{
		return (bool)0;
	}

IL_0018:
	{
		__this->___U3CU3E1__state = (-1);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5379));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5380));
		__this->___U3CmodifierU3E5__1 = (0.0f);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5381));
		DebugLogPopup_t521468752FCF53CE80E2E8989D7A42A10D8E216B* L_3 = __this->___U3CU3E4__this;
		NullCheck(L_3);
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_4 = L_3->___popupTransform;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5382));
		NullCheck(L_4);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_5;
		L_5 = RectTransform_get_anchoredPosition_m38F25A4253B0905BB058BE73DBF43C7172CE0680(L_4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5382));
		__this->___U3CinitialPosU3E5__2 = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5383));
		goto IL_009c;
	}

IL_0043:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5384));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5385));
		float L_6 = __this->___U3CmodifierU3E5__1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5386));
		float L_7;
		L_7 = Time_get_unscaledDeltaTime_mF057EECA857E5C0F90A3F910D26D3EE59F27C4B5(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5386));
		__this->___U3CmodifierU3E5__1 = ((float)il2cpp_codegen_add(L_6, ((float)il2cpp_codegen_multiply((4.0f), L_7))));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5387));
		DebugLogPopup_t521468752FCF53CE80E2E8989D7A42A10D8E216B* L_8 = __this->___U3CU3E4__this;
		NullCheck(L_8);
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_9 = L_8->___popupTransform;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_10 = __this->___U3CinitialPosU3E5__2;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_11 = __this->___targetPos;
		float L_12 = __this->___U3CmodifierU3E5__1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5388));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_13;
		L_13 = Vector2_Lerp_m1A36103F7967F653A929556E26E6D052C298C00C_inline(L_10, L_11, L_12, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5388));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5389));
		NullCheck(L_9);
		RectTransform_set_anchoredPosition_mF903ACE04F6959B1CD67E2B94FABC0263068F965(L_9, L_13, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5389));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5390));
		__this->___U3CU3E2__current = NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)NULL);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0094:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5391));
		__this->___U3CU3E1__state = (-1);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5392));
	}

IL_009c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5393));
		float L_14 = __this->___U3CmodifierU3E5__1;
		V_1 = (bool)((((float)L_14) < ((float)(1.0f)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5394));
		bool L_15 = V_1;
		if (L_15)
		{
			goto IL_0043;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5395));
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CMoveToPosAnimationU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1EB8FC262CCF5BA2744BE265E81C2FE5362E2C59 (U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CMoveToPosAnimationU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1EB8FC262CCF5BA2744BE265E81C2FE5362E2C59_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CMoveToPosAnimationU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1EB8FC262CCF5BA2744BE265E81C2FE5362E2C59_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_Reset_m27A880D1F617C814C12D25EC9516E99F05D8B24D (U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_Reset_m27A880D1F617C814C12D25EC9516E99F05D8B24D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_Reset_m27A880D1F617C814C12D25EC9516E99F05D8B24D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_Reset_m27A880D1F617C814C12D25EC9516E99F05D8B24D_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_get_Current_m0D3D86F36CF20F84ADDB433D3D1101DB84DECD15 (U3CMoveToPosAnimationU3Ed__25_t59A75BC2F376932697B10D7E487D85644C248DC7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_get_Current_m0D3D86F36CF20F84ADDB433D3D1101DB84DECD15_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_get_Current_m0D3D86F36CF20F84ADDB433D3D1101DB84DECD15_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5396));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5397));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5398));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5399));
		float L_0 = __this->___heightOfSelectedLogEntry;
		float L_1 = __this->___logItemHeight;
		V_0 = ((float)il2cpp_codegen_subtract(L_0, L_1));
		goto IL_0011;
	}

IL_0011:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5400));
		float L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float DebugLogRecycledListView_get_ItemHeight_m4FBFACEF0C31D99C4745209391D410234EAE6A80 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_get_ItemHeight_m4FBFACEF0C31D99C4745209391D410234EAE6A80_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_get_ItemHeight_m4FBFACEF0C31D99C4745209391D410234EAE6A80_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5401));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5402));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5403));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5404));
		float L_0 = __this->___logItemHeight;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5405));
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float DebugLogRecycledListView_get_SelectedItemHeight_m55C399776D3C681426A1E7BB96D17799AAF17358 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_get_SelectedItemHeight_m55C399776D3C681426A1E7BB96D17799AAF17358_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_get_SelectedItemHeight_m55C399776D3C681426A1E7BB96D17799AAF17358_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5406));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5407));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5408));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5409));
		float L_0 = __this->___heightOfSelectedLogEntry;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5410));
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_Awake_mB17543BD466BD4A736547268AAC2680CC75BC5B3 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponentInParent_TisScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E_mD5D105BD6034A3131A91D7B6534D7E74834D72AD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_Awake_mB17543BD466BD4A736547268AAC2680CC75BC5B3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_U3CAwakeU3Eb__25_0_m46F3DED084709344721E369E0D3AC052735594A5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityEvent_1_AddListener_m2B74313C91E347D6AD24CE5B036E190E77E70851_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_Awake_mB17543BD466BD4A736547268AAC2680CC75BC5B3_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5411));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5412));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5413));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5414));
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_0 = __this->___viewportTransform;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5415));
		NullCheck(L_0);
		ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* L_1;
		L_1 = Component_GetComponentInParent_TisScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E_mD5D105BD6034A3131A91D7B6534D7E74834D72AD(L_0, Component_GetComponentInParent_TisScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E_mD5D105BD6034A3131A91D7B6534D7E74834D72AD_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5415));
		__this->___scrollView = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___scrollView), (void*)L_1);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5416));
		ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* L_2 = __this->___scrollView;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5417));
		NullCheck(L_2);
		ScrollRectEvent_t812C011901E6101F2A0FFC34C66AC5F65C0DEC26* L_3;
		L_3 = ScrollRect_get_onValueChanged_mA6AF3832A97E82D31BB8C20BCD6E87A300E56C05(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5417));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5418));
		UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669* L_4 = (UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669*)il2cpp_codegen_object_new(UnityAction_1_t8FBFBC01962B7293F0E33F9D6F1CEAF2896D8669_il2cpp_TypeInfo_var);
		UnityAction_1__ctor_m71C125B79EE35648CFC416CA7DBBC27C8DBAD45A(L_4, __this, (intptr_t)((void*)DebugLogRecycledListView_U3CAwakeU3Eb__25_0_m46F3DED084709344721E369E0D3AC052735594A5_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5418));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5419));
		NullCheck(L_3);
		UnityEvent_1_AddListener_m2B74313C91E347D6AD24CE5B036E190E77E70851(L_3, L_4, UnityEvent_1_AddListener_m2B74313C91E347D6AD24CE5B036E190E77E70851_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5419));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5420));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_Initialize_m9AB919674122BE9855B08B8DC0E6BA736DFAAAD2 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* ___0_manager, DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* ___1_entriesToShow, DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* ___2_timestampsOfEntriesToShow, float ___3_logItemHeight, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogManager_PoolLogItem_m50869AD2E15FB447682B47A83D7B06DED349B736_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_Initialize_m9AB919674122BE9855B08B8DC0E6BA736DFAAAD2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_ShouldRemoveLogItem_m88B27AD516FEC0744C63EA1AE7ECDD4B4C086124_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_manager), (&___1_entriesToShow), (&___2_timestampsOfEntriesToShow), (&___3_logItemHeight));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_Initialize_m9AB919674122BE9855B08B8DC0E6BA736DFAAAD2_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5421));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5422));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5423));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5424));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_0 = ___0_manager;
		__this->___manager = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___manager), (void*)L_0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5425));
		DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* L_1 = ___1_entriesToShow;
		__this->___entriesToShow = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___entriesToShow), (void*)L_1);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5426));
		DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* L_2 = ___2_timestampsOfEntriesToShow;
		__this->___timestampsOfEntriesToShow = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___timestampsOfEntriesToShow), (void*)L_2);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5427));
		float L_3 = ___3_logItemHeight;
		__this->___logItemHeight = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5428));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5429));
		Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC* L_4 = (Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC*)il2cpp_codegen_object_new(Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC_il2cpp_TypeInfo_var);
		Predicate_1__ctor_m1D033D098E719D8745BF5D7D4D5302E92968F489(L_4, __this, (intptr_t)((void*)DebugLogRecycledListView_ShouldRemoveLogItem_m88B27AD516FEC0744C63EA1AE7ECDD4B4C086124_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5429));
		__this->___shouldRemoveLogItemPredicate = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___shouldRemoveLogItemPredicate), (void*)L_4);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5430));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_5 = ___0_manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5431));
		Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* L_6 = (Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6*)il2cpp_codegen_object_new(Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6_il2cpp_TypeInfo_var);
		Action_1__ctor_m8BE57CCFAF1B86FDC404B17AB6DCAA0A6DCD5EED(L_6, L_5, (intptr_t)((void*)DebugLogManager_PoolLogItem_m50869AD2E15FB447682B47A83D7B06DED349B736_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5431));
		__this->___poolLogItemAction = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___poolLogItemAction), (void*)L_6);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5432));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_SetCollapseMode_m8B9F13CBAB315B685DBFBFFBD9FF546DC3AEAFC2 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, bool ___0_collapse, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_SetCollapseMode_m8B9F13CBAB315B685DBFBFFBD9FF546DC3AEAFC2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_collapse));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_SetCollapseMode_m8B9F13CBAB315B685DBFBFFBD9FF546DC3AEAFC2_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5433));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5434));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5435));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5436));
		bool L_0 = ___0_collapse;
		__this->___isCollapseOn = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5437));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_OnLogItemClicked_m884D345CF8F6ACB003D53075D9A8B6F8B4BB19B2 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_item, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_OnLogItemClicked_m884D345CF8F6ACB003D53075D9A8B6F8B4BB19B2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_item));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_OnLogItemClicked_m884D345CF8F6ACB003D53075D9A8B6F8B4BB19B2_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5438));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5439));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5440));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5441));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_0 = ___0_item;
		NullCheck(L_0);
		int32_t L_1 = L_0->___Index;
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_2 = ___0_item;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5442));
		DebugLogRecycledListView_OnLogItemClickedInternal_m9C3AA7CA2C2C65BC15F72C0AA4AA9310F724707E(__this, L_1, L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5442));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5443));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_SelectAndFocusOnLogItemAtIndex_m76E2D97A52AD52976B1A9ACCEBDC1253B9A07BC9 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, int32_t ___0_itemIndex, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_SelectAndFocusOnLogItemAtIndex_m76E2D97A52AD52976B1A9ACCEBDC1253B9A07BC9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	bool V_4 = false;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D V_5;
	memset((&V_5), 0, sizeof(V_5));
	bool V_6 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_itemIndex));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0), (&V_1), (&V_2), (&V_3));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_SelectAndFocusOnLogItemAtIndex_m76E2D97A52AD52976B1A9ACCEBDC1253B9A07BC9_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5444));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5445));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5446));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5447));
		int32_t L_0 = __this->___indexOfSelectedLogEntry;
		int32_t L_1 = ___0_itemIndex;
		V_4 = (bool)((((int32_t)((((int32_t)L_0) == ((int32_t)L_1))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5448));
		bool L_2 = V_4;
		if (!L_2)
		{
			goto IL_001c;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5449));
		int32_t L_3 = ___0_itemIndex;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5450));
		DebugLogRecycledListView_OnLogItemClickedInternal_m9C3AA7CA2C2C65BC15F72C0AA4AA9310F724707E(__this, L_3, (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230*)NULL, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5450));
	}

IL_001c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5451));
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_4 = __this->___viewportTransform;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5452));
		NullCheck(L_4);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_5;
		L_5 = RectTransform_get_rect_mC82A60F8C3805ED9833508CCC233689641207488(L_4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5452));
		V_5 = L_5;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5453));
		float L_6;
		L_6 = Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_inline((&V_5), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5453));
		V_0 = L_6;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5454));
		float L_7 = V_0;
		V_1 = ((float)il2cpp_codegen_multiply(L_7, (0.5f)));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5455));
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_8 = __this->___transformComponent;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5456));
		NullCheck(L_8);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_9;
		L_9 = RectTransform_get_sizeDelta_m822A8493F2035677384F1540A2E9E5ACE63010BB(L_8, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5456));
		float L_10 = L_9.___y;
		float L_11 = V_0;
		V_2 = ((float)il2cpp_codegen_subtract(L_10, ((float)il2cpp_codegen_multiply(L_11, (0.5f)))));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5457));
		int32_t L_12 = ___0_itemIndex;
		float L_13 = __this->___logItemHeight;
		float L_14 = V_0;
		V_3 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)L_12), L_13)), ((float)il2cpp_codegen_multiply(L_14, (0.5f)))));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5458));
		float L_15 = V_1;
		float L_16 = V_2;
		V_6 = (bool)((((float)L_15) == ((float)L_16))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5459));
		bool L_17 = V_6;
		if (!L_17)
		{
			goto IL_0081;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5460));
		ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* L_18 = __this->___scrollView;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5461));
		NullCheck(L_18);
		ScrollRect_set_verticalNormalizedPosition_m4AF461113925E6710BF04F46A49CF1F856F7738C(L_18, (0.5f), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5461));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5462));
		goto IL_009a;
	}

IL_0081:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5463));
		ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* L_19 = __this->___scrollView;
		float L_20 = V_2;
		float L_21 = V_1;
		float L_22 = V_3;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5464));
		float L_23;
		L_23 = Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D_inline(L_20, L_21, L_22, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5464));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5465));
		float L_24;
		L_24 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_23, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5465));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5466));
		NullCheck(L_19);
		ScrollRect_set_verticalNormalizedPosition_m4AF461113925E6710BF04F46A49CF1F856F7738C(L_19, L_24, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5466));
	}

IL_009a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5467));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_25 = __this->___manager;
		NullCheck(L_25);
		L_25->___SnapToBottom = (bool)0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5468));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_OnLogItemClickedInternal_m9C3AA7CA2C2C65BC15F72C0AA4AA9310F724707E (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, int32_t ___0_itemIndex, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___1_referenceItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_OnLogItemClickedInternal_m9C3AA7CA2C2C65BC15F72C0AA4AA9310F724707E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_itemIndex), (&___1_referenceItem));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_OnLogItemClickedInternal_m9C3AA7CA2C2C65BC15F72C0AA4AA9310F724707E_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5469));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5470));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5471));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5472));
		int32_t L_0 = __this->___indexOfSelectedLogEntry;
		V_0 = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5473));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5474));
		DebugLogRecycledListView_DeselectSelectedLogItem_m04DC7746AB16AF9D1B7E9D9747ACA6B70787BF63(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5474));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5475));
		int32_t L_1 = V_0;
		int32_t L_2 = ___0_itemIndex;
		V_1 = (bool)((((int32_t)((((int32_t)L_1) == ((int32_t)L_2))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5476));
		bool L_3 = V_1;
		if (!L_3)
		{
			goto IL_0049;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5477));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5478));
		DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* L_4 = __this->___entriesToShow;
		int32_t L_5 = ___0_itemIndex;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5479));
		NullCheck(L_4);
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_6;
		L_6 = DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4(L_4, L_5, DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5479));
		__this->___selectedLogEntry = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___selectedLogEntry), (void*)L_6);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5480));
		int32_t L_7 = ___0_itemIndex;
		__this->___indexOfSelectedLogEntry = L_7;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5481));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_8 = ___1_referenceItem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5482));
		DebugLogRecycledListView_CalculateSelectedLogEntryHeight_mE78B718B6A980A95F3E9D538AB67D6075F253D44(__this, L_8, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5482));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5483));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_9 = __this->___manager;
		NullCheck(L_9);
		L_9->___SnapToBottom = (bool)0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5484));
	}

IL_0049:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5485));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5486));
		DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5486));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5487));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5488));
		DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA(__this, (bool)1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5488));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5489));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_10 = __this->___manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5490));
		NullCheck(L_10);
		DebugLogManager_ValidateScrollPosition_m2A11C6555ED058C996C5116F208E953848D479FF(L_10, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5490));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5491));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_DeselectSelectedLogItem_m04DC7746AB16AF9D1B7E9D9747ACA6B70787BF63 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_DeselectSelectedLogItem_m04DC7746AB16AF9D1B7E9D9747ACA6B70787BF63_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_DeselectSelectedLogItem_m04DC7746AB16AF9D1B7E9D9747ACA6B70787BF63_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5492));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5493));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5494));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5495));
		__this->___selectedLogEntry = (DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___selectedLogEntry), (void*)(DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD*)NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5496));
		__this->___indexOfSelectedLogEntry = ((int32_t)2147483647LL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5497));
		__this->___heightOfSelectedLogEntry = (0.0f);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5498));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_OnLogEntriesUpdated_m31B75B0BEFE49F0A6C735132A24575ED537259F1 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, bool ___0_updateAllVisibleItemContents, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_OnLogEntriesUpdated_m31B75B0BEFE49F0A6C735132A24575ED537259F1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_updateAllVisibleItemContents));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_OnLogEntriesUpdated_m31B75B0BEFE49F0A6C735132A24575ED537259F1_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5499));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5500));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5501));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5502));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5503));
		DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5503));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5504));
		bool L_0 = ___0_updateAllVisibleItemContents;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5505));
		DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5505));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5506));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_OnCollapsedLogEntryAtIndexUpdated_m83CA66274FC922C662D9A56A137F308EC39F1DDB (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_OnCollapsedLogEntryAtIndexUpdated_m83CA66274FC922C662D9A56A137F308EC39F1DDB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* V_1 = NULL;
	bool V_2 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_index));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_1));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_OnCollapsedLogEntryAtIndexUpdated_m83CA66274FC922C662D9A56A137F308EC39F1DDB_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5507));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5508));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5509));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5510));
		int32_t L_0 = ___0_index;
		int32_t L_1 = __this->___currentTopIndex;
		if ((((int32_t)L_0) < ((int32_t)L_1)))
		{
			goto IL_0018;
		}
	}
	{
		int32_t L_2 = ___0_index;
		int32_t L_3 = __this->___currentBottomIndex;
		G_B3_0 = ((((int32_t)((((int32_t)L_2) > ((int32_t)L_3))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_0019;
	}

IL_0018:
	{
		G_B3_0 = 0;
	}

IL_0019:
	{
		V_0 = (bool)G_B3_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5511));
		bool L_4 = V_0;
		if (!L_4)
		{
			goto IL_004e;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5512));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5513));
		int32_t L_5 = ___0_index;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5514));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_6;
		L_6 = DebugLogRecycledListView_GetLogItemAtIndex_m701E9237DDEC23011F430F2B47D52B573DC34BAD(__this, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5514));
		V_1 = L_6;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5515));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_7 = V_1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5516));
		NullCheck(L_7);
		DebugLogItem_ShowCount_m370DD836F4F51DE3090FBA4E46F0D5DAE4F6E7A4(L_7, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5516));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5517));
		DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* L_8 = __this->___timestampsOfEntriesToShow;
		V_2 = (bool)((!(((RuntimeObject*)(DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA*)L_8) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5518));
		bool L_9 = V_2;
		if (!L_9)
		{
			goto IL_004d;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5519));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_10 = V_1;
		DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* L_11 = __this->___timestampsOfEntriesToShow;
		int32_t L_12 = ___0_index;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5520));
		NullCheck(L_11);
		DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 L_13;
		L_13 = DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94(L_11, L_12, DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5520));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5521));
		NullCheck(L_10);
		DebugLogItem_UpdateTimestamp_m727217AF90E45BF3F269237C89C9477D0EC3CEB0(L_10, L_13, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5521));
	}

IL_004d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5522));
	}

IL_004e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5523));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_RefreshCollapsedLogEntryCounts_m9DD4F6E26D5AD4913B7D572BDC6C1A0A1712241D (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_RefreshCollapsedLogEntryCounts_m9DD4F6E26D5AD4913B7D572BDC6C1A0A1712241D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_RefreshCollapsedLogEntryCounts_m9DD4F6E26D5AD4913B7D572BDC6C1A0A1712241D_RuntimeMethod_var, methodExecutionContextThis, NULL, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5524));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5525));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5526));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5527));
		V_0 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5528));
		goto IL_001b;
	}

IL_0005:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5529));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_0 = __this->___visibleLogItems;
		int32_t L_1 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5530));
		NullCheck(L_0);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_2;
		L_2 = DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79(L_0, L_1, DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5530));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5531));
		NullCheck(L_2);
		DebugLogItem_ShowCount_m370DD836F4F51DE3090FBA4E46F0D5DAE4F6E7A4(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5531));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5532));
		int32_t L_3 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_3, 1));
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5533));
		int32_t L_4 = V_0;
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_5 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5534));
		NullCheck(L_5);
		int32_t L_6;
		L_6 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_5, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5534));
		V_1 = (bool)((((int32_t)L_4) < ((int32_t)L_6))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5535));
		bool L_7 = V_1;
		if (L_7)
		{
			goto IL_0005;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5536));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_OnLogEntriesRemoved_m76BF83BC5683B794E130C669C9089505FD8A6437 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, int32_t ___0_removedLogCount, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_OnLogEntriesRemoved_m76BF83BC5683B794E130C669C9089505FD8A6437_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_RemoveAll_mCC751454842A18019068E7B7D9C1A00CED8BF54F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	bool V_3 = false;
	bool V_4 = false;
	bool V_5 = false;
	bool V_6 = false;
	bool V_7 = false;
	float V_8 = 0.0f;
	int32_t V_9 = 0;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* V_10 = NULL;
	bool V_11 = false;
	bool V_12 = false;
	bool V_13 = false;
	bool V_14 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_removedLogCount));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_1), (&V_8), (&V_9), (&V_10));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_OnLogEntriesRemoved_m76BF83BC5683B794E130C669C9089505FD8A6437_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5537));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5538));
	int32_t G_B4_0 = 0;
	DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* G_B8_0 = NULL;
	DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* G_B7_0 = NULL;
	int32_t G_B9_0 = 0;
	DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* G_B9_1 = NULL;
	int32_t G_B14_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5539));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5540));
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_0 = __this->___selectedLogEntry;
		V_0 = (bool)((!(((RuntimeObject*)(DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD*)L_0) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5541));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_006a;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5542));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5543));
		bool L_2 = __this->___isCollapseOn;
		if (L_2)
		{
			goto IL_0022;
		}
	}
	{
		int32_t L_3 = __this->___indexOfSelectedLogEntry;
		int32_t L_4 = ___0_removedLogCount;
		G_B4_0 = ((((int32_t)L_3) < ((int32_t)L_4))? 1 : 0);
		goto IL_0030;
	}

IL_0022:
	{
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_5 = __this->___selectedLogEntry;
		NullCheck(L_5);
		int32_t L_6 = L_5->___count;
		G_B4_0 = ((((int32_t)L_6) == ((int32_t)0))? 1 : 0);
	}

IL_0030:
	{
		V_1 = (bool)G_B4_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5544));
		bool L_7 = V_1;
		V_2 = L_7;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5545));
		bool L_8 = V_2;
		if (!L_8)
		{
			goto IL_003f;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5546));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5547));
		DebugLogRecycledListView_DeselectSelectedLogItem_m04DC7746AB16AF9D1B7E9D9747ACA6B70787BF63(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5547));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5548));
		goto IL_0069;
	}

IL_003f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5549));
		bool L_9 = __this->___isCollapseOn;
		if (L_9)
		{
			G_B8_0 = __this;
			goto IL_0052;
		}
		G_B7_0 = __this;
	}
	{
		int32_t L_10 = __this->___indexOfSelectedLogEntry;
		int32_t L_11 = ___0_removedLogCount;
		G_B9_0 = ((int32_t)il2cpp_codegen_subtract(L_10, L_11));
		G_B9_1 = G_B7_0;
		goto IL_0064;
	}

IL_0052:
	{
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_12 = __this->___selectedLogEntry;
		int32_t L_13 = __this->___indexOfSelectedLogEntry;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5550));
		int32_t L_14;
		L_14 = DebugLogRecycledListView_FindIndexOfLogEntryInReverseDirection_m2638A20C51B02F596001C11B22890147D26D7B27(__this, L_12, L_13, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5550));
		G_B9_0 = L_14;
		G_B9_1 = G_B8_0;
	}

IL_0064:
	{
		NullCheck(G_B9_1);
		G_B9_1->___indexOfSelectedLogEntry = G_B9_0;
	}

IL_0069:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5551));
	}

IL_006a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5552));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_15 = __this->___manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5553));
		NullCheck(L_15);
		bool L_16;
		L_16 = DebugLogManager_get_IsLogWindowVisible_mA4654B1326D4234C4754F811A845C9C57E7F8F54(L_15, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5553));
		if (L_16)
		{
			goto IL_0084;
		}
	}
	{
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_17 = __this->___manager;
		NullCheck(L_17);
		bool L_18 = L_17->___SnapToBottom;
		G_B14_0 = ((int32_t)(L_18));
		goto IL_0085;
	}

IL_0084:
	{
		G_B14_0 = 0;
	}

IL_0085:
	{
		V_3 = (bool)G_B14_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5554));
		bool L_19 = V_3;
		if (!L_19)
		{
			goto IL_00ad;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5555));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5556));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_20 = __this->___visibleLogItems;
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_21 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5557));
		NullCheck(L_21);
		int32_t L_22;
		L_22 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_21, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5557));
		Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* L_23 = __this->___poolLogItemAction;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5558));
		NullCheck(L_20);
		DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08(L_20, L_22, L_23, DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5558));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5559));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5560));
		goto IL_0142;
	}

IL_00ad:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5561));
		bool L_24 = __this->___isCollapseOn;
		V_4 = (bool)((((int32_t)L_24) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5562));
		bool L_25 = V_4;
		if (!L_25)
		{
			goto IL_00e9;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5563));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_26 = __this->___visibleLogItems;
		int32_t L_27 = ___0_removedLogCount;
		int32_t L_28 = __this->___currentTopIndex;
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_29 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5564));
		NullCheck(L_29);
		int32_t L_30;
		L_30 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_29, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5564));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5565));
		int32_t L_31;
		L_31 = Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_inline(((int32_t)il2cpp_codegen_subtract(L_27, L_28)), 0, L_30, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5565));
		Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* L_32 = __this->___poolLogItemAction;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5566));
		NullCheck(L_26);
		DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08(L_26, L_31, L_32, DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5566));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5567));
		goto IL_0142;
	}

IL_00e9:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5568));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5569));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_33 = __this->___visibleLogItems;
		Predicate_1_t79318F84B8FE8B7200C1A2E8D3571F2770EB87FC* L_34 = __this->___shouldRemoveLogItemPredicate;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5570));
		NullCheck(L_33);
		int32_t L_35;
		L_35 = DynamicCircularBuffer_1_RemoveAll_mCC751454842A18019068E7B7D9C1A00CED8BF54F(L_33, L_34, DynamicCircularBuffer_1_RemoveAll_mCC751454842A18019068E7B7D9C1A00CED8BF54F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5570));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5571));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_36 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5572));
		NullCheck(L_36);
		int32_t L_37;
		L_37 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_36, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5572));
		V_5 = (bool)((((int32_t)L_37) > ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5573));
		bool L_38 = V_5;
		if (!L_38)
		{
			goto IL_0141;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5574));
		int32_t L_39 = __this->___currentTopIndex;
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_40 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5575));
		NullCheck(L_40);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_41;
		L_41 = DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79(L_40, 0, DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5575));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5576));
		NullCheck(L_41);
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_42;
		L_42 = DebugLogItem_get_Entry_mA632E3B925BF0ED661A1FC9BE52E87AE1ADC30D1(L_41, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5576));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_43 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5577));
		NullCheck(L_43);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_44;
		L_44 = DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79(L_43, 0, DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5577));
		NullCheck(L_44);
		int32_t L_45 = L_44->___Index;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5578));
		int32_t L_46;
		L_46 = DebugLogRecycledListView_FindIndexOfLogEntryInReverseDirection_m2638A20C51B02F596001C11B22890147D26D7B27(__this, L_42, L_45, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5578));
		___0_removedLogCount = ((int32_t)il2cpp_codegen_subtract(L_39, L_46));
	}

IL_0141:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5579));
	}

IL_0142:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5580));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_47 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5581));
		NullCheck(L_47);
		int32_t L_48;
		L_48 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_47, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5581));
		V_6 = (bool)((((int32_t)L_48) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5582));
		bool L_49 = V_6;
		if (!L_49)
		{
			goto IL_0189;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5583));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5584));
		__this->___currentTopIndex = (-1);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5585));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_50 = __this->___manager;
		NullCheck(L_50);
		bool L_51 = L_50->___SnapToBottom;
		V_7 = (bool)((((int32_t)L_51) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5586));
		bool L_52 = V_7;
		if (!L_52)
		{
			goto IL_0183;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5587));
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_53 = __this->___transformComponent;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5588));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_54;
		L_54 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5588));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5589));
		NullCheck(L_53);
		RectTransform_set_anchoredPosition_mF903ACE04F6959B1CD67E2B94FABC0263068F965(L_53, L_54, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5589));
	}

IL_0183:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5590));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5591));
		goto IL_02b3;
	}

IL_0189:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5592));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5593));
		int32_t L_55 = __this->___currentTopIndex;
		int32_t L_56 = ___0_removedLogCount;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5594));
		int32_t L_57;
		L_57 = Mathf_Max_m7FA442918DE37E3A00106D1F2E789D65829792B8_inline(0, ((int32_t)il2cpp_codegen_subtract(L_55, L_56)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5594));
		__this->___currentTopIndex = L_57;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5595));
		int32_t L_58 = __this->___currentTopIndex;
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_59 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5596));
		NullCheck(L_59);
		int32_t L_60;
		L_60 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_59, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5596));
		__this->___currentBottomIndex = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_add(L_58, L_60)), 1));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5597));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_61 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5598));
		NullCheck(L_61);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_62;
		L_62 = DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79(L_61, 0, DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5598));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5599));
		NullCheck(L_62);
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_63;
		L_63 = DebugLogItem_get_Transform_mBAA5D6E01F83683FF29CE440C3D2F4D5616E6466(L_62, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5599));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5600));
		NullCheck(L_63);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_64;
		L_64 = RectTransform_get_anchoredPosition_m38F25A4253B0905BB058BE73DBF43C7172CE0680(L_63, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5600));
		float L_65 = L_64.___y;
		V_8 = L_65;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5601));
		V_9 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5602));
		goto IL_023a;
	}

IL_01da:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5603));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5604));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_66 = __this->___visibleLogItems;
		int32_t L_67 = V_9;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5605));
		NullCheck(L_66);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_68;
		L_68 = DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79(L_66, L_67, DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5605));
		V_10 = L_68;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5606));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_69 = V_10;
		int32_t L_70 = __this->___currentTopIndex;
		int32_t L_71 = V_9;
		NullCheck(L_69);
		L_69->___Index = ((int32_t)il2cpp_codegen_add(L_70, L_71));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5607));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_72 = __this->___manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5608));
		NullCheck(L_72);
		bool L_73;
		L_73 = DebugLogManager_get_IsLogWindowVisible_mA4654B1326D4234C4754F811A845C9C57E7F8F54(L_72, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5608));
		V_11 = L_73;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5609));
		bool L_74 = V_11;
		if (!L_74)
		{
			goto IL_0233;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5610));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5611));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_75 = V_10;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5612));
		DebugLogRecycledListView_RepositionLogItem_m6A7A36C93FBA0C44560F9D9556C343F5EA052072(__this, L_75, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5612));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5613));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_76 = V_10;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5614));
		DebugLogRecycledListView_ColorLogItem_mC1A80473D95AFCE3DC87E38DCABEB90096C1D2A7(__this, L_76, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5614));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5615));
		bool L_77 = __this->___isCollapseOn;
		V_12 = L_77;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5616));
		bool L_78 = V_12;
		if (!L_78)
		{
			goto IL_0232;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5617));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_79 = V_10;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5618));
		NullCheck(L_79);
		DebugLogItem_ShowCount_m370DD836F4F51DE3090FBA4E46F0D5DAE4F6E7A4(L_79, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5618));
	}

IL_0232:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5619));
	}

IL_0233:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5620));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5621));
		int32_t L_80 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_add(L_80, 1));
	}

IL_023a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5622));
		int32_t L_81 = V_9;
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_82 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5623));
		NullCheck(L_82);
		int32_t L_83;
		L_83 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_82, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5623));
		V_13 = (bool)((((int32_t)L_81) < ((int32_t)L_83))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5624));
		bool L_84 = V_13;
		if (L_84)
		{
			goto IL_01da;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5625));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_85 = __this->___manager;
		NullCheck(L_85);
		bool L_86 = L_85->___SnapToBottom;
		V_14 = (bool)((((int32_t)L_86) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5626));
		bool L_87 = V_14;
		if (!L_87)
		{
			goto IL_02b2;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5627));
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_88 = __this->___transformComponent;
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_89 = __this->___transformComponent;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5628));
		NullCheck(L_89);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_90;
		L_90 = RectTransform_get_anchoredPosition_m38F25A4253B0905BB058BE73DBF43C7172CE0680(L_89, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5628));
		float L_91 = L_90.___y;
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_92 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5629));
		NullCheck(L_92);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_93;
		L_93 = DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79(L_92, 0, DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5629));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5630));
		NullCheck(L_93);
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_94;
		L_94 = DebugLogItem_get_Transform_mBAA5D6E01F83683FF29CE440C3D2F4D5616E6466(L_93, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5630));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5631));
		NullCheck(L_94);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_95;
		L_95 = RectTransform_get_anchoredPosition_m38F25A4253B0905BB058BE73DBF43C7172CE0680(L_94, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5631));
		float L_96 = L_95.___y;
		float L_97 = V_8;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5632));
		float L_98;
		L_98 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline((0.0f), ((float)il2cpp_codegen_subtract(L_91, ((float)il2cpp_codegen_subtract(L_96, L_97)))), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5632));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5633));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_99;
		memset((&L_99), 0, sizeof(L_99));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_99), (0.0f), L_98, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5633));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5634));
		NullCheck(L_88);
		RectTransform_set_anchoredPosition_mF903ACE04F6959B1CD67E2B94FABC0263068F965(L_88, L_99, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5634));
	}

IL_02b2:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5635));
	}

IL_02b3:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5636));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool DebugLogRecycledListView_ShouldRemoveLogItem_m88B27AD516FEC0744C63EA1AE7ECDD4B4C086124 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_logItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_ShouldRemoveLogItem_m88B27AD516FEC0744C63EA1AE7ECDD4B4C086124_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_logItem));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_ShouldRemoveLogItem_m88B27AD516FEC0744C63EA1AE7ECDD4B4C086124_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5637));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5638));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5639));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5640));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_0 = ___0_logItem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5641));
		NullCheck(L_0);
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_1;
		L_1 = DebugLogItem_get_Entry_mA632E3B925BF0ED661A1FC9BE52E87AE1ADC30D1(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5641));
		NullCheck(L_1);
		int32_t L_2 = L_1->___count;
		V_0 = (bool)((((int32_t)L_2) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5642));
		bool L_3 = V_0;
		if (!L_3)
		{
			goto IL_0025;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5643));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5644));
		Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* L_4 = __this->___poolLogItemAction;
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_5 = ___0_logItem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5645));
		NullCheck(L_4);
		Action_1_Invoke_m76B68BEAE6460AB4050CA2CC03792086EBE54EE2_inline(L_4, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5645));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5646));
		V_1 = (bool)1;
		goto IL_0029;
	}

IL_0025:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5647));
		V_1 = (bool)0;
		goto IL_0029;
	}

IL_0029:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5648));
		bool L_6 = V_1;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DebugLogRecycledListView_FindIndexOfLogEntryInReverseDirection_m2638A20C51B02F596001C11B22890147D26D7B27 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* ___0_logEntry, int32_t ___1_startIndex, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_FindIndexOfLogEntryInReverseDirection_m2638A20C51B02F596001C11B22890147D26D7B27_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	bool V_1 = false;
	int32_t V_2 = 0;
	bool V_3 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_logEntry), (&___1_startIndex));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_FindIndexOfLogEntryInReverseDirection_m2638A20C51B02F596001C11B22890147D26D7B27_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5649));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5650));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5651));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5652));
		int32_t L_0 = ___1_startIndex;
		DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* L_1 = __this->___entriesToShow;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5653));
		NullCheck(L_1);
		int32_t L_2;
		L_2 = DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_inline(L_1, DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5653));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5654));
		int32_t L_3;
		L_3 = Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_inline(L_0, ((int32_t)il2cpp_codegen_subtract(L_2, 1)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5654));
		V_0 = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5655));
		goto IL_0034;
	}

IL_0017:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5656));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5657));
		DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* L_4 = __this->___entriesToShow;
		int32_t L_5 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5658));
		NullCheck(L_4);
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_6;
		L_6 = DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4(L_4, L_5, DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5658));
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_7 = ___0_logEntry;
		V_1 = (bool)((((RuntimeObject*)(DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD*)L_6) == ((RuntimeObject*)(DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD*)L_7))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5659));
		bool L_8 = V_1;
		if (!L_8)
		{
			goto IL_002f;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5660));
		int32_t L_9 = V_0;
		V_2 = L_9;
		goto IL_0043;
	}

IL_002f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5661));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5662));
		int32_t L_10 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_10, 1));
	}

IL_0034:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5663));
		int32_t L_11 = V_0;
		V_3 = (bool)((((int32_t)((((int32_t)L_11) < ((int32_t)0))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5664));
		bool L_12 = V_3;
		if (L_12)
		{
			goto IL_0017;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5665));
		V_2 = (-1);
		goto IL_0043;
	}

IL_0043:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5666));
		int32_t L_13 = V_2;
		return L_13;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_OnViewportWidthChanged_mC15FBC039660C721E52590BFA3E6A08320AF6C4D (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_OnViewportWidthChanged_mC15FBC039660C721E52590BFA3E6A08320AF6C4D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_OnViewportWidthChanged_mC15FBC039660C721E52590BFA3E6A08320AF6C4D_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5667));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5668));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5669));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5670));
		int32_t L_0 = __this->___indexOfSelectedLogEntry;
		DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* L_1 = __this->___entriesToShow;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5671));
		NullCheck(L_1);
		int32_t L_2;
		L_2 = DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_inline(L_1, DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5671));
		V_0 = (bool)((((int32_t)((((int32_t)L_0) < ((int32_t)L_2))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5672));
		bool L_3 = V_0;
		if (!L_3)
		{
			goto IL_001d;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5673));
		goto IL_0040;
	}

IL_001d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5674));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5675));
		DebugLogRecycledListView_CalculateSelectedLogEntryHeight_mE78B718B6A980A95F3E9D538AB67D6075F253D44(__this, (DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230*)NULL, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5675));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5676));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5677));
		DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5677));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5678));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5679));
		DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA(__this, (bool)1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5679));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5680));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_4 = __this->___manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5681));
		NullCheck(L_4);
		DebugLogManager_ValidateScrollPosition_m2A11C6555ED058C996C5116F208E953848D479FF(L_4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5681));
	}

IL_0040:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5682));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_OnViewportHeightChanged_mC257005C0AC4AAB23CAB212ABC2B3CD3311E2484 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_OnViewportHeightChanged_mC257005C0AC4AAB23CAB212ABC2B3CD3311E2484_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_OnViewportHeightChanged_mC257005C0AC4AAB23CAB212ABC2B3CD3311E2484_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5683));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5684));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5685));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5686));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5687));
		DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA(__this, (bool)0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5687));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5688));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB_RuntimeMethod_var, methodExecutionContextThis, NULL, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5689));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5690));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5691));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5692));
		DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* L_0 = __this->___entriesToShow;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5693));
		NullCheck(L_0);
		int32_t L_1;
		L_1 = DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_inline(L_0, DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5693));
		float L_2 = __this->___logItemHeight;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5694));
		float L_3;
		L_3 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline((1.0f), ((float)il2cpp_codegen_multiply(((float)L_1), L_2)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5694));
		V_0 = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5695));
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_4 = __this->___selectedLogEntry;
		V_1 = (bool)((!(((RuntimeObject*)(DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD*)L_4) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5696));
		bool L_5 = V_1;
		if (!L_5)
		{
			goto IL_0035;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5697));
		float L_6 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5698));
		float L_7;
		L_7 = DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5698));
		V_0 = ((float)il2cpp_codegen_add(L_6, L_7));
	}

IL_0035:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5699));
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_8 = __this->___transformComponent;
		float L_9 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5700));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_10;
		memset((&L_10), 0, sizeof(L_10));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_10), (0.0f), L_9, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5700));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5701));
		NullCheck(L_8);
		RectTransform_set_sizeDelta_mC9A980EA6036E6725EF24CEDF3EE80A9B2B50EE5(L_8, L_10, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5701));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5702));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_CalculateSelectedLogEntryHeight_mE78B718B6A980A95F3E9D538AB67D6075F253D44 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_referenceItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_CalculateSelectedLogEntryHeight_mE78B718B6A980A95F3E9D538AB67D6075F253D44_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1__ctor_m745BF2FAC35C508F889804F26D66321FE3785685_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 V_3;
	memset((&V_3), 0, sizeof(V_3));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_referenceItem));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_CalculateSelectedLogEntryHeight_mE78B718B6A980A95F3E9D538AB67D6075F253D44_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5703));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5704));
	DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* G_B8_0 = NULL;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* G_B8_1 = NULL;
	DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* G_B8_2 = NULL;
	DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* G_B7_0 = NULL;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* G_B7_1 = NULL;
	DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* G_B7_2 = NULL;
	Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 G_B9_0;
	memset((&G_B9_0), 0, sizeof(G_B9_0));
	DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* G_B9_1 = NULL;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* G_B9_2 = NULL;
	DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* G_B9_3 = NULL;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5705));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5706));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_0 = ___0_referenceItem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5707));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5707));
		V_0 = (bool)((((int32_t)L_1) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5708));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_004e;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5709));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5710));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_3 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5711));
		NullCheck(L_3);
		int32_t L_4;
		L_4 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_3, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5711));
		V_1 = (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5712));
		bool L_5 = V_1;
		if (!L_5)
		{
			goto IL_003f;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5713));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5714));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5715));
		DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA(__this, (bool)0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5715));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5716));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_6 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5717));
		NullCheck(L_6);
		int32_t L_7;
		L_7 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_6, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5717));
		V_2 = (bool)((((int32_t)L_7) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5718));
		bool L_8 = V_2;
		if (!L_8)
		{
			goto IL_003e;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5719));
		goto IL_0089;
	}

IL_003e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5720));
	}

IL_003f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5721));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_9 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5722));
		NullCheck(L_9);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_10;
		L_10 = DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79(L_9, 0, DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5722));
		___0_referenceItem = L_10;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5723));
	}

IL_004e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5724));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_11 = ___0_referenceItem;
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_12 = __this->___selectedLogEntry;
		DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* L_13 = __this->___timestampsOfEntriesToShow;
		if (L_13)
		{
			G_B8_0 = L_12;
			G_B8_1 = L_11;
			G_B8_2 = __this;
			goto IL_0069;
		}
		G_B7_0 = L_12;
		G_B7_1 = L_11;
		G_B7_2 = __this;
	}
	{
		il2cpp_codegen_initobj((&V_3), sizeof(Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41));
		Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 L_14 = V_3;
		G_B9_0 = L_14;
		G_B9_1 = G_B7_0;
		G_B9_2 = G_B7_1;
		G_B9_3 = G_B7_2;
		goto IL_007f;
	}

IL_0069:
	{
		DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* L_15 = __this->___timestampsOfEntriesToShow;
		int32_t L_16 = __this->___indexOfSelectedLogEntry;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5725));
		NullCheck(L_15);
		DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 L_17;
		L_17 = DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94(L_15, L_16, DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5725));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5726));
		Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 L_18;
		memset((&L_18), 0, sizeof(L_18));
		Nullable_1__ctor_m745BF2FAC35C508F889804F26D66321FE3785685((&L_18), L_17, Nullable_1__ctor_m745BF2FAC35C508F889804F26D66321FE3785685_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5726));
		G_B9_0 = L_18;
		G_B9_1 = G_B8_0;
		G_B9_2 = G_B8_1;
		G_B9_3 = G_B8_2;
	}

IL_007f:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5727));
		NullCheck(G_B9_2);
		float L_19;
		L_19 = DebugLogItem_CalculateExpandedHeight_m55309AF097A86F270D520E266A7C3ACA00BA0EFC(G_B9_2, G_B9_1, G_B9_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5727));
		NullCheck(G_B9_3);
		G_B9_3->___heightOfSelectedLogEntry = L_19;
	}

IL_0089:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5728));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, bool ___0_updateAllVisibleItemContents, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_AddFirst_m75C4229800AC7F9A99A7781E3DAC12B2FD0C75B3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_Add_mB7CBE0E63E213E436C2F09949491E47182AB0DEE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_TrimEnd_mFB28BA268BCFAFE28246145B6EA96820BFAE3BA4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D V_6;
	memset((&V_6), 0, sizeof(V_6));
	bool V_7 = false;
	bool V_8 = false;
	bool V_9 = false;
	int32_t V_10 = 0;
	int32_t V_11 = 0;
	bool V_12 = false;
	bool V_13 = false;
	int32_t V_14 = 0;
	int32_t V_15 = 0;
	bool V_16 = false;
	bool V_17 = false;
	bool V_18 = false;
	bool V_19 = false;
	int32_t V_20 = 0;
	int32_t V_21 = 0;
	bool V_22 = false;
	bool V_23 = false;
	bool V_24 = false;
	int32_t V_25 = 0;
	int32_t V_26 = 0;
	bool V_27 = false;
	bool V_28 = false;
	bool V_29 = false;
	bool V_30 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_updateAllVisibleItemContents));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_1), (&V_2), (&V_3), (&V_4), (&V_5), (&V_10), (&V_11), (&V_14), (&V_15), (&V_20), (&V_21), (&V_25), (&V_26));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5729));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5730));
	int32_t G_B14_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5731));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5732));
		DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* L_0 = __this->___entriesToShow;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5733));
		NullCheck(L_0);
		int32_t L_1;
		L_1 = DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_inline(L_0, DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5733));
		V_0 = (bool)((((int32_t)L_1) > ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5734));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_030f;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5735));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5736));
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_3 = __this->___transformComponent;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5737));
		NullCheck(L_3);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4;
		L_4 = RectTransform_get_anchoredPosition_m38F25A4253B0905BB058BE73DBF43C7172CE0680(L_3, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5737));
		float L_5 = L_4.___y;
		V_1 = ((float)il2cpp_codegen_subtract(L_5, (1.0f)));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5738));
		float L_6 = V_1;
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_7 = __this->___viewportTransform;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5739));
		NullCheck(L_7);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_8;
		L_8 = RectTransform_get_rect_mC82A60F8C3805ED9833508CCC233689641207488(L_7, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5739));
		V_6 = L_8;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5740));
		float L_9;
		L_9 = Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_inline((&V_6), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5740));
		V_2 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_6, L_9)), (2.0f)));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5741));
		int32_t L_10 = __this->___indexOfSelectedLogEntry;
		float L_11 = __this->___logItemHeight;
		V_3 = ((float)il2cpp_codegen_multiply(((float)L_10), L_11));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5742));
		float L_12 = V_3;
		float L_13 = V_2;
		V_7 = (bool)((((int32_t)((!(((float)L_12) <= ((float)L_13)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5743));
		bool L_14 = V_7;
		if (!L_14)
		{
			goto IL_00b9;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5744));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5745));
		float L_15 = V_3;
		float L_16 = V_1;
		V_8 = (bool)((((int32_t)((!(((float)L_15) <= ((float)L_16)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5746));
		bool L_17 = V_8;
		if (!L_17)
		{
			goto IL_00a3;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5747));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5748));
		float L_18 = V_1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5749));
		float L_19;
		L_19 = DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5749));
		float L_20 = V_3;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5750));
		float L_21;
		L_21 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline(((float)il2cpp_codegen_subtract(L_18, L_19)), ((float)il2cpp_codegen_subtract(L_20, (1.0f))), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5750));
		V_1 = L_21;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5751));
		float L_22 = V_2;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5752));
		float L_23;
		L_23 = DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5752));
		float L_24 = V_1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5753));
		float L_25;
		L_25 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline(((float)il2cpp_codegen_subtract(L_22, L_23)), ((float)il2cpp_codegen_add(L_24, (2.0f))), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5753));
		V_2 = L_25;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5754));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5755));
		goto IL_00b8;
	}

IL_00a3:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5756));
		float L_26 = V_2;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5757));
		float L_27;
		L_27 = DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5757));
		float L_28 = V_3;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5758));
		float L_29;
		L_29 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline(((float)il2cpp_codegen_subtract(L_26, L_27)), ((float)il2cpp_codegen_add(L_28, (1.0f))), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5758));
		V_2 = L_29;
	}

IL_00b8:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5759));
	}

IL_00b9:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5760));
		float L_30 = V_2;
		float L_31 = __this->___logItemHeight;
		DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* L_32 = __this->___entriesToShow;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5761));
		NullCheck(L_32);
		int32_t L_33;
		L_33 = DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_inline(L_32, DynamicCircularBuffer_1_get_Count_m5109872097ECE2F6E5122F75718E9CEC5C9C6BC5_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5761));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5762));
		int32_t L_34;
		L_34 = Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_inline(il2cpp_codegen_cast_double_to_int<int32_t>(((float)(L_30/L_31))), ((int32_t)il2cpp_codegen_subtract(L_33, 1)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5762));
		V_4 = L_34;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5763));
		float L_35 = V_1;
		float L_36 = __this->___logItemHeight;
		int32_t L_37 = V_4;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5764));
		int32_t L_38;
		L_38 = Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_inline(il2cpp_codegen_cast_double_to_int<int32_t>(((float)(L_35/L_36))), 0, L_37, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5764));
		V_5 = L_38;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5765));
		int32_t L_39 = __this->___currentTopIndex;
		V_9 = (bool)((((int32_t)L_39) == ((int32_t)(-1)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5766));
		bool L_40 = V_9;
		if (!L_40)
		{
			goto IL_0139;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5767));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5768));
		___0_updateAllVisibleItemContents = (bool)1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5769));
		V_10 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5770));
		int32_t L_41 = V_4;
		int32_t L_42 = V_5;
		V_11 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_subtract(L_41, L_42)), 1));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5771));
		goto IL_0127;
	}

IL_010a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5772));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_43 = __this->___visibleLogItems;
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_44 = __this->___manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5773));
		NullCheck(L_44);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_45;
		L_45 = DebugLogManager_PopLogItem_m854ED51B251AD048C1097A5BF3F6E8518504E0EA(L_44, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5773));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5774));
		NullCheck(L_43);
		DynamicCircularBuffer_1_Add_mB7CBE0E63E213E436C2F09949491E47182AB0DEE(L_43, L_45, DynamicCircularBuffer_1_Add_mB7CBE0E63E213E436C2F09949491E47182AB0DEE_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5774));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5775));
		int32_t L_46 = V_10;
		V_10 = ((int32_t)il2cpp_codegen_add(L_46, 1));
	}

IL_0127:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5776));
		int32_t L_47 = V_10;
		int32_t L_48 = V_11;
		V_12 = (bool)((((int32_t)L_47) < ((int32_t)L_48))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5777));
		bool L_49 = V_12;
		if (L_49)
		{
			goto IL_010a;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5778));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5779));
		goto IL_02de;
	}

IL_0139:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5780));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5781));
		int32_t L_50 = V_4;
		int32_t L_51 = __this->___currentTopIndex;
		if ((((int32_t)L_50) < ((int32_t)L_51)))
		{
			goto IL_0150;
		}
	}
	{
		int32_t L_52 = V_5;
		int32_t L_53 = __this->___currentBottomIndex;
		G_B14_0 = ((((int32_t)L_52) > ((int32_t)L_53))? 1 : 0);
		goto IL_0151;
	}

IL_0150:
	{
		G_B14_0 = 1;
	}

IL_0151:
	{
		V_13 = (bool)G_B14_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5782));
		bool L_54 = V_13;
		if (!L_54)
		{
			goto IL_01b5;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5783));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5784));
		___0_updateAllVisibleItemContents = (bool)1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5785));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_55 = __this->___visibleLogItems;
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_56 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5786));
		NullCheck(L_56);
		int32_t L_57;
		L_57 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_56, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5786));
		Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* L_58 = __this->___poolLogItemAction;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5787));
		NullCheck(L_55);
		DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08(L_55, L_57, L_58, DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5787));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5788));
		V_14 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5789));
		int32_t L_59 = V_4;
		int32_t L_60 = V_5;
		V_15 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_subtract(L_59, L_60)), 1));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5790));
		goto IL_01a3;
	}

IL_0186:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5791));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_61 = __this->___visibleLogItems;
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_62 = __this->___manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5792));
		NullCheck(L_62);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_63;
		L_63 = DebugLogManager_PopLogItem_m854ED51B251AD048C1097A5BF3F6E8518504E0EA(L_62, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5792));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5793));
		NullCheck(L_61);
		DynamicCircularBuffer_1_Add_mB7CBE0E63E213E436C2F09949491E47182AB0DEE(L_61, L_63, DynamicCircularBuffer_1_Add_mB7CBE0E63E213E436C2F09949491E47182AB0DEE_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5793));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5794));
		int32_t L_64 = V_14;
		V_14 = ((int32_t)il2cpp_codegen_add(L_64, 1));
	}

IL_01a3:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5795));
		int32_t L_65 = V_14;
		int32_t L_66 = V_15;
		V_16 = (bool)((((int32_t)L_65) < ((int32_t)L_66))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5796));
		bool L_67 = V_16;
		if (L_67)
		{
			goto IL_0186;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5797));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5798));
		goto IL_02dd;
	}

IL_01b5:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5799));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5800));
		int32_t L_68 = V_5;
		int32_t L_69 = __this->___currentTopIndex;
		V_17 = (bool)((((int32_t)L_68) > ((int32_t)L_69))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5801));
		bool L_70 = V_17;
		if (!L_70)
		{
			goto IL_01e1;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5802));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_71 = __this->___visibleLogItems;
		int32_t L_72 = V_5;
		int32_t L_73 = __this->___currentTopIndex;
		Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* L_74 = __this->___poolLogItemAction;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5803));
		NullCheck(L_71);
		DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08(L_71, ((int32_t)il2cpp_codegen_subtract(L_72, L_73)), L_74, DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5803));
	}

IL_01e1:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5804));
		int32_t L_75 = V_4;
		int32_t L_76 = __this->___currentBottomIndex;
		V_18 = (bool)((((int32_t)L_75) < ((int32_t)L_76))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5805));
		bool L_77 = V_18;
		if (!L_77)
		{
			goto IL_020c;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5806));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_78 = __this->___visibleLogItems;
		int32_t L_79 = __this->___currentBottomIndex;
		int32_t L_80 = V_4;
		Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* L_81 = __this->___poolLogItemAction;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5807));
		NullCheck(L_78);
		DynamicCircularBuffer_1_TrimEnd_mFB28BA268BCFAFE28246145B6EA96820BFAE3BA4(L_78, ((int32_t)il2cpp_codegen_subtract(L_79, L_80)), L_81, DynamicCircularBuffer_1_TrimEnd_mFB28BA268BCFAFE28246145B6EA96820BFAE3BA4_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5807));
	}

IL_020c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5808));
		int32_t L_82 = V_5;
		int32_t L_83 = __this->___currentTopIndex;
		V_19 = (bool)((((int32_t)L_82) < ((int32_t)L_83))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5809));
		bool L_84 = V_19;
		if (!L_84)
		{
			goto IL_0274;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5810));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5811));
		V_20 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5812));
		int32_t L_85 = __this->___currentTopIndex;
		int32_t L_86 = V_5;
		V_21 = ((int32_t)il2cpp_codegen_subtract(L_85, L_86));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5813));
		goto IL_024a;
	}

IL_022d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5814));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_87 = __this->___visibleLogItems;
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_88 = __this->___manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5815));
		NullCheck(L_88);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_89;
		L_89 = DebugLogManager_PopLogItem_m854ED51B251AD048C1097A5BF3F6E8518504E0EA(L_88, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5815));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5816));
		NullCheck(L_87);
		DynamicCircularBuffer_1_AddFirst_m75C4229800AC7F9A99A7781E3DAC12B2FD0C75B3(L_87, L_89, DynamicCircularBuffer_1_AddFirst_m75C4229800AC7F9A99A7781E3DAC12B2FD0C75B3_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5816));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5817));
		int32_t L_90 = V_20;
		V_20 = ((int32_t)il2cpp_codegen_add(L_90, 1));
	}

IL_024a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5818));
		int32_t L_91 = V_20;
		int32_t L_92 = V_21;
		V_22 = (bool)((((int32_t)L_91) < ((int32_t)L_92))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5819));
		bool L_93 = V_22;
		if (L_93)
		{
			goto IL_022d;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5820));
		bool L_94 = ___0_updateAllVisibleItemContents;
		V_23 = (bool)((((int32_t)L_94) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5821));
		bool L_95 = V_23;
		if (!L_95)
		{
			goto IL_0273;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5822));
		int32_t L_96 = V_5;
		int32_t L_97 = __this->___currentTopIndex;
		int32_t L_98 = V_5;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5823));
		DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71(__this, L_96, ((int32_t)il2cpp_codegen_subtract(L_97, 1)), L_98, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5823));
	}

IL_0273:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5824));
	}

IL_0274:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5825));
		int32_t L_99 = V_4;
		int32_t L_100 = __this->___currentBottomIndex;
		V_24 = (bool)((((int32_t)L_99) > ((int32_t)L_100))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5826));
		bool L_101 = V_24;
		if (!L_101)
		{
			goto IL_02dc;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5827));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5828));
		V_25 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5829));
		int32_t L_102 = V_4;
		int32_t L_103 = __this->___currentBottomIndex;
		V_26 = ((int32_t)il2cpp_codegen_subtract(L_102, L_103));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5830));
		goto IL_02b2;
	}

IL_0295:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5831));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_104 = __this->___visibleLogItems;
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_105 = __this->___manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5832));
		NullCheck(L_105);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_106;
		L_106 = DebugLogManager_PopLogItem_m854ED51B251AD048C1097A5BF3F6E8518504E0EA(L_105, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5832));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5833));
		NullCheck(L_104);
		DynamicCircularBuffer_1_Add_mB7CBE0E63E213E436C2F09949491E47182AB0DEE(L_104, L_106, DynamicCircularBuffer_1_Add_mB7CBE0E63E213E436C2F09949491E47182AB0DEE_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5833));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5834));
		int32_t L_107 = V_25;
		V_25 = ((int32_t)il2cpp_codegen_add(L_107, 1));
	}

IL_02b2:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5835));
		int32_t L_108 = V_25;
		int32_t L_109 = V_26;
		V_27 = (bool)((((int32_t)L_108) < ((int32_t)L_109))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5836));
		bool L_110 = V_27;
		if (L_110)
		{
			goto IL_0295;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5837));
		bool L_111 = ___0_updateAllVisibleItemContents;
		V_28 = (bool)((((int32_t)L_111) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5838));
		bool L_112 = V_28;
		if (!L_112)
		{
			goto IL_02db;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5839));
		int32_t L_113 = __this->___currentBottomIndex;
		int32_t L_114 = V_4;
		int32_t L_115 = V_5;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5840));
		DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71(__this, ((int32_t)il2cpp_codegen_add(L_113, 1)), L_114, L_115, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5840));
	}

IL_02db:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5841));
	}

IL_02dc:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5842));
	}

IL_02dd:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5843));
	}

IL_02de:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5844));
		int32_t L_116 = V_5;
		__this->___currentTopIndex = L_116;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5845));
		int32_t L_117 = V_4;
		__this->___currentBottomIndex = L_117;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5846));
		bool L_118 = ___0_updateAllVisibleItemContents;
		V_29 = L_118;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5847));
		bool L_119 = V_29;
		if (!L_119)
		{
			goto IL_030c;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5848));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5849));
		int32_t L_120 = __this->___currentTopIndex;
		int32_t L_121 = __this->___currentBottomIndex;
		int32_t L_122 = V_5;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5850));
		DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71(__this, L_120, L_121, L_122, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5850));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5851));
	}

IL_030c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5852));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5853));
		goto IL_0347;
	}

IL_030f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5854));
		int32_t L_123 = __this->___currentTopIndex;
		V_30 = (bool)((((int32_t)((((int32_t)L_123) == ((int32_t)(-1)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5855));
		bool L_124 = V_30;
		if (!L_124)
		{
			goto IL_0347;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5856));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5857));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_125 = __this->___visibleLogItems;
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_126 = __this->___visibleLogItems;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5858));
		NullCheck(L_126);
		int32_t L_127;
		L_127 = DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_inline(L_126, DynamicCircularBuffer_1_get_Count_m61381C7D5B32F7A35269D49253AFCD7175C6551F_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5858));
		Action_1_t7699BDD2A7D3578F599E23FE0EC004B74E2296D6* L_128 = __this->___poolLogItemAction;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5859));
		NullCheck(L_125);
		DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08(L_125, L_127, L_128, DynamicCircularBuffer_1_TrimStart_m33D5A748B2DC0D0FDEE2975FEB2C6A8305A15E08_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5859));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5860));
		__this->___currentTopIndex = (-1);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5861));
	}

IL_0347:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5862));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* DebugLogRecycledListView_GetLogItemAtIndex_m701E9237DDEC23011F430F2B47D52B573DC34BAD (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_GetLogItemAtIndex_m701E9237DDEC23011F430F2B47D52B573DC34BAD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* V_0 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_index));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_GetLogItemAtIndex_m701E9237DDEC23011F430F2B47D52B573DC34BAD_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5863));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5864));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5865));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5866));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_0 = __this->___visibleLogItems;
		int32_t L_1 = ___0_index;
		int32_t L_2 = __this->___currentTopIndex;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5867));
		NullCheck(L_0);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_3;
		L_3 = DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79(L_0, ((int32_t)il2cpp_codegen_subtract(L_1, L_2)), DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5867));
		V_0 = L_3;
		goto IL_0017;
	}

IL_0017:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5868));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_4 = V_0;
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, int32_t ___0_topIndex, int32_t ___1_bottomIndex, int32_t ___2_logItemOffset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1__ctor_m745BF2FAC35C508F889804F26D66321FE3785685_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* V_1 = NULL;
	Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 V_2;
	memset((&V_2), 0, sizeof(V_2));
	bool V_3 = false;
	bool V_4 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_topIndex), (&___1_bottomIndex), (&___2_logItemOffset));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0), (&V_1));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5869));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5870));
	DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* G_B3_0 = NULL;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* G_B3_1 = NULL;
	DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* G_B2_0 = NULL;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* G_B2_1 = NULL;
	Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 G_B4_0;
	memset((&G_B4_0), 0, sizeof(G_B4_0));
	DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* G_B4_1 = NULL;
	DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* G_B4_2 = NULL;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5871));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5872));
		int32_t L_0 = ___0_topIndex;
		V_0 = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5873));
		goto IL_0088;
	}

IL_0008:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5874));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5875));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_1 = __this->___visibleLogItems;
		int32_t L_2 = V_0;
		int32_t L_3 = ___2_logItemOffset;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5876));
		NullCheck(L_1);
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_4;
		L_4 = DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79(L_1, ((int32_t)il2cpp_codegen_subtract(L_2, L_3)), DynamicCircularBuffer_1_get_Item_m0B4F079D8511EC8E7716B1627077CC00BC4C5E79_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5876));
		V_1 = L_4;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5877));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_5 = V_1;
		DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4* L_6 = __this->___entriesToShow;
		int32_t L_7 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5878));
		NullCheck(L_6);
		DebugLogEntry_tC2ACB64C8D5A84F9683A36DFD7F1532D1EADF5CD* L_8;
		L_8 = DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4(L_6, L_7, DynamicCircularBuffer_1_get_Item_m5CF4225D182DA548535A6198A048EA58C95720A4_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5878));
		DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* L_9 = __this->___timestampsOfEntriesToShow;
		if (L_9)
		{
			G_B3_0 = L_8;
			G_B3_1 = L_5;
			goto IL_0038;
		}
		G_B2_0 = L_8;
		G_B2_1 = L_5;
	}
	{
		il2cpp_codegen_initobj((&V_2), sizeof(Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41));
		Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 L_10 = V_2;
		G_B4_0 = L_10;
		G_B4_1 = G_B2_0;
		G_B4_2 = G_B2_1;
		goto IL_0049;
	}

IL_0038:
	{
		DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA* L_11 = __this->___timestampsOfEntriesToShow;
		int32_t L_12 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5879));
		NullCheck(L_11);
		DebugLogEntryTimestamp_t30582C9C3760265FDEF8895133213267986BFA97 L_13;
		L_13 = DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94(L_11, L_12, DynamicCircularBuffer_1_get_Item_mFC097A8A3718066586CC1E891AFF84CB1A8CAA94_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5879));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5880));
		Nullable_1_tE058FF1EF75E02B10E5B388F885D29B83C8F2B41 L_14;
		memset((&L_14), 0, sizeof(L_14));
		Nullable_1__ctor_m745BF2FAC35C508F889804F26D66321FE3785685((&L_14), L_13, Nullable_1__ctor_m745BF2FAC35C508F889804F26D66321FE3785685_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5880));
		G_B4_0 = L_14;
		G_B4_1 = G_B3_0;
		G_B4_2 = G_B3_1;
	}

IL_0049:
	{
		int32_t L_15 = V_0;
		int32_t L_16 = V_0;
		int32_t L_17 = __this->___indexOfSelectedLogEntry;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5881));
		NullCheck(G_B4_2);
		DebugLogItem_SetContent_mAA060346F58906FAA676D025BF7B440DEBA1C7E3(G_B4_2, G_B4_1, G_B4_0, L_15, (bool)((((int32_t)L_16) == ((int32_t)L_17))? 1 : 0), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5881));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5882));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_18 = V_1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5883));
		DebugLogRecycledListView_RepositionLogItem_m6A7A36C93FBA0C44560F9D9556C343F5EA052072(__this, L_18, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5883));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5884));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_19 = V_1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5885));
		DebugLogRecycledListView_ColorLogItem_mC1A80473D95AFCE3DC87E38DCABEB90096C1D2A7(__this, L_19, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5885));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5886));
		bool L_20 = __this->___isCollapseOn;
		V_3 = L_20;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5887));
		bool L_21 = V_3;
		if (!L_21)
		{
			goto IL_007c;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5888));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_22 = V_1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5889));
		NullCheck(L_22);
		DebugLogItem_ShowCount_m370DD836F4F51DE3090FBA4E46F0D5DAE4F6E7A4(L_22, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5889));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5890));
		goto IL_0083;
	}

IL_007c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5891));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_23 = V_1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5892));
		NullCheck(L_23);
		DebugLogItem_HideCount_m74445C719C5941D4DD597C50988526E5FAF7DBD2(L_23, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5892));
	}

IL_0083:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5893));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5894));
		int32_t L_24 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_24, 1));
	}

IL_0088:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5895));
		int32_t L_25 = V_0;
		int32_t L_26 = ___1_bottomIndex;
		V_4 = (bool)((((int32_t)((((int32_t)L_25) > ((int32_t)L_26))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5896));
		bool L_27 = V_4;
		if (L_27)
		{
			goto IL_0008;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5897));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_RepositionLogItem_m6A7A36C93FBA0C44560F9D9556C343F5EA052072 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_logItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_RepositionLogItem_m6A7A36C93FBA0C44560F9D9556C343F5EA052072_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_1;
	memset((&V_1), 0, sizeof(V_1));
	bool V_2 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_logItem));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0), (&V_1));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_RepositionLogItem_m6A7A36C93FBA0C44560F9D9556C343F5EA052072_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5898));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5899));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5900));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5901));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_0 = ___0_logItem;
		NullCheck(L_0);
		int32_t L_1 = L_0->___Index;
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5902));
		int32_t L_2 = V_0;
		float L_3 = __this->___logItemHeight;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5903));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&V_1), (1.0f), ((float)il2cpp_codegen_multiply(((float)((-L_2))), L_3)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5903));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5904));
		int32_t L_4 = V_0;
		int32_t L_5 = __this->___indexOfSelectedLogEntry;
		V_2 = (bool)((((int32_t)L_4) > ((int32_t)L_5))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5905));
		bool L_6 = V_2;
		if (!L_6)
		{
			goto IL_003c;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5906));
		float* L_7 = (float*)(&(&V_1)->___y);
		float* L_8 = L_7;
		float L_9 = *((float*)L_8);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5907));
		float L_10;
		L_10 = DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5907));
		*((float*)L_8) = (float)((float)il2cpp_codegen_subtract(L_9, L_10));
	}

IL_003c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5908));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_11 = ___0_logItem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5909));
		NullCheck(L_11);
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_12;
		L_12 = DebugLogItem_get_Transform_mBAA5D6E01F83683FF29CE440C3D2F4D5616E6466(L_11, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5909));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_13 = V_1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5910));
		NullCheck(L_12);
		RectTransform_set_anchoredPosition_mF903ACE04F6959B1CD67E2B94FABC0263068F965(L_12, L_13, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5910));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5911));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_ColorLogItem_mC1A80473D95AFCE3DC87E38DCABEB90096C1D2A7 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* ___0_logItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_ColorLogItem_mC1A80473D95AFCE3DC87E38DCABEB90096C1D2A7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	bool V_1 = false;
	bool V_2 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_logItem));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_ColorLogItem_mC1A80473D95AFCE3DC87E38DCABEB90096C1D2A7_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5912));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5913));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5914));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5915));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_0 = ___0_logItem;
		NullCheck(L_0);
		int32_t L_1 = L_0->___Index;
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5916));
		int32_t L_2 = V_0;
		int32_t L_3 = __this->___indexOfSelectedLogEntry;
		V_1 = (bool)((((int32_t)L_2) == ((int32_t)L_3))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5917));
		bool L_4 = V_1;
		if (!L_4)
		{
			goto IL_0029;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5918));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_5 = ___0_logItem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5919));
		NullCheck(L_5);
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_6;
		L_6 = DebugLogItem_get_Image_m19E45CDC961A393E891617E0AE8196EEC60827E2(L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5919));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_7 = __this->___logItemSelectedColor;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5920));
		NullCheck(L_6);
		VirtualActionInvoker1< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(23, L_6, L_7);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5920));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5921));
		goto IL_0059;
	}

IL_0029:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5922));
		int32_t L_8 = V_0;
		V_2 = (bool)((((int32_t)((int32_t)(L_8%2))) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5923));
		bool L_9 = V_2;
		if (!L_9)
		{
			goto IL_0047;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5924));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_10 = ___0_logItem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5925));
		NullCheck(L_10);
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_11;
		L_11 = DebugLogItem_get_Image_m19E45CDC961A393E891617E0AE8196EEC60827E2(L_10, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5925));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12 = __this->___logItemNormalColor1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5926));
		NullCheck(L_11);
		VirtualActionInvoker1< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(23, L_11, L_12);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5926));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5927));
		goto IL_0059;
	}

IL_0047:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5928));
		DebugLogItem_t88B4590FC214B8270C22089685CFF96ED51D5230* L_13 = ___0_logItem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5929));
		NullCheck(L_13);
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_14;
		L_14 = DebugLogItem_get_Image_m19E45CDC961A393E891617E0AE8196EEC60827E2(L_13, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5929));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_15 = __this->___logItemNormalColor2;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5930));
		NullCheck(L_14);
		VirtualActionInvoker1< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(23, L_14, L_15);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5930));
	}

IL_0059:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5931));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView__ctor_m187BC9A3FCE5156FCDA97401BF5431EB4BAC164A (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView__ctor_m187BC9A3FCE5156FCDA97401BF5431EB4BAC164A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1__ctor_mBE7966474BC269763705CA36F37661885024A65A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView__ctor_m187BC9A3FCE5156FCDA97401BF5431EB4BAC164A_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5932));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5933));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5934));
		__this->___entriesToShow = (DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___entriesToShow), (void*)(DynamicCircularBuffer_1_t62F500FF82205FA04BA17D9D6908B913774D4CF4*)NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5935));
		__this->___timestampsOfEntriesToShow = (DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___timestampsOfEntriesToShow), (void*)(DynamicCircularBuffer_1_t64462704C0D9E57163662FF29719C92F9B1C9BEA*)NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5936));
		__this->___indexOfSelectedLogEntry = ((int32_t)2147483647LL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5937));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5938));
		DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF* L_0 = (DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF*)il2cpp_codegen_object_new(DynamicCircularBuffer_1_t69CDB62CCA5836D7EB0259924A91A1F59B9203EF_il2cpp_TypeInfo_var);
		DynamicCircularBuffer_1__ctor_mBE7966474BC269763705CA36F37661885024A65A(L_0, ((int32_t)32), DynamicCircularBuffer_1__ctor_mBE7966474BC269763705CA36F37661885024A65A_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5938));
		__this->___visibleLogItems = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___visibleLogItems), (void*)L_0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5939));
		__this->___isCollapseOn = (bool)0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5940));
		__this->___currentTopIndex = (-1);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5941));
		__this->___currentBottomIndex = (-1);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5942));
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5942));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogRecycledListView_U3CAwakeU3Eb__25_0_m46F3DED084709344721E369E0D3AC052735594A5 (DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_pos, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_U3CAwakeU3Eb__25_0_m46F3DED084709344721E369E0D3AC052735594A5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogRecycledListView_t0A7F681EE9C1FB66CB45C36B2F919B018A18FEE2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_pos));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogRecycledListView_U3CAwakeU3Eb__25_0_m46F3DED084709344721E369E0D3AC052735594A5_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5943));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5944));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5945));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5946));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_0 = __this->___manager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5947));
		NullCheck(L_0);
		bool L_1;
		L_1 = DebugLogManager_get_IsLogWindowVisible_mA4654B1326D4234C4754F811A845C9C57E7F8F54(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5947));
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5948));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_0018;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5949));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5950));
		DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA(__this, (bool)0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5950));
	}

IL_0018:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5951));
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogResizeListener_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mA23891E13947BF5553F8E9463958B5733DCDAEAF (DebugLogResizeListener_tAEB6FDAE1D37AD856CBD82204674E6AC4C1764B5* __this, PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB* ___0_eventData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogResizeListener_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mA23891E13947BF5553F8E9463958B5733DCDAEAF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogResizeListener_tAEB6FDAE1D37AD856CBD82204674E6AC4C1764B5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_eventData));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogResizeListener_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mA23891E13947BF5553F8E9463958B5733DCDAEAF_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5952));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5953));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5954));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5955));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogResizeListener_UnityEngine_EventSystems_IDragHandler_OnDrag_m34D99B83889FBD661DE942A7C59522A8ABB0AD3A (DebugLogResizeListener_tAEB6FDAE1D37AD856CBD82204674E6AC4C1764B5* __this, PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB* ___0_eventData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogResizeListener_UnityEngine_EventSystems_IDragHandler_OnDrag_m34D99B83889FBD661DE942A7C59522A8ABB0AD3A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogResizeListener_tAEB6FDAE1D37AD856CBD82204674E6AC4C1764B5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_eventData));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogResizeListener_UnityEngine_EventSystems_IDragHandler_OnDrag_m34D99B83889FBD661DE942A7C59522A8ABB0AD3A_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5956));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5957));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5958));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5959));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_0 = __this->___debugManager;
		PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB* L_1 = ___0_eventData;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5960));
		NullCheck(L_0);
		DebugLogManager_Resize_m0F28C37B4111332354C18770A29BD3D3277DD0E4(L_0, L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5960));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5961));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugLogResizeListener__ctor_m9891E2D11D53F3900EB79C88301C937AD4FB8B33 (DebugLogResizeListener_tAEB6FDAE1D37AD856CBD82204674E6AC4C1764B5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugLogResizeListener__ctor_m9891E2D11D53F3900EB79C88301C937AD4FB8B33_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugLogResizeListener__ctor_m9891E2D11D53F3900EB79C88301C937AD4FB8B33_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugsOnScrollListener_OnScroll_m12A82295EEFBA395210BFDF1C80BA59B7438D8F2 (DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0* __this, PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB* ___0_data, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_OnScroll_m12A82295EEFBA395210BFDF1C80BA59B7438D8F2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_data));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugsOnScrollListener_OnScroll_m12A82295EEFBA395210BFDF1C80BA59B7438D8F2_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5962));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5963));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5964));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5965));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_0 = __this->___debugLogManager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5966));
		bool L_1;
		L_1 = DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5966));
		NullCheck(L_0);
		L_0->___SnapToBottom = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5967));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugsOnScrollListener_OnBeginDrag_mDA693E6910C47EFFFB1C9E749BACF843BE7680DB (DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0* __this, PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB* ___0_data, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_OnBeginDrag_mDA693E6910C47EFFFB1C9E749BACF843BE7680DB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_data));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugsOnScrollListener_OnBeginDrag_mDA693E6910C47EFFFB1C9E749BACF843BE7680DB_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5968));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5969));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5970));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5971));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_0 = __this->___debugLogManager;
		NullCheck(L_0);
		L_0->___SnapToBottom = (bool)0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5972));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugsOnScrollListener_OnEndDrag_m73CCC8AC42E0FC185F490DE8829D51554E59968E (DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0* __this, PointerEventData_t9670F3C7D823CCB738A1604C72A1EB90292396FB* ___0_data, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_OnEndDrag_m73CCC8AC42E0FC185F490DE8829D51554E59968E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_data));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugsOnScrollListener_OnEndDrag_m73CCC8AC42E0FC185F490DE8829D51554E59968E_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5973));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5974));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5975));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5976));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_0 = __this->___debugLogManager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5977));
		bool L_1;
		L_1 = DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5977));
		NullCheck(L_0);
		L_0->___SnapToBottom = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5978));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugsOnScrollListener_OnScrollbarDragStart_mC24D5BD9F61D0BC49F91808A5D9B20E59A13D30E (DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0* __this, BaseEventData_tE03A848325C0AE8E76C6CA15FD86395EBF83364F* ___0_data, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_OnScrollbarDragStart_mC24D5BD9F61D0BC49F91808A5D9B20E59A13D30E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_data));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugsOnScrollListener_OnScrollbarDragStart_mC24D5BD9F61D0BC49F91808A5D9B20E59A13D30E_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5979));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5980));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5981));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5982));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_0 = __this->___debugLogManager;
		NullCheck(L_0);
		L_0->___SnapToBottom = (bool)0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5983));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugsOnScrollListener_OnScrollbarDragEnd_mB867BC08242DE9B5EDD968AB6A01DBA5F96626F9 (DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0* __this, BaseEventData_tE03A848325C0AE8E76C6CA15FD86395EBF83364F* ___0_data, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_OnScrollbarDragEnd_mB867BC08242DE9B5EDD968AB6A01DBA5F96626F9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_data));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugsOnScrollListener_OnScrollbarDragEnd_mB867BC08242DE9B5EDD968AB6A01DBA5F96626F9_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5984));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5985));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5986));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5987));
		DebugLogManager_t4D04EF5680196548275960FC70628E2E857857A8* L_0 = __this->___debugLogManager;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5988));
		bool L_1;
		L_1 = DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5988));
		NullCheck(L_0);
		L_0->___SnapToBottom = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5989));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D (DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5990));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5991));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5992));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5993));
		ScrollRect_t17D2F2939CA8953110180DF53164CFC3DC88D70E* L_0 = __this->___debugsScrollRect;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5994));
		NullCheck(L_0);
		float L_1;
		L_1 = ScrollRect_get_verticalNormalizedPosition_m4FE766F04272C1805FDE2A4B72D80F6190841FA1(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5994));
		V_0 = (bool)((((int32_t)((!(((float)L_1) <= ((float)(9.99999997E-07f))))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_0019;
	}

IL_0019:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5995));
		bool L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebugsOnScrollListener__ctor_mC599E1B72DE41CF49476C9F2C121DDAE4B9F4B1F (DebugsOnScrollListener_t2E15E040AD7FE915FC4F583966EBDE135A4C0DB0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DebugsOnScrollListener__ctor_mC599E1B72DE41CF49476C9F2C121DDAE4B9F4B1F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DebugsOnScrollListener__ctor_mC599E1B72DE41CF49476C9F2C121DDAE4B9F4B1F_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventSystemHandler_OnEnable_mBE644BBFCA0529C37CB7C4BDEAA166858EF1545E (EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_OnEnable_mBE644BBFCA0529C37CB7C4BDEAA166858EF1545E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EventSystemHandler_OnEnable_mBE644BBFCA0529C37CB7C4BDEAA166858EF1545E_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5996));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5997));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5998));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 5999));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6000));
		UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A* L_0 = (UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A*)il2cpp_codegen_object_new(UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A_il2cpp_TypeInfo_var);
		UnityAction_2__ctor_m0E0C01B7056EB1CB1E6C6F4FC457EBCA3F6B0041(L_0, __this, (intptr_t)((void*)EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6000));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6001));
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_remove_sceneLoaded_m72A7C2A1B8EF1C21A208A9A015375577768B3978(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6001));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6002));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6003));
		UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A* L_1 = (UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A*)il2cpp_codegen_object_new(UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A_il2cpp_TypeInfo_var);
		UnityAction_2__ctor_m0E0C01B7056EB1CB1E6C6F4FC457EBCA3F6B0041(L_1, __this, (intptr_t)((void*)EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6003));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6004));
		SceneManager_add_sceneLoaded_m14BEBCC5E4A8DD2C806A48D79A4773315CB434C6(L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6004));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6005));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6006));
		UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C* L_2 = (UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C*)il2cpp_codegen_object_new(UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C_il2cpp_TypeInfo_var);
		UnityAction_1__ctor_m3D196ADE59DE13B9FDC5D827B1A6D00CBEF1F6DF(L_2, __this, (intptr_t)((void*)EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6006));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6007));
		SceneManager_remove_sceneUnloaded_m2CACDB3F47DED2C92E6AA1912906F7E2C61424EB(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6007));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6008));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6009));
		UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C* L_3 = (UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C*)il2cpp_codegen_object_new(UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C_il2cpp_TypeInfo_var);
		UnityAction_1__ctor_m3D196ADE59DE13B9FDC5D827B1A6D00CBEF1F6DF(L_3, __this, (intptr_t)((void*)EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6009));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6010));
		SceneManager_add_sceneUnloaded_mC3BAE77FFFA0DBA3F6EE3303CA78400A3932F029(L_3, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6010));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6011));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6012));
		EventSystemHandler_ActivateEventSystemIfNeeded_m162A2AECC9C9CB151849DD89B25485FAFC30313B(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6012));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6013));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventSystemHandler_OnDisable_mEDA7BEC448D249FC070F53363E935967AF5FB97F (EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_OnDisable_mEDA7BEC448D249FC070F53363E935967AF5FB97F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EventSystemHandler_OnDisable_mEDA7BEC448D249FC070F53363E935967AF5FB97F_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6014));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6015));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6016));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6017));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6018));
		UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A* L_0 = (UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A*)il2cpp_codegen_object_new(UnityAction_2_t1C08AEB5AA4F72FEFAB7F303E33C8CFFF80A8C3A_il2cpp_TypeInfo_var);
		UnityAction_2__ctor_m0E0C01B7056EB1CB1E6C6F4FC457EBCA3F6B0041(L_0, __this, (intptr_t)((void*)EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6018));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6019));
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_remove_sceneLoaded_m72A7C2A1B8EF1C21A208A9A015375577768B3978(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6019));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6020));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6021));
		UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C* L_1 = (UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C*)il2cpp_codegen_object_new(UnityAction_1_t9AA21AF4EE824B324F3F3897F91A2D460437F62C_il2cpp_TypeInfo_var);
		UnityAction_1__ctor_m3D196ADE59DE13B9FDC5D827B1A6D00CBEF1F6DF(L_1, __this, (intptr_t)((void*)EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6021));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6022));
		SceneManager_remove_sceneUnloaded_m2CACDB3F47DED2C92E6AA1912906F7E2C61424EB(L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6022));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6023));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6024));
		EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6024));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6025));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030 (EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E* __this, Scene_tA1DC762B79745EB5140F054C884855B922318356 ___0_scene, int32_t ___1_mode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_scene), (&___1_mode));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6026));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6027));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6028));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6029));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6030));
		EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6030));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6031));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6032));
		EventSystemHandler_ActivateEventSystemIfNeeded_m162A2AECC9C9CB151849DD89B25485FAFC30313B(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6032));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6033));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F (EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E* __this, Scene_tA1DC762B79745EB5140F054C884855B922318356 ___0_current, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_current));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6034));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6035));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6036));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6037));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6038));
		EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6038));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6039));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventSystemHandler_ActivateEventSystemIfNeeded_m162A2AECC9C9CB151849DD89B25485FAFC30313B (EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_ActivateEventSystemIfNeeded_m162A2AECC9C9CB151849DD89B25485FAFC30313B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EventSystemHandler_ActivateEventSystemIfNeeded_m162A2AECC9C9CB151849DD89B25485FAFC30313B_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6040));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6041));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6042));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6043));
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0 = __this->___embeddedEventSystem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6044));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6044));
		if (!L_1)
		{
			goto IL_001d;
		}
	}
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6045));
		il2cpp_codegen_runtime_class_init_inline(EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707_il2cpp_TypeInfo_var);
		EventSystem_t61C51380B105BE9D2C39C4F15B7E655659957707* L_2;
		L_2 = EventSystem_get_current_mC87C69FB418563DC2A571A10E2F9DB59A6785016(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6045));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6046));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6046));
		G_B3_0 = ((((int32_t)L_3) == ((int32_t)0))? 1 : 0);
		goto IL_001e;
	}

IL_001d:
	{
		G_B3_0 = 0;
	}

IL_001e:
	{
		V_0 = (bool)G_B3_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6047));
		bool L_4 = V_0;
		if (!L_4)
		{
			goto IL_002f;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6048));
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_5 = __this->___embeddedEventSystem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6049));
		NullCheck(L_5);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_5, (bool)1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6049));
	}

IL_002f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6050));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8 (EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6051));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6052));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6053));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6054));
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0 = __this->___embeddedEventSystem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6055));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6055));
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6056));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001d;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6057));
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3 = __this->___embeddedEventSystem;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6058));
		NullCheck(L_3);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_3, (bool)0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6058));
	}

IL_001d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 6059));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventSystemHandler__ctor_m2FD41F48C7ED26C15E12B40EDFBAAC64C9A40448 (EventSystemHandler_t682DE68ECD4C260AC11B771D2754D93C88E2AB5E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventSystemHandler__ctor_m2FD41F48C7ED26C15E12B40EDFBAAC64C9A40448_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EventSystemHandler__ctor_m2FD41F48C7ED26C15E12B40EDFBAAC64C9A40448_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InputFieldWarningsFixer__ctor_mA0352FB9DB9785DF7FBDE9CD4E1D2A94718F9C6C (InputFieldWarningsFixer_tE2789F5BD5EE03FE010196301F77663D483ABC90* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InputFieldWarningsFixer__ctor_mA0352FB9DB9785DF7FBDE9CD4E1D2A94718F9C6C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, InputFieldWarningsFixer__ctor_mA0352FB9DB9785DF7FBDE9CD4E1D2A94718F9C6C_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_Lerp_m1A36103F7967F653A929556E26E6D052C298C00C_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_b, float ___2_t, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_Lerp_m1A36103F7967F653A929556E26E6D052C298C00C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_a), (&___1_b), (&___2_t));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Vector2_Lerp_m1A36103F7967F653A929556E26E6D052C298C00C_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26653));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26654));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26655));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26656));
		float L_0 = ___2_t;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26657));
		float L_1;
		L_1 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26657));
		___2_t = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26658));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_2 = ___0_a;
		float L_3 = L_2.___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4 = ___1_b;
		float L_5 = L_4.___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6 = ___0_a;
		float L_7 = L_6.___x;
		float L_8 = ___2_t;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_9 = ___0_a;
		float L_10 = L_9.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_11 = ___1_b;
		float L_12 = L_11.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_13 = ___0_a;
		float L_14 = L_13.___y;
		float L_15 = ___2_t;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26659));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_16;
		memset((&L_16), 0, sizeof(L_16));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_16), ((float)il2cpp_codegen_add(L_3, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_5, L_7)), L_8)))), ((float)il2cpp_codegen_add(L_10, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_12, L_14)), L_15)))), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26659));
		V_0 = L_16;
		goto IL_003d;
	}

IL_003d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26660));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_17 = V_0;
		return L_17;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rect_get_height_mE1AA6C6C725CCD2D317BD2157396D3CF7D47C9D8_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 8608));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 8609));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 8610));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 8611));
		float L_0 = __this->___m_Height;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 8612));
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D_inline (float ___0_a, float ___1_b, float ___2_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	float V_1 = 0.0f;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_a), (&___1_b), (&___2_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26414));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26415));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26416));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26417));
		float L_0 = ___0_a;
		float L_1 = ___1_b;
		V_0 = (bool)((((int32_t)((((float)L_0) == ((float)L_1))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26418));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26419));
		float L_3 = ___2_value;
		float L_4 = ___0_a;
		float L_5 = ___1_b;
		float L_6 = ___0_a;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26420));
		float L_7;
		L_7 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(((float)(((float)il2cpp_codegen_subtract(L_3, L_4))/((float)il2cpp_codegen_subtract(L_5, L_6)))), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26420));
		V_1 = L_7;
		goto IL_0023;
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26421));
		V_1 = (0.0f);
		goto IL_0023;
	}

IL_0023:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26422));
		float L_8 = V_1;
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	float V_1 = 0.0f;
	bool V_2 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26246));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26247));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26248));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26249));
		float L_0 = ___0_value;
		V_0 = (bool)((((float)L_0) < ((float)(0.0f)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26250));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26251));
		V_1 = (0.0f);
		goto IL_002d;
	}

IL_0015:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26252));
		float L_2 = ___0_value;
		V_2 = (bool)((((float)L_2) > ((float)(1.0f)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26253));
		bool L_3 = V_2;
		if (!L_3)
		{
			goto IL_0029;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26254));
		V_1 = (1.0f);
		goto IL_002d;
	}

IL_0029:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26255));
		float L_4 = ___0_value;
		V_1 = L_4;
		goto IL_002d;
	}

IL_002d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26256));
		float L_5 = V_1;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_inline (int32_t ___0_value, int32_t ___1_min, int32_t ___2_max, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	int32_t V_2 = 0;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value), (&___1_min), (&___2_max));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26234));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26235));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26236));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26237));
		int32_t L_0 = ___0_value;
		int32_t L_1 = ___1_min;
		V_0 = (bool)((((int32_t)L_0) < ((int32_t)L_1))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26238));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_000e;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26239));
		int32_t L_3 = ___1_min;
		___0_value = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26240));
		goto IL_0019;
	}

IL_000e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26241));
		int32_t L_4 = ___0_value;
		int32_t L_5 = ___2_max;
		V_1 = (bool)((((int32_t)L_4) > ((int32_t)L_5))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26242));
		bool L_6 = V_1;
		if (!L_6)
		{
			goto IL_0019;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26243));
		int32_t L_7 = ___2_max;
		___0_value = L_7;
	}

IL_0019:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26244));
		int32_t L_8 = ___0_value;
		V_2 = L_8;
		goto IL_001d;
	}

IL_001d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26245));
		int32_t L_9 = V_2;
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27005));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27006));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27007));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27008));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ((Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields*)il2cpp_codegen_static_fields_for(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27009));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Max_m7FA442918DE37E3A00106D1F2E789D65829792B8_inline (int32_t ___0_a, int32_t ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Mathf_Max_m7FA442918DE37E3A00106D1F2E789D65829792B8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_a), (&___1_b));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Mathf_Max_m7FA442918DE37E3A00106D1F2E789D65829792B8_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26126));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26127));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26128));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26129));
		int32_t L_0 = ___0_a;
		int32_t L_1 = ___1_b;
		if ((((int32_t)L_0) > ((int32_t)L_1)))
		{
			goto IL_0008;
		}
	}
	{
		int32_t L_2 = ___1_b;
		G_B3_0 = L_2;
		goto IL_0009;
	}

IL_0008:
	{
		int32_t L_3 = ___0_a;
		G_B3_0 = L_3;
	}

IL_0009:
	{
		V_0 = G_B3_0;
		goto IL_000c;
	}

IL_000c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26130));
		int32_t L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline (float ___0_a, float ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_a), (&___1_b));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26101));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26102));
	float G_B3_0 = 0.0f;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26103));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26104));
		float L_0 = ___0_a;
		float L_1 = ___1_b;
		if ((((float)L_0) > ((float)L_1)))
		{
			goto IL_0008;
		}
	}
	{
		float L_2 = ___1_b;
		G_B3_0 = L_2;
		goto IL_0009;
	}

IL_0008:
	{
		float L_3 = ___0_a;
		G_B3_0 = L_3;
	}

IL_0009:
	{
		V_0 = G_B3_0;
		goto IL_000c;
	}

IL_000c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26105));
		float L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_x), (&___1_y));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26641));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26642));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26643));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26644));
		float L_0 = ___0_x;
		__this->___x = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26645));
		float L_1 = ___1_y;
		__this->___y = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26646));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_inline (int32_t ___0_a, int32_t ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_a), (&___1_b));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26076));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26077));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26078));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26079));
		int32_t L_0 = ___0_a;
		int32_t L_1 = ___1_b;
		if ((((int32_t)L_0) < ((int32_t)L_1)))
		{
			goto IL_0008;
		}
	}
	{
		int32_t L_2 = ___1_b;
		G_B3_0 = L_2;
		goto IL_0009;
	}

IL_0008:
	{
		int32_t L_3 = ___0_a;
		G_B3_0 = L_3;
	}

IL_0009:
	{
		V_0 = G_B3_0;
		goto IL_000c;
	}

IL_000c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26080));
		int32_t L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t DynamicCircularBuffer_1_get_Count_mE68C71C8EE6AA5DF7358E2043BD662DBE83EC476_gshared_inline (DynamicCircularBuffer_1_t5DF7BCE985505B059EBD18512868F7B8B8AD3669* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DynamicCircularBuffer_1_tB13F054EA429529FE1B22D4E3992D968A9379DDF_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, method, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 42));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 43));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsIngameDebugConsole_Runtime + 44));
		int32_t L_0 = __this->___U3CCountU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_m5A038831CEB84A7E374FE59D43444412629F833F_gshared_inline (Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99* __this, Il2CppFullySharedGenericAny ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, Il2CppFullySharedGenericAny, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
