﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct ProceduralPropertyDescriptionU5BU5D_t9FAF93A03219711BB1E9FFAF8E36864171C40470;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46;
struct Exception_t;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3;
struct ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD;
struct ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5;
struct ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct String_t;
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700;
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_SubstanceModule[];
IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5____enumOptions_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_CacheProceduralProperty_mE14C1363B6CC8E0D8110736527F432B28C4A5452_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_ClearCache_m6665395022225A0EAD439F4F9A7630C3E09C955E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_FreezeAndReleaseSourceData_mAD86A6D8CBFDF58EE3C7874949A30D7A22811F4A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetGeneratedTexture_mAD8D1076C1EF6931EBE55747E614EC3011B86354_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetGeneratedTextures_m8A1048A0F4C23B8B886B53C26EDB7FE92F9D778E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetProceduralBoolean_m18783749FD31D0A3D9DC79D22270B06EB941BEB7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetProceduralColor_m80417FD0345304C2A7F6A4C506EF31C033A4A9A4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetProceduralEnum_mD7E7B874FBD01B7270342C7864DDA454C43E5187_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetProceduralFloat_m7C8B13D1D264DD1AF5AFAB04EAF8EE9905EB63C7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetProceduralPropertyDescriptions_m68A10072ACA2AC56DFFC0E66F1E698628DCF2CA7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetProceduralString_m20D89FD1614DF70621F5696EB15D3E5349A3D1D9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetProceduralTexture_m0DE08FCF33082868F65A7C3E97617FAD35C24FD3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_GetProceduralVector_m86324726F0451F120C287450C6B6B7FC8C611CA6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_HasProceduralProperty_m5A18BB3008FFE11692D1EC242133CD265FCC1ADE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_IsProceduralPropertyCached_m3D3E4E2A10D46337174958F65662FBFB8BD417CC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_IsProceduralPropertyVisible_mD867EEBEDA12B26E72499B469941811171E3090A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_RebuildTexturesImmediately_m9A902446FBCE00B8A8784470DAE845CB4B52AC2A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_RebuildTextures_m5B5169A77373138DBF47901F76C98A27AC7CF854_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_SetProceduralBoolean_m495DEA6721AB8FF624C9B16CE51C29FA2B5F2726_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_SetProceduralColor_m593DDC78E673D5D0F02B9FE6AE661996D8A5D65C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_SetProceduralEnum_mCBAB7A515D18306984023477E4FF3C883D17CFB0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_SetProceduralFloat_mCDD13B5FE40F4856AA00C0C20C722B70F79992FE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_SetProceduralString_m81CCA685414DD4D88A7B05416033307D908C9189_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_SetProceduralTexture_m37E6161BFC81B090C932F60F4CD3B70E41BAB3F0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_SetProceduralVector_m8F03A9CB02FECFCD5231C280F33C572C8AC16CB6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_StopRebuilds_mC51D111348A78BBE307C0CCD2A7800323C14DD75_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial__ctor_m16602D62F215E9B98F076BBE1B050F6A0CD272A8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_animationUpdateRate_mE723769BD94211051D09CE9289A5A4E52785BF33_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_cacheSize_m8B5D156D2A8D1E19991FAED47429A0132D12C46F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_isCachedDataAvailable_m6EF86A0B138FB7ED937AF36ACC9AE6E880A0A26F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_isFrozen_m7205FE650CFBBADCA4222685B034380FB3955824_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_isLoadTimeGenerated_mB79CD55733AAD7BA483EA4CD3F457C54255ACF06_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_isProcessing_m833CA6339460C32B5432E0CCAE77AE8EB9A1CF7B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_isReadable_m4CBA0A192573AC2EA477C51695A9528EB09CF20C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_isSupported_m84C82139E2F02444F8E253CBDADAD44508D5456D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_loadingBehavior_m573BA7E233C33EEEFC6DC6A3E09605075C385DF0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_preset_m4AA01BB98A398A0A2DC723D79DBA2398BBC6B5F1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_get_substanceProcessorUsage_m1116E73AE7F05236B55734F760C3166190ECEFE1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_set_animationUpdateRate_mECFD2C34670AAB520F44A6CAD8216610767C3DAA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_set_cacheSize_mF0D3736DE780C25BA87DE642B4DDC20DDE707614_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_set_isLoadTimeGenerated_m0BD9C101D287394F33D8588A990698ED8D8301C8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_set_isReadable_m9B7EF37401398C21C102A241AC0DD74CE35B35BF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_set_preset_m937A5F33D8B7B5BD0AAC436C785DBDD4152D9A0C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial_set_substanceProcessorUsage_m9F9B8050F6A924109BF351BAE2187F276F95076B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralPropertyDescription__ctor_m0A11BD30B21500399952D18C9526C6AE77B0F941_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralTexture_GetPixels32_mD32FDC3C4F88353C1C27B19BA31BB63F7D024951_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralTexture_GetProceduralMaterial_mBD10FB22ECA3D791D41CC2F04633385ADE9A5664_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralTexture_GetProceduralOutputType_mB3433AFFB6AD3E8332887C12E6BBDC4C8FB3D28C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralTexture__ctor_m6E0E48FE3AECFCE69DE9E865ED74D5989A8586B8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralTexture_get_format_m76AFFD512F78936194FB9EA7EAE02D187FC65057_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralTexture_get_hasAlpha_m0FBB56A7000FAE27EF5FDCAE3FFC990F68F00113_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7_0_0_0_var;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259;
struct ProceduralPropertyDescriptionU5BU5D_t9FAF93A03219711BB1E9FFAF8E36864171C40470;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tC3E7876718B65FABD62B43A4FEB95DD141E65247 
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			int32_t ___rgba;
		};
		#pragma pack(pop, tp)
		struct
		{
			int32_t ___rgba_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			uint8_t ___r;
		};
		#pragma pack(pop, tp)
		struct
		{
			uint8_t ___r_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___g_OffsetPadding[1];
			uint8_t ___g;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___g_OffsetPadding_forAlignmentOnly[1];
			uint8_t ___g_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___b_OffsetPadding[2];
			uint8_t ___b;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___b_OffsetPadding_forAlignmentOnly[2];
			uint8_t ___b_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___a_OffsetPadding[3];
			uint8_t ___a;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___a_OffsetPadding_forAlignmentOnly[3];
			uint8_t ___a_forAlignmentOnly;
		};
	};
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct ProceduralCacheSize_t44C5F81E85893A4170B2AF916ED606B52603958E 
{
	int32_t ___value__;
};
struct ProceduralLoadingBehavior_t9C39A46BF4810E05116D7402131C3CBC41021881 
{
	int32_t ___value__;
};
struct ProceduralOutputType_t1FA6895041F33DC431D9AD42CB75FAC9E689A1E3 
{
	int32_t ___value__;
};
struct ProceduralProcessorUsage_t7978602BDEACBE71F8273CC5DCB6F672DB5BC644 
{
	int32_t ___value__;
};
struct ProceduralPropertyType_t9D8BB9E510D4D2C35C563BF4BD1610AFF7CEC82D 
{
	int32_t ___value__;
};
struct TextureFormat_t87A73E4A3850D3410DC211676FC14B94226C1C1D 
{
	int32_t ___value__;
};
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5  : public RuntimeObject
{
	String_t* ___name;
	String_t* ___label;
	String_t* ___group;
	int32_t ___type;
	bool ___hasRange;
	float ___minimum;
	float ___maximum;
	float ___step;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___enumOptions;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___componentLabels;
};
struct ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshaled_pinvoke
{
	char* ___name;
	char* ___label;
	char* ___group;
	int32_t ___type;
	int32_t ___hasRange;
	float ___minimum;
	float ___maximum;
	float ___step;
	char** ___enumOptions;
	char** ___componentLabels;
};
struct ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshaled_com
{
	Il2CppChar* ___name;
	Il2CppChar* ___label;
	Il2CppChar* ___group;
	int32_t ___type;
	int32_t ___hasRange;
	float ___minimum;
	float ___maximum;
	float ___step;
	Il2CppChar** ___enumOptions;
	Il2CppChar** ___componentLabels;
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD  : public Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3
{
};
struct ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7  : public Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700
{
};
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4  : public Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700
{
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_StaticFields
{
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___zeroVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___oneVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___positiveInfinityVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___negativeInfinityVector;
};
struct Exception_t_StaticFields
{
	RuntimeObject* ___s_EDILock;
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700_StaticFields
{
	int32_t ___GenerateAllMips;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ProceduralPropertyDescriptionU5BU5D_t9FAF93A03219711BB1E9FFAF8E36864171C40470  : public RuntimeArray
{
	ALIGN_FIELD (8) ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5* m_Items[1];

	inline ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46  : public RuntimeArray
{
	ALIGN_FIELD (8) Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* m_Items[1];

	inline Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248  : public RuntimeArray
{
	ALIGN_FIELD (8) String_t* m_Items[1];

	inline String_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline String_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, String_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline String_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline String_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, String_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259  : public RuntimeArray
{
	ALIGN_FIELD (8) Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B m_Items[1];

	inline Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B value)
	{
		m_Items[index] = value;
	}
};



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F (Exception_t* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_source, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Texture__ctor_mC0C7974BEBD867CEB281409FEA15A78CD91B19CC (Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 3));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 4));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 4));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial__ctor_m16602D62F215E9B98F076BBE1B050F6A0CD272A8 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial__ctor_m16602D62F215E9B98F076BBE1B050F6A0CD272A8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial__ctor_m16602D62F215E9B98F076BBE1B050F6A0CD272A8_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 5));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 6));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 7));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 8));
		Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C(__this, (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)NULL, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 8));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 9));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 10));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 11));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 11));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 12));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ProceduralPropertyDescriptionU5BU5D_t9FAF93A03219711BB1E9FFAF8E36864171C40470* ProceduralMaterial_GetProceduralPropertyDescriptions_m68A10072ACA2AC56DFFC0E66F1E698628DCF2CA7 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetProceduralPropertyDescriptions_m68A10072ACA2AC56DFFC0E66F1E698628DCF2CA7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetProceduralPropertyDescriptions_m68A10072ACA2AC56DFFC0E66F1E698628DCF2CA7_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 13));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 14));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 15));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 16));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 17));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 17));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetProceduralPropertyDescriptions_m68A10072ACA2AC56DFFC0E66F1E698628DCF2CA7_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_HasProceduralProperty_m5A18BB3008FFE11692D1EC242133CD265FCC1ADE (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_HasProceduralProperty_m5A18BB3008FFE11692D1EC242133CD265FCC1ADE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_HasProceduralProperty_m5A18BB3008FFE11692D1EC242133CD265FCC1ADE_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 18));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 19));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 20));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 21));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 22));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 22));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_HasProceduralProperty_m5A18BB3008FFE11692D1EC242133CD265FCC1ADE_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_GetProceduralBoolean_m18783749FD31D0A3D9DC79D22270B06EB941BEB7 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetProceduralBoolean_m18783749FD31D0A3D9DC79D22270B06EB941BEB7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetProceduralBoolean_m18783749FD31D0A3D9DC79D22270B06EB941BEB7_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 23));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 24));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 25));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 26));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 27));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 27));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetProceduralBoolean_m18783749FD31D0A3D9DC79D22270B06EB941BEB7_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_IsProceduralPropertyVisible_mD867EEBEDA12B26E72499B469941811171E3090A (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_IsProceduralPropertyVisible_mD867EEBEDA12B26E72499B469941811171E3090A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_IsProceduralPropertyVisible_mD867EEBEDA12B26E72499B469941811171E3090A_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 28));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 29));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 30));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 31));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 32));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 32));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_IsProceduralPropertyVisible_mD867EEBEDA12B26E72499B469941811171E3090A_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_SetProceduralBoolean_m495DEA6721AB8FF624C9B16CE51C29FA2B5F2726 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, bool ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_SetProceduralBoolean_m495DEA6721AB8FF624C9B16CE51C29FA2B5F2726_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_SetProceduralBoolean_m495DEA6721AB8FF624C9B16CE51C29FA2B5F2726_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 33));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 34));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 35));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 36));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 37));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 37));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 38));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ProceduralMaterial_GetProceduralFloat_m7C8B13D1D264DD1AF5AFAB04EAF8EE9905EB63C7 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetProceduralFloat_m7C8B13D1D264DD1AF5AFAB04EAF8EE9905EB63C7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetProceduralFloat_m7C8B13D1D264DD1AF5AFAB04EAF8EE9905EB63C7_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 39));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 40));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 41));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 42));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 43));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 43));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetProceduralFloat_m7C8B13D1D264DD1AF5AFAB04EAF8EE9905EB63C7_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_SetProceduralFloat_mCDD13B5FE40F4856AA00C0C20C722B70F79992FE (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, float ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_SetProceduralFloat_mCDD13B5FE40F4856AA00C0C20C722B70F79992FE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_SetProceduralFloat_mCDD13B5FE40F4856AA00C0C20C722B70F79992FE_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 44));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 45));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 46));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 47));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 48));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 48));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 49));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ProceduralMaterial_GetProceduralVector_m86324726F0451F120C287450C6B6B7FC8C611CA6 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetProceduralVector_m86324726F0451F120C287450C6B6B7FC8C611CA6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetProceduralVector_m86324726F0451F120C287450C6B6B7FC8C611CA6_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 50));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 51));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 52));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 53));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 54));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 54));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetProceduralVector_m86324726F0451F120C287450C6B6B7FC8C611CA6_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_SetProceduralVector_m8F03A9CB02FECFCD5231C280F33C572C8AC16CB6 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_SetProceduralVector_m8F03A9CB02FECFCD5231C280F33C572C8AC16CB6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_SetProceduralVector_m8F03A9CB02FECFCD5231C280F33C572C8AC16CB6_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 55));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 56));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 57));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 58));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 59));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 59));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 60));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ProceduralMaterial_GetProceduralColor_m80417FD0345304C2A7F6A4C506EF31C033A4A9A4 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetProceduralColor_m80417FD0345304C2A7F6A4C506EF31C033A4A9A4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetProceduralColor_m80417FD0345304C2A7F6A4C506EF31C033A4A9A4_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 61));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 62));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 63));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 64));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 65));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 65));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetProceduralColor_m80417FD0345304C2A7F6A4C506EF31C033A4A9A4_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_SetProceduralColor_m593DDC78E673D5D0F02B9FE6AE661996D8A5D65C (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_SetProceduralColor_m593DDC78E673D5D0F02B9FE6AE661996D8A5D65C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_SetProceduralColor_m593DDC78E673D5D0F02B9FE6AE661996D8A5D65C_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 66));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 67));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 68));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 69));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 70));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 70));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 71));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ProceduralMaterial_GetProceduralEnum_mD7E7B874FBD01B7270342C7864DDA454C43E5187 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetProceduralEnum_mD7E7B874FBD01B7270342C7864DDA454C43E5187_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetProceduralEnum_mD7E7B874FBD01B7270342C7864DDA454C43E5187_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 72));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 73));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 74));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 75));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 76));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 76));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetProceduralEnum_mD7E7B874FBD01B7270342C7864DDA454C43E5187_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_SetProceduralEnum_mCBAB7A515D18306984023477E4FF3C883D17CFB0 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, int32_t ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_SetProceduralEnum_mCBAB7A515D18306984023477E4FF3C883D17CFB0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_SetProceduralEnum_mCBAB7A515D18306984023477E4FF3C883D17CFB0_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 77));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 78));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 79));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 80));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 81));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 81));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 82));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ProceduralMaterial_GetProceduralTexture_m0DE08FCF33082868F65A7C3E97617FAD35C24FD3 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetProceduralTexture_m0DE08FCF33082868F65A7C3E97617FAD35C24FD3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetProceduralTexture_m0DE08FCF33082868F65A7C3E97617FAD35C24FD3_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 83));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 84));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 85));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 86));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 87));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 87));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetProceduralTexture_m0DE08FCF33082868F65A7C3E97617FAD35C24FD3_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_SetProceduralTexture_m37E6161BFC81B090C932F60F4CD3B70E41BAB3F0 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_SetProceduralTexture_m37E6161BFC81B090C932F60F4CD3B70E41BAB3F0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_SetProceduralTexture_m37E6161BFC81B090C932F60F4CD3B70E41BAB3F0_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 88));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 89));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 90));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 91));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 92));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 92));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 93));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ProceduralMaterial_GetProceduralString_m20D89FD1614DF70621F5696EB15D3E5349A3D1D9 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetProceduralString_m20D89FD1614DF70621F5696EB15D3E5349A3D1D9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetProceduralString_m20D89FD1614DF70621F5696EB15D3E5349A3D1D9_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 94));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 95));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 96));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 97));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 98));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 98));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetProceduralString_m20D89FD1614DF70621F5696EB15D3E5349A3D1D9_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_SetProceduralString_m81CCA685414DD4D88A7B05416033307D908C9189 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, String_t* ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_SetProceduralString_m81CCA685414DD4D88A7B05416033307D908C9189_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_SetProceduralString_m81CCA685414DD4D88A7B05416033307D908C9189_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 99));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 100));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 101));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 102));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 103));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 103));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 104));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_IsProceduralPropertyCached_m3D3E4E2A10D46337174958F65662FBFB8BD417CC (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_IsProceduralPropertyCached_m3D3E4E2A10D46337174958F65662FBFB8BD417CC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_IsProceduralPropertyCached_m3D3E4E2A10D46337174958F65662FBFB8BD417CC_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 105));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 106));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 107));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 108));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 109));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 109));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_IsProceduralPropertyCached_m3D3E4E2A10D46337174958F65662FBFB8BD417CC_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_CacheProceduralProperty_mE14C1363B6CC8E0D8110736527F432B28C4A5452 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_inputName, bool ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_CacheProceduralProperty_mE14C1363B6CC8E0D8110736527F432B28C4A5452_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_inputName), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_CacheProceduralProperty_mE14C1363B6CC8E0D8110736527F432B28C4A5452_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 110));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 111));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 112));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 113));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 114));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 114));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 115));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_ClearCache_m6665395022225A0EAD439F4F9A7630C3E09C955E (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_ClearCache_m6665395022225A0EAD439F4F9A7630C3E09C955E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_ClearCache_m6665395022225A0EAD439F4F9A7630C3E09C955E_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 116));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 117));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 118));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 119));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 120));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 120));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 121));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ProceduralMaterial_get_cacheSize_m8B5D156D2A8D1E19991FAED47429A0132D12C46F (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_cacheSize_m8B5D156D2A8D1E19991FAED47429A0132D12C46F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_cacheSize_m8B5D156D2A8D1E19991FAED47429A0132D12C46F_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 122));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 123));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 124));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 125));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 126));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 126));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_cacheSize_m8B5D156D2A8D1E19991FAED47429A0132D12C46F_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_set_cacheSize_mF0D3736DE780C25BA87DE642B4DDC20DDE707614 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_set_cacheSize_mF0D3736DE780C25BA87DE642B4DDC20DDE707614_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_set_cacheSize_mF0D3736DE780C25BA87DE642B4DDC20DDE707614_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 127));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 128));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 129));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 130));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 131));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 131));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 132));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ProceduralMaterial_get_animationUpdateRate_mE723769BD94211051D09CE9289A5A4E52785BF33 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_animationUpdateRate_mE723769BD94211051D09CE9289A5A4E52785BF33_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_animationUpdateRate_mE723769BD94211051D09CE9289A5A4E52785BF33_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 133));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 134));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 135));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 136));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 137));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 137));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_animationUpdateRate_mE723769BD94211051D09CE9289A5A4E52785BF33_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_set_animationUpdateRate_mECFD2C34670AAB520F44A6CAD8216610767C3DAA (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_set_animationUpdateRate_mECFD2C34670AAB520F44A6CAD8216610767C3DAA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_set_animationUpdateRate_mECFD2C34670AAB520F44A6CAD8216610767C3DAA_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 138));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 139));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 140));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 141));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 142));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 142));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 143));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_RebuildTextures_m5B5169A77373138DBF47901F76C98A27AC7CF854 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_RebuildTextures_m5B5169A77373138DBF47901F76C98A27AC7CF854_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_RebuildTextures_m5B5169A77373138DBF47901F76C98A27AC7CF854_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 144));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 145));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 146));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 147));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 148));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 148));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 149));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_RebuildTexturesImmediately_m9A902446FBCE00B8A8784470DAE845CB4B52AC2A (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_RebuildTexturesImmediately_m9A902446FBCE00B8A8784470DAE845CB4B52AC2A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_RebuildTexturesImmediately_m9A902446FBCE00B8A8784470DAE845CB4B52AC2A_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 150));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 151));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 152));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 153));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 154));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 154));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 155));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_get_isProcessing_m833CA6339460C32B5432E0CCAE77AE8EB9A1CF7B (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_isProcessing_m833CA6339460C32B5432E0CCAE77AE8EB9A1CF7B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_isProcessing_m833CA6339460C32B5432E0CCAE77AE8EB9A1CF7B_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 156));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 157));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 158));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 159));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 160));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 160));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_isProcessing_m833CA6339460C32B5432E0CCAE77AE8EB9A1CF7B_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_StopRebuilds_mC51D111348A78BBE307C0CCD2A7800323C14DD75 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_StopRebuilds_mC51D111348A78BBE307C0CCD2A7800323C14DD75_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_StopRebuilds_mC51D111348A78BBE307C0CCD2A7800323C14DD75_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 161));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 162));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 163));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 164));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 165));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 165));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 166));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_get_isCachedDataAvailable_m6EF86A0B138FB7ED937AF36ACC9AE6E880A0A26F (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_isCachedDataAvailable_m6EF86A0B138FB7ED937AF36ACC9AE6E880A0A26F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_isCachedDataAvailable_m6EF86A0B138FB7ED937AF36ACC9AE6E880A0A26F_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 167));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 168));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 169));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 170));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 171));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 171));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_isCachedDataAvailable_m6EF86A0B138FB7ED937AF36ACC9AE6E880A0A26F_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_get_isLoadTimeGenerated_mB79CD55733AAD7BA483EA4CD3F457C54255ACF06 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_isLoadTimeGenerated_mB79CD55733AAD7BA483EA4CD3F457C54255ACF06_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_isLoadTimeGenerated_mB79CD55733AAD7BA483EA4CD3F457C54255ACF06_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 172));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 173));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 174));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 175));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 176));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 176));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_isLoadTimeGenerated_mB79CD55733AAD7BA483EA4CD3F457C54255ACF06_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_set_isLoadTimeGenerated_m0BD9C101D287394F33D8588A990698ED8D8301C8 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, bool ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_set_isLoadTimeGenerated_m0BD9C101D287394F33D8588A990698ED8D8301C8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_set_isLoadTimeGenerated_m0BD9C101D287394F33D8588A990698ED8D8301C8_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 177));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 178));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 179));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 180));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 181));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 181));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 182));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ProceduralMaterial_get_loadingBehavior_m573BA7E233C33EEEFC6DC6A3E09605075C385DF0 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_loadingBehavior_m573BA7E233C33EEEFC6DC6A3E09605075C385DF0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_loadingBehavior_m573BA7E233C33EEEFC6DC6A3E09605075C385DF0_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 183));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 184));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 185));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 186));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 187));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 187));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_loadingBehavior_m573BA7E233C33EEEFC6DC6A3E09605075C385DF0_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_get_isSupported_m84C82139E2F02444F8E253CBDADAD44508D5456D (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_isSupported_m84C82139E2F02444F8E253CBDADAD44508D5456D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_isSupported_m84C82139E2F02444F8E253CBDADAD44508D5456D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 188));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 189));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 190));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 191));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 192));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 192));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_isSupported_m84C82139E2F02444F8E253CBDADAD44508D5456D_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ProceduralMaterial_get_substanceProcessorUsage_m1116E73AE7F05236B55734F760C3166190ECEFE1 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_substanceProcessorUsage_m1116E73AE7F05236B55734F760C3166190ECEFE1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_substanceProcessorUsage_m1116E73AE7F05236B55734F760C3166190ECEFE1_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 193));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 194));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 195));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 196));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 197));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 197));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_substanceProcessorUsage_m1116E73AE7F05236B55734F760C3166190ECEFE1_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_set_substanceProcessorUsage_m9F9B8050F6A924109BF351BAE2187F276F95076B (int32_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_set_substanceProcessorUsage_m9F9B8050F6A924109BF351BAE2187F276F95076B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_set_substanceProcessorUsage_m9F9B8050F6A924109BF351BAE2187F276F95076B_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 198));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 199));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 200));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 201));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 202));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 202));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 203));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ProceduralMaterial_get_preset_m4AA01BB98A398A0A2DC723D79DBA2398BBC6B5F1 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_preset_m4AA01BB98A398A0A2DC723D79DBA2398BBC6B5F1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_preset_m4AA01BB98A398A0A2DC723D79DBA2398BBC6B5F1_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 204));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 205));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 206));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 207));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 208));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 208));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_preset_m4AA01BB98A398A0A2DC723D79DBA2398BBC6B5F1_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_set_preset_m937A5F33D8B7B5BD0AAC436C785DBDD4152D9A0C (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_set_preset_m937A5F33D8B7B5BD0AAC436C785DBDD4152D9A0C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_set_preset_m937A5F33D8B7B5BD0AAC436C785DBDD4152D9A0C_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 209));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 210));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 211));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 212));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 213));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 213));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 214));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46* ProceduralMaterial_GetGeneratedTextures_m8A1048A0F4C23B8B886B53C26EDB7FE92F9D778E (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetGeneratedTextures_m8A1048A0F4C23B8B886B53C26EDB7FE92F9D778E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetGeneratedTextures_m8A1048A0F4C23B8B886B53C26EDB7FE92F9D778E_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 215));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 216));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 217));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 218));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 219));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 219));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetGeneratedTextures_m8A1048A0F4C23B8B886B53C26EDB7FE92F9D778E_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7* ProceduralMaterial_GetGeneratedTexture_mAD8D1076C1EF6931EBE55747E614EC3011B86354 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, String_t* ___0_textureName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_GetGeneratedTexture_mAD8D1076C1EF6931EBE55747E614EC3011B86354_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_textureName));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_GetGeneratedTexture_mAD8D1076C1EF6931EBE55747E614EC3011B86354_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 220));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 221));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 222));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 223));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 224));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 224));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_GetGeneratedTexture_mAD8D1076C1EF6931EBE55747E614EC3011B86354_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_get_isReadable_m4CBA0A192573AC2EA477C51695A9528EB09CF20C (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_isReadable_m4CBA0A192573AC2EA477C51695A9528EB09CF20C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_isReadable_m4CBA0A192573AC2EA477C51695A9528EB09CF20C_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 225));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 226));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 227));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 228));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 229));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 229));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_isReadable_m4CBA0A192573AC2EA477C51695A9528EB09CF20C_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_set_isReadable_m9B7EF37401398C21C102A241AC0DD74CE35B35BF (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, bool ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_set_isReadable_m9B7EF37401398C21C102A241AC0DD74CE35B35BF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_set_isReadable_m9B7EF37401398C21C102A241AC0DD74CE35B35BF_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 230));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 231));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 232));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 233));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 234));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 234));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 235));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial_FreezeAndReleaseSourceData_mAD86A6D8CBFDF58EE3C7874949A30D7A22811F4A (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_FreezeAndReleaseSourceData_mAD86A6D8CBFDF58EE3C7874949A30D7A22811F4A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_FreezeAndReleaseSourceData_mAD86A6D8CBFDF58EE3C7874949A30D7A22811F4A_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 236));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 237));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 238));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 239));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 240));
		ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 240));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 241));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralMaterial_get_isFrozen_m7205FE650CFBBADCA4222685B034380FB3955824 (ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_get_isFrozen_m7205FE650CFBBADCA4222685B034380FB3955824_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial_get_isFrozen_m7205FE650CFBBADCA4222685B034380FB3955824_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 242));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 243));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 244));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 245));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 246));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 246));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralMaterial_get_isFrozen_m7205FE650CFBBADCA4222685B034380FB3955824_RuntimeMethod_var)));
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshal_pinvoke(const ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5& unmarshaled, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5____enumOptions_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___enumOptionsException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s'.", ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5____enumOptions_FieldInfo_var, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___enumOptionsException, NULL);
}
IL2CPP_EXTERN_C void ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshal_pinvoke_back(const ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshaled_pinvoke& marshaled, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5____enumOptions_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___enumOptionsException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s'.", ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5____enumOptions_FieldInfo_var, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___enumOptionsException, NULL);
}
IL2CPP_EXTERN_C void ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshal_pinvoke_cleanup(ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshal_com(const ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5& unmarshaled, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5____enumOptions_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___enumOptionsException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s'.", ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5____enumOptions_FieldInfo_var, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___enumOptionsException, NULL);
}
IL2CPP_EXTERN_C void ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshal_com_back(const ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshaled_com& marshaled, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5____enumOptions_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___enumOptionsException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s'.", ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5____enumOptions_FieldInfo_var, ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___enumOptionsException, NULL);
}
IL2CPP_EXTERN_C void ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshal_com_cleanup(ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralPropertyDescription__ctor_m0A11BD30B21500399952D18C9526C6AE77B0F941 (ProceduralPropertyDescription_tE92EB68F4544FDE29F0373824D918E041ACE8FE5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription__ctor_m0A11BD30B21500399952D18C9526C6AE77B0F941_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralPropertyDescription__ctor_m0A11BD30B21500399952D18C9526C6AE77B0F941_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralTexture__ctor_m6E0E48FE3AECFCE69DE9E865ED74D5989A8586B8 (ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture__ctor_m6E0E48FE3AECFCE69DE9E865ED74D5989A8586B8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralTexture__ctor_m6E0E48FE3AECFCE69DE9E865ED74D5989A8586B8_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 247));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 248));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 249));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 250));
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700_il2cpp_TypeInfo_var)));
		Texture__ctor_mC0C7974BEBD867CEB281409FEA15A78CD91B19CC(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 250));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 251));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 252));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 253));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 253));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralTexture__ctor_m6E0E48FE3AECFCE69DE9E865ED74D5989A8586B8_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ProceduralTexture_GetProceduralOutputType_mB3433AFFB6AD3E8332887C12E6BBDC4C8FB3D28C (ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_GetProceduralOutputType_mB3433AFFB6AD3E8332887C12E6BBDC4C8FB3D28C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralTexture_GetProceduralOutputType_mB3433AFFB6AD3E8332887C12E6BBDC4C8FB3D28C_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 254));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 255));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 256));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 257));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 258));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 258));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralTexture_GetProceduralOutputType_mB3433AFFB6AD3E8332887C12E6BBDC4C8FB3D28C_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ProceduralMaterial_tEC177DECFB1872A961FC270F2DE49C0F386EAEBD* ProceduralTexture_GetProceduralMaterial_mBD10FB22ECA3D791D41CC2F04633385ADE9A5664 (ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_GetProceduralMaterial_mBD10FB22ECA3D791D41CC2F04633385ADE9A5664_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralTexture_GetProceduralMaterial_mBD10FB22ECA3D791D41CC2F04633385ADE9A5664_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 259));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 260));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 261));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 262));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 263));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 263));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralTexture_GetProceduralMaterial_mBD10FB22ECA3D791D41CC2F04633385ADE9A5664_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ProceduralTexture_get_hasAlpha_m0FBB56A7000FAE27EF5FDCAE3FFC990F68F00113 (ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_get_hasAlpha_m0FBB56A7000FAE27EF5FDCAE3FFC990F68F00113_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralTexture_get_hasAlpha_m0FBB56A7000FAE27EF5FDCAE3FFC990F68F00113_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 264));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 265));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 266));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 267));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 268));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 268));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralTexture_get_hasAlpha_m0FBB56A7000FAE27EF5FDCAE3FFC990F68F00113_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ProceduralTexture_get_format_m76AFFD512F78936194FB9EA7EAE02D187FC65057 (ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_get_format_m76AFFD512F78936194FB9EA7EAE02D187FC65057_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralTexture_get_format_m76AFFD512F78936194FB9EA7EAE02D187FC65057_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 269));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 270));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 271));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 272));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 273));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 273));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralTexture_get_format_m76AFFD512F78936194FB9EA7EAE02D187FC65057_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* ProceduralTexture_GetPixels32_mD32FDC3C4F88353C1C27B19BA31BB63F7D024951 (ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7* __this, int32_t ___0_x, int32_t ___1_y, int32_t ___2_blockWidth, int32_t ___3_blockHeight, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_GetPixels32_mD32FDC3C4F88353C1C27B19BA31BB63F7D024951_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture_tCCF2E7FD579C56FF4E37B05660751D12932086E7_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_x), (&___1_y), (&___2_blockWidth), (&___3_blockHeight));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralTexture_GetPixels32_mD32FDC3C4F88353C1C27B19BA31BB63F7D024951_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 274));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 275));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 276));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 277));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 278));
		Exception_t* L_0 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7F6DCE75EFAEA2B3005EDED849D6F4176A49880)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SubstanceModule + 278));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ProceduralTexture_GetPixels32_mD32FDC3C4F88353C1C27B19BA31BB63F7D024951_RuntimeMethod_var)));
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
