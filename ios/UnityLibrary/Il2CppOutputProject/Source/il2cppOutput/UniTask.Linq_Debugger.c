﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[2] = 
{
	{ 34780, 0,  8 },
	{ 37939, 0,  15 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = 
{
	"source",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[22] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 1, 1 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUniTask_Linq[];
Il2CppSequencePoint g_sequencePointsUniTask_Linq[272] = 
{
	{ 110259, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 110259, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 110259, 1, 8, 8, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 110259, 1, 9, 9, 13, 69, 1, kSequencePointKind_Normal, 0, 3 },
	{ 110259, 1, 9, 9, 13, 69, 3, kSequencePointKind_StepOut, 0, 4 },
	{ 110259, 1, 10, 10, 9, 10, 11, kSequencePointKind_Normal, 0, 5 },
	{ 110260, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 110260, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 110260, 2, 20, 20, 9, 10, 0, kSequencePointKind_Normal, 0, 8 },
	{ 110260, 2, 21, 21, 13, 96, 1, kSequencePointKind_Normal, 0, 9 },
	{ 110260, 2, 21, 21, 13, 96, 3, kSequencePointKind_StepOut, 0, 10 },
	{ 110260, 2, 21, 21, 13, 96, 11, kSequencePointKind_StepOut, 0, 11 },
	{ 110260, 2, 22, 22, 9, 10, 19, kSequencePointKind_Normal, 0, 12 },
	{ 110261, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 13 },
	{ 110261, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 14 },
	{ 110261, 1, 18, 18, 9, 82, 0, kSequencePointKind_Normal, 0, 15 },
	{ 110261, 1, 18, 18, 9, 82, 1, kSequencePointKind_StepOut, 0, 16 },
	{ 110261, 1, 19, 19, 9, 10, 7, kSequencePointKind_Normal, 0, 17 },
	{ 110261, 1, 20, 20, 13, 46, 8, kSequencePointKind_Normal, 0, 18 },
	{ 110261, 1, 21, 21, 13, 56, 15, kSequencePointKind_Normal, 0, 19 },
	{ 110261, 1, 22, 22, 9, 10, 22, kSequencePointKind_Normal, 0, 20 },
	{ 110262, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 21 },
	{ 110262, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 22 },
	{ 110262, 1, 25, 25, 9, 10, 0, kSequencePointKind_Normal, 0, 23 },
	{ 110262, 1, 26, 26, 13, 89, 1, kSequencePointKind_Normal, 0, 24 },
	{ 110262, 1, 26, 26, 13, 89, 14, kSequencePointKind_StepOut, 0, 25 },
	{ 110262, 1, 27, 27, 9, 10, 22, kSequencePointKind_Normal, 0, 26 },
	{ 110263, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 27 },
	{ 110263, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 28 },
	{ 110263, 1, 37, 37, 13, 124, 0, kSequencePointKind_Normal, 0, 29 },
	{ 110263, 1, 37, 37, 13, 124, 1, kSequencePointKind_StepOut, 0, 30 },
	{ 110263, 1, 38, 38, 13, 14, 7, kSequencePointKind_Normal, 0, 31 },
	{ 110263, 1, 39, 39, 17, 50, 8, kSequencePointKind_Normal, 0, 32 },
	{ 110263, 1, 40, 40, 17, 60, 15, kSequencePointKind_Normal, 0, 33 },
	{ 110263, 1, 42, 42, 17, 74, 22, kSequencePointKind_Normal, 0, 34 },
	{ 110263, 1, 42, 42, 17, 74, 27, kSequencePointKind_StepOut, 0, 35 },
	{ 110263, 1, 42, 42, 0, 0, 36, kSequencePointKind_Normal, 0, 36 },
	{ 110263, 1, 43, 43, 17, 18, 39, kSequencePointKind_Normal, 0, 37 },
	{ 110263, 1, 44, 48, 21, 30, 40, kSequencePointKind_Normal, 0, 38 },
	{ 110263, 1, 44, 48, 21, 30, 62, kSequencePointKind_StepOut, 0, 39 },
	{ 110263, 1, 44, 48, 21, 30, 74, kSequencePointKind_StepOut, 0, 40 },
	{ 110263, 1, 49, 49, 17, 18, 84, kSequencePointKind_Normal, 0, 41 },
	{ 110263, 1, 52, 52, 17, 64, 85, kSequencePointKind_Normal, 0, 42 },
	{ 110263, 1, 52, 52, 17, 64, 87, kSequencePointKind_StepOut, 0, 43 },
	{ 110263, 1, 53, 53, 13, 14, 93, kSequencePointKind_Normal, 0, 44 },
	{ 110264, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 45 },
	{ 110264, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 46 },
	{ 110264, 1, 55, 55, 41, 48, 0, kSequencePointKind_Normal, 0, 47 },
	{ 110265, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 48 },
	{ 110265, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 49 },
	{ 110265, 1, 58, 58, 13, 14, 0, kSequencePointKind_Normal, 0, 50 },
	{ 110265, 1, 59, 59, 17, 30, 1, kSequencePointKind_Normal, 0, 51 },
	{ 110265, 1, 59, 59, 0, 0, 8, kSequencePointKind_Normal, 0, 52 },
	{ 110265, 1, 59, 59, 31, 59, 11, kSequencePointKind_Normal, 0, 53 },
	{ 110265, 1, 61, 61, 17, 42, 19, kSequencePointKind_Normal, 0, 54 },
	{ 110265, 1, 61, 61, 17, 42, 25, kSequencePointKind_StepOut, 0, 55 },
	{ 110265, 1, 63, 63, 17, 63, 31, kSequencePointKind_Normal, 0, 56 },
	{ 110265, 1, 63, 63, 17, 63, 37, kSequencePointKind_StepOut, 0, 57 },
	{ 110265, 1, 63, 63, 0, 0, 43, kSequencePointKind_Normal, 0, 58 },
	{ 110265, 1, 64, 64, 17, 18, 46, kSequencePointKind_Normal, 0, 59 },
	{ 110265, 1, 65, 65, 21, 72, 47, kSequencePointKind_Normal, 0, 60 },
	{ 110265, 1, 65, 65, 21, 72, 59, kSequencePointKind_StepOut, 0, 61 },
	{ 110265, 1, 66, 66, 17, 18, 65, kSequencePointKind_Normal, 0, 62 },
	{ 110265, 1, 67, 67, 17, 74, 66, kSequencePointKind_Normal, 0, 63 },
	{ 110265, 1, 67, 67, 17, 74, 73, kSequencePointKind_StepOut, 0, 64 },
	{ 110265, 1, 67, 67, 17, 74, 78, kSequencePointKind_StepOut, 0, 65 },
	{ 110265, 1, 68, 68, 13, 14, 86, kSequencePointKind_Normal, 0, 66 },
	{ 110266, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 67 },
	{ 110266, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 68 },
	{ 110266, 1, 71, 71, 13, 14, 0, kSequencePointKind_Normal, 0, 69 },
	{ 110266, 1, 72, 72, 17, 31, 1, kSequencePointKind_Normal, 0, 70 },
	{ 110266, 1, 72, 72, 0, 0, 11, kSequencePointKind_Normal, 0, 71 },
	{ 110266, 1, 73, 73, 17, 18, 14, kSequencePointKind_Normal, 0, 72 },
	{ 110266, 1, 74, 74, 21, 61, 15, kSequencePointKind_Normal, 0, 73 },
	{ 110266, 1, 74, 74, 21, 61, 21, kSequencePointKind_StepOut, 0, 74 },
	{ 110266, 1, 75, 75, 21, 37, 27, kSequencePointKind_Normal, 0, 75 },
	{ 110266, 1, 77, 77, 17, 18, 34, kSequencePointKind_Normal, 0, 76 },
	{ 110266, 1, 78, 78, 17, 32, 35, kSequencePointKind_Normal, 0, 77 },
	{ 110266, 1, 79, 79, 13, 14, 47, kSequencePointKind_Normal, 0, 78 },
	{ 110267, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 79 },
	{ 110267, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 80 },
	{ 110267, 1, 82, 82, 13, 14, 0, kSequencePointKind_Normal, 0, 81 },
	{ 110267, 1, 83, 83, 17, 63, 1, kSequencePointKind_Normal, 0, 82 },
	{ 110267, 1, 83, 83, 17, 63, 7, kSequencePointKind_StepOut, 0, 83 },
	{ 110267, 1, 83, 83, 0, 0, 13, kSequencePointKind_Normal, 0, 84 },
	{ 110267, 1, 84, 84, 17, 18, 16, kSequencePointKind_Normal, 0, 85 },
	{ 110267, 1, 85, 85, 21, 72, 17, kSequencePointKind_Normal, 0, 86 },
	{ 110267, 1, 85, 85, 21, 72, 29, kSequencePointKind_StepOut, 0, 87 },
	{ 110267, 1, 86, 86, 21, 34, 35, kSequencePointKind_Normal, 0, 88 },
	{ 110267, 1, 89, 89, 17, 30, 39, kSequencePointKind_Normal, 0, 89 },
	{ 110267, 1, 89, 89, 0, 0, 46, kSequencePointKind_Normal, 0, 90 },
	{ 110267, 1, 90, 90, 17, 18, 49, kSequencePointKind_Normal, 0, 91 },
	{ 110267, 1, 91, 91, 21, 58, 50, kSequencePointKind_Normal, 0, 92 },
	{ 110267, 1, 91, 91, 21, 58, 57, kSequencePointKind_StepOut, 0, 93 },
	{ 110267, 1, 92, 92, 21, 34, 63, kSequencePointKind_Normal, 0, 94 },
	{ 110267, 1, 95, 95, 17, 53, 67, kSequencePointKind_Normal, 0, 95 },
	{ 110267, 1, 95, 95, 17, 53, 74, kSequencePointKind_StepOut, 0, 96 },
	{ 110267, 1, 96, 96, 17, 29, 80, kSequencePointKind_Normal, 0, 97 },
	{ 110267, 1, 97, 97, 13, 14, 84, kSequencePointKind_Normal, 0, 98 },
	{ 110270, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 99 },
	{ 110270, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 100 },
	{ 110270, 1, 45, 45, 21, 22, 0, kSequencePointKind_Normal, 0, 101 },
	{ 110270, 1, 46, 46, 25, 58, 1, kSequencePointKind_Normal, 0, 102 },
	{ 110270, 1, 47, 47, 25, 90, 8, kSequencePointKind_Normal, 0, 103 },
	{ 110270, 1, 47, 47, 25, 90, 20, kSequencePointKind_StepOut, 0, 104 },
	{ 110270, 1, 48, 48, 21, 22, 26, kSequencePointKind_Normal, 0, 105 },
	{ 110271, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 106 },
	{ 110271, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 107 },
	{ 110271, 2, 66, 66, 9, 134, 0, kSequencePointKind_Normal, 0, 108 },
	{ 110271, 2, 66, 66, 9, 134, 1, kSequencePointKind_StepOut, 0, 109 },
	{ 110271, 2, 67, 67, 9, 10, 7, kSequencePointKind_Normal, 0, 110 },
	{ 110271, 2, 68, 68, 13, 46, 8, kSequencePointKind_Normal, 0, 111 },
	{ 110271, 2, 69, 69, 13, 36, 15, kSequencePointKind_Normal, 0, 112 },
	{ 110271, 2, 70, 70, 13, 34, 22, kSequencePointKind_Normal, 0, 113 },
	{ 110271, 2, 71, 71, 13, 52, 29, kSequencePointKind_Normal, 0, 114 },
	{ 110271, 2, 72, 72, 13, 56, 37, kSequencePointKind_Normal, 0, 115 },
	{ 110271, 2, 73, 73, 9, 10, 45, kSequencePointKind_Normal, 0, 116 },
	{ 110272, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 117 },
	{ 110272, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 118 },
	{ 110272, 2, 76, 76, 9, 10, 0, kSequencePointKind_Normal, 0, 119 },
	{ 110272, 2, 77, 77, 13, 117, 1, kSequencePointKind_Normal, 0, 120 },
	{ 110272, 2, 77, 77, 13, 117, 32, kSequencePointKind_StepOut, 0, 121 },
	{ 110272, 2, 78, 78, 9, 10, 40, kSequencePointKind_Normal, 0, 122 },
	{ 110273, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 123 },
	{ 110273, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 124 },
	{ 110273, 2, 95, 95, 13, 176, 0, kSequencePointKind_Normal, 0, 125 },
	{ 110273, 2, 95, 95, 13, 176, 1, kSequencePointKind_StepOut, 0, 126 },
	{ 110273, 2, 96, 96, 13, 14, 7, kSequencePointKind_Normal, 0, 127 },
	{ 110273, 2, 97, 97, 17, 60, 8, kSequencePointKind_Normal, 0, 128 },
	{ 110273, 2, 97, 97, 17, 60, 11, kSequencePointKind_StepOut, 0, 129 },
	{ 110273, 2, 98, 98, 17, 91, 22, kSequencePointKind_Normal, 0, 130 },
	{ 110273, 2, 98, 98, 17, 91, 25, kSequencePointKind_StepOut, 0, 131 },
	{ 110273, 2, 98, 98, 17, 91, 34, kSequencePointKind_StepOut, 0, 132 },
	{ 110273, 2, 98, 98, 17, 91, 42, kSequencePointKind_StepOut, 0, 133 },
	{ 110273, 2, 98, 98, 17, 91, 48, kSequencePointKind_StepOut, 0, 134 },
	{ 110273, 2, 100, 100, 17, 39, 69, kSequencePointKind_Normal, 0, 135 },
	{ 110273, 2, 100, 100, 0, 0, 86, kSequencePointKind_Normal, 0, 136 },
	{ 110273, 2, 100, 100, 40, 57, 89, kSequencePointKind_Normal, 0, 137 },
	{ 110273, 2, 101, 101, 17, 41, 100, kSequencePointKind_Normal, 0, 138 },
	{ 110273, 2, 101, 101, 17, 41, 106, kSequencePointKind_StepOut, 0, 139 },
	{ 110273, 2, 101, 101, 0, 0, 112, kSequencePointKind_Normal, 0, 140 },
	{ 110273, 2, 102, 102, 17, 18, 115, kSequencePointKind_Normal, 0, 141 },
	{ 110273, 2, 103, 103, 21, 42, 116, kSequencePointKind_Normal, 0, 142 },
	{ 110273, 2, 103, 103, 21, 42, 132, kSequencePointKind_StepOut, 0, 143 },
	{ 110273, 2, 103, 103, 21, 42, 146, kSequencePointKind_StepOut, 0, 144 },
	{ 110273, 2, 103, 103, 0, 0, 154, kSequencePointKind_Normal, 0, 145 },
	{ 110273, 2, 103, 103, 43, 59, 158, kSequencePointKind_Normal, 0, 146 },
	{ 110273, 2, 103, 103, 43, 59, 161, kSequencePointKind_StepOut, 0, 147 },
	{ 110273, 2, 104, 104, 17, 18, 171, kSequencePointKind_Normal, 0, 148 },
	{ 110273, 2, 106, 106, 17, 90, 172, kSequencePointKind_Normal, 0, 149 },
	{ 110273, 2, 106, 106, 17, 90, 173, kSequencePointKind_StepOut, 0, 150 },
	{ 110273, 2, 106, 106, 17, 90, 183, kSequencePointKind_StepOut, 0, 151 },
	{ 110273, 2, 107, 107, 17, 42, 193, kSequencePointKind_Normal, 0, 152 },
	{ 110273, 2, 108, 108, 17, 50, 200, kSequencePointKind_Normal, 0, 153 },
	{ 110273, 2, 109, 109, 17, 56, 207, kSequencePointKind_Normal, 0, 154 },
	{ 110273, 2, 110, 110, 17, 60, 215, kSequencePointKind_Normal, 0, 155 },
	{ 110273, 2, 112, 112, 17, 74, 223, kSequencePointKind_Normal, 0, 156 },
	{ 110273, 2, 112, 112, 17, 74, 229, kSequencePointKind_StepOut, 0, 157 },
	{ 110273, 2, 112, 112, 0, 0, 239, kSequencePointKind_Normal, 0, 158 },
	{ 110273, 2, 113, 113, 17, 18, 243, kSequencePointKind_Normal, 0, 159 },
	{ 110273, 2, 114, 118, 21, 30, 244, kSequencePointKind_Normal, 0, 160 },
	{ 110273, 2, 114, 118, 21, 30, 267, kSequencePointKind_StepOut, 0, 161 },
	{ 110273, 2, 114, 118, 21, 30, 279, kSequencePointKind_StepOut, 0, 162 },
	{ 110273, 2, 119, 119, 17, 18, 289, kSequencePointKind_Normal, 0, 163 },
	{ 110273, 2, 122, 122, 17, 64, 290, kSequencePointKind_Normal, 0, 164 },
	{ 110273, 2, 122, 122, 17, 64, 292, kSequencePointKind_StepOut, 0, 165 },
	{ 110273, 2, 123, 123, 13, 14, 298, kSequencePointKind_Normal, 0, 166 },
	{ 110274, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 167 },
	{ 110274, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 168 },
	{ 110274, 2, 125, 125, 41, 48, 0, kSequencePointKind_Normal, 0, 169 },
	{ 110275, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 170 },
	{ 110275, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 171 },
	{ 110275, 2, 128, 128, 13, 14, 0, kSequencePointKind_Normal, 0, 172 },
	{ 110275, 2, 130, 130, 17, 43, 1, kSequencePointKind_Normal, 0, 173 },
	{ 110275, 2, 130, 130, 0, 0, 19, kSequencePointKind_Normal, 0, 174 },
	{ 110275, 2, 130, 130, 44, 72, 22, kSequencePointKind_Normal, 0, 175 },
	{ 110275, 2, 133, 133, 17, 34, 30, kSequencePointKind_Normal, 0, 176 },
	{ 110275, 2, 135, 135, 17, 42, 41, kSequencePointKind_Normal, 0, 177 },
	{ 110275, 2, 135, 135, 17, 42, 47, kSequencePointKind_StepOut, 0, 178 },
	{ 110275, 2, 136, 136, 17, 63, 53, kSequencePointKind_Normal, 0, 179 },
	{ 110275, 2, 136, 136, 17, 63, 59, kSequencePointKind_StepOut, 0, 180 },
	{ 110275, 2, 136, 136, 0, 0, 65, kSequencePointKind_Normal, 0, 181 },
	{ 110275, 2, 137, 137, 17, 18, 68, kSequencePointKind_Normal, 0, 182 },
	{ 110275, 2, 138, 138, 21, 72, 69, kSequencePointKind_Normal, 0, 183 },
	{ 110275, 2, 138, 138, 21, 72, 81, kSequencePointKind_StepOut, 0, 184 },
	{ 110275, 2, 139, 139, 17, 18, 87, kSequencePointKind_Normal, 0, 185 },
	{ 110275, 2, 140, 140, 17, 74, 88, kSequencePointKind_Normal, 0, 186 },
	{ 110275, 2, 140, 140, 17, 74, 95, kSequencePointKind_StepOut, 0, 187 },
	{ 110275, 2, 140, 140, 17, 74, 100, kSequencePointKind_StepOut, 0, 188 },
	{ 110275, 2, 141, 141, 13, 14, 108, kSequencePointKind_Normal, 0, 189 },
	{ 110276, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 190 },
	{ 110276, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 191 },
	{ 110276, 2, 144, 144, 13, 14, 0, kSequencePointKind_Normal, 0, 192 },
	{ 110276, 2, 145, 145, 17, 31, 1, kSequencePointKind_Normal, 0, 193 },
	{ 110276, 2, 145, 145, 0, 0, 11, kSequencePointKind_Normal, 0, 194 },
	{ 110276, 2, 146, 146, 17, 18, 14, kSequencePointKind_Normal, 0, 195 },
	{ 110276, 2, 147, 147, 21, 61, 15, kSequencePointKind_Normal, 0, 196 },
	{ 110276, 2, 147, 147, 21, 61, 21, kSequencePointKind_StepOut, 0, 197 },
	{ 110276, 2, 148, 148, 21, 37, 27, kSequencePointKind_Normal, 0, 198 },
	{ 110276, 2, 150, 150, 17, 18, 34, kSequencePointKind_Normal, 0, 199 },
	{ 110276, 2, 151, 151, 17, 32, 35, kSequencePointKind_Normal, 0, 200 },
	{ 110276, 2, 152, 152, 13, 14, 47, kSequencePointKind_Normal, 0, 201 },
	{ 110277, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 202 },
	{ 110277, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 203 },
	{ 110277, 2, 155, 155, 13, 14, 0, kSequencePointKind_Normal, 0, 204 },
	{ 110277, 2, 156, 156, 17, 30, 1, kSequencePointKind_Normal, 0, 205 },
	{ 110277, 2, 156, 156, 0, 0, 8, kSequencePointKind_Normal, 0, 206 },
	{ 110277, 2, 157, 157, 17, 18, 11, kSequencePointKind_Normal, 0, 207 },
	{ 110277, 2, 158, 158, 21, 58, 12, kSequencePointKind_Normal, 0, 208 },
	{ 110277, 2, 158, 158, 21, 58, 19, kSequencePointKind_StepOut, 0, 209 },
	{ 110277, 2, 159, 159, 21, 34, 25, kSequencePointKind_Normal, 0, 210 },
	{ 110277, 2, 161, 161, 17, 63, 32, kSequencePointKind_Normal, 0, 211 },
	{ 110277, 2, 161, 161, 17, 63, 38, kSequencePointKind_StepOut, 0, 212 },
	{ 110277, 2, 161, 161, 0, 0, 44, kSequencePointKind_Normal, 0, 213 },
	{ 110277, 2, 162, 162, 17, 18, 47, kSequencePointKind_Normal, 0, 214 },
	{ 110277, 2, 163, 163, 21, 72, 48, kSequencePointKind_Normal, 0, 215 },
	{ 110277, 2, 163, 163, 21, 72, 60, kSequencePointKind_StepOut, 0, 216 },
	{ 110277, 2, 164, 164, 21, 34, 66, kSequencePointKind_Normal, 0, 217 },
	{ 110277, 2, 167, 167, 17, 34, 73, kSequencePointKind_Normal, 0, 218 },
	{ 110277, 2, 167, 167, 0, 0, 80, kSequencePointKind_Normal, 0, 219 },
	{ 110277, 2, 168, 168, 17, 18, 86, kSequencePointKind_Normal, 0, 220 },
	{ 110277, 2, 169, 169, 21, 38, 87, kSequencePointKind_Normal, 0, 221 },
	{ 110277, 2, 169, 169, 0, 0, 102, kSequencePointKind_Normal, 0, 222 },
	{ 110277, 2, 170, 170, 21, 22, 106, kSequencePointKind_Normal, 0, 223 },
	{ 110277, 2, 172, 172, 25, 61, 107, kSequencePointKind_Normal, 0, 224 },
	{ 110277, 2, 172, 172, 25, 61, 113, kSequencePointKind_StepOut, 0, 225 },
	{ 110277, 2, 172, 172, 0, 0, 122, kSequencePointKind_Normal, 0, 226 },
	{ 110277, 2, 173, 173, 25, 26, 126, kSequencePointKind_Normal, 0, 227 },
	{ 110277, 2, 174, 174, 29, 41, 127, kSequencePointKind_Normal, 0, 228 },
	{ 110277, 2, 176, 176, 21, 22, 134, kSequencePointKind_Normal, 0, 229 },
	{ 110277, 2, 178, 178, 21, 116, 135, kSequencePointKind_Normal, 0, 230 },
	{ 110277, 2, 178, 178, 21, 116, 150, kSequencePointKind_StepOut, 0, 231 },
	{ 110277, 2, 178, 178, 21, 116, 157, kSequencePointKind_StepOut, 0, 232 },
	{ 110277, 2, 180, 180, 21, 44, 168, kSequencePointKind_Normal, 0, 233 },
	{ 110277, 2, 180, 180, 0, 0, 187, kSequencePointKind_Normal, 0, 234 },
	{ 110277, 2, 181, 181, 21, 22, 191, kSequencePointKind_Normal, 0, 235 },
	{ 110277, 2, 182, 182, 25, 46, 192, kSequencePointKind_Normal, 0, 236 },
	{ 110277, 2, 183, 183, 25, 61, 199, kSequencePointKind_Normal, 0, 237 },
	{ 110277, 2, 183, 183, 25, 61, 206, kSequencePointKind_StepOut, 0, 238 },
	{ 110277, 2, 184, 184, 21, 22, 212, kSequencePointKind_Normal, 0, 239 },
	{ 110277, 2, 185, 185, 17, 18, 213, kSequencePointKind_Normal, 0, 240 },
	{ 110277, 2, 185, 185, 0, 0, 214, kSequencePointKind_Normal, 0, 241 },
	{ 110277, 2, 187, 187, 17, 18, 219, kSequencePointKind_Normal, 0, 242 },
	{ 110277, 2, 188, 188, 21, 40, 220, kSequencePointKind_Normal, 0, 243 },
	{ 110277, 2, 188, 188, 21, 40, 226, kSequencePointKind_StepOut, 0, 244 },
	{ 110277, 2, 188, 188, 0, 0, 236, kSequencePointKind_Normal, 0, 245 },
	{ 110277, 2, 189, 189, 21, 22, 240, kSequencePointKind_Normal, 0, 246 },
	{ 110277, 2, 190, 190, 25, 42, 241, kSequencePointKind_Normal, 0, 247 },
	{ 110277, 2, 191, 191, 25, 62, 248, kSequencePointKind_Normal, 0, 248 },
	{ 110277, 2, 191, 191, 25, 62, 255, kSequencePointKind_StepOut, 0, 249 },
	{ 110277, 2, 192, 192, 25, 38, 261, kSequencePointKind_Normal, 0, 250 },
	{ 110277, 2, 195, 195, 21, 116, 265, kSequencePointKind_Normal, 0, 251 },
	{ 110277, 2, 195, 195, 21, 116, 280, kSequencePointKind_StepOut, 0, 252 },
	{ 110277, 2, 195, 195, 21, 116, 287, kSequencePointKind_StepOut, 0, 253 },
	{ 110277, 2, 197, 197, 21, 43, 298, kSequencePointKind_Normal, 0, 254 },
	{ 110277, 2, 197, 197, 21, 43, 314, kSequencePointKind_StepOut, 0, 255 },
	{ 110277, 2, 197, 197, 21, 43, 326, kSequencePointKind_StepOut, 0, 256 },
	{ 110277, 2, 197, 197, 0, 0, 334, kSequencePointKind_Normal, 0, 257 },
	{ 110277, 2, 198, 198, 21, 22, 338, kSequencePointKind_Normal, 0, 258 },
	{ 110277, 2, 199, 199, 25, 61, 339, kSequencePointKind_Normal, 0, 259 },
	{ 110277, 2, 199, 199, 25, 61, 346, kSequencePointKind_StepOut, 0, 260 },
	{ 110277, 2, 200, 200, 21, 22, 352, kSequencePointKind_Normal, 0, 261 },
	{ 110277, 2, 201, 201, 17, 18, 353, kSequencePointKind_Normal, 0, 262 },
	{ 110277, 2, 203, 203, 17, 29, 354, kSequencePointKind_Normal, 0, 263 },
	{ 110277, 2, 204, 204, 13, 14, 358, kSequencePointKind_Normal, 0, 264 },
	{ 110280, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 265 },
	{ 110280, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 266 },
	{ 110280, 2, 115, 115, 21, 22, 0, kSequencePointKind_Normal, 0, 267 },
	{ 110280, 2, 116, 116, 25, 52, 1, kSequencePointKind_Normal, 0, 268 },
	{ 110280, 2, 117, 117, 25, 90, 8, kSequencePointKind_Normal, 0, 269 },
	{ 110280, 2, 117, 117, 25, 90, 20, kSequencePointKind_StepOut, 0, 270 },
	{ 110280, 2, 118, 118, 21, 22, 26, kSequencePointKind_Normal, 0, 271 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUniTask_Linq[];
Il2CppSequencePoint g_sequencePointsUniTask_Linq[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "./Library/PackageCache/com.cysharp.unitask@f9fd769be7/Runtime/Linq/UnityExtensions/EveryUpdate.cs", { 247, 57, 206, 244, 245, 122, 112, 77, 79, 124, 218, 153, 116, 212, 216, 184} },
{ "./Library/PackageCache/com.cysharp.unitask@f9fd769be7/Runtime/Linq/UnityExtensions/Timer.cs", { 229, 220, 57, 101, 103, 145, 205, 101, 216, 105, 82, 213, 216, 209, 171, 191} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[8] = 
{
	{ 14221, 1 },
	{ 14221, 2 },
	{ 14224, 1 },
	{ 14223, 1 },
	{ 14222, 1 },
	{ 14227, 2 },
	{ 14226, 2 },
	{ 14225, 2 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[16] = 
{
	{ 0, 13 },
	{ 0, 21 },
	{ 0, 24 },
	{ 0, 94 },
	{ 0, 10 },
	{ 0, 88 },
	{ 0, 49 },
	{ 0, 86 },
	{ 0, 27 },
	{ 0, 42 },
	{ 0, 299 },
	{ 0, 10 },
	{ 0, 110 },
	{ 0, 49 },
	{ 0, 360 },
	{ 0, 27 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[22] = 
{
	{ 13, 0, 1 },
	{ 21, 1, 1 },
	{ 0, 0, 0 },
	{ 24, 2, 1 },
	{ 94, 3, 1 },
	{ 10, 4, 1 },
	{ 88, 5, 1 },
	{ 49, 6, 1 },
	{ 86, 7, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 27, 8, 1 },
	{ 0, 0, 0 },
	{ 42, 9, 1 },
	{ 299, 10, 1 },
	{ 10, 11, 1 },
	{ 110, 12, 1 },
	{ 49, 13, 1 },
	{ 360, 14, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 27, 15, 1 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUniTask_Linq;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUniTask_Linq = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	272,
	(Il2CppSequencePoint*)g_sequencePointsUniTask_Linq,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	8,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
