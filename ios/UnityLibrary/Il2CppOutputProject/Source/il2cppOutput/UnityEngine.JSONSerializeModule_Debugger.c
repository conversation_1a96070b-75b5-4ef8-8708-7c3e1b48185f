﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[7] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_JSONSerializeModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_JSONSerializeModule[69] = 
{
	{ 110061, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 110061, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 110061, 1, 17, 17, 49, 50, 0, kSequencePointKind_Normal, 0, 2 },
	{ 110061, 1, 17, 17, 51, 77, 1, kSequencePointKind_Normal, 0, 3 },
	{ 110061, 1, 17, 17, 51, 77, 3, kSequencePointKind_StepOut, 0, 4 },
	{ 110061, 1, 17, 17, 78, 79, 11, kSequencePointKind_Normal, 0, 5 },
	{ 110062, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 110062, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 110062, 1, 20, 20, 9, 10, 0, kSequencePointKind_Normal, 0, 8 },
	{ 110062, 1, 21, 21, 13, 29, 1, kSequencePointKind_Normal, 0, 9 },
	{ 110062, 1, 21, 21, 0, 0, 6, kSequencePointKind_Normal, 0, 10 },
	{ 110062, 1, 22, 22, 17, 27, 9, kSequencePointKind_Normal, 0, 11 },
	{ 110062, 1, 24, 24, 13, 97, 17, kSequencePointKind_Normal, 0, 12 },
	{ 110062, 1, 24, 24, 0, 0, 52, kSequencePointKind_Normal, 0, 13 },
	{ 110062, 1, 25, 25, 17, 98, 55, kSequencePointKind_Normal, 0, 14 },
	{ 110062, 1, 25, 25, 17, 98, 60, kSequencePointKind_StepOut, 0, 15 },
	{ 110062, 1, 27, 27, 13, 53, 66, kSequencePointKind_Normal, 0, 16 },
	{ 110062, 1, 27, 27, 13, 53, 68, kSequencePointKind_StepOut, 0, 17 },
	{ 110062, 1, 28, 28, 9, 10, 76, kSequencePointKind_Normal, 0, 18 },
	{ 110063, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 19 },
	{ 110063, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 20 },
	{ 110063, 1, 30, 30, 50, 51, 0, kSequencePointKind_Normal, 0, 21 },
	{ 110063, 1, 30, 30, 52, 88, 1, kSequencePointKind_Normal, 0, 22 },
	{ 110063, 1, 30, 30, 52, 88, 7, kSequencePointKind_StepOut, 0, 23 },
	{ 110063, 1, 30, 30, 52, 88, 12, kSequencePointKind_StepOut, 0, 24 },
	{ 110063, 1, 30, 30, 89, 90, 25, kSequencePointKind_Normal, 0, 25 },
	{ 110064, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 26 },
	{ 110064, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 27 },
	{ 110064, 1, 33, 33, 9, 10, 0, kSequencePointKind_Normal, 0, 28 },
	{ 110064, 1, 34, 34, 13, 44, 1, kSequencePointKind_Normal, 0, 29 },
	{ 110064, 1, 34, 34, 13, 44, 2, kSequencePointKind_StepOut, 0, 30 },
	{ 110064, 1, 34, 34, 0, 0, 8, kSequencePointKind_Normal, 0, 31 },
	{ 110064, 1, 35, 35, 17, 29, 11, kSequencePointKind_Normal, 0, 32 },
	{ 110064, 1, 36, 36, 13, 30, 15, kSequencePointKind_Normal, 0, 33 },
	{ 110064, 1, 36, 36, 13, 30, 17, kSequencePointKind_StepOut, 0, 34 },
	{ 110064, 1, 36, 36, 0, 0, 23, kSequencePointKind_Normal, 0, 35 },
	{ 110064, 1, 37, 37, 17, 57, 26, kSequencePointKind_Normal, 0, 36 },
	{ 110064, 1, 37, 37, 17, 57, 31, kSequencePointKind_StepOut, 0, 37 },
	{ 110064, 1, 39, 39, 13, 82, 37, kSequencePointKind_Normal, 0, 38 },
	{ 110064, 1, 39, 39, 13, 82, 38, kSequencePointKind_StepOut, 0, 39 },
	{ 110064, 1, 39, 39, 13, 82, 51, kSequencePointKind_StepOut, 0, 40 },
	{ 110064, 1, 39, 39, 13, 82, 56, kSequencePointKind_StepOut, 0, 41 },
	{ 110064, 1, 39, 39, 0, 0, 65, kSequencePointKind_Normal, 0, 42 },
	{ 110064, 1, 40, 40, 17, 118, 68, kSequencePointKind_Normal, 0, 43 },
	{ 110064, 1, 40, 40, 17, 118, 74, kSequencePointKind_StepOut, 0, 44 },
	{ 110064, 1, 40, 40, 17, 118, 84, kSequencePointKind_StepOut, 0, 45 },
	{ 110064, 1, 40, 40, 17, 118, 89, kSequencePointKind_StepOut, 0, 46 },
	{ 110064, 1, 42, 42, 13, 55, 95, kSequencePointKind_Normal, 0, 47 },
	{ 110064, 1, 42, 42, 13, 55, 98, kSequencePointKind_StepOut, 0, 48 },
	{ 110064, 1, 43, 43, 9, 10, 106, kSequencePointKind_Normal, 0, 49 },
	{ 110065, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 50 },
	{ 110065, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 51 },
	{ 110065, 1, 46, 46, 9, 10, 0, kSequencePointKind_Normal, 0, 52 },
	{ 110065, 1, 47, 47, 13, 44, 1, kSequencePointKind_Normal, 0, 53 },
	{ 110065, 1, 47, 47, 13, 44, 2, kSequencePointKind_StepOut, 0, 54 },
	{ 110065, 1, 47, 47, 0, 0, 8, kSequencePointKind_Normal, 0, 55 },
	{ 110065, 1, 48, 48, 17, 24, 11, kSequencePointKind_Normal, 0, 56 },
	{ 110065, 1, 50, 50, 13, 43, 13, kSequencePointKind_Normal, 0, 57 },
	{ 110065, 1, 50, 50, 0, 0, 18, kSequencePointKind_Normal, 0, 58 },
	{ 110065, 1, 51, 51, 17, 70, 21, kSequencePointKind_Normal, 0, 59 },
	{ 110065, 1, 51, 51, 17, 70, 26, kSequencePointKind_StepOut, 0, 60 },
	{ 110065, 1, 53, 53, 13, 139, 32, kSequencePointKind_Normal, 0, 61 },
	{ 110065, 1, 53, 53, 0, 0, 67, kSequencePointKind_Normal, 0, 62 },
	{ 110065, 1, 54, 54, 17, 116, 70, kSequencePointKind_Normal, 0, 63 },
	{ 110065, 1, 54, 54, 17, 116, 75, kSequencePointKind_StepOut, 0, 64 },
	{ 110065, 1, 56, 56, 13, 84, 81, kSequencePointKind_Normal, 0, 65 },
	{ 110065, 1, 56, 56, 13, 84, 84, kSequencePointKind_StepOut, 0, 66 },
	{ 110065, 1, 56, 56, 13, 84, 89, kSequencePointKind_StepOut, 0, 67 },
	{ 110065, 1, 57, 57, 9, 10, 95, kSequencePointKind_Normal, 0, 68 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_JSONSerializeModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_JSONSerializeModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/JSONSerialize/Public/JsonUtility.bindings.cs", { 172, 37, 124, 247, 142, 68, 238, 238, 204, 96, 145, 144, 249, 86, 105, 7} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = 
{
	{ 14149, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[5] = 
{
	{ 0, 13 },
	{ 0, 78 },
	{ 0, 27 },
	{ 0, 108 },
	{ 0, 96 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[7] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 13, 0, 1 },
	{ 78, 1, 1 },
	{ 27, 2, 1 },
	{ 108, 3, 1 },
	{ 96, 4, 1 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_JSONSerializeModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_JSONSerializeModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	69,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_JSONSerializeModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	1,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
