﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AudioSettings_GetSpeakerMode_m9ACCF1E3EC7A1F151C0B7AAD50B39352B501AEF3 (void);
extern void AudioSettings_SetConfiguration_m23590EFC835CF67DC0972681ACBBBBED0881117E (void);
extern void AudioSettings_GetSampleRate_mF571145D34E609D5F0F8C232FB9FDCCCC521F2A5 (void);
extern void AudioSettings_get_driverCapabilities_mE17FA788F706BBB1CCE5DF0DD33EB96F45E07701 (void);
extern void AudioSettings_get_speakerMode_mE38A6FBB1F5F61B39075062B413742A1E78317A9 (void);
extern void AudioSettings_set_speakerMode_m255E3F7E44959D9A365CBE05859A1B39D8B5373B (void);
extern void AudioSettings_get_profilerCaptureFlags_m3D9B8353004FC3DB1B17C4B2AED61917D85F37B1 (void);
extern void AudioSettings_get_dspTime_m246053C21870BC68BF7BBC5C5AC1B8F25337AEEA (void);
extern void AudioSettings_get_outputSampleRate_mEACF8FD7BFDFBD76EB8FC7E438B3AF19DB57BC6D (void);
extern void AudioSettings_set_outputSampleRate_m999BE5DA776313AB9BCB0DB5ABBBB8C822BF3CC1 (void);
extern void AudioSettings_GetDSPBufferSize_mA02A8E7960F597D6F6CE6402B59410B51EC32F59 (void);
extern void AudioSettings_SetDSPBufferSize_m14F06B59A5E365B746749C0F9310FC3F4C3E198F (void);
extern void AudioSettings_GetSpatializerPluginName_m60C2C3FA09CB0422D1EDAE5185F20BF01689BF00 (void);
extern void AudioSettings_GetConfiguration_mDA005BAD9577EBBE375F6D6C040D7F110508C910 (void);
extern void AudioSettings_Reset_mA15BD1F6C3A6C78D85845D3D86C4271559A12D75 (void);
extern void AudioSettings_add_OnAudioConfigurationChanged_m53F7FD8FF545E23217D2271424843E27AF31585F (void);
extern void AudioSettings_remove_OnAudioConfigurationChanged_mA9CB4575FC75DF3761B8AA0D0259A1255FFF908D (void);
extern void AudioSettings_add_OnAudioSystemShuttingDown_m085D01C33AA0B928411130FAC1EFF9E078880A8F (void);
extern void AudioSettings_remove_OnAudioSystemShuttingDown_m8CC63D0928D01C18BE6BB13D832C4AB795616459 (void);
extern void AudioSettings_add_OnAudioSystemStartedUp_m42720147F78075E9ED9AC288A39C0F7290E8A7C7 (void);
extern void AudioSettings_remove_OnAudioSystemStartedUp_mC47DA438649F6130CE9191E2C22CC05CE18048EE (void);
extern void AudioSettings_InvokeOnAudioConfigurationChanged_m8273D3AEB24F4C3E374238B6F699BE6696808E85 (void);
extern void AudioSettings_InvokeOnAudioSystemShuttingDown_m1B9895D60B3267EBDEC69B9169730DBAD8325E90 (void);
extern void AudioSettings_InvokeOnAudioSystemStartedUp_m7FE042936237E5BDCB20299D8C4CF583B661468C (void);
extern void AudioSettings_get_unityAudioDisabled_mB14CD46E59D1A9A8CBEFA75DB45C826B98DFC8CB (void);
extern void AudioSettings_set_unityAudioDisabled_m4D3E75DD229F6D02A6EAAE66C1DCB377DE399515 (void);
extern void AudioSettings_GetAmbisonicDecoderPluginName_mE0E37E5829FD3F936DEB8107B27A188444F4DC80 (void);
extern void AudioSettings_get_audioSpatialExperience_m9BDFF1E2D2B302ED94A3C0C5DB0DC5AFDFA6C275 (void);
extern void AudioSettings_set_audioSpatialExperience_mAD993CC27DFB54AE89D77770F854F0306AC8B437 (void);
extern void AudioSettings_StartAudioOutput_mB04D851DD0E6115DEEFB55779F880146263C67BE (void);
extern void AudioSettings_StopAudioOutput_m3FE7A8EADAB2FB570BB05F7C353E25E15885D1CB (void);
extern void AudioSettings_get_audioOutputStarted_m99BFE5F17D472653F04BCA0E5662D9728C11F07F (void);
extern void AudioSettings__ctor_mC0A73A13453F1775EEBCDBE82FA77B2EB7299219 (void);
extern void AudioSettings_SetConfiguration_Injected_mCA4F9A7C9034332E10425C289E6DCDA12EEE432F (void);
extern void AudioSettings_GetConfiguration_Injected_m74228B679C071A70B4C5F7C46A88AFC046B9F8DA (void);
extern void AudioConfigurationChangeHandler__ctor_mA9827AB9472EC8EE0A0F0FC24EBC06B4740DD944 (void);
extern void AudioConfigurationChangeHandler_Invoke_m4DC27DD11512481B60071B20284E6886DAE54DE2 (void);
extern void AudioConfigurationChangeHandler_BeginInvoke_mF4E66AE60AA308DFC955F9901EC65EEBAF99BF45 (void);
extern void AudioConfigurationChangeHandler_EndInvoke_m27F4E41715032039C276393290EA3F43E0D6FBCE (void);
extern void Mobile_get_muteState_m64C1E8C61537317A7F153E1A72F7D39D85DA684D (void);
extern void Mobile_set_muteState_m7C9A464BCA3762330E18CCAD79AF6C47B863CA02 (void);
extern void Mobile_get_stopAudioOutputOnMute_m43EC82258D38C418353DFE19F32B51B64B18DCCA (void);
extern void Mobile_set_stopAudioOutputOnMute_m3539E39AE59A8DCCEDFFAF4AC374905753C575B1 (void);
extern void Mobile_get_audioOutputStarted_m4EF227B2EB88D9EE6ED8D4F6B8CD9FDF085C425C (void);
extern void Mobile_add_OnMuteStateChanged_mB08BAE952B242738C9A9FDFF103CD41804AADCAD (void);
extern void Mobile_remove_OnMuteStateChanged_m05E93B6E4585933F1F0DE9690485FBFFBF6F845D (void);
extern void Mobile_InvokeOnMuteStateChanged_mE5242862F948BA9FBB013A2B45F645B6A21E6198 (void);
extern void Mobile_InvokeIsStopAudioOutputOnMuteEnabled_m854CB455C7BE7ADC06BABCB9AA24F60309AE7ED1 (void);
extern void Mobile_StartAudioOutput_m731D1EEEE7A0D56BAADD571BA0FCAC13FB071223 (void);
extern void Mobile_StopAudioOutput_m10B8CEF668EE4967D0AD1D6741B6A37540C28A46 (void);
extern void AudioClip__ctor_m038DA97CB07076D1D9391E1E103F0F41D3622F89 (void);
extern void AudioClip_GetData_mBDEFD7D7C8E5DEA3CCEE2D7DB406DBB0C244924E (void);
extern void AudioClip_SetData_mB49A9BC4639C62B9C8B22319D33D46AAD176BC3B (void);
extern void AudioClip_Construct_Internal_m88BC07CE3F412DDB62820F9430D1D52DA42A26F6 (void);
extern void AudioClip_GetName_m561BBA037957E25D5BC5A962A1AA0C789895C9D1 (void);
extern void AudioClip_CreateUserSound_m34DA102DD6848D555D4A9D45AFAA9D3E5574BC45 (void);
extern void AudioClip_get_length_m6102CB29AF65988797452E4D6E43D4788303873D (void);
extern void AudioClip_get_samples_mDEA01CA75E7DEA0F8D480E4AF97FB96085BCF38E (void);
extern void AudioClip_get_channels_mFEECF5D6389D196BA5102EB79257298B9FDC9F84 (void);
extern void AudioClip_get_frequency_m6647E10F4B2B1335187B0066E82468CCCF19647B (void);
extern void AudioClip_get_isReadyToPlay_m84A0F1A9E45C8C8A6932F68B8B9124A3C55144D0 (void);
extern void AudioClip_get_loadType_m87B9E136629F7C45118EBB5B6A39273A667EE838 (void);
extern void AudioClip_LoadAudioData_mF43E6195AA70C39045DCF08D01C61C9DAA6876DC (void);
extern void AudioClip_UnloadAudioData_m4022A02B836CDC945D634DD7CB4DA0018F718E62 (void);
extern void AudioClip_get_preloadAudioData_mA3D346D89D612D70EED427D95FD6CA254AE02D4A (void);
extern void AudioClip_get_ambisonic_m56A48DCA23ABD92C967B8BD26AEC6D7CE4711304 (void);
extern void AudioClip_get_loadInBackground_m13F3D5F7AEA3A845D3903D93F3E986F616D64FDC (void);
extern void AudioClip_get_loadState_mD5E89ED3E6C1C706C021598FDF86FEB7BF5DE669 (void);
extern void AudioClip_GetData_m1F6480FFDA2E354A7D8C8DE40F61AAB5AF6B4A1D (void);
extern void AudioClip_SetData_m7B473C614C11953D746770F4F89B44600B5A6AF3 (void);
extern void AudioClip_Create_mF6B34084B76355CBC1991D8F4EAA878AA3A033A2 (void);
extern void AudioClip_Create_m151B4E9C35D1B185FF1605DFC93D91DFAE11DB13 (void);
extern void AudioClip_Create_m845EAF7C4DB9F316162F8441D225587CD043B9BF (void);
extern void AudioClip_Create_mE8111F06981E42666B6A9A59D0A3EBE002D2CDFB (void);
extern void AudioClip_Create_m48B1434AA494303489CF28D8794B6CA110B51CD2 (void);
extern void AudioClip_Create_m988FEB04BC74440E65C3CF07414E4867AAE737F8 (void);
extern void AudioClip_add_m_PCMReaderCallback_mA226EA143D90E04117A740FC9FA9F1111346CA83 (void);
extern void AudioClip_remove_m_PCMReaderCallback_m3258A455005F4A94570B4F8FE28A5EDA91A88412 (void);
extern void AudioClip_add_m_PCMSetPositionCallback_mB280AD93A847C65F536D846FECC7DCBE9266C37F (void);
extern void AudioClip_remove_m_PCMSetPositionCallback_m39598139640580138742F129E0510917DF2E233C (void);
extern void AudioClip_InvokePCMReaderCallback_Internal_m766E5705AB5AE16F5F142867CC3758ABE4BF462C (void);
extern void AudioClip_InvokePCMSetPositionCallback_Internal_m986EF703B7DDE42343730DE93A095D05B9F4DBB8 (void);
extern void PCMReaderCallback__ctor_mF621B6CC1A4BA6525190C5037401CF2FD5C0CF28 (void);
extern void PCMReaderCallback_Invoke_m76784C690C36B513E2AA5B0E4FD9831B2C7E5152 (void);
extern void PCMReaderCallback_BeginInvoke_mD10A18EB98467EAFC6F47FDED227A91EE705E064 (void);
extern void PCMReaderCallback_EndInvoke_mE610CCFC5ACBC50C6A644FABA042325E007CB28D (void);
extern void PCMSetPositionCallback__ctor_mD16F77DDB552EB69BB3F5EF39420B2F09F95455B (void);
extern void PCMSetPositionCallback_Invoke_m434D4F02FA25F91DF6199EC5A799C551C7F93702 (void);
extern void PCMSetPositionCallback_BeginInvoke_m62A4BA4AA6BB78DF6B52292981FE526F4ECBE2A0 (void);
extern void PCMSetPositionCallback_EndInvoke_mEE244D859EEBB9B1710C61A7989E6D89AD06244A (void);
extern void AudioBehaviour__ctor_m6D88837496C42A746A51383F3D6F29CA72A9D309 (void);
extern void AudioListener_GetOutputDataHelper_mB30445231F5805870AA625983442CE881D7B66FB (void);
extern void AudioListener_GetSpectrumDataHelper_m2631C609AB0CC9ED84EB55C939EDC78456E0482F (void);
extern void AudioListener_get_volume_m8EAB8FBA127A53E689C1D8C1857781070381974A (void);
extern void AudioListener_set_volume_m72BAF2D558A5449091A59630EBF48095DEB4C721 (void);
extern void AudioListener_get_pause_mD5DE01AAFDE5CB1F747762091F18FF95963FF473 (void);
extern void AudioListener_set_pause_m4D52C9FFC6B10B0F281329FA0FB3CE2C64894F33 (void);
extern void AudioListener_get_velocityUpdateMode_m869BB586C70B4EE05527CA13184A243FE9909A74 (void);
extern void AudioListener_set_velocityUpdateMode_m9A4C2E6F9F814DEAE18D1FDF91A9E5D37DDDEC06 (void);
extern void AudioListener_GetOutputData_mC424B552A74589C2FA12A0EB9DCAEA1261DCB39F (void);
extern void AudioListener_GetOutputData_m296DA3768E887CCD587D5BDD55A3D9AB1ADECA9E (void);
extern void AudioListener_GetSpectrumData_m63364D81841D7FC8EAB015C441E92394236A646B (void);
extern void AudioListener_GetSpectrumData_m66A3A04DD3DF8A2CBE8DE16ED2CBD9AA42EBFABC (void);
extern void AudioListener__ctor_m428A6CC2CFA95A7D6065D33098191569A7412EE4 (void);
extern void AudioSource_GetPitch_m80F6D2BAF966F669253E9231AFCFFC303779913D (void);
extern void AudioSource_SetPitch_mE75DEDF8F37301BDA63E0F545A7A00850C24F53E (void);
extern void AudioSource_PlayHelper_m4DE8C48925C3548BED306DAB9F87939F24A46960 (void);
extern void AudioSource_Play_m10DB5ACD1CC32EE433DBC10416B1450A30DE5F16 (void);
extern void AudioSource_PlayOneShotHelper_mD110EAF42353687BD0B1190EEF30F0C65A4CF265 (void);
extern void AudioSource_Stop_m8A4872F0A2680798CD28894DD28609445C4783F5 (void);
extern void AudioSource_SetCustomCurveHelper_m3921C8867C4075133FEF2629601FF44400BA86E9 (void);
extern void AudioSource_GetCustomCurveHelper_mC0B206F9AE3FA7A900C64194461691451F71B2AD (void);
extern void AudioSource_GetOutputDataHelper_m209E9A63B5FEDFAA87E99B95E6D4D287AADC0444 (void);
extern void AudioSource_GetSpectrumDataHelper_m64E105A054751BD5E7477C7E309992EC0BF274EB (void);
extern void AudioSource_get_volume_m9CCF33BC636562EA282FDE07463B547D70134EE3 (void);
extern void AudioSource_set_volume_mD902BBDBBDE0E3C148609BF3C05096148E90F2C0 (void);
extern void AudioSource_get_pitch_mB1B0B8A52400B5C798BF1E644FE1C2FFA20A9863 (void);
extern void AudioSource_set_pitch_mD14631FC99BF38AAFB356D9C45546BC16CF9E811 (void);
extern void AudioSource_get_time_m130D08644F36736115FE082DAA2ED5E2C9D97A93 (void);
extern void AudioSource_set_time_m6670372FD9C494978B7B3E01B7F4D220616F6204 (void);
extern void AudioSource_get_timeSamples_mF230FF8ABBD5A5250CBC487D0E0FCE286BA95B82 (void);
extern void AudioSource_set_timeSamples_mAC3793C13390C591E4995A88A2CE90E26BBDA6BE (void);
extern void AudioSource_get_clip_m4F5027066F9FC44B44192713142B0C277BB418FE (void);
extern void AudioSource_set_clip_mFF441895E274286C88D9C75ED5CA1B1B39528D70 (void);
extern void AudioSource_get_outputAudioMixerGroup_mE141F3A6337D84F9BD43196A28CC85D092695CAB (void);
extern void AudioSource_set_outputAudioMixerGroup_m10D0A0EAE270424CD2F3BB960CFAA158D9FC24CF (void);
extern void AudioSource_Play_m95DF07111C61D0E0F00257A00384D31531D590C3 (void);
extern void AudioSource_Play_mC9D19FA54347ED102AD9913E3E7528BE969199FB (void);
extern void AudioSource_PlayDelayed_m6A4992F1A010DC12906C6002B22F19082967770E (void);
extern void AudioSource_PlayScheduled_m9F3C7245A13A1D4BC64AFA9A08763357133727D9 (void);
extern void AudioSource_PlayOneShot_m098BCAE084AABB128BB19ED805D2D985E7B75112 (void);
extern void AudioSource_PlayOneShot_mF6FE95C58996B38EF6E7F7482F95F5E15E0AB30B (void);
extern void AudioSource_SetScheduledStartTime_m831CB1AC7E3C70BEFB84892B0A50BA161CE1EDDD (void);
extern void AudioSource_SetScheduledEndTime_mC9BF39919029A6C6CB8981B09A792D45A60A3730 (void);
extern void AudioSource_Stop_m318F17F17A147C77FF6E0A5A7A6BE057DB90F537 (void);
extern void AudioSource_Pause_m2C2A09359E8AA924FEADECC1AFEA519B3C915B26 (void);
extern void AudioSource_UnPause_mC4A6A1E71439A3ADB4664B62DABDF4D79D3B21B9 (void);
extern void AudioSource_get_isPlaying_mC203303F2F7146B2C056CB47B9391463FDF408FC (void);
extern void AudioSource_get_isVirtual_m7AD36D47A866FF2047CF73D6A2E8BD72C9C03759 (void);
extern void AudioSource_PlayClipAtPoint_mA78328A70D3F1088B588EF6F811AAD6577F2B7BF (void);
extern void AudioSource_PlayClipAtPoint_mF9D129487C356127ADA3AB5C0A67C7D00F26E3DD (void);
extern void AudioSource_get_loop_m2D83BF58E1BD1BEE4CC80413C12E761D3310FC2C (void);
extern void AudioSource_set_loop_m834A590939D8456008C0F897FD80B0ECFFB7FE56 (void);
extern void AudioSource_get_ignoreListenerVolume_mC58B59373161017F770D42A36C536511805AE87C (void);
extern void AudioSource_set_ignoreListenerVolume_mAB973FFB2B666C4C6DE3BF34C930C28CC315731D (void);
extern void AudioSource_get_playOnAwake_mB07DE7C6BE0F5E6229FA160DA65BE8B8978BF9D1 (void);
extern void AudioSource_set_playOnAwake_m7EACC6ECEF12D7BA86A4E5A53603F1C8F9E11949 (void);
extern void AudioSource_get_ignoreListenerPause_m544337985D4025632846D4AB4EC1ADD0CF0B4B01 (void);
extern void AudioSource_set_ignoreListenerPause_m1BC14FA0984DEDF62E1CDBAB323950100A0BF2B4 (void);
extern void AudioSource_get_velocityUpdateMode_mEFF48403F8A591A14927408F806E0603391E153B (void);
extern void AudioSource_set_velocityUpdateMode_m379F5704F12211BFB9AF3E3DE6647A6B057C7426 (void);
extern void AudioSource_get_panStereo_mEB4CE5FF235A46C8B7CE62529A9DDA75A15C2505 (void);
extern void AudioSource_set_panStereo_mE3BA673B5F93F731114E8901355A63F07C8A54DF (void);
extern void AudioSource_get_spatialBlend_m06E7948B2813AA3EAE031BD4D1DE61A29416B1CE (void);
extern void AudioSource_set_spatialBlend_mCEE7A3E87A8C146E048B2CA3413FDC7BDB7BE001 (void);
extern void AudioSource_get_spatialize_m5CA89537077D4BB8DBAABFD8EB36D0B89BA8DACB (void);
extern void AudioSource_set_spatialize_mDFA357EDCB0C59EF11F53C845F7ACBF6BF7F7B3C (void);
extern void AudioSource_get_spatializePostEffects_m02D6863671C49B81DFACDA623C74188B1FD950A4 (void);
extern void AudioSource_set_spatializePostEffects_m7CC219B7790E27667D49F4A36C8F62FFF399DA54 (void);
extern void AudioSource_SetCustomCurve_m6597C180AE2DD79DA663ABD76FC26AC816CB7CFB (void);
extern void AudioSource_GetCustomCurve_m39ADDCACC6F9E55D4059E45A13092FFA7C39B23D (void);
extern void AudioSource_get_reverbZoneMix_mA1BE21696195BADD380311B236AA46314911B859 (void);
extern void AudioSource_set_reverbZoneMix_m0AD755F3841952B06F87767F97CA93E2C9545D1E (void);
extern void AudioSource_get_bypassEffects_m0172FACE00674F743A70870EB138B3223D42A35E (void);
extern void AudioSource_set_bypassEffects_m56E81C34448803D4B63105071D96AC644CFFEA9A (void);
extern void AudioSource_get_bypassListenerEffects_m47CE7EC60DB5D13E4D818CFA6D5E1B9D6134EF02 (void);
extern void AudioSource_set_bypassListenerEffects_m321403F18B6174D2E91D080DBF5090C29BC11899 (void);
extern void AudioSource_get_bypassReverbZones_mA640A5F9FF8E52777CF13950D966839729D1B3DF (void);
extern void AudioSource_set_bypassReverbZones_m900FD2BA30F36243B5A5B872B0D019CBAB6AC410 (void);
extern void AudioSource_get_dopplerLevel_m7BF6F31D1E8927E059BC87933AD9B81D63AF97BE (void);
extern void AudioSource_set_dopplerLevel_mB9AC5164E5AF16ACECA3B8E29F5C8573C37E40D6 (void);
extern void AudioSource_get_spread_mC21DF6C651AD67BEB5D721F0EA0B2F3B080F4C77 (void);
extern void AudioSource_set_spread_mDFBC1BF11837C26EF9763A8DEEFC56AF95F6E83F (void);
extern void AudioSource_get_priority_mD4B6D16F6BCB1D5ACA3F2CC096EDA8861DA66881 (void);
extern void AudioSource_set_priority_mD1AB7ED858D8A1233642F5DBA81AEFBE35DD4B40 (void);
extern void AudioSource_get_mute_mE23745FC15F1105556CB7590CA651628FC562DBD (void);
extern void AudioSource_set_mute_m6407E0AEE7F088AC69BD8C1D270C2B2049769B09 (void);
extern void AudioSource_get_minDistance_m459BE399BBBEA04CBBCF50CFB15A09CB3D7431F0 (void);
extern void AudioSource_set_minDistance_m6CBE3A60C03C0F179192FBDD62095B2E9D717690 (void);
extern void AudioSource_get_maxDistance_m8C31CB391B999C8D344EFF0AFB8E20488F7A5F7E (void);
extern void AudioSource_set_maxDistance_m4BF310D54761500A77A6C4841A0BBDBD09225813 (void);
extern void AudioSource_get_rolloffMode_m1D5F4CCF83174583ACF0C365051E58978ED02CFD (void);
extern void AudioSource_set_rolloffMode_m441D9552D8648D6040E66EE2C2650A79DC5E6FB4 (void);
extern void AudioSource_GetOutputData_m7482BBFA8AF652D3D4F13E4D9C9A8DC39A736D68 (void);
extern void AudioSource_GetOutputData_m8AEF8365E3B162E379E1D5FA6C1607999DE458F3 (void);
extern void AudioSource_GetSpectrumData_m8179FFE6FA60C9AE1E6F51B6FAB48C49D81F44FC (void);
extern void AudioSource_GetSpectrumData_m0F3872A4C6B41EFD5A23BA24322B08367BFF0CFE (void);
extern void AudioSource_get_minVolume_m8EBDCA4B2BBD49066F1B981771BCCDD75C197458 (void);
extern void AudioSource_set_minVolume_mA84614CAD182A1E6DF7BF32207618EB882910D33 (void);
extern void AudioSource_get_maxVolume_m4C9A8BEAF838B8BC2B938DAC360D84E44DD8DDA0 (void);
extern void AudioSource_set_maxVolume_mBBF6CA7789D92FC72C394C42F63812FCDF232018 (void);
extern void AudioSource_get_rolloffFactor_m8E37942D2DB42B017B43A3A261783B7CF9ECEEA9 (void);
extern void AudioSource_set_rolloffFactor_mB8ABAD5E639E429028B910A36CE0F16A4041AFDF (void);
extern void AudioSource_SetSpatializerFloat_m124ADF8D1FB75E1677A8891D9BF7138FD8398ADB (void);
extern void AudioSource_GetSpatializerFloat_mEC4F0121F73E3D68A99B527036C431C191554F59 (void);
extern void AudioSource_GetAmbisonicDecoderFloat_m2223F72DBFC57C4E9EEE3465325702ADB728D059 (void);
extern void AudioSource_SetAmbisonicDecoderFloat_mDA21CA3D9B098FB3704B5450D5D90319E9CC9BA8 (void);
extern void AudioSource__ctor_mC67BD65374AC3CDFB702307F4A89932D803191C1 (void);
extern void AudioReverbZone_get_minDistance_m604DC4E7952CCD3F231FC918873D3167AD536201 (void);
extern void AudioReverbZone_set_minDistance_m0C9EE04712E8925D4451B08028B09A127F003893 (void);
extern void AudioReverbZone_get_maxDistance_mD167A784DD3B68E0BD87BA4F427571F38FCC49BF (void);
extern void AudioReverbZone_set_maxDistance_m76A445B58F9F590441660048A23CF87B5136178F (void);
extern void AudioReverbZone_get_reverbPreset_m64434E787D94B9B94E4B71E9B1F367B89DA10DE0 (void);
extern void AudioReverbZone_set_reverbPreset_m06D572EC66CD0366B231EEA444C510039E932508 (void);
extern void AudioReverbZone_get_room_m177AE0751D1D4E24F0872FF14628635CFCA25638 (void);
extern void AudioReverbZone_set_room_m4A6D908E887AFF48B17A4D714D90405A77DCDA23 (void);
extern void AudioReverbZone_get_roomHF_m366BB86E42585AE62C2DF751981B81E8A507FA8B (void);
extern void AudioReverbZone_set_roomHF_m5719F85D38ACCA7E2EF3D2FE0E51118B18FD7730 (void);
extern void AudioReverbZone_get_roomLF_mC0D33BDFF54FE6E613C2BF4FCEB535F14B3B642E (void);
extern void AudioReverbZone_set_roomLF_m7CBC3E5E5F63314FF913864B6E97EC33855FD149 (void);
extern void AudioReverbZone_get_decayTime_mFE6375F34E3EF5CF17421EA6CDAC6A04607D0019 (void);
extern void AudioReverbZone_set_decayTime_mEC8A1C062306166C8DF04BE7E3B9EE9DBCA61EAE (void);
extern void AudioReverbZone_get_decayHFRatio_mB838649CAF924734973B5D32F021AC038E466B3F (void);
extern void AudioReverbZone_set_decayHFRatio_m11530DE7EEB8C8040285FCE7E7BA945B0783036D (void);
extern void AudioReverbZone_get_reflections_mA327CC41BCAE9737DAAF9ECDB6953041F6F27A95 (void);
extern void AudioReverbZone_set_reflections_m8D1A6EF3D58D3B0B3F3A8627AE1C49A492377D43 (void);
extern void AudioReverbZone_get_reflectionsDelay_m25AD8A786CB391F409489591A7A10454C5D7AB13 (void);
extern void AudioReverbZone_set_reflectionsDelay_m888146187B34E8ECD770C5E0A5C59D886FF6CFF6 (void);
extern void AudioReverbZone_get_reverb_m7237DD1B544F18802FC34B8717A337608B40900B (void);
extern void AudioReverbZone_set_reverb_mB3FA34C7B13C292E99FE5DE31DDF767954F9C3A3 (void);
extern void AudioReverbZone_get_reverbDelay_m8FE5034FC1BD0F18D3C032B5FA4EAB6B00EF50F4 (void);
extern void AudioReverbZone_set_reverbDelay_m19CB45AD5F697D28AD05D5917A7157BD92816F21 (void);
extern void AudioReverbZone_get_HFReference_m8449C3C5D8436EA4832CD86317C0DD66F7875B8B (void);
extern void AudioReverbZone_set_HFReference_m331EC64325F258615093855990B8B3181099B517 (void);
extern void AudioReverbZone_get_LFReference_mA7BDD93A2FB05546DBB00E29696D905B5F89829D (void);
extern void AudioReverbZone_set_LFReference_mD790C9855FC151F399990DED609153983EAE4D38 (void);
extern void AudioReverbZone_get_roomRolloffFactor_m32F6F8931E314AB0F3DA617E91D72A0C8E345C22 (void);
extern void AudioReverbZone_set_roomRolloffFactor_mDF41A2C59C38763A6D0AFBE1268BDF976FA7A65E (void);
extern void AudioReverbZone_get_diffusion_mD41B0A5AB6A954AF9453FFD4CA56DE1E99112F81 (void);
extern void AudioReverbZone_set_diffusion_mEBE6DD85D505213B5C017ED539EAAD8897043BAC (void);
extern void AudioReverbZone_get_density_m2735C507661A18EDA8DF843A610AC425A8AA05EF (void);
extern void AudioReverbZone_set_density_mD0AB2B4E9E31D3C30446F5D927E7A4D1B3E6661A (void);
extern void AudioReverbZone__ctor_m6125F0EA7D0C37A533B744C29A9611A688045A77 (void);
extern void AudioLowPassFilter_GetCustomLowpassLevelCurveCopy_m14BAC51BB10B6F00B3BAC19CE7FEC5231F99E15B (void);
extern void AudioLowPassFilter_SetCustomLowpassLevelCurveHelper_m47A4935A70317BB52F179FC0BFC6BEDEEA953CAC (void);
extern void AudioLowPassFilter_get_customCutoffCurve_mE5E13EB0D3D7414FBAE5DDDA31821027AA7FD116 (void);
extern void AudioLowPassFilter_set_customCutoffCurve_m3DD993213650BB06F924B9ECC8B77AEE7FFE9098 (void);
extern void AudioLowPassFilter_get_cutoffFrequency_m81B07E7C2CE10E23F89954A3445AE3E79FC41F76 (void);
extern void AudioLowPassFilter_set_cutoffFrequency_m593B7A476225759056C6DACCEBF92016FEE7B050 (void);
extern void AudioLowPassFilter_get_lowpassResonanceQ_mE4C49AFFCD409A295C6232C1560014D7B661CC95 (void);
extern void AudioLowPassFilter_set_lowpassResonanceQ_m99921D5254062F0CB3FFE1C608CF703AD1BA0F4B (void);
extern void AudioLowPassFilter__ctor_m84825EE53C006C52517EEA9F9F20D80A517F4BCA (void);
extern void AudioHighPassFilter_get_cutoffFrequency_m3B2D24CAE52BABA37E36DDBFFC5710F8346889CD (void);
extern void AudioHighPassFilter_set_cutoffFrequency_mBD5636A124C598DC0B54B8338FF834F422DD676C (void);
extern void AudioHighPassFilter_get_highpassResonanceQ_m64B258EA328733E39E4D37DAA9E29919CBCF29C1 (void);
extern void AudioHighPassFilter_set_highpassResonanceQ_m904290EB5077604E41690C97AE1160AB78FE24B6 (void);
extern void AudioHighPassFilter__ctor_mDAB8BDE4375EB6D37AEF7F10636038195818AF1B (void);
extern void AudioDistortionFilter_get_distortionLevel_mA6CAE95E51279B223842C5460DDF558AC2B9E5BF (void);
extern void AudioDistortionFilter_set_distortionLevel_m1C3A97CA5778481402593F35B9DF69C1ABE7FF3F (void);
extern void AudioDistortionFilter__ctor_m36416ED8C4E280AB866F56E30EEFA6D6F3103635 (void);
extern void AudioEchoFilter_get_delay_mCB330FE5F6FA47E2F150055D5B5DE7CF823B60C3 (void);
extern void AudioEchoFilter_set_delay_m397356ADDF75BA6494C0BB6D7010CE491EB380EA (void);
extern void AudioEchoFilter_get_decayRatio_m8FA03CFD848069F564CDAA623CBF8CBA54EA872E (void);
extern void AudioEchoFilter_set_decayRatio_m11BF2192C4E06EDD856936E0D911121A9B718B85 (void);
extern void AudioEchoFilter_get_dryMix_mC739CE6F775496904C2E4B3F9B4FD461BFF9A9C4 (void);
extern void AudioEchoFilter_set_dryMix_mD6D153883670F3E1C64ACC3C3E9B546A90786B58 (void);
extern void AudioEchoFilter_get_wetMix_mDD2098A367D66E20E7467D9C70775BDF9649FC79 (void);
extern void AudioEchoFilter_set_wetMix_mB73E5B2A5C73C6FC72E90EA1850B9497E4D09370 (void);
extern void AudioEchoFilter__ctor_m5100792BD25686BF7A44FED02F04D974D6DA0A4E (void);
extern void AudioChorusFilter_get_dryMix_m1A9342CF6C25C7A1F39036EF22053D7971C1548A (void);
extern void AudioChorusFilter_set_dryMix_mB1A570CFCA83E7774B818D4CC793EE8826036F57 (void);
extern void AudioChorusFilter_get_wetMix1_m8745993A0B1ACDC2D7B573AFE2D518A9F4D9C4DF (void);
extern void AudioChorusFilter_set_wetMix1_m29F12D2DAA2B9331BB403E2CCD51622A28CA9AB5 (void);
extern void AudioChorusFilter_get_wetMix2_mDA24F10D5F578A8A9B298D20EA96859540E84280 (void);
extern void AudioChorusFilter_set_wetMix2_m0FAF980C47D4AEE1D8E77FD18E87B4FBB6AF5FF4 (void);
extern void AudioChorusFilter_get_wetMix3_m34E39E60D6C3B6B1FB75DB16E6E0E0CAECFBC45D (void);
extern void AudioChorusFilter_set_wetMix3_mDB728423C888D810F3952E0EE1B539965816D5CA (void);
extern void AudioChorusFilter_get_delay_m672795F6AA8E8B25C82E7AD89F700A47EF006D13 (void);
extern void AudioChorusFilter_set_delay_m55A6C17C5720CD54E2635377DF9F2AA909BE6AB9 (void);
extern void AudioChorusFilter_get_rate_mE7CC401F69E7E5576B31B1D9BE22B04567DCF014 (void);
extern void AudioChorusFilter_set_rate_m5F33CF51BDE70FE8A955CAB1E8C4FB7C18E0E5D8 (void);
extern void AudioChorusFilter_get_depth_m7759E9F1054657F72E4DFEFB013BD79A230702BD (void);
extern void AudioChorusFilter_set_depth_m9CF37951B349E28FED863E207CFA2E5E451BDE2B (void);
extern void AudioChorusFilter_get_feedback_m5227967B0E1E516101D09CA21E671FD43E8EDADE (void);
extern void AudioChorusFilter_set_feedback_mFDEC32D90161D51FE3FDA1A8D42597F11DC16DAA (void);
extern void AudioChorusFilter__ctor_mEF2A7D547F029200A840E0457C90369E1BD7CDBA (void);
extern void AudioReverbFilter_get_reverbPreset_m0DD8D4359DB574E72F9A80FFB7BDCE4E15A1044B (void);
extern void AudioReverbFilter_set_reverbPreset_mF0D458E66A09909A3363CB82045D324A15D0A35C (void);
extern void AudioReverbFilter_get_dryLevel_mD08FE87B5DC628565F293CE1B56A9A1307AE0C09 (void);
extern void AudioReverbFilter_set_dryLevel_m38274A3E21082394406A720CAA563CC837C7E38C (void);
extern void AudioReverbFilter_get_room_m29E27BCA006A28DC57C48238713F5C7BB4150FBB (void);
extern void AudioReverbFilter_set_room_mEBB4B620CFC0CF674850C555EA9D387F01EE7B66 (void);
extern void AudioReverbFilter_get_roomHF_m3BCD46183B80171D31662CA8C14FD83EC79FEFA7 (void);
extern void AudioReverbFilter_set_roomHF_mE2105449BD5C099291B33096F7B07B2675F3F2FE (void);
extern void AudioReverbFilter_get_roomRolloffFactor_m87397401ACDCEE355A406B6615C3CAD2ED2A755E (void);
extern void AudioReverbFilter_set_roomRolloffFactor_mEA2B79BA611BF72D7C7877EC4C07ED25C499A3BF (void);
extern void AudioReverbFilter_get_decayTime_m9997163F354BA2E0AC8AEFAF0A176A7F29071C5A (void);
extern void AudioReverbFilter_set_decayTime_m5A0A13CCEB435EC02065BB2BBD52515C813E3269 (void);
extern void AudioReverbFilter_get_decayHFRatio_m0A583A0F2C049AC770DA5DCD96444C42505715CA (void);
extern void AudioReverbFilter_set_decayHFRatio_mAA637FF73CAB073B4B72333E49B8EF87CB835B96 (void);
extern void AudioReverbFilter_get_reflectionsLevel_mEF98034B0B4519F11928C18392244FF9E4D9CEEE (void);
extern void AudioReverbFilter_set_reflectionsLevel_m8155042594B3010E232EC28BF11A77B8A21DDF5C (void);
extern void AudioReverbFilter_get_reflectionsDelay_mD749A1A26AD423F1E1BE315FE63AFB8EF6EBC128 (void);
extern void AudioReverbFilter_set_reflectionsDelay_m36D46F143CFB54001467708C8E99074EE087431F (void);
extern void AudioReverbFilter_get_reverbLevel_m5C1CFAC4F1C69B6F58EE6C99BDF9B58EF9D56D9F (void);
extern void AudioReverbFilter_set_reverbLevel_m1772AB9B29E73CE217FB1311C584A5E905D8678A (void);
extern void AudioReverbFilter_get_reverbDelay_m57423C26BFCAE1C785D53E86D49CF5D53C220F8B (void);
extern void AudioReverbFilter_set_reverbDelay_m39032CE9C3873B99EED57ADDC0CAABF1DC476589 (void);
extern void AudioReverbFilter_get_diffusion_m9A4C2092D061AAC950EA9C366D56ADDE350CB261 (void);
extern void AudioReverbFilter_set_diffusion_mE2CA22104B7E38D40DE2F885941C049719117C94 (void);
extern void AudioReverbFilter_get_density_mAD87B83256988D3C56E012513FC8328599746637 (void);
extern void AudioReverbFilter_set_density_mF31C5ABD0AD7531217B5641D54246D5F0FDE1531 (void);
extern void AudioReverbFilter_get_hfReference_m2C7256088BAB07BA98BE0FFD6D9E7997072F2A8A (void);
extern void AudioReverbFilter_set_hfReference_m4DBCBB0F944EEFD8B617412A540E8D7C926603BC (void);
extern void AudioReverbFilter_get_roomLF_mF155DBD3B5A136D06DE3BF6200FDFFE1941F014A (void);
extern void AudioReverbFilter_set_roomLF_m8EF46CFC0599913676A9E88F76BD25E88911D03D (void);
extern void AudioReverbFilter_get_lfReference_mFA5EAB7CD86E9D96CC26DA9968594D3B11A49104 (void);
extern void AudioReverbFilter_set_lfReference_mA2FB3E1E3EEA40142C72F2CC071DC9ED12C11CA9 (void);
extern void AudioReverbFilter__ctor_m20060D1ED1FAFD97683C98E7ABAE796DA2B3B368 (void);
extern void Microphone_GetMicrophoneDeviceIDFromName_mD33349A5B41E037F04802638690FBA891035C238 (void);
extern void Microphone_StartRecord_m561E1A2B878937E556D6FCABC3FE735CB818D897 (void);
extern void Microphone_EndRecord_m6F4983F3A002DA6F07F979D42D0750A1C3D16156 (void);
extern void Microphone_IsRecording_m59B6BAF774312891C815FCC4D0304256FDC93CB0 (void);
extern void Microphone_GetRecordPosition_m73C6D07638BD2BB56C6FA91FF19AAE591A2782C6 (void);
extern void Microphone_GetDeviceCaps_mEE44F844E84A87EE9B8CB7F241DB365309CC80AC (void);
extern void Microphone_Start_mDA38C5376D122F27D9DEFD2AE811BAE460F2242E (void);
extern void Microphone_End_mB368877FCC9EA1522914006671E637848A0F7CC6 (void);
extern void Microphone_get_devices_mC2821E200C36C599DDC37927DEC9EA725240812D (void);
extern void Microphone_get_isAnyDeviceRecording_mA23DADE28B942271902CACC6FC92B60BA83B5858 (void);
extern void Microphone_IsRecording_m93CA54969E12BF2083326E43794D71F0FED5D653 (void);
extern void Microphone_GetPosition_m13F4C8EBE8536893D9AD8388B0E5B46D62E6A459 (void);
extern void Microphone_GetDeviceCaps_m8C443A4C8FDA86E23E2C5556C4E3AAA6FD181454 (void);
extern void Microphone__ctor_m4EA008BA12EFE1DF177C87B132A41AEFEDEBC067 (void);
extern void AudioRenderer_Start_mC32C9B9C9AAFF501CD62A2B30B270F7BF08E6429 (void);
extern void AudioRenderer_Stop_m08B0E63CC996CB8330B497E47A2161D59A400DDF (void);
extern void AudioRenderer_GetSampleCountForCaptureFrame_m145C6DDF7931564988F0F17210335B1588D84113 (void);
extern void AudioRenderer_AddMixerGroupSink_mE47E9079DB70B240748AC55DB3B4B78705FA7D7B (void);
extern void AudioRenderer_Render_m4AA1F575224F29628BEAF1A651C537BC6120BF35 (void);
extern void AudioRenderer_Internal_AudioRenderer_Start_mCE86EBF2CD0C4BF634EA665D77380A69B73300E0 (void);
extern void AudioRenderer_Internal_AudioRenderer_Stop_mE5058A6788AB5F599D1628FD8B22ABB673CF0DCE (void);
extern void AudioRenderer_Internal_AudioRenderer_GetSampleCountForCaptureFrame_m04AFD7EE530D8A61788927AAF565B0A9F088F6D9 (void);
extern void AudioRenderer_Internal_AudioRenderer_AddMixerGroupSink_m09D2301DB0F152CECFF48436ED175745EE2A49B9 (void);
extern void AudioRenderer_Internal_AudioRenderer_Render_m17C449F516B4C0FFFC0F1D7A96705EFEBDE0799C (void);
extern void AudioRenderer__ctor_m3A3FF24C4440427230DE67F72FBBC64C4F242CF6 (void);
extern void WebCamDevice_get_name_m2BF75E8EA486668299906EAC9B35214890D4601E (void);
extern void WebCamDevice_get_isFrontFacing_mA021D9DBDDB227FEA8AA635E833EAB7718672404 (void);
extern void WebCamDevice_get_kind_mABE370D6A0530D98A0DF38EAD2FF84129B909B59 (void);
extern void WebCamDevice_get_depthCameraName_mBAC7D1A21ADA2365EE56D9CA200B383E8E606772 (void);
extern void WebCamDevice_get_isAutoFocusPointSupported_m05C63A6BCF582174F4CCC78637C02DAF8029CF32 (void);
extern void WebCamDevice_get_availableResolutions_mFF88DEDAE444B4FA6ABE86888C451F7B68ED84F1 (void);
extern void WebCamTexture_get_devices_m57A8D669542CBDDB56B21C8DB62D703B7215EBFA (void);
extern void WebCamTexture__ctor_mAF6018FD8752F8527E23C6A800A314C87322D1DD (void);
extern void WebCamTexture__ctor_m8E716F7FBF6B0C63D60BEF8BF0C6A7EA819951A4 (void);
extern void WebCamTexture__ctor_mD235321AAF77352551BBF18CB34AD6293FD62881 (void);
extern void WebCamTexture__ctor_m75037ECD4778EE55F9987B9E56BD8AF8BC4A28CC (void);
extern void WebCamTexture__ctor_mE75D56732766528D49F3975A45C09A9A96208E99 (void);
extern void WebCamTexture__ctor_mCDCF373E41263EE323147C4A50609EBE9FA28269 (void);
extern void WebCamTexture_Play_mAB313C6F98D5433C414DA31DD96316BDE8D19A26 (void);
extern void WebCamTexture_Pause_mF77809A7C7EFF3B0DCEBDC09A3E1B0758F050367 (void);
extern void WebCamTexture_Stop_m6239B5D1E10C53B57BB30E124E3F541EBD46A184 (void);
extern void WebCamTexture_get_isPlaying_mE53901F249CD5FFF9D1C31DDCC2FC331DCEA80CF (void);
extern void WebCamTexture_get_deviceName_mE590B4CFECD04A12159BC99B5BE682882E5F8C55 (void);
extern void WebCamTexture_set_deviceName_mE98A9B0F7A93E4C1EF0DD5508E20F64CED31DF86 (void);
extern void WebCamTexture_get_requestedFPS_m3AA4E91B40A8F881E6530AEAF5103C0B198F3081 (void);
extern void WebCamTexture_set_requestedFPS_m1B942D1B9D351ECA5ED4D15B8EA6031BB39C3B3E (void);
extern void WebCamTexture_get_requestedWidth_m2A8C340F5E4ECFB144C31383997A17DF5CB7FE08 (void);
extern void WebCamTexture_set_requestedWidth_mF45C8D70FE7C22D84D86AC7CD81270067BF27D67 (void);
extern void WebCamTexture_get_requestedHeight_m0C02E5BB108C1808503A4B9468205BFBC8A7FDEC (void);
extern void WebCamTexture_set_requestedHeight_m2216C94A7C59856727881B36974DD8E09BAB103C (void);
extern void WebCamTexture_get_videoRotationAngle_m2BF420A1243F56415BEF82CC84AB4C7B342C991F (void);
extern void WebCamTexture_get_videoVerticallyMirrored_mDC7525B796A2629927EF113DA199DDE200B1B52A (void);
extern void WebCamTexture_get_didUpdateThisFrame_m3672350773BAA9131D648B886DFD4E3351F045BE (void);
extern void WebCamTexture_GetPixel_mFB7F5DCECD37AEE8CD1051292D22CEEC1B06D044 (void);
extern void WebCamTexture_GetPixels_mA2B07D6D761AA724A6AB1ED04BE0FA195D830FDC (void);
extern void WebCamTexture_GetPixels_mD30134473515AEA70C9DE43392F2ADD95747237A (void);
extern void WebCamTexture_GetPixels32_mCB0ABCB9D7BCB7ECABF7BF911946DE25C8F26B58 (void);
extern void WebCamTexture_GetPixels32_m7F4F302BE0E517451593C43E3F0D0D1B7840E39E (void);
extern void WebCamTexture_get_autoFocusPoint_mC5C2256C72411BE5EB8FF7D31E1DC177220D0669 (void);
extern void WebCamTexture_set_autoFocusPoint_mF3E35AAF12B1F3A88F08FA81EAE61A3E58D5BB0A (void);
extern void WebCamTexture_get_internalAutoFocusPoint_m481D9B757EE9EC40A5FD8E8FA69CE1A02699A555 (void);
extern void WebCamTexture_set_internalAutoFocusPoint_m29347F6736F67D3DFA70DC89580C5443553BEF99 (void);
extern void WebCamTexture_get_isDepth_mDA89B25706EFB6F100AF30C68A905A7544AE364B (void);
extern void WebCamTexture_Internal_CreateWebCamTexture_mE8B5E78C03DAD51A213D6D39D5A154919409BDD9 (void);
extern void WebCamTexture_GetPixel_Injected_mE8B1CFDFB7ED72F53E74D906FAE89DC575039E94 (void);
extern void WebCamTexture_get_internalAutoFocusPoint_Injected_m9ACB2C19E8240C26CA748D68358BE68D6D256ABE (void);
extern void WebCamTexture_set_internalAutoFocusPoint_Injected_m20952B51A9F862174DFF269D23BC8CFC53E709AC (void);
extern void AudioClipExtensionsInternal_Internal_CreateAudioClipSampleProvider_m6203311E8D9D969D6181DA9282898988A6FC7BB1 (void);
extern void AudioSampleProvider_Lookup_mAC9B381D61BBC6504C62B6956FD0CE7BEA989BF7 (void);
extern void AudioSampleProvider_Create_m8C52CB23AEF97E27E69F57440FA3D92650866B03 (void);
extern void AudioSampleProvider__ctor_m83670597E1EBE98AC6468172D464C9129CFCE375 (void);
extern void AudioSampleProvider_Finalize_m21E41F1E30B94174C66C6BF4EE8F415BE773E9DB (void);
extern void AudioSampleProvider_Dispose_mD4D855AD77CF61F9BDF38BE79564E67BA4627E2A (void);
extern void AudioSampleProvider_get_id_m54B9861281DA7D0B1BE31B01B4319986E649BAED (void);
extern void AudioSampleProvider_set_id_mA392A551DD19269C1C8C0134935E6CDAA51831A6 (void);
extern void AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6 (void);
extern void AudioSampleProvider_set_trackIndex_mC2EE9E4029DF0362F4EAC7FE4DA24C3BFC9AEBAB (void);
extern void AudioSampleProvider_get_owner_mC8461FD790AEC7BACE00C8A3F1DE4D00E08EF1AC (void);
extern void AudioSampleProvider_set_owner_mD620FF3D0BC2188F48D2741102385358F0B8E71B (void);
extern void AudioSampleProvider_get_valid_m777CC6D57DE2B2DB45C05A89566087C010569C4E (void);
extern void AudioSampleProvider_get_channelCount_m2E8B29584D96B878521E64400D6673C59E4A10B7 (void);
extern void AudioSampleProvider_set_channelCount_m32FE390F1DBCEF6483FA2B6DD80A1F7542B29DE5 (void);
extern void AudioSampleProvider_get_sampleRate_m75960A76B48C4686467E6C82C3A00F84B029BA57 (void);
extern void AudioSampleProvider_set_sampleRate_m3F8E715867B01BF4238E9A6D0B5705BFB6CDEFC7 (void);
extern void AudioSampleProvider_get_maxSampleFrameCount_m95D4731152161EB84944D54DC7CA3E36912CA53D (void);
extern void AudioSampleProvider_get_availableSampleFrameCount_m535089AFF861FEE5316943F2C521C3417F6F8595 (void);
extern void AudioSampleProvider_get_freeSampleFrameCount_mD19F0C0EC75728D3A7F25D8622B2C760D19EAD68 (void);
extern void AudioSampleProvider_get_freeSampleFrameCountLowThreshold_m28080901615175FE3025DA6949C9791E991798D6 (void);
extern void AudioSampleProvider_set_freeSampleFrameCountLowThreshold_m3C7E8B327BA4894ECB1A010674E655E23FD3BDC1 (void);
extern void AudioSampleProvider_get_enableSampleFramesAvailableEvents_mD1E0CAE084D82F2C8E565C1A7B9DD0AB366C1DB1 (void);
extern void AudioSampleProvider_set_enableSampleFramesAvailableEvents_m80282D7581B9957520C418B3CC06AAA43F5F2237 (void);
extern void AudioSampleProvider_get_enableSilencePadding_m6A6C0E9625800FFFF6DE94A42214381E39556FC8 (void);
extern void AudioSampleProvider_set_enableSilencePadding_mBD5BF4A0B01955991D54CE271B90BDEF405C2C50 (void);
extern void AudioSampleProvider_ConsumeSampleFrames_m7A8AC28A6C57F7FCEE30B82BFE4A3EC364A93A39 (void);
extern void AudioSampleProvider_get_consumeSampleFramesNativeFunction_mA808F07ED91B5E6191BF7CEAF2395BE5643C0EF6 (void);
extern void AudioSampleProvider_QueueSampleFrames_mBB5342E3C7839C39693046489E486D15154ADB96 (void);
extern void AudioSampleProvider_add_sampleFramesAvailable_mF7D95C615AAD8767724B3001D3B82E0071083F78 (void);
extern void AudioSampleProvider_remove_sampleFramesAvailable_mCE2779B438F1696659C06596B6DA6884F617FB7E (void);
extern void AudioSampleProvider_add_sampleFramesOverflow_m31D18E632926686F1A6190D5DF455714D82DBF63 (void);
extern void AudioSampleProvider_remove_sampleFramesOverflow_m833A07BC3B28DACE9DF51C76085BD0FE1443DB7D (void);
extern void AudioSampleProvider_SetSampleFramesAvailableNativeHandler_mC18EFEC722D5014A0A84E09E6AF57A84F8DF24B0 (void);
extern void AudioSampleProvider_ClearSampleFramesAvailableNativeHandler_mB9DABA77DB0B942BBC688A9F0F2F2577264A9A28 (void);
extern void AudioSampleProvider_SetSampleFramesOverflowNativeHandler_m43D40634FCD09DED46C3DAC7CB07319A9E4646EC (void);
extern void AudioSampleProvider_ClearSampleFramesOverflowNativeHandler_mB26B477B3C19521F08CD3E730408C653C031E19C (void);
extern void AudioSampleProvider_InvokeSampleFramesAvailable_mEB16F7230AB65A3576BF053AC5719F8E134FBCD4 (void);
extern void AudioSampleProvider_InvokeSampleFramesOverflow_m66593173A527981F5EB2A5EF77B0C9119DAB5E15 (void);
extern void AudioSampleProvider_InternalCreateSampleProvider_m3BD26F837CD7D2ED547A0AE02AC079A72BEE998D (void);
extern void AudioSampleProvider_InternalRemove_mBC651EF68D78CFB4310E3022002070ED2174BD24 (void);
extern void AudioSampleProvider_InternalGetFormatInfo_m8725A2417CD3304F009AA19328606EF2434FD703 (void);
extern void AudioSampleProvider_InternalGetScriptingPtr_mA1A158321BE37492A745702503F4233E234C1D62 (void);
extern void AudioSampleProvider_InternalSetScriptingPtr_m6FE4C028426BB46CC9648F66930324E2A708DEF8 (void);
extern void AudioSampleProvider_InternalIsValid_mCBDAABC4B1ED912B8F13D66AA587175BC549A548 (void);
extern void AudioSampleProvider_InternalGetMaxSampleFrameCount_m33AF5AF071B5354DC657C8521ACD6379094F8A79 (void);
extern void AudioSampleProvider_InternalGetAvailableSampleFrameCount_mD9A3A290D03ADB2D14625A2F2DC56D6FFD6D1AA0 (void);
extern void AudioSampleProvider_InternalGetFreeSampleFrameCount_mF71ABBF38D3686A855500D81EBC91F209C91E989 (void);
extern void AudioSampleProvider_InternalGetFreeSampleFrameCountLowThreshold_m011436ED9F51F1BDB79C7D2C418971EB29ED8F6B (void);
extern void AudioSampleProvider_InternalSetFreeSampleFrameCountLowThreshold_mD5788B7F4F5C2069F04AD6D343148ED9916E319B (void);
extern void AudioSampleProvider_InternalGetEnableSampleFramesAvailableEvents_m5828616745AD1EA4FC310DDE5001BBE5D3F5E62F (void);
extern void AudioSampleProvider_InternalSetEnableSampleFramesAvailableEvents_mB1F3F754FCD1952FC3C6B27EEAC27A961A30C66F (void);
extern void AudioSampleProvider_InternalSetSampleFramesAvailableNativeHandler_m76AD17D2A2B6F1387747538E681DC72EAC0A56A1 (void);
extern void AudioSampleProvider_InternalClearSampleFramesAvailableNativeHandler_m28838F1E2D5A6CE0910E1045A3EA27D1ADD311EA (void);
extern void AudioSampleProvider_InternalSetSampleFramesOverflowNativeHandler_mDEB472C3FBF948D64213C9D5C4748B819FFB08E3 (void);
extern void AudioSampleProvider_InternalClearSampleFramesOverflowNativeHandler_m4C2BB3111C516BF7AD0ED0503FE77F15E6DA553B (void);
extern void AudioSampleProvider_InternalGetEnableSilencePadding_m8C2198276DCB909C7327096B728C5489F012E4ED (void);
extern void AudioSampleProvider_InternalSetEnableSilencePadding_m4B055B4F1E7877BDC67BB611BBA5390193171D3F (void);
extern void AudioSampleProvider_InternalGetConsumeSampleFramesNativeFunctionPtr_m9F24D2DADDF9A36E4E81D16DE1F9C40A3A4FFE89 (void);
extern void AudioSampleProvider_InternalQueueSampleFrames_mDF93286C58F479F4FDBFF783D00B426F85C6624D (void);
extern void ConsumeSampleFramesNativeFunction__ctor_m32F2C9615F5256132FBF2EAD179A85B05E5B91B8 (void);
extern void ConsumeSampleFramesNativeFunction_Invoke_mBE5976A97FA5133DA1EFD35257CB184D71181B99 (void);
extern void ConsumeSampleFramesNativeFunction_BeginInvoke_mAF469C03FAF85358EF4CB6505BA73B2DDCA131B4 (void);
extern void ConsumeSampleFramesNativeFunction_EndInvoke_mE732490B182912952B918B474B638CD6B2176282 (void);
extern void SampleFramesHandler__ctor_m7DDE0BAD439CD80791140C7D42D661B598A7663A (void);
extern void SampleFramesHandler_Invoke_m478D5645634B8C734E58B59CF7750797FC54F1BC (void);
extern void SampleFramesHandler_BeginInvoke_mC73D6F192B4245376061EF30DCAB68643E4E1F34 (void);
extern void SampleFramesHandler_EndInvoke_m00D2842B05406BA4B8DC02ED0628CB2E0B16C45E (void);
extern void SampleFramesEventNativeFunction__ctor_m9950F1156D2F6C5BB6BD4E1A03305892C1073F6C (void);
extern void SampleFramesEventNativeFunction_Invoke_m8BB80F3A2283D82F99F79971A7C5E21D2BE2F3A8 (void);
extern void SampleFramesEventNativeFunction_BeginInvoke_mE9212A20D510C9888772566B1191DC712862BDA4 (void);
extern void SampleFramesEventNativeFunction_EndInvoke_m38E42512CE033289075453FFB8DB16AA45B7F649 (void);
extern void AudioSampleProviderExtensionsInternal_GetSpeed_m334970FF8D1B37C55EA261B8E3D2F5EF37121261 (void);
extern void AudioSampleProviderExtensionsInternal_InternalGetAudioSampleProviderSpeed_m4B89D873E08293C6DDDADB795D061049EAFEEEA8 (void);
extern void AudioSourceExtensionsInternal_RegisterSampleProvider_m082540DB541FF637B967212DA366A12F50B05978 (void);
extern void AudioSourceExtensionsInternal_UnregisterSampleProvider_m2E7741EB7338D32D4E47332CDAB2911DD524F4EB (void);
extern void AudioSourceExtensionsInternal_Internal_RegisterSampleProviderWithAudioSource_m44FC79BA5403BE0101D7CA745089AD5A70362528 (void);
extern void AudioSourceExtensionsInternal_Internal_UnregisterSampleProviderFromAudioSource_mE36C0772279DFEE17E6D9A07510D91C7058247D7 (void);
extern void AudioManagerTestProxy_ComputeAudibilityConsistency_mF0ED94512DBE88A3F0B2A1769CB0CC908E1A7DBA (void);
extern void AudioManagerTestProxy__ctor_mAA83AB2AF70DB86C7B9A945C5706DCF81D72CD7C (void);
extern void AudioClipPlayable_Create_m0B42D1553D2752E7E98D10677B2A7DE8CE4DEFD8 (void);
extern void AudioClipPlayable_CreateHandle_mD7CA217BC6DE899D586272C372456EBB7ED2A825 (void);
extern void AudioClipPlayable__ctor_m4E686B92CFA7C6A36AA794B1E8C36B1E60605751 (void);
extern void AudioClipPlayable_GetHandle_mEA1D664328FF9B08E4F7D5EBCD4B51A754D97C44 (void);
extern void AudioClipPlayable_op_Implicit_m49C31C8D61799E247CA509124B3E6E8F4009D237 (void);
extern void AudioClipPlayable_op_Explicit_mA9B643768436960809DB09A469A0DF09B716446B (void);
extern void AudioClipPlayable_Equals_m9C1C75ACBB74FE06AD02BE4643F6EB39413EFF83 (void);
extern void AudioClipPlayable_GetClip_m009747EE98DE59A9C7F31A069EEE77EBAC0A5A6F (void);
extern void AudioClipPlayable_SetClip_mC1D588D0FB1D42630C2A76F82A1C2BDA61009926 (void);
extern void AudioClipPlayable_GetLooped_m18E3568715253DAF09D74A3C08CBF25A07B54660 (void);
extern void AudioClipPlayable_SetLooped_m7B0B25EC1369786D181CB8DF9BF31E5BFF81201B (void);
extern void AudioClipPlayable_GetVolume_mEB0C89F492CD12218C579365A1F9B07D96E7AE8B (void);
extern void AudioClipPlayable_SetVolume_m3553EC43CBA43CA7802292710A1A284A6DEF8FCC (void);
extern void AudioClipPlayable_GetStereoPan_mB766EBDC23718A7FDC8D3510FB9A2A0B2AA38E69 (void);
extern void AudioClipPlayable_SetStereoPan_mB8403E93B06B2ECCD0D4D6B0D55C151D0CCCC494 (void);
extern void AudioClipPlayable_GetSpatialBlend_mB3479E606D27347E8AE9470A50FF13F567D1B5DA (void);
extern void AudioClipPlayable_SetSpatialBlend_m72F1B86E4E5940E8587C1EA32BB48463A00810BB (void);
extern void AudioClipPlayable_IsPlaying_mB24D86E9BA044A3CC82FB12866DC2EB61ADE238F (void);
extern void AudioClipPlayable_IsChannelPlaying_mA1C0DFAD142F4D43C5BB4EFE2929708CCE59EDA4 (void);
extern void AudioClipPlayable_GetStartDelay_m9C96B753369B9CDA1482CFBB76AFBE22DDED5E47 (void);
extern void AudioClipPlayable_SetStartDelay_mD7F1860C6EAD27123871D6B95B9D2B1A21DA17C3 (void);
extern void AudioClipPlayable_GetPauseDelay_m402FFC47A9FA96C5015D1268A7B0063308A3724A (void);
extern void AudioClipPlayable_GetPauseDelay_mAECC9DDE01CAFD50761079E4FBC1B00E8B9FE48C (void);
extern void AudioClipPlayable_Seek_m9444B6B715ECAB55500E1BBD04A1B6BD22980ECD (void);
extern void AudioClipPlayable_Seek_m2AB327B535C677793BBADEA634B7AAC9442B1391 (void);
extern void AudioClipPlayable_GetClipInternal_m0B594FA02A34EEFC4CAB34B778FF10CD538EDF71 (void);
extern void AudioClipPlayable_SetClipInternal_mACA6B51D98C05266D1D0D05D86B5C675006C2DDD (void);
extern void AudioClipPlayable_GetLoopedInternal_m337CC96BD2CFEFCDB78AE704FD99AF8ED73B34FA (void);
extern void AudioClipPlayable_SetLoopedInternal_mB8FA25F4121BEA4638075D388C2EA66011236341 (void);
extern void AudioClipPlayable_GetVolumeInternal_m5ED28FAFC2B3077BAE2CA8F90612C9932C272FCE (void);
extern void AudioClipPlayable_SetVolumeInternal_m71EFEE49486AACBE4F9C936F4898448AB071B55F (void);
extern void AudioClipPlayable_GetStereoPanInternal_mCC24F11786E25F26876873CCF6CE4B1368608244 (void);
extern void AudioClipPlayable_SetStereoPanInternal_m206A1B777709E25F42C9EF0BEAF3A84D622D4A90 (void);
extern void AudioClipPlayable_GetSpatialBlendInternal_m588B81B3B008D87A79B2DA76494BF359B2AA8125 (void);
extern void AudioClipPlayable_SetSpatialBlendInternal_mBAE4A56ACEE90D4732C5D2C5D2D721C65B3DD55B (void);
extern void AudioClipPlayable_GetIsChannelPlayingInternal_mE3D91DB6102F7D166F4D495E22330B98A4B14721 (void);
extern void AudioClipPlayable_GetStartDelayInternal_m45F9633CF2D28DD344D68231443AEF11CA3B12F6 (void);
extern void AudioClipPlayable_SetStartDelayInternal_m1A3816547BF61B7448654300EF078D464A5618CD (void);
extern void AudioClipPlayable_GetPauseDelayInternal_m0993546EFBB076183302FF7902815057D7A5A399 (void);
extern void AudioClipPlayable_SetPauseDelayInternal_m803360EBD0BB692C31D31D620AAB9C8D0CD7A9D1 (void);
extern void AudioClipPlayable_InternalCreateAudioClipPlayable_mBDAA54F35207F6C62F87CAE268732072C7287616 (void);
extern void AudioClipPlayable_ValidateType_mA9D284B70B2086E4CA09CEC913D449F3195C891E (void);
extern void AudioMixer__ctor_m8BB9BFC96DB436EE4CECE0BECECD5DFC7559058D (void);
extern void AudioMixer_get_outputAudioMixerGroup_m0B5B993AB7FD678B15276E06B226B06B709C560C (void);
extern void AudioMixer_set_outputAudioMixerGroup_m7362B6469DCAFB7D1A65BC20BDCF42AE76F90306 (void);
extern void AudioMixer_FindSnapshot_m289C3F55A58E9DE4EEE456AEFE444ECA4D3496C5 (void);
extern void AudioMixer_FindMatchingGroups_m4541BE177FFA0225AF159156ABB5FE3F5F6CF2CF (void);
extern void AudioMixer_TransitionToSnapshot_m308E8D50A4573EFDD381ED7DEC23162F8CD0EB5D (void);
extern void AudioMixer_TransitionToSnapshotInternal_m9D07330A3EF997CC4BB165829D8D2AD6187A8DD0 (void);
extern void AudioMixer_TransitionToSnapshots_m87D4E352A2696F6BF945EB1A519888A9B38BF7C6 (void);
extern void AudioMixer_get_updateMode_mBA8246F84EE1B3F135D4E76B91128DB295CFE5F9 (void);
extern void AudioMixer_set_updateMode_mA823193DD10F7737E036013BB63CEC2ED65E184F (void);
extern void AudioMixer_SetFloat_m4789959013BE79E4F84F446405914908ADC3F335 (void);
extern void AudioMixer_ClearFloat_mD6FD7AE99760D83DA6ECBCCF9A0F07F10C12E665 (void);
extern void AudioMixer_GetFloat_mAED8D277AD30D0346292555CBF81D8961117AEC9 (void);
extern void AudioMixer_GetAbsoluteAudibilityFromGroup_m109E7C4D1ECABCB80EB5F7C1952ACDD77986772D (void);
extern void AudioMixerGroup__ctor_m0D3A84EDAC9B01AEC0B07AFB1F5B1807F74B9CB8 (void);
extern void AudioMixerGroup_get_audioMixer_mFDEDBF17C3B84C6B777D2BF75CF40EECF4C889E4 (void);
extern void AudioMixerPlayable_Create_m323B71EBE332DAF5B2632BAB657BEA33F5870E71 (void);
extern void AudioMixerPlayable_CreateHandle_mCA2A6EF1CC490A8E59C8EE2020D3304D66B96852 (void);
extern void AudioMixerPlayable__ctor_m2CDDE33FCE0B3F7D9EB482D76515FBF771285F47 (void);
extern void AudioMixerPlayable_GetHandle_m6C182D9794E901D123223BB57738A302BEAB41FD (void);
extern void AudioMixerPlayable_op_Implicit_m479542341C4CAEE00B4F7DD0B68E39F8E4388974 (void);
extern void AudioMixerPlayable_op_Explicit_m5BFAA52FB8DF95288F7FD2DBEB0907687F98CD0B (void);
extern void AudioMixerPlayable_Equals_mDFB945EB48199A338BAD00D40FB8EEC34CF64D57 (void);
extern void AudioMixerPlayable_CreateAudioMixerPlayableInternal_mB91DD585A6A2903F01F49719CA0045C8727B5AA9 (void);
extern void AudioMixerSnapshot__ctor_m68E824AB2B201928EABE2FFCDDC401EE905D2D06 (void);
extern void AudioMixerSnapshot_get_audioMixer_mCC13199F7F9EBB2C5510DFA750A501747229CBF5 (void);
extern void AudioMixerSnapshot_TransitionTo_mABDDC418B89323A930A900E55336B5989CFD4AC8 (void);
extern void AudioPlayableBinding_Create_m4E506BD0649ADAB9D1C2D924D2767FBB0C63DACB (void);
extern void AudioPlayableBinding_CreateAudioOutput_m2BE0EDC74ADA082B5CD6247ACD78EFCBDB85ADC3 (void);
extern void AudioPlayableGraphExtensions_InternalCreateAudioOutput_m5EF8B18878AFFE0B462E615C8243C2433A3F5E78 (void);
extern void AudioPlayableOutput_Create_m90DF38B28813932D4246094FD4DB6105572619D2 (void);
extern void AudioPlayableOutput__ctor_mBEA3D7EE652908558720EDC0D40F7BF3EC50D409 (void);
extern void AudioPlayableOutput_get_Null_m8404386CE6506C8C0574B74023EB1BC17A45F205 (void);
extern void AudioPlayableOutput_GetHandle_m55153D572F8FB9BCFF3843402A20280273B934AE (void);
extern void AudioPlayableOutput_op_Implicit_mD2D35763126BDE08E10CA74D8E8C49988477F428 (void);
extern void AudioPlayableOutput_op_Explicit_mC51D8736040715BAA8AC5FA22B6E89F9CDBF25C1 (void);
extern void AudioPlayableOutput_GetTarget_m2258506791A9E370329AFD2D4FE9FD7CD2D2DB48 (void);
extern void AudioPlayableOutput_SetTarget_m34EE86E5C2833F12993681ABE5AC85961836DE04 (void);
extern void AudioPlayableOutput_GetEvaluateOnSeek_m23EF84B9C518CDB97E68214E2A2BD89A1FCD6F6E (void);
extern void AudioPlayableOutput_SetEvaluateOnSeek_mB3266A8A68E94933A82A0C431B7A2E7321929D92 (void);
extern void AudioPlayableOutput_InternalGetTarget_m34CC5798C297222E92D216941F1A98E76BF55F47 (void);
extern void AudioPlayableOutput_InternalSetTarget_m3A9912A00BC052FCEDEBB5EB75DCBE5B2B9DA86E (void);
extern void AudioPlayableOutput_InternalGetEvaluateOnSeek_m13F0BE232D32E1C4F6982CC22A386ACB3741560A (void);
extern void AudioPlayableOutput_InternalSetEvaluateOnSeek_m639F527A20B97D2617898E223B5CDC1DC8548804 (void);
static Il2CppMethodPointer s_methodPointers[543] = 
{
	AudioSettings_GetSpeakerMode_m9ACCF1E3EC7A1F151C0B7AAD50B39352B501AEF3,
	AudioSettings_SetConfiguration_m23590EFC835CF67DC0972681ACBBBBED0881117E,
	AudioSettings_GetSampleRate_mF571145D34E609D5F0F8C232FB9FDCCCC521F2A5,
	AudioSettings_get_driverCapabilities_mE17FA788F706BBB1CCE5DF0DD33EB96F45E07701,
	AudioSettings_get_speakerMode_mE38A6FBB1F5F61B39075062B413742A1E78317A9,
	AudioSettings_set_speakerMode_m255E3F7E44959D9A365CBE05859A1B39D8B5373B,
	AudioSettings_get_profilerCaptureFlags_m3D9B8353004FC3DB1B17C4B2AED61917D85F37B1,
	AudioSettings_get_dspTime_m246053C21870BC68BF7BBC5C5AC1B8F25337AEEA,
	AudioSettings_get_outputSampleRate_mEACF8FD7BFDFBD76EB8FC7E438B3AF19DB57BC6D,
	AudioSettings_set_outputSampleRate_m999BE5DA776313AB9BCB0DB5ABBBB8C822BF3CC1,
	AudioSettings_GetDSPBufferSize_mA02A8E7960F597D6F6CE6402B59410B51EC32F59,
	AudioSettings_SetDSPBufferSize_m14F06B59A5E365B746749C0F9310FC3F4C3E198F,
	AudioSettings_GetSpatializerPluginName_m60C2C3FA09CB0422D1EDAE5185F20BF01689BF00,
	AudioSettings_GetConfiguration_mDA005BAD9577EBBE375F6D6C040D7F110508C910,
	AudioSettings_Reset_mA15BD1F6C3A6C78D85845D3D86C4271559A12D75,
	AudioSettings_add_OnAudioConfigurationChanged_m53F7FD8FF545E23217D2271424843E27AF31585F,
	AudioSettings_remove_OnAudioConfigurationChanged_mA9CB4575FC75DF3761B8AA0D0259A1255FFF908D,
	AudioSettings_add_OnAudioSystemShuttingDown_m085D01C33AA0B928411130FAC1EFF9E078880A8F,
	AudioSettings_remove_OnAudioSystemShuttingDown_m8CC63D0928D01C18BE6BB13D832C4AB795616459,
	AudioSettings_add_OnAudioSystemStartedUp_m42720147F78075E9ED9AC288A39C0F7290E8A7C7,
	AudioSettings_remove_OnAudioSystemStartedUp_mC47DA438649F6130CE9191E2C22CC05CE18048EE,
	AudioSettings_InvokeOnAudioConfigurationChanged_m8273D3AEB24F4C3E374238B6F699BE6696808E85,
	AudioSettings_InvokeOnAudioSystemShuttingDown_m1B9895D60B3267EBDEC69B9169730DBAD8325E90,
	AudioSettings_InvokeOnAudioSystemStartedUp_m7FE042936237E5BDCB20299D8C4CF583B661468C,
	AudioSettings_get_unityAudioDisabled_mB14CD46E59D1A9A8CBEFA75DB45C826B98DFC8CB,
	AudioSettings_set_unityAudioDisabled_m4D3E75DD229F6D02A6EAAE66C1DCB377DE399515,
	AudioSettings_GetAmbisonicDecoderPluginName_mE0E37E5829FD3F936DEB8107B27A188444F4DC80,
	AudioSettings_get_audioSpatialExperience_m9BDFF1E2D2B302ED94A3C0C5DB0DC5AFDFA6C275,
	AudioSettings_set_audioSpatialExperience_mAD993CC27DFB54AE89D77770F854F0306AC8B437,
	AudioSettings_StartAudioOutput_mB04D851DD0E6115DEEFB55779F880146263C67BE,
	AudioSettings_StopAudioOutput_m3FE7A8EADAB2FB570BB05F7C353E25E15885D1CB,
	AudioSettings_get_audioOutputStarted_m99BFE5F17D472653F04BCA0E5662D9728C11F07F,
	AudioSettings__ctor_mC0A73A13453F1775EEBCDBE82FA77B2EB7299219,
	AudioSettings_SetConfiguration_Injected_mCA4F9A7C9034332E10425C289E6DCDA12EEE432F,
	AudioSettings_GetConfiguration_Injected_m74228B679C071A70B4C5F7C46A88AFC046B9F8DA,
	AudioConfigurationChangeHandler__ctor_mA9827AB9472EC8EE0A0F0FC24EBC06B4740DD944,
	AudioConfigurationChangeHandler_Invoke_m4DC27DD11512481B60071B20284E6886DAE54DE2,
	AudioConfigurationChangeHandler_BeginInvoke_mF4E66AE60AA308DFC955F9901EC65EEBAF99BF45,
	AudioConfigurationChangeHandler_EndInvoke_m27F4E41715032039C276393290EA3F43E0D6FBCE,
	Mobile_get_muteState_m64C1E8C61537317A7F153E1A72F7D39D85DA684D,
	Mobile_set_muteState_m7C9A464BCA3762330E18CCAD79AF6C47B863CA02,
	Mobile_get_stopAudioOutputOnMute_m43EC82258D38C418353DFE19F32B51B64B18DCCA,
	Mobile_set_stopAudioOutputOnMute_m3539E39AE59A8DCCEDFFAF4AC374905753C575B1,
	Mobile_get_audioOutputStarted_m4EF227B2EB88D9EE6ED8D4F6B8CD9FDF085C425C,
	Mobile_add_OnMuteStateChanged_mB08BAE952B242738C9A9FDFF103CD41804AADCAD,
	Mobile_remove_OnMuteStateChanged_m05E93B6E4585933F1F0DE9690485FBFFBF6F845D,
	Mobile_InvokeOnMuteStateChanged_mE5242862F948BA9FBB013A2B45F645B6A21E6198,
	Mobile_InvokeIsStopAudioOutputOnMuteEnabled_m854CB455C7BE7ADC06BABCB9AA24F60309AE7ED1,
	Mobile_StartAudioOutput_m731D1EEEE7A0D56BAADD571BA0FCAC13FB071223,
	Mobile_StopAudioOutput_m10B8CEF668EE4967D0AD1D6741B6A37540C28A46,
	AudioClip__ctor_m038DA97CB07076D1D9391E1E103F0F41D3622F89,
	AudioClip_GetData_mBDEFD7D7C8E5DEA3CCEE2D7DB406DBB0C244924E,
	AudioClip_SetData_mB49A9BC4639C62B9C8B22319D33D46AAD176BC3B,
	AudioClip_Construct_Internal_m88BC07CE3F412DDB62820F9430D1D52DA42A26F6,
	AudioClip_GetName_m561BBA037957E25D5BC5A962A1AA0C789895C9D1,
	AudioClip_CreateUserSound_m34DA102DD6848D555D4A9D45AFAA9D3E5574BC45,
	AudioClip_get_length_m6102CB29AF65988797452E4D6E43D4788303873D,
	AudioClip_get_samples_mDEA01CA75E7DEA0F8D480E4AF97FB96085BCF38E,
	AudioClip_get_channels_mFEECF5D6389D196BA5102EB79257298B9FDC9F84,
	AudioClip_get_frequency_m6647E10F4B2B1335187B0066E82468CCCF19647B,
	AudioClip_get_isReadyToPlay_m84A0F1A9E45C8C8A6932F68B8B9124A3C55144D0,
	AudioClip_get_loadType_m87B9E136629F7C45118EBB5B6A39273A667EE838,
	AudioClip_LoadAudioData_mF43E6195AA70C39045DCF08D01C61C9DAA6876DC,
	AudioClip_UnloadAudioData_m4022A02B836CDC945D634DD7CB4DA0018F718E62,
	AudioClip_get_preloadAudioData_mA3D346D89D612D70EED427D95FD6CA254AE02D4A,
	AudioClip_get_ambisonic_m56A48DCA23ABD92C967B8BD26AEC6D7CE4711304,
	AudioClip_get_loadInBackground_m13F3D5F7AEA3A845D3903D93F3E986F616D64FDC,
	AudioClip_get_loadState_mD5E89ED3E6C1C706C021598FDF86FEB7BF5DE669,
	AudioClip_GetData_m1F6480FFDA2E354A7D8C8DE40F61AAB5AF6B4A1D,
	AudioClip_SetData_m7B473C614C11953D746770F4F89B44600B5A6AF3,
	AudioClip_Create_mF6B34084B76355CBC1991D8F4EAA878AA3A033A2,
	AudioClip_Create_m151B4E9C35D1B185FF1605DFC93D91DFAE11DB13,
	AudioClip_Create_m845EAF7C4DB9F316162F8441D225587CD043B9BF,
	AudioClip_Create_mE8111F06981E42666B6A9A59D0A3EBE002D2CDFB,
	AudioClip_Create_m48B1434AA494303489CF28D8794B6CA110B51CD2,
	AudioClip_Create_m988FEB04BC74440E65C3CF07414E4867AAE737F8,
	AudioClip_add_m_PCMReaderCallback_mA226EA143D90E04117A740FC9FA9F1111346CA83,
	AudioClip_remove_m_PCMReaderCallback_m3258A455005F4A94570B4F8FE28A5EDA91A88412,
	AudioClip_add_m_PCMSetPositionCallback_mB280AD93A847C65F536D846FECC7DCBE9266C37F,
	AudioClip_remove_m_PCMSetPositionCallback_m39598139640580138742F129E0510917DF2E233C,
	AudioClip_InvokePCMReaderCallback_Internal_m766E5705AB5AE16F5F142867CC3758ABE4BF462C,
	AudioClip_InvokePCMSetPositionCallback_Internal_m986EF703B7DDE42343730DE93A095D05B9F4DBB8,
	PCMReaderCallback__ctor_mF621B6CC1A4BA6525190C5037401CF2FD5C0CF28,
	PCMReaderCallback_Invoke_m76784C690C36B513E2AA5B0E4FD9831B2C7E5152,
	PCMReaderCallback_BeginInvoke_mD10A18EB98467EAFC6F47FDED227A91EE705E064,
	PCMReaderCallback_EndInvoke_mE610CCFC5ACBC50C6A644FABA042325E007CB28D,
	PCMSetPositionCallback__ctor_mD16F77DDB552EB69BB3F5EF39420B2F09F95455B,
	PCMSetPositionCallback_Invoke_m434D4F02FA25F91DF6199EC5A799C551C7F93702,
	PCMSetPositionCallback_BeginInvoke_m62A4BA4AA6BB78DF6B52292981FE526F4ECBE2A0,
	PCMSetPositionCallback_EndInvoke_mEE244D859EEBB9B1710C61A7989E6D89AD06244A,
	AudioBehaviour__ctor_m6D88837496C42A746A51383F3D6F29CA72A9D309,
	AudioListener_GetOutputDataHelper_mB30445231F5805870AA625983442CE881D7B66FB,
	AudioListener_GetSpectrumDataHelper_m2631C609AB0CC9ED84EB55C939EDC78456E0482F,
	AudioListener_get_volume_m8EAB8FBA127A53E689C1D8C1857781070381974A,
	AudioListener_set_volume_m72BAF2D558A5449091A59630EBF48095DEB4C721,
	AudioListener_get_pause_mD5DE01AAFDE5CB1F747762091F18FF95963FF473,
	AudioListener_set_pause_m4D52C9FFC6B10B0F281329FA0FB3CE2C64894F33,
	AudioListener_get_velocityUpdateMode_m869BB586C70B4EE05527CA13184A243FE9909A74,
	AudioListener_set_velocityUpdateMode_m9A4C2E6F9F814DEAE18D1FDF91A9E5D37DDDEC06,
	AudioListener_GetOutputData_mC424B552A74589C2FA12A0EB9DCAEA1261DCB39F,
	AudioListener_GetOutputData_m296DA3768E887CCD587D5BDD55A3D9AB1ADECA9E,
	AudioListener_GetSpectrumData_m63364D81841D7FC8EAB015C441E92394236A646B,
	AudioListener_GetSpectrumData_m66A3A04DD3DF8A2CBE8DE16ED2CBD9AA42EBFABC,
	AudioListener__ctor_m428A6CC2CFA95A7D6065D33098191569A7412EE4,
	AudioSource_GetPitch_m80F6D2BAF966F669253E9231AFCFFC303779913D,
	AudioSource_SetPitch_mE75DEDF8F37301BDA63E0F545A7A00850C24F53E,
	AudioSource_PlayHelper_m4DE8C48925C3548BED306DAB9F87939F24A46960,
	AudioSource_Play_m10DB5ACD1CC32EE433DBC10416B1450A30DE5F16,
	AudioSource_PlayOneShotHelper_mD110EAF42353687BD0B1190EEF30F0C65A4CF265,
	AudioSource_Stop_m8A4872F0A2680798CD28894DD28609445C4783F5,
	AudioSource_SetCustomCurveHelper_m3921C8867C4075133FEF2629601FF44400BA86E9,
	AudioSource_GetCustomCurveHelper_mC0B206F9AE3FA7A900C64194461691451F71B2AD,
	AudioSource_GetOutputDataHelper_m209E9A63B5FEDFAA87E99B95E6D4D287AADC0444,
	AudioSource_GetSpectrumDataHelper_m64E105A054751BD5E7477C7E309992EC0BF274EB,
	AudioSource_get_volume_m9CCF33BC636562EA282FDE07463B547D70134EE3,
	AudioSource_set_volume_mD902BBDBBDE0E3C148609BF3C05096148E90F2C0,
	AudioSource_get_pitch_mB1B0B8A52400B5C798BF1E644FE1C2FFA20A9863,
	AudioSource_set_pitch_mD14631FC99BF38AAFB356D9C45546BC16CF9E811,
	AudioSource_get_time_m130D08644F36736115FE082DAA2ED5E2C9D97A93,
	AudioSource_set_time_m6670372FD9C494978B7B3E01B7F4D220616F6204,
	AudioSource_get_timeSamples_mF230FF8ABBD5A5250CBC487D0E0FCE286BA95B82,
	AudioSource_set_timeSamples_mAC3793C13390C591E4995A88A2CE90E26BBDA6BE,
	AudioSource_get_clip_m4F5027066F9FC44B44192713142B0C277BB418FE,
	AudioSource_set_clip_mFF441895E274286C88D9C75ED5CA1B1B39528D70,
	AudioSource_get_outputAudioMixerGroup_mE141F3A6337D84F9BD43196A28CC85D092695CAB,
	AudioSource_set_outputAudioMixerGroup_m10D0A0EAE270424CD2F3BB960CFAA158D9FC24CF,
	AudioSource_Play_m95DF07111C61D0E0F00257A00384D31531D590C3,
	AudioSource_Play_mC9D19FA54347ED102AD9913E3E7528BE969199FB,
	AudioSource_PlayDelayed_m6A4992F1A010DC12906C6002B22F19082967770E,
	AudioSource_PlayScheduled_m9F3C7245A13A1D4BC64AFA9A08763357133727D9,
	AudioSource_PlayOneShot_m098BCAE084AABB128BB19ED805D2D985E7B75112,
	AudioSource_PlayOneShot_mF6FE95C58996B38EF6E7F7482F95F5E15E0AB30B,
	AudioSource_SetScheduledStartTime_m831CB1AC7E3C70BEFB84892B0A50BA161CE1EDDD,
	AudioSource_SetScheduledEndTime_mC9BF39919029A6C6CB8981B09A792D45A60A3730,
	AudioSource_Stop_m318F17F17A147C77FF6E0A5A7A6BE057DB90F537,
	AudioSource_Pause_m2C2A09359E8AA924FEADECC1AFEA519B3C915B26,
	AudioSource_UnPause_mC4A6A1E71439A3ADB4664B62DABDF4D79D3B21B9,
	AudioSource_get_isPlaying_mC203303F2F7146B2C056CB47B9391463FDF408FC,
	AudioSource_get_isVirtual_m7AD36D47A866FF2047CF73D6A2E8BD72C9C03759,
	AudioSource_PlayClipAtPoint_mA78328A70D3F1088B588EF6F811AAD6577F2B7BF,
	AudioSource_PlayClipAtPoint_mF9D129487C356127ADA3AB5C0A67C7D00F26E3DD,
	AudioSource_get_loop_m2D83BF58E1BD1BEE4CC80413C12E761D3310FC2C,
	AudioSource_set_loop_m834A590939D8456008C0F897FD80B0ECFFB7FE56,
	AudioSource_get_ignoreListenerVolume_mC58B59373161017F770D42A36C536511805AE87C,
	AudioSource_set_ignoreListenerVolume_mAB973FFB2B666C4C6DE3BF34C930C28CC315731D,
	AudioSource_get_playOnAwake_mB07DE7C6BE0F5E6229FA160DA65BE8B8978BF9D1,
	AudioSource_set_playOnAwake_m7EACC6ECEF12D7BA86A4E5A53603F1C8F9E11949,
	AudioSource_get_ignoreListenerPause_m544337985D4025632846D4AB4EC1ADD0CF0B4B01,
	AudioSource_set_ignoreListenerPause_m1BC14FA0984DEDF62E1CDBAB323950100A0BF2B4,
	AudioSource_get_velocityUpdateMode_mEFF48403F8A591A14927408F806E0603391E153B,
	AudioSource_set_velocityUpdateMode_m379F5704F12211BFB9AF3E3DE6647A6B057C7426,
	AudioSource_get_panStereo_mEB4CE5FF235A46C8B7CE62529A9DDA75A15C2505,
	AudioSource_set_panStereo_mE3BA673B5F93F731114E8901355A63F07C8A54DF,
	AudioSource_get_spatialBlend_m06E7948B2813AA3EAE031BD4D1DE61A29416B1CE,
	AudioSource_set_spatialBlend_mCEE7A3E87A8C146E048B2CA3413FDC7BDB7BE001,
	AudioSource_get_spatialize_m5CA89537077D4BB8DBAABFD8EB36D0B89BA8DACB,
	AudioSource_set_spatialize_mDFA357EDCB0C59EF11F53C845F7ACBF6BF7F7B3C,
	AudioSource_get_spatializePostEffects_m02D6863671C49B81DFACDA623C74188B1FD950A4,
	AudioSource_set_spatializePostEffects_m7CC219B7790E27667D49F4A36C8F62FFF399DA54,
	AudioSource_SetCustomCurve_m6597C180AE2DD79DA663ABD76FC26AC816CB7CFB,
	AudioSource_GetCustomCurve_m39ADDCACC6F9E55D4059E45A13092FFA7C39B23D,
	AudioSource_get_reverbZoneMix_mA1BE21696195BADD380311B236AA46314911B859,
	AudioSource_set_reverbZoneMix_m0AD755F3841952B06F87767F97CA93E2C9545D1E,
	AudioSource_get_bypassEffects_m0172FACE00674F743A70870EB138B3223D42A35E,
	AudioSource_set_bypassEffects_m56E81C34448803D4B63105071D96AC644CFFEA9A,
	AudioSource_get_bypassListenerEffects_m47CE7EC60DB5D13E4D818CFA6D5E1B9D6134EF02,
	AudioSource_set_bypassListenerEffects_m321403F18B6174D2E91D080DBF5090C29BC11899,
	AudioSource_get_bypassReverbZones_mA640A5F9FF8E52777CF13950D966839729D1B3DF,
	AudioSource_set_bypassReverbZones_m900FD2BA30F36243B5A5B872B0D019CBAB6AC410,
	AudioSource_get_dopplerLevel_m7BF6F31D1E8927E059BC87933AD9B81D63AF97BE,
	AudioSource_set_dopplerLevel_mB9AC5164E5AF16ACECA3B8E29F5C8573C37E40D6,
	AudioSource_get_spread_mC21DF6C651AD67BEB5D721F0EA0B2F3B080F4C77,
	AudioSource_set_spread_mDFBC1BF11837C26EF9763A8DEEFC56AF95F6E83F,
	AudioSource_get_priority_mD4B6D16F6BCB1D5ACA3F2CC096EDA8861DA66881,
	AudioSource_set_priority_mD1AB7ED858D8A1233642F5DBA81AEFBE35DD4B40,
	AudioSource_get_mute_mE23745FC15F1105556CB7590CA651628FC562DBD,
	AudioSource_set_mute_m6407E0AEE7F088AC69BD8C1D270C2B2049769B09,
	AudioSource_get_minDistance_m459BE399BBBEA04CBBCF50CFB15A09CB3D7431F0,
	AudioSource_set_minDistance_m6CBE3A60C03C0F179192FBDD62095B2E9D717690,
	AudioSource_get_maxDistance_m8C31CB391B999C8D344EFF0AFB8E20488F7A5F7E,
	AudioSource_set_maxDistance_m4BF310D54761500A77A6C4841A0BBDBD09225813,
	AudioSource_get_rolloffMode_m1D5F4CCF83174583ACF0C365051E58978ED02CFD,
	AudioSource_set_rolloffMode_m441D9552D8648D6040E66EE2C2650A79DC5E6FB4,
	AudioSource_GetOutputData_m7482BBFA8AF652D3D4F13E4D9C9A8DC39A736D68,
	AudioSource_GetOutputData_m8AEF8365E3B162E379E1D5FA6C1607999DE458F3,
	AudioSource_GetSpectrumData_m8179FFE6FA60C9AE1E6F51B6FAB48C49D81F44FC,
	AudioSource_GetSpectrumData_m0F3872A4C6B41EFD5A23BA24322B08367BFF0CFE,
	AudioSource_get_minVolume_m8EBDCA4B2BBD49066F1B981771BCCDD75C197458,
	AudioSource_set_minVolume_mA84614CAD182A1E6DF7BF32207618EB882910D33,
	AudioSource_get_maxVolume_m4C9A8BEAF838B8BC2B938DAC360D84E44DD8DDA0,
	AudioSource_set_maxVolume_mBBF6CA7789D92FC72C394C42F63812FCDF232018,
	AudioSource_get_rolloffFactor_m8E37942D2DB42B017B43A3A261783B7CF9ECEEA9,
	AudioSource_set_rolloffFactor_mB8ABAD5E639E429028B910A36CE0F16A4041AFDF,
	AudioSource_SetSpatializerFloat_m124ADF8D1FB75E1677A8891D9BF7138FD8398ADB,
	AudioSource_GetSpatializerFloat_mEC4F0121F73E3D68A99B527036C431C191554F59,
	AudioSource_GetAmbisonicDecoderFloat_m2223F72DBFC57C4E9EEE3465325702ADB728D059,
	AudioSource_SetAmbisonicDecoderFloat_mDA21CA3D9B098FB3704B5450D5D90319E9CC9BA8,
	AudioSource__ctor_mC67BD65374AC3CDFB702307F4A89932D803191C1,
	AudioReverbZone_get_minDistance_m604DC4E7952CCD3F231FC918873D3167AD536201,
	AudioReverbZone_set_minDistance_m0C9EE04712E8925D4451B08028B09A127F003893,
	AudioReverbZone_get_maxDistance_mD167A784DD3B68E0BD87BA4F427571F38FCC49BF,
	AudioReverbZone_set_maxDistance_m76A445B58F9F590441660048A23CF87B5136178F,
	AudioReverbZone_get_reverbPreset_m64434E787D94B9B94E4B71E9B1F367B89DA10DE0,
	AudioReverbZone_set_reverbPreset_m06D572EC66CD0366B231EEA444C510039E932508,
	AudioReverbZone_get_room_m177AE0751D1D4E24F0872FF14628635CFCA25638,
	AudioReverbZone_set_room_m4A6D908E887AFF48B17A4D714D90405A77DCDA23,
	AudioReverbZone_get_roomHF_m366BB86E42585AE62C2DF751981B81E8A507FA8B,
	AudioReverbZone_set_roomHF_m5719F85D38ACCA7E2EF3D2FE0E51118B18FD7730,
	AudioReverbZone_get_roomLF_mC0D33BDFF54FE6E613C2BF4FCEB535F14B3B642E,
	AudioReverbZone_set_roomLF_m7CBC3E5E5F63314FF913864B6E97EC33855FD149,
	AudioReverbZone_get_decayTime_mFE6375F34E3EF5CF17421EA6CDAC6A04607D0019,
	AudioReverbZone_set_decayTime_mEC8A1C062306166C8DF04BE7E3B9EE9DBCA61EAE,
	AudioReverbZone_get_decayHFRatio_mB838649CAF924734973B5D32F021AC038E466B3F,
	AudioReverbZone_set_decayHFRatio_m11530DE7EEB8C8040285FCE7E7BA945B0783036D,
	AudioReverbZone_get_reflections_mA327CC41BCAE9737DAAF9ECDB6953041F6F27A95,
	AudioReverbZone_set_reflections_m8D1A6EF3D58D3B0B3F3A8627AE1C49A492377D43,
	AudioReverbZone_get_reflectionsDelay_m25AD8A786CB391F409489591A7A10454C5D7AB13,
	AudioReverbZone_set_reflectionsDelay_m888146187B34E8ECD770C5E0A5C59D886FF6CFF6,
	AudioReverbZone_get_reverb_m7237DD1B544F18802FC34B8717A337608B40900B,
	AudioReverbZone_set_reverb_mB3FA34C7B13C292E99FE5DE31DDF767954F9C3A3,
	AudioReverbZone_get_reverbDelay_m8FE5034FC1BD0F18D3C032B5FA4EAB6B00EF50F4,
	AudioReverbZone_set_reverbDelay_m19CB45AD5F697D28AD05D5917A7157BD92816F21,
	AudioReverbZone_get_HFReference_m8449C3C5D8436EA4832CD86317C0DD66F7875B8B,
	AudioReverbZone_set_HFReference_m331EC64325F258615093855990B8B3181099B517,
	AudioReverbZone_get_LFReference_mA7BDD93A2FB05546DBB00E29696D905B5F89829D,
	AudioReverbZone_set_LFReference_mD790C9855FC151F399990DED609153983EAE4D38,
	AudioReverbZone_get_roomRolloffFactor_m32F6F8931E314AB0F3DA617E91D72A0C8E345C22,
	AudioReverbZone_set_roomRolloffFactor_mDF41A2C59C38763A6D0AFBE1268BDF976FA7A65E,
	AudioReverbZone_get_diffusion_mD41B0A5AB6A954AF9453FFD4CA56DE1E99112F81,
	AudioReverbZone_set_diffusion_mEBE6DD85D505213B5C017ED539EAAD8897043BAC,
	AudioReverbZone_get_density_m2735C507661A18EDA8DF843A610AC425A8AA05EF,
	AudioReverbZone_set_density_mD0AB2B4E9E31D3C30446F5D927E7A4D1B3E6661A,
	AudioReverbZone__ctor_m6125F0EA7D0C37A533B744C29A9611A688045A77,
	AudioLowPassFilter_GetCustomLowpassLevelCurveCopy_m14BAC51BB10B6F00B3BAC19CE7FEC5231F99E15B,
	AudioLowPassFilter_SetCustomLowpassLevelCurveHelper_m47A4935A70317BB52F179FC0BFC6BEDEEA953CAC,
	AudioLowPassFilter_get_customCutoffCurve_mE5E13EB0D3D7414FBAE5DDDA31821027AA7FD116,
	AudioLowPassFilter_set_customCutoffCurve_m3DD993213650BB06F924B9ECC8B77AEE7FFE9098,
	AudioLowPassFilter_get_cutoffFrequency_m81B07E7C2CE10E23F89954A3445AE3E79FC41F76,
	AudioLowPassFilter_set_cutoffFrequency_m593B7A476225759056C6DACCEBF92016FEE7B050,
	AudioLowPassFilter_get_lowpassResonanceQ_mE4C49AFFCD409A295C6232C1560014D7B661CC95,
	AudioLowPassFilter_set_lowpassResonanceQ_m99921D5254062F0CB3FFE1C608CF703AD1BA0F4B,
	AudioLowPassFilter__ctor_m84825EE53C006C52517EEA9F9F20D80A517F4BCA,
	AudioHighPassFilter_get_cutoffFrequency_m3B2D24CAE52BABA37E36DDBFFC5710F8346889CD,
	AudioHighPassFilter_set_cutoffFrequency_mBD5636A124C598DC0B54B8338FF834F422DD676C,
	AudioHighPassFilter_get_highpassResonanceQ_m64B258EA328733E39E4D37DAA9E29919CBCF29C1,
	AudioHighPassFilter_set_highpassResonanceQ_m904290EB5077604E41690C97AE1160AB78FE24B6,
	AudioHighPassFilter__ctor_mDAB8BDE4375EB6D37AEF7F10636038195818AF1B,
	AudioDistortionFilter_get_distortionLevel_mA6CAE95E51279B223842C5460DDF558AC2B9E5BF,
	AudioDistortionFilter_set_distortionLevel_m1C3A97CA5778481402593F35B9DF69C1ABE7FF3F,
	AudioDistortionFilter__ctor_m36416ED8C4E280AB866F56E30EEFA6D6F3103635,
	AudioEchoFilter_get_delay_mCB330FE5F6FA47E2F150055D5B5DE7CF823B60C3,
	AudioEchoFilter_set_delay_m397356ADDF75BA6494C0BB6D7010CE491EB380EA,
	AudioEchoFilter_get_decayRatio_m8FA03CFD848069F564CDAA623CBF8CBA54EA872E,
	AudioEchoFilter_set_decayRatio_m11BF2192C4E06EDD856936E0D911121A9B718B85,
	AudioEchoFilter_get_dryMix_mC739CE6F775496904C2E4B3F9B4FD461BFF9A9C4,
	AudioEchoFilter_set_dryMix_mD6D153883670F3E1C64ACC3C3E9B546A90786B58,
	AudioEchoFilter_get_wetMix_mDD2098A367D66E20E7467D9C70775BDF9649FC79,
	AudioEchoFilter_set_wetMix_mB73E5B2A5C73C6FC72E90EA1850B9497E4D09370,
	AudioEchoFilter__ctor_m5100792BD25686BF7A44FED02F04D974D6DA0A4E,
	AudioChorusFilter_get_dryMix_m1A9342CF6C25C7A1F39036EF22053D7971C1548A,
	AudioChorusFilter_set_dryMix_mB1A570CFCA83E7774B818D4CC793EE8826036F57,
	AudioChorusFilter_get_wetMix1_m8745993A0B1ACDC2D7B573AFE2D518A9F4D9C4DF,
	AudioChorusFilter_set_wetMix1_m29F12D2DAA2B9331BB403E2CCD51622A28CA9AB5,
	AudioChorusFilter_get_wetMix2_mDA24F10D5F578A8A9B298D20EA96859540E84280,
	AudioChorusFilter_set_wetMix2_m0FAF980C47D4AEE1D8E77FD18E87B4FBB6AF5FF4,
	AudioChorusFilter_get_wetMix3_m34E39E60D6C3B6B1FB75DB16E6E0E0CAECFBC45D,
	AudioChorusFilter_set_wetMix3_mDB728423C888D810F3952E0EE1B539965816D5CA,
	AudioChorusFilter_get_delay_m672795F6AA8E8B25C82E7AD89F700A47EF006D13,
	AudioChorusFilter_set_delay_m55A6C17C5720CD54E2635377DF9F2AA909BE6AB9,
	AudioChorusFilter_get_rate_mE7CC401F69E7E5576B31B1D9BE22B04567DCF014,
	AudioChorusFilter_set_rate_m5F33CF51BDE70FE8A955CAB1E8C4FB7C18E0E5D8,
	AudioChorusFilter_get_depth_m7759E9F1054657F72E4DFEFB013BD79A230702BD,
	AudioChorusFilter_set_depth_m9CF37951B349E28FED863E207CFA2E5E451BDE2B,
	AudioChorusFilter_get_feedback_m5227967B0E1E516101D09CA21E671FD43E8EDADE,
	AudioChorusFilter_set_feedback_mFDEC32D90161D51FE3FDA1A8D42597F11DC16DAA,
	AudioChorusFilter__ctor_mEF2A7D547F029200A840E0457C90369E1BD7CDBA,
	AudioReverbFilter_get_reverbPreset_m0DD8D4359DB574E72F9A80FFB7BDCE4E15A1044B,
	AudioReverbFilter_set_reverbPreset_mF0D458E66A09909A3363CB82045D324A15D0A35C,
	AudioReverbFilter_get_dryLevel_mD08FE87B5DC628565F293CE1B56A9A1307AE0C09,
	AudioReverbFilter_set_dryLevel_m38274A3E21082394406A720CAA563CC837C7E38C,
	AudioReverbFilter_get_room_m29E27BCA006A28DC57C48238713F5C7BB4150FBB,
	AudioReverbFilter_set_room_mEBB4B620CFC0CF674850C555EA9D387F01EE7B66,
	AudioReverbFilter_get_roomHF_m3BCD46183B80171D31662CA8C14FD83EC79FEFA7,
	AudioReverbFilter_set_roomHF_mE2105449BD5C099291B33096F7B07B2675F3F2FE,
	AudioReverbFilter_get_roomRolloffFactor_m87397401ACDCEE355A406B6615C3CAD2ED2A755E,
	AudioReverbFilter_set_roomRolloffFactor_mEA2B79BA611BF72D7C7877EC4C07ED25C499A3BF,
	AudioReverbFilter_get_decayTime_m9997163F354BA2E0AC8AEFAF0A176A7F29071C5A,
	AudioReverbFilter_set_decayTime_m5A0A13CCEB435EC02065BB2BBD52515C813E3269,
	AudioReverbFilter_get_decayHFRatio_m0A583A0F2C049AC770DA5DCD96444C42505715CA,
	AudioReverbFilter_set_decayHFRatio_mAA637FF73CAB073B4B72333E49B8EF87CB835B96,
	AudioReverbFilter_get_reflectionsLevel_mEF98034B0B4519F11928C18392244FF9E4D9CEEE,
	AudioReverbFilter_set_reflectionsLevel_m8155042594B3010E232EC28BF11A77B8A21DDF5C,
	AudioReverbFilter_get_reflectionsDelay_mD749A1A26AD423F1E1BE315FE63AFB8EF6EBC128,
	AudioReverbFilter_set_reflectionsDelay_m36D46F143CFB54001467708C8E99074EE087431F,
	AudioReverbFilter_get_reverbLevel_m5C1CFAC4F1C69B6F58EE6C99BDF9B58EF9D56D9F,
	AudioReverbFilter_set_reverbLevel_m1772AB9B29E73CE217FB1311C584A5E905D8678A,
	AudioReverbFilter_get_reverbDelay_m57423C26BFCAE1C785D53E86D49CF5D53C220F8B,
	AudioReverbFilter_set_reverbDelay_m39032CE9C3873B99EED57ADDC0CAABF1DC476589,
	AudioReverbFilter_get_diffusion_m9A4C2092D061AAC950EA9C366D56ADDE350CB261,
	AudioReverbFilter_set_diffusion_mE2CA22104B7E38D40DE2F885941C049719117C94,
	AudioReverbFilter_get_density_mAD87B83256988D3C56E012513FC8328599746637,
	AudioReverbFilter_set_density_mF31C5ABD0AD7531217B5641D54246D5F0FDE1531,
	AudioReverbFilter_get_hfReference_m2C7256088BAB07BA98BE0FFD6D9E7997072F2A8A,
	AudioReverbFilter_set_hfReference_m4DBCBB0F944EEFD8B617412A540E8D7C926603BC,
	AudioReverbFilter_get_roomLF_mF155DBD3B5A136D06DE3BF6200FDFFE1941F014A,
	AudioReverbFilter_set_roomLF_m8EF46CFC0599913676A9E88F76BD25E88911D03D,
	AudioReverbFilter_get_lfReference_mFA5EAB7CD86E9D96CC26DA9968594D3B11A49104,
	AudioReverbFilter_set_lfReference_mA2FB3E1E3EEA40142C72F2CC071DC9ED12C11CA9,
	AudioReverbFilter__ctor_m20060D1ED1FAFD97683C98E7ABAE796DA2B3B368,
	Microphone_GetMicrophoneDeviceIDFromName_mD33349A5B41E037F04802638690FBA891035C238,
	Microphone_StartRecord_m561E1A2B878937E556D6FCABC3FE735CB818D897,
	Microphone_EndRecord_m6F4983F3A002DA6F07F979D42D0750A1C3D16156,
	Microphone_IsRecording_m59B6BAF774312891C815FCC4D0304256FDC93CB0,
	Microphone_GetRecordPosition_m73C6D07638BD2BB56C6FA91FF19AAE591A2782C6,
	Microphone_GetDeviceCaps_mEE44F844E84A87EE9B8CB7F241DB365309CC80AC,
	Microphone_Start_mDA38C5376D122F27D9DEFD2AE811BAE460F2242E,
	Microphone_End_mB368877FCC9EA1522914006671E637848A0F7CC6,
	Microphone_get_devices_mC2821E200C36C599DDC37927DEC9EA725240812D,
	Microphone_get_isAnyDeviceRecording_mA23DADE28B942271902CACC6FC92B60BA83B5858,
	Microphone_IsRecording_m93CA54969E12BF2083326E43794D71F0FED5D653,
	Microphone_GetPosition_m13F4C8EBE8536893D9AD8388B0E5B46D62E6A459,
	Microphone_GetDeviceCaps_m8C443A4C8FDA86E23E2C5556C4E3AAA6FD181454,
	Microphone__ctor_m4EA008BA12EFE1DF177C87B132A41AEFEDEBC067,
	AudioRenderer_Start_mC32C9B9C9AAFF501CD62A2B30B270F7BF08E6429,
	AudioRenderer_Stop_m08B0E63CC996CB8330B497E47A2161D59A400DDF,
	AudioRenderer_GetSampleCountForCaptureFrame_m145C6DDF7931564988F0F17210335B1588D84113,
	AudioRenderer_AddMixerGroupSink_mE47E9079DB70B240748AC55DB3B4B78705FA7D7B,
	AudioRenderer_Render_m4AA1F575224F29628BEAF1A651C537BC6120BF35,
	AudioRenderer_Internal_AudioRenderer_Start_mCE86EBF2CD0C4BF634EA665D77380A69B73300E0,
	AudioRenderer_Internal_AudioRenderer_Stop_mE5058A6788AB5F599D1628FD8B22ABB673CF0DCE,
	AudioRenderer_Internal_AudioRenderer_GetSampleCountForCaptureFrame_m04AFD7EE530D8A61788927AAF565B0A9F088F6D9,
	AudioRenderer_Internal_AudioRenderer_AddMixerGroupSink_m09D2301DB0F152CECFF48436ED175745EE2A49B9,
	AudioRenderer_Internal_AudioRenderer_Render_m17C449F516B4C0FFFC0F1D7A96705EFEBDE0799C,
	AudioRenderer__ctor_m3A3FF24C4440427230DE67F72FBBC64C4F242CF6,
	WebCamDevice_get_name_m2BF75E8EA486668299906EAC9B35214890D4601E,
	WebCamDevice_get_isFrontFacing_mA021D9DBDDB227FEA8AA635E833EAB7718672404,
	WebCamDevice_get_kind_mABE370D6A0530D98A0DF38EAD2FF84129B909B59,
	WebCamDevice_get_depthCameraName_mBAC7D1A21ADA2365EE56D9CA200B383E8E606772,
	WebCamDevice_get_isAutoFocusPointSupported_m05C63A6BCF582174F4CCC78637C02DAF8029CF32,
	WebCamDevice_get_availableResolutions_mFF88DEDAE444B4FA6ABE86888C451F7B68ED84F1,
	WebCamTexture_get_devices_m57A8D669542CBDDB56B21C8DB62D703B7215EBFA,
	WebCamTexture__ctor_mAF6018FD8752F8527E23C6A800A314C87322D1DD,
	WebCamTexture__ctor_m8E716F7FBF6B0C63D60BEF8BF0C6A7EA819951A4,
	WebCamTexture__ctor_mD235321AAF77352551BBF18CB34AD6293FD62881,
	WebCamTexture__ctor_m75037ECD4778EE55F9987B9E56BD8AF8BC4A28CC,
	WebCamTexture__ctor_mE75D56732766528D49F3975A45C09A9A96208E99,
	WebCamTexture__ctor_mCDCF373E41263EE323147C4A50609EBE9FA28269,
	WebCamTexture_Play_mAB313C6F98D5433C414DA31DD96316BDE8D19A26,
	WebCamTexture_Pause_mF77809A7C7EFF3B0DCEBDC09A3E1B0758F050367,
	WebCamTexture_Stop_m6239B5D1E10C53B57BB30E124E3F541EBD46A184,
	WebCamTexture_get_isPlaying_mE53901F249CD5FFF9D1C31DDCC2FC331DCEA80CF,
	WebCamTexture_get_deviceName_mE590B4CFECD04A12159BC99B5BE682882E5F8C55,
	WebCamTexture_set_deviceName_mE98A9B0F7A93E4C1EF0DD5508E20F64CED31DF86,
	WebCamTexture_get_requestedFPS_m3AA4E91B40A8F881E6530AEAF5103C0B198F3081,
	WebCamTexture_set_requestedFPS_m1B942D1B9D351ECA5ED4D15B8EA6031BB39C3B3E,
	WebCamTexture_get_requestedWidth_m2A8C340F5E4ECFB144C31383997A17DF5CB7FE08,
	WebCamTexture_set_requestedWidth_mF45C8D70FE7C22D84D86AC7CD81270067BF27D67,
	WebCamTexture_get_requestedHeight_m0C02E5BB108C1808503A4B9468205BFBC8A7FDEC,
	WebCamTexture_set_requestedHeight_m2216C94A7C59856727881B36974DD8E09BAB103C,
	WebCamTexture_get_videoRotationAngle_m2BF420A1243F56415BEF82CC84AB4C7B342C991F,
	WebCamTexture_get_videoVerticallyMirrored_mDC7525B796A2629927EF113DA199DDE200B1B52A,
	WebCamTexture_get_didUpdateThisFrame_m3672350773BAA9131D648B886DFD4E3351F045BE,
	WebCamTexture_GetPixel_mFB7F5DCECD37AEE8CD1051292D22CEEC1B06D044,
	WebCamTexture_GetPixels_mA2B07D6D761AA724A6AB1ED04BE0FA195D830FDC,
	WebCamTexture_GetPixels_mD30134473515AEA70C9DE43392F2ADD95747237A,
	WebCamTexture_GetPixels32_mCB0ABCB9D7BCB7ECABF7BF911946DE25C8F26B58,
	WebCamTexture_GetPixels32_m7F4F302BE0E517451593C43E3F0D0D1B7840E39E,
	WebCamTexture_get_autoFocusPoint_mC5C2256C72411BE5EB8FF7D31E1DC177220D0669,
	WebCamTexture_set_autoFocusPoint_mF3E35AAF12B1F3A88F08FA81EAE61A3E58D5BB0A,
	WebCamTexture_get_internalAutoFocusPoint_m481D9B757EE9EC40A5FD8E8FA69CE1A02699A555,
	WebCamTexture_set_internalAutoFocusPoint_m29347F6736F67D3DFA70DC89580C5443553BEF99,
	WebCamTexture_get_isDepth_mDA89B25706EFB6F100AF30C68A905A7544AE364B,
	WebCamTexture_Internal_CreateWebCamTexture_mE8B5E78C03DAD51A213D6D39D5A154919409BDD9,
	WebCamTexture_GetPixel_Injected_mE8B1CFDFB7ED72F53E74D906FAE89DC575039E94,
	WebCamTexture_get_internalAutoFocusPoint_Injected_m9ACB2C19E8240C26CA748D68358BE68D6D256ABE,
	WebCamTexture_set_internalAutoFocusPoint_Injected_m20952B51A9F862174DFF269D23BC8CFC53E709AC,
	AudioClipExtensionsInternal_Internal_CreateAudioClipSampleProvider_m6203311E8D9D969D6181DA9282898988A6FC7BB1,
	AudioSampleProvider_Lookup_mAC9B381D61BBC6504C62B6956FD0CE7BEA989BF7,
	AudioSampleProvider_Create_m8C52CB23AEF97E27E69F57440FA3D92650866B03,
	AudioSampleProvider__ctor_m83670597E1EBE98AC6468172D464C9129CFCE375,
	AudioSampleProvider_Finalize_m21E41F1E30B94174C66C6BF4EE8F415BE773E9DB,
	AudioSampleProvider_Dispose_mD4D855AD77CF61F9BDF38BE79564E67BA4627E2A,
	AudioSampleProvider_get_id_m54B9861281DA7D0B1BE31B01B4319986E649BAED,
	AudioSampleProvider_set_id_mA392A551DD19269C1C8C0134935E6CDAA51831A6,
	AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6,
	AudioSampleProvider_set_trackIndex_mC2EE9E4029DF0362F4EAC7FE4DA24C3BFC9AEBAB,
	AudioSampleProvider_get_owner_mC8461FD790AEC7BACE00C8A3F1DE4D00E08EF1AC,
	AudioSampleProvider_set_owner_mD620FF3D0BC2188F48D2741102385358F0B8E71B,
	AudioSampleProvider_get_valid_m777CC6D57DE2B2DB45C05A89566087C010569C4E,
	AudioSampleProvider_get_channelCount_m2E8B29584D96B878521E64400D6673C59E4A10B7,
	AudioSampleProvider_set_channelCount_m32FE390F1DBCEF6483FA2B6DD80A1F7542B29DE5,
	AudioSampleProvider_get_sampleRate_m75960A76B48C4686467E6C82C3A00F84B029BA57,
	AudioSampleProvider_set_sampleRate_m3F8E715867B01BF4238E9A6D0B5705BFB6CDEFC7,
	AudioSampleProvider_get_maxSampleFrameCount_m95D4731152161EB84944D54DC7CA3E36912CA53D,
	AudioSampleProvider_get_availableSampleFrameCount_m535089AFF861FEE5316943F2C521C3417F6F8595,
	AudioSampleProvider_get_freeSampleFrameCount_mD19F0C0EC75728D3A7F25D8622B2C760D19EAD68,
	AudioSampleProvider_get_freeSampleFrameCountLowThreshold_m28080901615175FE3025DA6949C9791E991798D6,
	AudioSampleProvider_set_freeSampleFrameCountLowThreshold_m3C7E8B327BA4894ECB1A010674E655E23FD3BDC1,
	AudioSampleProvider_get_enableSampleFramesAvailableEvents_mD1E0CAE084D82F2C8E565C1A7B9DD0AB366C1DB1,
	AudioSampleProvider_set_enableSampleFramesAvailableEvents_m80282D7581B9957520C418B3CC06AAA43F5F2237,
	AudioSampleProvider_get_enableSilencePadding_m6A6C0E9625800FFFF6DE94A42214381E39556FC8,
	AudioSampleProvider_set_enableSilencePadding_mBD5BF4A0B01955991D54CE271B90BDEF405C2C50,
	AudioSampleProvider_ConsumeSampleFrames_m7A8AC28A6C57F7FCEE30B82BFE4A3EC364A93A39,
	AudioSampleProvider_get_consumeSampleFramesNativeFunction_mA808F07ED91B5E6191BF7CEAF2395BE5643C0EF6,
	AudioSampleProvider_QueueSampleFrames_mBB5342E3C7839C39693046489E486D15154ADB96,
	AudioSampleProvider_add_sampleFramesAvailable_mF7D95C615AAD8767724B3001D3B82E0071083F78,
	AudioSampleProvider_remove_sampleFramesAvailable_mCE2779B438F1696659C06596B6DA6884F617FB7E,
	AudioSampleProvider_add_sampleFramesOverflow_m31D18E632926686F1A6190D5DF455714D82DBF63,
	AudioSampleProvider_remove_sampleFramesOverflow_m833A07BC3B28DACE9DF51C76085BD0FE1443DB7D,
	AudioSampleProvider_SetSampleFramesAvailableNativeHandler_mC18EFEC722D5014A0A84E09E6AF57A84F8DF24B0,
	AudioSampleProvider_ClearSampleFramesAvailableNativeHandler_mB9DABA77DB0B942BBC688A9F0F2F2577264A9A28,
	AudioSampleProvider_SetSampleFramesOverflowNativeHandler_m43D40634FCD09DED46C3DAC7CB07319A9E4646EC,
	AudioSampleProvider_ClearSampleFramesOverflowNativeHandler_mB26B477B3C19521F08CD3E730408C653C031E19C,
	AudioSampleProvider_InvokeSampleFramesAvailable_mEB16F7230AB65A3576BF053AC5719F8E134FBCD4,
	AudioSampleProvider_InvokeSampleFramesOverflow_m66593173A527981F5EB2A5EF77B0C9119DAB5E15,
	AudioSampleProvider_InternalCreateSampleProvider_m3BD26F837CD7D2ED547A0AE02AC079A72BEE998D,
	AudioSampleProvider_InternalRemove_mBC651EF68D78CFB4310E3022002070ED2174BD24,
	AudioSampleProvider_InternalGetFormatInfo_m8725A2417CD3304F009AA19328606EF2434FD703,
	AudioSampleProvider_InternalGetScriptingPtr_mA1A158321BE37492A745702503F4233E234C1D62,
	AudioSampleProvider_InternalSetScriptingPtr_m6FE4C028426BB46CC9648F66930324E2A708DEF8,
	AudioSampleProvider_InternalIsValid_mCBDAABC4B1ED912B8F13D66AA587175BC549A548,
	AudioSampleProvider_InternalGetMaxSampleFrameCount_m33AF5AF071B5354DC657C8521ACD6379094F8A79,
	AudioSampleProvider_InternalGetAvailableSampleFrameCount_mD9A3A290D03ADB2D14625A2F2DC56D6FFD6D1AA0,
	AudioSampleProvider_InternalGetFreeSampleFrameCount_mF71ABBF38D3686A855500D81EBC91F209C91E989,
	AudioSampleProvider_InternalGetFreeSampleFrameCountLowThreshold_m011436ED9F51F1BDB79C7D2C418971EB29ED8F6B,
	AudioSampleProvider_InternalSetFreeSampleFrameCountLowThreshold_mD5788B7F4F5C2069F04AD6D343148ED9916E319B,
	AudioSampleProvider_InternalGetEnableSampleFramesAvailableEvents_m5828616745AD1EA4FC310DDE5001BBE5D3F5E62F,
	AudioSampleProvider_InternalSetEnableSampleFramesAvailableEvents_mB1F3F754FCD1952FC3C6B27EEAC27A961A30C66F,
	AudioSampleProvider_InternalSetSampleFramesAvailableNativeHandler_m76AD17D2A2B6F1387747538E681DC72EAC0A56A1,
	AudioSampleProvider_InternalClearSampleFramesAvailableNativeHandler_m28838F1E2D5A6CE0910E1045A3EA27D1ADD311EA,
	AudioSampleProvider_InternalSetSampleFramesOverflowNativeHandler_mDEB472C3FBF948D64213C9D5C4748B819FFB08E3,
	AudioSampleProvider_InternalClearSampleFramesOverflowNativeHandler_m4C2BB3111C516BF7AD0ED0503FE77F15E6DA553B,
	AudioSampleProvider_InternalGetEnableSilencePadding_m8C2198276DCB909C7327096B728C5489F012E4ED,
	AudioSampleProvider_InternalSetEnableSilencePadding_m4B055B4F1E7877BDC67BB611BBA5390193171D3F,
	AudioSampleProvider_InternalGetConsumeSampleFramesNativeFunctionPtr_m9F24D2DADDF9A36E4E81D16DE1F9C40A3A4FFE89,
	AudioSampleProvider_InternalQueueSampleFrames_mDF93286C58F479F4FDBFF783D00B426F85C6624D,
	ConsumeSampleFramesNativeFunction__ctor_m32F2C9615F5256132FBF2EAD179A85B05E5B91B8,
	ConsumeSampleFramesNativeFunction_Invoke_mBE5976A97FA5133DA1EFD35257CB184D71181B99,
	ConsumeSampleFramesNativeFunction_BeginInvoke_mAF469C03FAF85358EF4CB6505BA73B2DDCA131B4,
	ConsumeSampleFramesNativeFunction_EndInvoke_mE732490B182912952B918B474B638CD6B2176282,
	SampleFramesHandler__ctor_m7DDE0BAD439CD80791140C7D42D661B598A7663A,
	SampleFramesHandler_Invoke_m478D5645634B8C734E58B59CF7750797FC54F1BC,
	SampleFramesHandler_BeginInvoke_mC73D6F192B4245376061EF30DCAB68643E4E1F34,
	SampleFramesHandler_EndInvoke_m00D2842B05406BA4B8DC02ED0628CB2E0B16C45E,
	SampleFramesEventNativeFunction__ctor_m9950F1156D2F6C5BB6BD4E1A03305892C1073F6C,
	SampleFramesEventNativeFunction_Invoke_m8BB80F3A2283D82F99F79971A7C5E21D2BE2F3A8,
	SampleFramesEventNativeFunction_BeginInvoke_mE9212A20D510C9888772566B1191DC712862BDA4,
	SampleFramesEventNativeFunction_EndInvoke_m38E42512CE033289075453FFB8DB16AA45B7F649,
	AudioSampleProviderExtensionsInternal_GetSpeed_m334970FF8D1B37C55EA261B8E3D2F5EF37121261,
	AudioSampleProviderExtensionsInternal_InternalGetAudioSampleProviderSpeed_m4B89D873E08293C6DDDADB795D061049EAFEEEA8,
	AudioSourceExtensionsInternal_RegisterSampleProvider_m082540DB541FF637B967212DA366A12F50B05978,
	AudioSourceExtensionsInternal_UnregisterSampleProvider_m2E7741EB7338D32D4E47332CDAB2911DD524F4EB,
	AudioSourceExtensionsInternal_Internal_RegisterSampleProviderWithAudioSource_m44FC79BA5403BE0101D7CA745089AD5A70362528,
	AudioSourceExtensionsInternal_Internal_UnregisterSampleProviderFromAudioSource_mE36C0772279DFEE17E6D9A07510D91C7058247D7,
	AudioManagerTestProxy_ComputeAudibilityConsistency_mF0ED94512DBE88A3F0B2A1769CB0CC908E1A7DBA,
	AudioManagerTestProxy__ctor_mAA83AB2AF70DB86C7B9A945C5706DCF81D72CD7C,
	AudioClipPlayable_Create_m0B42D1553D2752E7E98D10677B2A7DE8CE4DEFD8,
	AudioClipPlayable_CreateHandle_mD7CA217BC6DE899D586272C372456EBB7ED2A825,
	AudioClipPlayable__ctor_m4E686B92CFA7C6A36AA794B1E8C36B1E60605751,
	AudioClipPlayable_GetHandle_mEA1D664328FF9B08E4F7D5EBCD4B51A754D97C44,
	AudioClipPlayable_op_Implicit_m49C31C8D61799E247CA509124B3E6E8F4009D237,
	AudioClipPlayable_op_Explicit_mA9B643768436960809DB09A469A0DF09B716446B,
	AudioClipPlayable_Equals_m9C1C75ACBB74FE06AD02BE4643F6EB39413EFF83,
	AudioClipPlayable_GetClip_m009747EE98DE59A9C7F31A069EEE77EBAC0A5A6F,
	AudioClipPlayable_SetClip_mC1D588D0FB1D42630C2A76F82A1C2BDA61009926,
	AudioClipPlayable_GetLooped_m18E3568715253DAF09D74A3C08CBF25A07B54660,
	AudioClipPlayable_SetLooped_m7B0B25EC1369786D181CB8DF9BF31E5BFF81201B,
	AudioClipPlayable_GetVolume_mEB0C89F492CD12218C579365A1F9B07D96E7AE8B,
	AudioClipPlayable_SetVolume_m3553EC43CBA43CA7802292710A1A284A6DEF8FCC,
	AudioClipPlayable_GetStereoPan_mB766EBDC23718A7FDC8D3510FB9A2A0B2AA38E69,
	AudioClipPlayable_SetStereoPan_mB8403E93B06B2ECCD0D4D6B0D55C151D0CCCC494,
	AudioClipPlayable_GetSpatialBlend_mB3479E606D27347E8AE9470A50FF13F567D1B5DA,
	AudioClipPlayable_SetSpatialBlend_m72F1B86E4E5940E8587C1EA32BB48463A00810BB,
	AudioClipPlayable_IsPlaying_mB24D86E9BA044A3CC82FB12866DC2EB61ADE238F,
	AudioClipPlayable_IsChannelPlaying_mA1C0DFAD142F4D43C5BB4EFE2929708CCE59EDA4,
	AudioClipPlayable_GetStartDelay_m9C96B753369B9CDA1482CFBB76AFBE22DDED5E47,
	AudioClipPlayable_SetStartDelay_mD7F1860C6EAD27123871D6B95B9D2B1A21DA17C3,
	AudioClipPlayable_GetPauseDelay_m402FFC47A9FA96C5015D1268A7B0063308A3724A,
	AudioClipPlayable_GetPauseDelay_mAECC9DDE01CAFD50761079E4FBC1B00E8B9FE48C,
	AudioClipPlayable_Seek_m9444B6B715ECAB55500E1BBD04A1B6BD22980ECD,
	AudioClipPlayable_Seek_m2AB327B535C677793BBADEA634B7AAC9442B1391,
	AudioClipPlayable_GetClipInternal_m0B594FA02A34EEFC4CAB34B778FF10CD538EDF71,
	AudioClipPlayable_SetClipInternal_mACA6B51D98C05266D1D0D05D86B5C675006C2DDD,
	AudioClipPlayable_GetLoopedInternal_m337CC96BD2CFEFCDB78AE704FD99AF8ED73B34FA,
	AudioClipPlayable_SetLoopedInternal_mB8FA25F4121BEA4638075D388C2EA66011236341,
	AudioClipPlayable_GetVolumeInternal_m5ED28FAFC2B3077BAE2CA8F90612C9932C272FCE,
	AudioClipPlayable_SetVolumeInternal_m71EFEE49486AACBE4F9C936F4898448AB071B55F,
	AudioClipPlayable_GetStereoPanInternal_mCC24F11786E25F26876873CCF6CE4B1368608244,
	AudioClipPlayable_SetStereoPanInternal_m206A1B777709E25F42C9EF0BEAF3A84D622D4A90,
	AudioClipPlayable_GetSpatialBlendInternal_m588B81B3B008D87A79B2DA76494BF359B2AA8125,
	AudioClipPlayable_SetSpatialBlendInternal_mBAE4A56ACEE90D4732C5D2C5D2D721C65B3DD55B,
	AudioClipPlayable_GetIsChannelPlayingInternal_mE3D91DB6102F7D166F4D495E22330B98A4B14721,
	AudioClipPlayable_GetStartDelayInternal_m45F9633CF2D28DD344D68231443AEF11CA3B12F6,
	AudioClipPlayable_SetStartDelayInternal_m1A3816547BF61B7448654300EF078D464A5618CD,
	AudioClipPlayable_GetPauseDelayInternal_m0993546EFBB076183302FF7902815057D7A5A399,
	AudioClipPlayable_SetPauseDelayInternal_m803360EBD0BB692C31D31D620AAB9C8D0CD7A9D1,
	AudioClipPlayable_InternalCreateAudioClipPlayable_mBDAA54F35207F6C62F87CAE268732072C7287616,
	AudioClipPlayable_ValidateType_mA9D284B70B2086E4CA09CEC913D449F3195C891E,
	AudioMixer__ctor_m8BB9BFC96DB436EE4CECE0BECECD5DFC7559058D,
	AudioMixer_get_outputAudioMixerGroup_m0B5B993AB7FD678B15276E06B226B06B709C560C,
	AudioMixer_set_outputAudioMixerGroup_m7362B6469DCAFB7D1A65BC20BDCF42AE76F90306,
	AudioMixer_FindSnapshot_m289C3F55A58E9DE4EEE456AEFE444ECA4D3496C5,
	AudioMixer_FindMatchingGroups_m4541BE177FFA0225AF159156ABB5FE3F5F6CF2CF,
	AudioMixer_TransitionToSnapshot_m308E8D50A4573EFDD381ED7DEC23162F8CD0EB5D,
	AudioMixer_TransitionToSnapshotInternal_m9D07330A3EF997CC4BB165829D8D2AD6187A8DD0,
	AudioMixer_TransitionToSnapshots_m87D4E352A2696F6BF945EB1A519888A9B38BF7C6,
	AudioMixer_get_updateMode_mBA8246F84EE1B3F135D4E76B91128DB295CFE5F9,
	AudioMixer_set_updateMode_mA823193DD10F7737E036013BB63CEC2ED65E184F,
	AudioMixer_SetFloat_m4789959013BE79E4F84F446405914908ADC3F335,
	AudioMixer_ClearFloat_mD6FD7AE99760D83DA6ECBCCF9A0F07F10C12E665,
	AudioMixer_GetFloat_mAED8D277AD30D0346292555CBF81D8961117AEC9,
	AudioMixer_GetAbsoluteAudibilityFromGroup_m109E7C4D1ECABCB80EB5F7C1952ACDD77986772D,
	AudioMixerGroup__ctor_m0D3A84EDAC9B01AEC0B07AFB1F5B1807F74B9CB8,
	AudioMixerGroup_get_audioMixer_mFDEDBF17C3B84C6B777D2BF75CF40EECF4C889E4,
	AudioMixerPlayable_Create_m323B71EBE332DAF5B2632BAB657BEA33F5870E71,
	AudioMixerPlayable_CreateHandle_mCA2A6EF1CC490A8E59C8EE2020D3304D66B96852,
	AudioMixerPlayable__ctor_m2CDDE33FCE0B3F7D9EB482D76515FBF771285F47,
	AudioMixerPlayable_GetHandle_m6C182D9794E901D123223BB57738A302BEAB41FD,
	AudioMixerPlayable_op_Implicit_m479542341C4CAEE00B4F7DD0B68E39F8E4388974,
	AudioMixerPlayable_op_Explicit_m5BFAA52FB8DF95288F7FD2DBEB0907687F98CD0B,
	AudioMixerPlayable_Equals_mDFB945EB48199A338BAD00D40FB8EEC34CF64D57,
	AudioMixerPlayable_CreateAudioMixerPlayableInternal_mB91DD585A6A2903F01F49719CA0045C8727B5AA9,
	AudioMixerSnapshot__ctor_m68E824AB2B201928EABE2FFCDDC401EE905D2D06,
	AudioMixerSnapshot_get_audioMixer_mCC13199F7F9EBB2C5510DFA750A501747229CBF5,
	AudioMixerSnapshot_TransitionTo_mABDDC418B89323A930A900E55336B5989CFD4AC8,
	AudioPlayableBinding_Create_m4E506BD0649ADAB9D1C2D924D2767FBB0C63DACB,
	AudioPlayableBinding_CreateAudioOutput_m2BE0EDC74ADA082B5CD6247ACD78EFCBDB85ADC3,
	AudioPlayableGraphExtensions_InternalCreateAudioOutput_m5EF8B18878AFFE0B462E615C8243C2433A3F5E78,
	AudioPlayableOutput_Create_m90DF38B28813932D4246094FD4DB6105572619D2,
	AudioPlayableOutput__ctor_mBEA3D7EE652908558720EDC0D40F7BF3EC50D409,
	AudioPlayableOutput_get_Null_m8404386CE6506C8C0574B74023EB1BC17A45F205,
	AudioPlayableOutput_GetHandle_m55153D572F8FB9BCFF3843402A20280273B934AE,
	AudioPlayableOutput_op_Implicit_mD2D35763126BDE08E10CA74D8E8C49988477F428,
	AudioPlayableOutput_op_Explicit_mC51D8736040715BAA8AC5FA22B6E89F9CDBF25C1,
	AudioPlayableOutput_GetTarget_m2258506791A9E370329AFD2D4FE9FD7CD2D2DB48,
	AudioPlayableOutput_SetTarget_m34EE86E5C2833F12993681ABE5AC85961836DE04,
	AudioPlayableOutput_GetEvaluateOnSeek_m23EF84B9C518CDB97E68214E2A2BD89A1FCD6F6E,
	AudioPlayableOutput_SetEvaluateOnSeek_mB3266A8A68E94933A82A0C431B7A2E7321929D92,
	AudioPlayableOutput_InternalGetTarget_m34CC5798C297222E92D216941F1A98E76BF55F47,
	AudioPlayableOutput_InternalSetTarget_m3A9912A00BC052FCEDEBB5EB75DCBE5B2B9DA86E,
	AudioPlayableOutput_InternalGetEvaluateOnSeek_m13F0BE232D32E1C4F6982CC22A386ACB3741560A,
	AudioPlayableOutput_InternalSetEvaluateOnSeek_m639F527A20B97D2617898E223B5CDC1DC8548804,
	NULL,
};
extern void WebCamDevice_get_name_m2BF75E8EA486668299906EAC9B35214890D4601E_AdjustorThunk (void);
extern void WebCamDevice_get_isFrontFacing_mA021D9DBDDB227FEA8AA635E833EAB7718672404_AdjustorThunk (void);
extern void WebCamDevice_get_kind_mABE370D6A0530D98A0DF38EAD2FF84129B909B59_AdjustorThunk (void);
extern void WebCamDevice_get_depthCameraName_mBAC7D1A21ADA2365EE56D9CA200B383E8E606772_AdjustorThunk (void);
extern void WebCamDevice_get_isAutoFocusPointSupported_m05C63A6BCF582174F4CCC78637C02DAF8029CF32_AdjustorThunk (void);
extern void WebCamDevice_get_availableResolutions_mFF88DEDAE444B4FA6ABE86888C451F7B68ED84F1_AdjustorThunk (void);
extern void AudioClipPlayable__ctor_m4E686B92CFA7C6A36AA794B1E8C36B1E60605751_AdjustorThunk (void);
extern void AudioClipPlayable_GetHandle_mEA1D664328FF9B08E4F7D5EBCD4B51A754D97C44_AdjustorThunk (void);
extern void AudioClipPlayable_Equals_m9C1C75ACBB74FE06AD02BE4643F6EB39413EFF83_AdjustorThunk (void);
extern void AudioClipPlayable_GetClip_m009747EE98DE59A9C7F31A069EEE77EBAC0A5A6F_AdjustorThunk (void);
extern void AudioClipPlayable_SetClip_mC1D588D0FB1D42630C2A76F82A1C2BDA61009926_AdjustorThunk (void);
extern void AudioClipPlayable_GetLooped_m18E3568715253DAF09D74A3C08CBF25A07B54660_AdjustorThunk (void);
extern void AudioClipPlayable_SetLooped_m7B0B25EC1369786D181CB8DF9BF31E5BFF81201B_AdjustorThunk (void);
extern void AudioClipPlayable_GetVolume_mEB0C89F492CD12218C579365A1F9B07D96E7AE8B_AdjustorThunk (void);
extern void AudioClipPlayable_SetVolume_m3553EC43CBA43CA7802292710A1A284A6DEF8FCC_AdjustorThunk (void);
extern void AudioClipPlayable_GetStereoPan_mB766EBDC23718A7FDC8D3510FB9A2A0B2AA38E69_AdjustorThunk (void);
extern void AudioClipPlayable_SetStereoPan_mB8403E93B06B2ECCD0D4D6B0D55C151D0CCCC494_AdjustorThunk (void);
extern void AudioClipPlayable_GetSpatialBlend_mB3479E606D27347E8AE9470A50FF13F567D1B5DA_AdjustorThunk (void);
extern void AudioClipPlayable_SetSpatialBlend_m72F1B86E4E5940E8587C1EA32BB48463A00810BB_AdjustorThunk (void);
extern void AudioClipPlayable_IsPlaying_mB24D86E9BA044A3CC82FB12866DC2EB61ADE238F_AdjustorThunk (void);
extern void AudioClipPlayable_IsChannelPlaying_mA1C0DFAD142F4D43C5BB4EFE2929708CCE59EDA4_AdjustorThunk (void);
extern void AudioClipPlayable_GetStartDelay_m9C96B753369B9CDA1482CFBB76AFBE22DDED5E47_AdjustorThunk (void);
extern void AudioClipPlayable_SetStartDelay_mD7F1860C6EAD27123871D6B95B9D2B1A21DA17C3_AdjustorThunk (void);
extern void AudioClipPlayable_GetPauseDelay_m402FFC47A9FA96C5015D1268A7B0063308A3724A_AdjustorThunk (void);
extern void AudioClipPlayable_GetPauseDelay_mAECC9DDE01CAFD50761079E4FBC1B00E8B9FE48C_AdjustorThunk (void);
extern void AudioClipPlayable_Seek_m9444B6B715ECAB55500E1BBD04A1B6BD22980ECD_AdjustorThunk (void);
extern void AudioClipPlayable_Seek_m2AB327B535C677793BBADEA634B7AAC9442B1391_AdjustorThunk (void);
extern void AudioMixerPlayable__ctor_m2CDDE33FCE0B3F7D9EB482D76515FBF771285F47_AdjustorThunk (void);
extern void AudioMixerPlayable_GetHandle_m6C182D9794E901D123223BB57738A302BEAB41FD_AdjustorThunk (void);
extern void AudioMixerPlayable_Equals_mDFB945EB48199A338BAD00D40FB8EEC34CF64D57_AdjustorThunk (void);
extern void AudioPlayableOutput__ctor_mBEA3D7EE652908558720EDC0D40F7BF3EC50D409_AdjustorThunk (void);
extern void AudioPlayableOutput_GetHandle_m55153D572F8FB9BCFF3843402A20280273B934AE_AdjustorThunk (void);
extern void AudioPlayableOutput_GetTarget_m2258506791A9E370329AFD2D4FE9FD7CD2D2DB48_AdjustorThunk (void);
extern void AudioPlayableOutput_SetTarget_m34EE86E5C2833F12993681ABE5AC85961836DE04_AdjustorThunk (void);
extern void AudioPlayableOutput_GetEvaluateOnSeek_m23EF84B9C518CDB97E68214E2A2BD89A1FCD6F6E_AdjustorThunk (void);
extern void AudioPlayableOutput_SetEvaluateOnSeek_mB3266A8A68E94933A82A0C431B7A2E7321929D92_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[36] = 
{
	{ 0x0600014F, WebCamDevice_get_name_m2BF75E8EA486668299906EAC9B35214890D4601E_AdjustorThunk },
	{ 0x06000150, WebCamDevice_get_isFrontFacing_mA021D9DBDDB227FEA8AA635E833EAB7718672404_AdjustorThunk },
	{ 0x06000151, WebCamDevice_get_kind_mABE370D6A0530D98A0DF38EAD2FF84129B909B59_AdjustorThunk },
	{ 0x06000152, WebCamDevice_get_depthCameraName_mBAC7D1A21ADA2365EE56D9CA200B383E8E606772_AdjustorThunk },
	{ 0x06000153, WebCamDevice_get_isAutoFocusPointSupported_m05C63A6BCF582174F4CCC78637C02DAF8029CF32_AdjustorThunk },
	{ 0x06000154, WebCamDevice_get_availableResolutions_mFF88DEDAE444B4FA6ABE86888C451F7B68ED84F1_AdjustorThunk },
	{ 0x060001CB, AudioClipPlayable__ctor_m4E686B92CFA7C6A36AA794B1E8C36B1E60605751_AdjustorThunk },
	{ 0x060001CC, AudioClipPlayable_GetHandle_mEA1D664328FF9B08E4F7D5EBCD4B51A754D97C44_AdjustorThunk },
	{ 0x060001CF, AudioClipPlayable_Equals_m9C1C75ACBB74FE06AD02BE4643F6EB39413EFF83_AdjustorThunk },
	{ 0x060001D0, AudioClipPlayable_GetClip_m009747EE98DE59A9C7F31A069EEE77EBAC0A5A6F_AdjustorThunk },
	{ 0x060001D1, AudioClipPlayable_SetClip_mC1D588D0FB1D42630C2A76F82A1C2BDA61009926_AdjustorThunk },
	{ 0x060001D2, AudioClipPlayable_GetLooped_m18E3568715253DAF09D74A3C08CBF25A07B54660_AdjustorThunk },
	{ 0x060001D3, AudioClipPlayable_SetLooped_m7B0B25EC1369786D181CB8DF9BF31E5BFF81201B_AdjustorThunk },
	{ 0x060001D4, AudioClipPlayable_GetVolume_mEB0C89F492CD12218C579365A1F9B07D96E7AE8B_AdjustorThunk },
	{ 0x060001D5, AudioClipPlayable_SetVolume_m3553EC43CBA43CA7802292710A1A284A6DEF8FCC_AdjustorThunk },
	{ 0x060001D6, AudioClipPlayable_GetStereoPan_mB766EBDC23718A7FDC8D3510FB9A2A0B2AA38E69_AdjustorThunk },
	{ 0x060001D7, AudioClipPlayable_SetStereoPan_mB8403E93B06B2ECCD0D4D6B0D55C151D0CCCC494_AdjustorThunk },
	{ 0x060001D8, AudioClipPlayable_GetSpatialBlend_mB3479E606D27347E8AE9470A50FF13F567D1B5DA_AdjustorThunk },
	{ 0x060001D9, AudioClipPlayable_SetSpatialBlend_m72F1B86E4E5940E8587C1EA32BB48463A00810BB_AdjustorThunk },
	{ 0x060001DA, AudioClipPlayable_IsPlaying_mB24D86E9BA044A3CC82FB12866DC2EB61ADE238F_AdjustorThunk },
	{ 0x060001DB, AudioClipPlayable_IsChannelPlaying_mA1C0DFAD142F4D43C5BB4EFE2929708CCE59EDA4_AdjustorThunk },
	{ 0x060001DC, AudioClipPlayable_GetStartDelay_m9C96B753369B9CDA1482CFBB76AFBE22DDED5E47_AdjustorThunk },
	{ 0x060001DD, AudioClipPlayable_SetStartDelay_mD7F1860C6EAD27123871D6B95B9D2B1A21DA17C3_AdjustorThunk },
	{ 0x060001DE, AudioClipPlayable_GetPauseDelay_m402FFC47A9FA96C5015D1268A7B0063308A3724A_AdjustorThunk },
	{ 0x060001DF, AudioClipPlayable_GetPauseDelay_mAECC9DDE01CAFD50761079E4FBC1B00E8B9FE48C_AdjustorThunk },
	{ 0x060001E0, AudioClipPlayable_Seek_m9444B6B715ECAB55500E1BBD04A1B6BD22980ECD_AdjustorThunk },
	{ 0x060001E1, AudioClipPlayable_Seek_m2AB327B535C677793BBADEA634B7AAC9442B1391_AdjustorThunk },
	{ 0x06000205, AudioMixerPlayable__ctor_m2CDDE33FCE0B3F7D9EB482D76515FBF771285F47_AdjustorThunk },
	{ 0x06000206, AudioMixerPlayable_GetHandle_m6C182D9794E901D123223BB57738A302BEAB41FD_AdjustorThunk },
	{ 0x06000209, AudioMixerPlayable_Equals_mDFB945EB48199A338BAD00D40FB8EEC34CF64D57_AdjustorThunk },
	{ 0x06000212, AudioPlayableOutput__ctor_mBEA3D7EE652908558720EDC0D40F7BF3EC50D409_AdjustorThunk },
	{ 0x06000214, AudioPlayableOutput_GetHandle_m55153D572F8FB9BCFF3843402A20280273B934AE_AdjustorThunk },
	{ 0x06000217, AudioPlayableOutput_GetTarget_m2258506791A9E370329AFD2D4FE9FD7CD2D2DB48_AdjustorThunk },
	{ 0x06000218, AudioPlayableOutput_SetTarget_m34EE86E5C2833F12993681ABE5AC85961836DE04_AdjustorThunk },
	{ 0x06000219, AudioPlayableOutput_GetEvaluateOnSeek_m23EF84B9C518CDB97E68214E2A2BD89A1FCD6F6E_AdjustorThunk },
	{ 0x0600021A, AudioPlayableOutput_SetEvaluateOnSeek_mB3266A8A68E94933A82A0C431B7A2E7321929D92_AdjustorThunk },
};
static const int32_t s_InvokerIndices[543] = 
{
	9018,
	8202,
	9018,
	9018,
	9018,
	8880,
	9018,
	9009,
	9018,
	8880,
	7894,
	7928,
	9031,
	8985,
	8202,
	8887,
	8887,
	8887,
	8887,
	8887,
	8887,
	8868,
	9089,
	9089,
	8993,
	8868,
	9031,
	9018,
	8880,
	8993,
	8993,
	8993,
	4364,
	8200,
	8867,
	2798,
	3807,
	1763,
	3881,
	8993,
	8868,
	8993,
	8868,
	8993,
	8887,
	8887,
	8868,
	8993,
	9089,
	9089,
	4364,
	5695,
	5695,
	9031,
	4250,
	925,
	4298,
	4216,
	4216,
	4216,
	4168,
	4216,
	4168,
	4168,
	4168,
	4168,
	4168,
	4216,
	2320,
	2320,
	5001,
	4783,
	4650,
	5355,
	5002,
	4784,
	3881,
	3881,
	3881,
	3881,
	3881,
	3852,
	2798,
	3881,
	1801,
	3881,
	2798,
	3852,
	1775,
	3881,
	4364,
	7969,
	6965,
	9064,
	8899,
	8993,
	8868,
	4216,
	3852,
	7599,
	7969,
	6644,
	6965,
	4364,
	8626,
	7983,
	7988,
	3826,
	6988,
	3807,
	6967,
	7626,
	6982,
	6181,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4250,
	3881,
	4250,
	3881,
	4364,
	3974,
	3928,
	3826,
	3881,
	2810,
	3826,
	3826,
	4364,
	4364,
	4364,
	4168,
	4168,
	7991,
	7006,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	2739,
	3515,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	2507,
	2796,
	1769,
	2053,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	2300,
	2293,
	2293,
	2300,
	4364,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4298,
	3928,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
	4250,
	7975,
	4250,
	3881,
	4298,
	3928,
	4298,
	3928,
	4364,
	4298,
	3928,
	4298,
	3928,
	4364,
	4298,
	3928,
	4364,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
	8399,
	5909,
	8880,
	8216,
	8395,
	6896,
	5927,
	8887,
	9031,
	8993,
	8220,
	8399,
	6944,
	4364,
	8993,
	8993,
	9018,
	6347,
	8197,
	8993,
	8993,
	9018,
	5677,
	7144,
	4364,
	4250,
	4168,
	4216,
	4250,
	4168,
	4250,
	9031,
	1411,
	2053,
	3881,
	1973,
	2734,
	4364,
	4364,
	4364,
	4364,
	4168,
	4250,
	3881,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4216,
	4168,
	4168,
	2363,
	4250,
	1169,
	4250,
	3518,
	4087,
	3738,
	4356,
	3978,
	4168,
	5563,
	1967,
	3788,
	3788,
	5051,
	6725,
	7661,
	2159,
	4364,
	4364,
	4350,
	3973,
	4349,
	3972,
	4250,
	3881,
	4168,
	4349,
	3972,
	4350,
	3973,
	4350,
	4350,
	4350,
	4350,
	3973,
	4168,
	3807,
	4168,
	3807,
	3628,
	9031,
	3628,
	3881,
	3881,
	3881,
	3881,
	2798,
	4364,
	2798,
	4364,
	3852,
	3852,
	7817,
	8903,
	7027,
	8521,
	8025,
	8242,
	8791,
	8791,
	8791,
	8791,
	8026,
	8242,
	8024,
	7028,
	8903,
	7028,
	8903,
	8242,
	8024,
	9020,
	6812,
	2798,
	1862,
	807,
	3635,
	2798,
	2814,
	1223,
	3881,
	2798,
	2015,
	768,
	3881,
	8626,
	8631,
	7975,
	7975,
	7987,
	7987,
	8993,
	4364,
	6280,
	6745,
	3889,
	4259,
	8549,
	8178,
	3094,
	4250,
	3881,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	4168,
	4190,
	3826,
	4190,
	3826,
	2712,
	1949,
	8485,
	7900,
	8200,
	7895,
	8615,
	7902,
	8615,
	7902,
	8615,
	7902,
	8200,
	8318,
	7896,
	8318,
	7896,
	5652,
	8200,
	4364,
	4250,
	3881,
	3518,
	3518,
	2810,
	2810,
	2086,
	4216,
	3852,
	2323,
	3185,
	2315,
	3584,
	4364,
	4250,
	6281,
	6744,
	3889,
	4259,
	8550,
	8179,
	3095,
	6305,
	4364,
	4250,
	3928,
	7683,
	7686,
	6310,
	6282,
	3890,
	8986,
	4261,
	8560,
	8180,
	4250,
	3881,
	4168,
	3807,
	8485,
	7900,
	8200,
	7895,
	0,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_AudioModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AudioModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AudioModule_CodeGenModule = 
{
	"UnityEngine.AudioModule.dll",
	543,
	s_methodPointers,
	36,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_AudioModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
