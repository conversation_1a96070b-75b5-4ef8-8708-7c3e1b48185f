﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void CustomGridBrushAttribute_get_hideAssetInstances_mD6DB583085628D876BBCF3EBC46D66F50545EFB0 (void);
extern void CustomGridBrushAttribute_get_hideDefaultInstance_mA3B48C07EB3D0BE4DCAB29D79B2BC0C58F977D33 (void);
extern void CustomGridBrushAttribute_get_defaultBrush_mCF98AC1A3E33637052E17F5973D5734D8AB5229B (void);
extern void CustomGridBrushAttribute_get_defaultName_mA2ED760F97D84DD487BB9F3BB30978AAA3EE83F5 (void);
extern void CustomGridBrushAttribute__ctor_m5F1F905926920BE40EF1AC24AB6AFF2A78832456 (void);
extern void CustomGridBrushAttribute__ctor_mFA20642A55F366B72998E96AACB1E4E9193DA7E1 (void);
extern void GridBrushBase_Paint_mAB021A5CAD6E6F03305A05C608B28974E96D38E2 (void);
extern void GridBrushBase_Erase_m885E585BF9CE3F9F3DDAF82135955E42EB3D7381 (void);
extern void GridBrushBase_BoxFill_m339F8702368573CBE10F511B6361BE4FE27F7317 (void);
extern void GridBrushBase_BoxErase_m221878EA369238960CA66C5515E3DCF03F9C8257 (void);
extern void GridBrushBase_Select_m706785DFD40958CDDBA5DA385059259A3B2EC5EC (void);
extern void GridBrushBase_FloodFill_m918CDB97BAB08070FA4BB82AEBD75986CA427CCD (void);
extern void GridBrushBase_Rotate_m15AB38C5460AFF3EE07E474B755A61E150F32A8A (void);
extern void GridBrushBase_Flip_m2517F99AC14E4EA6D409CAA87FB7843F2736D158 (void);
extern void GridBrushBase_Pick_m289AD6CD6D1E1F61925C79D65B493D71810A6373 (void);
extern void GridBrushBase_Move_mC9C9C46E8B9FC9377C23C93EA21445120DF0FD2F (void);
extern void GridBrushBase_MoveStart_m2AFF8E7E281A676F104FED391A6548315F45F447 (void);
extern void GridBrushBase_MoveEnd_mA9222A8BA76AD1A15901684F5E71FA3E49C30F3F (void);
extern void GridBrushBase_ChangeZPosition_m1779910866817224A1A3F3FD8C373BC1E6203B71 (void);
extern void GridBrushBase_ResetZPosition_mDC3A07AFAFCA14F1EC4AAAF26BAAC742A2B3B580 (void);
extern void GridBrushBase__ctor_m915B283A203B8C8929AD7F0859CCB0D6631DD3F7 (void);
extern void ITilemap__ctor_m3281F6903F18F9B867E6B81E18BCCD0828084258 (void);
extern void ITilemap__ctor_mF9DA909125B9F00CC0015DD384DAD589121AC169 (void);
extern void ITilemap_op_Implicit_mCDEFFCC72BE7A896ACE6DD08747EA0B3AC1845A3 (void);
extern void ITilemap_SetTilemapInstance_m8B4453F43E9095D98A401A4E40396DEC9AEBEF34 (void);
extern void ITilemap_get_origin_m0829F20E3B4CA643D2AC6A5403F36478FD40DD80 (void);
extern void ITilemap_get_size_m5D65CF984A2463AE9A6556E14F1F3D59F31E1BD2 (void);
extern void ITilemap_get_localBounds_mDF136600B2EB878E9FBD0679F071108F8B24A38E (void);
extern void ITilemap_get_cellBounds_mFF3A47FDF04B9DCAB15EF850CC12C95B9BFA2B5B (void);
extern void ITilemap_GetSprite_m60702DA918F169E69DC86429BF7E812DAEA80AEB (void);
extern void ITilemap_GetColor_m7B9A4618615B5E11548055F462A37CABCA897F34 (void);
extern void ITilemap_GetTransformMatrix_m33E4C3779536D9B30E4F2BA3367D07924922F76E (void);
extern void ITilemap_GetTileFlags_mB255E90568B6F509FC4A270BFFEEFE3FA5E2C424 (void);
extern void ITilemap_GetTile_mA6D6CD833634FA3B4A4F38D3904AB5BE80C31E65 (void);
extern void ITilemap_RefreshTile_m4C4B0A062A13E986BD20AA87F056982D67FAF69D (void);
extern void ITilemap_CreateInstance_m63D3D1EDDCA86A1F1A116A2205D798AD2BAF3E96 (void);
extern void ITilemap_FindAllRefreshPositions_m681FAC77C526640B18549097C961C85EA5846CCC (void);
extern void ITilemap_GetAllTileData_m3B6CF200F925322F951314FE8336C581A782F804 (void);
extern void Tile_get_sprite_m3324CBA00505C3C95DA57FC3A6F8B0D5FA2EF553 (void);
extern void Tile_set_sprite_mD9F351775FDFDFFA0FCC40121B4C54D566052D18 (void);
extern void Tile_get_color_mD50E790F486A1E64757E9471D48BA42FC9ECCE4C (void);
extern void Tile_set_color_m9D76C21865CA89F39FF56C112CB13AFD45CD8B69 (void);
extern void Tile_get_transform_mFA119A0C353E4E75C92C8BE829C6BDFA40F17643 (void);
extern void Tile_set_transform_m2E46927D29823DBDC3B7B36E013845006075EB02 (void);
extern void Tile_get_gameObject_m8B1B09FD1B6B5A0402D63D3AFF139C6078754077 (void);
extern void Tile_set_gameObject_mD4C82AFCA4B96D44BE5549CFF9E0F36218A4ECE9 (void);
extern void Tile_get_flags_m4AC2E9F8CF43DB83E9F8389EFDF7E6111E5A9806 (void);
extern void Tile_set_flags_mE221D85F2B767EC5C3D473266CB7AABD66674DEA (void);
extern void Tile_get_colliderType_mDB7A2E3BEF055617F6AC198841938B79C289E967 (void);
extern void Tile_set_colliderType_m21E434F55E4CC8AEB867E7FCF88821EFFC9CEB3F (void);
extern void Tile_GetTileData_m187B4A0A655AAB70CC8EC203F78E4777ABB96D4E (void);
extern void Tile__ctor_m1680C25E80E5ACCB156133C14199BD5BFE00EA5E (void);
extern void TileBase_RefreshTile_m7302220B588658247D635871B92DBFF7708E2224 (void);
extern void TileBase_GetTileData_m04B3B474F4DBF88997FF29ABA115A2FFB91BAF81 (void);
extern void TileBase_GetTileDataNoRef_m657510B6853906E397D8FC7E6F1A8B2DC4B34397 (void);
extern void TileBase_GetTileAnimationData_m8E1C84B8BC0B38E978ECEE6C7AD50D7D8BF810FE (void);
extern void TileBase_GetTileAnimationDataNoRef_m061D2FB92E28E5C2379385827F78C22719287D97 (void);
extern void TileBase_GetTileAnimationDataRef_m10D856F55369986224F166E8EEF5633EB8EBA5C3 (void);
extern void TileBase_StartUp_mBAF37DBB4DCC7BDB384352D93AB609CEB0E2E78B (void);
extern void TileBase_StartUpRef_mB00DB38868F87645811DE4784F57278388FAEEF9 (void);
extern void TileBase__ctor_mBFD0A0ACF9DB1F08783B9F3F35D4E61C9205D4A2 (void);
extern void Tilemap_add_tilemapTileChanged_mEED794BBD361501749622D5EB6E0362F16F2E100 (void);
extern void Tilemap_remove_tilemapTileChanged_m5C38616AEBA01E6D988E27A8EC0BECDE7AC7F66D (void);
extern void Tilemap_add_tilemapPositionsChanged_m4870F364AB7AF0C9F0AB10486830041DC6EF627C (void);
extern void Tilemap_remove_tilemapPositionsChanged_mA60BF9DFD34C66C03EF5510E1E6BF0B80DA70B39 (void);
extern void Tilemap_get_bufferSyncTile_m5506F240CC262FD454CFF9B547F16530F9506B1D (void);
extern void Tilemap_set_bufferSyncTile_m45F411D20BA3FBDDDAE1E569F274F24638A45758 (void);
extern void Tilemap_HasSyncTileCallback_m522AE13C1DEBDDA7EBC7C9BAF1302EB75EF3A0EB (void);
extern void Tilemap_HasPositionsChangedCallback_mD02A9A567086C4F60CA7B4733EAFD173289857FF (void);
extern void Tilemap_HandleSyncTileCallback_mF1D8059E6F8ED90041313259D5DCFC3DBEB8750A (void);
extern void Tilemap_HandlePositionsChangedCallback_mCEC3B01A5328F6C83163C25CE9EDCD87E5895CD0 (void);
extern void Tilemap_SendTilemapTileChangedCallback_m66E5D12B134C48E57EF4C1B29658CD61B75366EF (void);
extern void Tilemap_SendTilemapPositionsChangedCallback_m8F1D0E0F18A797349A83465F5E68DF01972D75D4 (void);
extern void Tilemap_SetSyncTileCallback_mCC1B70B13C24FE10FEBBCB4EC00CD4A89310A7D7 (void);
extern void Tilemap_RemoveSyncTileCallback_m84CC8497745CE12E2F39EE72D6BF10405DC3DC15 (void);
extern void Tilemap_get_layoutGrid_m84B3A21E3E9744E83DBD07448DBD8C01CE0E257E (void);
extern void Tilemap_GetCellCenterLocal_m108918F862E23ECE7B0ED7AF2CF21A767F52124A (void);
extern void Tilemap_GetCellCenterWorld_m567FBE8E0822C9A75681D3B95AD9FDDF3C43A4F3 (void);
extern void Tilemap_get_cellBounds_m2C1EDCFFD145175A83457B4F7A88CEA037DF8EB9 (void);
extern void Tilemap_get_localBounds_m6188638F4F369F32E16D25AB71AFC41691A95342 (void);
extern void Tilemap_get_localFrameBounds_mA6650197618602764BC071D494CD32442B01B82E (void);
extern void Tilemap_get_animationFrameRate_m391ACF664A9239DCB5A3344AE6A27A6D924234AE (void);
extern void Tilemap_set_animationFrameRate_m38571B9C6F0E7ED975531118CA4240730C44F55F (void);
extern void Tilemap_get_color_mCA6C50E1BFAD7110551D2A261C2A0508CBDA7B93 (void);
extern void Tilemap_set_color_m27BC001757EF2950802916FAAF2BE86BEFEBF577 (void);
extern void Tilemap_get_origin_mB5E10582CFAA76144BB44DECAADB84E904D02E55 (void);
extern void Tilemap_set_origin_mA02F4D6E5E74AC5AC9026F9C044A8D581345560A (void);
extern void Tilemap_get_size_m8B9F0C2CC3CD37626AE921047DA5DC239B3F00EA (void);
extern void Tilemap_set_size_m83996A5DFE30566272FD3D29E17944510A61F7C6 (void);
extern void Tilemap_get_tileAnchor_mD3C7F2A024DC43019CEB93682307ED41EC3329E4 (void);
extern void Tilemap_set_tileAnchor_m0B89DD209FCB7B2B47C3B434E6D73BFF77A3D817 (void);
extern void Tilemap_get_orientation_m711F644B1523FA2D94AE0069C949C82C0579DA17 (void);
extern void Tilemap_set_orientation_m77F1E6BA44321F1B2607F24028BB8A99D939FD7C (void);
extern void Tilemap_get_orientationMatrix_mF63DF1E4FC7E4B7DE10CE67DFDBB130262784F24 (void);
extern void Tilemap_set_orientationMatrix_m92B5E5D5135DFFAAAE61EFC7FA72934B1FA33D47 (void);
extern void Tilemap_GetTileAsset_m3B9C96C2E2488141C4F6EBD52C6D807C801C6922 (void);
extern void Tilemap_GetTile_m8500FBFF853C9E813810929BD29D7A866B516225 (void);
extern void Tilemap_GetTileAssetsBlock_m28BAE62DD83EFD00528A150B7728880DACAC0026 (void);
extern void Tilemap_GetTilesBlock_mC108BEC2990B85024511A1B531D534843DEC2052 (void);
extern void Tilemap_GetTileAssetsBlockNonAlloc_m2BAA311398DE9C0B1590D662482FF292931B575F (void);
extern void Tilemap_GetTilesBlockNonAlloc_m07991C1F81371CA6D8451A26CC426316F37DB279 (void);
extern void Tilemap_GetTilesRangeCount_m6FE6DDC849E6040A3F2FF6A8361440071C1432DF (void);
extern void Tilemap_GetTileAssetsRangeNonAlloc_mA42F628851793097696368EEA780D0240112D3DE (void);
extern void Tilemap_GetTilesRangeNonAlloc_mB8576F4124BA4C95D37A1670F70DAD3ED344F52F (void);
extern void Tilemap_SetTileAsset_m88D70B08B3D291F99EB34F01136C2D4EEBE45D4B (void);
extern void Tilemap_SetTile_m880BD0CC6B69A4B15495C4FBD2CBEA50D1BE23BA (void);
extern void Tilemap_SetTileAssets_m80E6341D44AE4E50942B6A4A8E66CC45F8BEDAD5 (void);
extern void Tilemap_SetTiles_m640756E2253F1E6B5351B686862F6867D351C714 (void);
extern void Tilemap_INTERNAL_CALL_SetTileAssetsBlock_mD20C1320B3C36E2A5F79BFF9A6B9EAABF3AF5437 (void);
extern void Tilemap_SetTilesBlock_m5BCE73C279E736DC854CB5D5240933605F5C7FE0 (void);
extern void Tilemap_SetTile_mE616C36D859B495BED0154C448FFAE16E5D2BE5D (void);
extern void Tilemap_SetTiles_m4924C2A405E6EE00FA5D57E4EFDBA6DA0E291C80 (void);
extern void Tilemap_HasTile_mC868AB1BEA16A2C686BF5CC3C3267780791804F6 (void);
extern void Tilemap_RefreshTile_mEF4F94212FD9B311431DFFAFE092A4A6EBA580DF (void);
extern void Tilemap_RefreshTilesNative_mD73E77DFD7C808A3665CA8389F728CBC08A52232 (void);
extern void Tilemap_RefreshAllTiles_mA64BB2AFE77727C6358ACDA467A7B082A0034A9E (void);
extern void Tilemap_SwapTileAsset_m8185544F41428C07809B2F7DCFC4DD808083C287 (void);
extern void Tilemap_SwapTile_mF22407A415D515F4FF044D6562C56F76EC14A1F2 (void);
extern void Tilemap_ContainsTileAsset_m14064994385EE754CE7CCF34CE8FB8E5AB299282 (void);
extern void Tilemap_ContainsTile_mF736DC44D65279BFF1A13A1B3D0860E0FF27B65D (void);
extern void Tilemap_GetUsedTilesCount_mF75EB807D49AEF1AA1748984D238B55946A4DD4A (void);
extern void Tilemap_GetUsedSpritesCount_m387E8F4A641C83F8E9B5692FD5425CAB654DCC45 (void);
extern void Tilemap_GetUsedTilesNonAlloc_m4FEDE5F12A7A1333A4A2082637CFEC76B7903E14 (void);
extern void Tilemap_GetUsedSpritesNonAlloc_m1E4C3E0C152EFA240A79EC1DCD1CF62502951582 (void);
extern void Tilemap_Internal_GetUsedTilesNonAlloc_m79F745C755C26362833B0F0CC8C5846547EF5C4F (void);
extern void Tilemap_Internal_GetUsedSpritesNonAlloc_mCDD01ECE51A344C6224D34F199A9F553AF086784 (void);
extern void Tilemap_GetSprite_mF670E851C6BF5A3173758A2723D10F7E01000AD4 (void);
extern void Tilemap_GetTransformMatrix_mD2D0E0922FF1AB8478FB8ECEE0CD219FB4801D45 (void);
extern void Tilemap_SetTransformMatrix_mDE5369FA4AE0968EE5911677081D57F253890780 (void);
extern void Tilemap_GetColor_m988A369F124E34CB104F51F4F505C989097FFB22 (void);
extern void Tilemap_SetColor_mAD8F9AF56705F031E26DA66C337DBA265C3F664C (void);
extern void Tilemap_GetTileFlags_mE872A14709B173A779297DBD8BA9CA090C8B6670 (void);
extern void Tilemap_SetTileFlags_m7BD0C9773D97AE412E082DE2B1E1254A5C760CCD (void);
extern void Tilemap_AddTileFlags_m5E441CD29076AE8DFD96124C163304B13B186F6D (void);
extern void Tilemap_RemoveTileFlags_m26D2614D61FC658D32B482BD9434CC3A3400AA1A (void);
extern void Tilemap_GetInstantiatedObject_m2B87C2D1D2C2A34B5A9199FFA9D8B551B2F481BE (void);
extern void Tilemap_GetObjectToInstantiate_mFC2C5CEADF9702FA5859F85BD7CB897A538BCAA7 (void);
extern void Tilemap_SetColliderType_m7E4FAC8E760652936672988F7296033F7EE99175 (void);
extern void Tilemap_GetColliderType_mFA84D9B33D481FBDFB0FEED69E75A059CF253360 (void);
extern void Tilemap_GetAnimationFrameCount_m44F67E33E32B280FCC06D4A60956019D08AC999E (void);
extern void Tilemap_GetAnimationFrame_m5673F18913E65476A5A5649C3F87D4A7E99D86E8 (void);
extern void Tilemap_SetAnimationFrame_mB2FE463E151972FB400B38E13115E70D356C13F9 (void);
extern void Tilemap_GetAnimationTime_mB1BDC7AB2397D90B03D323C3569D400F894CE4DB (void);
extern void Tilemap_SetAnimationTime_m184310DDFC5FEB60D42C4D3CEA786A406CD60D4A (void);
extern void Tilemap_GetTileAnimationFlags_mEAAD44097328B4CBE061F0C6156B521D351B8F85 (void);
extern void Tilemap_SetTileAnimationFlags_mEA9A69719D5AE7EA413E6C6DF43584E74093C37F (void);
extern void Tilemap_AddTileAnimationFlags_mC983F01D0CB63718816E5F0B6A257FE57DAB287D (void);
extern void Tilemap_RemoveTileAnimationFlags_mD8BAD515F3F479EE2EC99A38DBFA962ABE11E410 (void);
extern void Tilemap_FloodFill_m946D30945658B425C405D8A84DB275B96249FF6B (void);
extern void Tilemap_FloodFillTileAsset_mE662F8026F3FA9E054D58640727D57417CD33A9F (void);
extern void Tilemap_BoxFill_mBBCC977A18778B3A8C670FABDB81E488D07D829C (void);
extern void Tilemap_BoxFillTileAsset_mE880ED900FEA8679D2FAB08810A660402EF5A640 (void);
extern void Tilemap_InsertCells_mC8B2BEA451DF07D3C024AA9E6E524A4707F6A2C8 (void);
extern void Tilemap_InsertCells_mD674C03ECC2F720959DD5140FA70D610355274F3 (void);
extern void Tilemap_DeleteCells_m59A5953C64C3870D3829152BD1B7A4F03FDC73F0 (void);
extern void Tilemap_DeleteCells_mF179BB21D0E8F5977ABF28FA370FB219CF2EAD6F (void);
extern void Tilemap_ClearAllTiles_m440B00506358103B65F7A2FE3B3AC44F621FE5B6 (void);
extern void Tilemap_ResizeBounds_m1D31DCE27D7A86B496D4025323210BDC90058C48 (void);
extern void Tilemap_CompressBounds_m18C02BCE7B7EFF644D5B7B7EC8E6E0CC7B46E531 (void);
extern void Tilemap_GetSyncTileCallbackSettings_m1630BBFA37F85D2E29E73EA92DB13C700CC86B29 (void);
extern void Tilemap_SendAndClearSyncTileBuffer_m08E46EDA97D453F5A365E3DC1BBEBB6F74301067 (void);
extern void Tilemap_DoSyncTileCallback_m7BF07E7C678E7A55BDF116FA7C5BEF29963402A2 (void);
extern void Tilemap_DoPositionsChangedCallback_mCD3C79A37783BB7DD22454981E0B51394B7990F4 (void);
extern void Tilemap__ctor_m1D0F3884F418FC0D39DE07F85E356B9A733F138C (void);
extern void Tilemap_get_localBounds_Injected_m4CAD2C2B9B4B9AEE6229C25DDFA54425B4AFEA9A (void);
extern void Tilemap_get_localFrameBounds_Injected_m5F4E34165C0EA88A26796EE6242729969D5DBB5C (void);
extern void Tilemap_get_color_Injected_m289F63DA71B34AAE6826E4D35A2164097C4813E6 (void);
extern void Tilemap_set_color_Injected_m3301BC28A921760D04B61597AFF6E6022591F74F (void);
extern void Tilemap_get_origin_Injected_m151021A1FFF979A4AC9CC2C33CC34AB60CA11ADB (void);
extern void Tilemap_set_origin_Injected_m719AD06C0133BEFC33997AC1D1524A57B1BFFBFC (void);
extern void Tilemap_get_size_Injected_m493367797FDFDAA59CC626261CE2F3D310BD3B17 (void);
extern void Tilemap_set_size_Injected_mB517504AC10D24A29A73E001C6845078D537AB84 (void);
extern void Tilemap_get_tileAnchor_Injected_mDE5386464C63ACB22779CD1C0B6D78EF00D19ADE (void);
extern void Tilemap_set_tileAnchor_Injected_m964D77CC892A3EAEBA530B9E478B8347056A2239 (void);
extern void Tilemap_get_orientationMatrix_Injected_mBF67A03412A164065694FCE3B0A7A53627191128 (void);
extern void Tilemap_set_orientationMatrix_Injected_mDBB95DE3F3D36296AC7985C5C8131A71E08A1A32 (void);
extern void Tilemap_GetTileAsset_Injected_m541A1F618DB6621D2ADCD4247D5127DEDDFA3B30 (void);
extern void Tilemap_GetTileAssetsBlock_Injected_m2CBEC1BB224C47AEB888F4C9FA39E64C73430BEE (void);
extern void Tilemap_GetTileAssetsBlockNonAlloc_Injected_mA924FF04A2AE7A3AD7476DEB33A1A6D85BFC91F9 (void);
extern void Tilemap_GetTilesRangeCount_Injected_m72E6122991422754F201528F8CD46273FD42FA11 (void);
extern void Tilemap_GetTileAssetsRangeNonAlloc_Injected_mA7E8C7D274060ECB4FB03EF48E4E550FD71E2CD3 (void);
extern void Tilemap_SetTileAsset_Injected_m45031F05D325CDEA542B7B64AAD9893B2FFDA3CA (void);
extern void Tilemap_INTERNAL_CALL_SetTileAssetsBlock_Injected_mEE997E9C8EE9C12354F05C969389E956638AC091 (void);
extern void Tilemap_SetTile_Injected_m898AA464C83D0CFDC77523C98C265F34D728DD19 (void);
extern void Tilemap_RefreshTile_Injected_m99F1EC3F340590E282B01EC7C96F1F8D1BA03A69 (void);
extern void Tilemap_GetSprite_Injected_m1AC87EB502B50969FD98369D52D86AD559CA979D (void);
extern void Tilemap_GetTransformMatrix_Injected_mC76882554060CD7648515C690BEFA2E9940B460B (void);
extern void Tilemap_SetTransformMatrix_Injected_m88340E47F9B135ECFBEB7975CCEDD80CC1633E10 (void);
extern void Tilemap_GetColor_Injected_m5FD5600D223DFA45F0CEE26E610D4B67E7DCB208 (void);
extern void Tilemap_SetColor_Injected_m450C967EEEFE1174056E7EC2A15516F8E27A69AD (void);
extern void Tilemap_GetTileFlags_Injected_mA2A6713301B3AB8CD4764CCFC81BAAC0AB466B0F (void);
extern void Tilemap_SetTileFlags_Injected_m83119EED93BC5F0D99DC4CD9C55D8229C5666C0F (void);
extern void Tilemap_AddTileFlags_Injected_m64CCA40E2B0F79874B51530604054B79BDAB1B00 (void);
extern void Tilemap_RemoveTileFlags_Injected_m8D6A05B5BC88CE2C5201C6EDF7500B3515E52412 (void);
extern void Tilemap_GetInstantiatedObject_Injected_m6C5E99B111B446D484D0C218655B7A73997F6B14 (void);
extern void Tilemap_GetObjectToInstantiate_Injected_m36912C90869C9A9DF7054EC0E8FCC6987E49E0B9 (void);
extern void Tilemap_SetColliderType_Injected_m566B4B3BCF44CB5A30E2798BB5761DFA71548838 (void);
extern void Tilemap_GetColliderType_Injected_mC20B1CB561B1230391EE779C1B772DE672D0112B (void);
extern void Tilemap_GetAnimationFrameCount_Injected_m35D7E7AA8610A049D4CC20C5B0DD4FEE296A591A (void);
extern void Tilemap_GetAnimationFrame_Injected_m7ED5754B942DBB734C8A29E7FD2DFAD611333A8F (void);
extern void Tilemap_SetAnimationFrame_Injected_mDB66AD750CA91C833AA9BC4B6AF8C007D9406862 (void);
extern void Tilemap_GetAnimationTime_Injected_m99ED64409B69D42969B5B2A425F8DCD4C85DA25A (void);
extern void Tilemap_SetAnimationTime_Injected_mB9A56E73F9820BBFB220E6B896DBC3FBD897EF77 (void);
extern void Tilemap_GetTileAnimationFlags_Injected_mB57294E620647EFBDE43BBC42B840CE438265BB5 (void);
extern void Tilemap_SetTileAnimationFlags_Injected_mBE49BDD6BF53D3AC9F41ECECDCCA6DA164A6E5B4 (void);
extern void Tilemap_AddTileAnimationFlags_Injected_m9EB378B9B47E1ED377DB0BDD664A7CB1F7C5A038 (void);
extern void Tilemap_RemoveTileAnimationFlags_Injected_mE67E879EBBA86E37BA90DBFD2B82A61723D8568C (void);
extern void Tilemap_FloodFillTileAsset_Injected_mE576684EA3396DA63CDD2A9046FECA35631F12B2 (void);
extern void Tilemap_BoxFillTileAsset_Injected_mAC37DD9FAF6B478739B95A55B82D601B29188965 (void);
extern void Tilemap_InsertCells_Injected_m3C52681434780B1E732451BD8DC0CB313E8B4144 (void);
extern void Tilemap_DeleteCells_Injected_m2AFB877EBE83B5C62B71D6615D12DE9E7AA0A19E (void);
extern void SyncTile_get_position_mD695EB42E035B67BB880253B3AFD8CF22E7DB681 (void);
extern void SyncTile_get_tile_m9E09DFD87E065D24135B377FC5BC8A4FE60C2ECA (void);
extern void SyncTile_get_tileData_mAB00B5C96FCB905D19D03018FFD057D3A1C82FE8 (void);
extern void TilemapRenderer_get_chunkSize_m7D19867A855856955A7C6F1C8BF0875BC3427AC3 (void);
extern void TilemapRenderer_set_chunkSize_m5CFDCDE54FF72415499F620C4D79EDEE402C4402 (void);
extern void TilemapRenderer_get_chunkCullingBounds_m49A6D8F15810FB1536DE9374E57A40A205B09B3A (void);
extern void TilemapRenderer_set_chunkCullingBounds_m5E6C496342BD14FE8C594A555C366722C123061D (void);
extern void TilemapRenderer_get_maxChunkCount_mF58222A7DFF1332D102F6A625C2DE787C5F8E8F0 (void);
extern void TilemapRenderer_set_maxChunkCount_mD91628AE8E564F807410DDEB9EE67F13359A31A6 (void);
extern void TilemapRenderer_get_maxFrameAge_m98F7BEA0C7D0147AABC53E3882986735B063550C (void);
extern void TilemapRenderer_set_maxFrameAge_mB347526302FF8254C4E1755C4F61870BEE2E846C (void);
extern void TilemapRenderer_get_sortOrder_m6D4688F40BAAC4697EACDC53C392248CD6C536AA (void);
extern void TilemapRenderer_set_sortOrder_m43C942832581D0E2D5DC8553D5DE89713094676E (void);
extern void TilemapRenderer_get_mode_m030D6FE35D238E85CC6E42E6B5E873E8EE9702B9 (void);
extern void TilemapRenderer_set_mode_m57492E00C75930E9CA2D518F71B1F7A32DD16AC3 (void);
extern void TilemapRenderer_get_detectChunkCullingBounds_m3A8383E994AF302B3E2EC3AFACEDC0B11E8954E9 (void);
extern void TilemapRenderer_set_detectChunkCullingBounds_m7EECA4EC0AE104C34E7B8BC240E6CBA6B8234153 (void);
extern void TilemapRenderer_get_maskInteraction_m8C65636D0663C6CE1B04A70D5627427847716CE3 (void);
extern void TilemapRenderer_set_maskInteraction_mBB928D483066888AFDC966B152E445C48A234234 (void);
extern void TilemapRenderer_RegisterSpriteAtlasRegistered_m5D7676A05B0B16ABCCF4CEE57BA9E28FA4D171BC (void);
extern void TilemapRenderer_UnregisterSpriteAtlasRegistered_mFE33C972CF738A50A631203D0DD7E325AADFCB08 (void);
extern void TilemapRenderer_OnSpriteAtlasRegistered_m4348D78754045C8B10CEA76195A313790F412ED1 (void);
extern void TilemapRenderer__ctor_m0BF73FD7D70A7F8B74288CCB2DF52E9525DF9682 (void);
extern void TilemapRenderer_get_chunkSize_Injected_m390939496D3FD6B77C79D106A081C53FBEB13AA1 (void);
extern void TilemapRenderer_set_chunkSize_Injected_mD9643F92E6B02C090F8D8E3540153ABBD8101E1B (void);
extern void TilemapRenderer_get_chunkCullingBounds_Injected_m1B7F28F5B82681B2344D855F3B4E52428DA693DC (void);
extern void TilemapRenderer_set_chunkCullingBounds_Injected_m61415554CF936B37FF57D6448A7F2A68496DE622 (void);
extern void TileData_get_sprite_m8117B1A4F4A5722F067271A0B9BEB5C1806F90D1 (void);
extern void TileData_set_sprite_m3566544847F9C9C27EDB154324B6FBDB446EFE94 (void);
extern void TileData_get_color_m4295C0A6B7B4C64346C35CEAC67288D555864C9C (void);
extern void TileData_set_color_m5E759823878243A226EF46419FAD7C0CC3D5F40A (void);
extern void TileData_get_transform_mFE61A8584117E278A08845ED7933EBA8A6A4D1AD (void);
extern void TileData_set_transform_m71074A780C066292F940002A7165658E9CC01F9F (void);
extern void TileData_get_gameObject_m5CD61F71B1A0AC65B8B29A17C1A8D02374312FBC (void);
extern void TileData_set_gameObject_m1CE5B2AAAB5BF5AEF36EBAF2BCE23E4D2E5A9E09 (void);
extern void TileData_get_flags_m4837A688D8CB34334487838696FB191BDB9FB3E2 (void);
extern void TileData_set_flags_mEB46B1364D6DB7F77C2E1E43AFD31381B291BD30 (void);
extern void TileData_get_colliderType_mE24736B2027681E98CE454717A92833CE7E64D55 (void);
extern void TileData_set_colliderType_mE12359ADEF5F42CC0B635DCBAEC3035F0526FA96 (void);
extern void TileData_CreateDefault_m13A39001A47B60635B10FFD06AD65082CBB7D12D (void);
extern void TileData__cctor_mE2F5A802075C68DE978E46092DC8BF465182934A (void);
extern void TileDataNative_get_sprite_mB81AF44089B465F1039108B1EB1166AF3B1B1C42 (void);
extern void TileDataNative_set_sprite_mF852740033301EB69E09A592553B1B42EF9E7D9A (void);
extern void TileDataNative_get_color_mD24D7A62139693695B999760EE15CA47AF08F524 (void);
extern void TileDataNative_set_color_m4D6BCD85B0CBC9F8CC54D93E00AE7D84FAC87315 (void);
extern void TileDataNative_get_transform_mE8EF348FEAFED32B0AC6259A49563819D47080C1 (void);
extern void TileDataNative_set_transform_mE179C2A14C2C91D23CF0DC44290E5A5EE1F4D7F5 (void);
extern void TileDataNative_get_gameObject_m6EE8C68E57D7B9125D34AC43FCFEB30D371A6574 (void);
extern void TileDataNative_set_gameObject_mC346973665AE04A07846E58EF60E6A1162250F2C (void);
extern void TileDataNative_get_flags_m1C4548D1BE2E0BB0272F7A7DED1FED103B023CB9 (void);
extern void TileDataNative_set_flags_mFEE1AB34AE117E2AEF5EF20730D6631F9BE21F97 (void);
extern void TileDataNative_get_colliderType_mB91037F7EAEA9CAED95A7BE3781FE7F47003990B (void);
extern void TileDataNative_set_colliderType_mA530C61F19B85103CFB10445D7AC61785671B018 (void);
extern void TileDataNative_op_Implicit_mB1FED6CD246037D2995C5A3D3496844BC2F117F7 (void);
extern void TileChangeData_get_position_m2CEFE44B31F602A99450595D536D7E35F5AD487F (void);
extern void TileChangeData_set_position_mAEFB28585EAFABCA05D027118B5B05ED9B71DF1D (void);
extern void TileChangeData_get_tile_mDBF8C089B3AE67A312C0CF511FCFA9BA02B55D6D (void);
extern void TileChangeData_set_tile_m452C30A40E0EB1C08ADC8E0121F7BE041B7563C3 (void);
extern void TileChangeData_get_color_mC8642D45A1DA2588EC9F97EFB8543C4BF44B4868 (void);
extern void TileChangeData_set_color_mBBF03E52A23B8726A0BC450FAB327C7D756FFE3A (void);
extern void TileChangeData_get_transform_mC78733C53E477C8384205388597142A559A68A51 (void);
extern void TileChangeData_set_transform_mBA8AE961820A2BD978AA2AD424B0AA78D5C4DAF4 (void);
extern void TileChangeData__ctor_m1B47E18D78AB675EB05E748F419DD82D8D33D9AC (void);
extern void TileAnimationData_get_animatedSprites_mCC6863CFDF838099D42DA46F66D2A9E5224AB0D9 (void);
extern void TileAnimationData_set_animatedSprites_m315FE8DAB5071E1FA594AEA74B1B66BBF6A5C3E1 (void);
extern void TileAnimationData_get_animationSpeed_m7CCE976FA4FEEF7A55669ABB426EF94DAE97F032 (void);
extern void TileAnimationData_set_animationSpeed_mE1DB382A9D7F0385D70248A93B998405890D4611 (void);
extern void TileAnimationData_get_animationStartTime_mCA202FBC945C9514558C4618CB5A3D209A03D8D5 (void);
extern void TileAnimationData_set_animationStartTime_mBC2F61289403253C6B43C12576A98654B94A9B40 (void);
extern void TileAnimationData_get_flags_mF2A55E6A4868FAC4FC1BF9BAE41BA3D0CCCE2E15 (void);
extern void TileAnimationData_set_flags_m5C1157264844D313EB81E33FFD5CB7EAE165603D (void);
extern void TilemapCollider2D_get_useDelaunayMesh_mE905CD68FE2C597CFC4605C0C6D0918AA7E93CD4 (void);
extern void TilemapCollider2D_set_useDelaunayMesh_mC37750FD364653C16003BC276AF15C80F895E541 (void);
extern void TilemapCollider2D_get_maximumTileChangeCount_mCA6E3766B62AA1E1A04755EE2DE94FE141FD28D0 (void);
extern void TilemapCollider2D_set_maximumTileChangeCount_m97463BA84F6A1155859E06E0D824A6DE853A47D8 (void);
extern void TilemapCollider2D_get_extrusionFactor_m0AD1E008157C9D7A9C1989F338E9FA39B73C2C51 (void);
extern void TilemapCollider2D_set_extrusionFactor_m6CCB29ECA52A31308C64738C3EF7F499E71DBF64 (void);
extern void TilemapCollider2D_get_hasTilemapChanges_mFD0B7E0F5FCB08030BB277A2896DE4135FBC20CC (void);
extern void TilemapCollider2D_ProcessTilemapChanges_mE4477B0659CF2244978A617CAEE5AFDCB1FCE6A5 (void);
extern void TilemapCollider2D__ctor_m1DC2BBFC493CDF5FC7D2640765EF75F261DA824B (void);
static Il2CppMethodPointer s_methodPointers[294] = 
{
	CustomGridBrushAttribute_get_hideAssetInstances_mD6DB583085628D876BBCF3EBC46D66F50545EFB0,
	CustomGridBrushAttribute_get_hideDefaultInstance_mA3B48C07EB3D0BE4DCAB29D79B2BC0C58F977D33,
	CustomGridBrushAttribute_get_defaultBrush_mCF98AC1A3E33637052E17F5973D5734D8AB5229B,
	CustomGridBrushAttribute_get_defaultName_mA2ED760F97D84DD487BB9F3BB30978AAA3EE83F5,
	CustomGridBrushAttribute__ctor_m5F1F905926920BE40EF1AC24AB6AFF2A78832456,
	CustomGridBrushAttribute__ctor_mFA20642A55F366B72998E96AACB1E4E9193DA7E1,
	GridBrushBase_Paint_mAB021A5CAD6E6F03305A05C608B28974E96D38E2,
	GridBrushBase_Erase_m885E585BF9CE3F9F3DDAF82135955E42EB3D7381,
	GridBrushBase_BoxFill_m339F8702368573CBE10F511B6361BE4FE27F7317,
	GridBrushBase_BoxErase_m221878EA369238960CA66C5515E3DCF03F9C8257,
	GridBrushBase_Select_m706785DFD40958CDDBA5DA385059259A3B2EC5EC,
	GridBrushBase_FloodFill_m918CDB97BAB08070FA4BB82AEBD75986CA427CCD,
	GridBrushBase_Rotate_m15AB38C5460AFF3EE07E474B755A61E150F32A8A,
	GridBrushBase_Flip_m2517F99AC14E4EA6D409CAA87FB7843F2736D158,
	GridBrushBase_Pick_m289AD6CD6D1E1F61925C79D65B493D71810A6373,
	GridBrushBase_Move_mC9C9C46E8B9FC9377C23C93EA21445120DF0FD2F,
	GridBrushBase_MoveStart_m2AFF8E7E281A676F104FED391A6548315F45F447,
	GridBrushBase_MoveEnd_mA9222A8BA76AD1A15901684F5E71FA3E49C30F3F,
	GridBrushBase_ChangeZPosition_m1779910866817224A1A3F3FD8C373BC1E6203B71,
	GridBrushBase_ResetZPosition_mDC3A07AFAFCA14F1EC4AAAF26BAAC742A2B3B580,
	GridBrushBase__ctor_m915B283A203B8C8929AD7F0859CCB0D6631DD3F7,
	ITilemap__ctor_m3281F6903F18F9B867E6B81E18BCCD0828084258,
	ITilemap__ctor_mF9DA909125B9F00CC0015DD384DAD589121AC169,
	ITilemap_op_Implicit_mCDEFFCC72BE7A896ACE6DD08747EA0B3AC1845A3,
	ITilemap_SetTilemapInstance_m8B4453F43E9095D98A401A4E40396DEC9AEBEF34,
	ITilemap_get_origin_m0829F20E3B4CA643D2AC6A5403F36478FD40DD80,
	ITilemap_get_size_m5D65CF984A2463AE9A6556E14F1F3D59F31E1BD2,
	ITilemap_get_localBounds_mDF136600B2EB878E9FBD0679F071108F8B24A38E,
	ITilemap_get_cellBounds_mFF3A47FDF04B9DCAB15EF850CC12C95B9BFA2B5B,
	ITilemap_GetSprite_m60702DA918F169E69DC86429BF7E812DAEA80AEB,
	ITilemap_GetColor_m7B9A4618615B5E11548055F462A37CABCA897F34,
	ITilemap_GetTransformMatrix_m33E4C3779536D9B30E4F2BA3367D07924922F76E,
	ITilemap_GetTileFlags_mB255E90568B6F509FC4A270BFFEEFE3FA5E2C424,
	ITilemap_GetTile_mA6D6CD833634FA3B4A4F38D3904AB5BE80C31E65,
	NULL,
	ITilemap_RefreshTile_m4C4B0A062A13E986BD20AA87F056982D67FAF69D,
	NULL,
	ITilemap_CreateInstance_m63D3D1EDDCA86A1F1A116A2205D798AD2BAF3E96,
	ITilemap_FindAllRefreshPositions_m681FAC77C526640B18549097C961C85EA5846CCC,
	ITilemap_GetAllTileData_m3B6CF200F925322F951314FE8336C581A782F804,
	Tile_get_sprite_m3324CBA00505C3C95DA57FC3A6F8B0D5FA2EF553,
	Tile_set_sprite_mD9F351775FDFDFFA0FCC40121B4C54D566052D18,
	Tile_get_color_mD50E790F486A1E64757E9471D48BA42FC9ECCE4C,
	Tile_set_color_m9D76C21865CA89F39FF56C112CB13AFD45CD8B69,
	Tile_get_transform_mFA119A0C353E4E75C92C8BE829C6BDFA40F17643,
	Tile_set_transform_m2E46927D29823DBDC3B7B36E013845006075EB02,
	Tile_get_gameObject_m8B1B09FD1B6B5A0402D63D3AFF139C6078754077,
	Tile_set_gameObject_mD4C82AFCA4B96D44BE5549CFF9E0F36218A4ECE9,
	Tile_get_flags_m4AC2E9F8CF43DB83E9F8389EFDF7E6111E5A9806,
	Tile_set_flags_mE221D85F2B767EC5C3D473266CB7AABD66674DEA,
	Tile_get_colliderType_mDB7A2E3BEF055617F6AC198841938B79C289E967,
	Tile_set_colliderType_m21E434F55E4CC8AEB867E7FCF88821EFFC9CEB3F,
	Tile_GetTileData_m187B4A0A655AAB70CC8EC203F78E4777ABB96D4E,
	Tile__ctor_m1680C25E80E5ACCB156133C14199BD5BFE00EA5E,
	TileBase_RefreshTile_m7302220B588658247D635871B92DBFF7708E2224,
	TileBase_GetTileData_m04B3B474F4DBF88997FF29ABA115A2FFB91BAF81,
	TileBase_GetTileDataNoRef_m657510B6853906E397D8FC7E6F1A8B2DC4B34397,
	TileBase_GetTileAnimationData_m8E1C84B8BC0B38E978ECEE6C7AD50D7D8BF810FE,
	TileBase_GetTileAnimationDataNoRef_m061D2FB92E28E5C2379385827F78C22719287D97,
	TileBase_GetTileAnimationDataRef_m10D856F55369986224F166E8EEF5633EB8EBA5C3,
	TileBase_StartUp_mBAF37DBB4DCC7BDB384352D93AB609CEB0E2E78B,
	TileBase_StartUpRef_mB00DB38868F87645811DE4784F57278388FAEEF9,
	TileBase__ctor_mBFD0A0ACF9DB1F08783B9F3F35D4E61C9205D4A2,
	Tilemap_add_tilemapTileChanged_mEED794BBD361501749622D5EB6E0362F16F2E100,
	Tilemap_remove_tilemapTileChanged_m5C38616AEBA01E6D988E27A8EC0BECDE7AC7F66D,
	Tilemap_add_tilemapPositionsChanged_m4870F364AB7AF0C9F0AB10486830041DC6EF627C,
	Tilemap_remove_tilemapPositionsChanged_mA60BF9DFD34C66C03EF5510E1E6BF0B80DA70B39,
	Tilemap_get_bufferSyncTile_m5506F240CC262FD454CFF9B547F16530F9506B1D,
	Tilemap_set_bufferSyncTile_m45F411D20BA3FBDDDAE1E569F274F24638A45758,
	Tilemap_HasSyncTileCallback_m522AE13C1DEBDDA7EBC7C9BAF1302EB75EF3A0EB,
	Tilemap_HasPositionsChangedCallback_mD02A9A567086C4F60CA7B4733EAFD173289857FF,
	Tilemap_HandleSyncTileCallback_mF1D8059E6F8ED90041313259D5DCFC3DBEB8750A,
	Tilemap_HandlePositionsChangedCallback_mCEC3B01A5328F6C83163C25CE9EDCD87E5895CD0,
	Tilemap_SendTilemapTileChangedCallback_m66E5D12B134C48E57EF4C1B29658CD61B75366EF,
	Tilemap_SendTilemapPositionsChangedCallback_m8F1D0E0F18A797349A83465F5E68DF01972D75D4,
	Tilemap_SetSyncTileCallback_mCC1B70B13C24FE10FEBBCB4EC00CD4A89310A7D7,
	Tilemap_RemoveSyncTileCallback_m84CC8497745CE12E2F39EE72D6BF10405DC3DC15,
	Tilemap_get_layoutGrid_m84B3A21E3E9744E83DBD07448DBD8C01CE0E257E,
	Tilemap_GetCellCenterLocal_m108918F862E23ECE7B0ED7AF2CF21A767F52124A,
	Tilemap_GetCellCenterWorld_m567FBE8E0822C9A75681D3B95AD9FDDF3C43A4F3,
	Tilemap_get_cellBounds_m2C1EDCFFD145175A83457B4F7A88CEA037DF8EB9,
	Tilemap_get_localBounds_m6188638F4F369F32E16D25AB71AFC41691A95342,
	Tilemap_get_localFrameBounds_mA6650197618602764BC071D494CD32442B01B82E,
	Tilemap_get_animationFrameRate_m391ACF664A9239DCB5A3344AE6A27A6D924234AE,
	Tilemap_set_animationFrameRate_m38571B9C6F0E7ED975531118CA4240730C44F55F,
	Tilemap_get_color_mCA6C50E1BFAD7110551D2A261C2A0508CBDA7B93,
	Tilemap_set_color_m27BC001757EF2950802916FAAF2BE86BEFEBF577,
	Tilemap_get_origin_mB5E10582CFAA76144BB44DECAADB84E904D02E55,
	Tilemap_set_origin_mA02F4D6E5E74AC5AC9026F9C044A8D581345560A,
	Tilemap_get_size_m8B9F0C2CC3CD37626AE921047DA5DC239B3F00EA,
	Tilemap_set_size_m83996A5DFE30566272FD3D29E17944510A61F7C6,
	Tilemap_get_tileAnchor_mD3C7F2A024DC43019CEB93682307ED41EC3329E4,
	Tilemap_set_tileAnchor_m0B89DD209FCB7B2B47C3B434E6D73BFF77A3D817,
	Tilemap_get_orientation_m711F644B1523FA2D94AE0069C949C82C0579DA17,
	Tilemap_set_orientation_m77F1E6BA44321F1B2607F24028BB8A99D939FD7C,
	Tilemap_get_orientationMatrix_mF63DF1E4FC7E4B7DE10CE67DFDBB130262784F24,
	Tilemap_set_orientationMatrix_m92B5E5D5135DFFAAAE61EFC7FA72934B1FA33D47,
	Tilemap_GetTileAsset_m3B9C96C2E2488141C4F6EBD52C6D807C801C6922,
	Tilemap_GetTile_m8500FBFF853C9E813810929BD29D7A866B516225,
	NULL,
	Tilemap_GetTileAssetsBlock_m28BAE62DD83EFD00528A150B7728880DACAC0026,
	Tilemap_GetTilesBlock_mC108BEC2990B85024511A1B531D534843DEC2052,
	Tilemap_GetTileAssetsBlockNonAlloc_m2BAA311398DE9C0B1590D662482FF292931B575F,
	Tilemap_GetTilesBlockNonAlloc_m07991C1F81371CA6D8451A26CC426316F37DB279,
	Tilemap_GetTilesRangeCount_m6FE6DDC849E6040A3F2FF6A8361440071C1432DF,
	Tilemap_GetTileAssetsRangeNonAlloc_mA42F628851793097696368EEA780D0240112D3DE,
	Tilemap_GetTilesRangeNonAlloc_mB8576F4124BA4C95D37A1670F70DAD3ED344F52F,
	Tilemap_SetTileAsset_m88D70B08B3D291F99EB34F01136C2D4EEBE45D4B,
	Tilemap_SetTile_m880BD0CC6B69A4B15495C4FBD2CBEA50D1BE23BA,
	Tilemap_SetTileAssets_m80E6341D44AE4E50942B6A4A8E66CC45F8BEDAD5,
	Tilemap_SetTiles_m640756E2253F1E6B5351B686862F6867D351C714,
	Tilemap_INTERNAL_CALL_SetTileAssetsBlock_mD20C1320B3C36E2A5F79BFF9A6B9EAABF3AF5437,
	Tilemap_SetTilesBlock_m5BCE73C279E736DC854CB5D5240933605F5C7FE0,
	Tilemap_SetTile_mE616C36D859B495BED0154C448FFAE16E5D2BE5D,
	Tilemap_SetTiles_m4924C2A405E6EE00FA5D57E4EFDBA6DA0E291C80,
	Tilemap_HasTile_mC868AB1BEA16A2C686BF5CC3C3267780791804F6,
	Tilemap_RefreshTile_mEF4F94212FD9B311431DFFAFE092A4A6EBA580DF,
	Tilemap_RefreshTilesNative_mD73E77DFD7C808A3665CA8389F728CBC08A52232,
	Tilemap_RefreshAllTiles_mA64BB2AFE77727C6358ACDA467A7B082A0034A9E,
	Tilemap_SwapTileAsset_m8185544F41428C07809B2F7DCFC4DD808083C287,
	Tilemap_SwapTile_mF22407A415D515F4FF044D6562C56F76EC14A1F2,
	Tilemap_ContainsTileAsset_m14064994385EE754CE7CCF34CE8FB8E5AB299282,
	Tilemap_ContainsTile_mF736DC44D65279BFF1A13A1B3D0860E0FF27B65D,
	Tilemap_GetUsedTilesCount_mF75EB807D49AEF1AA1748984D238B55946A4DD4A,
	Tilemap_GetUsedSpritesCount_m387E8F4A641C83F8E9B5692FD5425CAB654DCC45,
	Tilemap_GetUsedTilesNonAlloc_m4FEDE5F12A7A1333A4A2082637CFEC76B7903E14,
	Tilemap_GetUsedSpritesNonAlloc_m1E4C3E0C152EFA240A79EC1DCD1CF62502951582,
	Tilemap_Internal_GetUsedTilesNonAlloc_m79F745C755C26362833B0F0CC8C5846547EF5C4F,
	Tilemap_Internal_GetUsedSpritesNonAlloc_mCDD01ECE51A344C6224D34F199A9F553AF086784,
	Tilemap_GetSprite_mF670E851C6BF5A3173758A2723D10F7E01000AD4,
	Tilemap_GetTransformMatrix_mD2D0E0922FF1AB8478FB8ECEE0CD219FB4801D45,
	Tilemap_SetTransformMatrix_mDE5369FA4AE0968EE5911677081D57F253890780,
	Tilemap_GetColor_m988A369F124E34CB104F51F4F505C989097FFB22,
	Tilemap_SetColor_mAD8F9AF56705F031E26DA66C337DBA265C3F664C,
	Tilemap_GetTileFlags_mE872A14709B173A779297DBD8BA9CA090C8B6670,
	Tilemap_SetTileFlags_m7BD0C9773D97AE412E082DE2B1E1254A5C760CCD,
	Tilemap_AddTileFlags_m5E441CD29076AE8DFD96124C163304B13B186F6D,
	Tilemap_RemoveTileFlags_m26D2614D61FC658D32B482BD9434CC3A3400AA1A,
	Tilemap_GetInstantiatedObject_m2B87C2D1D2C2A34B5A9199FFA9D8B551B2F481BE,
	Tilemap_GetObjectToInstantiate_mFC2C5CEADF9702FA5859F85BD7CB897A538BCAA7,
	Tilemap_SetColliderType_m7E4FAC8E760652936672988F7296033F7EE99175,
	Tilemap_GetColliderType_mFA84D9B33D481FBDFB0FEED69E75A059CF253360,
	Tilemap_GetAnimationFrameCount_m44F67E33E32B280FCC06D4A60956019D08AC999E,
	Tilemap_GetAnimationFrame_m5673F18913E65476A5A5649C3F87D4A7E99D86E8,
	Tilemap_SetAnimationFrame_mB2FE463E151972FB400B38E13115E70D356C13F9,
	Tilemap_GetAnimationTime_mB1BDC7AB2397D90B03D323C3569D400F894CE4DB,
	Tilemap_SetAnimationTime_m184310DDFC5FEB60D42C4D3CEA786A406CD60D4A,
	Tilemap_GetTileAnimationFlags_mEAAD44097328B4CBE061F0C6156B521D351B8F85,
	Tilemap_SetTileAnimationFlags_mEA9A69719D5AE7EA413E6C6DF43584E74093C37F,
	Tilemap_AddTileAnimationFlags_mC983F01D0CB63718816E5F0B6A257FE57DAB287D,
	Tilemap_RemoveTileAnimationFlags_mD8BAD515F3F479EE2EC99A38DBFA962ABE11E410,
	Tilemap_FloodFill_m946D30945658B425C405D8A84DB275B96249FF6B,
	Tilemap_FloodFillTileAsset_mE662F8026F3FA9E054D58640727D57417CD33A9F,
	Tilemap_BoxFill_mBBCC977A18778B3A8C670FABDB81E488D07D829C,
	Tilemap_BoxFillTileAsset_mE880ED900FEA8679D2FAB08810A660402EF5A640,
	Tilemap_InsertCells_mC8B2BEA451DF07D3C024AA9E6E524A4707F6A2C8,
	Tilemap_InsertCells_mD674C03ECC2F720959DD5140FA70D610355274F3,
	Tilemap_DeleteCells_m59A5953C64C3870D3829152BD1B7A4F03FDC73F0,
	Tilemap_DeleteCells_mF179BB21D0E8F5977ABF28FA370FB219CF2EAD6F,
	Tilemap_ClearAllTiles_m440B00506358103B65F7A2FE3B3AC44F621FE5B6,
	Tilemap_ResizeBounds_m1D31DCE27D7A86B496D4025323210BDC90058C48,
	Tilemap_CompressBounds_m18C02BCE7B7EFF644D5B7B7EC8E6E0CC7B46E531,
	Tilemap_GetSyncTileCallbackSettings_m1630BBFA37F85D2E29E73EA92DB13C700CC86B29,
	Tilemap_SendAndClearSyncTileBuffer_m08E46EDA97D453F5A365E3DC1BBEBB6F74301067,
	Tilemap_DoSyncTileCallback_m7BF07E7C678E7A55BDF116FA7C5BEF29963402A2,
	Tilemap_DoPositionsChangedCallback_mCD3C79A37783BB7DD22454981E0B51394B7990F4,
	Tilemap__ctor_m1D0F3884F418FC0D39DE07F85E356B9A733F138C,
	Tilemap_get_localBounds_Injected_m4CAD2C2B9B4B9AEE6229C25DDFA54425B4AFEA9A,
	Tilemap_get_localFrameBounds_Injected_m5F4E34165C0EA88A26796EE6242729969D5DBB5C,
	Tilemap_get_color_Injected_m289F63DA71B34AAE6826E4D35A2164097C4813E6,
	Tilemap_set_color_Injected_m3301BC28A921760D04B61597AFF6E6022591F74F,
	Tilemap_get_origin_Injected_m151021A1FFF979A4AC9CC2C33CC34AB60CA11ADB,
	Tilemap_set_origin_Injected_m719AD06C0133BEFC33997AC1D1524A57B1BFFBFC,
	Tilemap_get_size_Injected_m493367797FDFDAA59CC626261CE2F3D310BD3B17,
	Tilemap_set_size_Injected_mB517504AC10D24A29A73E001C6845078D537AB84,
	Tilemap_get_tileAnchor_Injected_mDE5386464C63ACB22779CD1C0B6D78EF00D19ADE,
	Tilemap_set_tileAnchor_Injected_m964D77CC892A3EAEBA530B9E478B8347056A2239,
	Tilemap_get_orientationMatrix_Injected_mBF67A03412A164065694FCE3B0A7A53627191128,
	Tilemap_set_orientationMatrix_Injected_mDBB95DE3F3D36296AC7985C5C8131A71E08A1A32,
	Tilemap_GetTileAsset_Injected_m541A1F618DB6621D2ADCD4247D5127DEDDFA3B30,
	Tilemap_GetTileAssetsBlock_Injected_m2CBEC1BB224C47AEB888F4C9FA39E64C73430BEE,
	Tilemap_GetTileAssetsBlockNonAlloc_Injected_mA924FF04A2AE7A3AD7476DEB33A1A6D85BFC91F9,
	Tilemap_GetTilesRangeCount_Injected_m72E6122991422754F201528F8CD46273FD42FA11,
	Tilemap_GetTileAssetsRangeNonAlloc_Injected_mA7E8C7D274060ECB4FB03EF48E4E550FD71E2CD3,
	Tilemap_SetTileAsset_Injected_m45031F05D325CDEA542B7B64AAD9893B2FFDA3CA,
	Tilemap_INTERNAL_CALL_SetTileAssetsBlock_Injected_mEE997E9C8EE9C12354F05C969389E956638AC091,
	Tilemap_SetTile_Injected_m898AA464C83D0CFDC77523C98C265F34D728DD19,
	Tilemap_RefreshTile_Injected_m99F1EC3F340590E282B01EC7C96F1F8D1BA03A69,
	Tilemap_GetSprite_Injected_m1AC87EB502B50969FD98369D52D86AD559CA979D,
	Tilemap_GetTransformMatrix_Injected_mC76882554060CD7648515C690BEFA2E9940B460B,
	Tilemap_SetTransformMatrix_Injected_m88340E47F9B135ECFBEB7975CCEDD80CC1633E10,
	Tilemap_GetColor_Injected_m5FD5600D223DFA45F0CEE26E610D4B67E7DCB208,
	Tilemap_SetColor_Injected_m450C967EEEFE1174056E7EC2A15516F8E27A69AD,
	Tilemap_GetTileFlags_Injected_mA2A6713301B3AB8CD4764CCFC81BAAC0AB466B0F,
	Tilemap_SetTileFlags_Injected_m83119EED93BC5F0D99DC4CD9C55D8229C5666C0F,
	Tilemap_AddTileFlags_Injected_m64CCA40E2B0F79874B51530604054B79BDAB1B00,
	Tilemap_RemoveTileFlags_Injected_m8D6A05B5BC88CE2C5201C6EDF7500B3515E52412,
	Tilemap_GetInstantiatedObject_Injected_m6C5E99B111B446D484D0C218655B7A73997F6B14,
	Tilemap_GetObjectToInstantiate_Injected_m36912C90869C9A9DF7054EC0E8FCC6987E49E0B9,
	Tilemap_SetColliderType_Injected_m566B4B3BCF44CB5A30E2798BB5761DFA71548838,
	Tilemap_GetColliderType_Injected_mC20B1CB561B1230391EE779C1B772DE672D0112B,
	Tilemap_GetAnimationFrameCount_Injected_m35D7E7AA8610A049D4CC20C5B0DD4FEE296A591A,
	Tilemap_GetAnimationFrame_Injected_m7ED5754B942DBB734C8A29E7FD2DFAD611333A8F,
	Tilemap_SetAnimationFrame_Injected_mDB66AD750CA91C833AA9BC4B6AF8C007D9406862,
	Tilemap_GetAnimationTime_Injected_m99ED64409B69D42969B5B2A425F8DCD4C85DA25A,
	Tilemap_SetAnimationTime_Injected_mB9A56E73F9820BBFB220E6B896DBC3FBD897EF77,
	Tilemap_GetTileAnimationFlags_Injected_mB57294E620647EFBDE43BBC42B840CE438265BB5,
	Tilemap_SetTileAnimationFlags_Injected_mBE49BDD6BF53D3AC9F41ECECDCCA6DA164A6E5B4,
	Tilemap_AddTileAnimationFlags_Injected_m9EB378B9B47E1ED377DB0BDD664A7CB1F7C5A038,
	Tilemap_RemoveTileAnimationFlags_Injected_mE67E879EBBA86E37BA90DBFD2B82A61723D8568C,
	Tilemap_FloodFillTileAsset_Injected_mE576684EA3396DA63CDD2A9046FECA35631F12B2,
	Tilemap_BoxFillTileAsset_Injected_mAC37DD9FAF6B478739B95A55B82D601B29188965,
	Tilemap_InsertCells_Injected_m3C52681434780B1E732451BD8DC0CB313E8B4144,
	Tilemap_DeleteCells_Injected_m2AFB877EBE83B5C62B71D6615D12DE9E7AA0A19E,
	SyncTile_get_position_mD695EB42E035B67BB880253B3AFD8CF22E7DB681,
	SyncTile_get_tile_m9E09DFD87E065D24135B377FC5BC8A4FE60C2ECA,
	SyncTile_get_tileData_mAB00B5C96FCB905D19D03018FFD057D3A1C82FE8,
	TilemapRenderer_get_chunkSize_m7D19867A855856955A7C6F1C8BF0875BC3427AC3,
	TilemapRenderer_set_chunkSize_m5CFDCDE54FF72415499F620C4D79EDEE402C4402,
	TilemapRenderer_get_chunkCullingBounds_m49A6D8F15810FB1536DE9374E57A40A205B09B3A,
	TilemapRenderer_set_chunkCullingBounds_m5E6C496342BD14FE8C594A555C366722C123061D,
	TilemapRenderer_get_maxChunkCount_mF58222A7DFF1332D102F6A625C2DE787C5F8E8F0,
	TilemapRenderer_set_maxChunkCount_mD91628AE8E564F807410DDEB9EE67F13359A31A6,
	TilemapRenderer_get_maxFrameAge_m98F7BEA0C7D0147AABC53E3882986735B063550C,
	TilemapRenderer_set_maxFrameAge_mB347526302FF8254C4E1755C4F61870BEE2E846C,
	TilemapRenderer_get_sortOrder_m6D4688F40BAAC4697EACDC53C392248CD6C536AA,
	TilemapRenderer_set_sortOrder_m43C942832581D0E2D5DC8553D5DE89713094676E,
	TilemapRenderer_get_mode_m030D6FE35D238E85CC6E42E6B5E873E8EE9702B9,
	TilemapRenderer_set_mode_m57492E00C75930E9CA2D518F71B1F7A32DD16AC3,
	TilemapRenderer_get_detectChunkCullingBounds_m3A8383E994AF302B3E2EC3AFACEDC0B11E8954E9,
	TilemapRenderer_set_detectChunkCullingBounds_m7EECA4EC0AE104C34E7B8BC240E6CBA6B8234153,
	TilemapRenderer_get_maskInteraction_m8C65636D0663C6CE1B04A70D5627427847716CE3,
	TilemapRenderer_set_maskInteraction_mBB928D483066888AFDC966B152E445C48A234234,
	TilemapRenderer_RegisterSpriteAtlasRegistered_m5D7676A05B0B16ABCCF4CEE57BA9E28FA4D171BC,
	TilemapRenderer_UnregisterSpriteAtlasRegistered_mFE33C972CF738A50A631203D0DD7E325AADFCB08,
	TilemapRenderer_OnSpriteAtlasRegistered_m4348D78754045C8B10CEA76195A313790F412ED1,
	TilemapRenderer__ctor_m0BF73FD7D70A7F8B74288CCB2DF52E9525DF9682,
	TilemapRenderer_get_chunkSize_Injected_m390939496D3FD6B77C79D106A081C53FBEB13AA1,
	TilemapRenderer_set_chunkSize_Injected_mD9643F92E6B02C090F8D8E3540153ABBD8101E1B,
	TilemapRenderer_get_chunkCullingBounds_Injected_m1B7F28F5B82681B2344D855F3B4E52428DA693DC,
	TilemapRenderer_set_chunkCullingBounds_Injected_m61415554CF936B37FF57D6448A7F2A68496DE622,
	TileData_get_sprite_m8117B1A4F4A5722F067271A0B9BEB5C1806F90D1,
	TileData_set_sprite_m3566544847F9C9C27EDB154324B6FBDB446EFE94,
	TileData_get_color_m4295C0A6B7B4C64346C35CEAC67288D555864C9C,
	TileData_set_color_m5E759823878243A226EF46419FAD7C0CC3D5F40A,
	TileData_get_transform_mFE61A8584117E278A08845ED7933EBA8A6A4D1AD,
	TileData_set_transform_m71074A780C066292F940002A7165658E9CC01F9F,
	TileData_get_gameObject_m5CD61F71B1A0AC65B8B29A17C1A8D02374312FBC,
	TileData_set_gameObject_m1CE5B2AAAB5BF5AEF36EBAF2BCE23E4D2E5A9E09,
	TileData_get_flags_m4837A688D8CB34334487838696FB191BDB9FB3E2,
	TileData_set_flags_mEB46B1364D6DB7F77C2E1E43AFD31381B291BD30,
	TileData_get_colliderType_mE24736B2027681E98CE454717A92833CE7E64D55,
	TileData_set_colliderType_mE12359ADEF5F42CC0B635DCBAEC3035F0526FA96,
	TileData_CreateDefault_m13A39001A47B60635B10FFD06AD65082CBB7D12D,
	TileData__cctor_mE2F5A802075C68DE978E46092DC8BF465182934A,
	TileDataNative_get_sprite_mB81AF44089B465F1039108B1EB1166AF3B1B1C42,
	TileDataNative_set_sprite_mF852740033301EB69E09A592553B1B42EF9E7D9A,
	TileDataNative_get_color_mD24D7A62139693695B999760EE15CA47AF08F524,
	TileDataNative_set_color_m4D6BCD85B0CBC9F8CC54D93E00AE7D84FAC87315,
	TileDataNative_get_transform_mE8EF348FEAFED32B0AC6259A49563819D47080C1,
	TileDataNative_set_transform_mE179C2A14C2C91D23CF0DC44290E5A5EE1F4D7F5,
	TileDataNative_get_gameObject_m6EE8C68E57D7B9125D34AC43FCFEB30D371A6574,
	TileDataNative_set_gameObject_mC346973665AE04A07846E58EF60E6A1162250F2C,
	TileDataNative_get_flags_m1C4548D1BE2E0BB0272F7A7DED1FED103B023CB9,
	TileDataNative_set_flags_mFEE1AB34AE117E2AEF5EF20730D6631F9BE21F97,
	TileDataNative_get_colliderType_mB91037F7EAEA9CAED95A7BE3781FE7F47003990B,
	TileDataNative_set_colliderType_mA530C61F19B85103CFB10445D7AC61785671B018,
	TileDataNative_op_Implicit_mB1FED6CD246037D2995C5A3D3496844BC2F117F7,
	TileChangeData_get_position_m2CEFE44B31F602A99450595D536D7E35F5AD487F,
	TileChangeData_set_position_mAEFB28585EAFABCA05D027118B5B05ED9B71DF1D,
	TileChangeData_get_tile_mDBF8C089B3AE67A312C0CF511FCFA9BA02B55D6D,
	TileChangeData_set_tile_m452C30A40E0EB1C08ADC8E0121F7BE041B7563C3,
	TileChangeData_get_color_mC8642D45A1DA2588EC9F97EFB8543C4BF44B4868,
	TileChangeData_set_color_mBBF03E52A23B8726A0BC450FAB327C7D756FFE3A,
	TileChangeData_get_transform_mC78733C53E477C8384205388597142A559A68A51,
	TileChangeData_set_transform_mBA8AE961820A2BD978AA2AD424B0AA78D5C4DAF4,
	TileChangeData__ctor_m1B47E18D78AB675EB05E748F419DD82D8D33D9AC,
	TileAnimationData_get_animatedSprites_mCC6863CFDF838099D42DA46F66D2A9E5224AB0D9,
	TileAnimationData_set_animatedSprites_m315FE8DAB5071E1FA594AEA74B1B66BBF6A5C3E1,
	TileAnimationData_get_animationSpeed_m7CCE976FA4FEEF7A55669ABB426EF94DAE97F032,
	TileAnimationData_set_animationSpeed_mE1DB382A9D7F0385D70248A93B998405890D4611,
	TileAnimationData_get_animationStartTime_mCA202FBC945C9514558C4618CB5A3D209A03D8D5,
	TileAnimationData_set_animationStartTime_mBC2F61289403253C6B43C12576A98654B94A9B40,
	TileAnimationData_get_flags_mF2A55E6A4868FAC4FC1BF9BAE41BA3D0CCCE2E15,
	TileAnimationData_set_flags_m5C1157264844D313EB81E33FFD5CB7EAE165603D,
	TilemapCollider2D_get_useDelaunayMesh_mE905CD68FE2C597CFC4605C0C6D0918AA7E93CD4,
	TilemapCollider2D_set_useDelaunayMesh_mC37750FD364653C16003BC276AF15C80F895E541,
	TilemapCollider2D_get_maximumTileChangeCount_mCA6E3766B62AA1E1A04755EE2DE94FE141FD28D0,
	TilemapCollider2D_set_maximumTileChangeCount_m97463BA84F6A1155859E06E0D824A6DE853A47D8,
	TilemapCollider2D_get_extrusionFactor_m0AD1E008157C9D7A9C1989F338E9FA39B73C2C51,
	TilemapCollider2D_set_extrusionFactor_m6CCB29ECA52A31308C64738C3EF7F499E71DBF64,
	TilemapCollider2D_get_hasTilemapChanges_mFD0B7E0F5FCB08030BB277A2896DE4135FBC20CC,
	TilemapCollider2D_ProcessTilemapChanges_mE4477B0659CF2244978A617CAEE5AFDCB1FCE6A5,
	TilemapCollider2D__ctor_m1DC2BBFC493CDF5FC7D2640765EF75F261DA824B,
};
extern void SyncTile_get_position_mD695EB42E035B67BB880253B3AFD8CF22E7DB681_AdjustorThunk (void);
extern void SyncTile_get_tile_m9E09DFD87E065D24135B377FC5BC8A4FE60C2ECA_AdjustorThunk (void);
extern void SyncTile_get_tileData_mAB00B5C96FCB905D19D03018FFD057D3A1C82FE8_AdjustorThunk (void);
extern void TileData_get_sprite_m8117B1A4F4A5722F067271A0B9BEB5C1806F90D1_AdjustorThunk (void);
extern void TileData_set_sprite_m3566544847F9C9C27EDB154324B6FBDB446EFE94_AdjustorThunk (void);
extern void TileData_get_color_m4295C0A6B7B4C64346C35CEAC67288D555864C9C_AdjustorThunk (void);
extern void TileData_set_color_m5E759823878243A226EF46419FAD7C0CC3D5F40A_AdjustorThunk (void);
extern void TileData_get_transform_mFE61A8584117E278A08845ED7933EBA8A6A4D1AD_AdjustorThunk (void);
extern void TileData_set_transform_m71074A780C066292F940002A7165658E9CC01F9F_AdjustorThunk (void);
extern void TileData_get_gameObject_m5CD61F71B1A0AC65B8B29A17C1A8D02374312FBC_AdjustorThunk (void);
extern void TileData_set_gameObject_m1CE5B2AAAB5BF5AEF36EBAF2BCE23E4D2E5A9E09_AdjustorThunk (void);
extern void TileData_get_flags_m4837A688D8CB34334487838696FB191BDB9FB3E2_AdjustorThunk (void);
extern void TileData_set_flags_mEB46B1364D6DB7F77C2E1E43AFD31381B291BD30_AdjustorThunk (void);
extern void TileData_get_colliderType_mE24736B2027681E98CE454717A92833CE7E64D55_AdjustorThunk (void);
extern void TileData_set_colliderType_mE12359ADEF5F42CC0B635DCBAEC3035F0526FA96_AdjustorThunk (void);
extern void TileDataNative_get_sprite_mB81AF44089B465F1039108B1EB1166AF3B1B1C42_AdjustorThunk (void);
extern void TileDataNative_set_sprite_mF852740033301EB69E09A592553B1B42EF9E7D9A_AdjustorThunk (void);
extern void TileDataNative_get_color_mD24D7A62139693695B999760EE15CA47AF08F524_AdjustorThunk (void);
extern void TileDataNative_set_color_m4D6BCD85B0CBC9F8CC54D93E00AE7D84FAC87315_AdjustorThunk (void);
extern void TileDataNative_get_transform_mE8EF348FEAFED32B0AC6259A49563819D47080C1_AdjustorThunk (void);
extern void TileDataNative_set_transform_mE179C2A14C2C91D23CF0DC44290E5A5EE1F4D7F5_AdjustorThunk (void);
extern void TileDataNative_get_gameObject_m6EE8C68E57D7B9125D34AC43FCFEB30D371A6574_AdjustorThunk (void);
extern void TileDataNative_set_gameObject_mC346973665AE04A07846E58EF60E6A1162250F2C_AdjustorThunk (void);
extern void TileDataNative_get_flags_m1C4548D1BE2E0BB0272F7A7DED1FED103B023CB9_AdjustorThunk (void);
extern void TileDataNative_set_flags_mFEE1AB34AE117E2AEF5EF20730D6631F9BE21F97_AdjustorThunk (void);
extern void TileDataNative_get_colliderType_mB91037F7EAEA9CAED95A7BE3781FE7F47003990B_AdjustorThunk (void);
extern void TileDataNative_set_colliderType_mA530C61F19B85103CFB10445D7AC61785671B018_AdjustorThunk (void);
extern void TileChangeData_get_position_m2CEFE44B31F602A99450595D536D7E35F5AD487F_AdjustorThunk (void);
extern void TileChangeData_set_position_mAEFB28585EAFABCA05D027118B5B05ED9B71DF1D_AdjustorThunk (void);
extern void TileChangeData_get_tile_mDBF8C089B3AE67A312C0CF511FCFA9BA02B55D6D_AdjustorThunk (void);
extern void TileChangeData_set_tile_m452C30A40E0EB1C08ADC8E0121F7BE041B7563C3_AdjustorThunk (void);
extern void TileChangeData_get_color_mC8642D45A1DA2588EC9F97EFB8543C4BF44B4868_AdjustorThunk (void);
extern void TileChangeData_set_color_mBBF03E52A23B8726A0BC450FAB327C7D756FFE3A_AdjustorThunk (void);
extern void TileChangeData_get_transform_mC78733C53E477C8384205388597142A559A68A51_AdjustorThunk (void);
extern void TileChangeData_set_transform_mBA8AE961820A2BD978AA2AD424B0AA78D5C4DAF4_AdjustorThunk (void);
extern void TileChangeData__ctor_m1B47E18D78AB675EB05E748F419DD82D8D33D9AC_AdjustorThunk (void);
extern void TileAnimationData_get_animatedSprites_mCC6863CFDF838099D42DA46F66D2A9E5224AB0D9_AdjustorThunk (void);
extern void TileAnimationData_set_animatedSprites_m315FE8DAB5071E1FA594AEA74B1B66BBF6A5C3E1_AdjustorThunk (void);
extern void TileAnimationData_get_animationSpeed_m7CCE976FA4FEEF7A55669ABB426EF94DAE97F032_AdjustorThunk (void);
extern void TileAnimationData_set_animationSpeed_mE1DB382A9D7F0385D70248A93B998405890D4611_AdjustorThunk (void);
extern void TileAnimationData_get_animationStartTime_mCA202FBC945C9514558C4618CB5A3D209A03D8D5_AdjustorThunk (void);
extern void TileAnimationData_set_animationStartTime_mBC2F61289403253C6B43C12576A98654B94A9B40_AdjustorThunk (void);
extern void TileAnimationData_get_flags_mF2A55E6A4868FAC4FC1BF9BAE41BA3D0CCCE2E15_AdjustorThunk (void);
extern void TileAnimationData_set_flags_m5C1157264844D313EB81E33FFD5CB7EAE165603D_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[44] = 
{
	{ 0x060000D7, SyncTile_get_position_mD695EB42E035B67BB880253B3AFD8CF22E7DB681_AdjustorThunk },
	{ 0x060000D8, SyncTile_get_tile_m9E09DFD87E065D24135B377FC5BC8A4FE60C2ECA_AdjustorThunk },
	{ 0x060000D9, SyncTile_get_tileData_mAB00B5C96FCB905D19D03018FFD057D3A1C82FE8_AdjustorThunk },
	{ 0x060000F2, TileData_get_sprite_m8117B1A4F4A5722F067271A0B9BEB5C1806F90D1_AdjustorThunk },
	{ 0x060000F3, TileData_set_sprite_m3566544847F9C9C27EDB154324B6FBDB446EFE94_AdjustorThunk },
	{ 0x060000F4, TileData_get_color_m4295C0A6B7B4C64346C35CEAC67288D555864C9C_AdjustorThunk },
	{ 0x060000F5, TileData_set_color_m5E759823878243A226EF46419FAD7C0CC3D5F40A_AdjustorThunk },
	{ 0x060000F6, TileData_get_transform_mFE61A8584117E278A08845ED7933EBA8A6A4D1AD_AdjustorThunk },
	{ 0x060000F7, TileData_set_transform_m71074A780C066292F940002A7165658E9CC01F9F_AdjustorThunk },
	{ 0x060000F8, TileData_get_gameObject_m5CD61F71B1A0AC65B8B29A17C1A8D02374312FBC_AdjustorThunk },
	{ 0x060000F9, TileData_set_gameObject_m1CE5B2AAAB5BF5AEF36EBAF2BCE23E4D2E5A9E09_AdjustorThunk },
	{ 0x060000FA, TileData_get_flags_m4837A688D8CB34334487838696FB191BDB9FB3E2_AdjustorThunk },
	{ 0x060000FB, TileData_set_flags_mEB46B1364D6DB7F77C2E1E43AFD31381B291BD30_AdjustorThunk },
	{ 0x060000FC, TileData_get_colliderType_mE24736B2027681E98CE454717A92833CE7E64D55_AdjustorThunk },
	{ 0x060000FD, TileData_set_colliderType_mE12359ADEF5F42CC0B635DCBAEC3035F0526FA96_AdjustorThunk },
	{ 0x06000100, TileDataNative_get_sprite_mB81AF44089B465F1039108B1EB1166AF3B1B1C42_AdjustorThunk },
	{ 0x06000101, TileDataNative_set_sprite_mF852740033301EB69E09A592553B1B42EF9E7D9A_AdjustorThunk },
	{ 0x06000102, TileDataNative_get_color_mD24D7A62139693695B999760EE15CA47AF08F524_AdjustorThunk },
	{ 0x06000103, TileDataNative_set_color_m4D6BCD85B0CBC9F8CC54D93E00AE7D84FAC87315_AdjustorThunk },
	{ 0x06000104, TileDataNative_get_transform_mE8EF348FEAFED32B0AC6259A49563819D47080C1_AdjustorThunk },
	{ 0x06000105, TileDataNative_set_transform_mE179C2A14C2C91D23CF0DC44290E5A5EE1F4D7F5_AdjustorThunk },
	{ 0x06000106, TileDataNative_get_gameObject_m6EE8C68E57D7B9125D34AC43FCFEB30D371A6574_AdjustorThunk },
	{ 0x06000107, TileDataNative_set_gameObject_mC346973665AE04A07846E58EF60E6A1162250F2C_AdjustorThunk },
	{ 0x06000108, TileDataNative_get_flags_m1C4548D1BE2E0BB0272F7A7DED1FED103B023CB9_AdjustorThunk },
	{ 0x06000109, TileDataNative_set_flags_mFEE1AB34AE117E2AEF5EF20730D6631F9BE21F97_AdjustorThunk },
	{ 0x0600010A, TileDataNative_get_colliderType_mB91037F7EAEA9CAED95A7BE3781FE7F47003990B_AdjustorThunk },
	{ 0x0600010B, TileDataNative_set_colliderType_mA530C61F19B85103CFB10445D7AC61785671B018_AdjustorThunk },
	{ 0x0600010D, TileChangeData_get_position_m2CEFE44B31F602A99450595D536D7E35F5AD487F_AdjustorThunk },
	{ 0x0600010E, TileChangeData_set_position_mAEFB28585EAFABCA05D027118B5B05ED9B71DF1D_AdjustorThunk },
	{ 0x0600010F, TileChangeData_get_tile_mDBF8C089B3AE67A312C0CF511FCFA9BA02B55D6D_AdjustorThunk },
	{ 0x06000110, TileChangeData_set_tile_m452C30A40E0EB1C08ADC8E0121F7BE041B7563C3_AdjustorThunk },
	{ 0x06000111, TileChangeData_get_color_mC8642D45A1DA2588EC9F97EFB8543C4BF44B4868_AdjustorThunk },
	{ 0x06000112, TileChangeData_set_color_mBBF03E52A23B8726A0BC450FAB327C7D756FFE3A_AdjustorThunk },
	{ 0x06000113, TileChangeData_get_transform_mC78733C53E477C8384205388597142A559A68A51_AdjustorThunk },
	{ 0x06000114, TileChangeData_set_transform_mBA8AE961820A2BD978AA2AD424B0AA78D5C4DAF4_AdjustorThunk },
	{ 0x06000115, TileChangeData__ctor_m1B47E18D78AB675EB05E748F419DD82D8D33D9AC_AdjustorThunk },
	{ 0x06000116, TileAnimationData_get_animatedSprites_mCC6863CFDF838099D42DA46F66D2A9E5224AB0D9_AdjustorThunk },
	{ 0x06000117, TileAnimationData_set_animatedSprites_m315FE8DAB5071E1FA594AEA74B1B66BBF6A5C3E1_AdjustorThunk },
	{ 0x06000118, TileAnimationData_get_animationSpeed_m7CCE976FA4FEEF7A55669ABB426EF94DAE97F032_AdjustorThunk },
	{ 0x06000119, TileAnimationData_set_animationSpeed_mE1DB382A9D7F0385D70248A93B998405890D4611_AdjustorThunk },
	{ 0x0600011A, TileAnimationData_get_animationStartTime_mCA202FBC945C9514558C4618CB5A3D209A03D8D5_AdjustorThunk },
	{ 0x0600011B, TileAnimationData_set_animationStartTime_mBC2F61289403253C6B43C12576A98654B94A9B40_AdjustorThunk },
	{ 0x0600011C, TileAnimationData_get_flags_mF2A55E6A4868FAC4FC1BF9BAE41BA3D0CCCE2E15_AdjustorThunk },
	{ 0x0600011D, TileAnimationData_set_flags_m5C1157264844D313EB81E33FFD5CB7EAE165603D_AdjustorThunk },
};
static const int32_t s_InvokerIndices[294] = 
{
	4168,
	4168,
	4168,
	4250,
	4364,
	1307,
	2094,
	2094,
	2076,
	2076,
	2076,
	2094,
	2734,
	2734,
	1434,
	1433,
	2076,
	2076,
	3852,
	4364,
	4364,
	4364,
	3881,
	8505,
	3881,
	4359,
	4359,
	4166,
	4167,
	3531,
	3317,
	3487,
	3454,
	3531,
	0,
	3981,
	0,
	9031,
	5550,
	5550,
	4250,
	3881,
	4172,
	3811,
	4240,
	3875,
	4250,
	3881,
	4216,
	3852,
	4216,
	3852,
	2197,
	4364,
	2899,
	2197,
	2588,
	1660,
	2587,
	1548,
	1661,
	1550,
	4364,
	8887,
	8887,
	8887,
	8887,
	4168,
	3807,
	8993,
	8993,
	3881,
	2736,
	3881,
	3705,
	8887,
	8887,
	4250,
	3671,
	3671,
	4167,
	4166,
	4166,
	4298,
	3928,
	4172,
	3811,
	4359,
	3981,
	4359,
	3981,
	4358,
	3980,
	4216,
	3852,
	4240,
	3875,
	3531,
	3531,
	0,
	2550,
	3502,
	1743,
	2415,
	2457,
	1150,
	1150,
	2899,
	2899,
	2802,
	2802,
	2198,
	2686,
	2859,
	2788,
	3280,
	3981,
	2659,
	4364,
	2802,
	2802,
	3185,
	3185,
	4216,
	4216,
	3419,
	3419,
	3419,
	3419,
	3531,
	3487,
	2898,
	3317,
	2896,
	3454,
	2897,
	2897,
	2897,
	3531,
	3531,
	2897,
	3454,
	3454,
	3454,
	2897,
	3591,
	2900,
	3454,
	2897,
	2897,
	2897,
	2899,
	2899,
	678,
	678,
	2901,
	1547,
	2901,
	1547,
	4364,
	4364,
	4364,
	3788,
	4364,
	3881,
	2736,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3498,
	2486,
	1687,
	2412,
	1106,
	2661,
	1907,
	2658,
	3788,
	3498,
	2657,
	2657,
	2657,
	2657,
	3397,
	2659,
	2659,
	2659,
	3498,
	3498,
	2659,
	3397,
	3397,
	3397,
	2659,
	3575,
	2663,
	3397,
	2659,
	2659,
	2659,
	2661,
	526,
	1292,
	1292,
	4359,
	4250,
	4342,
	4359,
	3981,
	4358,
	3980,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4364,
	4364,
	3881,
	4364,
	3788,
	3788,
	3788,
	3788,
	4250,
	3881,
	4172,
	3811,
	4240,
	3875,
	4250,
	3881,
	4216,
	3852,
	4216,
	3852,
	9074,
	9089,
	4216,
	3852,
	4172,
	3811,
	4240,
	3875,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	8753,
	4359,
	3981,
	4250,
	3881,
	4172,
	3811,
	4240,
	3875,
	1549,
	4250,
	3881,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4168,
	3807,
	4350,
	3973,
	4298,
	3928,
	4168,
	4364,
	4364,
};
static const Il2CppTokenRangePair s_rgctxIndices[3] = 
{
	{ 0x06000023, { 0, 2 } },
	{ 0x06000025, { 2, 3 } },
	{ 0x06000064, { 5, 1 } },
};
extern const uint32_t g_rgctx_Tilemap_GetTile_TisT_t0506F25F5CF6E4A27D76F5D7AE2FC9C7534D2285_m22851E543B07DA0FCA6C3C67D73C9F84217D4942;
extern const uint32_t g_rgctx_T_t0506F25F5CF6E4A27D76F5D7AE2FC9C7534D2285;
extern const uint32_t g_rgctx_T_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831;
extern const uint32_t g_rgctx_T_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831;
extern const uint32_t g_rgctx_Component_GetComponent_TisT_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831_m69FA7CD01EDFF57DFB8E90A49F6A1C8CBF91C628;
extern const uint32_t g_rgctx_T_tD0EF40EFCFB442BE437235000AA84288524D1092;
static const Il2CppRGCTXDefinition s_rgctxValues[6] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tilemap_GetTile_TisT_t0506F25F5CF6E4A27D76F5D7AE2FC9C7534D2285_m22851E543B07DA0FCA6C3C67D73C9F84217D4942 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0506F25F5CF6E4A27D76F5D7AE2FC9C7534D2285 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisT_t5861EDE6EE239F7CB8B7041B5D5F5178632F4831_m69FA7CD01EDFF57DFB8E90A49F6A1C8CBF91C628 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD0EF40EFCFB442BE437235000AA84288524D1092 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_TilemapModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TilemapModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TilemapModule_CodeGenModule = 
{
	"UnityEngine.TilemapModule.dll",
	294,
	s_methodPointers,
	44,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	3,
	s_rgctxIndices,
	6,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_TilemapModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
