﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C (void);
extern void PlayableDirector_set_extrapolationMode_m4F34CE7E1527D8F2A0BC1E5D003C78A4604FADB7 (void);
extern void PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD (void);
extern void PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C (void);
extern void PlayableDirector_set_playableAsset_mC8E8E91BAD45035A183E0C8E920626B437D6263B (void);
extern void PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F (void);
extern void PlayableDirector_get_playOnAwake_m2A99756D8D17B8FBA329BABF6B641D473DBD90E2 (void);
extern void PlayableDirector_set_playOnAwake_mE95232C47209C2F9E414D561C58D53418B8E3A8F (void);
extern void PlayableDirector_DeferredEvaluate_mB87912913EA74033DF28BD325E55F3D1E171C23C (void);
extern void PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B (void);
extern void PlayableDirector_Play_m8C2D0E5C525817068EFA757221AFBEA4B4678F27 (void);
extern void PlayableDirector_Play_m35ABD2C6E99A600DBAA6E2A8FC4EA47C74F8F452 (void);
extern void PlayableDirector_SetGenericBinding_m6A14E1731C8CB0198777B8063DC278CE1166D244 (void);
extern void PlayableDirector_set_timeUpdateMode_m31564B33CFAB9E9BBF7964970E1990F491272E17 (void);
extern void PlayableDirector_get_timeUpdateMode_m0E3162CC808AED8F717496C53F92C61AFF87FB80 (void);
extern void PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1 (void);
extern void PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357 (void);
extern void PlayableDirector_set_initialTime_mB36EFEA98AC9F650CD92A73B4B717B44E0A9CD02 (void);
extern void PlayableDirector_get_initialTime_mDB1A2FEE06944870DCE1A71AF6A84844E9B694BF (void);
extern void PlayableDirector_get_duration_mEA5C8076E9806A26B9E9075D07485CBF7046E1F6 (void);
extern void PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842 (void);
extern void PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5 (void);
extern void PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988 (void);
extern void PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E (void);
extern void PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104 (void);
extern void PlayableDirector_Resume_m8D0E95B7A33EEA6F5A7CEF05FE5D6A6B7E3AD178 (void);
extern void PlayableDirector_RebuildGraph_m344F4BA72962159E6F76F4B4C2E203BEB802D0F5 (void);
extern void PlayableDirector_ClearReferenceValue_m011DC51A81993B00D95ACC74FD90291363AB534C (void);
extern void PlayableDirector_SetReferenceValue_m2CD757BCF2F23538F99A0DE09AC87A03EC90EADE (void);
extern void PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8 (void);
extern void PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020 (void);
extern void PlayableDirector_ClearGenericBinding_mB68594465FC0DA4DA847830887157C1BE6D366E1 (void);
extern void PlayableDirector_RebindPlayableGraphOutputs_mEDA5855AED2223620A84E0DB99186F4953874B2A (void);
extern void PlayableDirector_ProcessPendingGraphChanges_m97DFC4879F7CC9B7B55B242494D62DDF843844D5 (void);
extern void PlayableDirector_HasGenericBinding_m94B154CC81AFA99A98383CA1ED7D56AB46E463DB (void);
extern void PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE (void);
extern void PlayableDirector_SetWrapMode_mDA42ABB7479C351AA377797F29DCEA54684264DB (void);
extern void PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A (void);
extern void PlayableDirector_EvaluateNextFrame_m07FBD909431C2A913B46581622A247A469B3FBEC (void);
extern void PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896 (void);
extern void PlayableDirector_SetPlayOnAwake_m4ABF4DB3685AB38CBBC856FD52D8C1BD3D89D755 (void);
extern void PlayableDirector_GetPlayOnAwake_mDF2FAEAD040E29871245FB5AD6A9AA525B5B64C3 (void);
extern void PlayableDirector_Internal_SetGenericBinding_m5889234A1C5222C3ACFA329202AD6D58B3C4AFC5 (void);
extern void PlayableDirector_SetPlayableAsset_mCB30A400861B0D554BF6B397A2469B4C15AAE01D (void);
extern void PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24 (void);
extern void PlayableDirector_add_played_m8A41D810B43EA46886904460F74EFD184AAE8604 (void);
extern void PlayableDirector_remove_played_mEDED15F8A15D2D32F464D1B9C07AF51451BF4374 (void);
extern void PlayableDirector_add_paused_m23F6F60C960AAA38C9C73CC75265D3E3FF0105DB (void);
extern void PlayableDirector_remove_paused_m5AC5D63CBF564106D6A4EA018CC5D851C6F5AF31 (void);
extern void PlayableDirector_add_stopped_m8162B112FF06D90F144C49E6C990F705DFC28C96 (void);
extern void PlayableDirector_remove_stopped_m615BBD51C1CA79B01469504CD9403C84938C535F (void);
extern void PlayableDirector_ResetFrameTiming_m6842816BD7123E2F0AD11D198DA684199BB03206 (void);
extern void PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0 (void);
extern void PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1 (void);
extern void PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7 (void);
extern void PlayableDirector__ctor_mAC3BE2F955641A2D384EAE3235FFBF8ECFD27E3B (void);
extern void PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F (void);
extern void PlayableDirector_ClearReferenceValue_Injected_m9955C0A6558AA830976F3072700092D374A79A8B (void);
extern void PlayableDirector_SetReferenceValue_Injected_m62E8502787C5A05B1361424D93C3AE7DB9646C43 (void);
extern void PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106 (void);
extern void PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC (void);
static Il2CppMethodPointer s_methodPointers[61] = 
{
	PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C,
	PlayableDirector_set_extrapolationMode_m4F34CE7E1527D8F2A0BC1E5D003C78A4604FADB7,
	PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD,
	PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C,
	PlayableDirector_set_playableAsset_mC8E8E91BAD45035A183E0C8E920626B437D6263B,
	PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F,
	PlayableDirector_get_playOnAwake_m2A99756D8D17B8FBA329BABF6B641D473DBD90E2,
	PlayableDirector_set_playOnAwake_mE95232C47209C2F9E414D561C58D53418B8E3A8F,
	PlayableDirector_DeferredEvaluate_mB87912913EA74033DF28BD325E55F3D1E171C23C,
	PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B,
	PlayableDirector_Play_m8C2D0E5C525817068EFA757221AFBEA4B4678F27,
	PlayableDirector_Play_m35ABD2C6E99A600DBAA6E2A8FC4EA47C74F8F452,
	PlayableDirector_SetGenericBinding_m6A14E1731C8CB0198777B8063DC278CE1166D244,
	PlayableDirector_set_timeUpdateMode_m31564B33CFAB9E9BBF7964970E1990F491272E17,
	PlayableDirector_get_timeUpdateMode_m0E3162CC808AED8F717496C53F92C61AFF87FB80,
	PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1,
	PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357,
	PlayableDirector_set_initialTime_mB36EFEA98AC9F650CD92A73B4B717B44E0A9CD02,
	PlayableDirector_get_initialTime_mDB1A2FEE06944870DCE1A71AF6A84844E9B694BF,
	PlayableDirector_get_duration_mEA5C8076E9806A26B9E9075D07485CBF7046E1F6,
	PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842,
	PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5,
	PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988,
	PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E,
	PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104,
	PlayableDirector_Resume_m8D0E95B7A33EEA6F5A7CEF05FE5D6A6B7E3AD178,
	PlayableDirector_RebuildGraph_m344F4BA72962159E6F76F4B4C2E203BEB802D0F5,
	PlayableDirector_ClearReferenceValue_m011DC51A81993B00D95ACC74FD90291363AB534C,
	PlayableDirector_SetReferenceValue_m2CD757BCF2F23538F99A0DE09AC87A03EC90EADE,
	PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8,
	PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020,
	PlayableDirector_ClearGenericBinding_mB68594465FC0DA4DA847830887157C1BE6D366E1,
	PlayableDirector_RebindPlayableGraphOutputs_mEDA5855AED2223620A84E0DB99186F4953874B2A,
	PlayableDirector_ProcessPendingGraphChanges_m97DFC4879F7CC9B7B55B242494D62DDF843844D5,
	PlayableDirector_HasGenericBinding_m94B154CC81AFA99A98383CA1ED7D56AB46E463DB,
	PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE,
	PlayableDirector_SetWrapMode_mDA42ABB7479C351AA377797F29DCEA54684264DB,
	PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A,
	PlayableDirector_EvaluateNextFrame_m07FBD909431C2A913B46581622A247A469B3FBEC,
	PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896,
	PlayableDirector_SetPlayOnAwake_m4ABF4DB3685AB38CBBC856FD52D8C1BD3D89D755,
	PlayableDirector_GetPlayOnAwake_mDF2FAEAD040E29871245FB5AD6A9AA525B5B64C3,
	PlayableDirector_Internal_SetGenericBinding_m5889234A1C5222C3ACFA329202AD6D58B3C4AFC5,
	PlayableDirector_SetPlayableAsset_mCB30A400861B0D554BF6B397A2469B4C15AAE01D,
	PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24,
	PlayableDirector_add_played_m8A41D810B43EA46886904460F74EFD184AAE8604,
	PlayableDirector_remove_played_mEDED15F8A15D2D32F464D1B9C07AF51451BF4374,
	PlayableDirector_add_paused_m23F6F60C960AAA38C9C73CC75265D3E3FF0105DB,
	PlayableDirector_remove_paused_m5AC5D63CBF564106D6A4EA018CC5D851C6F5AF31,
	PlayableDirector_add_stopped_m8162B112FF06D90F144C49E6C990F705DFC28C96,
	PlayableDirector_remove_stopped_m615BBD51C1CA79B01469504CD9403C84938C535F,
	PlayableDirector_ResetFrameTiming_m6842816BD7123E2F0AD11D198DA684199BB03206,
	PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0,
	PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1,
	PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7,
	PlayableDirector__ctor_mAC3BE2F955641A2D384EAE3235FFBF8ECFD27E3B,
	PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F,
	PlayableDirector_ClearReferenceValue_Injected_m9955C0A6558AA830976F3072700092D374A79A8B,
	PlayableDirector_SetReferenceValue_Injected_m62E8502787C5A05B1361424D93C3AE7DB9646C43,
	PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106,
	PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC,
};
static const int32_t s_InvokerIndices[61] = 
{
	4216,
	3852,
	4216,
	4250,
	3881,
	4258,
	4168,
	3807,
	4364,
	3834,
	3881,
	2796,
	2802,
	3852,
	4216,
	3826,
	4190,
	3826,
	4190,
	4190,
	4364,
	3834,
	4364,
	4364,
	4364,
	4364,
	4364,
	3894,
	2828,
	2528,
	3518,
	3881,
	4364,
	4364,
	3185,
	4216,
	3852,
	4216,
	4364,
	4258,
	3807,
	4168,
	2802,
	3881,
	4250,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	9089,
	4364,
	4364,
	4364,
	4364,
	3788,
	3788,
	2661,
	2486,
	3788,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_DirectorModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_DirectorModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_DirectorModule_CodeGenModule = 
{
	"UnityEngine.DirectorModule.dll",
	61,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_DirectorModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
