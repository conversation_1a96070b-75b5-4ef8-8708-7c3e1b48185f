﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[45] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_VRModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_VRModule[45] = 
{
	{ 109476, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109476, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109476, 1, 45, 45, 95, 96, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109476, 1, 45, 45, 96, 97, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109477, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 4 },
	{ 109477, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 5 },
	{ 109477, 1, 47, 47, 50, 51, 0, kSequencePointKind_Normal, 0, 6 },
	{ 109477, 1, 47, 47, 51, 52, 1, kSequencePointKind_Normal, 0, 7 },
	{ 109492, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 8 },
	{ 109492, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 9 },
	{ 109492, 1, 111, 111, 13, 14, 0, kSequencePointKind_Normal, 0, 10 },
	{ 109492, 1, 112, 112, 17, 52, 1, kSequencePointKind_Normal, 0, 11 },
	{ 109492, 1, 112, 112, 17, 52, 1, kSequencePointKind_StepOut, 0, 12 },
	{ 109492, 1, 113, 113, 13, 14, 9, kSequencePointKind_Normal, 0, 13 },
	{ 109493, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 14 },
	{ 109493, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 15 },
	{ 109493, 1, 115, 115, 13, 14, 0, kSequencePointKind_Normal, 0, 16 },
	{ 109493, 1, 116, 116, 17, 50, 1, kSequencePointKind_Normal, 0, 17 },
	{ 109493, 1, 116, 116, 0, 0, 21, kSequencePointKind_Normal, 0, 18 },
	{ 109493, 1, 117, 117, 21, 120, 24, kSequencePointKind_Normal, 0, 19 },
	{ 109493, 1, 117, 117, 21, 120, 34, kSequencePointKind_StepOut, 0, 20 },
	{ 109493, 1, 118, 118, 17, 53, 40, kSequencePointKind_Normal, 0, 21 },
	{ 109493, 1, 118, 118, 17, 53, 41, kSequencePointKind_StepOut, 0, 22 },
	{ 109493, 1, 119, 119, 13, 14, 47, kSequencePointKind_Normal, 0, 23 },
	{ 109501, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 24 },
	{ 109501, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 25 },
	{ 109501, 1, 138, 138, 9, 10, 0, kSequencePointKind_Normal, 0, 26 },
	{ 109501, 1, 139, 139, 13, 59, 1, kSequencePointKind_Normal, 0, 27 },
	{ 109501, 1, 139, 139, 13, 59, 11, kSequencePointKind_StepOut, 0, 28 },
	{ 109501, 1, 140, 140, 9, 10, 17, kSequencePointKind_Normal, 0, 29 },
	{ 109506, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 30 },
	{ 109506, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 31 },
	{ 109506, 1, 170, 170, 44, 45, 0, kSequencePointKind_Normal, 0, 32 },
	{ 109506, 1, 170, 170, 45, 181, 1, kSequencePointKind_Normal, 0, 33 },
	{ 109506, 1, 170, 170, 45, 181, 6, kSequencePointKind_StepOut, 0, 34 },
	{ 109517, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 35 },
	{ 109517, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 36 },
	{ 109517, 1, 209, 209, 9, 10, 0, kSequencePointKind_Normal, 0, 37 },
	{ 109517, 1, 210, 210, 13, 38, 1, kSequencePointKind_Normal, 0, 38 },
	{ 109517, 1, 210, 210, 0, 0, 10, kSequencePointKind_Normal, 0, 39 },
	{ 109517, 1, 211, 211, 13, 14, 13, kSequencePointKind_Normal, 0, 40 },
	{ 109517, 1, 212, 212, 17, 48, 14, kSequencePointKind_Normal, 0, 41 },
	{ 109517, 1, 212, 212, 17, 48, 20, kSequencePointKind_StepOut, 0, 42 },
	{ 109517, 1, 213, 213, 13, 14, 26, kSequencePointKind_Normal, 0, 43 },
	{ 109517, 1, 214, 214, 9, 10, 27, kSequencePointKind_Normal, 0, 44 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_VRModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_VRModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/VR/ScriptBindings/XR.bindings.cs", { 235, 229, 26, 237, 96, 202, 30, 137, 61, 24, 103, 210, 120, 86, 189, 121} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[3] = 
{
	{ 14073, 1 },
	{ 14076, 1 },
	{ 14078, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[3] = 
{
	{ 0, 11 },
	{ 0, 48 },
	{ 0, 28 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[45] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 11, 0, 1 },
	{ 48, 1, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 28, 2, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_VRModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_VRModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	45,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_VRModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	3,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
