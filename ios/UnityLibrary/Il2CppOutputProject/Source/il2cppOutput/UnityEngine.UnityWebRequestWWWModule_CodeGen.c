﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void WWW_EscapeURL_m2FC43D0805D57D161D661839C17E147B9EBE31E5 (void);
extern void WWW_EscapeURL_m072297470E428928626550ACC130A3CEEC9ACC2B (void);
extern void WWW_UnEscapeURL_m6D51170631248E58948E0221C6A0E99CB46C7EFE (void);
extern void WWW_UnEscapeURL_mBE1A51E8AD62C22B843A9D97558B7542FD0A35E2 (void);
extern void WWW_LoadFromCacheOrDownload_mE75A94DFDCDD17412FDB89CB60498BF4405F64F1 (void);
extern void WWW_LoadFromCacheOrDownload_mB384AAC7E38EB3D152BAA71749DE422A7B4117A1 (void);
extern void WWW_LoadFromCacheOrDownload_m4D1C62B3DE6C5F0E9F4DEC9EDE719B7658011920 (void);
extern void WWW_LoadFromCacheOrDownload_mFD27E3686C2415421CF328AE75017D39FF7A792F (void);
extern void WWW_LoadFromCacheOrDownload_m8F1C9DB4BBE821128B59733BEAAC5306C48A69BD (void);
extern void WWW__ctor_m5D29D83E9EE0925ED8252347CE24EC236401503D (void);
extern void WWW__ctor_mAA7DCD89AE9B79BD685BA8A1853C42C9A9B0C533 (void);
extern void WWW__ctor_m0CA60044A418EE7CC242A3780D66254353F25629 (void);
extern void WWW__ctor_m5C4B331123CF25248BF866143A94B2275F0E5975 (void);
extern void WWW__ctor_mD51946C4380DAAA935F7C5996E5A6AF135165A7E (void);
extern void WWW__ctor_mE54C20112FA803BB706A405F2892106B2BCB86FD (void);
extern void WWW_get_assetBundle_mB4DC5AFC3732922D2AE792A02699058DABF5B38E (void);
extern void WWW_get_audioClip_m916DAADDEF02A8614F2AC6E9B071028D87FD3307 (void);
extern void WWW_get_bytes_m83F5C24FC5AC80C5F3B9AF1E290E08F8B96C0642 (void);
extern void WWW_get_size_m7EC5D697C80F65ECD128507135323FC099CF22E5 (void);
extern void WWW_get_bytesDownloaded_m6B0FB6A1264515565A9D97B7BFE7FE30773E1681 (void);
extern void WWW_get_error_m6B2E4F6DB8ECC8217A112EC62AAA1D5E71AA1C93 (void);
extern void WWW_get_isDone_m7E88B666AD0E3903757043813B2811BBFCCCA52E (void);
extern void WWW_get_progress_m8BE51921011B9C737C690F8776F93109E5481B47 (void);
extern void WWW_get_responseHeaders_m6C9B2980A00E0EE5F537D5CDD48BF6C42C4CF066 (void);
extern void WWW_get_data_m0AC54B7D1F65ED26D8ED3C57F9FA1147A1604AE1 (void);
extern void WWW_get_text_m95F7CAAC33FD0BAB9B535E3AEAFDA36B81B3EC4F (void);
extern void WWW_CreateTextureFromDownloadedData_m670DE15775E657BC8CE6CD19FE2C29EA3429F8F0 (void);
extern void WWW_get_texture_mB38F7FC4220AC09935423B84FD4EB852CF172AAE (void);
extern void WWW_get_textureNonReadable_m8D15D4BFEF1F5F6FB10319C71387F9A074760FD6 (void);
extern void WWW_LoadImageIntoTexture_m81D37261ED690E2548887532443C2DBEDCBE43D4 (void);
extern void WWW_get_threadPriority_mD9063F88AC6750CE98A82A8B46432870F73A0337 (void);
extern void WWW_set_threadPriority_m07533B2624B8FD97FF6073E9ED5E746C35C16EB1 (void);
extern void WWW_get_uploadProgress_m21E5E22CDA98A2FD19CD7AB65344967661F7C85C (void);
extern void WWW_get_url_m368B1D7D23DC22E412A3F802C6E3047760665519 (void);
extern void WWW_get_keepWaiting_m2D6B60FD9CB9C3E86D69E87EA32953AA68AE26B9 (void);
extern void WWW_Dispose_mE5FC4A2013C63A68C287F139B177D86F75C4A74F (void);
extern void WWW_GetAudioClipInternal_m08B69E33747BD466B99370200B183B6341798974 (void);
extern void WWW_GetAudioClip_m884E6607F0A93B19013DB7AA86DDC1DDC94C4B2F (void);
extern void WWW_GetAudioClip_mEE3280D26D910755A5E3B8E97632FAD22710AEFD (void);
extern void WWW_GetAudioClip_mACAD8F04E72EF939034B1EA9E8BF2AE524C67714 (void);
extern void WWW_GetAudioClip_m38EFF82040D63C9424EC3A4EDA9E562FB0097116 (void);
extern void WWW_GetAudioClipCompressed_m8140DE3B2F1E4D07A77CC8E4F8FB8AA8380F7FE1 (void);
extern void WWW_GetAudioClipCompressed_m4239F0E858DC83D60C7C0067DA330854F013A936 (void);
extern void WWW_GetAudioClipCompressed_mBBDF065BCA711AFC9E59BCBCF51E8C49E791567B (void);
extern void WWW_WaitUntilDoneIfPossible_mD975AFF6737F00BB5003C5AEDBD795751F129A84 (void);
extern void WWWAudioExtensions_GetAudioClip_mEC8CC5FDFBB0AD8256BE358CC1C1B342C967F75C (void);
extern void WWWAudioExtensions_GetAudioClip_m6BBD1691F3AA61C323419CC51C3971CB2A85926F (void);
extern void WWWAudioExtensions_GetAudioClip_mBCC080532F11545DA56B2BB9F0DA595EC53129AE (void);
extern void WWWAudioExtensions_GetAudioClip_m27ABEB2C8F316384CD8AA42420A144D8EFF12E8E (void);
extern void WWWAudioExtensions_GetAudioClipCompressed_m980AB9C32F51FDD9D63F8B1D7511A693F29315CE (void);
extern void WWWAudioExtensions_GetAudioClipCompressed_m56761C01DACB472F16BC001A6546427066B914D8 (void);
extern void WWWAudioExtensions_GetAudioClipCompressed_m8D6E3701347192A5E7DB03EFC39FFA8CD84D11B8 (void);
extern void WebRequestWWW_InternalCreateAudioClipUsingDH_m2FC91348090ABC797376D75D1F69200DE4946EC8 (void);
static Il2CppMethodPointer s_methodPointers[53] = 
{
	WWW_EscapeURL_m2FC43D0805D57D161D661839C17E147B9EBE31E5,
	WWW_EscapeURL_m072297470E428928626550ACC130A3CEEC9ACC2B,
	WWW_UnEscapeURL_m6D51170631248E58948E0221C6A0E99CB46C7EFE,
	WWW_UnEscapeURL_mBE1A51E8AD62C22B843A9D97558B7542FD0A35E2,
	WWW_LoadFromCacheOrDownload_mE75A94DFDCDD17412FDB89CB60498BF4405F64F1,
	WWW_LoadFromCacheOrDownload_mB384AAC7E38EB3D152BAA71749DE422A7B4117A1,
	WWW_LoadFromCacheOrDownload_m4D1C62B3DE6C5F0E9F4DEC9EDE719B7658011920,
	WWW_LoadFromCacheOrDownload_mFD27E3686C2415421CF328AE75017D39FF7A792F,
	WWW_LoadFromCacheOrDownload_m8F1C9DB4BBE821128B59733BEAAC5306C48A69BD,
	WWW__ctor_m5D29D83E9EE0925ED8252347CE24EC236401503D,
	WWW__ctor_mAA7DCD89AE9B79BD685BA8A1853C42C9A9B0C533,
	WWW__ctor_m0CA60044A418EE7CC242A3780D66254353F25629,
	WWW__ctor_m5C4B331123CF25248BF866143A94B2275F0E5975,
	WWW__ctor_mD51946C4380DAAA935F7C5996E5A6AF135165A7E,
	WWW__ctor_mE54C20112FA803BB706A405F2892106B2BCB86FD,
	WWW_get_assetBundle_mB4DC5AFC3732922D2AE792A02699058DABF5B38E,
	WWW_get_audioClip_m916DAADDEF02A8614F2AC6E9B071028D87FD3307,
	WWW_get_bytes_m83F5C24FC5AC80C5F3B9AF1E290E08F8B96C0642,
	WWW_get_size_m7EC5D697C80F65ECD128507135323FC099CF22E5,
	WWW_get_bytesDownloaded_m6B0FB6A1264515565A9D97B7BFE7FE30773E1681,
	WWW_get_error_m6B2E4F6DB8ECC8217A112EC62AAA1D5E71AA1C93,
	WWW_get_isDone_m7E88B666AD0E3903757043813B2811BBFCCCA52E,
	WWW_get_progress_m8BE51921011B9C737C690F8776F93109E5481B47,
	WWW_get_responseHeaders_m6C9B2980A00E0EE5F537D5CDD48BF6C42C4CF066,
	WWW_get_data_m0AC54B7D1F65ED26D8ED3C57F9FA1147A1604AE1,
	WWW_get_text_m95F7CAAC33FD0BAB9B535E3AEAFDA36B81B3EC4F,
	WWW_CreateTextureFromDownloadedData_m670DE15775E657BC8CE6CD19FE2C29EA3429F8F0,
	WWW_get_texture_mB38F7FC4220AC09935423B84FD4EB852CF172AAE,
	WWW_get_textureNonReadable_m8D15D4BFEF1F5F6FB10319C71387F9A074760FD6,
	WWW_LoadImageIntoTexture_m81D37261ED690E2548887532443C2DBEDCBE43D4,
	WWW_get_threadPriority_mD9063F88AC6750CE98A82A8B46432870F73A0337,
	WWW_set_threadPriority_m07533B2624B8FD97FF6073E9ED5E746C35C16EB1,
	WWW_get_uploadProgress_m21E5E22CDA98A2FD19CD7AB65344967661F7C85C,
	WWW_get_url_m368B1D7D23DC22E412A3F802C6E3047760665519,
	WWW_get_keepWaiting_m2D6B60FD9CB9C3E86D69E87EA32953AA68AE26B9,
	WWW_Dispose_mE5FC4A2013C63A68C287F139B177D86F75C4A74F,
	WWW_GetAudioClipInternal_m08B69E33747BD466B99370200B183B6341798974,
	WWW_GetAudioClip_m884E6607F0A93B19013DB7AA86DDC1DDC94C4B2F,
	WWW_GetAudioClip_mEE3280D26D910755A5E3B8E97632FAD22710AEFD,
	WWW_GetAudioClip_mACAD8F04E72EF939034B1EA9E8BF2AE524C67714,
	WWW_GetAudioClip_m38EFF82040D63C9424EC3A4EDA9E562FB0097116,
	WWW_GetAudioClipCompressed_m8140DE3B2F1E4D07A77CC8E4F8FB8AA8380F7FE1,
	WWW_GetAudioClipCompressed_m4239F0E858DC83D60C7C0067DA330854F013A936,
	WWW_GetAudioClipCompressed_mBBDF065BCA711AFC9E59BCBCF51E8C49E791567B,
	WWW_WaitUntilDoneIfPossible_mD975AFF6737F00BB5003C5AEDBD795751F129A84,
	WWWAudioExtensions_GetAudioClip_mEC8CC5FDFBB0AD8256BE358CC1C1B342C967F75C,
	WWWAudioExtensions_GetAudioClip_m6BBD1691F3AA61C323419CC51C3971CB2A85926F,
	WWWAudioExtensions_GetAudioClip_mBCC080532F11545DA56B2BB9F0DA595EC53129AE,
	WWWAudioExtensions_GetAudioClip_m27ABEB2C8F316384CD8AA42420A144D8EFF12E8E,
	WWWAudioExtensions_GetAudioClipCompressed_m980AB9C32F51FDD9D63F8B1D7511A693F29315CE,
	WWWAudioExtensions_GetAudioClipCompressed_m56761C01DACB472F16BC001A6546427066B914D8,
	WWWAudioExtensions_GetAudioClipCompressed_m8D6E3701347192A5E7DB03EFC39FFA8CD84D11B8,
	WebRequestWWW_InternalCreateAudioClipUsingDH_m2FC91348090ABC797376D75D1F69200DE4946EC8,
};
static const int32_t s_InvokerIndices[53] = 
{
	8505,
	7631,
	8505,
	7631,
	7626,
	6688,
	7623,
	6681,
	6676,
	3881,
	2802,
	2802,
	2084,
	2084,
	1444,
	4250,
	4250,
	4250,
	4216,
	4216,
	4250,
	4168,
	4298,
	4250,
	4250,
	4250,
	3503,
	4250,
	4250,
	3881,
	4216,
	3852,
	4298,
	4250,
	4168,
	4364,
	1164,
	4250,
	3503,
	2491,
	1759,
	4250,
	3503,
	2493,
	4168,
	8505,
	7616,
	6673,
	5925,
	8505,
	7616,
	6674,
	5365,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestWWWModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule = 
{
	"UnityEngine.UnityWebRequestWWWModule.dll",
	53,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestWWWModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
