﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void DataContractAttribute__ctor_mAFB75FDCDDBB5925CCE916496408E843E9DDB6BC (void);
extern void DataContractAttribute_get_IsReference_mEC2FFE0351B0DD896E7805670D6A614B1AE4C101 (void);
extern void DataContractAttribute_set_Namespace_m98C63DE53771DF8A1A1FD4C33BC508FE1A5B81D2 (void);
extern void DataMemberAttribute__ctor_m077F867534BAD9865D6DD156A8FBBB4068283A42 (void);
extern void DataMemberAttribute_get_Name_m8C4BF39A517D901076BAFF6FF89DD53C5F4B2B3E (void);
extern void DataMemberAttribute_get_Order_m34D8C756AE07BD345011D887546DF54D71898956 (void);
extern void DataMemberAttribute_set_Order_m666DF5C9737032126F7A589A17320DEA2FF8FECA (void);
extern void DataMemberAttribute_get_IsRequired_m318C586E28E349EA87096FE6FE473B4C4FD58C73 (void);
extern void DataMemberAttribute_get_EmitDefaultValue_m430708B4CFB34DD522B6D01A66CE8FDEDCC088E8 (void);
extern void EnumMemberAttribute_get_Value_mB41126B613B9FD1CD8A05D08FCEC4B6663864BE9 (void);
extern void InvalidDataContractException__ctor_m1B7907967BE71E252FB194B48A5BAC81EBD7A497 (void);
extern void InvalidDataContractException__ctor_m6C50A1259C6FD9C49EA59E0905FBCA103A472559 (void);
extern void InvalidDataContractException__ctor_m4B8827EF5D454CB2E44460D237769C1067C198B8 (void);
extern void DiagnosticUtility__cctor_m5F64B6C8FE8B54849A165BE67252B32CC1ACED1A (void);
extern void ExceptionUtility_ThrowHelperError_mB01429D47ABA81955EC561D8286C63E8C74D5FB6 (void);
extern void ExceptionUtility_ThrowHelper_mB8572AC383576258341DCD063A8B28ECB635F081 (void);
extern void SR_GetString_mBD8F2ECD53B5DF9AADDE7871DB25FCBA4D50025D (void);
static Il2CppMethodPointer s_methodPointers[17] = 
{
	DataContractAttribute__ctor_mAFB75FDCDDBB5925CCE916496408E843E9DDB6BC,
	DataContractAttribute_get_IsReference_mEC2FFE0351B0DD896E7805670D6A614B1AE4C101,
	DataContractAttribute_set_Namespace_m98C63DE53771DF8A1A1FD4C33BC508FE1A5B81D2,
	DataMemberAttribute__ctor_m077F867534BAD9865D6DD156A8FBBB4068283A42,
	DataMemberAttribute_get_Name_m8C4BF39A517D901076BAFF6FF89DD53C5F4B2B3E,
	DataMemberAttribute_get_Order_m34D8C756AE07BD345011D887546DF54D71898956,
	DataMemberAttribute_set_Order_m666DF5C9737032126F7A589A17320DEA2FF8FECA,
	DataMemberAttribute_get_IsRequired_m318C586E28E349EA87096FE6FE473B4C4FD58C73,
	DataMemberAttribute_get_EmitDefaultValue_m430708B4CFB34DD522B6D01A66CE8FDEDCC088E8,
	EnumMemberAttribute_get_Value_mB41126B613B9FD1CD8A05D08FCEC4B6663864BE9,
	InvalidDataContractException__ctor_m1B7907967BE71E252FB194B48A5BAC81EBD7A497,
	InvalidDataContractException__ctor_m6C50A1259C6FD9C49EA59E0905FBCA103A472559,
	InvalidDataContractException__ctor_m4B8827EF5D454CB2E44460D237769C1067C198B8,
	DiagnosticUtility__cctor_m5F64B6C8FE8B54849A165BE67252B32CC1ACED1A,
	ExceptionUtility_ThrowHelperError_mB01429D47ABA81955EC561D8286C63E8C74D5FB6,
	ExceptionUtility_ThrowHelper_mB8572AC383576258341DCD063A8B28ECB635F081,
	SR_GetString_mBD8F2ECD53B5DF9AADDE7871DB25FCBA4D50025D,
};
static const int32_t s_InvokerIndices[17] = 
{
	4364,
	4168,
	3881,
	4364,
	4250,
	4216,
	3852,
	4168,
	4168,
	4250,
	4364,
	3881,
	2811,
	9089,
	8505,
	7626,
	8505,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationSystem_Runtime_Serialization;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Runtime_Serialization_CodeGenModule;
const Il2CppCodeGenModule g_System_Runtime_Serialization_CodeGenModule = 
{
	"System.Runtime.Serialization.dll",
	17,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationSystem_Runtime_Serialization,
	NULL,
	NULL,
	NULL,
	NULL,
};
