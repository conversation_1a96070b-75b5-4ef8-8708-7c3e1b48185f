﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[10] = 
{
	{ 24489, 0,  2 },
	{ 15842, 1,  4 },
	{ 15843, 1,  6 },
	{ 38516, 2,  7 },
	{ 16738, 3,  7 },
	{ 24489, 4,  7 },
	{ 24489, 0,  8 },
	{ 24489, 5,  8 },
	{ 24489, 0,  9 },
	{ 24489, 5,  9 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[6] = 
{
	"i",
	"p",
	"CS$<>8__locals0",
	"acceptableColors",
	"numUniqueColors",
	"count",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[12] = 
{
	{ 0, 0 },
	{ 0, 1 },
	{ 1, 1 },
	{ 2, 1 },
	{ 3, 7 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_AccessibilityModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_AccessibilityModule[160] = 
{
	{ 109994, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109994, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109994, 1, 52, 52, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109994, 1, 53, 53, 13, 34, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109994, 1, 53, 53, 13, 34, 3, kSequencePointKind_StepOut, 0, 4 },
	{ 109994, 1, 54, 54, 13, 104, 10, kSequencePointKind_Normal, 0, 5 },
	{ 109994, 1, 54, 54, 13, 104, 48, kSequencePointKind_StepOut, 0, 6 },
	{ 109994, 1, 55, 55, 9, 10, 56, kSequencePointKind_Normal, 0, 7 },
	{ 109995, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 8 },
	{ 109995, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 9 },
	{ 109995, 1, 58, 58, 9, 10, 0, kSequencePointKind_Normal, 0, 10 },
	{ 109995, 1, 59, 59, 13, 108, 1, kSequencePointKind_Normal, 0, 11 },
	{ 109995, 1, 59, 59, 13, 108, 17, kSequencePointKind_StepOut, 0, 12 },
	{ 109995, 1, 60, 60, 13, 122, 23, kSequencePointKind_Normal, 0, 13 },
	{ 109995, 1, 60, 60, 13, 122, 37, kSequencePointKind_StepOut, 0, 14 },
	{ 109995, 1, 61, 61, 18, 27, 43, kSequencePointKind_Normal, 0, 15 },
	{ 109995, 1, 61, 61, 0, 0, 45, kSequencePointKind_Normal, 0, 16 },
	{ 109995, 1, 62, 62, 13, 14, 47, kSequencePointKind_Normal, 0, 17 },
	{ 109995, 1, 63, 63, 17, 79, 48, kSequencePointKind_Normal, 0, 18 },
	{ 109995, 1, 63, 63, 17, 79, 58, kSequencePointKind_StepOut, 0, 19 },
	{ 109995, 1, 64, 64, 13, 14, 64, kSequencePointKind_Normal, 0, 20 },
	{ 109995, 1, 61, 61, 49, 52, 65, kSequencePointKind_Normal, 0, 21 },
	{ 109995, 1, 61, 61, 29, 47, 69, kSequencePointKind_Normal, 0, 22 },
	{ 109995, 1, 61, 61, 0, 0, 76, kSequencePointKind_Normal, 0, 23 },
	{ 109995, 1, 65, 65, 9, 10, 79, kSequencePointKind_Normal, 0, 24 },
	{ 109996, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 25 },
	{ 109996, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 26 },
	{ 109996, 1, 68, 68, 9, 10, 0, kSequencePointKind_Normal, 0, 27 },
	{ 109996, 1, 69, 69, 13, 33, 1, kSequencePointKind_Normal, 0, 28 },
	{ 109996, 1, 69, 69, 0, 0, 6, kSequencePointKind_Normal, 0, 29 },
	{ 109996, 1, 70, 70, 17, 60, 9, kSequencePointKind_Normal, 0, 30 },
	{ 109996, 1, 70, 70, 17, 60, 14, kSequencePointKind_StepOut, 0, 31 },
	{ 109996, 1, 72, 72, 13, 14, 20, kSequencePointKind_Normal, 0, 32 },
	{ 109996, 1, 73, 73, 23, 42, 21, kSequencePointKind_Normal, 0, 33 },
	{ 109996, 1, 74, 74, 17, 18, 45, kSequencePointKind_Normal, 0, 34 },
	{ 109996, 1, 75, 75, 21, 135, 46, kSequencePointKind_Normal, 0, 35 },
	{ 109996, 1, 75, 75, 21, 135, 53, kSequencePointKind_StepOut, 0, 36 },
	{ 109996, 1, 78, 78, 9, 10, 61, kSequencePointKind_Normal, 0, 37 },
	{ 109997, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 38 },
	{ 109997, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 39 },
	{ 109997, 1, 81, 81, 9, 10, 0, kSequencePointKind_Normal, 0, 40 },
	{ 109997, 1, 82, 82, 13, 33, 1, kSequencePointKind_Normal, 0, 41 },
	{ 109997, 1, 82, 82, 0, 0, 6, kSequencePointKind_Normal, 0, 42 },
	{ 109997, 1, 83, 83, 17, 60, 9, kSequencePointKind_Normal, 0, 43 },
	{ 109997, 1, 83, 83, 17, 60, 14, kSequencePointKind_StepOut, 0, 44 },
	{ 109997, 1, 85, 85, 13, 14, 20, kSequencePointKind_Normal, 0, 45 },
	{ 109997, 1, 86, 86, 23, 44, 21, kSequencePointKind_Normal, 0, 46 },
	{ 109997, 1, 87, 87, 17, 18, 45, kSequencePointKind_Normal, 0, 47 },
	{ 109997, 1, 88, 88, 21, 134, 46, kSequencePointKind_Normal, 0, 48 },
	{ 109997, 1, 88, 88, 21, 134, 53, kSequencePointKind_StepOut, 0, 49 },
	{ 109997, 1, 91, 91, 9, 10, 61, kSequencePointKind_Normal, 0, 50 },
	{ 109998, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 51 },
	{ 109998, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 52 },
	{ 109998, 1, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 53 },
	{ 109998, 1, 0, 0, 0, 0, 0, kSequencePointKind_StepOut, 0, 54 },
	{ 109998, 1, 96, 96, 9, 10, 20, kSequencePointKind_Normal, 0, 55 },
	{ 109998, 1, 97, 97, 13, 33, 21, kSequencePointKind_Normal, 0, 56 },
	{ 109998, 1, 97, 97, 0, 0, 27, kSequencePointKind_Normal, 0, 57 },
	{ 109998, 1, 98, 98, 17, 60, 30, kSequencePointKind_Normal, 0, 58 },
	{ 109998, 1, 98, 98, 17, 60, 35, kSequencePointKind_StepOut, 0, 59 },
	{ 109998, 1, 100, 103, 13, 29, 41, kSequencePointKind_Normal, 0, 60 },
	{ 109998, 1, 100, 103, 13, 29, 49, kSequencePointKind_StepOut, 0, 61 },
	{ 109998, 1, 100, 103, 13, 29, 61, kSequencePointKind_StepOut, 0, 62 },
	{ 109998, 1, 100, 103, 13, 29, 66, kSequencePointKind_StepOut, 0, 63 },
	{ 109998, 1, 100, 103, 13, 29, 91, kSequencePointKind_StepOut, 0, 64 },
	{ 109998, 1, 100, 103, 13, 29, 102, kSequencePointKind_StepOut, 0, 65 },
	{ 109998, 1, 100, 103, 13, 29, 107, kSequencePointKind_StepOut, 0, 66 },
	{ 109998, 1, 105, 105, 13, 85, 113, kSequencePointKind_Normal, 0, 67 },
	{ 109998, 1, 105, 105, 13, 85, 117, kSequencePointKind_StepOut, 0, 68 },
	{ 109998, 1, 107, 107, 13, 37, 123, kSequencePointKind_Normal, 0, 69 },
	{ 109998, 1, 107, 107, 0, 0, 129, kSequencePointKind_Normal, 0, 70 },
	{ 109998, 1, 108, 108, 13, 14, 133, kSequencePointKind_Normal, 0, 71 },
	{ 109998, 1, 109, 109, 22, 31, 134, kSequencePointKind_Normal, 0, 72 },
	{ 109998, 1, 109, 109, 33, 54, 137, kSequencePointKind_Normal, 0, 73 },
	{ 109998, 1, 109, 109, 0, 0, 140, kSequencePointKind_Normal, 0, 74 },
	{ 109998, 1, 110, 110, 17, 18, 142, kSequencePointKind_Normal, 0, 75 },
	{ 109998, 1, 111, 111, 21, 36, 143, kSequencePointKind_Normal, 0, 76 },
	{ 109998, 1, 111, 111, 0, 0, 147, kSequencePointKind_Normal, 0, 77 },
	{ 109998, 1, 112, 112, 25, 97, 151, kSequencePointKind_Normal, 0, 78 },
	{ 109998, 1, 112, 112, 25, 97, 173, kSequencePointKind_StepOut, 0, 79 },
	{ 109998, 1, 112, 112, 0, 0, 183, kSequencePointKind_Normal, 0, 80 },
	{ 109998, 1, 114, 114, 25, 93, 185, kSequencePointKind_Normal, 0, 81 },
	{ 109998, 1, 115, 115, 17, 18, 212, kSequencePointKind_Normal, 0, 82 },
	{ 109998, 1, 109, 109, 67, 70, 213, kSequencePointKind_Normal, 0, 83 },
	{ 109998, 1, 109, 109, 56, 65, 219, kSequencePointKind_Normal, 0, 84 },
	{ 109998, 1, 109, 109, 0, 0, 227, kSequencePointKind_Normal, 0, 85 },
	{ 109998, 1, 116, 116, 13, 14, 231, kSequencePointKind_Normal, 0, 86 },
	{ 109998, 1, 116, 116, 0, 0, 232, kSequencePointKind_Normal, 0, 87 },
	{ 109998, 1, 118, 118, 13, 14, 234, kSequencePointKind_Normal, 0, 88 },
	{ 109998, 1, 119, 119, 22, 31, 235, kSequencePointKind_Normal, 0, 89 },
	{ 109998, 1, 119, 119, 33, 54, 238, kSequencePointKind_Normal, 0, 90 },
	{ 109998, 1, 119, 119, 0, 0, 241, kSequencePointKind_Normal, 0, 91 },
	{ 109998, 1, 120, 120, 17, 18, 243, kSequencePointKind_Normal, 0, 92 },
	{ 109998, 1, 121, 121, 21, 36, 244, kSequencePointKind_Normal, 0, 93 },
	{ 109998, 1, 121, 121, 0, 0, 248, kSequencePointKind_Normal, 0, 94 },
	{ 109998, 1, 122, 122, 25, 67, 252, kSequencePointKind_Normal, 0, 95 },
	{ 109998, 1, 122, 122, 0, 0, 270, kSequencePointKind_Normal, 0, 96 },
	{ 109998, 1, 124, 124, 25, 63, 272, kSequencePointKind_Normal, 0, 97 },
	{ 109998, 1, 125, 125, 17, 18, 290, kSequencePointKind_Normal, 0, 98 },
	{ 109998, 1, 119, 119, 67, 70, 291, kSequencePointKind_Normal, 0, 99 },
	{ 109998, 1, 119, 119, 56, 65, 297, kSequencePointKind_Normal, 0, 100 },
	{ 109998, 1, 119, 119, 0, 0, 305, kSequencePointKind_Normal, 0, 101 },
	{ 109998, 1, 126, 126, 13, 14, 309, kSequencePointKind_Normal, 0, 102 },
	{ 109998, 1, 128, 128, 13, 36, 310, kSequencePointKind_Normal, 0, 103 },
	{ 109998, 1, 129, 129, 9, 10, 315, kSequencePointKind_Normal, 0, 104 },
	{ 109999, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 105 },
	{ 109999, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 106 },
	{ 109999, 1, 15, 45, 9, 11, 0, kSequencePointKind_Normal, 0, 107 },
	{ 109999, 1, 15, 45, 9, 11, 17, kSequencePointKind_StepOut, 0, 108 },
	{ 109999, 1, 15, 45, 9, 11, 22, kSequencePointKind_StepOut, 0, 109 },
	{ 109999, 1, 15, 45, 9, 11, 47, kSequencePointKind_StepOut, 0, 110 },
	{ 109999, 1, 15, 45, 9, 11, 52, kSequencePointKind_StepOut, 0, 111 },
	{ 109999, 1, 15, 45, 9, 11, 74, kSequencePointKind_StepOut, 0, 112 },
	{ 109999, 1, 15, 45, 9, 11, 79, kSequencePointKind_StepOut, 0, 113 },
	{ 109999, 1, 15, 45, 9, 11, 107, kSequencePointKind_StepOut, 0, 114 },
	{ 109999, 1, 15, 45, 9, 11, 112, kSequencePointKind_StepOut, 0, 115 },
	{ 109999, 1, 15, 45, 9, 11, 141, kSequencePointKind_StepOut, 0, 116 },
	{ 109999, 1, 15, 45, 9, 11, 146, kSequencePointKind_StepOut, 0, 117 },
	{ 109999, 1, 15, 45, 9, 11, 175, kSequencePointKind_StepOut, 0, 118 },
	{ 109999, 1, 15, 45, 9, 11, 180, kSequencePointKind_StepOut, 0, 119 },
	{ 109999, 1, 15, 45, 9, 11, 209, kSequencePointKind_StepOut, 0, 120 },
	{ 109999, 1, 15, 45, 9, 11, 214, kSequencePointKind_StepOut, 0, 121 },
	{ 109999, 1, 15, 45, 9, 11, 240, kSequencePointKind_StepOut, 0, 122 },
	{ 109999, 1, 15, 45, 9, 11, 245, kSequencePointKind_StepOut, 0, 123 },
	{ 109999, 1, 15, 45, 9, 11, 277, kSequencePointKind_StepOut, 0, 124 },
	{ 109999, 1, 15, 45, 9, 11, 282, kSequencePointKind_StepOut, 0, 125 },
	{ 109999, 1, 15, 45, 9, 11, 315, kSequencePointKind_StepOut, 0, 126 },
	{ 109999, 1, 15, 45, 9, 11, 320, kSequencePointKind_StepOut, 0, 127 },
	{ 109999, 1, 15, 45, 9, 11, 350, kSequencePointKind_StepOut, 0, 128 },
	{ 109999, 1, 15, 45, 9, 11, 355, kSequencePointKind_StepOut, 0, 129 },
	{ 109999, 1, 15, 45, 9, 11, 379, kSequencePointKind_StepOut, 0, 130 },
	{ 109999, 1, 15, 45, 9, 11, 384, kSequencePointKind_StepOut, 0, 131 },
	{ 109999, 1, 15, 45, 9, 11, 411, kSequencePointKind_StepOut, 0, 132 },
	{ 109999, 1, 15, 45, 9, 11, 416, kSequencePointKind_StepOut, 0, 133 },
	{ 109999, 1, 15, 45, 9, 11, 443, kSequencePointKind_StepOut, 0, 134 },
	{ 109999, 1, 15, 45, 9, 11, 448, kSequencePointKind_StepOut, 0, 135 },
	{ 109999, 1, 15, 45, 9, 11, 478, kSequencePointKind_StepOut, 0, 136 },
	{ 109999, 1, 15, 45, 9, 11, 483, kSequencePointKind_StepOut, 0, 137 },
	{ 109999, 1, 15, 45, 9, 11, 510, kSequencePointKind_StepOut, 0, 138 },
	{ 109999, 1, 15, 45, 9, 11, 515, kSequencePointKind_StepOut, 0, 139 },
	{ 109999, 1, 15, 45, 9, 11, 539, kSequencePointKind_StepOut, 0, 140 },
	{ 109999, 1, 15, 45, 9, 11, 544, kSequencePointKind_StepOut, 0, 141 },
	{ 109999, 1, 15, 45, 9, 11, 574, kSequencePointKind_StepOut, 0, 142 },
	{ 109999, 1, 15, 45, 9, 11, 579, kSequencePointKind_StepOut, 0, 143 },
	{ 109999, 1, 15, 45, 9, 11, 609, kSequencePointKind_StepOut, 0, 144 },
	{ 109999, 1, 15, 45, 9, 11, 614, kSequencePointKind_StepOut, 0, 145 },
	{ 109999, 1, 47, 48, 9, 89, 629, kSequencePointKind_Normal, 0, 146 },
	{ 109999, 1, 47, 48, 9, 89, 645, kSequencePointKind_StepOut, 0, 147 },
	{ 109999, 1, 47, 48, 9, 89, 650, kSequencePointKind_StepOut, 0, 148 },
	{ 109999, 1, 47, 48, 9, 89, 655, kSequencePointKind_StepOut, 0, 149 },
	{ 110002, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 150 },
	{ 110002, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 151 },
	{ 110002, 1, 102, 102, 31, 57, 0, kSequencePointKind_Normal, 0, 152 },
	{ 110003, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 153 },
	{ 110003, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 154 },
	{ 110003, 1, 48, 48, 49, 77, 0, kSequencePointKind_Normal, 0, 155 },
	{ 110003, 1, 48, 48, 49, 77, 1, kSequencePointKind_StepOut, 0, 156 },
	{ 110005, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 157 },
	{ 110005, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 158 },
	{ 110005, 1, 101, 101, 22, 148, 0, kSequencePointKind_Normal, 0, 159 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_AccessibilityModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_AccessibilityModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Accessibility/VisionUtility.cs", { 8, 231, 221, 45, 64, 250, 123, 201, 76, 231, 35, 154, 168, 13, 71, 89} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[3] = 
{
	{ 14137, 1 },
	{ 14135, 1 },
	{ 14136, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[10] = 
{
	{ 0, 58 },
	{ 0, 80 },
	{ 43, 79 },
	{ 0, 63 },
	{ 21, 61 },
	{ 0, 63 },
	{ 21, 61 },
	{ 0, 318 },
	{ 134, 231 },
	{ 235, 309 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[12] = 
{
	{ 58, 0, 1 },
	{ 80, 1, 2 },
	{ 63, 3, 2 },
	{ 63, 5, 2 },
	{ 318, 7, 3 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_AccessibilityModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_AccessibilityModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	160,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_AccessibilityModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	3,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
