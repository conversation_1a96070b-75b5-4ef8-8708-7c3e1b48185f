﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87;
struct Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99;
struct IUniTaskAsyncEnumerable_1_t5B7634972511B7F2B9CA93D17B062DD7D8EA247A;
struct IUniTaskAsyncEnumerator_1_t21358676B8858ABFBD647A323B3FFAB2FC05EA93;
struct IUniTaskSource_1_t41EA7A95FBAE1E11C685B8E76E3A864529B3A81A;
struct IUniTaskSource_1_tB6B218F9BB467453A6CBAEE053820D3B3E8F891C;
struct IUniTaskSource_1_t8F97352CA996D0781DB009E630AB2143DE1614D3;
struct IUniTaskSource_1_tA27C284FF17E3AAB5B5D07A7E4DE207B56F764AB;
struct SparselyPopulatedArrayFragment_1_tB32DA8C2B7461E80CE4C271B76C103629BDFA035;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct CancellationCallbackInfo_tC8BE558ED1E173434DD1919D574C9FAFE501E22D;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61;
struct IPlayerLoopItem_tB73003A682F5D754D64D74135621FC47F638F66F;
struct IUniTaskSource_t5AF6C202B2616C72E989C90B6E76DFEC4118174D;
struct MethodInfo_t;
struct MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5;
struct String_t;
struct Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct _EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310;
struct _Timer_t02D4B03E9C868588091570453DD875CF717280FE;
struct U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422;
struct U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUniTask[];
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUniTask_Linq[];
IL2CPP_EXTERN_C RuntimeClass* Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CancellationTokenExtensions_tDCF32020B3569F58EFEE1EE41A992652A6F720E6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlayerLoopHelper_tA497C2C44D13E21B40E76B01973260BB49C9CF01_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* _EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* _Timer_t02D4B03E9C868588091570453DD875CF717280FE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* EveryUpdate_GetAsyncEnumerator_mA5FC54EDF34ED5BB326271A43A7264A2702F3840_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EveryUpdate__ctor_m7E0BB8B1BB6D9E6674566F5D4040DC06A6C64133_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1_GetValueOrDefault_m068A148705ED1E215A5E85D18BA6852B192DA419_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1__ctor_m57671EDBF4C8A3BEF5EF30DC71BE6C17C6A657AC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1__ctor_mF3D65C30ACED71826A2F8078A5D10F3CC827E420_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1_get_HasValue_mD63976C410D14373D6D1D3E713C09B1E152F2166_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1_get_Value_mC7511CCEBD1BB6FEEA7903DBB81ED7290D00DDD3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Timer_GetAsyncEnumerator_mBE61A5FEDB1387353BF4E15E8DBA832BA126913D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Timer__ctor_m5ABA17C5028D1F0176E530D20DF37A60119C0D8F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3C_ctorU3Eb__11_0_mC0ED724BEC945D3F1A6EAC63C6660EE7D87AEF6F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3C_ctorU3Eb__4_0_m819AE164F7F119D264F64FBF6515DD3EAA730FB2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__cctor_m1853D83A8C62BC152E3C5A1B3D82BC8C03A2F513_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__cctor_mC3A825E1D0D47B2DF7DC8E84857ED248A7601865_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__ctor_m13E90642B99BFBEC8C07BDB8B7F173A969DD5662_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__ctor_m159C954281DAB83E3033E723DFB83AB2CDAAEFF5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UniTaskAsyncEnumerable_EveryUpdate_m956B189411A0CA182EF8B525064626145F4CF092_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UniTaskAsyncEnumerable_Interval_mDC4D7EE47A309C1C5FB64518F05F2A6FA2BCAE84_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UniTaskCompletionSourceCore_1_Reset_m11E75ED2E4CC547CB5A568C0063665353803495D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UniTaskCompletionSourceCore_1_get_Version_m49DEF08C91978DDFDE6A27F0C2B6630BB48116C5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UniTask_1__ctor_mE4AE554655B64D8F20BD92C5C769A9E39167D200_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _EveryUpdate_DisposeAsync_m697EA4E391FC38D6CA01AE56DE13A0C7D605ADAD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _EveryUpdate_MoveNextAsync_m26553288670515E44E150AEFBDF39D5AF93ED0C1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _EveryUpdate_MoveNext_m901D698E90BE0DA3A2F39C587E0C1AADE5EAE061_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _EveryUpdate__ctor_m720DB73134A52F587B5C06D4AB65C5695DB8525B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _EveryUpdate_get_Current_m9530A0604459C52E0B4E1C867D6130FB58FA1472_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _Timer_DisposeAsync_mAAF11D3D9223205036F0695E5A6E4DA4C092B565_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _Timer_MoveNextAsync_m1C536CA3782940F8CDA20FE36160B2130B9DA833_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _Timer_MoveNext_mE978E92C7BCABBE941ECAACA29BDEC938A740BA4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _Timer__ctor_m0D814AB06478A51B470E2C4A08C3C43F410EE2A2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* _Timer_get_Current_m1DFFF2DB3E1FDA56BBC2BD8973DEAA81946BA498_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* IUniTaskAsyncEnumerable_1_t5B7634972511B7F2B9CA93D17B062DD7D8EA247A_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* IUniTaskAsyncEnumerator_1_t21358676B8858ABFBD647A323B3FFAB2FC05EA93_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UniTaskCompletionSourceCore_1_t7DADC575C5ED6A5929DB19A1EBFC3DA4A86BC202_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UniTask_1_t9BDB4A2D84D7D834CF4ADFBE0BE59E9A38B3A1B4_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* _EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* _Timer_t02D4B03E9C868588091570453DD875CF717280FE_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t83D73517740A0B879B5AB1082DE1CE51DAF5F78C 
{
};
struct CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B  : public RuntimeObject
{
};
struct UniTaskAsyncEnumerable_t414ED2B0A45CC0290E4B30BEDCD6738C3527BD52  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422  : public RuntimeObject
{
};
struct U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77  : public RuntimeObject
{
};
struct Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 
{
	bool ___hasValue;
	float ___value;
};
typedef Il2CppFullySharedGenericStruct Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339;
struct SparselyPopulatedArrayAddInfo_1_t3C73DC53EB2CF8545348E3275C09690FFA1E5359 
{
	SparselyPopulatedArrayFragment_1_tB32DA8C2B7461E80CE4C271B76C103629BDFA035* ____source;
	int32_t ____index;
};
struct UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2 
{
	bool ___result;
	RuntimeObject* ___error;
	int16_t ___version;
	bool ___hasUnhandledError;
	int32_t ___completedCount;
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___continuation;
	RuntimeObject* ___continuationState;
};
typedef Il2CppFullySharedGenericStruct UniTaskCompletionSourceCore_1_t146409427AB3F018819917FFA1FDC70FDB5C0BBF;
struct UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 
{
	RuntimeObject* ___source;
	bool ___result;
	int16_t ___token;
};
struct UniTask_1_t0E622BA64CCA8AEC241A4A21713E84028FD0BF48 
{
	RuntimeObject* ___source;
	int32_t ___result;
	int16_t ___token;
};
typedef Il2CppFullySharedGenericStruct UniTask_1_t462EA7468BEE3A822B4D759A9930C6DF4DED6483;
struct AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A 
{
	union
	{
		struct
		{
		};
		uint8_t AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A__padding[1];
	};
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED 
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ____source;
};
struct CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_marshaled_pinvoke
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ____source;
};
struct CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_marshaled_com
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ____source;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175 
{
	int16_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A 
{
	int64_t ____ticks;
};
struct UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 
{
	RuntimeObject* ___source;
	int16_t ___token;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272 
{
	bool ___hasValue;
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___value;
};
struct UniTask_1_tF16183324C23C2BED9F2F4151405F68DC5AD994A 
{
	RuntimeObject* ___source;
	AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A ___result;
	int16_t ___token;
};
struct CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389 
{
	CancellationCallbackInfo_tC8BE558ED1E173434DD1919D574C9FAFE501E22D* ___m_callbackInfo;
	SparselyPopulatedArrayAddInfo_1_t3C73DC53EB2CF8545348E3275C09690FFA1E5359 ___m_registrationInfo;
};
struct CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389_marshaled_pinvoke
{
	CancellationCallbackInfo_tC8BE558ED1E173434DD1919D574C9FAFE501E22D* ___m_callbackInfo;
	SparselyPopulatedArrayAddInfo_1_t3C73DC53EB2CF8545348E3275C09690FFA1E5359 ___m_registrationInfo;
};
struct CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389_marshaled_com
{
	CancellationCallbackInfo_tC8BE558ED1E173434DD1919D574C9FAFE501E22D* ___m_callbackInfo;
	SparselyPopulatedArrayAddInfo_1_t3C73DC53EB2CF8545348E3275C09690FFA1E5359 ___m_registrationInfo;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5  : public RuntimeObject
{
	UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2 ___completionSource;
};
struct PlayerLoopTiming_tA0561E77DCF3749CC535F4F45642F515BDF040C2 
{
	int32_t ___value__;
};
struct EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61  : public RuntimeObject
{
	int32_t ___updateTiming;
	bool ___cancelImmediately;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5  : public RuntimeObject
{
	int32_t ___updateTiming;
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___dueTime;
	Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272 ___period;
	bool ___ignoreTimeScale;
	bool ___cancelImmediately;
};
struct _EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310  : public MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5
{
	int32_t ___updateTiming;
	CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___cancellationToken;
	CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389 ___cancellationTokenRegistration;
	bool ___disposed;
};
struct _Timer_t02D4B03E9C868588091570453DD875CF717280FE  : public MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5
{
	float ___dueTime;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___period;
	int32_t ___updateTiming;
	bool ___ignoreTimeScale;
	CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___cancellationToken;
	CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389 ___cancellationTokenRegistration;
	int32_t ___initialFrame;
	float ___elapsed;
	bool ___dueTimePhase;
	bool ___completed;
	bool ___disposed;
};
struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87  : public MulticastDelegate_t
{
};
struct CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_StaticFields
{
	UniTask_1_tF16183324C23C2BED9F2F4151405F68DC5AD994A ___AsyncUnit;
	UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 ___True;
	UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 ___False;
	UniTask_1_t0E622BA64CCA8AEC241A4A21713E84028FD0BF48 ___Zero;
	UniTask_1_t0E622BA64CCA8AEC241A4A21713E84028FD0BF48 ___MinusOne;
	UniTask_1_t0E622BA64CCA8AEC241A4A21713E84028FD0BF48 ___One;
};
struct U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_StaticFields
{
	U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422* ___U3CU3E9;
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___U3CU3E9__4_0;
};
struct U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_StaticFields
{
	U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77* ___U3CU3E9;
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___U3CU3E9__11_0;
};
struct AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A_StaticFields
{
	AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A ___Default;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_StaticFields
{
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___s_actionToActionObjShunt;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_StaticFields
{
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___Zero;
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___MaxValue;
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___MinValue;
};
struct UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_StaticFields
{
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 ___CanceledUniTask;
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 ___CompletedTask;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Nullable_1__ctor_m4257D7FF23A495D1B204F20330FBDED58248E4CC_gshared (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339* __this, Il2CppFullySharedGenericStruct ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m685A441EC9FAC9D554B26FA83A08F4BEF96DFF0E_gshared (Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UniTaskCompletionSourceCore_1_Reset_m788665B21E38E2A5451D5A30194E957469083C5C_gshared (UniTaskCompletionSourceCore_1_t146409427AB3F018819917FFA1FDC70FDB5C0BBF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool UniTaskCompletionSourceCore_1_TrySetCanceled_mEA4B7341003C6B1365405F43730371BF3D935E14_gshared (UniTaskCompletionSourceCore_1_t146409427AB3F018819917FFA1FDC70FDB5C0BBF* __this, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___0_cancellationToken, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int16_t UniTaskCompletionSourceCore_1_get_Version_mC206FEA615DEB3676B72991ABAE79848523CAC0B_gshared_inline (UniTaskCompletionSourceCore_1_t146409427AB3F018819917FFA1FDC70FDB5C0BBF* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void UniTask_1__ctor_m608BBDBA054799FF72D4ED7758DA9BA32EBB8F22_gshared_inline (UniTask_1_t462EA7468BEE3A822B4D759A9930C6DF4DED6483* __this, RuntimeObject* ___0_source, int16_t ___1_token, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool UniTaskCompletionSourceCore_1_TrySetResult_m7DA02F53706893C67B6747B3F95C9B82D0946D40_gshared (UniTaskCompletionSourceCore_1_t146409427AB3F018819917FFA1FDC70FDB5C0BBF* __this, Il2CppFullySharedGenericAny ___0_result, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Nullable_1_get_HasValue_m14F273FB376DF00D727434CDCD28AB4EDCC14C3C_gshared_inline (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Nullable_1_get_Value_mA083C4D9192050DC38513BDD9D364C5C68A3A675_gshared (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339* __this, Il2CppFullySharedGenericStruct* il2cppRetVal, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Nullable_1_GetValueOrDefault_mC057FBD944AF068B90EBDD0B496231A01B2A4228_gshared_inline (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339* __this, Il2CppFullySharedGenericStruct* il2cppRetVal, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EveryUpdate__ctor_m7E0BB8B1BB6D9E6674566F5D4040DC06A6C64133 (EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61* __this, int32_t ___0_updateTiming, bool ___1_cancelImmediately, const RuntimeMethod* method) ;
inline void Nullable_1__ctor_m57671EDBF4C8A3BEF5EF30DC71BE6C17C6A657AC (Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272* __this, TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___0_value, const RuntimeMethod* method)
{
	((  void (*) (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*, Il2CppFullySharedGenericStruct, const RuntimeMethod*))Nullable_1__ctor_m4257D7FF23A495D1B204F20330FBDED58248E4CC_gshared)((Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*)__this, (Il2CppFullySharedGenericStruct)&___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Timer__ctor_m5ABA17C5028D1F0176E530D20DF37A60119C0D8F (Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5* __this, TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___0_dueTime, Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272 ___1_period, int32_t ___2_updateTiming, bool ___3_ignoreTimeScale, bool ___4_cancelImmediately, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void _EveryUpdate__ctor_m720DB73134A52F587B5C06D4AB65C5695DB8525B (_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* __this, int32_t ___0_updateTiming, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___1_cancellationToken, bool ___2_cancelImmediately, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MoveNextSource__ctor_mAD2FB060DAA5FB4AC333260F502B4FD563133640 (MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CancellationToken_get_CanBeCanceled_mC3751330B171DB14B70B9BAAD90A7D098A2309EC (CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED* __this, const RuntimeMethod* method) ;
inline void Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4 (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m685A441EC9FAC9D554B26FA83A08F4BEF96DFF0E_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389 CancellationTokenExtensions_RegisterWithoutCaptureExecutionContext_mA5A09F053F6E50AD047E0DB08666E4270863AC69 (CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___0_cancellationToken, Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___1_callback, RuntimeObject* ___2_state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerLoopHelper_AddAction_mAD5D5DA039BE42ECF032A1835BD95C318D6ED22D (int32_t ___0_timing, RuntimeObject* ___1_action, const RuntimeMethod* method) ;
inline void UniTaskCompletionSourceCore_1_Reset_m11E75ED2E4CC547CB5A568C0063665353803495D (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* __this, const RuntimeMethod* method)
{
	((  void (*) (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*, const RuntimeMethod*))UniTaskCompletionSourceCore_1_Reset_m788665B21E38E2A5451D5A30194E957469083C5C_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CancellationToken_get_IsCancellationRequested_m9744F7A1A82946FDD1DC68E905F1ED826471D350 (CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED* __this, const RuntimeMethod* method) ;
inline bool UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168 (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* __this, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___0_cancellationToken, const RuntimeMethod* method)
{
	return ((  bool (*) (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED, const RuntimeMethod*))UniTaskCompletionSourceCore_1_TrySetCanceled_mEA4B7341003C6B1365405F43730371BF3D935E14_gshared)(__this, ___0_cancellationToken, method);
}
inline int16_t UniTaskCompletionSourceCore_1_get_Version_m49DEF08C91978DDFDE6A27F0C2B6630BB48116C5_inline (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* __this, const RuntimeMethod* method)
{
	return ((  int16_t (*) (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*, const RuntimeMethod*))UniTaskCompletionSourceCore_1_get_Version_mC206FEA615DEB3676B72991ABAE79848523CAC0B_gshared_inline)(__this, method);
}
inline void UniTask_1__ctor_mE4AE554655B64D8F20BD92C5C769A9E39167D200_inline (UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949* __this, RuntimeObject* ___0_source, int16_t ___1_token, const RuntimeMethod* method)
{
	((  void (*) (UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949*, RuntimeObject*, int16_t, const RuntimeMethod*))UniTask_1__ctor_m608BBDBA054799FF72D4ED7758DA9BA32EBB8F22_gshared_inline)(__this, ___0_source, ___1_token, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CancellationTokenRegistration_Dispose_m9EAF1228573E8278DDF7A3BEB5EE0E18DA6DC0E1 (CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389* __this, const RuntimeMethod* method) ;
inline bool UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037 (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* __this, bool ___0_result, const RuntimeMethod* method)
{
	return ((  bool (*) (UniTaskCompletionSourceCore_1_t146409427AB3F018819917FFA1FDC70FDB5C0BBF*, Il2CppFullySharedGenericAny, const RuntimeMethod*))UniTaskCompletionSourceCore_1_TrySetResult_m7DA02F53706893C67B6747B3F95C9B82D0946D40_gshared)((UniTaskCompletionSourceCore_1_t146409427AB3F018819917FFA1FDC70FDB5C0BBF*)__this, (Il2CppFullySharedGenericAny)&___0_result, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m13E90642B99BFBEC8C07BDB8B7F173A969DD5662 (U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void _Timer__ctor_m0D814AB06478A51B470E2C4A08C3C43F410EE2A2 (_Timer_t02D4B03E9C868588091570453DD875CF717280FE* __this, TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___0_dueTime, Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272 ___1_period, int32_t ___2_updateTiming, bool ___3_ignoreTimeScale, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___4_cancellationToken, bool ___5_cancelImmediately, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double TimeSpan_get_TotalSeconds_mED686E7CECE6A76A7DC38518698B9199DB8CDEA8 (TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A* __this, const RuntimeMethod* method) ;
inline bool Nullable_1_get_HasValue_mD63976C410D14373D6D1D3E713C09B1E152F2166_inline (Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272*, const RuntimeMethod*))Nullable_1_get_HasValue_m14F273FB376DF00D727434CDCD28AB4EDCC14C3C_gshared_inline)(__this, method);
}
inline TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A Nullable_1_get_Value_mC7511CCEBD1BB6FEEA7903DBB81ED7290D00DDD3 (Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272* __this, const RuntimeMethod* method)
{
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A il2cppRetVal;
	((  void (*) (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*, Il2CppFullySharedGenericStruct*, const RuntimeMethod*))Nullable_1_get_Value_mA083C4D9192050DC38513BDD9D364C5C68A3A675_gshared)((Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*)__this, (Il2CppFullySharedGenericStruct*)&il2cppRetVal, method);
	return il2cppRetVal;
}
inline void Nullable_1__ctor_mF3D65C30ACED71826A2F8078A5D10F3CC827E420 (Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75* __this, float ___0_value, const RuntimeMethod* method)
{
	((  void (*) (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*, Il2CppFullySharedGenericStruct, const RuntimeMethod*))Nullable_1__ctor_m4257D7FF23A495D1B204F20330FBDED58248E4CC_gshared)((Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*)__this, (Il2CppFullySharedGenericStruct)&___0_value, method);
}
inline bool Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_inline (Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75*, const RuntimeMethod*))Nullable_1_get_HasValue_m14F273FB376DF00D727434CDCD28AB4EDCC14C3C_gshared_inline)(__this, method);
}
inline float Nullable_1_GetValueOrDefault_m068A148705ED1E215A5E85D18BA6852B192DA419_inline (Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75* __this, const RuntimeMethod* method)
{
	float il2cppRetVal;
	((  void (*) (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*, Il2CppFullySharedGenericStruct*, const RuntimeMethod*))Nullable_1_GetValueOrDefault_mC057FBD944AF068B90EBDD0B496231A01B2A4228_gshared_inline)((Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339*)__this, (Il2CppFullySharedGenericStruct*)&il2cppRetVal, method);
	return il2cppRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PlayerLoopHelper_get_IsMainThread_mC32C7D62B20488CD0BA2F337EBFEC8128086DD5A (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Time_get_frameCount_m4A42E558A71301A216BDC49EC402D62F19C79667 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_unscaledDeltaTime_mF057EECA857E5C0F90A3F910D26D3EE59F27C4B5 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m159C954281DAB83E3033E723DFB83AB2CDAAEFF5 (U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* UniTaskAsyncEnumerable_EveryUpdate_m956B189411A0CA182EF8B525064626145F4CF092 (int32_t ___0_updateTiming, bool ___1_cancelImmediately, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUniTaskAsyncEnumerable_1_t5B7634972511B7F2B9CA93D17B062DD7D8EA247A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskAsyncEnumerable_EveryUpdate_m956B189411A0CA182EF8B525064626145F4CF092_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_updateTiming), (&___1_cancelImmediately));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, UniTaskAsyncEnumerable_EveryUpdate_m956B189411A0CA182EF8B525064626145F4CF092_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 3));
		int32_t L_0 = ___0_updateTiming;
		bool L_1 = ___1_cancelImmediately;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 4));
		EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61* L_2 = (EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61*)il2cpp_codegen_object_new(EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61_il2cpp_TypeInfo_var);
		EveryUpdate__ctor_m7E0BB8B1BB6D9E6674566F5D4040DC06A6C64133(L_2, L_0, L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 4));
		V_0 = L_2;
		goto IL_000b;
	}

IL_000b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 5));
		RuntimeObject* L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* UniTaskAsyncEnumerable_Interval_mDC4D7EE47A309C1C5FB64518F05F2A6FA2BCAE84 (TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___0_period, int32_t ___1_updateTiming, bool ___2_ignoreTimeScale, bool ___3_cancelImmediately, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUniTaskAsyncEnumerable_1_t5B7634972511B7F2B9CA93D17B062DD7D8EA247A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1__ctor_m57671EDBF4C8A3BEF5EF30DC71BE6C17C6A657AC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskAsyncEnumerable_Interval_mDC4D7EE47A309C1C5FB64518F05F2A6FA2BCAE84_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_period), (&___1_updateTiming), (&___2_ignoreTimeScale), (&___3_cancelImmediately));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, UniTaskAsyncEnumerable_Interval_mDC4D7EE47A309C1C5FB64518F05F2A6FA2BCAE84_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 6));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 7));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 8));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 9));
		TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A L_0 = ___0_period;
		TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A L_1 = ___0_period;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 10));
		Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272 L_2;
		memset((&L_2), 0, sizeof(L_2));
		Nullable_1__ctor_m57671EDBF4C8A3BEF5EF30DC71BE6C17C6A657AC((&L_2), L_1, Nullable_1__ctor_m57671EDBF4C8A3BEF5EF30DC71BE6C17C6A657AC_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 10));
		int32_t L_3 = ___1_updateTiming;
		bool L_4 = ___2_ignoreTimeScale;
		bool L_5 = ___3_cancelImmediately;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 11));
		Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5* L_6 = (Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5*)il2cpp_codegen_object_new(Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5_il2cpp_TypeInfo_var);
		Timer__ctor_m5ABA17C5028D1F0176E530D20DF37A60119C0D8F(L_6, L_0, L_2, L_3, L_4, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 11));
		V_0 = L_6;
		goto IL_0013;
	}

IL_0013:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 12));
		RuntimeObject* L_7 = V_0;
		return L_7;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EveryUpdate__ctor_m7E0BB8B1BB6D9E6674566F5D4040DC06A6C64133 (EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61* __this, int32_t ___0_updateTiming, bool ___1_cancelImmediately, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EveryUpdate__ctor_m7E0BB8B1BB6D9E6674566F5D4040DC06A6C64133_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_updateTiming), (&___1_cancelImmediately));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EveryUpdate__ctor_m7E0BB8B1BB6D9E6674566F5D4040DC06A6C64133_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 13));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 14));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 15));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 16));
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 16));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 17));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 18));
		int32_t L_0 = ___0_updateTiming;
		__this->___updateTiming = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 19));
		bool L_1 = ___1_cancelImmediately;
		__this->___cancelImmediately = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 20));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EveryUpdate_GetAsyncEnumerator_mA5FC54EDF34ED5BB326271A43A7264A2702F3840 (EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61* __this, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___0_cancellationToken, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EveryUpdate_GetAsyncEnumerator_mA5FC54EDF34ED5BB326271A43A7264A2702F3840_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EveryUpdate_t2661E947245C694121EECD8246C501F29E146F61_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUniTaskAsyncEnumerator_1_t21358676B8858ABFBD647A323B3FFAB2FC05EA93_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_cancellationToken));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EveryUpdate_GetAsyncEnumerator_mA5FC54EDF34ED5BB326271A43A7264A2702F3840_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 21));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 22));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 23));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 24));
		int32_t L_0 = __this->___updateTiming;
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_1 = ___0_cancellationToken;
		bool L_2 = __this->___cancelImmediately;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 25));
		_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* L_3 = (_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310*)il2cpp_codegen_object_new(_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_il2cpp_TypeInfo_var);
		_EveryUpdate__ctor_m720DB73134A52F587B5C06D4AB65C5695DB8525B(L_3, L_0, L_1, L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 25));
		V_0 = L_3;
		goto IL_0016;
	}

IL_0016:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 26));
		RuntimeObject* L_4 = V_0;
		return L_4;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void _EveryUpdate__ctor_m720DB73134A52F587B5C06D4AB65C5695DB8525B (_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* __this, int32_t ___0_updateTiming, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___1_cancellationToken, bool ___2_cancelImmediately, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CancellationTokenExtensions_tDCF32020B3569F58EFEE1EE41A992652A6F720E6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayerLoopHelper_tA497C2C44D13E21B40E76B01973260BB49C9CF01_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_ctorU3Eb__4_0_m819AE164F7F119D264F64FBF6515DD3EAA730FB2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate__ctor_m720DB73134A52F587B5C06D4AB65C5695DB8525B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_updateTiming), (&___1_cancellationToken), (&___2_cancelImmediately));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _EveryUpdate__ctor_m720DB73134A52F587B5C06D4AB65C5695DB8525B_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 27));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 28));
	int32_t G_B3_0 = 0;
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* G_B6_0 = NULL;
	CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED G_B6_1;
	memset((&G_B6_1), 0, sizeof(G_B6_1));
	_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* G_B6_2 = NULL;
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* G_B5_0 = NULL;
	CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED G_B5_1;
	memset((&G_B5_1), 0, sizeof(G_B5_1));
	_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* G_B5_2 = NULL;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 29));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 30));
		MoveNextSource__ctor_mAD2FB060DAA5FB4AC333260F502B4FD563133640(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 30));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 31));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 32));
		int32_t L_0 = ___0_updateTiming;
		__this->___updateTiming = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 33));
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_1 = ___1_cancellationToken;
		__this->___cancellationToken = L_1;
		Il2CppCodeGenWriteBarrier((void**)&(((&__this->___cancellationToken))->____source), (void*)NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 34));
		bool L_2 = ___2_cancelImmediately;
		if (!L_2)
		{
			goto IL_0022;
		}
	}
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 35));
		il2cpp_codegen_runtime_class_init_inline(CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = CancellationToken_get_CanBeCanceled_mC3751330B171DB14B70B9BAAD90A7D098A2309EC((&___1_cancellationToken), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 35));
		G_B3_0 = ((int32_t)(L_3));
		goto IL_0023;
	}

IL_0022:
	{
		G_B3_0 = 0;
	}

IL_0023:
	{
		V_0 = (bool)G_B3_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 36));
		bool L_4 = V_0;
		if (!L_4)
		{
			goto IL_0055;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 37));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 38));
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_5 = ___1_cancellationToken;
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var);
		Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* L_6 = ((U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var))->___U3CU3E9__4_0;
		Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* L_7 = L_6;
		if (L_7)
		{
			G_B6_0 = L_7;
			G_B6_1 = L_5;
			G_B6_2 = __this;
			goto IL_0049;
		}
		G_B5_0 = L_7;
		G_B5_1 = L_5;
		G_B5_2 = __this;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var);
		U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422* L_8 = ((U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var))->___U3CU3E9;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 39));
		Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* L_9 = (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87*)il2cpp_codegen_object_new(Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87_il2cpp_TypeInfo_var);
		Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4(L_9, L_8, (intptr_t)((void*)U3CU3Ec_U3C_ctorU3Eb__4_0_m819AE164F7F119D264F64FBF6515DD3EAA730FB2_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 39));
		Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* L_10 = L_9;
		((U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var))->___U3CU3E9__4_0 = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var))->___U3CU3E9__4_0), (void*)L_10);
		G_B6_0 = L_10;
		G_B6_1 = G_B5_1;
		G_B6_2 = G_B5_2;
	}

IL_0049:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 40));
		il2cpp_codegen_runtime_class_init_inline(CancellationTokenExtensions_tDCF32020B3569F58EFEE1EE41A992652A6F720E6_il2cpp_TypeInfo_var);
		CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389 L_11;
		L_11 = CancellationTokenExtensions_RegisterWithoutCaptureExecutionContext_mA5A09F053F6E50AD047E0DB08666E4270863AC69(G_B6_1, G_B6_0, __this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 40));
		NullCheck(G_B6_2);
		G_B6_2->___cancellationTokenRegistration = L_11;
		Il2CppCodeGenWriteBarrier((void**)&(((&G_B6_2->___cancellationTokenRegistration))->___m_callbackInfo), (void*)NULL);
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((&(((&G_B6_2->___cancellationTokenRegistration))->___m_registrationInfo))->____source), (void*)NULL);
		#endif
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 41));
	}

IL_0055:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 42));
		int32_t L_12 = ___0_updateTiming;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 43));
		il2cpp_codegen_runtime_class_init_inline(PlayerLoopHelper_tA497C2C44D13E21B40E76B01973260BB49C9CF01_il2cpp_TypeInfo_var);
		PlayerLoopHelper_AddAction_mAD5D5DA039BE42ECF032A1835BD95C318D6ED22D(L_12, __this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 43));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 44));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A _EveryUpdate_get_Current_m9530A0604459C52E0B4E1C867D6130FB58FA1472 (_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_get_Current_m9530A0604459C52E0B4E1C867D6130FB58FA1472_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _EveryUpdate_get_Current_m9530A0604459C52E0B4E1C867D6130FB58FA1472_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 45));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 46));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 47));
		il2cpp_codegen_initobj((&V_0), sizeof(AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A));
		AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 _EveryUpdate_MoveNextAsync_m26553288670515E44E150AEFBDF39D5AF93ED0C1 (_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_Reset_m11E75ED2E4CC547CB5A568C0063665353803495D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_get_Version_m49DEF08C91978DDFDE6A27F0C2B6630BB48116C5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_1__ctor_mE4AE554655B64D8F20BD92C5C769A9E39167D200_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_MoveNextAsync_m26553288670515E44E150AEFBDF39D5AF93ED0C1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 V_1;
	memset((&V_1), 0, sizeof(V_1));
	bool V_2 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _EveryUpdate_MoveNextAsync_m26553288670515E44E150AEFBDF39D5AF93ED0C1_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 48));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 49));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 50));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 51));
		bool L_0 = __this->___disposed;
		V_0 = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 52));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0013;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 53));
		il2cpp_codegen_runtime_class_init_inline(CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_il2cpp_TypeInfo_var);
		UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 L_2 = ((CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_StaticFields*)il2cpp_codegen_static_fields_for(CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_il2cpp_TypeInfo_var))->___False;
		V_1 = L_2;
		goto IL_0056;
	}

IL_0013:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 54));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_3 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 55));
		UniTaskCompletionSourceCore_1_Reset_m11E75ED2E4CC547CB5A568C0063665353803495D(L_3, UniTaskCompletionSourceCore_1_Reset_m11E75ED2E4CC547CB5A568C0063665353803495D_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 55));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 56));
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED* L_4 = (CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED*)(&__this->___cancellationToken);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 57));
		il2cpp_codegen_runtime_class_init_inline(CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = CancellationToken_get_IsCancellationRequested_m9744F7A1A82946FDD1DC68E905F1ED826471D350(L_4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 57));
		V_2 = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 58));
		bool L_6 = V_2;
		if (!L_6)
		{
			goto IL_0042;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 59));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 60));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_7 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_8 = __this->___cancellationToken;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 61));
		bool L_9;
		L_9 = UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168(L_7, L_8, UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 61));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 62));
	}

IL_0042:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 63));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_10 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 64));
		int16_t L_11;
		L_11 = UniTaskCompletionSourceCore_1_get_Version_m49DEF08C91978DDFDE6A27F0C2B6630BB48116C5_inline(L_10, UniTaskCompletionSourceCore_1_get_Version_m49DEF08C91978DDFDE6A27F0C2B6630BB48116C5_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 64));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 65));
		UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 L_12;
		memset((&L_12), 0, sizeof(L_12));
		UniTask_1__ctor_mE4AE554655B64D8F20BD92C5C769A9E39167D200_inline((&L_12), __this, L_11, UniTask_1__ctor_mE4AE554655B64D8F20BD92C5C769A9E39167D200_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 65));
		V_1 = L_12;
		goto IL_0056;
	}

IL_0056:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 66));
		UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 L_13 = V_1;
		return L_13;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 _EveryUpdate_DisposeAsync_m697EA4E391FC38D6CA01AE56DE13A0C7D605ADAD (_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_DisposeAsync_m697EA4E391FC38D6CA01AE56DE13A0C7D605ADAD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 V_1;
	memset((&V_1), 0, sizeof(V_1));
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 V_2;
	memset((&V_2), 0, sizeof(V_2));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _EveryUpdate_DisposeAsync_m697EA4E391FC38D6CA01AE56DE13A0C7D605ADAD_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 67));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 68));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 69));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 70));
		bool L_0 = __this->___disposed;
		V_0 = (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 71));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0023;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 72));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 73));
		CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389* L_2 = (CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389*)(&__this->___cancellationTokenRegistration);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 74));
		CancellationTokenRegistration_Dispose_m9EAF1228573E8278DDF7A3BEB5EE0E18DA6DC0E1(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 74));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 75));
		__this->___disposed = (bool)1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 76));
	}

IL_0023:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 77));
		il2cpp_codegen_initobj((&V_1), sizeof(UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270));
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 L_3 = V_1;
		V_2 = L_3;
		goto IL_002f;
	}

IL_002f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 78));
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 L_4 = V_2;
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool _EveryUpdate_MoveNext_m901D698E90BE0DA3A2F39C587E0C1AADE5EAE061 (_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_MoveNext_m901D698E90BE0DA3A2F39C587E0C1AADE5EAE061_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _EveryUpdate_MoveNext_m901D698E90BE0DA3A2F39C587E0C1AADE5EAE061_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 79));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 80));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 81));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 82));
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED* L_0 = (CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED*)(&__this->___cancellationToken);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 83));
		il2cpp_codegen_runtime_class_init_inline(CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = CancellationToken_get_IsCancellationRequested_m9744F7A1A82946FDD1DC68E905F1ED826471D350(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 83));
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 84));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_0027;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 85));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 86));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_3 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_4 = __this->___cancellationToken;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 87));
		bool L_5;
		L_5 = UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168(L_3, L_4, UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 87));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 88));
		V_1 = (bool)0;
		goto IL_0054;
	}

IL_0027:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 89));
		bool L_6 = __this->___disposed;
		V_2 = L_6;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 90));
		bool L_7 = V_2;
		if (!L_7)
		{
			goto IL_0043;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 91));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 92));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_8 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 93));
		bool L_9;
		L_9 = UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037(L_8, (bool)0, UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 93));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 94));
		V_1 = (bool)0;
		goto IL_0054;
	}

IL_0043:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 95));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_10 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 96));
		bool L_11;
		L_11 = UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037(L_10, (bool)1, UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 96));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 97));
		V_1 = (bool)1;
		goto IL_0054;
	}

IL_0054:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 98));
		bool L_12 = V_1;
		return L_12;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_m1853D83A8C62BC152E3C5A1B3D82BC8C03A2F513 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__cctor_m1853D83A8C62BC152E3C5A1B3D82BC8C03A2F513_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec__cctor_m1853D83A8C62BC152E3C5A1B3D82BC8C03A2F513_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422* L_0 = (U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422*)il2cpp_codegen_object_new(U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_m13E90642B99BFBEC8C07BDB8B7F173A969DD5662(L_0, NULL);
		((U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m13E90642B99BFBEC8C07BDB8B7F173A969DD5662 (U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__ctor_m13E90642B99BFBEC8C07BDB8B7F173A969DD5662_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec__ctor_m13E90642B99BFBEC8C07BDB8B7F173A969DD5662_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec_U3C_ctorU3Eb__4_0_m819AE164F7F119D264F64FBF6515DD3EAA730FB2 (U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422* __this, RuntimeObject* ___0_state, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_ctorU3Eb__4_0_m819AE164F7F119D264F64FBF6515DD3EAA730FB2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t57525BDCDD10CDB8F2F080B2090B0B0288D38422_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* V_0 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_state));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec_U3C_ctorU3Eb__4_0_m819AE164F7F119D264F64FBF6515DD3EAA730FB2_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 99));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 100));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 101));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 102));
		RuntimeObject* L_0 = ___0_state;
		V_0 = ((_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310*)CastclassClass((RuntimeObject*)L_0, _EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310_il2cpp_TypeInfo_var));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 103));
		_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* L_1 = V_0;
		NullCheck(L_1);
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_2 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)L_1)->___completionSource);
		_EveryUpdate_t1BDE92B21B6FC1B5BDD80BE162E182EC0AFA9310* L_3 = V_0;
		NullCheck(L_3);
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_4 = L_3->___cancellationToken;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 104));
		bool L_5;
		L_5 = UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168(L_2, L_4, UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 104));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 105));
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Timer__ctor_m5ABA17C5028D1F0176E530D20DF37A60119C0D8F (Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5* __this, TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___0_dueTime, Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272 ___1_period, int32_t ___2_updateTiming, bool ___3_ignoreTimeScale, bool ___4_cancelImmediately, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Timer__ctor_m5ABA17C5028D1F0176E530D20DF37A60119C0D8F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_dueTime), (&___1_period), (&___2_updateTiming), (&___3_ignoreTimeScale), (&___4_cancelImmediately));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Timer__ctor_m5ABA17C5028D1F0176E530D20DF37A60119C0D8F_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 106));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 107));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 108));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 109));
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 109));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 110));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 111));
		int32_t L_0 = ___2_updateTiming;
		__this->___updateTiming = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 112));
		TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A L_1 = ___0_dueTime;
		__this->___dueTime = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 113));
		Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272 L_2 = ___1_period;
		__this->___period = L_2;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 114));
		bool L_3 = ___3_ignoreTimeScale;
		__this->___ignoreTimeScale = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 115));
		bool L_4 = ___4_cancelImmediately;
		__this->___cancelImmediately = L_4;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 116));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Timer_GetAsyncEnumerator_mBE61A5FEDB1387353BF4E15E8DBA832BA126913D (Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5* __this, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___0_cancellationToken, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IUniTaskAsyncEnumerator_1_t21358676B8858ABFBD647A323B3FFAB2FC05EA93_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Timer_GetAsyncEnumerator_mBE61A5FEDB1387353BF4E15E8DBA832BA126913D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Timer_t263FFAEC1D68951855ECAD0BB631C1073A2DEFB5_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_t02D4B03E9C868588091570453DD875CF717280FE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_cancellationToken));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Timer_GetAsyncEnumerator_mBE61A5FEDB1387353BF4E15E8DBA832BA126913D_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 117));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 118));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 119));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 120));
		TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A L_0 = __this->___dueTime;
		Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272 L_1 = __this->___period;
		int32_t L_2 = __this->___updateTiming;
		bool L_3 = __this->___ignoreTimeScale;
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_4 = ___0_cancellationToken;
		bool L_5 = __this->___cancelImmediately;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 121));
		_Timer_t02D4B03E9C868588091570453DD875CF717280FE* L_6 = (_Timer_t02D4B03E9C868588091570453DD875CF717280FE*)il2cpp_codegen_object_new(_Timer_t02D4B03E9C868588091570453DD875CF717280FE_il2cpp_TypeInfo_var);
		_Timer__ctor_m0D814AB06478A51B470E2C4A08C3C43F410EE2A2(L_6, L_0, L_1, L_2, L_3, L_4, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 121));
		V_0 = L_6;
		goto IL_0028;
	}

IL_0028:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 122));
		RuntimeObject* L_7 = V_0;
		return L_7;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void _Timer__ctor_m0D814AB06478A51B470E2C4A08C3C43F410EE2A2 (_Timer_t02D4B03E9C868588091570453DD875CF717280FE* __this, TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___0_dueTime, Nullable_1_tE151CE1F6892804B41C4004C95CB57020ABB3272 ___1_period, int32_t ___2_updateTiming, bool ___3_ignoreTimeScale, CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED ___4_cancellationToken, bool ___5_cancelImmediately, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CancellationTokenExtensions_tDCF32020B3569F58EFEE1EE41A992652A6F720E6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_GetValueOrDefault_m068A148705ED1E215A5E85D18BA6852B192DA419_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1__ctor_mF3D65C30ACED71826A2F8078A5D10F3CC827E420_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_HasValue_mD63976C410D14373D6D1D3E713C09B1E152F2166_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_Value_mC7511CCEBD1BB6FEEA7903DBB81ED7290D00DDD3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayerLoopHelper_tA497C2C44D13E21B40E76B01973260BB49C9CF01_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_ctorU3Eb__11_0_mC0ED724BEC945D3F1A6EAC63C6660EE7D87AEF6F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer__ctor_m0D814AB06478A51B470E2C4A08C3C43F410EE2A2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_t02D4B03E9C868588091570453DD875CF717280FE_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A V_0;
	memset((&V_0), 0, sizeof(V_0));
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 V_1;
	memset((&V_1), 0, sizeof(V_1));
	bool V_2 = false;
	bool V_3 = false;
	bool V_4 = false;
	float V_5 = 0.0f;
	bool V_6 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_dueTime), (&___1_period), (&___2_updateTiming), (&___3_ignoreTimeScale), (&___4_cancellationToken), (&___5_cancelImmediately));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _Timer__ctor_m0D814AB06478A51B470E2C4A08C3C43F410EE2A2_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 123));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 124));
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B2_0 = NULL;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B1_0 = NULL;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 G_B3_0;
	memset((&G_B3_0), 0, sizeof(G_B3_0));
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B3_1 = NULL;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B11_0 = NULL;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B10_0 = NULL;
	int32_t G_B12_0 = 0;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B12_1 = NULL;
	int32_t G_B15_0 = 0;
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* G_B18_0 = NULL;
	CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED G_B18_1;
	memset((&G_B18_1), 0, sizeof(G_B18_1));
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B18_2 = NULL;
	Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* G_B17_0 = NULL;
	CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED G_B17_1;
	memset((&G_B17_1), 0, sizeof(G_B17_1));
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B17_2 = NULL;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 125));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 126));
		MoveNextSource__ctor_mAD2FB060DAA5FB4AC333260F502B4FD563133640(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 126));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 127));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 128));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 129));
		il2cpp_codegen_runtime_class_init_inline(TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_il2cpp_TypeInfo_var);
		double L_0;
		L_0 = TimeSpan_get_TotalSeconds_mED686E7CECE6A76A7DC38518698B9199DB8CDEA8((&___0_dueTime), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 129));
		__this->___dueTime = ((float)L_0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 130));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 131));
		bool L_1;
		L_1 = Nullable_1_get_HasValue_mD63976C410D14373D6D1D3E713C09B1E152F2166_inline((&___1_period), Nullable_1_get_HasValue_mD63976C410D14373D6D1D3E713C09B1E152F2166_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 131));
		if (!L_1)
		{
			G_B2_0 = __this;
			goto IL_0037;
		}
		G_B1_0 = __this;
	}
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 132));
		TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A L_2;
		L_2 = Nullable_1_get_Value_mC7511CCEBD1BB6FEEA7903DBB81ED7290D00DDD3((&___1_period), Nullable_1_get_Value_mC7511CCEBD1BB6FEEA7903DBB81ED7290D00DDD3_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 132));
		V_0 = L_2;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 133));
		il2cpp_codegen_runtime_class_init_inline(TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_il2cpp_TypeInfo_var);
		double L_3;
		L_3 = TimeSpan_get_TotalSeconds_mED686E7CECE6A76A7DC38518698B9199DB8CDEA8((&V_0), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 133));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 134));
		Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 L_4;
		memset((&L_4), 0, sizeof(L_4));
		Nullable_1__ctor_mF3D65C30ACED71826A2F8078A5D10F3CC827E420((&L_4), ((float)L_3), Nullable_1__ctor_mF3D65C30ACED71826A2F8078A5D10F3CC827E420_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 134));
		G_B3_0 = L_4;
		G_B3_1 = G_B1_0;
		goto IL_0040;
	}

IL_0037:
	{
		il2cpp_codegen_initobj((&V_1), sizeof(Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75));
		Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 L_5 = V_1;
		G_B3_0 = L_5;
		G_B3_1 = G_B2_0;
	}

IL_0040:
	{
		NullCheck(G_B3_1);
		G_B3_1->___period = G_B3_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 135));
		float L_6 = __this->___dueTime;
		V_2 = (bool)((((int32_t)((!(((float)L_6) <= ((float)(0.0f))))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 136));
		bool L_7 = V_2;
		if (!L_7)
		{
			goto IL_0064;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 137));
		__this->___dueTime = (0.0f);
	}

IL_0064:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 138));
		Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75* L_8 = (Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75*)(&__this->___period);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 139));
		bool L_9;
		L_9 = Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_inline(L_8, Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 139));
		V_3 = L_9;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 140));
		bool L_10 = V_3;
		if (!L_10)
		{
			goto IL_00ac;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 141));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 142));
		Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 L_11 = __this->___period;
		V_1 = L_11;
		V_5 = (0.0f);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 143));
		float L_12;
		L_12 = Nullable_1_GetValueOrDefault_m068A148705ED1E215A5E85D18BA6852B192DA419_inline((&V_1), Nullable_1_GetValueOrDefault_m068A148705ED1E215A5E85D18BA6852B192DA419_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 143));
		float L_13 = V_5;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 144));
		bool L_14;
		L_14 = Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_inline((&V_1), Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 144));
		V_4 = (bool)((int32_t)(((((int32_t)((!(((float)L_12) <= ((float)L_13)))? 1 : 0)) == ((int32_t)0))? 1 : 0)&(int32_t)L_14));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 145));
		bool L_15 = V_4;
		if (!L_15)
		{
			goto IL_00ab;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 146));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 147));
		Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 L_16;
		memset((&L_16), 0, sizeof(L_16));
		Nullable_1__ctor_mF3D65C30ACED71826A2F8078A5D10F3CC827E420((&L_16), ((float)1), Nullable_1__ctor_mF3D65C30ACED71826A2F8078A5D10F3CC827E420_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 147));
		__this->___period = L_16;
	}

IL_00ab:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 148));
	}

IL_00ac:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 149));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 150));
		il2cpp_codegen_runtime_class_init_inline(PlayerLoopHelper_tA497C2C44D13E21B40E76B01973260BB49C9CF01_il2cpp_TypeInfo_var);
		bool L_17;
		L_17 = PlayerLoopHelper_get_IsMainThread_mC32C7D62B20488CD0BA2F337EBFEC8128086DD5A(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 150));
		if (L_17)
		{
			G_B11_0 = __this;
			goto IL_00b7;
		}
		G_B10_0 = __this;
	}
	{
		G_B12_0 = (-1);
		G_B12_1 = G_B10_0;
		goto IL_00bc;
	}

IL_00b7:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 151));
		int32_t L_18;
		L_18 = Time_get_frameCount_m4A42E558A71301A216BDC49EC402D62F19C79667(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 151));
		G_B12_0 = L_18;
		G_B12_1 = G_B11_0;
	}

IL_00bc:
	{
		NullCheck(G_B12_1);
		G_B12_1->___initialFrame = G_B12_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 152));
		__this->___dueTimePhase = (bool)1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 153));
		int32_t L_19 = ___2_updateTiming;
		__this->___updateTiming = L_19;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 154));
		bool L_20 = ___3_ignoreTimeScale;
		__this->___ignoreTimeScale = L_20;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 155));
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_21 = ___4_cancellationToken;
		__this->___cancellationToken = L_21;
		Il2CppCodeGenWriteBarrier((void**)&(((&__this->___cancellationToken))->____source), (void*)NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 156));
		bool L_22 = ___5_cancelImmediately;
		if (!L_22)
		{
			goto IL_00ec;
		}
	}
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 157));
		il2cpp_codegen_runtime_class_init_inline(CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		bool L_23;
		L_23 = CancellationToken_get_CanBeCanceled_mC3751330B171DB14B70B9BAAD90A7D098A2309EC((&___4_cancellationToken), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 157));
		G_B15_0 = ((int32_t)(L_23));
		goto IL_00ed;
	}

IL_00ec:
	{
		G_B15_0 = 0;
	}

IL_00ed:
	{
		V_6 = (bool)G_B15_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 158));
		bool L_24 = V_6;
		if (!L_24)
		{
			goto IL_0122;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 159));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 160));
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_25 = ___4_cancellationToken;
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var);
		Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* L_26 = ((U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var))->___U3CU3E9__11_0;
		Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* L_27 = L_26;
		if (L_27)
		{
			G_B18_0 = L_27;
			G_B18_1 = L_25;
			G_B18_2 = __this;
			goto IL_0116;
		}
		G_B17_0 = L_27;
		G_B17_1 = L_25;
		G_B17_2 = __this;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var);
		U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77* L_28 = ((U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var))->___U3CU3E9;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 161));
		Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* L_29 = (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87*)il2cpp_codegen_object_new(Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87_il2cpp_TypeInfo_var);
		Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4(L_29, L_28, (intptr_t)((void*)U3CU3Ec_U3C_ctorU3Eb__11_0_mC0ED724BEC945D3F1A6EAC63C6660EE7D87AEF6F_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 161));
		Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* L_30 = L_29;
		((U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var))->___U3CU3E9__11_0 = L_30;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var))->___U3CU3E9__11_0), (void*)L_30);
		G_B18_0 = L_30;
		G_B18_1 = G_B17_1;
		G_B18_2 = G_B17_2;
	}

IL_0116:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 162));
		il2cpp_codegen_runtime_class_init_inline(CancellationTokenExtensions_tDCF32020B3569F58EFEE1EE41A992652A6F720E6_il2cpp_TypeInfo_var);
		CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389 L_31;
		L_31 = CancellationTokenExtensions_RegisterWithoutCaptureExecutionContext_mA5A09F053F6E50AD047E0DB08666E4270863AC69(G_B18_1, G_B18_0, __this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 162));
		NullCheck(G_B18_2);
		G_B18_2->___cancellationTokenRegistration = L_31;
		Il2CppCodeGenWriteBarrier((void**)&(((&G_B18_2->___cancellationTokenRegistration))->___m_callbackInfo), (void*)NULL);
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((&(((&G_B18_2->___cancellationTokenRegistration))->___m_registrationInfo))->____source), (void*)NULL);
		#endif
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 163));
	}

IL_0122:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 164));
		int32_t L_32 = ___2_updateTiming;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 165));
		il2cpp_codegen_runtime_class_init_inline(PlayerLoopHelper_tA497C2C44D13E21B40E76B01973260BB49C9CF01_il2cpp_TypeInfo_var);
		PlayerLoopHelper_AddAction_mAD5D5DA039BE42ECF032A1835BD95C318D6ED22D(L_32, __this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 165));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 166));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A _Timer_get_Current_m1DFFF2DB3E1FDA56BBC2BD8973DEAA81946BA498 (_Timer_t02D4B03E9C868588091570453DD875CF717280FE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_get_Current_m1DFFF2DB3E1FDA56BBC2BD8973DEAA81946BA498_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_t02D4B03E9C868588091570453DD875CF717280FE_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _Timer_get_Current_m1DFFF2DB3E1FDA56BBC2BD8973DEAA81946BA498_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 167));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 168));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 169));
		il2cpp_codegen_initobj((&V_0), sizeof(AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A));
		AsyncUnit_t28ED8F3D14C76244CF24D9C7A627585A9EC7491A L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 _Timer_MoveNextAsync_m1C536CA3782940F8CDA20FE36160B2130B9DA833 (_Timer_t02D4B03E9C868588091570453DD875CF717280FE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_Reset_m11E75ED2E4CC547CB5A568C0063665353803495D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_get_Version_m49DEF08C91978DDFDE6A27F0C2B6630BB48116C5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_1__ctor_mE4AE554655B64D8F20BD92C5C769A9E39167D200_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_MoveNextAsync_m1C536CA3782940F8CDA20FE36160B2130B9DA833_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_t02D4B03E9C868588091570453DD875CF717280FE_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 V_1;
	memset((&V_1), 0, sizeof(V_1));
	bool V_2 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _Timer_MoveNextAsync_m1C536CA3782940F8CDA20FE36160B2130B9DA833_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 170));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 171));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 172));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 173));
		bool L_0 = __this->___disposed;
		if (L_0)
		{
			goto IL_0011;
		}
	}
	{
		bool L_1 = __this->___completed;
		G_B3_0 = ((int32_t)(L_1));
		goto IL_0012;
	}

IL_0011:
	{
		G_B3_0 = 1;
	}

IL_0012:
	{
		V_0 = (bool)G_B3_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 174));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001e;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 175));
		il2cpp_codegen_runtime_class_init_inline(CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_il2cpp_TypeInfo_var);
		UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 L_3 = ((CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_StaticFields*)il2cpp_codegen_static_fields_for(CompletedTasks_t3D6A58059BB55C44DE470DE0D29C78D1817DB19B_il2cpp_TypeInfo_var))->___False;
		V_1 = L_3;
		goto IL_006c;
	}

IL_001e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 176));
		__this->___elapsed = (0.0f);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 177));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_4 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 178));
		UniTaskCompletionSourceCore_1_Reset_m11E75ED2E4CC547CB5A568C0063665353803495D(L_4, UniTaskCompletionSourceCore_1_Reset_m11E75ED2E4CC547CB5A568C0063665353803495D_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 178));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 179));
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED* L_5 = (CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED*)(&__this->___cancellationToken);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 180));
		il2cpp_codegen_runtime_class_init_inline(CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		bool L_6;
		L_6 = CancellationToken_get_IsCancellationRequested_m9744F7A1A82946FDD1DC68E905F1ED826471D350(L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 180));
		V_2 = L_6;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 181));
		bool L_7 = V_2;
		if (!L_7)
		{
			goto IL_0058;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 182));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 183));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_8 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_9 = __this->___cancellationToken;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 184));
		bool L_10;
		L_10 = UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168(L_8, L_9, UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 184));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 185));
	}

IL_0058:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 186));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_11 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 187));
		int16_t L_12;
		L_12 = UniTaskCompletionSourceCore_1_get_Version_m49DEF08C91978DDFDE6A27F0C2B6630BB48116C5_inline(L_11, UniTaskCompletionSourceCore_1_get_Version_m49DEF08C91978DDFDE6A27F0C2B6630BB48116C5_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 187));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 188));
		UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 L_13;
		memset((&L_13), 0, sizeof(L_13));
		UniTask_1__ctor_mE4AE554655B64D8F20BD92C5C769A9E39167D200_inline((&L_13), __this, L_12, UniTask_1__ctor_mE4AE554655B64D8F20BD92C5C769A9E39167D200_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 188));
		V_1 = L_13;
		goto IL_006c;
	}

IL_006c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 189));
		UniTask_1_tE29E6E2A7DE0BF1051805ACEFA412937BC430949 L_14 = V_1;
		return L_14;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 _Timer_DisposeAsync_mAAF11D3D9223205036F0695E5A6E4DA4C092B565 (_Timer_t02D4B03E9C868588091570453DD875CF717280FE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_DisposeAsync_mAAF11D3D9223205036F0695E5A6E4DA4C092B565_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_t02D4B03E9C868588091570453DD875CF717280FE_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 V_1;
	memset((&V_1), 0, sizeof(V_1));
	UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 V_2;
	memset((&V_2), 0, sizeof(V_2));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _Timer_DisposeAsync_mAAF11D3D9223205036F0695E5A6E4DA4C092B565_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 190));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 191));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 192));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 193));
		bool L_0 = __this->___disposed;
		V_0 = (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 194));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0023;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 195));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 196));
		CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389* L_2 = (CancellationTokenRegistration_tC925A8BC86C629A2A3DA73765FA964A95FC83389*)(&__this->___cancellationTokenRegistration);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 197));
		CancellationTokenRegistration_Dispose_m9EAF1228573E8278DDF7A3BEB5EE0E18DA6DC0E1(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 197));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 198));
		__this->___disposed = (bool)1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 199));
	}

IL_0023:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 200));
		il2cpp_codegen_initobj((&V_1), sizeof(UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270));
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 L_3 = V_1;
		V_2 = L_3;
		goto IL_002f;
	}

IL_002f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 201));
		UniTask_t8E1453C1D8424B1FC22B0E51B017D3B028E17270 L_4 = V_2;
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool _Timer_MoveNext_mE978E92C7BCABBE941ECAACA29BDEC938A740BA4 (_Timer_t02D4B03E9C868588091570453DD875CF717280FE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_GetValueOrDefault_m068A148705ED1E215A5E85D18BA6852B192DA419_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_MoveNext_mE978E92C7BCABBE941ECAACA29BDEC938A740BA4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_t02D4B03E9C868588091570453DD875CF717280FE_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	bool V_3 = false;
	bool V_4 = false;
	bool V_5 = false;
	bool V_6 = false;
	bool V_7 = false;
	bool V_8 = false;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 V_9;
	memset((&V_9), 0, sizeof(V_9));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, _Timer_MoveNext_mE978E92C7BCABBE941ECAACA29BDEC938A740BA4_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 202));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 203));
	float G_B11_0 = 0.0f;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B11_1 = NULL;
	float G_B10_0 = 0.0f;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B10_1 = NULL;
	float G_B12_0 = 0.0f;
	float G_B12_1 = 0.0f;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B12_2 = NULL;
	float G_B19_0 = 0.0f;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B19_1 = NULL;
	float G_B18_0 = 0.0f;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B18_1 = NULL;
	float G_B20_0 = 0.0f;
	float G_B20_1 = 0.0f;
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* G_B20_2 = NULL;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 204));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 205));
		bool L_0 = __this->___disposed;
		V_0 = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 206));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0020;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 207));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 208));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_2 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 209));
		bool L_3;
		L_3 = UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037(L_2, (bool)0, UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 209));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 210));
		V_1 = (bool)0;
		goto IL_0166;
	}

IL_0020:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 211));
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED* L_4 = (CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED*)(&__this->___cancellationToken);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 212));
		il2cpp_codegen_runtime_class_init_inline(CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = CancellationToken_get_IsCancellationRequested_m9744F7A1A82946FDD1DC68E905F1ED826471D350(L_4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 212));
		V_2 = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 213));
		bool L_6 = V_2;
		if (!L_6)
		{
			goto IL_0049;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 214));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 215));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_7 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_8 = __this->___cancellationToken;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 216));
		bool L_9;
		L_9 = UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168(L_7, L_8, UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 216));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 217));
		V_1 = (bool)0;
		goto IL_0166;
	}

IL_0049:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 218));
		bool L_10 = __this->___dueTimePhase;
		V_3 = L_10;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 219));
		bool L_11 = V_3;
		if (!L_11)
		{
			goto IL_00db;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 220));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 221));
		float L_12 = __this->___elapsed;
		V_4 = (bool)((((float)L_12) == ((float)(0.0f)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 222));
		bool L_13 = V_4;
		if (!L_13)
		{
			goto IL_0087;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 223));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 224));
		int32_t L_14 = __this->___initialFrame;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 225));
		int32_t L_15;
		L_15 = Time_get_frameCount_m4A42E558A71301A216BDC49EC402D62F19C79667(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 225));
		V_5 = (bool)((((int32_t)L_14) == ((int32_t)L_15))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 226));
		bool L_16 = V_5;
		if (!L_16)
		{
			goto IL_0086;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 227));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 228));
		V_1 = (bool)1;
		goto IL_0166;
	}

IL_0086:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 229));
	}

IL_0087:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 230));
		float L_17 = __this->___elapsed;
		bool L_18 = __this->___ignoreTimeScale;
		if (L_18)
		{
			G_B11_0 = L_17;
			G_B11_1 = __this;
			goto IL_009d;
		}
		G_B10_0 = L_17;
		G_B10_1 = __this;
	}
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 231));
		float L_19;
		L_19 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 231));
		G_B12_0 = L_19;
		G_B12_1 = G_B10_0;
		G_B12_2 = G_B10_1;
		goto IL_00a2;
	}

IL_009d:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 232));
		float L_20;
		L_20 = Time_get_unscaledDeltaTime_mF057EECA857E5C0F90A3F910D26D3EE59F27C4B5(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 232));
		G_B12_0 = L_20;
		G_B12_1 = G_B11_0;
		G_B12_2 = G_B11_1;
	}

IL_00a2:
	{
		NullCheck(G_B12_2);
		G_B12_2->___elapsed = ((float)il2cpp_codegen_add(G_B12_1, G_B12_0));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 233));
		float L_21 = __this->___elapsed;
		float L_22 = __this->___dueTime;
		V_6 = (bool)((((int32_t)((!(((float)L_21) >= ((float)L_22)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 234));
		bool L_23 = V_6;
		if (!L_23)
		{
			goto IL_00d5;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 235));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 236));
		__this->___dueTimePhase = (bool)0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 237));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_24 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 238));
		bool L_25;
		L_25 = UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037(L_24, (bool)1, UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 238));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 239));
	}

IL_00d5:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 240));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 241));
		goto IL_0162;
	}

IL_00db:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 242));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 243));
		Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75* L_26 = (Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75*)(&__this->___period);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 244));
		bool L_27;
		L_27 = Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_inline(L_26, Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 244));
		V_7 = (bool)((((int32_t)L_27) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 245));
		bool L_28 = V_7;
		if (!L_28)
		{
			goto IL_0109;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 246));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 247));
		__this->___completed = (bool)1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 248));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_29 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 249));
		bool L_30;
		L_30 = UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037(L_29, (bool)0, UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 249));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 250));
		V_1 = (bool)0;
		goto IL_0166;
	}

IL_0109:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 251));
		float L_31 = __this->___elapsed;
		bool L_32 = __this->___ignoreTimeScale;
		if (L_32)
		{
			G_B19_0 = L_31;
			G_B19_1 = __this;
			goto IL_011f;
		}
		G_B18_0 = L_31;
		G_B18_1 = __this;
	}
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 252));
		float L_33;
		L_33 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 252));
		G_B20_0 = L_33;
		G_B20_1 = G_B18_0;
		G_B20_2 = G_B18_1;
		goto IL_0124;
	}

IL_011f:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 253));
		float L_34;
		L_34 = Time_get_unscaledDeltaTime_mF057EECA857E5C0F90A3F910D26D3EE59F27C4B5(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 253));
		G_B20_0 = L_34;
		G_B20_1 = G_B19_0;
		G_B20_2 = G_B19_1;
	}

IL_0124:
	{
		NullCheck(G_B20_2);
		G_B20_2->___elapsed = ((float)il2cpp_codegen_add(G_B20_1, G_B20_0));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 254));
		float L_35 = __this->___elapsed;
		Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 L_36 = __this->___period;
		V_9 = L_36;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 255));
		float L_37;
		L_37 = Nullable_1_GetValueOrDefault_m068A148705ED1E215A5E85D18BA6852B192DA419_inline((&V_9), Nullable_1_GetValueOrDefault_m068A148705ED1E215A5E85D18BA6852B192DA419_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 255));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 256));
		bool L_38;
		L_38 = Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_inline((&V_9), Nullable_1_get_HasValue_mC149B1C717AF506BBE8932F2C1DC86C378D17EA8_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 256));
		V_8 = (bool)((int32_t)(((((int32_t)((!(((float)L_35) >= ((float)L_37)))? 1 : 0)) == ((int32_t)0))? 1 : 0)&(int32_t)L_38));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 257));
		bool L_39 = V_8;
		if (!L_39)
		{
			goto IL_0161;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 258));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 259));
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_40 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)__this)->___completionSource);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 260));
		bool L_41;
		L_41 = UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037(L_40, (bool)1, UniTaskCompletionSourceCore_1_TrySetResult_m4AD85964381E8B6B5D92F6747C81FCF5E9CF3037_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 260));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 261));
	}

IL_0161:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 262));
	}

IL_0162:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 263));
		V_1 = (bool)1;
		goto IL_0166;
	}

IL_0166:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 264));
		bool L_42 = V_1;
		return L_42;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_mC3A825E1D0D47B2DF7DC8E84857ED248A7601865 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__cctor_mC3A825E1D0D47B2DF7DC8E84857ED248A7601865_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec__cctor_mC3A825E1D0D47B2DF7DC8E84857ED248A7601865_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77* L_0 = (U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77*)il2cpp_codegen_object_new(U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_m159C954281DAB83E3033E723DFB83AB2CDAAEFF5(L_0, NULL);
		((U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m159C954281DAB83E3033E723DFB83AB2CDAAEFF5 (U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__ctor_m159C954281DAB83E3033E723DFB83AB2CDAAEFF5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec__ctor_m159C954281DAB83E3033E723DFB83AB2CDAAEFF5_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec_U3C_ctorU3Eb__11_0_mC0ED724BEC945D3F1A6EAC63C6660EE7D87AEF6F (U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77* __this, RuntimeObject* ___0_state, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_ctorU3Eb__11_0_mC0ED724BEC945D3F1A6EAC63C6660EE7D87AEF6F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tE019F895E3EC2750F0145C152E501C6B241F1E77_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_t02D4B03E9C868588091570453DD875CF717280FE_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_Timer_t02D4B03E9C868588091570453DD875CF717280FE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	_Timer_t02D4B03E9C868588091570453DD875CF717280FE* V_0 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_state));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec_U3C_ctorU3Eb__11_0_mC0ED724BEC945D3F1A6EAC63C6660EE7D87AEF6F_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 265));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask_Linq + 266));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 267));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 268));
		RuntimeObject* L_0 = ___0_state;
		V_0 = ((_Timer_t02D4B03E9C868588091570453DD875CF717280FE*)CastclassClass((RuntimeObject*)L_0, _Timer_t02D4B03E9C868588091570453DD875CF717280FE_il2cpp_TypeInfo_var));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 269));
		_Timer_t02D4B03E9C868588091570453DD875CF717280FE* L_1 = V_0;
		NullCheck(L_1);
		UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2* L_2 = (UniTaskCompletionSourceCore_1_t9AED094DEF9EF1E050603D158F4FFA169DE3E9D2*)(&((MoveNextSource_tEE80ED59B6626D9814CD4A1DDD53D32F0A9495B5*)L_1)->___completionSource);
		_Timer_t02D4B03E9C868588091570453DD875CF717280FE* L_3 = V_0;
		NullCheck(L_3);
		CancellationToken_t51142D9C6D7C02D314DA34A6A7988C528992FFED L_4 = L_3->___cancellationToken;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 270));
		bool L_5;
		L_5 = UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168(L_2, L_4, UniTaskCompletionSourceCore_1_TrySetCanceled_mD66F5F925074EA6E22B0422A10F073A1447C7168_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 270));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask_Linq + 271));
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int16_t UniTaskCompletionSourceCore_1_get_Version_mC206FEA615DEB3676B72991ABAE79848523CAC0B_gshared_inline (UniTaskCompletionSourceCore_1_t146409427AB3F018819917FFA1FDC70FDB5C0BBF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTaskCompletionSourceCore_1_t7DADC575C5ED6A5929DB19A1EBFC3DA4A86BC202_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, method, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask + 2770));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask + 2771));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask + 2772));
		int16_t L_0 = *(int16_t*)il2cpp_codegen_get_instance_field_data_pointer(__this, il2cpp_rgctx_field(il2cpp_rgctx_data_no_init(InitializedTypeInfo(method->klass)->rgctx_data, 2),2));
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void UniTask_1__ctor_m608BBDBA054799FF72D4ED7758DA9BA32EBB8F22_gshared_inline (UniTask_1_t462EA7468BEE3A822B4D759A9930C6DF4DED6483* __this, RuntimeObject* ___0_source, int16_t ___1_token, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniTask_1_t9BDB4A2D84D7D834CF4ADFBE0BE59E9A38B3A1B4_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	const uint32_t SizeOf_T_tFF5A4BDB19D1BCCEC9E3BFEC979335AAEF368430 = il2cpp_codegen_sizeof(il2cpp_rgctx_data_no_init(InitializedTypeInfo(method->klass)->rgctx_data, 2));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_source), (&___1_token));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, method, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask + 2429));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUniTask + 2430));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask + 2431));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask + 2432));
		RuntimeObject* L_0 = ___0_source;
		il2cpp_codegen_write_instance_field_data<RuntimeObject*>(__this, il2cpp_rgctx_field(il2cpp_rgctx_data_no_init(InitializedTypeInfo(method->klass)->rgctx_data, 0),0), L_0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask + 2433));
		int16_t L_1 = ___1_token;
		il2cpp_codegen_write_instance_field_data<int16_t>(__this, il2cpp_rgctx_field(il2cpp_rgctx_data_no_init(InitializedTypeInfo(method->klass)->rgctx_data, 0),2), L_1);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask + 2434));
		il2cpp_codegen_initobj((((Il2CppFullySharedGenericAny*)il2cpp_codegen_get_instance_field_data_pointer(__this, il2cpp_rgctx_field(il2cpp_rgctx_data_no_init(InitializedTypeInfo(method->klass)->rgctx_data, 0),1)))), SizeOf_T_tFF5A4BDB19D1BCCEC9E3BFEC979335AAEF368430);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUniTask + 2435));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Nullable_1_get_HasValue_m14F273FB376DF00D727434CDCD28AB4EDCC14C3C_gshared_inline (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339* __this, const RuntimeMethod* method) 
{
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, method, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		bool L_0 = *(bool*)il2cpp_codegen_get_instance_field_data_pointer(__this, il2cpp_rgctx_field(il2cpp_rgctx_data_no_init(InitializedTypeInfo(method->klass)->rgctx_data, 1),0));
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Nullable_1_GetValueOrDefault_mC057FBD944AF068B90EBDD0B496231A01B2A4228_gshared_inline (Nullable_1_t71C4EA4E848DBD7A4A97704069FB951159A3A339* __this, Il2CppFullySharedGenericStruct* il2cppRetVal, const RuntimeMethod* method) 
{
	const uint32_t SizeOf_T_tF1352992D99D495C6F2BE3DC43F8A00BAD6B2EAA = il2cpp_codegen_sizeof(il2cpp_rgctx_data_no_init(InitializedTypeInfo(method->klass)->rgctx_data, 0));
	const Il2CppFullySharedGenericStruct L_0 = alloca(SizeOf_T_tF1352992D99D495C6F2BE3DC43F8A00BAD6B2EAA);
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, method, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		il2cpp_codegen_memcpy(L_0, il2cpp_codegen_get_instance_field_data_pointer(__this, il2cpp_rgctx_field(il2cpp_rgctx_data_no_init(InitializedTypeInfo(method->klass)->rgctx_data, 1),1)), SizeOf_T_tF1352992D99D495C6F2BE3DC43F8A00BAD6B2EAA);
		il2cpp_codegen_memcpy(il2cppRetVal, L_0, SizeOf_T_tF1352992D99D495C6F2BE3DC43F8A00BAD6B2EAA);
		return;
	}
}
