﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[73] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_ClothModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ClothModule[55] = 
{
	{ 109521, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109521, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109521, 1, 17, 17, 39, 43, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109522, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 3 },
	{ 109522, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 4 },
	{ 109522, 1, 17, 17, 44, 48, 0, kSequencePointKind_Normal, 0, 5 },
	{ 109523, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 109523, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 109523, 1, 18, 18, 40, 44, 0, kSequencePointKind_Normal, 0, 8 },
	{ 109524, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 9 },
	{ 109524, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 10 },
	{ 109524, 1, 18, 18, 45, 49, 0, kSequencePointKind_Normal, 0, 11 },
	{ 109525, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 109525, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 109525, 1, 21, 21, 9, 10, 0, kSequencePointKind_Normal, 0, 14 },
	{ 109525, 1, 23, 23, 13, 23, 1, kSequencePointKind_Normal, 0, 15 },
	{ 109525, 1, 23, 23, 13, 23, 3, kSequencePointKind_StepOut, 0, 16 },
	{ 109525, 1, 24, 24, 13, 27, 9, kSequencePointKind_Normal, 0, 17 },
	{ 109525, 1, 24, 24, 13, 27, 11, kSequencePointKind_StepOut, 0, 18 },
	{ 109525, 1, 25, 25, 9, 10, 17, kSequencePointKind_Normal, 0, 19 },
	{ 109526, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 20 },
	{ 109526, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 21 },
	{ 109526, 1, 28, 28, 9, 10, 0, kSequencePointKind_Normal, 0, 22 },
	{ 109526, 1, 30, 30, 13, 23, 1, kSequencePointKind_Normal, 0, 23 },
	{ 109526, 1, 30, 30, 13, 23, 3, kSequencePointKind_StepOut, 0, 24 },
	{ 109526, 1, 31, 31, 13, 24, 9, kSequencePointKind_Normal, 0, 25 },
	{ 109526, 1, 31, 31, 13, 24, 11, kSequencePointKind_StepOut, 0, 26 },
	{ 109526, 1, 32, 32, 9, 10, 17, kSequencePointKind_Normal, 0, 27 },
	{ 109565, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 28 },
	{ 109565, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 29 },
	{ 109565, 1, 102, 102, 17, 18, 0, kSequencePointKind_Normal, 0, 30 },
	{ 109565, 1, 102, 102, 19, 54, 1, kSequencePointKind_Normal, 0, 31 },
	{ 109565, 1, 102, 102, 19, 54, 2, kSequencePointKind_StepOut, 0, 32 },
	{ 109565, 1, 102, 102, 55, 56, 17, kSequencePointKind_Normal, 0, 33 },
	{ 109566, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 34 },
	{ 109566, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 35 },
	{ 109566, 1, 103, 103, 17, 18, 0, kSequencePointKind_Normal, 0, 36 },
	{ 109566, 1, 103, 103, 19, 70, 1, kSequencePointKind_Normal, 0, 37 },
	{ 109566, 1, 103, 103, 19, 70, 17, kSequencePointKind_StepOut, 0, 38 },
	{ 109566, 1, 103, 103, 71, 72, 23, kSequencePointKind_Normal, 0, 39 },
	{ 109582, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 40 },
	{ 109582, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 41 },
	{ 109582, 1, 129, 129, 47, 51, 0, kSequencePointKind_Normal, 0, 42 },
	{ 109583, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 43 },
	{ 109583, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 44 },
	{ 109583, 1, 129, 129, 52, 56, 0, kSequencePointKind_Normal, 0, 45 },
	{ 109584, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 46 },
	{ 109584, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 47 },
	{ 109584, 1, 132, 132, 37, 41, 0, kSequencePointKind_Normal, 0, 48 },
	{ 109586, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 49 },
	{ 109586, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 50 },
	{ 109586, 1, 138, 138, 9, 10, 0, kSequencePointKind_Normal, 0, 51 },
	{ 109586, 1, 139, 139, 13, 45, 1, kSequencePointKind_Normal, 0, 52 },
	{ 109586, 1, 139, 139, 13, 45, 8, kSequencePointKind_StepOut, 0, 53 },
	{ 109586, 1, 140, 140, 9, 10, 14, kSequencePointKind_Normal, 0, 54 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_ClothModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ClothModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Cloth/Cloth.bindings.cs", { 141, 87, 253, 135, 182, 57, 213, 209, 221, 159, 178, 225, 126, 171, 92, 217} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[2] = 
{
	{ 14084, 1 },
	{ 14086, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[1] = 
{
	{ 0, 19 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[73] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 19, 0, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ClothModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ClothModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	55,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_ClothModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	2,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
