﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct String_t;
struct UnityConnectSettings_t70F8B3FFCEA5C5B6385372A9A139D12A1C798EDA;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_UnityConnectModule[];
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* UnityAdsSettings_IsPlatformEnabled_m5544575971444B4D60A66E75872DB99D04F6CA2F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UnityAdsSettings_SetPlatformEnabled_mE15C6EBB1CA537EE9477CA5576C342320D560551_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UnityConnectSettings__ctor_m52B180993E2FBFBA90A679B119590FDA83E0D2AB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t20BC6C5E4D80C29A6E6E6B839565A4DD9D5299DD 
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct UnityAdsSettings_t46C33C4A101BF17C033F6D52FAD89245B50B20CE  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct RuntimePlatform_t9A8AAF204603076FCAAECCCC05DA386AEE7BF66E 
{
	int32_t ___value__;
};
struct UnityConnectSettings_t70F8B3FFCEA5C5B6385372A9A139D12A1C798EDA  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m2149FA40CEC8D82AC20D3508AB40C0D8EFEF68E6 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool UnityConnectSettings_get_enabled_m867E6927DE3787A5259CCD98C70D5CDC6B36ADA5 (const RuntimeMethod* method) 
{
	typedef bool (*UnityConnectSettings_get_enabled_m867E6927DE3787A5259CCD98C70D5CDC6B36ADA5_ftn) ();
	static UnityConnectSettings_get_enabled_m867E6927DE3787A5259CCD98C70D5CDC6B36ADA5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_get_enabled_m867E6927DE3787A5259CCD98C70D5CDC6B36ADA5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::get_enabled()");
	bool icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityConnectSettings_set_enabled_mB696F73FD98AB871072E3D8134EB4D9B549E76A7 (bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*UnityConnectSettings_set_enabled_mB696F73FD98AB871072E3D8134EB4D9B549E76A7_ftn) (bool);
	static UnityConnectSettings_set_enabled_mB696F73FD98AB871072E3D8134EB4D9B549E76A7_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_set_enabled_mB696F73FD98AB871072E3D8134EB4D9B549E76A7_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::set_enabled(System.Boolean)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool UnityConnectSettings_get_testMode_mDFAFDB7CF6591A3C1175264BA690DB215EDA5D46 (const RuntimeMethod* method) 
{
	typedef bool (*UnityConnectSettings_get_testMode_mDFAFDB7CF6591A3C1175264BA690DB215EDA5D46_ftn) ();
	static UnityConnectSettings_get_testMode_mDFAFDB7CF6591A3C1175264BA690DB215EDA5D46_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_get_testMode_mDFAFDB7CF6591A3C1175264BA690DB215EDA5D46_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::get_testMode()");
	bool icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityConnectSettings_set_testMode_m69C03E750DF3B2F03910C78F3285E40FD545B475 (bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*UnityConnectSettings_set_testMode_m69C03E750DF3B2F03910C78F3285E40FD545B475_ftn) (bool);
	static UnityConnectSettings_set_testMode_m69C03E750DF3B2F03910C78F3285E40FD545B475_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_set_testMode_m69C03E750DF3B2F03910C78F3285E40FD545B475_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::set_testMode(System.Boolean)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* UnityConnectSettings_get_eventUrl_m4F530A4F56AEB2421110E8E5490A2224D3D9AC62 (const RuntimeMethod* method) 
{
	typedef String_t* (*UnityConnectSettings_get_eventUrl_m4F530A4F56AEB2421110E8E5490A2224D3D9AC62_ftn) ();
	static UnityConnectSettings_get_eventUrl_m4F530A4F56AEB2421110E8E5490A2224D3D9AC62_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_get_eventUrl_m4F530A4F56AEB2421110E8E5490A2224D3D9AC62_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::get_eventUrl()");
	String_t* icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityConnectSettings_set_eventUrl_mA85C06C998DEE553E85E1553C460315CB6C0392B (String_t* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*UnityConnectSettings_set_eventUrl_mA85C06C998DEE553E85E1553C460315CB6C0392B_ftn) (String_t*);
	static UnityConnectSettings_set_eventUrl_mA85C06C998DEE553E85E1553C460315CB6C0392B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_set_eventUrl_mA85C06C998DEE553E85E1553C460315CB6C0392B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::set_eventUrl(System.String)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* UnityConnectSettings_get_eventOldUrl_mC6F81A1E6394386CD5C2537E5431AE7FC803333B (const RuntimeMethod* method) 
{
	typedef String_t* (*UnityConnectSettings_get_eventOldUrl_mC6F81A1E6394386CD5C2537E5431AE7FC803333B_ftn) ();
	static UnityConnectSettings_get_eventOldUrl_mC6F81A1E6394386CD5C2537E5431AE7FC803333B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_get_eventOldUrl_mC6F81A1E6394386CD5C2537E5431AE7FC803333B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::get_eventOldUrl()");
	String_t* icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityConnectSettings_set_eventOldUrl_m12CD35CA5AF9D3FC92D4917FA8E521CAF70AEA02 (String_t* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*UnityConnectSettings_set_eventOldUrl_m12CD35CA5AF9D3FC92D4917FA8E521CAF70AEA02_ftn) (String_t*);
	static UnityConnectSettings_set_eventOldUrl_m12CD35CA5AF9D3FC92D4917FA8E521CAF70AEA02_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_set_eventOldUrl_m12CD35CA5AF9D3FC92D4917FA8E521CAF70AEA02_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::set_eventOldUrl(System.String)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* UnityConnectSettings_get_configUrl_m13D4E12D95DB2135570B8D774EDBB57572640C70 (const RuntimeMethod* method) 
{
	typedef String_t* (*UnityConnectSettings_get_configUrl_m13D4E12D95DB2135570B8D774EDBB57572640C70_ftn) ();
	static UnityConnectSettings_get_configUrl_m13D4E12D95DB2135570B8D774EDBB57572640C70_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_get_configUrl_m13D4E12D95DB2135570B8D774EDBB57572640C70_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::get_configUrl()");
	String_t* icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityConnectSettings_set_configUrl_mAF280198CC19DC82B1A0078B455D24031918AABC (String_t* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*UnityConnectSettings_set_configUrl_mAF280198CC19DC82B1A0078B455D24031918AABC_ftn) (String_t*);
	static UnityConnectSettings_set_configUrl_mAF280198CC19DC82B1A0078B455D24031918AABC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_set_configUrl_mAF280198CC19DC82B1A0078B455D24031918AABC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::set_configUrl(System.String)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t UnityConnectSettings_get_testInitMode_m127F04D899DDDEF0AE5A2B986B2414849A3DC3C3 (const RuntimeMethod* method) 
{
	typedef int32_t (*UnityConnectSettings_get_testInitMode_m127F04D899DDDEF0AE5A2B986B2414849A3DC3C3_ftn) ();
	static UnityConnectSettings_get_testInitMode_m127F04D899DDDEF0AE5A2B986B2414849A3DC3C3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_get_testInitMode_m127F04D899DDDEF0AE5A2B986B2414849A3DC3C3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::get_testInitMode()");
	int32_t icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityConnectSettings_set_testInitMode_m8B90D81B0ABAC31965FB939721320F082A81A59D (int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*UnityConnectSettings_set_testInitMode_m8B90D81B0ABAC31965FB939721320F082A81A59D_ftn) (int32_t);
	static UnityConnectSettings_set_testInitMode_m8B90D81B0ABAC31965FB939721320F082A81A59D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityConnectSettings_set_testInitMode_m8B90D81B0ABAC31965FB939721320F082A81A59D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Connect.UnityConnectSettings::set_testInitMode(System.Int32)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityConnectSettings__ctor_m52B180993E2FBFBA90A679B119590FDA83E0D2AB (UnityConnectSettings_t70F8B3FFCEA5C5B6385372A9A139D12A1C798EDA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityConnectSettings__ctor_m52B180993E2FBFBA90A679B119590FDA83E0D2AB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, UnityConnectSettings__ctor_m52B180993E2FBFBA90A679B119590FDA83E0D2AB_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object__ctor_m2149FA40CEC8D82AC20D3508AB40C0D8EFEF68E6(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool UnityAdsSettings_get_enabled_m7FC5CA5CAC32D89E997E56F85709E95D693FFDD6 (const RuntimeMethod* method) 
{
	typedef bool (*UnityAdsSettings_get_enabled_m7FC5CA5CAC32D89E997E56F85709E95D693FFDD6_ftn) ();
	static UnityAdsSettings_get_enabled_m7FC5CA5CAC32D89E997E56F85709E95D693FFDD6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityAdsSettings_get_enabled_m7FC5CA5CAC32D89E997E56F85709E95D693FFDD6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Advertisements.UnityAdsSettings::get_enabled()");
	bool icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityAdsSettings_set_enabled_mED8F9E3987137A06FAC0DED1074976FA27A20279 (bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*UnityAdsSettings_set_enabled_mED8F9E3987137A06FAC0DED1074976FA27A20279_ftn) (bool);
	static UnityAdsSettings_set_enabled_mED8F9E3987137A06FAC0DED1074976FA27A20279_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityAdsSettings_set_enabled_mED8F9E3987137A06FAC0DED1074976FA27A20279_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Advertisements.UnityAdsSettings::set_enabled(System.Boolean)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool UnityAdsSettings_IsPlatformEnabled_m5544575971444B4D60A66E75872DB99D04F6CA2F (int32_t ___0_platform, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityAdsSettings_IsPlatformEnabled_m5544575971444B4D60A66E75872DB99D04F6CA2F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_platform));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, UnityAdsSettings_IsPlatformEnabled_m5544575971444B4D60A66E75872DB99D04F6CA2F_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_UnityConnectModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_UnityConnectModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_UnityConnectModule + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_UnityConnectModule + 3));
		V_0 = (bool)1;
		goto IL_0005;
	}

IL_0005:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_UnityConnectModule + 4));
		bool L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityAdsSettings_SetPlatformEnabled_mE15C6EBB1CA537EE9477CA5576C342320D560551 (int32_t ___0_platform, bool ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityAdsSettings_SetPlatformEnabled_mE15C6EBB1CA537EE9477CA5576C342320D560551_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_platform), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, UnityAdsSettings_SetPlatformEnabled_mE15C6EBB1CA537EE9477CA5576C342320D560551_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_UnityConnectModule + 5));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_UnityConnectModule + 6));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_UnityConnectModule + 7));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_UnityConnectModule + 8));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool UnityAdsSettings_get_initializeOnStartup_m7C45929DD9C44F2C41978FF2F1F83DB24BB85698 (const RuntimeMethod* method) 
{
	typedef bool (*UnityAdsSettings_get_initializeOnStartup_m7C45929DD9C44F2C41978FF2F1F83DB24BB85698_ftn) ();
	static UnityAdsSettings_get_initializeOnStartup_m7C45929DD9C44F2C41978FF2F1F83DB24BB85698_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityAdsSettings_get_initializeOnStartup_m7C45929DD9C44F2C41978FF2F1F83DB24BB85698_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Advertisements.UnityAdsSettings::get_initializeOnStartup()");
	bool icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityAdsSettings_set_initializeOnStartup_m5EF6EACF0342129CB46CB4A9A19E545ADB4E09EE (bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*UnityAdsSettings_set_initializeOnStartup_m5EF6EACF0342129CB46CB4A9A19E545ADB4E09EE_ftn) (bool);
	static UnityAdsSettings_set_initializeOnStartup_m5EF6EACF0342129CB46CB4A9A19E545ADB4E09EE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityAdsSettings_set_initializeOnStartup_m5EF6EACF0342129CB46CB4A9A19E545ADB4E09EE_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Advertisements.UnityAdsSettings::set_initializeOnStartup(System.Boolean)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool UnityAdsSettings_get_testMode_mF098639F51F4AD94C01CA0DFA0CF70B81769BB55 (const RuntimeMethod* method) 
{
	typedef bool (*UnityAdsSettings_get_testMode_mF098639F51F4AD94C01CA0DFA0CF70B81769BB55_ftn) ();
	static UnityAdsSettings_get_testMode_mF098639F51F4AD94C01CA0DFA0CF70B81769BB55_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityAdsSettings_get_testMode_mF098639F51F4AD94C01CA0DFA0CF70B81769BB55_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Advertisements.UnityAdsSettings::get_testMode()");
	bool icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityAdsSettings_set_testMode_m7786647A721E0A19C2ADA5794F62FB4871C8BD8E (bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*UnityAdsSettings_set_testMode_m7786647A721E0A19C2ADA5794F62FB4871C8BD8E_ftn) (bool);
	static UnityAdsSettings_set_testMode_m7786647A721E0A19C2ADA5794F62FB4871C8BD8E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityAdsSettings_set_testMode_m7786647A721E0A19C2ADA5794F62FB4871C8BD8E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Advertisements.UnityAdsSettings::set_testMode(System.Boolean)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* UnityAdsSettings_GetGameId_m86D0B75C01498D76593CEC10A3D89A90DD3B6CCF (int32_t ___0_platform, const RuntimeMethod* method) 
{
	typedef String_t* (*UnityAdsSettings_GetGameId_m86D0B75C01498D76593CEC10A3D89A90DD3B6CCF_ftn) (int32_t);
	static UnityAdsSettings_GetGameId_m86D0B75C01498D76593CEC10A3D89A90DD3B6CCF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityAdsSettings_GetGameId_m86D0B75C01498D76593CEC10A3D89A90DD3B6CCF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Advertisements.UnityAdsSettings::GetGameId(UnityEngine.RuntimePlatform)");
	String_t* icallRetVal = _il2cpp_icall_func(___0_platform);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityAdsSettings_SetGameId_m1D9A3409CDF70F991D28B087B0D6EED04144DB3D (int32_t ___0_platform, String_t* ___1_gameId, const RuntimeMethod* method) 
{
	typedef void (*UnityAdsSettings_SetGameId_m1D9A3409CDF70F991D28B087B0D6EED04144DB3D_ftn) (int32_t, String_t*);
	static UnityAdsSettings_SetGameId_m1D9A3409CDF70F991D28B087B0D6EED04144DB3D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (UnityAdsSettings_SetGameId_m1D9A3409CDF70F991D28B087B0D6EED04144DB3D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Advertisements.UnityAdsSettings::SetGameId(UnityEngine.RuntimePlatform,System.String)");
	_il2cpp_icall_func(___0_platform, ___1_gameId);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
