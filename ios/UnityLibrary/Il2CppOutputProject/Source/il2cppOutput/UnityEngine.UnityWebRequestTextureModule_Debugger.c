﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[13] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestTextureModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestTextureModule[76] = 
{
	{ 110127, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 110127, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 110127, 1, 20, 20, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 110127, 1, 21, 21, 13, 44, 1, kSequencePointKind_Normal, 0, 3 },
	{ 110127, 1, 21, 21, 13, 44, 4, kSequencePointKind_StepOut, 0, 4 },
	{ 110127, 1, 22, 22, 9, 10, 14, kSequencePointKind_Normal, 0, 5 },
	{ 110128, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 110128, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 110128, 1, 24, 24, 9, 40, 0, kSequencePointKind_Normal, 0, 8 },
	{ 110128, 1, 24, 24, 9, 40, 1, kSequencePointKind_StepOut, 0, 9 },
	{ 110128, 1, 25, 25, 9, 10, 7, kSequencePointKind_Normal, 0, 10 },
	{ 110128, 1, 26, 26, 13, 41, 8, kSequencePointKind_Normal, 0, 11 },
	{ 110128, 1, 26, 26, 13, 41, 10, kSequencePointKind_StepOut, 0, 12 },
	{ 110128, 1, 27, 27, 9, 10, 16, kSequencePointKind_Normal, 0, 13 },
	{ 110129, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 14 },
	{ 110129, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 15 },
	{ 110129, 1, 29, 29, 9, 53, 0, kSequencePointKind_Normal, 0, 16 },
	{ 110129, 1, 29, 29, 9, 53, 1, kSequencePointKind_StepOut, 0, 17 },
	{ 110129, 1, 30, 30, 9, 10, 7, kSequencePointKind_Normal, 0, 18 },
	{ 110129, 1, 31, 31, 13, 45, 8, kSequencePointKind_Normal, 0, 19 },
	{ 110129, 1, 31, 31, 13, 45, 10, kSequencePointKind_StepOut, 0, 20 },
	{ 110129, 1, 32, 32, 13, 38, 16, kSequencePointKind_Normal, 0, 21 },
	{ 110129, 1, 33, 33, 9, 10, 26, kSequencePointKind_Normal, 0, 22 },
	{ 110130, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 23 },
	{ 110130, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 24 },
	{ 110130, 1, 36, 36, 9, 10, 0, kSequencePointKind_Normal, 0, 25 },
	{ 110130, 1, 37, 37, 13, 67, 1, kSequencePointKind_Normal, 0, 26 },
	{ 110130, 1, 37, 37, 13, 67, 8, kSequencePointKind_StepOut, 0, 27 },
	{ 110130, 1, 38, 38, 9, 10, 16, kSequencePointKind_Normal, 0, 28 },
	{ 110131, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 29 },
	{ 110131, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 30 },
	{ 110131, 1, 41, 41, 9, 10, 0, kSequencePointKind_Normal, 0, 31 },
	{ 110131, 1, 42, 42, 13, 50, 1, kSequencePointKind_Normal, 0, 32 },
	{ 110131, 1, 42, 42, 13, 50, 7, kSequencePointKind_StepOut, 0, 33 },
	{ 110131, 1, 43, 43, 13, 28, 13, kSequencePointKind_Normal, 0, 34 },
	{ 110131, 1, 43, 43, 13, 28, 14, kSequencePointKind_StepOut, 0, 35 },
	{ 110131, 1, 44, 44, 9, 10, 20, kSequencePointKind_Normal, 0, 36 },
	{ 110132, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 37 },
	{ 110132, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 38 },
	{ 110132, 1, 48, 48, 17, 18, 0, kSequencePointKind_Normal, 0, 39 },
	{ 110132, 1, 48, 48, 19, 53, 1, kSequencePointKind_Normal, 0, 40 },
	{ 110132, 1, 48, 48, 19, 53, 2, kSequencePointKind_StepOut, 0, 41 },
	{ 110132, 1, 48, 48, 54, 55, 10, kSequencePointKind_Normal, 0, 42 },
	{ 110134, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 43 },
	{ 110134, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 44 },
	{ 110134, 1, 55, 55, 9, 10, 0, kSequencePointKind_Normal, 0, 45 },
	{ 110134, 1, 56, 56, 13, 78, 1, kSequencePointKind_Normal, 0, 46 },
	{ 110134, 1, 56, 56, 13, 78, 2, kSequencePointKind_StepOut, 0, 47 },
	{ 110134, 1, 56, 56, 13, 78, 7, kSequencePointKind_StepOut, 0, 48 },
	{ 110134, 1, 57, 57, 9, 10, 15, kSequencePointKind_Normal, 0, 49 },
	{ 110135, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 50 },
	{ 110135, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 51 },
	{ 110135, 2, 9, 9, 9, 10, 0, kSequencePointKind_Normal, 0, 52 },
	{ 110135, 2, 10, 10, 13, 66, 1, kSequencePointKind_Normal, 0, 53 },
	{ 110135, 2, 10, 10, 13, 66, 3, kSequencePointKind_StepOut, 0, 54 },
	{ 110135, 2, 11, 11, 9, 10, 11, kSequencePointKind_Normal, 0, 55 },
	{ 110136, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 56 },
	{ 110136, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 57 },
	{ 110136, 2, 14, 14, 9, 10, 0, kSequencePointKind_Normal, 0, 58 },
	{ 110136, 2, 15, 15, 13, 66, 1, kSequencePointKind_Normal, 0, 59 },
	{ 110136, 2, 15, 15, 13, 66, 3, kSequencePointKind_StepOut, 0, 60 },
	{ 110136, 2, 16, 16, 9, 10, 11, kSequencePointKind_Normal, 0, 61 },
	{ 110137, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 62 },
	{ 110137, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 63 },
	{ 110137, 2, 19, 19, 9, 10, 0, kSequencePointKind_Normal, 0, 64 },
	{ 110137, 2, 20, 20, 13, 123, 1, kSequencePointKind_Normal, 0, 65 },
	{ 110137, 2, 20, 20, 13, 123, 11, kSequencePointKind_StepOut, 0, 66 },
	{ 110137, 2, 20, 20, 13, 123, 17, kSequencePointKind_StepOut, 0, 67 },
	{ 110137, 2, 21, 21, 9, 10, 25, kSequencePointKind_Normal, 0, 68 },
	{ 110138, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 69 },
	{ 110138, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 70 },
	{ 110138, 2, 24, 24, 9, 10, 0, kSequencePointKind_Normal, 0, 71 },
	{ 110138, 2, 25, 25, 13, 123, 1, kSequencePointKind_Normal, 0, 72 },
	{ 110138, 2, 25, 25, 13, 123, 11, kSequencePointKind_StepOut, 0, 73 },
	{ 110138, 2, 25, 25, 13, 123, 17, kSequencePointKind_StepOut, 0, 74 },
	{ 110138, 2, 26, 26, 9, 10, 25, kSequencePointKind_Normal, 0, 75 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestTextureModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestTextureModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UnityWebRequestTexture/Public/DownloadHandlerTexture.bindings.cs", { 246, 164, 37, 22, 128, 229, 73, 150, 158, 38, 48, 159, 92, 65, 250, 14} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UnityWebRequestTexture/UnityWebRequestTexture.cs", { 225, 163, 238, 69, 102, 237, 22, 230, 49, 123, 40, 148, 160, 16, 132, 233} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[2] = 
{
	{ 14161, 1 },
	{ 14162, 2 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[7] = 
{
	{ 0, 18 },
	{ 0, 12 },
	{ 0, 17 },
	{ 0, 13 },
	{ 0, 13 },
	{ 0, 27 },
	{ 0, 27 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[13] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 18, 0, 1 },
	{ 0, 0, 0 },
	{ 12, 1, 1 },
	{ 0, 0, 0 },
	{ 17, 2, 1 },
	{ 13, 3, 1 },
	{ 13, 4, 1 },
	{ 27, 5, 1 },
	{ 27, 6, 1 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestTextureModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestTextureModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	76,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_UnityWebRequestTextureModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	2,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
