﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641 (void);
extern void FaceInfo_set_faceIndex_m6B57F32E81090DCB77DA466535224CAC5ACF9AFA (void);
extern void FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241 (void);
extern void FaceInfo_set_familyName_m49CB07A51AC9008B4F399A3064EC4FF6EA8E839D (void);
extern void FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6 (void);
extern void FaceInfo_set_styleName_m3BEBF7E576032A668BCC83D88D2F6902F561B5E6 (void);
extern void FaceInfo_get_pointSize_m7EF7429A4725AB715931A220F6BB498C3D6BF7CB (void);
extern void FaceInfo_set_pointSize_m17D0B03C4A762F657A4ABD15327B08D6B55EF492 (void);
extern void FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD (void);
extern void FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4 (void);
extern void FaceInfo_get_unitsPerEM_m92CD79B71FC504DE1C87923716E999D85A35AC0E (void);
extern void FaceInfo_set_unitsPerEM_m3E48D789D666984A4EA8D221BED22833761E9A90 (void);
extern void FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D (void);
extern void FaceInfo_set_lineHeight_m5952A394C6055DD86700448D9E33EBBE852E05AE (void);
extern void FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD (void);
extern void FaceInfo_set_ascentLine_mDFB32635374875A695D3BB686D2A16C648A20D78 (void);
extern void FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88 (void);
extern void FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF (void);
extern void FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96 (void);
extern void FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99 (void);
extern void FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A (void);
extern void FaceInfo_set_baseline_m528F6ADAF4F45A31E9D2BA362FB3C02B03DDD741 (void);
extern void FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3 (void);
extern void FaceInfo_set_descentLine_m62423E864258229A85FD3684D8385A44C01AF97C (void);
extern void FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A (void);
extern void FaceInfo_set_superscriptOffset_mC81144590FA80858D489AFC46AAA383E11D3D2B5 (void);
extern void FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0 (void);
extern void FaceInfo_set_superscriptSize_m89F17C1502C07A3846402A5A43369BFB66723515 (void);
extern void FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A (void);
extern void FaceInfo_set_subscriptOffset_m796EF61DE0677BFB134F6B8C4B6C1869BCF8782A (void);
extern void FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31 (void);
extern void FaceInfo_set_subscriptSize_m5759439F9D100591A6FFF1D7377495CEBDCD6B5D (void);
extern void FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251 (void);
extern void FaceInfo_set_underlineOffset_m1C0E755772FE8C47D565E6CC7DC4E953B71794AA (void);
extern void FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2 (void);
extern void FaceInfo_set_underlineThickness_mDD002D08CE97E0C7DA6FA6FFBDD15295D24B303A (void);
extern void FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5 (void);
extern void FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0 (void);
extern void FaceInfo_get_strikethroughThickness_m079405B5FDA3F9CC4147597444598A173CFD74C3 (void);
extern void FaceInfo_set_strikethroughThickness_m8CA3C9897FF3D5D0F653539CCBF5E16E01CACF52 (void);
extern void FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161 (void);
extern void FaceInfo_set_tabWidth_m44234ED657FAB54320C48C34D48532F8232F6E1D (void);
extern void FaceInfo__ctor_m5392B651EAD0672BE1FF0EB8E878B6078D9C3201 (void);
extern void FaceInfo_Compare_m5C40431846D8FC040C4AC93EEBCE599DF40E4940 (void);
extern void GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898 (void);
extern void GlyphRect_set_x_m1AF3570B0730D18F11DEBCC5D639EE83549C06E1 (void);
extern void GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA (void);
extern void GlyphRect_set_y_mE9697A41067D3BCB1CF0B92472A259A94FB95257 (void);
extern void GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12 (void);
extern void GlyphRect_set_width_mD99A7B04328F95C75FA2403C7E6DDC3CA40951CD (void);
extern void GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A (void);
extern void GlyphRect_set_height_m4E34D98DDD7A403571AEC2C3A05F4AE07453085E (void);
extern void GlyphRect_get_zero_m359121752EE1A46C51118D84F03204F3285FF3FA (void);
extern void GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95 (void);
extern void GlyphRect__ctor_m6540996F96C6A3C7BA757EDC4A02196993EC3400 (void);
extern void GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F (void);
extern void GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B (void);
extern void GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87 (void);
extern void GlyphRect_op_Equality_mB46B18B3849F2DDE28C7E5D5202FC427EE58731D (void);
extern void GlyphRect_op_Inequality_mDBC612E6947E2CFEF7101902C158213038D09678 (void);
extern void GlyphRect__cctor_m74BDAD5150F67B623F7D02238DD25D30C133BBDA (void);
extern void GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13 (void);
extern void GlyphMetrics_set_width_m236B586A8A7CB4FD2C46E6581ADB2037CD6019A4 (void);
extern void GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C (void);
extern void GlyphMetrics_set_height_m98DA746BE7362BD1EE03D4083213DA33B6306CF1 (void);
extern void GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F (void);
extern void GlyphMetrics_set_horizontalBearingX_m5F05916CA7AC363ED6AEE4AD2809B9925D829F0C (void);
extern void GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223 (void);
extern void GlyphMetrics_set_horizontalBearingY_m3459042D77350137A7CD0EAC0401103F151D5314 (void);
extern void GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1 (void);
extern void GlyphMetrics_set_horizontalAdvance_m13C7C6BDFA7BC4A384F8517193FAE21CA614B814 (void);
extern void GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D (void);
extern void GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6 (void);
extern void GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970 (void);
extern void GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB (void);
extern void GlyphMetrics_op_Equality_m4CA774EAF33456AF2EBAFA05EDB930054C19CB5E (void);
extern void GlyphMetrics_op_Inequality_mBD3116773EEC2B4C7891F957AF997BE32B409E72 (void);
extern void Glyph_get_index_mCFBBCF85E7F3434B7A595EEE3411EFFB78E5675B (void);
extern void Glyph_set_index_mD033C966D79B910424B985F9D81C01D4E056B72C (void);
extern void Glyph_get_metrics_mB6E9D3D1899E35BA257638F6F58B7D260170B6FA (void);
extern void Glyph_set_metrics_m3350984977FC50061481B1EC563DE59147428BC2 (void);
extern void Glyph_get_glyphRect_m94E7C5FE682695CDC096248EF027079F33768EE5 (void);
extern void Glyph_set_glyphRect_mC21EB362D6EC56E0D110B0A08505CAD2DF26A6A8 (void);
extern void Glyph_get_scale_m3ED738CBB032247526DB38161E180759B2D06F29 (void);
extern void Glyph_set_scale_m44247C5948E32562931FA8C44799A3E1E4F0562A (void);
extern void Glyph_get_atlasIndex_m575332307F2C182655EE9AD352E92F1B5F4D26DF (void);
extern void Glyph_set_atlasIndex_m622CB24F3110B65CADB0C9F0223133B0DA926ABE (void);
extern void Glyph_get_classDefinitionType_m06B4C1F243350980962B289B664A4AC6FDE89F85 (void);
extern void Glyph_set_classDefinitionType_mFF3AA3EFDB3BC89B192DC001CEC6E650BAC61E37 (void);
extern void Glyph__ctor_m9FB83C6B166AC59E03B585F76C09C5FC1720281F (void);
extern void Glyph__ctor_mCAEB1E07D206BE7EC7A459CD9D6EFFBBC7DA7C03 (void);
extern void Glyph__ctor_m2E3C296A46BE48B869634BBE3F97B30F3442CC94 (void);
extern void Glyph__ctor_m80F1C9D6478C98861C20F026BDA6919A206B9FDC (void);
extern void Glyph__ctor_m71D55A8FBEA48ECCD78D65CAC9D008905E56FBF1 (void);
extern void Glyph_Compare_m9F99433F619E7EF484B1A603B4C8E2162E95EF72 (void);
extern void FontEngine__ctor_m5EA5CC866E0E0B96C135270936391DF938F18013 (void);
extern void FontEngine_InitializeFontEngine_mCA2F9C3294A61C0294B4B9849082C0344169F322 (void);
extern void FontEngine_InitializeFontEngine_Internal_m64ABDC3A5AF9D87C995FF6B98979933E7074AB06 (void);
extern void FontEngine_DestroyFontEngine_mE5DE04FEFB654EDE496EEEC38EBDBEBF8468A856 (void);
extern void FontEngine_DestroyFontEngine_Internal_m6C2885F248EB7E05F91B9DF8780D428A8EB65F16 (void);
extern void FontEngine_SendCancellationRequest_m37D2956E605C0A5D4E79B357171EB481421B7695 (void);
extern void FontEngine_SendCancellationRequest_Internal_mBD3E0E85F4EFD928E9F16322E7E976C5D5F6557F (void);
extern void FontEngine_get_isProcessingDone_m6E21B2CDC24AAD7DB8BBC0EC3EBD20A9F91958B7 (void);
extern void FontEngine_get_generationProgress_m5F65AEFD59F3C4011A2C8E3424671A006357BF50 (void);
extern void FontEngine_LoadFontFace_mF3AD8D250D32D8B7CB347F91D409E63431CF4F9F (void);
extern void FontEngine_LoadFontFace_Internal_mAF51FEDD7DE13341FB210DC0743D877AD34416C8 (void);
extern void FontEngine_LoadFontFace_m9CD5ECFDAF16C50B54B9A5F9BAE17E5B464808CD (void);
extern void FontEngine_LoadFontFace_With_Size_Internal_m9338A2134807FF890EDC04E646D0ADB3F6FBFB2D (void);
extern void FontEngine_LoadFontFace_m6DCF863A84AF3E2EEB6AC69C7C19D937909698C1 (void);
extern void FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_mD89D8C9D6A2B8E7D29BAE669C15781DBCC63B8E4 (void);
extern void FontEngine_LoadFontFace_m11543AEE17CD53129A31308D2F449CBA0BE1D7BE (void);
extern void FontEngine_LoadFontFace_FromSourceFontFile_Internal_mD8E89F312B1D3840975BA422268F9EE1FDA01FE0 (void);
extern void FontEngine_LoadFontFace_mD4CE1EFF0B0092DE423648561AF0AC9F58CA184A (void);
extern void FontEngine_LoadFontFace_With_Size_FromSourceFontFile_Internal_mDCB5FCCC7C06A2F61CD581E428F97F59B1263336 (void);
extern void FontEngine_LoadFontFace_m2FFD3573F585BE112E920FEE83F422326F017514 (void);
extern void FontEngine_LoadFontFace_With_Size_And_FaceIndex_FromSourceFontFile_Internal_m94ECF937874F8CB2A2454E83474D65CD9D0AAB34 (void);
extern void FontEngine_LoadFontFace_m48D4BE6D0EE255FF8DD239CBBA110402ED48E0CC (void);
extern void FontEngine_LoadFontFace_FromFont_Internal_m17C1DE5ADF3F4E04935B9D8C25D08AA1EADA3D1B (void);
extern void FontEngine_LoadFontFace_m63A9171030B36C960896CEF55E7ECF55AF990548 (void);
extern void FontEngine_LoadFontFace_With_Size_FromFont_Internal_m13A7F3EB357E2D2C67B14D10B96F3C490BF6AC11 (void);
extern void FontEngine_LoadFontFace_m15ECA69542615468DB27EE5DCA11EE855BDAA356 (void);
extern void FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_m7C4E8F4F88CDDE6B838D890FDC1E0722EBD40497 (void);
extern void FontEngine_LoadFontFace_m58B0B8AFDFCED3964C98DE267A2CC9036014ADA1 (void);
extern void FontEngine_LoadFontFace_by_FamilyName_and_StyleName_Internal_m84930FF90D45392469B3A284BA6A83764A5B7A1D (void);
extern void FontEngine_LoadFontFace_m793FE6557D378E25CDF12DDA5EFAA1F0F9A51C26 (void);
extern void FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_m0856180C0F5FFA09E964986A78153D021F810F30 (void);
extern void FontEngine_UnloadFontFace_m09E0B255BF0B5CEDA90D1C38E1371B3440DD2E51 (void);
extern void FontEngine_UnloadFontFace_Internal_mC25601FC5B89A8F169771813EF33C2B97FCCFDDF (void);
extern void FontEngine_UnloadAllFontFaces_mC810507B1901B5F369CE61DBB9AFA39410C3ED1A (void);
extern void FontEngine_UnloadAllFontFaces_Internal_m321F5FA4814E1B49D6F432E61E523A1A82DF17C2 (void);
extern void FontEngine_GetSystemFontNames_m687D2EC1A3087E326AEFDDBEDBF902D5710B9C94 (void);
extern void FontEngine_GetSystemFontNames_Internal_mA03C78FC31A8C0D3B08C40DFEDD4E7FE5C245214 (void);
extern void FontEngine_GetSystemFontReferences_mDA521582960B0B316BEB3053FFD8A5E3C0326261 (void);
extern void FontEngine_TryGetSystemFontReference_mA32D1513035E9B58417092500DDC3A7C939367A1 (void);
extern void FontEngine_TryGetSystemFontReference_Internal_m61AAB78124D19B1BC88C22D520287893E7280E1F (void);
extern void FontEngine_SetFaceSize_m29B9E871E8B20E3B75AF7132FD1F5319E4D26819 (void);
extern void FontEngine_SetFaceSize_Internal_mF123F8536549A1820A8AB1A25342C7B78AFBF8B3 (void);
extern void FontEngine_GetFaceInfo_mF371B75CDEEDA91FF910BED8DEE8FEB2A493BE37 (void);
extern void FontEngine_GetFaceInfo_Internal_m34E21653DF4724C3FCE289DA20AD5AB1B2F24B90 (void);
extern void FontEngine_GetFaceCount_mB68518336320248CB257B03630A7C269B8E89D79 (void);
extern void FontEngine_GetFontFaces_m7EC5E883BE91B715F363A6D5FFA83888144D3BA1 (void);
extern void FontEngine_GetFontFaces_Internal_m130B4EBF35348F810B30BF42208886281F2B04FF (void);
extern void FontEngine_GetVariantGlyphIndex_m4C3A4AA47058F12996EC35715AF52F06E198B9DA (void);
extern void FontEngine_GetGlyphIndex_mEAE36421D92783413286344213D6EFD52E90CC00 (void);
extern void FontEngine_TryGetGlyphIndex_m7624A886DD1A56BCB95A4B1B9F050DC8A5DC2DB9 (void);
extern void FontEngine_LoadGlyph_mCA25DE5E57C3F0C503FF8E67F5F999C8BAA9FFBA (void);
extern void FontEngine_LoadGlyph_Internal_mE053AC9788323FE10F3B3CFD364B132156255921 (void);
extern void FontEngine_TryGetGlyphWithUnicodeValue_m58889809E3D65A0F1C5AEFF4DF6D319EBD139159 (void);
extern void FontEngine_TryGetGlyphWithUnicodeValue_Internal_mD799CD2B5CEA61ED5ABA16EDAA428385FD906BD8 (void);
extern void FontEngine_TryGetGlyphWithIndexValue_mD922A7EB95949E95D96C222D2CA1ED56BA2E81C3 (void);
extern void FontEngine_TryGetGlyphWithIndexValue_Internal_m2C5359B3C62139D20F52C1D232938CFAC9C3459D (void);
extern void FontEngine_TryPackGlyphInAtlas_m9F93C4D9107F4A7419867532D8CE974EBD890BD0 (void);
extern void FontEngine_TryPackGlyphInAtlas_Internal_mD7C1849C5DAD16A0DCECE8B18F46C66EB6F52D8E (void);
extern void FontEngine_TryPackGlyphsInAtlas_mC4859FF2956BA1F97390A8EC6A61EFA2178D3440 (void);
extern void FontEngine_TryPackGlyphsInAtlas_Internal_m0847DC54AA6A0F5A98791FFE3016D0FDAD48A144 (void);
extern void FontEngine_RenderGlyphToTexture_m2E1A9146151D4632B065CD69792E97EA50ACBF3B (void);
extern void FontEngine_RenderGlyphToTexture_Internal_m26C761F2C50E95DDD7BDEEA1ABC617709B690280 (void);
extern void FontEngine_RenderGlyphsToTexture_mC979B0FFAC2A11C512DF435A9E228349833DC577 (void);
extern void FontEngine_RenderGlyphsToTexture_Internal_mC43F5F34F56CD622B25EFEC7F8406F03A7CB5030 (void);
extern void FontEngine_RenderGlyphsToTexture_m32240D8BBE3AAE9F54DE73BAD4724F4B623D2166 (void);
extern void FontEngine_RenderGlyphsToTextureBuffer_Internal_m3F34D45F88AA9935BD6627B157744ED9172F521E (void);
extern void FontEngine_RenderGlyphsToSharedTexture_mB2853E18C07BE73CB8CE776DED0D4CEF87B25D2E (void);
extern void FontEngine_RenderGlyphsToSharedTexture_Internal_m8E969EF8A38E7789C234F3CD6E8B511CBA3B99EB (void);
extern void FontEngine_SetSharedTexture_m23492D37EA965C442C6875BDABE15B365AE65B73 (void);
extern void FontEngine_ReleaseSharedTexture_mA7B1FEE61B1C3E7E8844B2BB3C3F035D977AC734 (void);
extern void FontEngine_SetTextureUploadMode_m04D13DFE627B79D5EB574EC74556E31DF42A83F3 (void);
extern void FontEngine_TryAddGlyphToTexture_m45A94FA06ADDCE2FA6B139B29E942496B760A090 (void);
extern void FontEngine_TryAddGlyphToTexture_Internal_mD6ED15BDDDC4F874C98D514D8DCE699EADB8C708 (void);
extern void FontEngine_TryAddGlyphsToTexture_m25FA7243A69693449F7F5E109F39E758F03376D9 (void);
extern void FontEngine_TryAddGlyphsToTexture_Internal_MultiThread_m4C938678D281A80A1CB576EFFC1AA8C2FC860C28 (void);
extern void FontEngine_TryAddGlyphsToTexture_m18740AD9F7264F54C397916268C0AB0738879801 (void);
extern void FontEngine_TryAddGlyphsToTexture_Internal_m43D4D242873C647DF5A20F7579FD90E373999EA8 (void);
extern void FontEngine_GetOpenTypeLayoutTable_m6E3FEED945F5DEB760F602E64BBC303F40169D01 (void);
extern void FontEngine_GetOpenTypeLayoutScripts_mD75DC816DE6F0F7A817EBD5C5B24A2B072087AF1 (void);
extern void FontEngine_GetOpenTypeLayoutFeatures_mB9E4374C068B14C96DFD75CED59C0B18697CEA4D (void);
extern void FontEngine_GetOpenTypeLayoutLookups_m36263ACB798AB0B23B022994D36E58E3890B2728 (void);
extern void FontEngine_GetOpenTypeFontFeatureList_m5CD64F81C7DD797D26BC803BD4FC62864630A7D1 (void);
extern void FontEngine_GetAllSingleSubstitutionRecords_mD17E69A9683D9CF1AD58F645C34BEE0909F11FB9 (void);
extern void FontEngine_GetSingleSubstitutionRecords_mBD8559C9872AE511D1354E12E1CBB9CEAFF187C5 (void);
extern void FontEngine_GetSingleSubstitutionRecords_m2E4DBFBEE4C05227DD6BB62ECE15E026B9B11702 (void);
extern void FontEngine_GetSingleSubstitutionRecords_m9F204C0A1EACA448C70C4ABC8C562376C4B54237 (void);
extern void FontEngine_PopulateSingleSubstitutionRecordMarshallingArray_from_GlyphIndexes_m08696426BC414C9377A1D0EA088C5CDF2F5EFF97 (void);
extern void FontEngine_GetSingleSubstitutionRecordsFromMarshallingArray_m367C216DBEFC901EAE8BA89EFA5B064C9345B8B2 (void);
extern void FontEngine_GetAllMultipleSubstitutionRecords_mCC653F3E102255BCA03EB06357259B01929E3028 (void);
extern void FontEngine_GetMultipleSubstitutionRecords_m3FA13066E21933EC0B68D271C497DDF84D42F7CB (void);
extern void FontEngine_GetMultipleSubstitutionRecords_m3A52CB7270EDE5243182AD33358F1C8D9060EBC7 (void);
extern void FontEngine_GetMultipleSubstitutionRecords_mBFF4AD72E99F503F90B154876328D9A240AFD667 (void);
extern void FontEngine_PopulateMultipleSubstitutionRecordMarshallingArray_from_GlyphIndexes_m4CBDEB8FE75DC34B642180DAF9F3E84D9BFF24AA (void);
extern void FontEngine_GetMultipleSubstitutionRecordsFromMarshallingArray_mFD66EDDC06637158FCC4755BAE17DF099FDDD733 (void);
extern void FontEngine_GetAllAlternateSubstitutionRecords_mD13307D20DEBBC0144419DE66E74217A23AC98D3 (void);
extern void FontEngine_GetAlternateSubstitutionRecords_mB9C9985993D2923087BF9238486134C063FB177D (void);
extern void FontEngine_GetAlternateSubstitutionRecords_mFD279E3D5509ADB7156B9E42D2370A9368BB5B2E (void);
extern void FontEngine_GetAlternateSubstitutionRecords_m1E55EB9EE2A62972588418C3A5363715F50B954C (void);
extern void FontEngine_PopulateAlternateSubstitutionRecordMarshallingArray_from_GlyphIndexes_m643BCB05471E3ADFB75246D652CC963F5868E765 (void);
extern void FontEngine_GetAlternateSubstitutionRecordsFromMarshallingArray_m6D7363B0B32E47F377CE3BD4EF78CA16D7E68789 (void);
extern void FontEngine_GetAllLigatureSubstitutionRecords_m362CD420864108B994FCA46FCE362CC8E7CE05B6 (void);
extern void FontEngine_GetLigatureSubstitutionRecords_m4189533C549B466BC84F22F5B8F6A24E18258134 (void);
extern void FontEngine_GetLigatureSubstitutionRecords_m65ADF14EB9A148D4BCB7DF107D99A83BEDF36009 (void);
extern void FontEngine_GetLigatureSubstitutionRecords_mF8D49054EA5590D6D3EE74C597298C507ED1569A (void);
extern void FontEngine_GetLigatureSubstitutionRecords_m728CAEE30E649D00FBC9E1A6C62EDE769B5D38C1 (void);
extern void FontEngine_GetLigatureSubstitutionRecords_mAE1873C8AED5673CBF3789CB2E4C1E1FFA0E272F (void);
extern void FontEngine_GetLigatureSubstitutionRecords_m2F7295EC1D6EEC1DAED317433A93DF13D0FBF637 (void);
extern void FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_mFF6E6E4EF766AFDCE5E94ACD22704995EA1774A7 (void);
extern void FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_for_LookupIndex_mC269C1A9E8E04AA7B22DFD9EB101A1BF4CC7AEEC (void);
extern void FontEngine_GetLigatureSubstitutionRecordsFromMarshallingArray_mB3F24B46CFCB60CFCC8C054A71F2B3E534A70B72 (void);
extern void FontEngine_GetAllContextualSubstitutionRecords_m1301DC9B0BCCFF6FC487038A1CD8856FA38A07C3 (void);
extern void FontEngine_GetContextualSubstitutionRecords_m5E2658BE3B777FC51A7D5C49ACACE198D58715F6 (void);
extern void FontEngine_GetContextualSubstitutionRecords_m308AABCF0DCBFE53AFBCCB9E6914C2E0A07F5A3F (void);
extern void FontEngine_GetContextualSubstitutionRecords_mFB6282AA5C1888EA6799C2EEF94582CCF422EE53 (void);
extern void FontEngine_PopulateContextualSubstitutionRecordMarshallingArray_from_GlyphIndexes_mF84DA309F04F98AF0DC0AF252C8368DB4651CA8C (void);
extern void FontEngine_GetContextualSubstitutionRecordsFromMarshallingArray_m487D548644DCC05412390B009D751674C3C8FC4C (void);
extern void FontEngine_GetAllChainingContextualSubstitutionRecords_mF932A8BDC4A311D7A90F910C6E2CB6B0AD7ABDCD (void);
extern void FontEngine_GetChainingContextualSubstitutionRecords_m42D56A69684DEE46B6C3FDC9EBB4D8B12710B752 (void);
extern void FontEngine_GetChainingContextualSubstitutionRecords_m2EDB681762EAF1DE99AE018FB19268188D35EB33 (void);
extern void FontEngine_GetChainingContextualSubstitutionRecords_mA9921BE959ECA292AB75B8AF3DD82C8784B28B07 (void);
extern void FontEngine_PopulateChainingContextualSubstitutionRecordMarshallingArray_from_GlyphIndexes_m4E83F547A1081C66D8AF1EDBA35D8F9A7B40A80B (void);
extern void FontEngine_GetChainingContextualSubstitutionRecordsFromMarshallingArray_mEA1B3D95450624CF1177CE525133BB4AC6CC293E (void);
extern void FontEngine_GetGlyphPairAdjustmentTable_m67DAC7C0029384FC814621F85F8B35D0D3327BC5 (void);
extern void FontEngine_GetGlyphPairAdjustmentRecords_m23D346BEC5BA63185A01DF33576E98650947ABA8 (void);
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_KernTable_mE3B5D21C5D72CEADB2BEA5AA8022E466356386E7 (void);
extern void FontEngine_GetGlyphPairAdjustmentRecords_m17810E7931ECB679ABB4075F7905297CF01A4306 (void);
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_GlyphIndex_m683C1CB2CA87F7EC8284F734E3E8399F549CC740 (void);
extern void FontEngine_GetGlyphPairAdjustmentRecords_mC7573525397545B2DFA0996046F6C3DB3ECEED47 (void);
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_for_NewlyAddedGlyphIndexes_m5F1BF301D27185C895ED78C8CA11A0B4F265FAE3 (void);
extern void FontEngine_GetGlyphPairAdjustmentRecord_m79F7FEA519DC93AD1487058386DF2FC6880ED1A3 (void);
extern void FontEngine_GetSingleAdjustmentRecords_m3095AC5F622D225C71A7456294A0CC4773908945 (void);
extern void FontEngine_GetSingleAdjustmentRecords_m45CB1804F760369B9EE5369E9BFEB8B9DE07B573 (void);
extern void FontEngine_GetSingleAdjustmentRecords_m3BC9816CAEC2BBB12032C812943BA125D66A017B (void);
extern void FontEngine_PopulateSingleAdjustmentRecordMarshallingArray_from_GlyphIndexes_mA6A5B513EFDFB02316F4AF3C670EB1C565EAEDD8 (void);
extern void FontEngine_GetSingleAdjustmentRecordsFromMarshallingArray_m508F7F4E00B40648F128750433F671B6AA418908 (void);
extern void FontEngine_GetPairAdjustmentRecords_m732A89603A857BA1007BDA90EC87DDAF46E18EAA (void);
extern void FontEngine_GetPairAdjustmentRecord_mBCBCF553E9795ECF71AE8406F4CA630AF8183020 (void);
extern void FontEngine_GetAllPairAdjustmentRecords_m1994497F95AB84FFB5982B00E585C4559B8191AC (void);
extern void FontEngine_GetPairAdjustmentRecords_m763EA14AA400F46B391CD6B81AE7CD897A66565B (void);
extern void FontEngine_GetPairAdjustmentRecords_m477F7ADB06BF983D42ACFBD0FC18DDBE1C804B53 (void);
extern void FontEngine_GetPairAdjustmentRecords_m5672E694566FB86272576FA050C95538D861CE13 (void);
extern void FontEngine_GetPairAdjustmentRecords_mE8570C2BF546FDD3B9EFEA60C211B0F204ED4416 (void);
extern void FontEngine_GetPairAdjustmentRecords_m832CCA888FBACDECCC204123EF0C79EBB05DC77F (void);
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_m31A262416D56194C32CCE05C12DBEB31B22A36CD (void);
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_for_LookupIndex_m2B6A3354F628DE35B8E080BAE5B0D3A771F9883B (void);
extern void FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_m81121A95D2196747D3597C8BEFF53842577599F6 (void);
extern void FontEngine_GetAllMarkToBaseAdjustmentRecords_m046FCE9EBE1B7011A6B52531AB694C45041B42F6 (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecords_mEAE470B6CA2D95139BC8DCCDC6A149CD684A4080 (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecord_mD38FFAC92C55B82D1180E7097B8747F5D0C94E17 (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecords_mBBA5CD78F1F95CA8198EA76AFBDC7D0BD2C26265 (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecords_m29F1E828E6CEE345FCDC2B9E611EF091E609D4D1 (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecords_m408B8B0363B6F923D696140EFC62370A4928686A (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecords_m84E6B2CCA908CA059AAEAAC9A4C13C5EB7F3AC5B (void);
extern void FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_m20E04208FC0890DF48163AC3ABBF33F23E57237D (void);
extern void FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_for_LookupIndex_m5B5138782A4A3ACEFA70DBCDB952E88E2795426A (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecordsFromMarshallingArray_mF8E57B182D5061FDE535AD9F4EA842B600710491 (void);
extern void FontEngine_GetAllMarkToMarkAdjustmentRecords_mA11C88084182EB94F428AA97E40448C93B5A64A2 (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecords_m43A916A5F4BB5542F4ED505C230C9BB5681F1DBB (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecord_mEBAFE236D9ACB2DACFEFDAC45E92602179D2EE3A (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecords_m53AC560161E74D387D08ECFCA1255CC1763AA10A (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecords_mED59CC785ABB86DAFE872780C1FC8A974925F16C (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecords_m83EC8E58C4706BA4B4CC3C045DAC9D3F057F120E (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecords_mD80A02BE943D75890025C64861BD970ADD660FA3 (void);
extern void FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_m9C7BC1E19B7F3ED79079E1308CBDB5790287F9D2 (void);
extern void FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_for_LookupIndex_m660EE914924C42B8C291A8A0BF0BC1DC7AE31483 (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecordsFromMarshallingArray_mD8679B420C4AB3D372C6364D55C4BBE2CB576889 (void);
extern void FontEngine_GlyphIndexToMarshallingArray_mF3A3CD803687D55F90DED04F3AE52284EFB89831 (void);
extern void FontEngine_ResetAtlasTexture_m15BBE67DFDD8A1E740BC0C4B29612A8C866860DC (void);
extern void FontEngine_RenderBufferToTexture_m14E5B43D0EABE692878CADC2BC20362F0694CFED (void);
extern void FontEngine__cctor_mD8EC115E258FD225726CBC71B249C1F411447D79 (void);
extern void FontEngine_RenderGlyphToTexture_Internal_Injected_mD3096F31E7CEEE9401936450D0C2CFD9CEE2AEDE (void);
extern void FontEngine_GetOpenTypeLayoutTable_Injected_mADA2FABD8FB5F6C5D0451310D10C72EDE3FEFF89 (void);
extern void FontEngine_GetGlyphPairAdjustmentRecord_Injected_m3758944CBC2B87C214BB375B859F0F6E3E81DD8D (void);
extern void FontEngine_GetPairAdjustmentRecord_Injected_m3E55DD6CB4A898FE7A110B7F61D65F4D8B53522D (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecord_Injected_m294C43885873BA0F0D0A0B165FC1C742AA669A9F (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecord_Injected_mC7772FEF202FE1AE5EFCB053D0FA094CBA63B26B (void);
extern void FontEngineUtilities_Approximately_m4D1C3053A5A0F6974E1820D3B0D26530ED073389 (void);
extern void FontEngineUtilities_MaxValue_m7E0FBF90FE07F65C9895ACD56FD032A53E417F83 (void);
extern void GlyphMarshallingStruct__ctor_mF74998CA1395853967081C3799D6EC0AEADFAD8D (void);
extern void GlyphMarshallingStruct__ctor_mF944582B068F848BECC4D80278F3AEFD33843129 (void);
extern void GlyphMarshallingStruct__ctor_m7E489C38186D01A566DD1FC72D6B6D2846DBFBCD (void);
extern void OpenTypeLayoutLookup_UpdateRecords_m6AF4754A1DAEEDE9F75113BC06029EB1F0931271 (void);
extern void OpenTypeLayoutLookup_UpdateRecords_m6C7A5700D6122215DBB6D246CB2A9C14370030A2 (void);
extern void OpenTypeLayoutLookup_UpdateRecords_m5082BD3D95DFFEC11D7AEBED5EB6A588F5B850F7 (void);
extern void OpenTypeLayoutLookup_UpdateRecords_mCBFDE6D2FE4AF94A7CF4FD4E370792393D1D65CD (void);
extern void OpenTypeLayoutLookup__ctor_mD9392C8C75ABC627FCCA874611E0609E03B33747 (void);
extern void GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8 (void);
extern void GlyphValueRecord_set_xPlacement_m79F92029922BDE50ED63A6A03EBE478869F1CCFC (void);
extern void GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA (void);
extern void GlyphValueRecord_set_yPlacement_m04DA300FAB827A708CB291DA3B2EA3128279CA2B (void);
extern void GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E (void);
extern void GlyphValueRecord_set_xAdvance_mA8475DBF8792908481A5DA5FFE7C7FAD3E596A35 (void);
extern void GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6 (void);
extern void GlyphValueRecord_set_yAdvance_m37EDC0646862034F5625BC58428B7E2F3869D0D4 (void);
extern void GlyphValueRecord__ctor_m01488AC8714CC329C1A23B034C1ABF4B52F0740A (void);
extern void GlyphValueRecord_op_Addition_mF26165B4CE61A5409AEFF24B0D1727804E13602B (void);
extern void GlyphValueRecord_op_Multiply_m3F7E8FB8B2AB345D65B80AB9F3C464DAF3AD2D5D (void);
extern void GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60 (void);
extern void GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6 (void);
extern void GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972 (void);
extern void GlyphValueRecord_op_Equality_m2AFC5B719C0208115B6990D2808776B3A1C90A59 (void);
extern void GlyphValueRecord_op_Inequality_mA07F4D32FB5AC7EE656E0CEC880D37FFE2B10921 (void);
extern void GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9 (void);
extern void GlyphAdjustmentRecord_set_glyphIndex_m97850B31C30FA0AC2B497A6F864F76E70D189446 (void);
extern void GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7 (void);
extern void GlyphAdjustmentRecord_set_glyphValueRecord_m5107E793964C7C945DD62D330589AA7D14777047 (void);
extern void GlyphAdjustmentRecord__ctor_m54853163D58AD883E19A7C0D795A4CF73FCAE28C (void);
extern void GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C (void);
extern void GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D (void);
extern void GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535 (void);
extern void GlyphAdjustmentRecord_op_Equality_mA86E910E24C5AD36005A9694FC635DA4A19BCB61 (void);
extern void GlyphAdjustmentRecord_op_Inequality_mBC02C69F7B6C9564D6F3B7131DC5B69CF0BC8063 (void);
extern void GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C (void);
extern void GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_mA19E5BFDFB5B5EF6B82BE195BC407390E3754296 (void);
extern void GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91 (void);
extern void GlyphPairAdjustmentRecord_set_secondAdjustmentRecord_m5C90F3C44ADD213E181BDB955AB214B6F56B2FBF (void);
extern void GlyphPairAdjustmentRecord_get_featureLookupFlags_m08DA76766FDE949068B881DBEA29955C9C43E8A9 (void);
extern void GlyphPairAdjustmentRecord_set_featureLookupFlags_m4900CDB13F697D5CD7012383E71910AEAD170359 (void);
extern void GlyphPairAdjustmentRecord__ctor_m2B1BCCA950399944824CFB79D74C39A7A4F87A6A (void);
extern void GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5 (void);
extern void GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD (void);
extern void GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689 (void);
extern void GlyphPairAdjustmentRecord_op_Equality_m0F93964041166C7D04A88FEA17D6E52102AC14AF (void);
extern void GlyphPairAdjustmentRecord_op_Inequality_mE925301B5EAA3E6E3FBD08527AA9386CFF2B591D (void);
extern void GlyphAnchorPoint_get_xCoordinate_mCD33464763911ECB78DEB1965970A916FA27DD1C (void);
extern void GlyphAnchorPoint_set_xCoordinate_m6820F49984BD779C6274A656AE013575C2E121FA (void);
extern void GlyphAnchorPoint_get_yCoordinate_m2683C19C6A3D750E4D6C536307313E55589909D6 (void);
extern void GlyphAnchorPoint_set_yCoordinate_m3DC770A8B68515510EC83140E7CC2164880EDA97 (void);
extern void MarkPositionAdjustment_get_xPositionAdjustment_m5ACBB4C515357320C12597CAE5E4D409BA298765 (void);
extern void MarkPositionAdjustment_set_xPositionAdjustment_mBD0304ADF7B91F788755A319868513C1D5C7020C (void);
extern void MarkPositionAdjustment_get_yPositionAdjustment_m1F5F7DBBFEB0B52CCC772F68664D06B11D6A9F2C (void);
extern void MarkPositionAdjustment_set_yPositionAdjustment_m80D07753C658004FD58CD20B4B0D70EFB791836E (void);
extern void MarkPositionAdjustment__ctor_mBD39BFE7BE793324DDF1C67BAFE2C9DEE069286B (void);
extern void MarkToBaseAdjustmentRecord_get_baseGlyphID_mD6A0DD18DBE69E0E68ACA6AABF47B1EA61B633A5 (void);
extern void MarkToBaseAdjustmentRecord_set_baseGlyphID_mC8151EF01F0FCFFA0611EB65EC03099B6562ABAC (void);
extern void MarkToBaseAdjustmentRecord_get_baseGlyphAnchorPoint_mCBF57932B7A89C532B0EF750DFD81F8FE389EE08 (void);
extern void MarkToBaseAdjustmentRecord_set_baseGlyphAnchorPoint_m1C79E0AD92B197313FCD2273FD57CB6CA3B3E379 (void);
extern void MarkToBaseAdjustmentRecord_get_markGlyphID_mB4BB0291733ECFC2433F5C2837F0B28EB05CAF5C (void);
extern void MarkToBaseAdjustmentRecord_set_markGlyphID_mCA5B1438023B89A5FDE7B6F8F1C20F7CEB8A8C09 (void);
extern void MarkToBaseAdjustmentRecord_get_markPositionAdjustment_m570715D1D0F84361A90564D4A958394453E1F9AB (void);
extern void MarkToBaseAdjustmentRecord_set_markPositionAdjustment_m585228E1ADB46FEB33EF9400B3A477FEE6559AFC (void);
extern void MarkToMarkAdjustmentRecord_get_baseMarkGlyphID_mA0EAEA751467A8841F9D20D122C4998905A508CC (void);
extern void MarkToMarkAdjustmentRecord_set_baseMarkGlyphID_m3F60246B56DB78EF3754421041378A08E9FC018F (void);
extern void MarkToMarkAdjustmentRecord_get_baseMarkGlyphAnchorPoint_mB87ADA10491B42650BAD4DB7330771061827ACAB (void);
extern void MarkToMarkAdjustmentRecord_set_baseMarkGlyphAnchorPoint_mF99F7E7CBCB4B3C36D87BC6F6B92232EEE63B17B (void);
extern void MarkToMarkAdjustmentRecord_get_combiningMarkGlyphID_m84613A2A27F87AA21CAEDD08759031302C9A8FBF (void);
extern void MarkToMarkAdjustmentRecord_set_combiningMarkGlyphID_mABD6E44107C367EE21EB258DB5681707C4B34075 (void);
extern void MarkToMarkAdjustmentRecord_get_combiningMarkPositionAdjustment_mC109ECEDB4AD314A25C0EB1F6F6151AE611DE15C (void);
extern void MarkToMarkAdjustmentRecord_set_combiningMarkPositionAdjustment_mFAB0D9C53F43D866DD2DA6F3B85A5E1AE23897EC (void);
extern void SingleSubstitutionRecord_get_targetGlyphID_mFD058D5092C3674A7B71EBA67FD6829AC51B5359 (void);
extern void SingleSubstitutionRecord_set_targetGlyphID_m76B6E23641060D8C394379DE91B051D867BD090A (void);
extern void SingleSubstitutionRecord_get_substituteGlyphID_mB14D0FA76335020198ADE6D10ABEBF06D0214B7D (void);
extern void SingleSubstitutionRecord_set_substituteGlyphID_m93A8DF73A184C7E38F3199C4258258E81852E052 (void);
extern void MultipleSubstitutionRecord_get_targetGlyphID_mF01201C747BA2FAEF8567CFDA974F6473E09D162 (void);
extern void MultipleSubstitutionRecord_set_targetGlyphID_mF237E40BBF7D12A45DA5662E11FC3DED640310F2 (void);
extern void MultipleSubstitutionRecord_get_substituteGlyphIDs_m6391274416D6936BE038ED3546BDBB5D88AE6451 (void);
extern void MultipleSubstitutionRecord_set_substituteGlyphIDs_m4F558CBB6F2EBBE4CBD74E2EA049518E0C457B42 (void);
extern void AlternateSubstitutionRecord_get_targetGlyphID_m46389FE0D0A235D35A96A5F3BEECD33D3E269BBA (void);
extern void AlternateSubstitutionRecord_set_targetGlyphID_m3F04DAA4001337ED8501DFA61DA45D11E16C4142 (void);
extern void AlternateSubstitutionRecord_get_substituteGlyphIDs_mB88AA4734922B7304398923CDBD7EC6ACDA3AC86 (void);
extern void AlternateSubstitutionRecord_set_substituteGlyphIDs_mC6DD2CE2FBE55E3CD9276F8F21EAE718A5B3A98C (void);
extern void LigatureSubstitutionRecord_get_componentGlyphIDs_m3BBDC9421E3A7369B198379F5433FBB13ADCE628 (void);
extern void LigatureSubstitutionRecord_set_componentGlyphIDs_mDD8D76279FF545B516D4CE689568AA513A95EF63 (void);
extern void LigatureSubstitutionRecord_get_ligatureGlyphID_m5FD629E204026FB8B6279498CDE5CAB1D23827EC (void);
extern void LigatureSubstitutionRecord_set_ligatureGlyphID_mDB4F809745EF0536182FAF90AFB276F389C35EB6 (void);
extern void GlyphIDSequence_get_glyphIDs_m9BEB975FB20104387FAC6120BB90D4055E854BB1 (void);
extern void GlyphIDSequence_set_glyphIDs_mB877FBB483AAD37119B32B2797E6BACB2EE22256 (void);
extern void SequenceLookupRecord_get_glyphSequenceIndex_mDFE2CB0E6F409FC6E76371AE9C901787A043EDBC (void);
extern void SequenceLookupRecord_set_glyphSequenceIndex_m4903487C7478B5BC34166C892CB3DDA69C5D6A45 (void);
extern void SequenceLookupRecord_get_lookupListIndex_m59A597A93C9DB6D00BE7F408327AFF551D25D829 (void);
extern void SequenceLookupRecord_set_lookupListIndex_mC1F94638EBF47068E6D9BEE948FF08CA6227306C (void);
extern void ContextualSubstitutionRecord_get_inputSequences_mA178071C71E922AB18142760AE712C3D37B11749 (void);
extern void ContextualSubstitutionRecord_set_inputSequences_mB9FFBA40115AF953C54C23520A0D6EED2124D792 (void);
extern void ContextualSubstitutionRecord_get_sequenceLookupRecords_m888925F050EFB2980B87BCDCCC342E4BECA034FC (void);
extern void ContextualSubstitutionRecord_set_sequenceLookupRecords_m63A429B7D96EA86DBD8128908ACA96ADB7FA494A (void);
extern void ChainingContextualSubstitutionRecord_get_backtrackGlyphSequences_m7FD183544371F7A853BBF197E66592C0C2D9D8A1 (void);
extern void ChainingContextualSubstitutionRecord_set_backtrackGlyphSequences_m249B99EB124ACB0A7B27D234A4F10EF3F87ACB4E (void);
extern void ChainingContextualSubstitutionRecord_get_inputGlyphSequences_m0236D68BE22540A633BB2590AE34282BD6EA5F97 (void);
extern void ChainingContextualSubstitutionRecord_set_inputGlyphSequences_mEAC5074B5B0A5BDAC19359134C514DC76D33C760 (void);
extern void ChainingContextualSubstitutionRecord_get_lookaheadGlyphSequences_m3C8A441E18555617D4D3455978894962D3BA6FE4 (void);
extern void ChainingContextualSubstitutionRecord_set_lookaheadGlyphSequences_m061486A5432AB15C4BACBA8AC2851A5449219594 (void);
extern void ChainingContextualSubstitutionRecord_get_sequenceLookupRecords_mB0FAEACA23F664767344BD416081AED5BE813C00 (void);
extern void ChainingContextualSubstitutionRecord_set_sequenceLookupRecords_m5AD305FAC33438FE23703DC65C6031A27A7D14BF (void);
static Il2CppMethodPointer s_methodPointers[382] = 
{
	FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641,
	FaceInfo_set_faceIndex_m6B57F32E81090DCB77DA466535224CAC5ACF9AFA,
	FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241,
	FaceInfo_set_familyName_m49CB07A51AC9008B4F399A3064EC4FF6EA8E839D,
	FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6,
	FaceInfo_set_styleName_m3BEBF7E576032A668BCC83D88D2F6902F561B5E6,
	FaceInfo_get_pointSize_m7EF7429A4725AB715931A220F6BB498C3D6BF7CB,
	FaceInfo_set_pointSize_m17D0B03C4A762F657A4ABD15327B08D6B55EF492,
	FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD,
	FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4,
	FaceInfo_get_unitsPerEM_m92CD79B71FC504DE1C87923716E999D85A35AC0E,
	FaceInfo_set_unitsPerEM_m3E48D789D666984A4EA8D221BED22833761E9A90,
	FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D,
	FaceInfo_set_lineHeight_m5952A394C6055DD86700448D9E33EBBE852E05AE,
	FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD,
	FaceInfo_set_ascentLine_mDFB32635374875A695D3BB686D2A16C648A20D78,
	FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88,
	FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF,
	FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96,
	FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99,
	FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A,
	FaceInfo_set_baseline_m528F6ADAF4F45A31E9D2BA362FB3C02B03DDD741,
	FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3,
	FaceInfo_set_descentLine_m62423E864258229A85FD3684D8385A44C01AF97C,
	FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A,
	FaceInfo_set_superscriptOffset_mC81144590FA80858D489AFC46AAA383E11D3D2B5,
	FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0,
	FaceInfo_set_superscriptSize_m89F17C1502C07A3846402A5A43369BFB66723515,
	FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A,
	FaceInfo_set_subscriptOffset_m796EF61DE0677BFB134F6B8C4B6C1869BCF8782A,
	FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31,
	FaceInfo_set_subscriptSize_m5759439F9D100591A6FFF1D7377495CEBDCD6B5D,
	FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251,
	FaceInfo_set_underlineOffset_m1C0E755772FE8C47D565E6CC7DC4E953B71794AA,
	FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2,
	FaceInfo_set_underlineThickness_mDD002D08CE97E0C7DA6FA6FFBDD15295D24B303A,
	FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5,
	FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0,
	FaceInfo_get_strikethroughThickness_m079405B5FDA3F9CC4147597444598A173CFD74C3,
	FaceInfo_set_strikethroughThickness_m8CA3C9897FF3D5D0F653539CCBF5E16E01CACF52,
	FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161,
	FaceInfo_set_tabWidth_m44234ED657FAB54320C48C34D48532F8232F6E1D,
	FaceInfo__ctor_m5392B651EAD0672BE1FF0EB8E878B6078D9C3201,
	FaceInfo_Compare_m5C40431846D8FC040C4AC93EEBCE599DF40E4940,
	GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898,
	GlyphRect_set_x_m1AF3570B0730D18F11DEBCC5D639EE83549C06E1,
	GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA,
	GlyphRect_set_y_mE9697A41067D3BCB1CF0B92472A259A94FB95257,
	GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12,
	GlyphRect_set_width_mD99A7B04328F95C75FA2403C7E6DDC3CA40951CD,
	GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A,
	GlyphRect_set_height_m4E34D98DDD7A403571AEC2C3A05F4AE07453085E,
	GlyphRect_get_zero_m359121752EE1A46C51118D84F03204F3285FF3FA,
	GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95,
	GlyphRect__ctor_m6540996F96C6A3C7BA757EDC4A02196993EC3400,
	GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F,
	GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B,
	GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87,
	GlyphRect_op_Equality_mB46B18B3849F2DDE28C7E5D5202FC427EE58731D,
	GlyphRect_op_Inequality_mDBC612E6947E2CFEF7101902C158213038D09678,
	GlyphRect__cctor_m74BDAD5150F67B623F7D02238DD25D30C133BBDA,
	GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13,
	GlyphMetrics_set_width_m236B586A8A7CB4FD2C46E6581ADB2037CD6019A4,
	GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C,
	GlyphMetrics_set_height_m98DA746BE7362BD1EE03D4083213DA33B6306CF1,
	GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F,
	GlyphMetrics_set_horizontalBearingX_m5F05916CA7AC363ED6AEE4AD2809B9925D829F0C,
	GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223,
	GlyphMetrics_set_horizontalBearingY_m3459042D77350137A7CD0EAC0401103F151D5314,
	GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1,
	GlyphMetrics_set_horizontalAdvance_m13C7C6BDFA7BC4A384F8517193FAE21CA614B814,
	GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D,
	GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6,
	GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970,
	GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB,
	GlyphMetrics_op_Equality_m4CA774EAF33456AF2EBAFA05EDB930054C19CB5E,
	GlyphMetrics_op_Inequality_mBD3116773EEC2B4C7891F957AF997BE32B409E72,
	Glyph_get_index_mCFBBCF85E7F3434B7A595EEE3411EFFB78E5675B,
	Glyph_set_index_mD033C966D79B910424B985F9D81C01D4E056B72C,
	Glyph_get_metrics_mB6E9D3D1899E35BA257638F6F58B7D260170B6FA,
	Glyph_set_metrics_m3350984977FC50061481B1EC563DE59147428BC2,
	Glyph_get_glyphRect_m94E7C5FE682695CDC096248EF027079F33768EE5,
	Glyph_set_glyphRect_mC21EB362D6EC56E0D110B0A08505CAD2DF26A6A8,
	Glyph_get_scale_m3ED738CBB032247526DB38161E180759B2D06F29,
	Glyph_set_scale_m44247C5948E32562931FA8C44799A3E1E4F0562A,
	Glyph_get_atlasIndex_m575332307F2C182655EE9AD352E92F1B5F4D26DF,
	Glyph_set_atlasIndex_m622CB24F3110B65CADB0C9F0223133B0DA926ABE,
	Glyph_get_classDefinitionType_m06B4C1F243350980962B289B664A4AC6FDE89F85,
	Glyph_set_classDefinitionType_mFF3AA3EFDB3BC89B192DC001CEC6E650BAC61E37,
	Glyph__ctor_m9FB83C6B166AC59E03B585F76C09C5FC1720281F,
	Glyph__ctor_mCAEB1E07D206BE7EC7A459CD9D6EFFBBC7DA7C03,
	Glyph__ctor_m2E3C296A46BE48B869634BBE3F97B30F3442CC94,
	Glyph__ctor_m80F1C9D6478C98861C20F026BDA6919A206B9FDC,
	Glyph__ctor_m71D55A8FBEA48ECCD78D65CAC9D008905E56FBF1,
	Glyph_Compare_m9F99433F619E7EF484B1A603B4C8E2162E95EF72,
	FontEngine__ctor_m5EA5CC866E0E0B96C135270936391DF938F18013,
	FontEngine_InitializeFontEngine_mCA2F9C3294A61C0294B4B9849082C0344169F322,
	FontEngine_InitializeFontEngine_Internal_m64ABDC3A5AF9D87C995FF6B98979933E7074AB06,
	FontEngine_DestroyFontEngine_mE5DE04FEFB654EDE496EEEC38EBDBEBF8468A856,
	FontEngine_DestroyFontEngine_Internal_m6C2885F248EB7E05F91B9DF8780D428A8EB65F16,
	FontEngine_SendCancellationRequest_m37D2956E605C0A5D4E79B357171EB481421B7695,
	FontEngine_SendCancellationRequest_Internal_mBD3E0E85F4EFD928E9F16322E7E976C5D5F6557F,
	FontEngine_get_isProcessingDone_m6E21B2CDC24AAD7DB8BBC0EC3EBD20A9F91958B7,
	FontEngine_get_generationProgress_m5F65AEFD59F3C4011A2C8E3424671A006357BF50,
	FontEngine_LoadFontFace_mF3AD8D250D32D8B7CB347F91D409E63431CF4F9F,
	FontEngine_LoadFontFace_Internal_mAF51FEDD7DE13341FB210DC0743D877AD34416C8,
	FontEngine_LoadFontFace_m9CD5ECFDAF16C50B54B9A5F9BAE17E5B464808CD,
	FontEngine_LoadFontFace_With_Size_Internal_m9338A2134807FF890EDC04E646D0ADB3F6FBFB2D,
	FontEngine_LoadFontFace_m6DCF863A84AF3E2EEB6AC69C7C19D937909698C1,
	FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_mD89D8C9D6A2B8E7D29BAE669C15781DBCC63B8E4,
	FontEngine_LoadFontFace_m11543AEE17CD53129A31308D2F449CBA0BE1D7BE,
	FontEngine_LoadFontFace_FromSourceFontFile_Internal_mD8E89F312B1D3840975BA422268F9EE1FDA01FE0,
	FontEngine_LoadFontFace_mD4CE1EFF0B0092DE423648561AF0AC9F58CA184A,
	FontEngine_LoadFontFace_With_Size_FromSourceFontFile_Internal_mDCB5FCCC7C06A2F61CD581E428F97F59B1263336,
	FontEngine_LoadFontFace_m2FFD3573F585BE112E920FEE83F422326F017514,
	FontEngine_LoadFontFace_With_Size_And_FaceIndex_FromSourceFontFile_Internal_m94ECF937874F8CB2A2454E83474D65CD9D0AAB34,
	FontEngine_LoadFontFace_m48D4BE6D0EE255FF8DD239CBBA110402ED48E0CC,
	FontEngine_LoadFontFace_FromFont_Internal_m17C1DE5ADF3F4E04935B9D8C25D08AA1EADA3D1B,
	FontEngine_LoadFontFace_m63A9171030B36C960896CEF55E7ECF55AF990548,
	FontEngine_LoadFontFace_With_Size_FromFont_Internal_m13A7F3EB357E2D2C67B14D10B96F3C490BF6AC11,
	FontEngine_LoadFontFace_m15ECA69542615468DB27EE5DCA11EE855BDAA356,
	FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_m7C4E8F4F88CDDE6B838D890FDC1E0722EBD40497,
	FontEngine_LoadFontFace_m58B0B8AFDFCED3964C98DE267A2CC9036014ADA1,
	FontEngine_LoadFontFace_by_FamilyName_and_StyleName_Internal_m84930FF90D45392469B3A284BA6A83764A5B7A1D,
	FontEngine_LoadFontFace_m793FE6557D378E25CDF12DDA5EFAA1F0F9A51C26,
	FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_m0856180C0F5FFA09E964986A78153D021F810F30,
	FontEngine_UnloadFontFace_m09E0B255BF0B5CEDA90D1C38E1371B3440DD2E51,
	FontEngine_UnloadFontFace_Internal_mC25601FC5B89A8F169771813EF33C2B97FCCFDDF,
	FontEngine_UnloadAllFontFaces_mC810507B1901B5F369CE61DBB9AFA39410C3ED1A,
	FontEngine_UnloadAllFontFaces_Internal_m321F5FA4814E1B49D6F432E61E523A1A82DF17C2,
	FontEngine_GetSystemFontNames_m687D2EC1A3087E326AEFDDBEDBF902D5710B9C94,
	FontEngine_GetSystemFontNames_Internal_mA03C78FC31A8C0D3B08C40DFEDD4E7FE5C245214,
	FontEngine_GetSystemFontReferences_mDA521582960B0B316BEB3053FFD8A5E3C0326261,
	FontEngine_TryGetSystemFontReference_mA32D1513035E9B58417092500DDC3A7C939367A1,
	FontEngine_TryGetSystemFontReference_Internal_m61AAB78124D19B1BC88C22D520287893E7280E1F,
	FontEngine_SetFaceSize_m29B9E871E8B20E3B75AF7132FD1F5319E4D26819,
	FontEngine_SetFaceSize_Internal_mF123F8536549A1820A8AB1A25342C7B78AFBF8B3,
	FontEngine_GetFaceInfo_mF371B75CDEEDA91FF910BED8DEE8FEB2A493BE37,
	FontEngine_GetFaceInfo_Internal_m34E21653DF4724C3FCE289DA20AD5AB1B2F24B90,
	FontEngine_GetFaceCount_mB68518336320248CB257B03630A7C269B8E89D79,
	FontEngine_GetFontFaces_m7EC5E883BE91B715F363A6D5FFA83888144D3BA1,
	FontEngine_GetFontFaces_Internal_m130B4EBF35348F810B30BF42208886281F2B04FF,
	FontEngine_GetVariantGlyphIndex_m4C3A4AA47058F12996EC35715AF52F06E198B9DA,
	FontEngine_GetGlyphIndex_mEAE36421D92783413286344213D6EFD52E90CC00,
	FontEngine_TryGetGlyphIndex_m7624A886DD1A56BCB95A4B1B9F050DC8A5DC2DB9,
	FontEngine_LoadGlyph_mCA25DE5E57C3F0C503FF8E67F5F999C8BAA9FFBA,
	FontEngine_LoadGlyph_Internal_mE053AC9788323FE10F3B3CFD364B132156255921,
	FontEngine_TryGetGlyphWithUnicodeValue_m58889809E3D65A0F1C5AEFF4DF6D319EBD139159,
	FontEngine_TryGetGlyphWithUnicodeValue_Internal_mD799CD2B5CEA61ED5ABA16EDAA428385FD906BD8,
	FontEngine_TryGetGlyphWithIndexValue_mD922A7EB95949E95D96C222D2CA1ED56BA2E81C3,
	FontEngine_TryGetGlyphWithIndexValue_Internal_m2C5359B3C62139D20F52C1D232938CFAC9C3459D,
	FontEngine_TryPackGlyphInAtlas_m9F93C4D9107F4A7419867532D8CE974EBD890BD0,
	FontEngine_TryPackGlyphInAtlas_Internal_mD7C1849C5DAD16A0DCECE8B18F46C66EB6F52D8E,
	FontEngine_TryPackGlyphsInAtlas_mC4859FF2956BA1F97390A8EC6A61EFA2178D3440,
	FontEngine_TryPackGlyphsInAtlas_Internal_m0847DC54AA6A0F5A98791FFE3016D0FDAD48A144,
	FontEngine_RenderGlyphToTexture_m2E1A9146151D4632B065CD69792E97EA50ACBF3B,
	FontEngine_RenderGlyphToTexture_Internal_m26C761F2C50E95DDD7BDEEA1ABC617709B690280,
	FontEngine_RenderGlyphsToTexture_mC979B0FFAC2A11C512DF435A9E228349833DC577,
	FontEngine_RenderGlyphsToTexture_Internal_mC43F5F34F56CD622B25EFEC7F8406F03A7CB5030,
	FontEngine_RenderGlyphsToTexture_m32240D8BBE3AAE9F54DE73BAD4724F4B623D2166,
	FontEngine_RenderGlyphsToTextureBuffer_Internal_m3F34D45F88AA9935BD6627B157744ED9172F521E,
	FontEngine_RenderGlyphsToSharedTexture_mB2853E18C07BE73CB8CE776DED0D4CEF87B25D2E,
	FontEngine_RenderGlyphsToSharedTexture_Internal_m8E969EF8A38E7789C234F3CD6E8B511CBA3B99EB,
	FontEngine_SetSharedTexture_m23492D37EA965C442C6875BDABE15B365AE65B73,
	FontEngine_ReleaseSharedTexture_mA7B1FEE61B1C3E7E8844B2BB3C3F035D977AC734,
	FontEngine_SetTextureUploadMode_m04D13DFE627B79D5EB574EC74556E31DF42A83F3,
	FontEngine_TryAddGlyphToTexture_m45A94FA06ADDCE2FA6B139B29E942496B760A090,
	FontEngine_TryAddGlyphToTexture_Internal_mD6ED15BDDDC4F874C98D514D8DCE699EADB8C708,
	FontEngine_TryAddGlyphsToTexture_m25FA7243A69693449F7F5E109F39E758F03376D9,
	FontEngine_TryAddGlyphsToTexture_Internal_MultiThread_m4C938678D281A80A1CB576EFFC1AA8C2FC860C28,
	FontEngine_TryAddGlyphsToTexture_m18740AD9F7264F54C397916268C0AB0738879801,
	FontEngine_TryAddGlyphsToTexture_Internal_m43D4D242873C647DF5A20F7579FD90E373999EA8,
	FontEngine_GetOpenTypeLayoutTable_m6E3FEED945F5DEB760F602E64BBC303F40169D01,
	FontEngine_GetOpenTypeLayoutScripts_mD75DC816DE6F0F7A817EBD5C5B24A2B072087AF1,
	FontEngine_GetOpenTypeLayoutFeatures_mB9E4374C068B14C96DFD75CED59C0B18697CEA4D,
	FontEngine_GetOpenTypeLayoutLookups_m36263ACB798AB0B23B022994D36E58E3890B2728,
	FontEngine_GetOpenTypeFontFeatureList_m5CD64F81C7DD797D26BC803BD4FC62864630A7D1,
	FontEngine_GetAllSingleSubstitutionRecords_mD17E69A9683D9CF1AD58F645C34BEE0909F11FB9,
	FontEngine_GetSingleSubstitutionRecords_mBD8559C9872AE511D1354E12E1CBB9CEAFF187C5,
	FontEngine_GetSingleSubstitutionRecords_m2E4DBFBEE4C05227DD6BB62ECE15E026B9B11702,
	FontEngine_GetSingleSubstitutionRecords_m9F204C0A1EACA448C70C4ABC8C562376C4B54237,
	FontEngine_PopulateSingleSubstitutionRecordMarshallingArray_from_GlyphIndexes_m08696426BC414C9377A1D0EA088C5CDF2F5EFF97,
	FontEngine_GetSingleSubstitutionRecordsFromMarshallingArray_m367C216DBEFC901EAE8BA89EFA5B064C9345B8B2,
	FontEngine_GetAllMultipleSubstitutionRecords_mCC653F3E102255BCA03EB06357259B01929E3028,
	FontEngine_GetMultipleSubstitutionRecords_m3FA13066E21933EC0B68D271C497DDF84D42F7CB,
	FontEngine_GetMultipleSubstitutionRecords_m3A52CB7270EDE5243182AD33358F1C8D9060EBC7,
	FontEngine_GetMultipleSubstitutionRecords_mBFF4AD72E99F503F90B154876328D9A240AFD667,
	FontEngine_PopulateMultipleSubstitutionRecordMarshallingArray_from_GlyphIndexes_m4CBDEB8FE75DC34B642180DAF9F3E84D9BFF24AA,
	FontEngine_GetMultipleSubstitutionRecordsFromMarshallingArray_mFD66EDDC06637158FCC4755BAE17DF099FDDD733,
	FontEngine_GetAllAlternateSubstitutionRecords_mD13307D20DEBBC0144419DE66E74217A23AC98D3,
	FontEngine_GetAlternateSubstitutionRecords_mB9C9985993D2923087BF9238486134C063FB177D,
	FontEngine_GetAlternateSubstitutionRecords_mFD279E3D5509ADB7156B9E42D2370A9368BB5B2E,
	FontEngine_GetAlternateSubstitutionRecords_m1E55EB9EE2A62972588418C3A5363715F50B954C,
	FontEngine_PopulateAlternateSubstitutionRecordMarshallingArray_from_GlyphIndexes_m643BCB05471E3ADFB75246D652CC963F5868E765,
	FontEngine_GetAlternateSubstitutionRecordsFromMarshallingArray_m6D7363B0B32E47F377CE3BD4EF78CA16D7E68789,
	FontEngine_GetAllLigatureSubstitutionRecords_m362CD420864108B994FCA46FCE362CC8E7CE05B6,
	FontEngine_GetLigatureSubstitutionRecords_m4189533C549B466BC84F22F5B8F6A24E18258134,
	FontEngine_GetLigatureSubstitutionRecords_m65ADF14EB9A148D4BCB7DF107D99A83BEDF36009,
	FontEngine_GetLigatureSubstitutionRecords_mF8D49054EA5590D6D3EE74C597298C507ED1569A,
	FontEngine_GetLigatureSubstitutionRecords_m728CAEE30E649D00FBC9E1A6C62EDE769B5D38C1,
	FontEngine_GetLigatureSubstitutionRecords_mAE1873C8AED5673CBF3789CB2E4C1E1FFA0E272F,
	FontEngine_GetLigatureSubstitutionRecords_m2F7295EC1D6EEC1DAED317433A93DF13D0FBF637,
	FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_mFF6E6E4EF766AFDCE5E94ACD22704995EA1774A7,
	FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_for_LookupIndex_mC269C1A9E8E04AA7B22DFD9EB101A1BF4CC7AEEC,
	FontEngine_GetLigatureSubstitutionRecordsFromMarshallingArray_mB3F24B46CFCB60CFCC8C054A71F2B3E534A70B72,
	FontEngine_GetAllContextualSubstitutionRecords_m1301DC9B0BCCFF6FC487038A1CD8856FA38A07C3,
	FontEngine_GetContextualSubstitutionRecords_m5E2658BE3B777FC51A7D5C49ACACE198D58715F6,
	FontEngine_GetContextualSubstitutionRecords_m308AABCF0DCBFE53AFBCCB9E6914C2E0A07F5A3F,
	FontEngine_GetContextualSubstitutionRecords_mFB6282AA5C1888EA6799C2EEF94582CCF422EE53,
	FontEngine_PopulateContextualSubstitutionRecordMarshallingArray_from_GlyphIndexes_mF84DA309F04F98AF0DC0AF252C8368DB4651CA8C,
	FontEngine_GetContextualSubstitutionRecordsFromMarshallingArray_m487D548644DCC05412390B009D751674C3C8FC4C,
	FontEngine_GetAllChainingContextualSubstitutionRecords_mF932A8BDC4A311D7A90F910C6E2CB6B0AD7ABDCD,
	FontEngine_GetChainingContextualSubstitutionRecords_m42D56A69684DEE46B6C3FDC9EBB4D8B12710B752,
	FontEngine_GetChainingContextualSubstitutionRecords_m2EDB681762EAF1DE99AE018FB19268188D35EB33,
	FontEngine_GetChainingContextualSubstitutionRecords_mA9921BE959ECA292AB75B8AF3DD82C8784B28B07,
	FontEngine_PopulateChainingContextualSubstitutionRecordMarshallingArray_from_GlyphIndexes_m4E83F547A1081C66D8AF1EDBA35D8F9A7B40A80B,
	FontEngine_GetChainingContextualSubstitutionRecordsFromMarshallingArray_mEA1B3D95450624CF1177CE525133BB4AC6CC293E,
	FontEngine_GetGlyphPairAdjustmentTable_m67DAC7C0029384FC814621F85F8B35D0D3327BC5,
	FontEngine_GetGlyphPairAdjustmentRecords_m23D346BEC5BA63185A01DF33576E98650947ABA8,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_KernTable_mE3B5D21C5D72CEADB2BEA5AA8022E466356386E7,
	FontEngine_GetGlyphPairAdjustmentRecords_m17810E7931ECB679ABB4075F7905297CF01A4306,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_GlyphIndex_m683C1CB2CA87F7EC8284F734E3E8399F549CC740,
	FontEngine_GetGlyphPairAdjustmentRecords_mC7573525397545B2DFA0996046F6C3DB3ECEED47,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_for_NewlyAddedGlyphIndexes_m5F1BF301D27185C895ED78C8CA11A0B4F265FAE3,
	FontEngine_GetGlyphPairAdjustmentRecord_m79F7FEA519DC93AD1487058386DF2FC6880ED1A3,
	FontEngine_GetSingleAdjustmentRecords_m3095AC5F622D225C71A7456294A0CC4773908945,
	FontEngine_GetSingleAdjustmentRecords_m45CB1804F760369B9EE5369E9BFEB8B9DE07B573,
	FontEngine_GetSingleAdjustmentRecords_m3BC9816CAEC2BBB12032C812943BA125D66A017B,
	FontEngine_PopulateSingleAdjustmentRecordMarshallingArray_from_GlyphIndexes_mA6A5B513EFDFB02316F4AF3C670EB1C565EAEDD8,
	FontEngine_GetSingleAdjustmentRecordsFromMarshallingArray_m508F7F4E00B40648F128750433F671B6AA418908,
	FontEngine_GetPairAdjustmentRecords_m732A89603A857BA1007BDA90EC87DDAF46E18EAA,
	FontEngine_GetPairAdjustmentRecord_mBCBCF553E9795ECF71AE8406F4CA630AF8183020,
	FontEngine_GetAllPairAdjustmentRecords_m1994497F95AB84FFB5982B00E585C4559B8191AC,
	FontEngine_GetPairAdjustmentRecords_m763EA14AA400F46B391CD6B81AE7CD897A66565B,
	FontEngine_GetPairAdjustmentRecords_m477F7ADB06BF983D42ACFBD0FC18DDBE1C804B53,
	FontEngine_GetPairAdjustmentRecords_m5672E694566FB86272576FA050C95538D861CE13,
	FontEngine_GetPairAdjustmentRecords_mE8570C2BF546FDD3B9EFEA60C211B0F204ED4416,
	FontEngine_GetPairAdjustmentRecords_m832CCA888FBACDECCC204123EF0C79EBB05DC77F,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_m31A262416D56194C32CCE05C12DBEB31B22A36CD,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_for_LookupIndex_m2B6A3354F628DE35B8E080BAE5B0D3A771F9883B,
	FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_m81121A95D2196747D3597C8BEFF53842577599F6,
	FontEngine_GetAllMarkToBaseAdjustmentRecords_m046FCE9EBE1B7011A6B52531AB694C45041B42F6,
	FontEngine_GetMarkToBaseAdjustmentRecords_mEAE470B6CA2D95139BC8DCCDC6A149CD684A4080,
	FontEngine_GetMarkToBaseAdjustmentRecord_mD38FFAC92C55B82D1180E7097B8747F5D0C94E17,
	FontEngine_GetMarkToBaseAdjustmentRecords_mBBA5CD78F1F95CA8198EA76AFBDC7D0BD2C26265,
	FontEngine_GetMarkToBaseAdjustmentRecords_m29F1E828E6CEE345FCDC2B9E611EF091E609D4D1,
	FontEngine_GetMarkToBaseAdjustmentRecords_m408B8B0363B6F923D696140EFC62370A4928686A,
	FontEngine_GetMarkToBaseAdjustmentRecords_m84E6B2CCA908CA059AAEAAC9A4C13C5EB7F3AC5B,
	FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_m20E04208FC0890DF48163AC3ABBF33F23E57237D,
	FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_for_LookupIndex_m5B5138782A4A3ACEFA70DBCDB952E88E2795426A,
	FontEngine_GetMarkToBaseAdjustmentRecordsFromMarshallingArray_mF8E57B182D5061FDE535AD9F4EA842B600710491,
	FontEngine_GetAllMarkToMarkAdjustmentRecords_mA11C88084182EB94F428AA97E40448C93B5A64A2,
	FontEngine_GetMarkToMarkAdjustmentRecords_m43A916A5F4BB5542F4ED505C230C9BB5681F1DBB,
	FontEngine_GetMarkToMarkAdjustmentRecord_mEBAFE236D9ACB2DACFEFDAC45E92602179D2EE3A,
	FontEngine_GetMarkToMarkAdjustmentRecords_m53AC560161E74D387D08ECFCA1255CC1763AA10A,
	FontEngine_GetMarkToMarkAdjustmentRecords_mED59CC785ABB86DAFE872780C1FC8A974925F16C,
	FontEngine_GetMarkToMarkAdjustmentRecords_m83EC8E58C4706BA4B4CC3C045DAC9D3F057F120E,
	FontEngine_GetMarkToMarkAdjustmentRecords_mD80A02BE943D75890025C64861BD970ADD660FA3,
	FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_m9C7BC1E19B7F3ED79079E1308CBDB5790287F9D2,
	FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_for_LookupIndex_m660EE914924C42B8C291A8A0BF0BC1DC7AE31483,
	FontEngine_GetMarkToMarkAdjustmentRecordsFromMarshallingArray_mD8679B420C4AB3D372C6364D55C4BBE2CB576889,
	FontEngine_GlyphIndexToMarshallingArray_mF3A3CD803687D55F90DED04F3AE52284EFB89831,
	NULL,
	NULL,
	FontEngine_ResetAtlasTexture_m15BBE67DFDD8A1E740BC0C4B29612A8C866860DC,
	FontEngine_RenderBufferToTexture_m14E5B43D0EABE692878CADC2BC20362F0694CFED,
	FontEngine__cctor_mD8EC115E258FD225726CBC71B249C1F411447D79,
	FontEngine_RenderGlyphToTexture_Internal_Injected_mD3096F31E7CEEE9401936450D0C2CFD9CEE2AEDE,
	FontEngine_GetOpenTypeLayoutTable_Injected_mADA2FABD8FB5F6C5D0451310D10C72EDE3FEFF89,
	FontEngine_GetGlyphPairAdjustmentRecord_Injected_m3758944CBC2B87C214BB375B859F0F6E3E81DD8D,
	FontEngine_GetPairAdjustmentRecord_Injected_m3E55DD6CB4A898FE7A110B7F61D65F4D8B53522D,
	FontEngine_GetMarkToBaseAdjustmentRecord_Injected_m294C43885873BA0F0D0A0B165FC1C742AA669A9F,
	FontEngine_GetMarkToMarkAdjustmentRecord_Injected_mC7772FEF202FE1AE5EFCB053D0FA094CBA63B26B,
	FontEngineUtilities_Approximately_m4D1C3053A5A0F6974E1820D3B0D26530ED073389,
	FontEngineUtilities_MaxValue_m7E0FBF90FE07F65C9895ACD56FD032A53E417F83,
	GlyphMarshallingStruct__ctor_mF74998CA1395853967081C3799D6EC0AEADFAD8D,
	GlyphMarshallingStruct__ctor_mF944582B068F848BECC4D80278F3AEFD33843129,
	GlyphMarshallingStruct__ctor_m7E489C38186D01A566DD1FC72D6B6D2846DBFBCD,
	NULL,
	OpenTypeLayoutLookup_UpdateRecords_m6AF4754A1DAEEDE9F75113BC06029EB1F0931271,
	OpenTypeLayoutLookup_UpdateRecords_m6C7A5700D6122215DBB6D246CB2A9C14370030A2,
	OpenTypeLayoutLookup_UpdateRecords_m5082BD3D95DFFEC11D7AEBED5EB6A588F5B850F7,
	OpenTypeLayoutLookup_UpdateRecords_mCBFDE6D2FE4AF94A7CF4FD4E370792393D1D65CD,
	NULL,
	OpenTypeLayoutLookup__ctor_mD9392C8C75ABC627FCCA874611E0609E03B33747,
	GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8,
	GlyphValueRecord_set_xPlacement_m79F92029922BDE50ED63A6A03EBE478869F1CCFC,
	GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA,
	GlyphValueRecord_set_yPlacement_m04DA300FAB827A708CB291DA3B2EA3128279CA2B,
	GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E,
	GlyphValueRecord_set_xAdvance_mA8475DBF8792908481A5DA5FFE7C7FAD3E596A35,
	GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6,
	GlyphValueRecord_set_yAdvance_m37EDC0646862034F5625BC58428B7E2F3869D0D4,
	GlyphValueRecord__ctor_m01488AC8714CC329C1A23B034C1ABF4B52F0740A,
	GlyphValueRecord_op_Addition_mF26165B4CE61A5409AEFF24B0D1727804E13602B,
	GlyphValueRecord_op_Multiply_m3F7E8FB8B2AB345D65B80AB9F3C464DAF3AD2D5D,
	GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60,
	GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6,
	GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972,
	GlyphValueRecord_op_Equality_m2AFC5B719C0208115B6990D2808776B3A1C90A59,
	GlyphValueRecord_op_Inequality_mA07F4D32FB5AC7EE656E0CEC880D37FFE2B10921,
	GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9,
	GlyphAdjustmentRecord_set_glyphIndex_m97850B31C30FA0AC2B497A6F864F76E70D189446,
	GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7,
	GlyphAdjustmentRecord_set_glyphValueRecord_m5107E793964C7C945DD62D330589AA7D14777047,
	GlyphAdjustmentRecord__ctor_m54853163D58AD883E19A7C0D795A4CF73FCAE28C,
	GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C,
	GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D,
	GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535,
	GlyphAdjustmentRecord_op_Equality_mA86E910E24C5AD36005A9694FC635DA4A19BCB61,
	GlyphAdjustmentRecord_op_Inequality_mBC02C69F7B6C9564D6F3B7131DC5B69CF0BC8063,
	GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C,
	GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_mA19E5BFDFB5B5EF6B82BE195BC407390E3754296,
	GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91,
	GlyphPairAdjustmentRecord_set_secondAdjustmentRecord_m5C90F3C44ADD213E181BDB955AB214B6F56B2FBF,
	GlyphPairAdjustmentRecord_get_featureLookupFlags_m08DA76766FDE949068B881DBEA29955C9C43E8A9,
	GlyphPairAdjustmentRecord_set_featureLookupFlags_m4900CDB13F697D5CD7012383E71910AEAD170359,
	GlyphPairAdjustmentRecord__ctor_m2B1BCCA950399944824CFB79D74C39A7A4F87A6A,
	GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5,
	GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD,
	GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689,
	GlyphPairAdjustmentRecord_op_Equality_m0F93964041166C7D04A88FEA17D6E52102AC14AF,
	GlyphPairAdjustmentRecord_op_Inequality_mE925301B5EAA3E6E3FBD08527AA9386CFF2B591D,
	GlyphAnchorPoint_get_xCoordinate_mCD33464763911ECB78DEB1965970A916FA27DD1C,
	GlyphAnchorPoint_set_xCoordinate_m6820F49984BD779C6274A656AE013575C2E121FA,
	GlyphAnchorPoint_get_yCoordinate_m2683C19C6A3D750E4D6C536307313E55589909D6,
	GlyphAnchorPoint_set_yCoordinate_m3DC770A8B68515510EC83140E7CC2164880EDA97,
	MarkPositionAdjustment_get_xPositionAdjustment_m5ACBB4C515357320C12597CAE5E4D409BA298765,
	MarkPositionAdjustment_set_xPositionAdjustment_mBD0304ADF7B91F788755A319868513C1D5C7020C,
	MarkPositionAdjustment_get_yPositionAdjustment_m1F5F7DBBFEB0B52CCC772F68664D06B11D6A9F2C,
	MarkPositionAdjustment_set_yPositionAdjustment_m80D07753C658004FD58CD20B4B0D70EFB791836E,
	MarkPositionAdjustment__ctor_mBD39BFE7BE793324DDF1C67BAFE2C9DEE069286B,
	MarkToBaseAdjustmentRecord_get_baseGlyphID_mD6A0DD18DBE69E0E68ACA6AABF47B1EA61B633A5,
	MarkToBaseAdjustmentRecord_set_baseGlyphID_mC8151EF01F0FCFFA0611EB65EC03099B6562ABAC,
	MarkToBaseAdjustmentRecord_get_baseGlyphAnchorPoint_mCBF57932B7A89C532B0EF750DFD81F8FE389EE08,
	MarkToBaseAdjustmentRecord_set_baseGlyphAnchorPoint_m1C79E0AD92B197313FCD2273FD57CB6CA3B3E379,
	MarkToBaseAdjustmentRecord_get_markGlyphID_mB4BB0291733ECFC2433F5C2837F0B28EB05CAF5C,
	MarkToBaseAdjustmentRecord_set_markGlyphID_mCA5B1438023B89A5FDE7B6F8F1C20F7CEB8A8C09,
	MarkToBaseAdjustmentRecord_get_markPositionAdjustment_m570715D1D0F84361A90564D4A958394453E1F9AB,
	MarkToBaseAdjustmentRecord_set_markPositionAdjustment_m585228E1ADB46FEB33EF9400B3A477FEE6559AFC,
	MarkToMarkAdjustmentRecord_get_baseMarkGlyphID_mA0EAEA751467A8841F9D20D122C4998905A508CC,
	MarkToMarkAdjustmentRecord_set_baseMarkGlyphID_m3F60246B56DB78EF3754421041378A08E9FC018F,
	MarkToMarkAdjustmentRecord_get_baseMarkGlyphAnchorPoint_mB87ADA10491B42650BAD4DB7330771061827ACAB,
	MarkToMarkAdjustmentRecord_set_baseMarkGlyphAnchorPoint_mF99F7E7CBCB4B3C36D87BC6F6B92232EEE63B17B,
	MarkToMarkAdjustmentRecord_get_combiningMarkGlyphID_m84613A2A27F87AA21CAEDD08759031302C9A8FBF,
	MarkToMarkAdjustmentRecord_set_combiningMarkGlyphID_mABD6E44107C367EE21EB258DB5681707C4B34075,
	MarkToMarkAdjustmentRecord_get_combiningMarkPositionAdjustment_mC109ECEDB4AD314A25C0EB1F6F6151AE611DE15C,
	MarkToMarkAdjustmentRecord_set_combiningMarkPositionAdjustment_mFAB0D9C53F43D866DD2DA6F3B85A5E1AE23897EC,
	SingleSubstitutionRecord_get_targetGlyphID_mFD058D5092C3674A7B71EBA67FD6829AC51B5359,
	SingleSubstitutionRecord_set_targetGlyphID_m76B6E23641060D8C394379DE91B051D867BD090A,
	SingleSubstitutionRecord_get_substituteGlyphID_mB14D0FA76335020198ADE6D10ABEBF06D0214B7D,
	SingleSubstitutionRecord_set_substituteGlyphID_m93A8DF73A184C7E38F3199C4258258E81852E052,
	MultipleSubstitutionRecord_get_targetGlyphID_mF01201C747BA2FAEF8567CFDA974F6473E09D162,
	MultipleSubstitutionRecord_set_targetGlyphID_mF237E40BBF7D12A45DA5662E11FC3DED640310F2,
	MultipleSubstitutionRecord_get_substituteGlyphIDs_m6391274416D6936BE038ED3546BDBB5D88AE6451,
	MultipleSubstitutionRecord_set_substituteGlyphIDs_m4F558CBB6F2EBBE4CBD74E2EA049518E0C457B42,
	AlternateSubstitutionRecord_get_targetGlyphID_m46389FE0D0A235D35A96A5F3BEECD33D3E269BBA,
	AlternateSubstitutionRecord_set_targetGlyphID_m3F04DAA4001337ED8501DFA61DA45D11E16C4142,
	AlternateSubstitutionRecord_get_substituteGlyphIDs_mB88AA4734922B7304398923CDBD7EC6ACDA3AC86,
	AlternateSubstitutionRecord_set_substituteGlyphIDs_mC6DD2CE2FBE55E3CD9276F8F21EAE718A5B3A98C,
	LigatureSubstitutionRecord_get_componentGlyphIDs_m3BBDC9421E3A7369B198379F5433FBB13ADCE628,
	LigatureSubstitutionRecord_set_componentGlyphIDs_mDD8D76279FF545B516D4CE689568AA513A95EF63,
	LigatureSubstitutionRecord_get_ligatureGlyphID_m5FD629E204026FB8B6279498CDE5CAB1D23827EC,
	LigatureSubstitutionRecord_set_ligatureGlyphID_mDB4F809745EF0536182FAF90AFB276F389C35EB6,
	GlyphIDSequence_get_glyphIDs_m9BEB975FB20104387FAC6120BB90D4055E854BB1,
	GlyphIDSequence_set_glyphIDs_mB877FBB483AAD37119B32B2797E6BACB2EE22256,
	SequenceLookupRecord_get_glyphSequenceIndex_mDFE2CB0E6F409FC6E76371AE9C901787A043EDBC,
	SequenceLookupRecord_set_glyphSequenceIndex_m4903487C7478B5BC34166C892CB3DDA69C5D6A45,
	SequenceLookupRecord_get_lookupListIndex_m59A597A93C9DB6D00BE7F408327AFF551D25D829,
	SequenceLookupRecord_set_lookupListIndex_mC1F94638EBF47068E6D9BEE948FF08CA6227306C,
	ContextualSubstitutionRecord_get_inputSequences_mA178071C71E922AB18142760AE712C3D37B11749,
	ContextualSubstitutionRecord_set_inputSequences_mB9FFBA40115AF953C54C23520A0D6EED2124D792,
	ContextualSubstitutionRecord_get_sequenceLookupRecords_m888925F050EFB2980B87BCDCCC342E4BECA034FC,
	ContextualSubstitutionRecord_set_sequenceLookupRecords_m63A429B7D96EA86DBD8128908ACA96ADB7FA494A,
	ChainingContextualSubstitutionRecord_get_backtrackGlyphSequences_m7FD183544371F7A853BBF197E66592C0C2D9D8A1,
	ChainingContextualSubstitutionRecord_set_backtrackGlyphSequences_m249B99EB124ACB0A7B27D234A4F10EF3F87ACB4E,
	ChainingContextualSubstitutionRecord_get_inputGlyphSequences_m0236D68BE22540A633BB2590AE34282BD6EA5F97,
	ChainingContextualSubstitutionRecord_set_inputGlyphSequences_mEAC5074B5B0A5BDAC19359134C514DC76D33C760,
	ChainingContextualSubstitutionRecord_get_lookaheadGlyphSequences_m3C8A441E18555617D4D3455978894962D3BA6FE4,
	ChainingContextualSubstitutionRecord_set_lookaheadGlyphSequences_m061486A5432AB15C4BACBA8AC2851A5449219594,
	ChainingContextualSubstitutionRecord_get_sequenceLookupRecords_mB0FAEACA23F664767344BD416081AED5BE813C00,
	ChainingContextualSubstitutionRecord_set_sequenceLookupRecords_m5AD305FAC33438FE23703DC65C6031A27A7D14BF,
};
extern void FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641_AdjustorThunk (void);
extern void FaceInfo_set_faceIndex_m6B57F32E81090DCB77DA466535224CAC5ACF9AFA_AdjustorThunk (void);
extern void FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241_AdjustorThunk (void);
extern void FaceInfo_set_familyName_m49CB07A51AC9008B4F399A3064EC4FF6EA8E839D_AdjustorThunk (void);
extern void FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6_AdjustorThunk (void);
extern void FaceInfo_set_styleName_m3BEBF7E576032A668BCC83D88D2F6902F561B5E6_AdjustorThunk (void);
extern void FaceInfo_get_pointSize_m7EF7429A4725AB715931A220F6BB498C3D6BF7CB_AdjustorThunk (void);
extern void FaceInfo_set_pointSize_m17D0B03C4A762F657A4ABD15327B08D6B55EF492_AdjustorThunk (void);
extern void FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD_AdjustorThunk (void);
extern void FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4_AdjustorThunk (void);
extern void FaceInfo_get_unitsPerEM_m92CD79B71FC504DE1C87923716E999D85A35AC0E_AdjustorThunk (void);
extern void FaceInfo_set_unitsPerEM_m3E48D789D666984A4EA8D221BED22833761E9A90_AdjustorThunk (void);
extern void FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D_AdjustorThunk (void);
extern void FaceInfo_set_lineHeight_m5952A394C6055DD86700448D9E33EBBE852E05AE_AdjustorThunk (void);
extern void FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD_AdjustorThunk (void);
extern void FaceInfo_set_ascentLine_mDFB32635374875A695D3BB686D2A16C648A20D78_AdjustorThunk (void);
extern void FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88_AdjustorThunk (void);
extern void FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF_AdjustorThunk (void);
extern void FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96_AdjustorThunk (void);
extern void FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99_AdjustorThunk (void);
extern void FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A_AdjustorThunk (void);
extern void FaceInfo_set_baseline_m528F6ADAF4F45A31E9D2BA362FB3C02B03DDD741_AdjustorThunk (void);
extern void FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3_AdjustorThunk (void);
extern void FaceInfo_set_descentLine_m62423E864258229A85FD3684D8385A44C01AF97C_AdjustorThunk (void);
extern void FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A_AdjustorThunk (void);
extern void FaceInfo_set_superscriptOffset_mC81144590FA80858D489AFC46AAA383E11D3D2B5_AdjustorThunk (void);
extern void FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0_AdjustorThunk (void);
extern void FaceInfo_set_superscriptSize_m89F17C1502C07A3846402A5A43369BFB66723515_AdjustorThunk (void);
extern void FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A_AdjustorThunk (void);
extern void FaceInfo_set_subscriptOffset_m796EF61DE0677BFB134F6B8C4B6C1869BCF8782A_AdjustorThunk (void);
extern void FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31_AdjustorThunk (void);
extern void FaceInfo_set_subscriptSize_m5759439F9D100591A6FFF1D7377495CEBDCD6B5D_AdjustorThunk (void);
extern void FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251_AdjustorThunk (void);
extern void FaceInfo_set_underlineOffset_m1C0E755772FE8C47D565E6CC7DC4E953B71794AA_AdjustorThunk (void);
extern void FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2_AdjustorThunk (void);
extern void FaceInfo_set_underlineThickness_mDD002D08CE97E0C7DA6FA6FFBDD15295D24B303A_AdjustorThunk (void);
extern void FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5_AdjustorThunk (void);
extern void FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0_AdjustorThunk (void);
extern void FaceInfo_get_strikethroughThickness_m079405B5FDA3F9CC4147597444598A173CFD74C3_AdjustorThunk (void);
extern void FaceInfo_set_strikethroughThickness_m8CA3C9897FF3D5D0F653539CCBF5E16E01CACF52_AdjustorThunk (void);
extern void FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161_AdjustorThunk (void);
extern void FaceInfo_set_tabWidth_m44234ED657FAB54320C48C34D48532F8232F6E1D_AdjustorThunk (void);
extern void FaceInfo__ctor_m5392B651EAD0672BE1FF0EB8E878B6078D9C3201_AdjustorThunk (void);
extern void FaceInfo_Compare_m5C40431846D8FC040C4AC93EEBCE599DF40E4940_AdjustorThunk (void);
extern void GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898_AdjustorThunk (void);
extern void GlyphRect_set_x_m1AF3570B0730D18F11DEBCC5D639EE83549C06E1_AdjustorThunk (void);
extern void GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA_AdjustorThunk (void);
extern void GlyphRect_set_y_mE9697A41067D3BCB1CF0B92472A259A94FB95257_AdjustorThunk (void);
extern void GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12_AdjustorThunk (void);
extern void GlyphRect_set_width_mD99A7B04328F95C75FA2403C7E6DDC3CA40951CD_AdjustorThunk (void);
extern void GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A_AdjustorThunk (void);
extern void GlyphRect_set_height_m4E34D98DDD7A403571AEC2C3A05F4AE07453085E_AdjustorThunk (void);
extern void GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95_AdjustorThunk (void);
extern void GlyphRect__ctor_m6540996F96C6A3C7BA757EDC4A02196993EC3400_AdjustorThunk (void);
extern void GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F_AdjustorThunk (void);
extern void GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B_AdjustorThunk (void);
extern void GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87_AdjustorThunk (void);
extern void GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13_AdjustorThunk (void);
extern void GlyphMetrics_set_width_m236B586A8A7CB4FD2C46E6581ADB2037CD6019A4_AdjustorThunk (void);
extern void GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C_AdjustorThunk (void);
extern void GlyphMetrics_set_height_m98DA746BE7362BD1EE03D4083213DA33B6306CF1_AdjustorThunk (void);
extern void GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F_AdjustorThunk (void);
extern void GlyphMetrics_set_horizontalBearingX_m5F05916CA7AC363ED6AEE4AD2809B9925D829F0C_AdjustorThunk (void);
extern void GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223_AdjustorThunk (void);
extern void GlyphMetrics_set_horizontalBearingY_m3459042D77350137A7CD0EAC0401103F151D5314_AdjustorThunk (void);
extern void GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1_AdjustorThunk (void);
extern void GlyphMetrics_set_horizontalAdvance_m13C7C6BDFA7BC4A384F8517193FAE21CA614B814_AdjustorThunk (void);
extern void GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D_AdjustorThunk (void);
extern void GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6_AdjustorThunk (void);
extern void GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970_AdjustorThunk (void);
extern void GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB_AdjustorThunk (void);
extern void GlyphMarshallingStruct__ctor_mF74998CA1395853967081C3799D6EC0AEADFAD8D_AdjustorThunk (void);
extern void GlyphMarshallingStruct__ctor_mF944582B068F848BECC4D80278F3AEFD33843129_AdjustorThunk (void);
extern void GlyphMarshallingStruct__ctor_m7E489C38186D01A566DD1FC72D6B6D2846DBFBCD_AdjustorThunk (void);
extern void GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8_AdjustorThunk (void);
extern void GlyphValueRecord_set_xPlacement_m79F92029922BDE50ED63A6A03EBE478869F1CCFC_AdjustorThunk (void);
extern void GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA_AdjustorThunk (void);
extern void GlyphValueRecord_set_yPlacement_m04DA300FAB827A708CB291DA3B2EA3128279CA2B_AdjustorThunk (void);
extern void GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E_AdjustorThunk (void);
extern void GlyphValueRecord_set_xAdvance_mA8475DBF8792908481A5DA5FFE7C7FAD3E596A35_AdjustorThunk (void);
extern void GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6_AdjustorThunk (void);
extern void GlyphValueRecord_set_yAdvance_m37EDC0646862034F5625BC58428B7E2F3869D0D4_AdjustorThunk (void);
extern void GlyphValueRecord__ctor_m01488AC8714CC329C1A23B034C1ABF4B52F0740A_AdjustorThunk (void);
extern void GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60_AdjustorThunk (void);
extern void GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6_AdjustorThunk (void);
extern void GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_set_glyphIndex_m97850B31C30FA0AC2B497A6F864F76E70D189446_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_set_glyphValueRecord_m5107E793964C7C945DD62D330589AA7D14777047_AdjustorThunk (void);
extern void GlyphAdjustmentRecord__ctor_m54853163D58AD883E19A7C0D795A4CF73FCAE28C_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_mA19E5BFDFB5B5EF6B82BE195BC407390E3754296_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_set_secondAdjustmentRecord_m5C90F3C44ADD213E181BDB955AB214B6F56B2FBF_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_get_featureLookupFlags_m08DA76766FDE949068B881DBEA29955C9C43E8A9_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_set_featureLookupFlags_m4900CDB13F697D5CD7012383E71910AEAD170359_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord__ctor_m2B1BCCA950399944824CFB79D74C39A7A4F87A6A_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689_AdjustorThunk (void);
extern void GlyphAnchorPoint_get_xCoordinate_mCD33464763911ECB78DEB1965970A916FA27DD1C_AdjustorThunk (void);
extern void GlyphAnchorPoint_set_xCoordinate_m6820F49984BD779C6274A656AE013575C2E121FA_AdjustorThunk (void);
extern void GlyphAnchorPoint_get_yCoordinate_m2683C19C6A3D750E4D6C536307313E55589909D6_AdjustorThunk (void);
extern void GlyphAnchorPoint_set_yCoordinate_m3DC770A8B68515510EC83140E7CC2164880EDA97_AdjustorThunk (void);
extern void MarkPositionAdjustment_get_xPositionAdjustment_m5ACBB4C515357320C12597CAE5E4D409BA298765_AdjustorThunk (void);
extern void MarkPositionAdjustment_set_xPositionAdjustment_mBD0304ADF7B91F788755A319868513C1D5C7020C_AdjustorThunk (void);
extern void MarkPositionAdjustment_get_yPositionAdjustment_m1F5F7DBBFEB0B52CCC772F68664D06B11D6A9F2C_AdjustorThunk (void);
extern void MarkPositionAdjustment_set_yPositionAdjustment_m80D07753C658004FD58CD20B4B0D70EFB791836E_AdjustorThunk (void);
extern void MarkPositionAdjustment__ctor_mBD39BFE7BE793324DDF1C67BAFE2C9DEE069286B_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_get_baseGlyphID_mD6A0DD18DBE69E0E68ACA6AABF47B1EA61B633A5_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_set_baseGlyphID_mC8151EF01F0FCFFA0611EB65EC03099B6562ABAC_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_get_baseGlyphAnchorPoint_mCBF57932B7A89C532B0EF750DFD81F8FE389EE08_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_set_baseGlyphAnchorPoint_m1C79E0AD92B197313FCD2273FD57CB6CA3B3E379_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_get_markGlyphID_mB4BB0291733ECFC2433F5C2837F0B28EB05CAF5C_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_set_markGlyphID_mCA5B1438023B89A5FDE7B6F8F1C20F7CEB8A8C09_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_get_markPositionAdjustment_m570715D1D0F84361A90564D4A958394453E1F9AB_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_set_markPositionAdjustment_m585228E1ADB46FEB33EF9400B3A477FEE6559AFC_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_get_baseMarkGlyphID_mA0EAEA751467A8841F9D20D122C4998905A508CC_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_set_baseMarkGlyphID_m3F60246B56DB78EF3754421041378A08E9FC018F_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_get_baseMarkGlyphAnchorPoint_mB87ADA10491B42650BAD4DB7330771061827ACAB_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_set_baseMarkGlyphAnchorPoint_mF99F7E7CBCB4B3C36D87BC6F6B92232EEE63B17B_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_get_combiningMarkGlyphID_m84613A2A27F87AA21CAEDD08759031302C9A8FBF_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_set_combiningMarkGlyphID_mABD6E44107C367EE21EB258DB5681707C4B34075_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_get_combiningMarkPositionAdjustment_mC109ECEDB4AD314A25C0EB1F6F6151AE611DE15C_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_set_combiningMarkPositionAdjustment_mFAB0D9C53F43D866DD2DA6F3B85A5E1AE23897EC_AdjustorThunk (void);
extern void SingleSubstitutionRecord_get_targetGlyphID_mFD058D5092C3674A7B71EBA67FD6829AC51B5359_AdjustorThunk (void);
extern void SingleSubstitutionRecord_set_targetGlyphID_m76B6E23641060D8C394379DE91B051D867BD090A_AdjustorThunk (void);
extern void SingleSubstitutionRecord_get_substituteGlyphID_mB14D0FA76335020198ADE6D10ABEBF06D0214B7D_AdjustorThunk (void);
extern void SingleSubstitutionRecord_set_substituteGlyphID_m93A8DF73A184C7E38F3199C4258258E81852E052_AdjustorThunk (void);
extern void MultipleSubstitutionRecord_get_targetGlyphID_mF01201C747BA2FAEF8567CFDA974F6473E09D162_AdjustorThunk (void);
extern void MultipleSubstitutionRecord_set_targetGlyphID_mF237E40BBF7D12A45DA5662E11FC3DED640310F2_AdjustorThunk (void);
extern void MultipleSubstitutionRecord_get_substituteGlyphIDs_m6391274416D6936BE038ED3546BDBB5D88AE6451_AdjustorThunk (void);
extern void MultipleSubstitutionRecord_set_substituteGlyphIDs_m4F558CBB6F2EBBE4CBD74E2EA049518E0C457B42_AdjustorThunk (void);
extern void AlternateSubstitutionRecord_get_targetGlyphID_m46389FE0D0A235D35A96A5F3BEECD33D3E269BBA_AdjustorThunk (void);
extern void AlternateSubstitutionRecord_set_targetGlyphID_m3F04DAA4001337ED8501DFA61DA45D11E16C4142_AdjustorThunk (void);
extern void AlternateSubstitutionRecord_get_substituteGlyphIDs_mB88AA4734922B7304398923CDBD7EC6ACDA3AC86_AdjustorThunk (void);
extern void AlternateSubstitutionRecord_set_substituteGlyphIDs_mC6DD2CE2FBE55E3CD9276F8F21EAE718A5B3A98C_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_get_componentGlyphIDs_m3BBDC9421E3A7369B198379F5433FBB13ADCE628_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_set_componentGlyphIDs_mDD8D76279FF545B516D4CE689568AA513A95EF63_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_get_ligatureGlyphID_m5FD629E204026FB8B6279498CDE5CAB1D23827EC_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_set_ligatureGlyphID_mDB4F809745EF0536182FAF90AFB276F389C35EB6_AdjustorThunk (void);
extern void GlyphIDSequence_get_glyphIDs_m9BEB975FB20104387FAC6120BB90D4055E854BB1_AdjustorThunk (void);
extern void GlyphIDSequence_set_glyphIDs_mB877FBB483AAD37119B32B2797E6BACB2EE22256_AdjustorThunk (void);
extern void SequenceLookupRecord_get_glyphSequenceIndex_mDFE2CB0E6F409FC6E76371AE9C901787A043EDBC_AdjustorThunk (void);
extern void SequenceLookupRecord_set_glyphSequenceIndex_m4903487C7478B5BC34166C892CB3DDA69C5D6A45_AdjustorThunk (void);
extern void SequenceLookupRecord_get_lookupListIndex_m59A597A93C9DB6D00BE7F408327AFF551D25D829_AdjustorThunk (void);
extern void SequenceLookupRecord_set_lookupListIndex_mC1F94638EBF47068E6D9BEE948FF08CA6227306C_AdjustorThunk (void);
extern void ContextualSubstitutionRecord_get_inputSequences_mA178071C71E922AB18142760AE712C3D37B11749_AdjustorThunk (void);
extern void ContextualSubstitutionRecord_set_inputSequences_mB9FFBA40115AF953C54C23520A0D6EED2124D792_AdjustorThunk (void);
extern void ContextualSubstitutionRecord_get_sequenceLookupRecords_m888925F050EFB2980B87BCDCCC342E4BECA034FC_AdjustorThunk (void);
extern void ContextualSubstitutionRecord_set_sequenceLookupRecords_m63A429B7D96EA86DBD8128908ACA96ADB7FA494A_AdjustorThunk (void);
extern void ChainingContextualSubstitutionRecord_get_backtrackGlyphSequences_m7FD183544371F7A853BBF197E66592C0C2D9D8A1_AdjustorThunk (void);
extern void ChainingContextualSubstitutionRecord_set_backtrackGlyphSequences_m249B99EB124ACB0A7B27D234A4F10EF3F87ACB4E_AdjustorThunk (void);
extern void ChainingContextualSubstitutionRecord_get_inputGlyphSequences_m0236D68BE22540A633BB2590AE34282BD6EA5F97_AdjustorThunk (void);
extern void ChainingContextualSubstitutionRecord_set_inputGlyphSequences_mEAC5074B5B0A5BDAC19359134C514DC76D33C760_AdjustorThunk (void);
extern void ChainingContextualSubstitutionRecord_get_lookaheadGlyphSequences_m3C8A441E18555617D4D3455978894962D3BA6FE4_AdjustorThunk (void);
extern void ChainingContextualSubstitutionRecord_set_lookaheadGlyphSequences_m061486A5432AB15C4BACBA8AC2851A5449219594_AdjustorThunk (void);
extern void ChainingContextualSubstitutionRecord_get_sequenceLookupRecords_mB0FAEACA23F664767344BD416081AED5BE813C00_AdjustorThunk (void);
extern void ChainingContextualSubstitutionRecord_set_sequenceLookupRecords_m5AD305FAC33438FE23703DC65C6031A27A7D14BF_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[163] = 
{
	{ 0x06000001, FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641_AdjustorThunk },
	{ 0x06000002, FaceInfo_set_faceIndex_m6B57F32E81090DCB77DA466535224CAC5ACF9AFA_AdjustorThunk },
	{ 0x06000003, FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241_AdjustorThunk },
	{ 0x06000004, FaceInfo_set_familyName_m49CB07A51AC9008B4F399A3064EC4FF6EA8E839D_AdjustorThunk },
	{ 0x06000005, FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6_AdjustorThunk },
	{ 0x06000006, FaceInfo_set_styleName_m3BEBF7E576032A668BCC83D88D2F6902F561B5E6_AdjustorThunk },
	{ 0x06000007, FaceInfo_get_pointSize_m7EF7429A4725AB715931A220F6BB498C3D6BF7CB_AdjustorThunk },
	{ 0x06000008, FaceInfo_set_pointSize_m17D0B03C4A762F657A4ABD15327B08D6B55EF492_AdjustorThunk },
	{ 0x06000009, FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD_AdjustorThunk },
	{ 0x0600000A, FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4_AdjustorThunk },
	{ 0x0600000B, FaceInfo_get_unitsPerEM_m92CD79B71FC504DE1C87923716E999D85A35AC0E_AdjustorThunk },
	{ 0x0600000C, FaceInfo_set_unitsPerEM_m3E48D789D666984A4EA8D221BED22833761E9A90_AdjustorThunk },
	{ 0x0600000D, FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D_AdjustorThunk },
	{ 0x0600000E, FaceInfo_set_lineHeight_m5952A394C6055DD86700448D9E33EBBE852E05AE_AdjustorThunk },
	{ 0x0600000F, FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD_AdjustorThunk },
	{ 0x06000010, FaceInfo_set_ascentLine_mDFB32635374875A695D3BB686D2A16C648A20D78_AdjustorThunk },
	{ 0x06000011, FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88_AdjustorThunk },
	{ 0x06000012, FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF_AdjustorThunk },
	{ 0x06000013, FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96_AdjustorThunk },
	{ 0x06000014, FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99_AdjustorThunk },
	{ 0x06000015, FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A_AdjustorThunk },
	{ 0x06000016, FaceInfo_set_baseline_m528F6ADAF4F45A31E9D2BA362FB3C02B03DDD741_AdjustorThunk },
	{ 0x06000017, FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3_AdjustorThunk },
	{ 0x06000018, FaceInfo_set_descentLine_m62423E864258229A85FD3684D8385A44C01AF97C_AdjustorThunk },
	{ 0x06000019, FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A_AdjustorThunk },
	{ 0x0600001A, FaceInfo_set_superscriptOffset_mC81144590FA80858D489AFC46AAA383E11D3D2B5_AdjustorThunk },
	{ 0x0600001B, FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0_AdjustorThunk },
	{ 0x0600001C, FaceInfo_set_superscriptSize_m89F17C1502C07A3846402A5A43369BFB66723515_AdjustorThunk },
	{ 0x0600001D, FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A_AdjustorThunk },
	{ 0x0600001E, FaceInfo_set_subscriptOffset_m796EF61DE0677BFB134F6B8C4B6C1869BCF8782A_AdjustorThunk },
	{ 0x0600001F, FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31_AdjustorThunk },
	{ 0x06000020, FaceInfo_set_subscriptSize_m5759439F9D100591A6FFF1D7377495CEBDCD6B5D_AdjustorThunk },
	{ 0x06000021, FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251_AdjustorThunk },
	{ 0x06000022, FaceInfo_set_underlineOffset_m1C0E755772FE8C47D565E6CC7DC4E953B71794AA_AdjustorThunk },
	{ 0x06000023, FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2_AdjustorThunk },
	{ 0x06000024, FaceInfo_set_underlineThickness_mDD002D08CE97E0C7DA6FA6FFBDD15295D24B303A_AdjustorThunk },
	{ 0x06000025, FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5_AdjustorThunk },
	{ 0x06000026, FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0_AdjustorThunk },
	{ 0x06000027, FaceInfo_get_strikethroughThickness_m079405B5FDA3F9CC4147597444598A173CFD74C3_AdjustorThunk },
	{ 0x06000028, FaceInfo_set_strikethroughThickness_m8CA3C9897FF3D5D0F653539CCBF5E16E01CACF52_AdjustorThunk },
	{ 0x06000029, FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161_AdjustorThunk },
	{ 0x0600002A, FaceInfo_set_tabWidth_m44234ED657FAB54320C48C34D48532F8232F6E1D_AdjustorThunk },
	{ 0x0600002B, FaceInfo__ctor_m5392B651EAD0672BE1FF0EB8E878B6078D9C3201_AdjustorThunk },
	{ 0x0600002C, FaceInfo_Compare_m5C40431846D8FC040C4AC93EEBCE599DF40E4940_AdjustorThunk },
	{ 0x0600002D, GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898_AdjustorThunk },
	{ 0x0600002E, GlyphRect_set_x_m1AF3570B0730D18F11DEBCC5D639EE83549C06E1_AdjustorThunk },
	{ 0x0600002F, GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA_AdjustorThunk },
	{ 0x06000030, GlyphRect_set_y_mE9697A41067D3BCB1CF0B92472A259A94FB95257_AdjustorThunk },
	{ 0x06000031, GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12_AdjustorThunk },
	{ 0x06000032, GlyphRect_set_width_mD99A7B04328F95C75FA2403C7E6DDC3CA40951CD_AdjustorThunk },
	{ 0x06000033, GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A_AdjustorThunk },
	{ 0x06000034, GlyphRect_set_height_m4E34D98DDD7A403571AEC2C3A05F4AE07453085E_AdjustorThunk },
	{ 0x06000036, GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95_AdjustorThunk },
	{ 0x06000037, GlyphRect__ctor_m6540996F96C6A3C7BA757EDC4A02196993EC3400_AdjustorThunk },
	{ 0x06000038, GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F_AdjustorThunk },
	{ 0x06000039, GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B_AdjustorThunk },
	{ 0x0600003A, GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87_AdjustorThunk },
	{ 0x0600003E, GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13_AdjustorThunk },
	{ 0x0600003F, GlyphMetrics_set_width_m236B586A8A7CB4FD2C46E6581ADB2037CD6019A4_AdjustorThunk },
	{ 0x06000040, GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C_AdjustorThunk },
	{ 0x06000041, GlyphMetrics_set_height_m98DA746BE7362BD1EE03D4083213DA33B6306CF1_AdjustorThunk },
	{ 0x06000042, GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F_AdjustorThunk },
	{ 0x06000043, GlyphMetrics_set_horizontalBearingX_m5F05916CA7AC363ED6AEE4AD2809B9925D829F0C_AdjustorThunk },
	{ 0x06000044, GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223_AdjustorThunk },
	{ 0x06000045, GlyphMetrics_set_horizontalBearingY_m3459042D77350137A7CD0EAC0401103F151D5314_AdjustorThunk },
	{ 0x06000046, GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1_AdjustorThunk },
	{ 0x06000047, GlyphMetrics_set_horizontalAdvance_m13C7C6BDFA7BC4A384F8517193FAE21CA614B814_AdjustorThunk },
	{ 0x06000048, GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D_AdjustorThunk },
	{ 0x06000049, GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6_AdjustorThunk },
	{ 0x0600004A, GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970_AdjustorThunk },
	{ 0x0600004B, GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB_AdjustorThunk },
	{ 0x06000114, GlyphMarshallingStruct__ctor_mF74998CA1395853967081C3799D6EC0AEADFAD8D_AdjustorThunk },
	{ 0x06000115, GlyphMarshallingStruct__ctor_mF944582B068F848BECC4D80278F3AEFD33843129_AdjustorThunk },
	{ 0x06000116, GlyphMarshallingStruct__ctor_m7E489C38186D01A566DD1FC72D6B6D2846DBFBCD_AdjustorThunk },
	{ 0x0600011E, GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8_AdjustorThunk },
	{ 0x0600011F, GlyphValueRecord_set_xPlacement_m79F92029922BDE50ED63A6A03EBE478869F1CCFC_AdjustorThunk },
	{ 0x06000120, GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA_AdjustorThunk },
	{ 0x06000121, GlyphValueRecord_set_yPlacement_m04DA300FAB827A708CB291DA3B2EA3128279CA2B_AdjustorThunk },
	{ 0x06000122, GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E_AdjustorThunk },
	{ 0x06000123, GlyphValueRecord_set_xAdvance_mA8475DBF8792908481A5DA5FFE7C7FAD3E596A35_AdjustorThunk },
	{ 0x06000124, GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6_AdjustorThunk },
	{ 0x06000125, GlyphValueRecord_set_yAdvance_m37EDC0646862034F5625BC58428B7E2F3869D0D4_AdjustorThunk },
	{ 0x06000126, GlyphValueRecord__ctor_m01488AC8714CC329C1A23B034C1ABF4B52F0740A_AdjustorThunk },
	{ 0x06000129, GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60_AdjustorThunk },
	{ 0x0600012A, GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6_AdjustorThunk },
	{ 0x0600012B, GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972_AdjustorThunk },
	{ 0x0600012E, GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9_AdjustorThunk },
	{ 0x0600012F, GlyphAdjustmentRecord_set_glyphIndex_m97850B31C30FA0AC2B497A6F864F76E70D189446_AdjustorThunk },
	{ 0x06000130, GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7_AdjustorThunk },
	{ 0x06000131, GlyphAdjustmentRecord_set_glyphValueRecord_m5107E793964C7C945DD62D330589AA7D14777047_AdjustorThunk },
	{ 0x06000132, GlyphAdjustmentRecord__ctor_m54853163D58AD883E19A7C0D795A4CF73FCAE28C_AdjustorThunk },
	{ 0x06000133, GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C_AdjustorThunk },
	{ 0x06000134, GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D_AdjustorThunk },
	{ 0x06000135, GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535_AdjustorThunk },
	{ 0x06000138, GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C_AdjustorThunk },
	{ 0x06000139, GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_mA19E5BFDFB5B5EF6B82BE195BC407390E3754296_AdjustorThunk },
	{ 0x0600013A, GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91_AdjustorThunk },
	{ 0x0600013B, GlyphPairAdjustmentRecord_set_secondAdjustmentRecord_m5C90F3C44ADD213E181BDB955AB214B6F56B2FBF_AdjustorThunk },
	{ 0x0600013C, GlyphPairAdjustmentRecord_get_featureLookupFlags_m08DA76766FDE949068B881DBEA29955C9C43E8A9_AdjustorThunk },
	{ 0x0600013D, GlyphPairAdjustmentRecord_set_featureLookupFlags_m4900CDB13F697D5CD7012383E71910AEAD170359_AdjustorThunk },
	{ 0x0600013E, GlyphPairAdjustmentRecord__ctor_m2B1BCCA950399944824CFB79D74C39A7A4F87A6A_AdjustorThunk },
	{ 0x0600013F, GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5_AdjustorThunk },
	{ 0x06000140, GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD_AdjustorThunk },
	{ 0x06000141, GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689_AdjustorThunk },
	{ 0x06000144, GlyphAnchorPoint_get_xCoordinate_mCD33464763911ECB78DEB1965970A916FA27DD1C_AdjustorThunk },
	{ 0x06000145, GlyphAnchorPoint_set_xCoordinate_m6820F49984BD779C6274A656AE013575C2E121FA_AdjustorThunk },
	{ 0x06000146, GlyphAnchorPoint_get_yCoordinate_m2683C19C6A3D750E4D6C536307313E55589909D6_AdjustorThunk },
	{ 0x06000147, GlyphAnchorPoint_set_yCoordinate_m3DC770A8B68515510EC83140E7CC2164880EDA97_AdjustorThunk },
	{ 0x06000148, MarkPositionAdjustment_get_xPositionAdjustment_m5ACBB4C515357320C12597CAE5E4D409BA298765_AdjustorThunk },
	{ 0x06000149, MarkPositionAdjustment_set_xPositionAdjustment_mBD0304ADF7B91F788755A319868513C1D5C7020C_AdjustorThunk },
	{ 0x0600014A, MarkPositionAdjustment_get_yPositionAdjustment_m1F5F7DBBFEB0B52CCC772F68664D06B11D6A9F2C_AdjustorThunk },
	{ 0x0600014B, MarkPositionAdjustment_set_yPositionAdjustment_m80D07753C658004FD58CD20B4B0D70EFB791836E_AdjustorThunk },
	{ 0x0600014C, MarkPositionAdjustment__ctor_mBD39BFE7BE793324DDF1C67BAFE2C9DEE069286B_AdjustorThunk },
	{ 0x0600014D, MarkToBaseAdjustmentRecord_get_baseGlyphID_mD6A0DD18DBE69E0E68ACA6AABF47B1EA61B633A5_AdjustorThunk },
	{ 0x0600014E, MarkToBaseAdjustmentRecord_set_baseGlyphID_mC8151EF01F0FCFFA0611EB65EC03099B6562ABAC_AdjustorThunk },
	{ 0x0600014F, MarkToBaseAdjustmentRecord_get_baseGlyphAnchorPoint_mCBF57932B7A89C532B0EF750DFD81F8FE389EE08_AdjustorThunk },
	{ 0x06000150, MarkToBaseAdjustmentRecord_set_baseGlyphAnchorPoint_m1C79E0AD92B197313FCD2273FD57CB6CA3B3E379_AdjustorThunk },
	{ 0x06000151, MarkToBaseAdjustmentRecord_get_markGlyphID_mB4BB0291733ECFC2433F5C2837F0B28EB05CAF5C_AdjustorThunk },
	{ 0x06000152, MarkToBaseAdjustmentRecord_set_markGlyphID_mCA5B1438023B89A5FDE7B6F8F1C20F7CEB8A8C09_AdjustorThunk },
	{ 0x06000153, MarkToBaseAdjustmentRecord_get_markPositionAdjustment_m570715D1D0F84361A90564D4A958394453E1F9AB_AdjustorThunk },
	{ 0x06000154, MarkToBaseAdjustmentRecord_set_markPositionAdjustment_m585228E1ADB46FEB33EF9400B3A477FEE6559AFC_AdjustorThunk },
	{ 0x06000155, MarkToMarkAdjustmentRecord_get_baseMarkGlyphID_mA0EAEA751467A8841F9D20D122C4998905A508CC_AdjustorThunk },
	{ 0x06000156, MarkToMarkAdjustmentRecord_set_baseMarkGlyphID_m3F60246B56DB78EF3754421041378A08E9FC018F_AdjustorThunk },
	{ 0x06000157, MarkToMarkAdjustmentRecord_get_baseMarkGlyphAnchorPoint_mB87ADA10491B42650BAD4DB7330771061827ACAB_AdjustorThunk },
	{ 0x06000158, MarkToMarkAdjustmentRecord_set_baseMarkGlyphAnchorPoint_mF99F7E7CBCB4B3C36D87BC6F6B92232EEE63B17B_AdjustorThunk },
	{ 0x06000159, MarkToMarkAdjustmentRecord_get_combiningMarkGlyphID_m84613A2A27F87AA21CAEDD08759031302C9A8FBF_AdjustorThunk },
	{ 0x0600015A, MarkToMarkAdjustmentRecord_set_combiningMarkGlyphID_mABD6E44107C367EE21EB258DB5681707C4B34075_AdjustorThunk },
	{ 0x0600015B, MarkToMarkAdjustmentRecord_get_combiningMarkPositionAdjustment_mC109ECEDB4AD314A25C0EB1F6F6151AE611DE15C_AdjustorThunk },
	{ 0x0600015C, MarkToMarkAdjustmentRecord_set_combiningMarkPositionAdjustment_mFAB0D9C53F43D866DD2DA6F3B85A5E1AE23897EC_AdjustorThunk },
	{ 0x0600015D, SingleSubstitutionRecord_get_targetGlyphID_mFD058D5092C3674A7B71EBA67FD6829AC51B5359_AdjustorThunk },
	{ 0x0600015E, SingleSubstitutionRecord_set_targetGlyphID_m76B6E23641060D8C394379DE91B051D867BD090A_AdjustorThunk },
	{ 0x0600015F, SingleSubstitutionRecord_get_substituteGlyphID_mB14D0FA76335020198ADE6D10ABEBF06D0214B7D_AdjustorThunk },
	{ 0x06000160, SingleSubstitutionRecord_set_substituteGlyphID_m93A8DF73A184C7E38F3199C4258258E81852E052_AdjustorThunk },
	{ 0x06000161, MultipleSubstitutionRecord_get_targetGlyphID_mF01201C747BA2FAEF8567CFDA974F6473E09D162_AdjustorThunk },
	{ 0x06000162, MultipleSubstitutionRecord_set_targetGlyphID_mF237E40BBF7D12A45DA5662E11FC3DED640310F2_AdjustorThunk },
	{ 0x06000163, MultipleSubstitutionRecord_get_substituteGlyphIDs_m6391274416D6936BE038ED3546BDBB5D88AE6451_AdjustorThunk },
	{ 0x06000164, MultipleSubstitutionRecord_set_substituteGlyphIDs_m4F558CBB6F2EBBE4CBD74E2EA049518E0C457B42_AdjustorThunk },
	{ 0x06000165, AlternateSubstitutionRecord_get_targetGlyphID_m46389FE0D0A235D35A96A5F3BEECD33D3E269BBA_AdjustorThunk },
	{ 0x06000166, AlternateSubstitutionRecord_set_targetGlyphID_m3F04DAA4001337ED8501DFA61DA45D11E16C4142_AdjustorThunk },
	{ 0x06000167, AlternateSubstitutionRecord_get_substituteGlyphIDs_mB88AA4734922B7304398923CDBD7EC6ACDA3AC86_AdjustorThunk },
	{ 0x06000168, AlternateSubstitutionRecord_set_substituteGlyphIDs_mC6DD2CE2FBE55E3CD9276F8F21EAE718A5B3A98C_AdjustorThunk },
	{ 0x06000169, LigatureSubstitutionRecord_get_componentGlyphIDs_m3BBDC9421E3A7369B198379F5433FBB13ADCE628_AdjustorThunk },
	{ 0x0600016A, LigatureSubstitutionRecord_set_componentGlyphIDs_mDD8D76279FF545B516D4CE689568AA513A95EF63_AdjustorThunk },
	{ 0x0600016B, LigatureSubstitutionRecord_get_ligatureGlyphID_m5FD629E204026FB8B6279498CDE5CAB1D23827EC_AdjustorThunk },
	{ 0x0600016C, LigatureSubstitutionRecord_set_ligatureGlyphID_mDB4F809745EF0536182FAF90AFB276F389C35EB6_AdjustorThunk },
	{ 0x0600016D, GlyphIDSequence_get_glyphIDs_m9BEB975FB20104387FAC6120BB90D4055E854BB1_AdjustorThunk },
	{ 0x0600016E, GlyphIDSequence_set_glyphIDs_mB877FBB483AAD37119B32B2797E6BACB2EE22256_AdjustorThunk },
	{ 0x0600016F, SequenceLookupRecord_get_glyphSequenceIndex_mDFE2CB0E6F409FC6E76371AE9C901787A043EDBC_AdjustorThunk },
	{ 0x06000170, SequenceLookupRecord_set_glyphSequenceIndex_m4903487C7478B5BC34166C892CB3DDA69C5D6A45_AdjustorThunk },
	{ 0x06000171, SequenceLookupRecord_get_lookupListIndex_m59A597A93C9DB6D00BE7F408327AFF551D25D829_AdjustorThunk },
	{ 0x06000172, SequenceLookupRecord_set_lookupListIndex_mC1F94638EBF47068E6D9BEE948FF08CA6227306C_AdjustorThunk },
	{ 0x06000173, ContextualSubstitutionRecord_get_inputSequences_mA178071C71E922AB18142760AE712C3D37B11749_AdjustorThunk },
	{ 0x06000174, ContextualSubstitutionRecord_set_inputSequences_mB9FFBA40115AF953C54C23520A0D6EED2124D792_AdjustorThunk },
	{ 0x06000175, ContextualSubstitutionRecord_get_sequenceLookupRecords_m888925F050EFB2980B87BCDCCC342E4BECA034FC_AdjustorThunk },
	{ 0x06000176, ContextualSubstitutionRecord_set_sequenceLookupRecords_m63A429B7D96EA86DBD8128908ACA96ADB7FA494A_AdjustorThunk },
	{ 0x06000177, ChainingContextualSubstitutionRecord_get_backtrackGlyphSequences_m7FD183544371F7A853BBF197E66592C0C2D9D8A1_AdjustorThunk },
	{ 0x06000178, ChainingContextualSubstitutionRecord_set_backtrackGlyphSequences_m249B99EB124ACB0A7B27D234A4F10EF3F87ACB4E_AdjustorThunk },
	{ 0x06000179, ChainingContextualSubstitutionRecord_get_inputGlyphSequences_m0236D68BE22540A633BB2590AE34282BD6EA5F97_AdjustorThunk },
	{ 0x0600017A, ChainingContextualSubstitutionRecord_set_inputGlyphSequences_mEAC5074B5B0A5BDAC19359134C514DC76D33C760_AdjustorThunk },
	{ 0x0600017B, ChainingContextualSubstitutionRecord_get_lookaheadGlyphSequences_m3C8A441E18555617D4D3455978894962D3BA6FE4_AdjustorThunk },
	{ 0x0600017C, ChainingContextualSubstitutionRecord_set_lookaheadGlyphSequences_m061486A5432AB15C4BACBA8AC2851A5449219594_AdjustorThunk },
	{ 0x0600017D, ChainingContextualSubstitutionRecord_get_sequenceLookupRecords_mB0FAEACA23F664767344BD416081AED5BE813C00_AdjustorThunk },
	{ 0x0600017E, ChainingContextualSubstitutionRecord_set_sequenceLookupRecords_m5AD305FAC33438FE23703DC65C6031A27A7D14BF_AdjustorThunk },
};
static const int32_t s_InvokerIndices[382] = 
{
	4216,
	3852,
	4250,
	3881,
	4250,
	3881,
	4216,
	3852,
	4298,
	3928,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	8,
	3143,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	9013,
	1346,
	3904,
	4216,
	3185,
	3152,
	7209,
	7209,
	9089,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	1010,
	4216,
	3185,
	3150,
	7207,
	7207,
	4350,
	3973,
	4202,
	3841,
	4203,
	3843,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4364,
	3881,
	3840,
	2156,
	1014,
	3185,
	4364,
	9018,
	9018,
	9018,
	9018,
	9089,
	9089,
	8993,
	9064,
	8399,
	8399,
	7488,
	7488,
	6529,
	6529,
	8399,
	8399,
	7488,
	7488,
	6529,
	6529,
	8399,
	8399,
	7488,
	7488,
	6529,
	6529,
	7489,
	7489,
	6537,
	6537,
	9018,
	9018,
	9018,
	9018,
	9031,
	9031,
	9031,
	6365,
	6365,
	8395,
	8395,
	9010,
	8380,
	9018,
	9031,
	9031,
	7820,
	8791,
	7352,
	7508,
	7508,
	6403,
	6403,
	6403,
	6403,
	4607,
	4492,
	4530,
	4435,
	5815,
	5789,
	5815,
	5267,
	4944,
	4750,
	6529,
	5814,
	8887,
	9089,
	8868,
	4615,
	4495,
	4611,
	4445,
	4608,
	4466,
	8481,
	9031,
	9031,
	9031,
	9031,
	9031,
	7604,
	7601,
	7601,
	6526,
	8399,
	9031,
	7604,
	7601,
	7601,
	6526,
	8399,
	9031,
	7604,
	7601,
	7601,
	6526,
	8399,
	9031,
	8521,
	8505,
	7604,
	7601,
	8505,
	7601,
	7485,
	6526,
	8399,
	9031,
	7604,
	7601,
	7601,
	6526,
	8399,
	9031,
	7604,
	7601,
	7601,
	6526,
	8399,
	8505,
	7615,
	7485,
	7662,
	7507,
	7631,
	6535,
	7441,
	7604,
	7601,
	7601,
	6526,
	8399,
	8521,
	7441,
	9031,
	8505,
	7604,
	7601,
	8505,
	7601,
	7485,
	6526,
	8399,
	9031,
	8521,
	7558,
	8505,
	7601,
	8505,
	7601,
	7485,
	6526,
	8399,
	9031,
	8521,
	7559,
	8505,
	7601,
	8505,
	7601,
	7485,
	6526,
	8399,
	8023,
	0,
	0,
	8887,
	6160,
	9089,
	5784,
	7925,
	7029,
	7029,
	7029,
	7029,
	7306,
	6499,
	3881,
	1014,
	664,
	0,
	2749,
	2000,
	2739,
	1989,
	0,
	4364,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	1517,
	7442,
	7443,
	4216,
	3185,
	3153,
	7210,
	7210,
	4350,
	3973,
	4204,
	3844,
	2872,
	4216,
	3185,
	3149,
	7206,
	7206,
	4200,
	3838,
	4200,
	3838,
	4216,
	3852,
	2719,
	4216,
	3185,
	3151,
	7208,
	7208,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	2850,
	4350,
	3973,
	4201,
	3839,
	4350,
	3973,
	4238,
	3873,
	4350,
	3973,
	4201,
	3839,
	4350,
	3973,
	4238,
	3873,
	4350,
	3973,
	4350,
	3973,
	4350,
	3973,
	4250,
	3881,
	4350,
	3973,
	4250,
	3881,
	4250,
	3881,
	4350,
	3973,
	4250,
	3881,
	4350,
	3973,
	4350,
	3973,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x06000107, { 0, 8 } },
	{ 0x06000108, { 8, 3 } },
};
extern const uint32_t g_rgctx_List_1U26_t7426D5EB55FE6CBFC77194E5A84AA31237C491DC;
extern const uint32_t g_rgctx_List_1_tAF861B916DD21383F06D73A5F0F2132D57F5A2E5;
extern const uint32_t g_rgctx_List_1_get_Count_mAFBAA494C44EA10C5165ABCAF3C7D9948C9776BB;
extern const uint32_t g_rgctx_TU5BU5DU26_tC514DD32B78B2B0D4C149D2FBD65B54EA4723659;
extern const uint32_t g_rgctx_TU5BU5D_t04DF92FC5A6ED2E137925B2D4AAF396747A65B9D;
extern const uint32_t g_rgctx_Array_Resize_TisT_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017_mC449C792316CB2FED9221E20AB9071D2651433EE;
extern const uint32_t g_rgctx_List_1_get_Item_mFBCECC084098088A5E18C51A686368A5839C7EF3;
extern const uint32_t g_rgctx_T_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017;
extern const uint32_t g_rgctx_TU5BU5DU26_tDB8D3F81015DC6570941CF27000F6AA4B87251A2;
extern const uint32_t g_rgctx_TU5BU5D_t8A34B43F2CD5F32F323C003A2FDB6F9C9A6C977A;
extern const uint32_t g_rgctx_Array_Resize_TisT_t201BA83724B99DBE4155ED3CCD796A8773447525_mE500A00B5DE2E8E6605EF95ECC80FCBF77FD2DDC;
static const Il2CppRGCTXDefinition s_rgctxValues[11] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1U26_t7426D5EB55FE6CBFC77194E5A84AA31237C491DC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tAF861B916DD21383F06D73A5F0F2132D57F5A2E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mAFBAA494C44EA10C5165ABCAF3C7D9948C9776BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tC514DD32B78B2B0D4C149D2FBD65B54EA4723659 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t04DF92FC5A6ED2E137925B2D4AAF396747A65B9D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017_mC449C792316CB2FED9221E20AB9071D2651433EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mFBCECC084098088A5E18C51A686368A5839C7EF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tDB8D3F81015DC6570941CF27000F6AA4B87251A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t8A34B43F2CD5F32F323C003A2FDB6F9C9A6C977A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t201BA83724B99DBE4155ED3CCD796A8773447525_mE500A00B5DE2E8E6605EF95ECC80FCBF77FD2DDC },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_TextCoreFontEngineModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextCoreFontEngineModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextCoreFontEngineModule_CodeGenModule = 
{
	"UnityEngine.TextCoreFontEngineModule.dll",
	382,
	s_methodPointers,
	163,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	11,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_TextCoreFontEngineModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
