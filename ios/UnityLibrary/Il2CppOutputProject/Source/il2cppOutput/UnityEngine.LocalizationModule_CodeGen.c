﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void LocalizationAsset__ctor_m46CD29B0A6C783B9E1FC59DE2E0F0D1AEAD99024 (void);
extern void LocalizationAsset_Internal_CreateInstance_mC84135ED0D09DB20376DD8FD4DDF022C8C29D25B (void);
extern void LocalizationAsset_SetLocalizedString_m84173DFA5EA21BA9BE6F7624809B1CA9E372B851 (void);
extern void LocalizationAsset_GetLocalizedString_m2715A79A67104837D21825FDA24C144C0E6ADE75 (void);
extern void LocalizationAsset_get_localeIsoCode_m9313AB325D233D2797370673C25D2FE14E46F25A (void);
extern void LocalizationAsset_set_localeIsoCode_m32F7A49BB3A7CBA6AA27B1332A5559C99BA33A09 (void);
extern void LocalizationAsset_get_isEditorAsset_m3988BFB87CC7B61F1DA24D48B4F5E78C23FB4149 (void);
extern void LocalizationAsset_set_isEditorAsset_m5FCCD7D337594A82756ED5B59436EE14AF58824E (void);
static Il2CppMethodPointer s_methodPointers[8] = 
{
	LocalizationAsset__ctor_m46CD29B0A6C783B9E1FC59DE2E0F0D1AEAD99024,
	LocalizationAsset_Internal_CreateInstance_mC84135ED0D09DB20376DD8FD4DDF022C8C29D25B,
	LocalizationAsset_SetLocalizedString_m84173DFA5EA21BA9BE6F7624809B1CA9E372B851,
	LocalizationAsset_GetLocalizedString_m2715A79A67104837D21825FDA24C144C0E6ADE75,
	LocalizationAsset_get_localeIsoCode_m9313AB325D233D2797370673C25D2FE14E46F25A,
	LocalizationAsset_set_localeIsoCode_m32F7A49BB3A7CBA6AA27B1332A5559C99BA33A09,
	LocalizationAsset_get_isEditorAsset_m3988BFB87CC7B61F1DA24D48B4F5E78C23FB4149,
	LocalizationAsset_set_isEditorAsset_m5FCCD7D337594A82756ED5B59436EE14AF58824E,
};
static const int32_t s_InvokerIndices[8] = 
{
	4364,
	8887,
	2802,
	3518,
	4250,
	3881,
	4168,
	3807,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_LocalizationModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_LocalizationModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_LocalizationModule_CodeGenModule = 
{
	"UnityEngine.LocalizationModule.dll",
	8,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_LocalizationModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
