﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A;
struct List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B;
struct CapsuleColliderU5BU5D_t812EDDF9927ACFDDA87D5D99C525FCDDDCB4D1BC;
struct ClothSkinningCoefficientU5BU5D_t50CC51EBDF127E5A0F67CCEEFE4CF620D902D1AF;
struct ClothSphereColliderPairU5BU5D_t533E73D008F2A94B23F9B021AD694D61A4A542AA;
struct UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
struct CapsuleCollider_t3A1671C74F0836ABEF5D01A7470B5B2BE290A808;
struct Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948;
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
struct SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_ClothModule[];
IL2CPP_EXTERN_C RuntimeField* ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F____U3CfirstU3Ek__BackingField_FieldInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* ClothSphereColliderPair__ctor_m2CFFF98A73A6A63C177F0CE3C8E15B51ACEA955F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ClothSphereColliderPair__ctor_m403496CF4C36C6BFA621F66E0CECC96100A80673_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_Raycast_m5BB6CD5E82EB2F4F2B17A38FA2641DB2CBFB0525_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_SetEnabledFading_mFC216321A4512E3BD8A3EA2557F723E92FBDE12B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth__ctor_mBC218989C0A6B3EC5CA5490BABC664E7F2B08320_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_get_externalAcceleration_m252384E27A2557D66B5F83D284B2FC1A89641FF4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_get_randomAcceleration_m45512198CA7F3781B436EF819355FD259D7B78A5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_get_selfCollision_m3D2C90B0B129F802488CF3DABB7884D797F2A5FF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_get_solverFrequency_mC4806844753FA0FAE3D4C33DAF1425E4B868995F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_get_useContinuousCollision_m3338C62BE424E9E4A2206A349F274B10D3A2A318_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_set_externalAcceleration_mAAF12B724469EC6D711AB51E1229FCF66AB3599A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_set_randomAcceleration_mF837DC2C70F08496451B2BF3C71848E9FE0E613E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_set_solverFrequency_m3EF836CDE77D1BCBC010623B6CE891896B50C1DC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Cloth_set_useContinuousCollision_m6B72DB7BD80AF0F15288E50BC4CF528F9A2A87D7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948_0_0_0_var;

struct CapsuleColliderU5BU5D_t812EDDF9927ACFDDA87D5D99C525FCDDDCB4D1BC;
struct ClothSkinningCoefficientU5BU5D_t50CC51EBDF127E5A0F67CCEEFE4CF620D902D1AF;
struct ClothSphereColliderPairU5BU5D_t533E73D008F2A94B23F9B021AD694D61A4A542AA;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tBDB21C82E5D641816D02C86F2FF72A69E88865B5 
{
};
struct List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A  : public RuntimeObject
{
	UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B  : public RuntimeObject
{
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct ClothSkinningCoefficient_tDC8B19FE78A128F6A12EF7DD8EFBB059835F096A 
{
	float ___maxDistance;
	float ___collisionSphereDistance;
};
struct ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F 
{
	SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___U3CfirstU3Ek__BackingField;
	SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___U3CsecondU3Ek__BackingField;
};
struct ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshaled_pinvoke
{
	SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___U3CfirstU3Ek__BackingField;
	SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___U3CsecondU3Ek__BackingField;
};
struct ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshaled_com
{
	SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___U3CfirstU3Ek__BackingField;
	SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___U3CsecondU3Ek__BackingField;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Origin;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Direction;
};
struct RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal;
	uint32_t ___m_FaceID;
	float ___m_Distance;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_UV;
	int32_t ___m_Collider;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
	float ___U3CuseContinuousCollisionU3Ek__BackingField;
	bool ___U3CselfCollisionU3Ek__BackingField;
};
struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct CapsuleCollider_t3A1671C74F0836ABEF5D01A7470B5B2BE290A808  : public Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76
{
};
struct SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275  : public Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76
{
};
struct List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A_StaticFields
{
	UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA* ___s_emptyArray;
};
struct List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B_StaticFields
{
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___s_emptyArray;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C  : public RuntimeArray
{
	ALIGN_FIELD (8) Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 m_Items[1];

	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 value)
	{
		m_Items[index] = value;
	}
};
struct ClothSkinningCoefficientU5BU5D_t50CC51EBDF127E5A0F67CCEEFE4CF620D902D1AF  : public RuntimeArray
{
	ALIGN_FIELD (8) ClothSkinningCoefficient_tDC8B19FE78A128F6A12EF7DD8EFBB059835F096A m_Items[1];

	inline ClothSkinningCoefficient_tDC8B19FE78A128F6A12EF7DD8EFBB059835F096A GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline ClothSkinningCoefficient_tDC8B19FE78A128F6A12EF7DD8EFBB059835F096A* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, ClothSkinningCoefficient_tDC8B19FE78A128F6A12EF7DD8EFBB059835F096A value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline ClothSkinningCoefficient_tDC8B19FE78A128F6A12EF7DD8EFBB059835F096A GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline ClothSkinningCoefficient_tDC8B19FE78A128F6A12EF7DD8EFBB059835F096A* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, ClothSkinningCoefficient_tDC8B19FE78A128F6A12EF7DD8EFBB059835F096A value)
	{
		m_Items[index] = value;
	}
};
struct CapsuleColliderU5BU5D_t812EDDF9927ACFDDA87D5D99C525FCDDDCB4D1BC  : public RuntimeArray
{
	ALIGN_FIELD (8) CapsuleCollider_t3A1671C74F0836ABEF5D01A7470B5B2BE290A808* m_Items[1];

	inline CapsuleCollider_t3A1671C74F0836ABEF5D01A7470B5B2BE290A808* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline CapsuleCollider_t3A1671C74F0836ABEF5D01A7470B5B2BE290A808** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, CapsuleCollider_t3A1671C74F0836ABEF5D01A7470B5B2BE290A808* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline CapsuleCollider_t3A1671C74F0836ABEF5D01A7470B5B2BE290A808* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline CapsuleCollider_t3A1671C74F0836ABEF5D01A7470B5B2BE290A808** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, CapsuleCollider_t3A1671C74F0836ABEF5D01A7470B5B2BE290A808* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct ClothSphereColliderPairU5BU5D_t533E73D008F2A94B23F9B021AD694D61A4A542AA  : public RuntimeArray
{
	ALIGN_FIELD (8) ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F m_Items[1];

	inline ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CfirstU3Ek__BackingField), (void*)NULL);
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CsecondU3Ek__BackingField), (void*)NULL);
		#endif
	}
	inline ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CfirstU3Ek__BackingField), (void*)NULL);
		#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___U3CsecondU3Ek__BackingField), (void*)NULL);
		#endif
	}
};



IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D_inline (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_inline (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4_inline (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_inline (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ClothSphereColliderPair__ctor_m403496CF4C36C6BFA621F66E0CECC96100A80673 (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_a, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ClothSphereColliderPair__ctor_m2CFFF98A73A6A63C177F0CE3C8E15B51ACEA955F (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_a, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_get_externalAcceleration_Injected_m447914E289BF0CDDD0F7003A9BE2C0D1F062AF86 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_externalAcceleration_Injected_m7D6915CC37E94AA29764A5B02BC19D27D41998AD (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_get_randomAcceleration_Injected_m97B63C18E1B3FB737D9330FAE693F3EF3A03A052 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_randomAcceleration_Injected_m232B588EF417B76FAFA2B28669EC76F5383AD30A (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_clothSolverFrequency_mDBB7222AB0355E19195C513B6DDEED35F88A7B4C (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_clothSolverFrequency_m75608DE320A074915C3B3244E6A4C38CE48CD5E9 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_SetEnabledFading_mD98337A5404F8314CB32D91644F6995579D9855F (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, bool ___0_enabled, float ___1_interpolationTime, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_Raycast_Injected_mD254C34D2EFA5B8F3278CF864D6C62B6564DA46C (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00* ___0_ray, float ___1_maxDistance, bool* ___2_hasHit, RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* ___3_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Component__ctor_m4319162A6E6B02301078C1233F6E7F4A3E735486 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshal_pinvoke(const ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F& unmarshaled, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F____U3CfirstU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CfirstU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F____U3CfirstU3Ek__BackingField_FieldInfo_var, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CfirstU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshal_pinvoke_back(const ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshaled_pinvoke& marshaled, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F____U3CfirstU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CfirstU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F____U3CfirstU3Ek__BackingField_FieldInfo_var, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CfirstU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshal_pinvoke_cleanup(ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshal_com(const ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F& unmarshaled, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F____U3CfirstU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CfirstU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F____U3CfirstU3Ek__BackingField_FieldInfo_var, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CfirstU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshal_com_back(const ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshaled_com& marshaled, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F____U3CfirstU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CfirstU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F____U3CfirstU3Ek__BackingField_FieldInfo_var, ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CfirstU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshal_com_cleanup(ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 2));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = __this->___U3CfirstU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F*>(__this + _offset);
	SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* _returnValue;
	_returnValue = ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 3));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 4));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 5));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = ___0_value;
		__this->___U3CfirstU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CfirstU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_AdjustorThunk (RuntimeObject* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_value, const RuntimeMethod* method)
{
	ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F*>(__this + _offset);
	ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4 (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 6));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 7));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 8));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = __this->___U3CsecondU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F*>(__this + _offset);
	SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* _returnValue;
	_returnValue = ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3 (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 9));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 10));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 11));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = ___0_value;
		__this->___U3CsecondU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsecondU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_AdjustorThunk (RuntimeObject* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_value, const RuntimeMethod* method)
{
	ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F*>(__this + _offset);
	ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ClothSphereColliderPair__ctor_m403496CF4C36C6BFA621F66E0CECC96100A80673 (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_a, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair__ctor_m403496CF4C36C6BFA621F66E0CECC96100A80673_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_a));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair__ctor_m403496CF4C36C6BFA621F66E0CECC96100A80673_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 12));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 13));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 14));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 15));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = ___0_a;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 16));
		ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_inline(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 16));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 17));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 18));
		ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_inline(__this, (SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275*)NULL, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 18));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 19));
		return;
	}
}
IL2CPP_EXTERN_C  void ClothSphereColliderPair__ctor_m403496CF4C36C6BFA621F66E0CECC96100A80673_AdjustorThunk (RuntimeObject* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_a, const RuntimeMethod* method)
{
	ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F*>(__this + _offset);
	ClothSphereColliderPair__ctor_m403496CF4C36C6BFA621F66E0CECC96100A80673(_thisAdjusted, ___0_a, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ClothSphereColliderPair__ctor_m2CFFF98A73A6A63C177F0CE3C8E15B51ACEA955F (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_a, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair__ctor_m2CFFF98A73A6A63C177F0CE3C8E15B51ACEA955F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_a), (&___1_b));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair__ctor_m2CFFF98A73A6A63C177F0CE3C8E15B51ACEA955F_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 20));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 21));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 22));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 23));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = ___0_a;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 24));
		ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_inline(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 24));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 25));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_1 = ___1_b;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 26));
		ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_inline(__this, L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 26));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 27));
		return;
	}
}
IL2CPP_EXTERN_C  void ClothSphereColliderPair__ctor_m2CFFF98A73A6A63C177F0CE3C8E15B51ACEA955F_AdjustorThunk (RuntimeObject* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_a, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___1_b, const RuntimeMethod* method)
{
	ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F*>(__this + _offset);
	ClothSphereColliderPair__ctor_m2CFFF98A73A6A63C177F0CE3C8E15B51ACEA955F(_thisAdjusted, ___0_a, ___1_b, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* Cloth_get_vertices_mCED0A9D74601DE52BACAAFEEF847131810A07EA2 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* (*Cloth_get_vertices_mCED0A9D74601DE52BACAAFEEF847131810A07EA2_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_vertices_mCED0A9D74601DE52BACAAFEEF847131810A07EA2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_vertices_mCED0A9D74601DE52BACAAFEEF847131810A07EA2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_vertices()");
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* Cloth_get_normals_mDE5E8F213E05AC73C2861367BFEFF2726C857E5D (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* (*Cloth_get_normals_mDE5E8F213E05AC73C2861367BFEFF2726C857E5D_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_normals_mDE5E8F213E05AC73C2861367BFEFF2726C857E5D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_normals_mDE5E8F213E05AC73C2861367BFEFF2726C857E5D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_normals()");
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ClothSkinningCoefficientU5BU5D_t50CC51EBDF127E5A0F67CCEEFE4CF620D902D1AF* Cloth_get_coefficients_mFD7EB4A4A5B997FDE2787AEF924C4AD78510AB6C (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef ClothSkinningCoefficientU5BU5D_t50CC51EBDF127E5A0F67CCEEFE4CF620D902D1AF* (*Cloth_get_coefficients_mFD7EB4A4A5B997FDE2787AEF924C4AD78510AB6C_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_coefficients_mFD7EB4A4A5B997FDE2787AEF924C4AD78510AB6C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_coefficients_mFD7EB4A4A5B997FDE2787AEF924C4AD78510AB6C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_coefficients()");
	ClothSkinningCoefficientU5BU5D_t50CC51EBDF127E5A0F67CCEEFE4CF620D902D1AF* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_coefficients_m3F62405E90C802A2B9761A4E131CD5C8412E029A (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, ClothSkinningCoefficientU5BU5D_t50CC51EBDF127E5A0F67CCEEFE4CF620D902D1AF* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_coefficients_m3F62405E90C802A2B9761A4E131CD5C8412E029A_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, ClothSkinningCoefficientU5BU5D_t50CC51EBDF127E5A0F67CCEEFE4CF620D902D1AF*);
	static Cloth_set_coefficients_m3F62405E90C802A2B9761A4E131CD5C8412E029A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_coefficients_m3F62405E90C802A2B9761A4E131CD5C8412E029A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_coefficients(UnityEngine.ClothSkinningCoefficient[])");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CapsuleColliderU5BU5D_t812EDDF9927ACFDDA87D5D99C525FCDDDCB4D1BC* Cloth_get_capsuleColliders_m34E82DDD57B77A50F7BCB10DD5AE6148D2D389D1 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef CapsuleColliderU5BU5D_t812EDDF9927ACFDDA87D5D99C525FCDDDCB4D1BC* (*Cloth_get_capsuleColliders_m34E82DDD57B77A50F7BCB10DD5AE6148D2D389D1_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_capsuleColliders_m34E82DDD57B77A50F7BCB10DD5AE6148D2D389D1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_capsuleColliders_m34E82DDD57B77A50F7BCB10DD5AE6148D2D389D1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_capsuleColliders()");
	CapsuleColliderU5BU5D_t812EDDF9927ACFDDA87D5D99C525FCDDDCB4D1BC* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_capsuleColliders_m3235BE8A0E6B9108D6A8A3F7D11991571562D83D (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, CapsuleColliderU5BU5D_t812EDDF9927ACFDDA87D5D99C525FCDDDCB4D1BC* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_capsuleColliders_m3235BE8A0E6B9108D6A8A3F7D11991571562D83D_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, CapsuleColliderU5BU5D_t812EDDF9927ACFDDA87D5D99C525FCDDDCB4D1BC*);
	static Cloth_set_capsuleColliders_m3235BE8A0E6B9108D6A8A3F7D11991571562D83D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_capsuleColliders_m3235BE8A0E6B9108D6A8A3F7D11991571562D83D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_capsuleColliders(UnityEngine.CapsuleCollider[])");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ClothSphereColliderPairU5BU5D_t533E73D008F2A94B23F9B021AD694D61A4A542AA* Cloth_get_sphereColliders_mDDE201272A0BC03A8A776C7D15D5F748F8DB373F (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef ClothSphereColliderPairU5BU5D_t533E73D008F2A94B23F9B021AD694D61A4A542AA* (*Cloth_get_sphereColliders_mDDE201272A0BC03A8A776C7D15D5F748F8DB373F_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_sphereColliders_mDDE201272A0BC03A8A776C7D15D5F748F8DB373F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_sphereColliders_mDDE201272A0BC03A8A776C7D15D5F748F8DB373F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_sphereColliders()");
	ClothSphereColliderPairU5BU5D_t533E73D008F2A94B23F9B021AD694D61A4A542AA* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_sphereColliders_m1B3D44590D44B6C0666614AFB1C3EAB694BCA468 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, ClothSphereColliderPairU5BU5D_t533E73D008F2A94B23F9B021AD694D61A4A542AA* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_sphereColliders_m1B3D44590D44B6C0666614AFB1C3EAB694BCA468_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, ClothSphereColliderPairU5BU5D_t533E73D008F2A94B23F9B021AD694D61A4A542AA*);
	static Cloth_set_sphereColliders_m1B3D44590D44B6C0666614AFB1C3EAB694BCA468_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_sphereColliders_m1B3D44590D44B6C0666614AFB1C3EAB694BCA468_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_sphereColliders(UnityEngine.ClothSphereColliderPair[])");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_sleepThreshold_m1B304484FAAD6101AADAF9FB3BDCBA058A59D8C0 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_sleepThreshold_m1B304484FAAD6101AADAF9FB3BDCBA058A59D8C0_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_sleepThreshold_m1B304484FAAD6101AADAF9FB3BDCBA058A59D8C0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_sleepThreshold_m1B304484FAAD6101AADAF9FB3BDCBA058A59D8C0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_sleepThreshold()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_sleepThreshold_m4151FE84347318B1CCF6E05D5D2970091DBB7034 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_sleepThreshold_m4151FE84347318B1CCF6E05D5D2970091DBB7034_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_sleepThreshold_m4151FE84347318B1CCF6E05D5D2970091DBB7034_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_sleepThreshold_m4151FE84347318B1CCF6E05D5D2970091DBB7034_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_sleepThreshold(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_bendingStiffness_m6C6BF65C71036864EA55FC287505797C3D6F6ACB (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_bendingStiffness_m6C6BF65C71036864EA55FC287505797C3D6F6ACB_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_bendingStiffness_m6C6BF65C71036864EA55FC287505797C3D6F6ACB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_bendingStiffness_m6C6BF65C71036864EA55FC287505797C3D6F6ACB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_bendingStiffness()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_bendingStiffness_m90110047F4CCFA556073248AECD2EE1F79A8034D (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_bendingStiffness_m90110047F4CCFA556073248AECD2EE1F79A8034D_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_bendingStiffness_m90110047F4CCFA556073248AECD2EE1F79A8034D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_bendingStiffness_m90110047F4CCFA556073248AECD2EE1F79A8034D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_bendingStiffness(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_stretchingStiffness_m8D1FCD7A1F2D4EEDACE7DDAACED977EA5BA4139C (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_stretchingStiffness_m8D1FCD7A1F2D4EEDACE7DDAACED977EA5BA4139C_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_stretchingStiffness_m8D1FCD7A1F2D4EEDACE7DDAACED977EA5BA4139C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_stretchingStiffness_m8D1FCD7A1F2D4EEDACE7DDAACED977EA5BA4139C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_stretchingStiffness()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_stretchingStiffness_m4B0A4FDA719EC784178ED9C25EC6C21BCEE73CDE (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_stretchingStiffness_m4B0A4FDA719EC784178ED9C25EC6C21BCEE73CDE_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_stretchingStiffness_m4B0A4FDA719EC784178ED9C25EC6C21BCEE73CDE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_stretchingStiffness_m4B0A4FDA719EC784178ED9C25EC6C21BCEE73CDE_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_stretchingStiffness(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_damping_m7554F6DC3355BB4FD3677E4CA8FFBAC2FF8E285B (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_damping_m7554F6DC3355BB4FD3677E4CA8FFBAC2FF8E285B_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_damping_m7554F6DC3355BB4FD3677E4CA8FFBAC2FF8E285B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_damping_m7554F6DC3355BB4FD3677E4CA8FFBAC2FF8E285B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_damping()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_damping_m4D1E540B2E58E7E96AD1FD3A30E39AC79062BFA5 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_damping_m4D1E540B2E58E7E96AD1FD3A30E39AC79062BFA5_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_damping_m4D1E540B2E58E7E96AD1FD3A30E39AC79062BFA5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_damping_m4D1E540B2E58E7E96AD1FD3A30E39AC79062BFA5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_damping(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Cloth_get_externalAcceleration_m252384E27A2557D66B5F83D284B2FC1A89641FF4 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_get_externalAcceleration_m252384E27A2557D66B5F83D284B2FC1A89641FF4_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_get_externalAcceleration_m252384E27A2557D66B5F83D284B2FC1A89641FF4_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Cloth_get_externalAcceleration_Injected_m447914E289BF0CDDD0F7003A9BE2C0D1F062AF86(__this, (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_externalAcceleration_mAAF12B724469EC6D711AB51E1229FCF66AB3599A (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_set_externalAcceleration_mAAF12B724469EC6D711AB51E1229FCF66AB3599A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_set_externalAcceleration_mAAF12B724469EC6D711AB51E1229FCF66AB3599A_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Cloth_set_externalAcceleration_Injected_m7D6915CC37E94AA29764A5B02BC19D27D41998AD(__this, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Cloth_get_randomAcceleration_m45512198CA7F3781B436EF819355FD259D7B78A5 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_get_randomAcceleration_m45512198CA7F3781B436EF819355FD259D7B78A5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_get_randomAcceleration_m45512198CA7F3781B436EF819355FD259D7B78A5_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Cloth_get_randomAcceleration_Injected_m97B63C18E1B3FB737D9330FAE693F3EF3A03A052(__this, (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_randomAcceleration_mF837DC2C70F08496451B2BF3C71848E9FE0E613E (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_set_randomAcceleration_mF837DC2C70F08496451B2BF3C71848E9FE0E613E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_set_randomAcceleration_mF837DC2C70F08496451B2BF3C71848E9FE0E613E_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Cloth_set_randomAcceleration_Injected_m232B588EF417B76FAFA2B28669EC76F5383AD30A(__this, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Cloth_get_useGravity_mB719AC46F791BB975606C95F61921C0BBB682F40 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef bool (*Cloth_get_useGravity_mB719AC46F791BB975606C95F61921C0BBB682F40_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_useGravity_mB719AC46F791BB975606C95F61921C0BBB682F40_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_useGravity_mB719AC46F791BB975606C95F61921C0BBB682F40_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_useGravity()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_useGravity_m2B41A8BBD2F45201F736A7FF9333B607B0944A2D (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_useGravity_m2B41A8BBD2F45201F736A7FF9333B607B0944A2D_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, bool);
	static Cloth_set_useGravity_m2B41A8BBD2F45201F736A7FF9333B607B0944A2D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_useGravity_m2B41A8BBD2F45201F736A7FF9333B607B0944A2D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_useGravity(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Cloth_get_enabled_m8FBD0C2569D07854E8AE41B4EA6987C2D2974E1A (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef bool (*Cloth_get_enabled_m8FBD0C2569D07854E8AE41B4EA6987C2D2974E1A_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_enabled_m8FBD0C2569D07854E8AE41B4EA6987C2D2974E1A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_enabled_m8FBD0C2569D07854E8AE41B4EA6987C2D2974E1A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_enabled()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_enabled_m314D18DBB1943E9984694DBAD2535EFDA393B3DB (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_enabled_m314D18DBB1943E9984694DBAD2535EFDA393B3DB_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, bool);
	static Cloth_set_enabled_m314D18DBB1943E9984694DBAD2535EFDA393B3DB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_enabled_m314D18DBB1943E9984694DBAD2535EFDA393B3DB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_enabled(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_friction_m9901130FAE8B86E65F1A2BCA7FEC5C7406B627F3 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_friction_m9901130FAE8B86E65F1A2BCA7FEC5C7406B627F3_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_friction_m9901130FAE8B86E65F1A2BCA7FEC5C7406B627F3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_friction_m9901130FAE8B86E65F1A2BCA7FEC5C7406B627F3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_friction()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_friction_m176B626212006C70AB17F2A25074356DD43C2F27 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_friction_m176B626212006C70AB17F2A25074356DD43C2F27_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_friction_m176B626212006C70AB17F2A25074356DD43C2F27_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_friction_m176B626212006C70AB17F2A25074356DD43C2F27_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_friction(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_collisionMassScale_m8E238BD5E6A305603175163AF17C9BA364F57109 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_collisionMassScale_m8E238BD5E6A305603175163AF17C9BA364F57109_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_collisionMassScale_m8E238BD5E6A305603175163AF17C9BA364F57109_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_collisionMassScale_m8E238BD5E6A305603175163AF17C9BA364F57109_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_collisionMassScale()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_collisionMassScale_mB2A34759FFC104E82F66557113A6FD53A4465A1C (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_collisionMassScale_mB2A34759FFC104E82F66557113A6FD53A4465A1C_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_collisionMassScale_mB2A34759FFC104E82F66557113A6FD53A4465A1C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_collisionMassScale_mB2A34759FFC104E82F66557113A6FD53A4465A1C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_collisionMassScale(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Cloth_get_enableContinuousCollision_mD01452D0B91495479201DB31B227DA5B58F58403 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef bool (*Cloth_get_enableContinuousCollision_mD01452D0B91495479201DB31B227DA5B58F58403_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_enableContinuousCollision_mD01452D0B91495479201DB31B227DA5B58F58403_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_enableContinuousCollision_mD01452D0B91495479201DB31B227DA5B58F58403_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_enableContinuousCollision()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_enableContinuousCollision_m6A5A4AAA3D7FF792BD0F0BB4986AA8B5250ABE33 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_enableContinuousCollision_m6A5A4AAA3D7FF792BD0F0BB4986AA8B5250ABE33_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, bool);
	static Cloth_set_enableContinuousCollision_m6A5A4AAA3D7FF792BD0F0BB4986AA8B5250ABE33_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_enableContinuousCollision_m6A5A4AAA3D7FF792BD0F0BB4986AA8B5250ABE33_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_enableContinuousCollision(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_useVirtualParticles_m40A99317F45E81DF8D0E6C1863CF948755188A2F (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_useVirtualParticles_m40A99317F45E81DF8D0E6C1863CF948755188A2F_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_useVirtualParticles_m40A99317F45E81DF8D0E6C1863CF948755188A2F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_useVirtualParticles_m40A99317F45E81DF8D0E6C1863CF948755188A2F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_useVirtualParticles()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_useVirtualParticles_mE8058B9161BE16A3945C56DA0C21B8EDE325637E (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_useVirtualParticles_mE8058B9161BE16A3945C56DA0C21B8EDE325637E_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_useVirtualParticles_mE8058B9161BE16A3945C56DA0C21B8EDE325637E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_useVirtualParticles_mE8058B9161BE16A3945C56DA0C21B8EDE325637E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_useVirtualParticles(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_worldVelocityScale_mBD2081338FC233BF3D8DB155058C7817135A2FE2 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_worldVelocityScale_mBD2081338FC233BF3D8DB155058C7817135A2FE2_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_worldVelocityScale_mBD2081338FC233BF3D8DB155058C7817135A2FE2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_worldVelocityScale_mBD2081338FC233BF3D8DB155058C7817135A2FE2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_worldVelocityScale()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_worldVelocityScale_m560E8BACD096D26085410D5CBA2EA65E7E995108 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_worldVelocityScale_m560E8BACD096D26085410D5CBA2EA65E7E995108_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_worldVelocityScale_m560E8BACD096D26085410D5CBA2EA65E7E995108_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_worldVelocityScale_m560E8BACD096D26085410D5CBA2EA65E7E995108_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_worldVelocityScale(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_worldAccelerationScale_m387BFE853B711BE5A6CF182F99D3B233F411A453 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_worldAccelerationScale_m387BFE853B711BE5A6CF182F99D3B233F411A453_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_worldAccelerationScale_m387BFE853B711BE5A6CF182F99D3B233F411A453_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_worldAccelerationScale_m387BFE853B711BE5A6CF182F99D3B233F411A453_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_worldAccelerationScale()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_worldAccelerationScale_m9191999C9886FECEB8C89E01D95E9E309D60E984 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_worldAccelerationScale_m9191999C9886FECEB8C89E01D95E9E309D60E984_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_worldAccelerationScale_m9191999C9886FECEB8C89E01D95E9E309D60E984_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_worldAccelerationScale_m9191999C9886FECEB8C89E01D95E9E309D60E984_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_worldAccelerationScale(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_clothSolverFrequency_mDBB7222AB0355E19195C513B6DDEED35F88A7B4C (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_clothSolverFrequency_mDBB7222AB0355E19195C513B6DDEED35F88A7B4C_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_clothSolverFrequency_mDBB7222AB0355E19195C513B6DDEED35F88A7B4C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_clothSolverFrequency_mDBB7222AB0355E19195C513B6DDEED35F88A7B4C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_clothSolverFrequency()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_clothSolverFrequency_m75608DE320A074915C3B3244E6A4C38CE48CD5E9 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_clothSolverFrequency_m75608DE320A074915C3B3244E6A4C38CE48CD5E9_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_clothSolverFrequency_m75608DE320A074915C3B3244E6A4C38CE48CD5E9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_clothSolverFrequency_m75608DE320A074915C3B3244E6A4C38CE48CD5E9_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_clothSolverFrequency(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Cloth_get_solverFrequency_mC4806844753FA0FAE3D4C33DAF1425E4B868995F (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_get_solverFrequency_mC4806844753FA0FAE3D4C33DAF1425E4B868995F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_get_solverFrequency_mC4806844753FA0FAE3D4C33DAF1425E4B868995F_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 28));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 29));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 30));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 31));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 32));
		float L_0;
		L_0 = Cloth_get_clothSolverFrequency_mDBB7222AB0355E19195C513B6DDEED35F88A7B4C(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 32));
		V_0 = (bool)((((float)L_0) > ((float)(0.0f)))? 1 : 0);
		goto IL_0011;
	}

IL_0011:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 33));
		bool L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_solverFrequency_m3EF836CDE77D1BCBC010623B6CE891896B50C1DC (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, bool ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_set_solverFrequency_m3EF836CDE77D1BCBC010623B6CE891896B50C1DC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_set_solverFrequency_m3EF836CDE77D1BCBC010623B6CE891896B50C1DC_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 34));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 35));
	Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* G_B2_0 = NULL;
	Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* G_B1_0 = NULL;
	float G_B3_0 = 0.0f;
	Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* G_B3_1 = NULL;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 36));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 37));
		bool L_0 = ___0_value;
		if (L_0)
		{
			G_B2_0 = __this;
			goto IL_000c;
		}
		G_B1_0 = __this;
	}
	{
		G_B3_0 = (0.0f);
		G_B3_1 = G_B1_0;
		goto IL_0011;
	}

IL_000c:
	{
		G_B3_0 = (120.0f);
		G_B3_1 = G_B2_0;
	}

IL_0011:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 38));
		NullCheck(G_B3_1);
		Cloth_set_clothSolverFrequency_m75608DE320A074915C3B3244E6A4C38CE48CD5E9(G_B3_1, G_B3_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 38));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 39));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Cloth_get_useTethers_m46ACD80C27CE2B31BEC725E70A06005C3F6556DF (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef bool (*Cloth_get_useTethers_m46ACD80C27CE2B31BEC725E70A06005C3F6556DF_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_useTethers_m46ACD80C27CE2B31BEC725E70A06005C3F6556DF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_useTethers_m46ACD80C27CE2B31BEC725E70A06005C3F6556DF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_useTethers()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_useTethers_m5489A91F922A09FAE9EA41A5BA005C40325B93F3 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_useTethers_m5489A91F922A09FAE9EA41A5BA005C40325B93F3_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, bool);
	static Cloth_set_useTethers_m5489A91F922A09FAE9EA41A5BA005C40325B93F3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_useTethers_m5489A91F922A09FAE9EA41A5BA005C40325B93F3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_useTethers(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_stiffnessFrequency_m6C895A02AE142457DA570223C4855DD9F3B889B4 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_stiffnessFrequency_m6C895A02AE142457DA570223C4855DD9F3B889B4_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_stiffnessFrequency_m6C895A02AE142457DA570223C4855DD9F3B889B4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_stiffnessFrequency_m6C895A02AE142457DA570223C4855DD9F3B889B4_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_stiffnessFrequency()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_stiffnessFrequency_m55D281DBF6466517F0F0E5BCE7EBFA33AA4B3D68 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_stiffnessFrequency_m55D281DBF6466517F0F0E5BCE7EBFA33AA4B3D68_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_stiffnessFrequency_m55D281DBF6466517F0F0E5BCE7EBFA33AA4B3D68_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_stiffnessFrequency_m55D281DBF6466517F0F0E5BCE7EBFA33AA4B3D68_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_stiffnessFrequency(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_selfCollisionDistance_mEF57920DE88E7EE7316D1B976D900EBEA97187D3 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_selfCollisionDistance_mEF57920DE88E7EE7316D1B976D900EBEA97187D3_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_selfCollisionDistance_mEF57920DE88E7EE7316D1B976D900EBEA97187D3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_selfCollisionDistance_mEF57920DE88E7EE7316D1B976D900EBEA97187D3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_selfCollisionDistance()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_selfCollisionDistance_m063905EE7B17B313361744B2A9298891BD04458B (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_selfCollisionDistance_m063905EE7B17B313361744B2A9298891BD04458B_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_selfCollisionDistance_m063905EE7B17B313361744B2A9298891BD04458B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_selfCollisionDistance_m063905EE7B17B313361744B2A9298891BD04458B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_selfCollisionDistance(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_selfCollisionStiffness_m3AB07989A94F1003B1A503786035DA9798750B79 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef float (*Cloth_get_selfCollisionStiffness_m3AB07989A94F1003B1A503786035DA9798750B79_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_get_selfCollisionStiffness_m3AB07989A94F1003B1A503786035DA9798750B79_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_selfCollisionStiffness_m3AB07989A94F1003B1A503786035DA9798750B79_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_selfCollisionStiffness()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_selfCollisionStiffness_m135046E1757E8DC1FCCBE36A2B3639C2F590F148 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_selfCollisionStiffness_m135046E1757E8DC1FCCBE36A2B3639C2F590F148_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, float);
	static Cloth_set_selfCollisionStiffness_m135046E1757E8DC1FCCBE36A2B3639C2F590F148_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_selfCollisionStiffness_m135046E1757E8DC1FCCBE36A2B3639C2F590F148_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_selfCollisionStiffness(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_ClearTransformMotion_m753025BD5BC096D05442CD608B7A92E5F50C5EAB (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	typedef void (*Cloth_ClearTransformMotion_m753025BD5BC096D05442CD608B7A92E5F50C5EAB_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*);
	static Cloth_ClearTransformMotion_m753025BD5BC096D05442CD608B7A92E5F50C5EAB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_ClearTransformMotion_m753025BD5BC096D05442CD608B7A92E5F50C5EAB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::ClearTransformMotion()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_GetSelfAndInterCollisionIndices_mD9966556AF927D7AF90DEEC019A380180BF373D8 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A* ___0_indices, const RuntimeMethod* method) 
{
	typedef void (*Cloth_GetSelfAndInterCollisionIndices_mD9966556AF927D7AF90DEEC019A380180BF373D8_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A*);
	static Cloth_GetSelfAndInterCollisionIndices_mD9966556AF927D7AF90DEEC019A380180BF373D8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_GetSelfAndInterCollisionIndices_mD9966556AF927D7AF90DEEC019A380180BF373D8_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::GetSelfAndInterCollisionIndices(System.Collections.Generic.List`1<System.UInt32>)");
	_il2cpp_icall_func(__this, ___0_indices);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_SetSelfAndInterCollisionIndices_mD0908641806B573F05B8948FDD63E898B8EE29DB (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A* ___0_indices, const RuntimeMethod* method) 
{
	typedef void (*Cloth_SetSelfAndInterCollisionIndices_mD0908641806B573F05B8948FDD63E898B8EE29DB_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A*);
	static Cloth_SetSelfAndInterCollisionIndices_mD0908641806B573F05B8948FDD63E898B8EE29DB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_SetSelfAndInterCollisionIndices_mD0908641806B573F05B8948FDD63E898B8EE29DB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::SetSelfAndInterCollisionIndices(System.Collections.Generic.List`1<System.UInt32>)");
	_il2cpp_icall_func(__this, ___0_indices);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_GetVirtualParticleIndices_m278174767DD72C2F1859D6D7A040B0EB5D7543F9 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A* ___0_indicesOutList, const RuntimeMethod* method) 
{
	typedef void (*Cloth_GetVirtualParticleIndices_m278174767DD72C2F1859D6D7A040B0EB5D7543F9_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A*);
	static Cloth_GetVirtualParticleIndices_m278174767DD72C2F1859D6D7A040B0EB5D7543F9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_GetVirtualParticleIndices_m278174767DD72C2F1859D6D7A040B0EB5D7543F9_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::GetVirtualParticleIndices(System.Collections.Generic.List`1<System.UInt32>)");
	_il2cpp_icall_func(__this, ___0_indicesOutList);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_SetVirtualParticleIndices_mDC58009B42A02B3AC350116A0A696206A9AF880B (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A* ___0_indicesIn, const RuntimeMethod* method) 
{
	typedef void (*Cloth_SetVirtualParticleIndices_mDC58009B42A02B3AC350116A0A696206A9AF880B_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A*);
	static Cloth_SetVirtualParticleIndices_mDC58009B42A02B3AC350116A0A696206A9AF880B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_SetVirtualParticleIndices_mDC58009B42A02B3AC350116A0A696206A9AF880B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::SetVirtualParticleIndices(System.Collections.Generic.List`1<System.UInt32>)");
	_il2cpp_icall_func(__this, ___0_indicesIn);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_GetVirtualParticleWeights_mC2B8785C9A37C4A487D0088F21C0FFF2804E13DD (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B* ___0_weightsOutList, const RuntimeMethod* method) 
{
	typedef void (*Cloth_GetVirtualParticleWeights_mC2B8785C9A37C4A487D0088F21C0FFF2804E13DD_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B*);
	static Cloth_GetVirtualParticleWeights_mC2B8785C9A37C4A487D0088F21C0FFF2804E13DD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_GetVirtualParticleWeights_mC2B8785C9A37C4A487D0088F21C0FFF2804E13DD_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::GetVirtualParticleWeights(System.Collections.Generic.List`1<UnityEngine.Vector3>)");
	_il2cpp_icall_func(__this, ___0_weightsOutList);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_SetVirtualParticleWeights_m9529D339A6850E153581E70CC951DC686934E377 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B* ___0_weights, const RuntimeMethod* method) 
{
	typedef void (*Cloth_SetVirtualParticleWeights_m9529D339A6850E153581E70CC951DC686934E377_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B*);
	static Cloth_SetVirtualParticleWeights_m9529D339A6850E153581E70CC951DC686934E377_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_SetVirtualParticleWeights_m9529D339A6850E153581E70CC951DC686934E377_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::SetVirtualParticleWeights(System.Collections.Generic.List`1<UnityEngine.Vector3>)");
	_il2cpp_icall_func(__this, ___0_weights);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cloth_get_useContinuousCollision_m3338C62BE424E9E4A2206A349F274B10D3A2A318 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_get_useContinuousCollision_m3338C62BE424E9E4A2206A349F274B10D3A2A318_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_get_useContinuousCollision_m3338C62BE424E9E4A2206A349F274B10D3A2A318_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 40));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 41));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 42));
		float L_0 = __this->___U3CuseContinuousCollisionU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_useContinuousCollision_m6B72DB7BD80AF0F15288E50BC4CF528F9A2A87D7 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_set_useContinuousCollision_m6B72DB7BD80AF0F15288E50BC4CF528F9A2A87D7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_set_useContinuousCollision_m6B72DB7BD80AF0F15288E50BC4CF528F9A2A87D7_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 43));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 44));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 45));
		float L_0 = ___0_value;
		__this->___U3CuseContinuousCollisionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Cloth_get_selfCollision_m3D2C90B0B129F802488CF3DABB7884D797F2A5FF (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_get_selfCollision_m3D2C90B0B129F802488CF3DABB7884D797F2A5FF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_get_selfCollision_m3D2C90B0B129F802488CF3DABB7884D797F2A5FF_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 46));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 47));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 48));
		bool L_0 = __this->___U3CselfCollisionU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_SetEnabledFading_mD98337A5404F8314CB32D91644F6995579D9855F (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, bool ___0_enabled, float ___1_interpolationTime, const RuntimeMethod* method) 
{
	typedef void (*Cloth_SetEnabledFading_mD98337A5404F8314CB32D91644F6995579D9855F_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, bool, float);
	static Cloth_SetEnabledFading_mD98337A5404F8314CB32D91644F6995579D9855F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_SetEnabledFading_mD98337A5404F8314CB32D91644F6995579D9855F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::SetEnabledFading(System.Boolean,System.Single)");
	_il2cpp_icall_func(__this, ___0_enabled, ___1_interpolationTime);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_SetEnabledFading_mFC216321A4512E3BD8A3EA2557F723E92FBDE12B (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, bool ___0_enabled, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_SetEnabledFading_mFC216321A4512E3BD8A3EA2557F723E92FBDE12B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_enabled));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_SetEnabledFading_mFC216321A4512E3BD8A3EA2557F723E92FBDE12B_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 49));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 50));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 51));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 52));
		bool L_0 = ___0_enabled;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 53));
		Cloth_SetEnabledFading_mD98337A5404F8314CB32D91644F6995579D9855F(__this, L_0, (0.5f), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 53));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 54));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 Cloth_Raycast_m5BB6CD5E82EB2F4F2B17A38FA2641DB2CBFB0525 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00 ___0_ray, float ___1_maxDistance, bool* ___2_hasHit, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth_Raycast_m5BB6CD5E82EB2F4F2B17A38FA2641DB2CBFB0525_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth_Raycast_m5BB6CD5E82EB2F4F2B17A38FA2641DB2CBFB0525_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = ___1_maxDistance;
		bool* L_1 = ___2_hasHit;
		Cloth_Raycast_Injected_mD254C34D2EFA5B8F3278CF864D6C62B6564DA46C(__this, (&___0_ray), L_0, L_1, (&V_0), NULL);
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth__ctor_mBC218989C0A6B3EC5CA5490BABC664E7F2B08320 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Cloth__ctor_mBC218989C0A6B3EC5CA5490BABC664E7F2B08320_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Cloth__ctor_mBC218989C0A6B3EC5CA5490BABC664E7F2B08320_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Component__ctor_m4319162A6E6B02301078C1233F6E7F4A3E735486(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_get_externalAcceleration_Injected_m447914E289BF0CDDD0F7003A9BE2C0D1F062AF86 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*Cloth_get_externalAcceleration_Injected_m447914E289BF0CDDD0F7003A9BE2C0D1F062AF86_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Cloth_get_externalAcceleration_Injected_m447914E289BF0CDDD0F7003A9BE2C0D1F062AF86_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_externalAcceleration_Injected_m447914E289BF0CDDD0F7003A9BE2C0D1F062AF86_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_externalAcceleration_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_externalAcceleration_Injected_m7D6915CC37E94AA29764A5B02BC19D27D41998AD (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_externalAcceleration_Injected_m7D6915CC37E94AA29764A5B02BC19D27D41998AD_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Cloth_set_externalAcceleration_Injected_m7D6915CC37E94AA29764A5B02BC19D27D41998AD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_externalAcceleration_Injected_m7D6915CC37E94AA29764A5B02BC19D27D41998AD_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_externalAcceleration_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_get_randomAcceleration_Injected_m97B63C18E1B3FB737D9330FAE693F3EF3A03A052 (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*Cloth_get_randomAcceleration_Injected_m97B63C18E1B3FB737D9330FAE693F3EF3A03A052_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Cloth_get_randomAcceleration_Injected_m97B63C18E1B3FB737D9330FAE693F3EF3A03A052_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_get_randomAcceleration_Injected_m97B63C18E1B3FB737D9330FAE693F3EF3A03A052_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::get_randomAcceleration_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_set_randomAcceleration_Injected_m232B588EF417B76FAFA2B28669EC76F5383AD30A (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Cloth_set_randomAcceleration_Injected_m232B588EF417B76FAFA2B28669EC76F5383AD30A_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Cloth_set_randomAcceleration_Injected_m232B588EF417B76FAFA2B28669EC76F5383AD30A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_set_randomAcceleration_Injected_m232B588EF417B76FAFA2B28669EC76F5383AD30A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::set_randomAcceleration_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Cloth_Raycast_Injected_mD254C34D2EFA5B8F3278CF864D6C62B6564DA46C (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948* __this, Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00* ___0_ray, float ___1_maxDistance, bool* ___2_hasHit, RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* ___3_ret, const RuntimeMethod* method) 
{
	typedef void (*Cloth_Raycast_Injected_mD254C34D2EFA5B8F3278CF864D6C62B6564DA46C_ftn) (Cloth_t42A539800D474B2BBC00D5847BCE3D46B0850948*, Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00*, float, bool*, RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*);
	static Cloth_Raycast_Injected_mD254C34D2EFA5B8F3278CF864D6C62B6564DA46C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Cloth_Raycast_Injected_mD254C34D2EFA5B8F3278CF864D6C62B6564DA46C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Cloth::Raycast_Injected(UnityEngine.Ray&,System.Single,System.Boolean&,UnityEngine.RaycastHit&)");
	_il2cpp_icall_func(__this, ___0_ray, ___1_maxDistance, ___2_hasHit, ___3_ret);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D_inline (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair_get_first_m946C5B40948A5D91176D9498D36F160FC8CBA69D_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 2));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = __this->___U3CfirstU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_inline (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair_set_first_m0A1B7959D953AE101AFE5E2D05816A00AD3FE75E_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 3));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 4));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 5));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = ___0_value;
		__this->___U3CfirstU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CfirstU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4_inline (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair_get_second_mF90FC567FB7EF22C50848D1D3DD19217193781F4_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 6));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 7));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 8));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = __this->___U3CsecondU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_inline (ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F* __this, SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ClothSphereColliderPair_t8F081FA93134D336CD2AC63D91565D08B33D8A6F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ClothSphereColliderPair_set_second_mD06B1045D859C8E0D1E04C5B43DFD0C9D6AA91E3_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 9));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 10));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_ClothModule + 11));
		SphereCollider_tBA111C542CE97F6873DE742757213D6265C7D275* L_0 = ___0_value;
		__this->___U3CsecondU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsecondU3Ek__BackingField), (void*)L_0);
		return;
	}
}
