﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4;
struct SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B;
struct SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED;
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3;
struct Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4;
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF;
struct Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99;
struct SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC;
struct SpriteShapeUtility_t9344C0D12FC3B3D4E280BE0CB6F7398B14335874;
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_SpriteShapeModule[];
IL2CPP_EXTERN_C RuntimeClass* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45____fillTexture_FieldInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetBounds_mB1109C67BE9B7A2376B92299C07B89E25026E42A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannelInfo_m6B622671C214A2920BAC1DF0190DEC62ABA7BADA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannels_m08BD580C754823A2578D887BEEF70AA43019DB8F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannels_m2F1DEBB233238E415AFF4B82BA7D1B3C4A9FC85C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannels_m481C5B4F534E8037FFA0B856BCD371DAB112C3CF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannels_mB103260A403E60CB28AFFE799447DE075A114FC6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannels_mB9F0096C5B4DCCB7BA9A9DAFA085B5400E0B33E2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetChannels_mBFF908DA30D2D2A9650F917211D83F6A5795D755_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetDataInfo_m8BEE8EE1332F043EA4FF0CFDF2F0C2882BDF800D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetNativeDataArray_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m0838818F39B270DF99CC37D5CC7E50864E4FE913_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetNativeDataArray_TisSpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_mF62707A86DE10260EC8D744C6B3F2AC75D8EEED1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_GetSegments_m20EAF8C9AA9B74C31053A0F69B60B15D7967AA20_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_Prepare_m3EB1D60213EC54306CEC059519D22E2754072950_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_SetLocalAABB_m50672DFB7F3EF0AB13FF725A86EA3DA718C8F080_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer__ctor_m44BF2C8BE32D32910B2E162D0830BFC36694D6A6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_get_color_mB5F692D74527187556CAE5F02D93EA792B03D45F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeRenderer_set_color_mD5C6A8ADF2BA925EBA287105C5450251004E0E51_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeUtility_GenerateSpriteShape_mB19EF6B7F3B223E8E10CD85EFA808D4A7D6330AF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeUtility_Generate_m37169E418B14B90AE2B0D7E2CA35C7B666B55016_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteShapeUtility__ctor_m15FD3D11661152CFCE8FEE92BEBA837B55238426_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* NativeArray_1_t2BC742D9A03AF608A0AB5B638F49639E1C7BD6ED_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* NativeArray_1_t596D8D9BF28AE72A671779EB28469319AC3F1147_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_0_0_0_var;

struct AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4;
struct SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B;
struct SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t4BC86AB59C63F2CC6EB51BE560C15CFCAE821BC7 
{
};
struct SpriteShapeUtility_t9344C0D12FC3B3D4E280BE0CB6F7398B14335874  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F 
{
	uint8_t* ___m_Buffer;
	int32_t ___m_Stride;
	int32_t ___m_Length;
};
struct NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF 
{
	uint8_t* ___m_Buffer;
	int32_t ___m_Stride;
	int32_t ___m_Length;
};
struct NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A 
{
	uint8_t* ___m_Buffer;
	int32_t ___m_Stride;
	int32_t ___m_Length;
};
struct NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F 
{
	uint8_t* ___m_Buffer;
	int32_t ___m_Stride;
	int32_t ___m_Length;
};
struct NativeSlice_1_tA54E5D259EBCC7CD8512AA352C6F3709EB237B52 
{
	uint8_t* ___m_Buffer;
	int32_t ___m_Stride;
	int32_t ___m_Length;
};
struct AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB 
{
	float ___start;
	float ___end;
	uint32_t ___order;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___sprites;
};
struct AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshaled_pinvoke
{
	float ___start;
	float ___end;
	uint32_t ___order;
	Il2CppSafeArray* ___sprites;
};
struct AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshaled_com
{
	float ___start;
	float ___end;
	uint32_t ___order;
	Il2CppSafeArray* ___sprites;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08 
{
	uint64_t ___jobGroup;
	int32_t ___version;
};
struct Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 
{
	float ___m00;
	float ___m10;
	float ___m20;
	float ___m30;
	float ___m01;
	float ___m11;
	float ___m21;
	float ___m31;
	float ___m02;
	float ___m12;
	float ___m22;
	float ___m32;
	float ___m03;
	float ___m13;
	float ___m23;
	float ___m33;
};
struct SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501 
{
	float ___height;
	float ___bevelCutoff;
	float ___bevelSize;
	uint32_t ___spriteIndex;
	bool ___corner;
};
struct SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshaled_pinvoke
{
	float ___height;
	float ___bevelCutoff;
	float ___bevelSize;
	uint32_t ___spriteIndex;
	int32_t ___corner;
};
struct SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshaled_com
{
	float ___height;
	float ___bevelCutoff;
	float ___bevelSize;
	uint32_t ___spriteIndex;
	int32_t ___corner;
};
struct SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5 
{
	int32_t ___m_GeomIndex;
	int32_t ___m_IndexCount;
	int32_t ___m_VertexCount;
	int32_t ___m_SpriteIndex;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Allocator_t996642592271AAD9EE688F142741D512C07B5824 
{
	int32_t ___value__;
};
struct Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Center;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Extents;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct ShapeControlPoint_tFB166AFC7B226867782300A7448C406D6DE6F8F5 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___position;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftTangent;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightTangent;
	int32_t ___mode;
};
struct SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA 
{
	intptr_t ___m_Buffer;
	int32_t ___m_Count;
	int32_t ___m_Offset;
	int32_t ___m_Stride;
};
struct SpriteMaskInteraction_tC888A8071B3F72BFCCC7717F292C303839070D42 
{
	int32_t ___value__;
};
struct SpriteShapeDataType_t8669A0394F6632D6C9965352319BC2612F7A5F69 
{
	int32_t ___value__;
};
struct SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45 
{
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___transform;
	Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___fillTexture;
	uint32_t ___fillScale;
	uint32_t ___splineDetail;
	float ___angleThreshold;
	float ___borderPivot;
	float ___bevelCutoff;
	float ___bevelSize;
	bool ___carpet;
	bool ___smartSprite;
	bool ___adaptiveUV;
	bool ___spriteBorders;
	bool ___stretchUV;
};
struct SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshaled_pinvoke
{
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___transform;
	Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___fillTexture;
	uint32_t ___fillScale;
	uint32_t ___splineDetail;
	float ___angleThreshold;
	float ___borderPivot;
	float ___bevelCutoff;
	float ___bevelSize;
	int32_t ___carpet;
	int32_t ___smartSprite;
	int32_t ___adaptiveUV;
	int32_t ___spriteBorders;
	int32_t ___stretchUV;
};
struct SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshaled_com
{
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___transform;
	Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___fillTexture;
	uint32_t ___fillScale;
	uint32_t ___splineDetail;
	float ___angleThreshold;
	float ___borderPivot;
	float ___bevelCutoff;
	float ___bevelSize;
	int32_t ___carpet;
	int32_t ___smartSprite;
	int32_t ___adaptiveUV;
	int32_t ___spriteBorders;
	int32_t ___stretchUV;
};
struct VertexAttribute_tF34C1B76F20CA4AEC9D606BCD37A8A0C4A24C9A6 
{
	int32_t ___value__;
};
struct NativeArray_1_t596D8D9BF28AE72A671779EB28469319AC3F1147 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t2BC742D9A03AF608A0AB5B638F49639E1C7BD6ED 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_tDB8B8DC66CC8E16ED6D9A8C75D2C1AFC80AC1E18 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4  : public Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700
{
};
struct SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC  : public Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF
{
};
struct Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6_StaticFields
{
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___zeroMatrix;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___identityMatrix;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B  : public RuntimeArray
{
	ALIGN_FIELD (8) Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* m_Items[1];

	inline Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C  : public RuntimeArray
{
	ALIGN_FIELD (8) int32_t m_Items[1];

	inline int32_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int32_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int32_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int32_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int32_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int32_t value)
	{
		m_Items[index] = value;
	}
};
struct ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4  : public RuntimeArray
{
	ALIGN_FIELD (8) ShapeControlPoint_tFB166AFC7B226867782300A7448C406D6DE6F8F5 m_Items[1];

	inline ShapeControlPoint_tFB166AFC7B226867782300A7448C406D6DE6F8F5 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline ShapeControlPoint_tFB166AFC7B226867782300A7448C406D6DE6F8F5* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, ShapeControlPoint_tFB166AFC7B226867782300A7448C406D6DE6F8F5 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline ShapeControlPoint_tFB166AFC7B226867782300A7448C406D6DE6F8F5 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline ShapeControlPoint_tFB166AFC7B226867782300A7448C406D6DE6F8F5* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, ShapeControlPoint_tFB166AFC7B226867782300A7448C406D6DE6F8F5 value)
	{
		m_Items[index] = value;
	}
};
struct SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED  : public RuntimeArray
{
	ALIGN_FIELD (8) SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501 m_Items[1];

	inline SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501 value)
	{
		m_Items[index] = value;
	}
};
struct AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8  : public RuntimeArray
{
	ALIGN_FIELD (8) AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB m_Items[1];

	inline AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___sprites), (void*)NULL);
	}
	inline AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___sprites), (void*)NULL);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR NativeArray_1_tDB8B8DC66CC8E16ED6D9A8C75D2C1AFC80AC1E18 SpriteShapeRenderer_GetNativeDataArray_TisIl2CppFullySharedGenericStruct_m3EC374EDA7CB935928FDEA4BB3275EA985868516_gshared (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataType, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR NativeSlice_1_tA54E5D259EBCC7CD8512AA352C6F3709EB237B52 SpriteShapeRenderer_GetChannelDataArray_TisIl2CppFullySharedGenericStruct_m4A328169E4C95A673DC6714D9E57D9CD0260A7D6_gshared (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataType, int32_t ___1_channel, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_get_color_Injected_m5DF8CA684904BB1D9EB6A1D8C51050C33AE837E3 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_set_color_Injected_m3738681F39E29CDC45E204CAFE8B417D8B7969CF (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_Prepare_Injected_mD1195648DC363033B06C211A6F5A00F30C8B0F7D (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08* ___0_handle, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45* ___1_shapeParams, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___2_sprites, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetDataInfo_Injected_m9F93C146CC8377F6C657038271C48CBD8E2E784C (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_arrayType, SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetChannelInfo_Injected_mABC1866E231BC32890EC4CE7768652F2DFA4913D (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_channel, SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_SetLocalAABB_Injected_m1539FADCD075EFA3FA8BFE925FC503192847F105 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3* ___0_bounds, const RuntimeMethod* method) ;
inline NativeArray_1_t596D8D9BF28AE72A671779EB28469319AC3F1147 SpriteShapeRenderer_GetNativeDataArray_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m0838818F39B270DF99CC37D5CC7E50864E4FE913 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataType, const RuntimeMethod* method)
{
	return ((  NativeArray_1_t596D8D9BF28AE72A671779EB28469319AC3F1147 (*) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, const RuntimeMethod*))SpriteShapeRenderer_GetNativeDataArray_TisIl2CppFullySharedGenericStruct_m3EC374EDA7CB935928FDEA4BB3275EA985868516_gshared)(__this, ___0_dataType, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_geomCount, const RuntimeMethod* method) ;
inline NativeArray_1_t2BC742D9A03AF608A0AB5B638F49639E1C7BD6ED SpriteShapeRenderer_GetNativeDataArray_TisSpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_mF62707A86DE10260EC8D744C6B3F2AC75D8EEED1 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataType, const RuntimeMethod* method)
{
	return ((  NativeArray_1_t2BC742D9A03AF608A0AB5B638F49639E1C7BD6ED (*) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, const RuntimeMethod*))SpriteShapeRenderer_GetNativeDataArray_TisIl2CppFullySharedGenericStruct_m3EC374EDA7CB935928FDEA4BB3275EA985868516_gshared)(__this, ___0_dataType, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_vertexCount, int32_t ___1_indexCount, const RuntimeMethod* method) ;
inline NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934 SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataType, const RuntimeMethod* method)
{
	return ((  NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934 (*) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, const RuntimeMethod*))SpriteShapeRenderer_GetNativeDataArray_TisIl2CppFullySharedGenericStruct_m3EC374EDA7CB935928FDEA4BB3275EA985868516_gshared)(__this, ___0_dataType, method);
}
inline NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataType, int32_t ___1_channel, const RuntimeMethod* method)
{
	return ((  NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A (*) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, int32_t, const RuntimeMethod*))SpriteShapeRenderer_GetChannelDataArray_TisIl2CppFullySharedGenericStruct_m4A328169E4C95A673DC6714D9E57D9CD0260A7D6_gshared)(__this, ___0_dataType, ___1_channel, method);
}
inline NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataType, int32_t ___1_channel, const RuntimeMethod* method)
{
	return ((  NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF (*) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, int32_t, const RuntimeMethod*))SpriteShapeRenderer_GetChannelDataArray_TisIl2CppFullySharedGenericStruct_m4A328169E4C95A673DC6714D9E57D9CD0260A7D6_gshared)(__this, ___0_dataType, ___1_channel, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_vertexCount, int32_t ___1_indexCount, int32_t ___2_hotChannelMask, const RuntimeMethod* method) ;
inline NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataType, int32_t ___1_channel, const RuntimeMethod* method)
{
	return ((  NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F (*) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, int32_t, const RuntimeMethod*))SpriteShapeRenderer_GetChannelDataArray_TisIl2CppFullySharedGenericStruct_m4A328169E4C95A673DC6714D9E57D9CD0260A7D6_gshared)(__this, ___0_dataType, ___1_channel, method);
}
inline NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataType, int32_t ___1_channel, const RuntimeMethod* method)
{
	return ((  NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F (*) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, int32_t, const RuntimeMethod*))SpriteShapeRenderer_GetChannelDataArray_TisIl2CppFullySharedGenericStruct_m4A328169E4C95A673DC6714D9E57D9CD0260A7D6_gshared)(__this, ___0_dataType, ___1_channel, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Renderer__ctor_m8B4EE9696B155A1B0A2CF13EBFC363CE175B9271 (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* SpriteShapeUtility_Generate_Injected_m7EDCAE2EEE61FA62B4DC05DE3B42C2E7FA7CFBA4 (Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___0_mesh, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45* ___1_shapeParams, ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4* ___2_points, SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED* ___3_metaData, AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8* ___4_angleRange, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___5_sprites, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___6_corners, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeUtility_GenerateSpriteShape_Injected_m29F14BC6698B8BEF672D4B734B9BBE08E481F5AB (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* ___0_renderer, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45* ___1_shapeParams, ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4* ___2_points, SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED* ___3_metaData, AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8* ___4_angleRange, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___5_sprites, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___6_corners, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshal_pinvoke(const SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45& unmarshaled, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45____fillTexture_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___fillTextureException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45____fillTexture_FieldInfo_var, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___fillTextureException, NULL);
}
IL2CPP_EXTERN_C void SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshal_pinvoke_back(const SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshaled_pinvoke& marshaled, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45____fillTexture_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___fillTextureException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45____fillTexture_FieldInfo_var, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___fillTextureException, NULL);
}
IL2CPP_EXTERN_C void SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshal_pinvoke_cleanup(SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshal_com(const SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45& unmarshaled, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45____fillTexture_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___fillTextureException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45____fillTexture_FieldInfo_var, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___fillTextureException, NULL);
}
IL2CPP_EXTERN_C void SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshal_com_back(const SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshaled_com& marshaled, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45____fillTexture_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___fillTextureException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45____fillTexture_FieldInfo_var, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___fillTextureException, NULL);
}
IL2CPP_EXTERN_C void SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshal_com_cleanup(SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45_marshaled_com& marshaled)
{
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 3));
		int32_t L_0 = __this->___m_GeomIndex;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 4));
		int32_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  int32_t SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 5));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 6));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 7));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 8));
		int32_t L_0 = ___0_value;
		__this->___m_GeomIndex = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 9));
		return;
	}
}
IL2CPP_EXTERN_C  void SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5*>(__this + _offset);
	SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 10));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 11));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 12));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 13));
		int32_t L_0 = __this->___m_IndexCount;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 14));
		int32_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  int32_t SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 15));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 16));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 17));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 18));
		int32_t L_0 = ___0_value;
		__this->___m_IndexCount = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 19));
		return;
	}
}
IL2CPP_EXTERN_C  void SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5*>(__this + _offset);
	SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 20));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 21));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 22));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 23));
		int32_t L_0 = __this->___m_VertexCount;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 24));
		int32_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  int32_t SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 25));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 26));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 27));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 28));
		int32_t L_0 = ___0_value;
		__this->___m_VertexCount = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 29));
		return;
	}
}
IL2CPP_EXTERN_C  void SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5*>(__this + _offset);
	SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 30));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 31));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 32));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 33));
		int32_t L_0 = __this->___m_SpriteIndex;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 34));
		int32_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  int32_t SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13 (SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 35));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 36));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 37));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 38));
		int32_t L_0 = ___0_value;
		__this->___m_SpriteIndex = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 39));
		return;
	}
}
IL2CPP_EXTERN_C  void SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5*>(__this + _offset);
	SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13(_thisAdjusted, ___0_value, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F SpriteShapeRenderer_get_color_mB5F692D74527187556CAE5F02D93EA792B03D45F (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_get_color_mB5F692D74527187556CAE5F02D93EA792B03D45F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_get_color_mB5F692D74527187556CAE5F02D93EA792B03D45F_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		SpriteShapeRenderer_get_color_Injected_m5DF8CA684904BB1D9EB6A1D8C51050C33AE837E3(__this, (&V_0), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_set_color_mD5C6A8ADF2BA925EBA287105C5450251004E0E51 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_set_color_mD5C6A8ADF2BA925EBA287105C5450251004E0E51_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_set_color_mD5C6A8ADF2BA925EBA287105C5450251004E0E51_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		SpriteShapeRenderer_set_color_Injected_m3738681F39E29CDC45E204CAFE8B417D8B7969CF(__this, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteShapeRenderer_get_maskInteraction_mDC3CCF2A83DBA14E86841D07208DD388BBD4B652 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*SpriteShapeRenderer_get_maskInteraction_mDC3CCF2A83DBA14E86841D07208DD388BBD4B652_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*);
	static SpriteShapeRenderer_get_maskInteraction_mDC3CCF2A83DBA14E86841D07208DD388BBD4B652_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_get_maskInteraction_mDC3CCF2A83DBA14E86841D07208DD388BBD4B652_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::get_maskInteraction()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_set_maskInteraction_mEB99D9FDCAE6E313335F1DDB1719ABE99CD2F67A (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_set_maskInteraction_mEB99D9FDCAE6E313335F1DDB1719ABE99CD2F67A_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t);
	static SpriteShapeRenderer_set_maskInteraction_mEB99D9FDCAE6E313335F1DDB1719ABE99CD2F67A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_set_maskInteraction_mEB99D9FDCAE6E313335F1DDB1719ABE99CD2F67A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::set_maskInteraction(UnityEngine.SpriteMaskInteraction)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_Prepare_m3EB1D60213EC54306CEC059519D22E2754072950 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08 ___0_handle, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45 ___1_shapeParams, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___2_sprites, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_Prepare_m3EB1D60213EC54306CEC059519D22E2754072950_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_Prepare_m3EB1D60213EC54306CEC059519D22E2754072950_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* L_0 = ___2_sprites;
		SpriteShapeRenderer_Prepare_Injected_mD1195648DC363033B06C211A6F5A00F30C8B0F7D(__this, (&___0_handle), (&___1_shapeParams), L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_geomCount, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t);
	static SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::SetSegmentCount(System.Int32)");
	_il2cpp_icall_func(__this, ___0_geomCount);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_vertexCount, int32_t ___1_indexCount, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, int32_t);
	static SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::SetMeshDataCount(System.Int32,System.Int32)");
	_il2cpp_icall_func(__this, ___0_vertexCount, ___1_indexCount);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_vertexCount, int32_t ___1_indexCount, int32_t ___2_hotChannelMask, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, int32_t, int32_t);
	static SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::SetMeshChannelInfo(System.Int32,System.Int32,System.Int32)");
	_il2cpp_icall_func(__this, ___0_vertexCount, ___1_indexCount, ___2_hotChannelMask);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA SpriteShapeRenderer_GetDataInfo_m8BEE8EE1332F043EA4FF0CFDF2F0C2882BDF800D (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_arrayType, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetDataInfo_m8BEE8EE1332F043EA4FF0CFDF2F0C2882BDF800D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetDataInfo_m8BEE8EE1332F043EA4FF0CFDF2F0C2882BDF800D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = ___0_arrayType;
		SpriteShapeRenderer_GetDataInfo_Injected_m9F93C146CC8377F6C657038271C48CBD8E2E784C(__this, L_0, (&V_0), NULL);
		SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA SpriteShapeRenderer_GetChannelInfo_m6B622671C214A2920BAC1DF0190DEC62ABA7BADA (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_channel, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelInfo_m6B622671C214A2920BAC1DF0190DEC62ABA7BADA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetChannelInfo_m6B622671C214A2920BAC1DF0190DEC62ABA7BADA_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = ___0_channel;
		SpriteShapeRenderer_GetChannelInfo_Injected_mABC1866E231BC32890EC4CE7768652F2DFA4913D(__this, L_0, (&V_0), NULL);
		SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_SetLocalAABB_m50672DFB7F3EF0AB13FF725A86EA3DA718C8F080 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 ___0_bounds, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_SetLocalAABB_m50672DFB7F3EF0AB13FF725A86EA3DA718C8F080_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_SetLocalAABB_m50672DFB7F3EF0AB13FF725A86EA3DA718C8F080_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		SpriteShapeRenderer_SetLocalAABB_Injected_m1539FADCD075EFA3FA8BFE925FC503192847F105(__this, (&___0_bounds), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR NativeArray_1_t596D8D9BF28AE72A671779EB28469319AC3F1147 SpriteShapeRenderer_GetBounds_mB1109C67BE9B7A2376B92299C07B89E25026E42A (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_t596D8D9BF28AE72A671779EB28469319AC3F1147_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetBounds_mB1109C67BE9B7A2376B92299C07B89E25026E42A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetNativeDataArray_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m0838818F39B270DF99CC37D5CC7E50864E4FE913_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	NativeArray_1_t596D8D9BF28AE72A671779EB28469319AC3F1147 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetBounds_mB1109C67BE9B7A2376B92299C07B89E25026E42A_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 65));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 66));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 67));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 68));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 69));
		NativeArray_1_t596D8D9BF28AE72A671779EB28469319AC3F1147 L_0;
		L_0 = SpriteShapeRenderer_GetNativeDataArray_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m0838818F39B270DF99CC37D5CC7E50864E4FE913(__this, 2, SpriteShapeRenderer_GetNativeDataArray_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m0838818F39B270DF99CC37D5CC7E50864E4FE913_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 69));
		V_0 = L_0;
		goto IL_000b;
	}

IL_000b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 70));
		NativeArray_1_t596D8D9BF28AE72A671779EB28469319AC3F1147 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR NativeArray_1_t2BC742D9A03AF608A0AB5B638F49639E1C7BD6ED SpriteShapeRenderer_GetSegments_m20EAF8C9AA9B74C31053A0F69B60B15D7967AA20 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataSize, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_t2BC742D9A03AF608A0AB5B638F49639E1C7BD6ED_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetNativeDataArray_TisSpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_mF62707A86DE10260EC8D744C6B3F2AC75D8EEED1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetSegments_m20EAF8C9AA9B74C31053A0F69B60B15D7967AA20_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	NativeArray_1_t2BC742D9A03AF608A0AB5B638F49639E1C7BD6ED V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_dataSize));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetSegments_m20EAF8C9AA9B74C31053A0F69B60B15D7967AA20_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 71));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 72));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 73));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 74));
		int32_t L_0 = ___0_dataSize;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 75));
		SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 75));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 76));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 77));
		NativeArray_1_t2BC742D9A03AF608A0AB5B638F49639E1C7BD6ED L_1;
		L_1 = SpriteShapeRenderer_GetNativeDataArray_TisSpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_mF62707A86DE10260EC8D744C6B3F2AC75D8EEED1(__this, 1, SpriteShapeRenderer_GetNativeDataArray_TisSpriteShapeSegment_tB32CE039E823A27997165CD087F6DE1906C4C8D5_mF62707A86DE10260EC8D744C6B3F2AC75D8EEED1_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 77));
		V_0 = L_1;
		goto IL_0013;
	}

IL_0013:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 78));
		NativeArray_1_t2BC742D9A03AF608A0AB5B638F49639E1C7BD6ED L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetChannels_mBFF908DA30D2D2A9650F917211D83F6A5795D755 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataSize, NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* ___1_indices, NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* ___2_vertices, NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* ___3_texcoords, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannels_mBFF908DA30D2D2A9650F917211D83F6A5795D755_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_dataSize), (&___1_indices), (&___2_vertices), (&___3_texcoords));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetChannels_mBFF908DA30D2D2A9650F917211D83F6A5795D755_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 79));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 80));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 81));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 82));
		int32_t L_0 = ___0_dataSize;
		int32_t L_1 = ___0_dataSize;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 83));
		SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6(__this, L_0, L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 83));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 84));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* L_2 = ___1_indices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 85));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934 L_3;
		L_3 = SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C(__this, 0, SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 85));
		*(NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934*)L_2 = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 86));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* L_4 = ___2_vertices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 87));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A L_5;
		L_5 = SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A(__this, 3, 0, SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 87));
		*(NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A*)L_4 = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 88));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* L_6 = ___3_texcoords;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 89));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF L_7;
		L_7 = SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375(__this, 4, 4, SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 89));
		*(NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF*)L_6 = L_7;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 90));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetChannels_m481C5B4F534E8037FFA0B856BCD371DAB112C3CF (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataSize, NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* ___1_indices, NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* ___2_vertices, NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* ___3_texcoords, NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F* ___4_colors, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannels_m481C5B4F534E8037FFA0B856BCD371DAB112C3CF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_dataSize), (&___1_indices), (&___2_vertices), (&___3_texcoords), (&___4_colors));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetChannels_m481C5B4F534E8037FFA0B856BCD371DAB112C3CF_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 91));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 92));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 93));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 94));
		int32_t L_0 = ___0_dataSize;
		int32_t L_1 = ___0_dataSize;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 95));
		SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF(__this, L_0, L_1, 8, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 95));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 96));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* L_2 = ___1_indices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 97));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934 L_3;
		L_3 = SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C(__this, 0, SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 97));
		*(NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934*)L_2 = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 98));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* L_4 = ___2_vertices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 99));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A L_5;
		L_5 = SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A(__this, 3, 0, SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 99));
		*(NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A*)L_4 = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 100));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* L_6 = ___3_texcoords;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 101));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF L_7;
		L_7 = SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375(__this, 4, 4, SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 101));
		*(NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF*)L_6 = L_7;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 102));
		NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F* L_8 = ___4_colors;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 103));
		NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F L_9;
		L_9 = SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85(__this, 7, 3, SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 103));
		*(NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F*)L_8 = L_9;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 104));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetChannels_m08BD580C754823A2578D887BEEF70AA43019DB8F (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataSize, NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* ___1_indices, NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* ___2_vertices, NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* ___3_texcoords, NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F* ___4_tangents, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannels_m08BD580C754823A2578D887BEEF70AA43019DB8F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_dataSize), (&___1_indices), (&___2_vertices), (&___3_texcoords), (&___4_tangents));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetChannels_m08BD580C754823A2578D887BEEF70AA43019DB8F_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 105));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 106));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 107));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 108));
		int32_t L_0 = ___0_dataSize;
		int32_t L_1 = ___0_dataSize;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 109));
		SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF(__this, L_0, L_1, 4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 109));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 110));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* L_2 = ___1_indices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 111));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934 L_3;
		L_3 = SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C(__this, 0, SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 111));
		*(NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934*)L_2 = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 112));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* L_4 = ___2_vertices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 113));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A L_5;
		L_5 = SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A(__this, 3, 0, SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 113));
		*(NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A*)L_4 = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 114));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* L_6 = ___3_texcoords;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 115));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF L_7;
		L_7 = SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375(__this, 4, 4, SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 115));
		*(NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF*)L_6 = L_7;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 116));
		NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F* L_8 = ___4_tangents;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 117));
		NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F L_9;
		L_9 = SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6(__this, 6, 2, SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 117));
		*(NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F*)L_8 = L_9;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 118));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetChannels_mB103260A403E60CB28AFFE799447DE075A114FC6 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataSize, NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* ___1_indices, NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* ___2_vertices, NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* ___3_texcoords, NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F* ___4_colors, NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F* ___5_tangents, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannels_mB103260A403E60CB28AFFE799447DE075A114FC6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_dataSize), (&___1_indices), (&___2_vertices), (&___3_texcoords), (&___4_colors), (&___5_tangents));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetChannels_mB103260A403E60CB28AFFE799447DE075A114FC6_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 119));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 120));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 121));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 122));
		int32_t L_0 = ___0_dataSize;
		int32_t L_1 = ___0_dataSize;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 123));
		SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF(__this, L_0, L_1, ((int32_t)12), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 123));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 124));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* L_2 = ___1_indices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 125));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934 L_3;
		L_3 = SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C(__this, 0, SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 125));
		*(NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934*)L_2 = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 126));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* L_4 = ___2_vertices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 127));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A L_5;
		L_5 = SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A(__this, 3, 0, SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 127));
		*(NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A*)L_4 = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 128));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* L_6 = ___3_texcoords;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 129));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF L_7;
		L_7 = SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375(__this, 4, 4, SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 129));
		*(NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF*)L_6 = L_7;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 130));
		NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F* L_8 = ___4_colors;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 131));
		NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F L_9;
		L_9 = SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85(__this, 7, 3, SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 131));
		*(NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F*)L_8 = L_9;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 132));
		NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F* L_10 = ___5_tangents;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 133));
		NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F L_11;
		L_11 = SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6(__this, 6, 2, SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 133));
		*(NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F*)L_10 = L_11;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 134));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetChannels_mB9F0096C5B4DCCB7BA9A9DAFA085B5400E0B33E2 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataSize, NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* ___1_indices, NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* ___2_vertices, NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* ___3_texcoords, NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F* ___4_tangents, NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* ___5_normals, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannels_mB9F0096C5B4DCCB7BA9A9DAFA085B5400E0B33E2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_dataSize), (&___1_indices), (&___2_vertices), (&___3_texcoords), (&___4_tangents), (&___5_normals));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetChannels_mB9F0096C5B4DCCB7BA9A9DAFA085B5400E0B33E2_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 135));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 136));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 137));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 138));
		int32_t L_0 = ___0_dataSize;
		int32_t L_1 = ___0_dataSize;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 139));
		SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF(__this, L_0, L_1, 6, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 139));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 140));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* L_2 = ___1_indices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 141));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934 L_3;
		L_3 = SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C(__this, 0, SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 141));
		*(NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934*)L_2 = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 142));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* L_4 = ___2_vertices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 143));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A L_5;
		L_5 = SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A(__this, 3, 0, SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 143));
		*(NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A*)L_4 = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 144));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* L_6 = ___3_texcoords;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 145));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF L_7;
		L_7 = SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375(__this, 4, 4, SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 145));
		*(NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF*)L_6 = L_7;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 146));
		NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F* L_8 = ___4_tangents;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 147));
		NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F L_9;
		L_9 = SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6(__this, 6, 2, SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 147));
		*(NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F*)L_8 = L_9;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 148));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* L_10 = ___5_normals;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 149));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A L_11;
		L_11 = SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A(__this, 5, 1, SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 149));
		*(NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A*)L_10 = L_11;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 150));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetChannels_m2F1DEBB233238E415AFF4B82BA7D1B3C4A9FC85C (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_dataSize, NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* ___1_indices, NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* ___2_vertices, NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* ___3_texcoords, NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F* ___4_colors, NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F* ___5_tangents, NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* ___6_normals, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetChannels_m2F1DEBB233238E415AFF4B82BA7D1B3C4A9FC85C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_dataSize), (&___1_indices), (&___2_vertices), (&___3_texcoords), (&___4_colors), (&___5_tangents), (&___6_normals));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer_GetChannels_m2F1DEBB233238E415AFF4B82BA7D1B3C4A9FC85C_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 151));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 152));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 153));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 154));
		int32_t L_0 = ___0_dataSize;
		int32_t L_1 = ___0_dataSize;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 155));
		SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF(__this, L_0, L_1, ((int32_t)14), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 155));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 156));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934* L_2 = ___1_indices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 157));
		NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934 L_3;
		L_3 = SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C(__this, 0, SpriteShapeRenderer_GetNativeDataArray_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mA713E8BC67B0B90AD897353ADE6CF271E43C4C0C_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 157));
		*(NativeArray_1_t275C00CC374DEA66C69B3BB3992116F315A8E934*)L_2 = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 158));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* L_4 = ___2_vertices;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 159));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A L_5;
		L_5 = SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A(__this, 3, 0, SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 159));
		*(NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A*)L_4 = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 160));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF* L_6 = ___3_texcoords;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 161));
		NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF L_7;
		L_7 = SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375(__this, 4, 4, SpriteShapeRenderer_GetChannelDataArray_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m985874D8CD9F2711E49E0DF155BC507338930375_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 161));
		*(NativeSlice_1_tBDD0B6C963222DE23BD34911416213E058F9FBAF*)L_6 = L_7;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 162));
		NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F* L_8 = ___4_colors;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 163));
		NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F L_9;
		L_9 = SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85(__this, 7, 3, SpriteShapeRenderer_GetChannelDataArray_TisColor32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_m213817FB773A87FFDF5D2FA604B15E052C5AFD85_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 163));
		*(NativeSlice_1_t161790ED7691CA594B3A5F9F699BACB89FF5328F*)L_8 = L_9;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 164));
		NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F* L_10 = ___5_tangents;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 165));
		NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F L_11;
		L_11 = SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6(__this, 6, 2, SpriteShapeRenderer_GetChannelDataArray_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m7BE92EECFDEB833E64904EB94C92985BC37832C6_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 165));
		*(NativeSlice_1_tA687F314957178F2A299D03D59B960DDC218680F*)L_10 = L_11;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 166));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A* L_12 = ___6_normals;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 167));
		NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A L_13;
		L_13 = SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A(__this, 5, 1, SpriteShapeRenderer_GetChannelDataArray_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_mBEF4590ABBB66C0A2FE21F8567D98D1A8770401A_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 167));
		*(NativeSlice_1_t4B5C42A704ED060AB92A8716135FE435B1E6C23A*)L_12 = L_13;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_SpriteShapeModule + 168));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer__ctor_m44BF2C8BE32D32910B2E162D0830BFC36694D6A6 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeRenderer__ctor_m44BF2C8BE32D32910B2E162D0830BFC36694D6A6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeRenderer__ctor_m44BF2C8BE32D32910B2E162D0830BFC36694D6A6_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Renderer__ctor_m8B4EE9696B155A1B0A2CF13EBFC363CE175B9271(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_get_color_Injected_m5DF8CA684904BB1D9EB6A1D8C51050C33AE837E3 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_get_color_Injected_m5DF8CA684904BB1D9EB6A1D8C51050C33AE837E3_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*);
	static SpriteShapeRenderer_get_color_Injected_m5DF8CA684904BB1D9EB6A1D8C51050C33AE837E3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_get_color_Injected_m5DF8CA684904BB1D9EB6A1D8C51050C33AE837E3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::get_color_Injected(UnityEngine.Color&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_set_color_Injected_m3738681F39E29CDC45E204CAFE8B417D8B7969CF (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_set_color_Injected_m3738681F39E29CDC45E204CAFE8B417D8B7969CF_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*);
	static SpriteShapeRenderer_set_color_Injected_m3738681F39E29CDC45E204CAFE8B417D8B7969CF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_set_color_Injected_m3738681F39E29CDC45E204CAFE8B417D8B7969CF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::set_color_Injected(UnityEngine.Color&)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_Prepare_Injected_mD1195648DC363033B06C211A6F5A00F30C8B0F7D (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08* ___0_handle, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45* ___1_shapeParams, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___2_sprites, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_Prepare_Injected_mD1195648DC363033B06C211A6F5A00F30C8B0F7D_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08*, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45*, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B*);
	static SpriteShapeRenderer_Prepare_Injected_mD1195648DC363033B06C211A6F5A00F30C8B0F7D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_Prepare_Injected_mD1195648DC363033B06C211A6F5A00F30C8B0F7D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::Prepare_Injected(Unity.Jobs.JobHandle&,UnityEngine.U2D.SpriteShapeParameters&,UnityEngine.Sprite[])");
	_il2cpp_icall_func(__this, ___0_handle, ___1_shapeParams, ___2_sprites);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetDataInfo_Injected_m9F93C146CC8377F6C657038271C48CBD8E2E784C (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_arrayType, SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_GetDataInfo_Injected_m9F93C146CC8377F6C657038271C48CBD8E2E784C_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA*);
	static SpriteShapeRenderer_GetDataInfo_Injected_m9F93C146CC8377F6C657038271C48CBD8E2E784C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_GetDataInfo_Injected_m9F93C146CC8377F6C657038271C48CBD8E2E784C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::GetDataInfo_Injected(UnityEngine.U2D.SpriteShapeDataType,UnityEngine.U2D.SpriteChannelInfo&)");
	_il2cpp_icall_func(__this, ___0_arrayType, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_GetChannelInfo_Injected_mABC1866E231BC32890EC4CE7768652F2DFA4913D (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, int32_t ___0_channel, SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_GetChannelInfo_Injected_mABC1866E231BC32890EC4CE7768652F2DFA4913D_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, int32_t, SpriteChannelInfo_t059F8D7ED52326BD2136B9AC41287F3E82FDE4CA*);
	static SpriteShapeRenderer_GetChannelInfo_Injected_mABC1866E231BC32890EC4CE7768652F2DFA4913D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_GetChannelInfo_Injected_mABC1866E231BC32890EC4CE7768652F2DFA4913D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::GetChannelInfo_Injected(UnityEngine.Rendering.VertexAttribute,UnityEngine.U2D.SpriteChannelInfo&)");
	_il2cpp_icall_func(__this, ___0_channel, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeRenderer_SetLocalAABB_Injected_m1539FADCD075EFA3FA8BFE925FC503192847F105 (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* __this, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3* ___0_bounds, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeRenderer_SetLocalAABB_Injected_m1539FADCD075EFA3FA8BFE925FC503192847F105_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3*);
	static SpriteShapeRenderer_SetLocalAABB_Injected_m1539FADCD075EFA3FA8BFE925FC503192847F105_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeRenderer_SetLocalAABB_Injected_m1539FADCD075EFA3FA8BFE925FC503192847F105_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeRenderer::SetLocalAABB_Injected(UnityEngine.Bounds&)");
	_il2cpp_icall_func(__this, ___0_bounds);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshal_pinvoke(const SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501& unmarshaled, SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshaled_pinvoke& marshaled)
{
	marshaled.___height = unmarshaled.___height;
	marshaled.___bevelCutoff = unmarshaled.___bevelCutoff;
	marshaled.___bevelSize = unmarshaled.___bevelSize;
	marshaled.___spriteIndex = unmarshaled.___spriteIndex;
	marshaled.___corner = static_cast<int32_t>(unmarshaled.___corner);
}
IL2CPP_EXTERN_C void SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshal_pinvoke_back(const SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshaled_pinvoke& marshaled, SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501& unmarshaled)
{
	float unmarshaledheight_temp_0 = 0.0f;
	unmarshaledheight_temp_0 = marshaled.___height;
	unmarshaled.___height = unmarshaledheight_temp_0;
	float unmarshaledbevelCutoff_temp_1 = 0.0f;
	unmarshaledbevelCutoff_temp_1 = marshaled.___bevelCutoff;
	unmarshaled.___bevelCutoff = unmarshaledbevelCutoff_temp_1;
	float unmarshaledbevelSize_temp_2 = 0.0f;
	unmarshaledbevelSize_temp_2 = marshaled.___bevelSize;
	unmarshaled.___bevelSize = unmarshaledbevelSize_temp_2;
	uint32_t unmarshaledspriteIndex_temp_3 = 0;
	unmarshaledspriteIndex_temp_3 = marshaled.___spriteIndex;
	unmarshaled.___spriteIndex = unmarshaledspriteIndex_temp_3;
	bool unmarshaledcorner_temp_4 = false;
	unmarshaledcorner_temp_4 = static_cast<bool>(marshaled.___corner);
	unmarshaled.___corner = unmarshaledcorner_temp_4;
}
IL2CPP_EXTERN_C void SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshal_pinvoke_cleanup(SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshal_com(const SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501& unmarshaled, SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshaled_com& marshaled)
{
	marshaled.___height = unmarshaled.___height;
	marshaled.___bevelCutoff = unmarshaled.___bevelCutoff;
	marshaled.___bevelSize = unmarshaled.___bevelSize;
	marshaled.___spriteIndex = unmarshaled.___spriteIndex;
	marshaled.___corner = static_cast<int32_t>(unmarshaled.___corner);
}
IL2CPP_EXTERN_C void SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshal_com_back(const SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshaled_com& marshaled, SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501& unmarshaled)
{
	float unmarshaledheight_temp_0 = 0.0f;
	unmarshaledheight_temp_0 = marshaled.___height;
	unmarshaled.___height = unmarshaledheight_temp_0;
	float unmarshaledbevelCutoff_temp_1 = 0.0f;
	unmarshaledbevelCutoff_temp_1 = marshaled.___bevelCutoff;
	unmarshaled.___bevelCutoff = unmarshaledbevelCutoff_temp_1;
	float unmarshaledbevelSize_temp_2 = 0.0f;
	unmarshaledbevelSize_temp_2 = marshaled.___bevelSize;
	unmarshaled.___bevelSize = unmarshaledbevelSize_temp_2;
	uint32_t unmarshaledspriteIndex_temp_3 = 0;
	unmarshaledspriteIndex_temp_3 = marshaled.___spriteIndex;
	unmarshaled.___spriteIndex = unmarshaledspriteIndex_temp_3;
	bool unmarshaledcorner_temp_4 = false;
	unmarshaledcorner_temp_4 = static_cast<bool>(marshaled.___corner);
	unmarshaled.___corner = unmarshaledcorner_temp_4;
}
IL2CPP_EXTERN_C void SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshal_com_cleanup(SpriteShapeMetaData_t4BE8536E08C9D310F1CD53928F802D0B9439A501_marshaled_com& marshaled)
{
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshal_pinvoke(const AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB& unmarshaled, AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshaled_pinvoke& marshaled)
{
	marshaled.___start = unmarshaled.___start;
	marshaled.___end = unmarshaled.___end;
	marshaled.___order = unmarshaled.___order;
	marshaled.___sprites = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I4, unmarshaled.___sprites);
}
IL2CPP_EXTERN_C void AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshal_pinvoke_back(const AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshaled_pinvoke& marshaled, AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float unmarshaledstart_temp_0 = 0.0f;
	unmarshaledstart_temp_0 = marshaled.___start;
	unmarshaled.___start = unmarshaledstart_temp_0;
	float unmarshaledend_temp_1 = 0.0f;
	unmarshaledend_temp_1 = marshaled.___end;
	unmarshaled.___end = unmarshaledend_temp_1;
	uint32_t unmarshaledorder_temp_2 = 0;
	unmarshaledorder_temp_2 = marshaled.___order;
	unmarshaled.___order = unmarshaledorder_temp_2;
	unmarshaled.___sprites = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I4, Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var, marshaled.___sprites);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___sprites), (void*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I4, Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var, marshaled.___sprites));
}
IL2CPP_EXTERN_C void AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshal_pinvoke_cleanup(AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___sprites);
	marshaled.___sprites = NULL;
}
IL2CPP_EXTERN_C void AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshal_com(const AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB& unmarshaled, AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshaled_com& marshaled)
{
	marshaled.___start = unmarshaled.___start;
	marshaled.___end = unmarshaled.___end;
	marshaled.___order = unmarshaled.___order;
	marshaled.___sprites = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I4, unmarshaled.___sprites);
}
IL2CPP_EXTERN_C void AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshal_com_back(const AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshaled_com& marshaled, AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float unmarshaledstart_temp_0 = 0.0f;
	unmarshaledstart_temp_0 = marshaled.___start;
	unmarshaled.___start = unmarshaledstart_temp_0;
	float unmarshaledend_temp_1 = 0.0f;
	unmarshaledend_temp_1 = marshaled.___end;
	unmarshaled.___end = unmarshaledend_temp_1;
	uint32_t unmarshaledorder_temp_2 = 0;
	unmarshaledorder_temp_2 = marshaled.___order;
	unmarshaled.___order = unmarshaledorder_temp_2;
	unmarshaled.___sprites = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I4, Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var, marshaled.___sprites);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___sprites), (void*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I4, Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var, marshaled.___sprites));
}
IL2CPP_EXTERN_C void AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshal_com_cleanup(AngleRangeInfo_t54B4C94C605EABEC2D401C612F1D8CCB42985DBB_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___sprites);
	marshaled.___sprites = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* SpriteShapeUtility_Generate_m37169E418B14B90AE2B0D7E2CA35C7B666B55016 (Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___0_mesh, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45 ___1_shapeParams, ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4* ___2_points, SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED* ___3_metaData, AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8* ___4_angleRange, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___5_sprites, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___6_corners, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeUtility_Generate_m37169E418B14B90AE2B0D7E2CA35C7B666B55016_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeUtility_Generate_m37169E418B14B90AE2B0D7E2CA35C7B666B55016_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* L_0 = ___0_mesh;
		ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4* L_1 = ___2_points;
		SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED* L_2 = ___3_metaData;
		AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8* L_3 = ___4_angleRange;
		SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* L_4 = ___5_sprites;
		SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* L_5 = ___6_corners;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6;
		L_6 = SpriteShapeUtility_Generate_Injected_m7EDCAE2EEE61FA62B4DC05DE3B42C2E7FA7CFBA4(L_0, (&___1_shapeParams), L_1, L_2, L_3, L_4, L_5, NULL);
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeUtility_GenerateSpriteShape_mB19EF6B7F3B223E8E10CD85EFA808D4A7D6330AF (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* ___0_renderer, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45 ___1_shapeParams, ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4* ___2_points, SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED* ___3_metaData, AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8* ___4_angleRange, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___5_sprites, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___6_corners, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeUtility_GenerateSpriteShape_mB19EF6B7F3B223E8E10CD85EFA808D4A7D6330AF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeUtility_GenerateSpriteShape_mB19EF6B7F3B223E8E10CD85EFA808D4A7D6330AF_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* L_0 = ___0_renderer;
		ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4* L_1 = ___2_points;
		SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED* L_2 = ___3_metaData;
		AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8* L_3 = ___4_angleRange;
		SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* L_4 = ___5_sprites;
		SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* L_5 = ___6_corners;
		SpriteShapeUtility_GenerateSpriteShape_Injected_m29F14BC6698B8BEF672D4B734B9BBE08E481F5AB(L_0, (&___1_shapeParams), L_1, L_2, L_3, L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeUtility__ctor_m15FD3D11661152CFCE8FEE92BEBA837B55238426 (SpriteShapeUtility_t9344C0D12FC3B3D4E280BE0CB6F7398B14335874* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteShapeUtility__ctor_m15FD3D11661152CFCE8FEE92BEBA837B55238426_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteShapeUtility__ctor_m15FD3D11661152CFCE8FEE92BEBA837B55238426_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* SpriteShapeUtility_Generate_Injected_m7EDCAE2EEE61FA62B4DC05DE3B42C2E7FA7CFBA4 (Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___0_mesh, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45* ___1_shapeParams, ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4* ___2_points, SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED* ___3_metaData, AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8* ___4_angleRange, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___5_sprites, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___6_corners, const RuntimeMethod* method) 
{
	typedef Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* (*SpriteShapeUtility_Generate_Injected_m7EDCAE2EEE61FA62B4DC05DE3B42C2E7FA7CFBA4_ftn) (Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4*, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45*, ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4*, SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED*, AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8*, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B*, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B*);
	static SpriteShapeUtility_Generate_Injected_m7EDCAE2EEE61FA62B4DC05DE3B42C2E7FA7CFBA4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeUtility_Generate_Injected_m7EDCAE2EEE61FA62B4DC05DE3B42C2E7FA7CFBA4_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeUtility::Generate_Injected(UnityEngine.Mesh,UnityEngine.U2D.SpriteShapeParameters&,UnityEngine.U2D.ShapeControlPoint[],UnityEngine.U2D.SpriteShapeMetaData[],UnityEngine.U2D.AngleRangeInfo[],UnityEngine.Sprite[],UnityEngine.Sprite[])");
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* icallRetVal = _il2cpp_icall_func(___0_mesh, ___1_shapeParams, ___2_points, ___3_metaData, ___4_angleRange, ___5_sprites, ___6_corners);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteShapeUtility_GenerateSpriteShape_Injected_m29F14BC6698B8BEF672D4B734B9BBE08E481F5AB (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC* ___0_renderer, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45* ___1_shapeParams, ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4* ___2_points, SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED* ___3_metaData, AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8* ___4_angleRange, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___5_sprites, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B* ___6_corners, const RuntimeMethod* method) 
{
	typedef void (*SpriteShapeUtility_GenerateSpriteShape_Injected_m29F14BC6698B8BEF672D4B734B9BBE08E481F5AB_ftn) (SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC*, SpriteShapeParameters_tC047BDC50B45EE3C0035646195EFA31FB89F2E45*, ShapeControlPointU5BU5D_tBFC215BA5B9E35EFC7749E6BC90BB6815AB138F4*, SpriteShapeMetaDataU5BU5D_tA834F58454A03F0F3870A1924C37E09A6D2E47ED*, AngleRangeInfoU5BU5D_t8AE5F89B8CA102A1093EFEA4E67B9364AC690BC8*, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B*, SpriteU5BU5D_tCEE379E10CAD9DBFA770B331480592548ED0EA1B*);
	static SpriteShapeUtility_GenerateSpriteShape_Injected_m29F14BC6698B8BEF672D4B734B9BBE08E481F5AB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteShapeUtility_GenerateSpriteShape_Injected_m29F14BC6698B8BEF672D4B734B9BBE08E481F5AB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.U2D.SpriteShapeUtility::GenerateSpriteShape_Injected(UnityEngine.U2D.SpriteShapeRenderer,UnityEngine.U2D.SpriteShapeParameters&,UnityEngine.U2D.ShapeControlPoint[],UnityEngine.U2D.SpriteShapeMetaData[],UnityEngine.U2D.AngleRangeInfo[],UnityEngine.Sprite[],UnityEngine.Sprite[])");
	_il2cpp_icall_func(___0_renderer, ___1_shapeParams, ___2_points, ___3_metaData, ___4_angleRange, ___5_sprites, ___6_corners);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
