﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void PoseData_get_rotation_m6A15A78CF66E4D2F9E2EE9FE88BC8D89A65EB904 (void);
extern void PoseData_get_position_mEE4843D61931F8AB21EE810CD308D74D725F2008 (void);
extern void TangoInputTracking_Internal_TryGetPoseAtTime_m0AD46B35FDD35A1F12D069BC6D5F2EC33D403A54 (void);
extern void TangoInputTracking_TryGetPoseAtTime_m5BCF0745A286DFC34FB215B24CDD0A2E76073CA6 (void);
static Il2CppMethodPointer s_methodPointers[4] = 
{
	PoseData_get_rotation_m6A15A78CF66E4D2F9E2EE9FE88BC8D89A65EB904,
	PoseData_get_position_mEE4843D61931F8AB21EE810CD308D74D725F2008,
	TangoInputTracking_Internal_TryGetPoseAtTime_m0AD46B35FDD35A1F12D069BC6D5F2EC33D403A54,
	TangoInputTracking_TryGetPoseAtTime_m5BCF0745A286DFC34FB215B24CDD0A2E76073CA6,
};
extern void PoseData_get_rotation_m6A15A78CF66E4D2F9E2EE9FE88BC8D89A65EB904_AdjustorThunk (void);
extern void PoseData_get_position_mEE4843D61931F8AB21EE810CD308D74D725F2008_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[2] = 
{
	{ 0x06000001, PoseData_get_rotation_m6A15A78CF66E4D2F9E2EE9FE88BC8D89A65EB904_AdjustorThunk },
	{ 0x06000002, PoseData_get_position_mEE4843D61931F8AB21EE810CD308D74D725F2008_AdjustorThunk },
};
static const int32_t s_InvokerIndices[4] = 
{
	4267,
	4358,
	8200,
	8200,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ARModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_ARModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_ARModule_CodeGenModule = 
{
	"UnityEngine.ARModule.dll",
	4,
	s_methodPointers,
	2,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_ARModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
