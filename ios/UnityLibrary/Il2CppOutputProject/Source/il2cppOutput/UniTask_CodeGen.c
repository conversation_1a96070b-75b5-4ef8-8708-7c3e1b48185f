﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AsyncMethodBuilderAttribute__ctor_m362B0044366C29B2E6E2626F1FB61D9CF443111C (void);
extern void AsyncUnit_GetHashCode_mEA99722071116F872F4FD9397FCE42319B9C3407 (void);
extern void AsyncUnit_Equals_m4FA66A2168F60FBACE2254A0D6F1513BC8111389 (void);
extern void AsyncUnit_ToString_m41E4FD5E5F3F3FCBC1C00EB15CDFA98EC9CDE3DF (void);
extern void AsyncUnit__cctor_mEA0725B246A2EBC4E49EABD1D296D90295019593 (void);
extern void CancellationTokenExtensions_Callback_m5DB0ABF0185CBBA5D111D208800188E29C17F22A (void);
extern void CancellationTokenExtensions_RegisterWithoutCaptureExecutionContext_mA5A09F053F6E50AD047E0DB08666E4270863AC69 (void);
extern void CancellationTokenExtensions_DisposeCallback_mE3FAA9A576B2269AA49E437E9D1E4655634E65EF (void);
extern void CancellationTokenExtensions__cctor_m0A79502349C53CA61E0A731D51B22BA93FB57292 (void);
extern void IUniTaskSource_System_Threading_Tasks_Sources_IValueTaskSource_GetStatus_mC63A1B0702E6297684613D702BDF4BAB87191E8E (void);
extern void IUniTaskSource_System_Threading_Tasks_Sources_IValueTaskSource_GetResult_m15B83960847D64A05AF0445C43CE6A81006CB9F0 (void);
extern void IUniTaskSource_System_Threading_Tasks_Sources_IValueTaskSource_OnCompleted_m57AEB63BFA408747FDE3415104C2EC542DF832B0 (void);
extern void UniTaskStatusExtensions_IsCompleted_mF43C41C9CEB640E381D1F7A8B40142843AED87AE (void);
extern void MoveNextSource_GetResult_mFCD01A5925FA4577700231792E6394FBE1C32EE6 (void);
extern void MoveNextSource_GetStatus_m460FE5EF11578BF85CF6A857D3197049FAD4DF47 (void);
extern void MoveNextSource_OnCompleted_mA6A8C6E7CB4D8BDFC06A30DA370D4DB88F41F9F2 (void);
extern void MoveNextSource_UnsafeGetStatus_mC553AFFB2B851CB0C8EF12D1168F4D28CD40856F (void);
extern void MoveNextSource_Cysharp_Threading_Tasks_IUniTaskSource_GetResult_m086008DCD8B258C9616511263B0E6FC57AD766E8 (void);
extern void MoveNextSource__ctor_mAD2FB060DAA5FB4AC333260F502B4FD563133640 (void);
extern void PlayerLoopHelper_get_UnitySynchronizationContext_mBE814607A95340320B3074E622431CEFC29765EE (void);
extern void PlayerLoopHelper_get_MainThreadId_m187CD1091ABFC2F5638C9FD630F89364D9BE8A1D (void);
extern void PlayerLoopHelper_get_IsMainThread_mC32C7D62B20488CD0BA2F337EBFEC8128086DD5A (void);
extern void PlayerLoopHelper_InsertRunner_m8855844A2F4F8B434E2ACE94CDFC53F7F7D6F0B2 (void);
extern void PlayerLoopHelper_RemoveRunner_m8B2228E19BA6B7DEEDF70A3A10EF8B475489B3E7 (void);
extern void PlayerLoopHelper_InsertUniTaskSynchronizationContext_mF47A1A4A57B9D526D4138B6D125DCB71A7FA3ECC (void);
extern void PlayerLoopHelper_Init_m4CF7695E03702F0AED776686D060A45D5F09386B (void);
extern void PlayerLoopHelper_FindLoopSystemIndex_mFC2B12111D667F039EAA9795EC34E2F73815A52C (void);
extern void PlayerLoopHelper_InsertLoop_m0A3AD5C2A5DCF56368E328838B001156ECBC5EAD (void);
extern void PlayerLoopHelper_Initialize_m68A88284085A80341C8083E9131443DB8416B3B0 (void);
extern void PlayerLoopHelper_AddAction_mAD5D5DA039BE42ECF032A1835BD95C318D6ED22D (void);
extern void PlayerLoopHelper_ThrowInvalidLoopTiming_m0B0B6B47071024DA6FE9DE246EA177C1DA34EBDB (void);
extern void PlayerLoopHelper_AddContinuation_m5B8F03E45A820D91B17CFA56AA93E7444BEC17E0 (void);
extern void PlayerLoopHelper__cctor_mFFCB195045BCACFAEE25BB6877D59E792278F265 (void);
extern void U3CU3Ec__cctor_mD96B9FBDAA89B459C19B3D7908FAA783D962110D (void);
extern void U3CU3Ec__ctor_m7B9FF6BEE6BE46908D89E71C6B4F5AD5A2C866E3 (void);
extern void U3CU3Ec_U3CInsertUniTaskSynchronizationContextU3Eb__21_0_m8EF75464EEE2DD9BFC71B913EC7969CC18D6F03E (void);
extern void U3CU3Ec_U3CInsertUniTaskSynchronizationContextU3Eb__21_1_m78CF9CAEE453AFC76EF7B62208F56F07C87A3F2F (void);
extern void U3CU3Ec_U3CInsertUniTaskSynchronizationContextU3Eb__21_2_mF2DF172BE816640569D6A246A04A4697DA9869C4 (void);
extern void U3CU3Ec__DisplayClass20_0__ctor_mB403A5464295A5CBC610678A8252E2EE95D894B1 (void);
extern void U3CU3Ec__DisplayClass20_0_U3CRemoveRunnerU3Eb__0_m2E763B97ED0D87CE029D0D0D452340D7DD144248 (void);
extern void TaskPool__cctor_m11E9B67729C2AB10470F79030676B1C32F16B88F (void);
extern void TaskPool_RegisterSizeGetter_m9CB7F337911201F7B7012F0CA8B35D697D8AC9F6 (void);
extern void UniTask__ctor_m5CBDA4598A89633256BF98AB40B3BA549A4CF84F (void);
extern void UniTask_get_Status_mA15B0F13DE3CE36730357CF50F65AE99ADF564DA (void);
extern void UniTask_GetAwaiter_mF05A09B81913BECFD58FC67A16C0251FFCCAC939 (void);
extern void UniTask_ToString_m40AB733888F35A7079CDF8B5399A7DD5AC4AC214 (void);
extern void UniTask_Yield_m3362988BC281D8FBE6DE9D42C3123214920954DE (void);
extern void UniTask_NextFrame_mEB306E0E08DD5AFB4841953D92825A43C80B95F4 (void);
extern void UniTask_DelayFrame_m8D556B751B8A1CF275BDADBDB4B2C7839657FBB7 (void);
extern void UniTask_Delay_m0F6B9A8CE95AB6C59ACCCF49E96369AB9FAD4960 (void);
extern void UniTask_Delay_m446244C96F6C971AA44C9F458176F25FCA708F2F (void);
extern void UniTask_Delay_mDBE9F54246D1E057EF8528601E2A86E451FB4462 (void);
extern void UniTask_FromException_mC6AC508C727D591C6BE908F2014570963CE9E5EE (void);
extern void UniTask_FromCanceled_mCB25269CC2BD34F255BC5DC67D83C09140C49458 (void);
extern void UniTask_Create_mEF4619EAAEFE6AC75B95375CA558D14CB0FBDE3B (void);
extern void UniTask_Void_m75F7036399F769EA171E65F9635A74220B7E7B29 (void);
extern void UniTask_Void_m1BD846B7FC6BD19E7BE013D12B8A51237B5C2757 (void);
extern void UniTask_RunOnThreadPool_m95F4F71334B98E9F18743AAEC7B32342FF7E460D (void);
extern void UniTask_SwitchToMainThread_mBC162C95F0ED605F8E5416A60CCA153E0993CBF1 (void);
extern void UniTask_SwitchToThreadPool_m4A79CBAD5A44C5037CD0A366CA801875F5206010 (void);
extern void UniTask_WaitUntil_m3CC6559258C98FE64A372B1AE9B7CAEA077D7795 (void);
extern void UniTask_WhenAll_m9C68DE3D712A9EA42B6711E0D28EE324056137E1 (void);
extern void UniTask__cctor_m17B980F936F60071A3DD10AD8366298D2886E62A (void);
extern void Awaiter__ctor_m4154A3A6D62BB1657D17A8106633CD9E1CE51F04 (void);
extern void Awaiter_get_IsCompleted_m2D01E3AB8A7C5AA8AA1E1EF58D92A6A095C00B9A (void);
extern void Awaiter_GetResult_mC439993563D3BD49CEC67AAF6AFB3AEF72E916BD (void);
extern void Awaiter_UnsafeOnCompleted_m6C4775F8F4F2BEFB8118EBBA1EA621440CE84D62 (void);
extern void Awaiter_SourceOnCompleted_m7A5A045E2ED6F2B0729D11CAC4E18F2BC5B758EB (void);
extern void NextFramePromise_get_NextNode_mEBE190751ED87E6ED0EDFF69F341E968AA24DB78 (void);
extern void NextFramePromise__cctor_mC59005174F368748DA23910F8F585FD33DC09DD3 (void);
extern void NextFramePromise__ctor_m1A527C69C686250659A816FC4805494B45A451EC (void);
extern void NextFramePromise_Create_m33567EC8A25A6FAF09B870BD5086595F57537094 (void);
extern void NextFramePromise_GetResult_mE381CAAF08DB307E632D789ABB32C9F741840B98 (void);
extern void NextFramePromise_GetStatus_mC17DE65A641EC520FDD1D794434D7721F11FF046 (void);
extern void NextFramePromise_UnsafeGetStatus_mB83C781946E1D779D64CEA80E57B5F0949FEB371 (void);
extern void NextFramePromise_OnCompleted_m9F4A6D33004DB8626614F48665425A8C20693225 (void);
extern void NextFramePromise_MoveNext_m18BC7278D7DA0160538B12DC8C2F719967D5BE5A (void);
extern void NextFramePromise_TryReturn_m9837FA0455BAC3CC26BBBFBAF6CE27A29401AB44 (void);
extern void U3CU3Ec__cctor_m0E4C35CBFCA70900634AD2C33811BAAF22C02A0D (void);
extern void U3CU3Ec__ctor_m293F2D26E7857D0E1954C5AE63C1944A0179F2CA (void);
extern void U3CU3Ec_U3C_cctorU3Eb__4_0_m5F46A495AE0371145399A0FAD5D06FBD542DDB2C (void);
extern void U3CU3Ec_U3CCreateU3Eb__11_0_mB8D6D0A000F028998510A3BEAAFA346F106AF97D (void);
extern void DelayFramePromise_get_NextNode_m5E39E63DFA2B973B50EBEE216FA14EDB2B12CD8B (void);
extern void DelayFramePromise__cctor_m370D1F2F63B63E002B9A0B3ECADF2388D682B26F (void);
extern void DelayFramePromise__ctor_m654FCF4355DEF52EA1D39E37B55E1456C6BAFE37 (void);
extern void DelayFramePromise_Create_m1EFF3370EB1B19B6C153C9E5F0FC82179A255D9E (void);
extern void DelayFramePromise_GetResult_m47A37A2092283E62B12E3759E6A4CB900C8E95E6 (void);
extern void DelayFramePromise_GetStatus_m9053C231AC019F2DDDDA4C8B313B35305C6700D2 (void);
extern void DelayFramePromise_UnsafeGetStatus_m1D921F426F932B27009E65877F4D62A75B9CD0D0 (void);
extern void DelayFramePromise_OnCompleted_m129580852FA8EA6401095482F31C6A8D0F5D1073 (void);
extern void DelayFramePromise_MoveNext_mD075C9B35C34B139ECA9E3C02B7F92DF21E50E1A (void);
extern void DelayFramePromise_TryReturn_m1AC8FB8392C2A95D8AF55EFB0BD1BCE07EF947FF (void);
extern void U3CU3Ec__cctor_mEBB3223756C9BB1FF1ED2B2DD4D705CB2E7AFB89 (void);
extern void U3CU3Ec__ctor_m6A7F789ECB7D4E3CACD77EE4B335EA08581D097A (void);
extern void U3CU3Ec_U3C_cctorU3Eb__4_0_mA2680085CAC8308E14A11913EBB5B4BD317F866C (void);
extern void U3CU3Ec_U3CCreateU3Eb__13_0_m921D671755F3675AFD35D98D512D2C79F693933C (void);
extern void DelayPromise_get_NextNode_m052F74D1010B856461FFE289BBCE397C23309424 (void);
extern void DelayPromise__cctor_mB615EC27C3FAB986A9170F6C16AD17090CEBD04E (void);
extern void DelayPromise__ctor_m4F76D103946E1BF73889E5176769AEC683C559B0 (void);
extern void DelayPromise_Create_m891B4E88F71B0718965855E39342115543A9C218 (void);
extern void DelayPromise_GetResult_m0E215D75987A62E367C1EC94A8D7646FF5422675 (void);
extern void DelayPromise_GetStatus_mCBF2239EB5E8A1B4D2F2F2A37329CCCA82B8A3C0 (void);
extern void DelayPromise_UnsafeGetStatus_mBCEED523B78BB889EEE752754011EA847B8CB4C4 (void);
extern void DelayPromise_OnCompleted_m9E6029B1E08DFC77527BE7977C5AE722D0A822C1 (void);
extern void DelayPromise_MoveNext_mE40C8EAC8E59FB421A260131A2211A32D18CA297 (void);
extern void DelayPromise_TryReturn_m5F9BFE6413F89D571880334FD38AE8751BE7D0F8 (void);
extern void U3CU3Ec__cctor_mB174830645DD5169EFB16D295F5F1C6BF46530D8 (void);
extern void U3CU3Ec__ctor_mE9D5F68457F1916EC15D9E6655A2DB2EF294FE84 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__4_0_m46F5B3B989A9FD3E3ACBF47368DD1745B1362394 (void);
extern void U3CU3Ec_U3CCreateU3Eb__13_0_m51A539073FF4FD5619A020951E05D41039954749 (void);
extern void DelayIgnoreTimeScalePromise_get_NextNode_m843C0B86113D4572E8A581D19E9A1E681E3378D5 (void);
extern void DelayIgnoreTimeScalePromise__cctor_m1C95CA9701159FE24C1393FCABD1BE0AF9A2B1A0 (void);
extern void DelayIgnoreTimeScalePromise__ctor_mA52BF17839EB6AA55F60B1372C8A97BE98807D3B (void);
extern void DelayIgnoreTimeScalePromise_Create_mF528197D8221A96419847E0647A31334E04FAA40 (void);
extern void DelayIgnoreTimeScalePromise_GetResult_mBBAB4F9DDEA466CD879B2FA3B11A52C15A8467E2 (void);
extern void DelayIgnoreTimeScalePromise_GetStatus_mF1A430774D5AB51EBBC84D6F86D9B6AFC0C5E3AF (void);
extern void DelayIgnoreTimeScalePromise_UnsafeGetStatus_m247A75DF579B04129E29E8A3C727290C9D5E696C (void);
extern void DelayIgnoreTimeScalePromise_OnCompleted_mB3D8642120A8AE7142FEA205F5207FE5EB89D878 (void);
extern void DelayIgnoreTimeScalePromise_MoveNext_m219A29F83B062357AE56861AE3450938775A821D (void);
extern void DelayIgnoreTimeScalePromise_TryReturn_mC21F9BE7A738D73E622BE6CB44AE1262811B295D (void);
extern void U3CU3Ec__cctor_mBDD2DA3503A97010FE47380FD165B1CD288136CB (void);
extern void U3CU3Ec__ctor_m03FD2027D47FFA1F391213F7884F7E46DFD8CA0C (void);
extern void U3CU3Ec_U3C_cctorU3Eb__4_0_mA9F8A6C82393773CC14D351BDD7A4CF1799B2A18 (void);
extern void U3CU3Ec_U3CCreateU3Eb__13_0_m1E462B91FD3F600F567F13481C47703F27B33806 (void);
extern void DelayRealtimePromise_get_NextNode_m930A50FBE32B638FE5C2E52F907A257E4AE8589D (void);
extern void DelayRealtimePromise__cctor_m6FAE6BD5140883FB8040431545B92712B3161D6B (void);
extern void DelayRealtimePromise__ctor_m03F34287DE1C842E9696417787D446E3C4AD5C51 (void);
extern void DelayRealtimePromise_Create_m5ACD0D119D883A0D470F3621E983292C96425439 (void);
extern void DelayRealtimePromise_GetResult_mB593C0EC23F51A291E4534BFFC26736069BC0DF4 (void);
extern void DelayRealtimePromise_GetStatus_mDAF74B7067AF460C849BE14725B8AD5A33832656 (void);
extern void DelayRealtimePromise_UnsafeGetStatus_m863E28053457D66C7AE2A26545E6CB206409DAC1 (void);
extern void DelayRealtimePromise_OnCompleted_m9A69A6B6DEC33B8F10BC758B64F734D51C6AF0A0 (void);
extern void DelayRealtimePromise_MoveNext_m5C14410A376AC58B3B2807E1F0FDE08C3FC463E7 (void);
extern void DelayRealtimePromise_TryReturn_mE24471EAFBC0BB4A3E549376E01091C638AAC6FA (void);
extern void U3CU3Ec__cctor_m98DFB32E0E467B914148A6A74BE2C8D8DFC32BAE (void);
extern void U3CU3Ec__ctor_m9FA833DBE7208035DB659BADDDBDC124731D3B57 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__4_0_mEB2B3297429136CF4F3D96A546B441B9A657E2E6 (void);
extern void U3CU3Ec_U3CCreateU3Eb__12_0_mF1E414CDC4C8249C2E062C327E6B567E40E5EAC2 (void);
extern void ExceptionResultSource__ctor_m73B2CEF2430F981844640DCE7012B4132E24DBE0 (void);
extern void ExceptionResultSource_GetResult_mB8192735CBF28EBC6F4F7488C5DCAB46BE1A1C17 (void);
extern void ExceptionResultSource_GetStatus_m250E761F8FE12938E51F82579B3F68D4BA96E8C6 (void);
extern void ExceptionResultSource_UnsafeGetStatus_mC7E9976510C2EDFAF6DF21DE0B9DE94F2C9D2197 (void);
extern void ExceptionResultSource_OnCompleted_m3C73A0A4CEC3A10F0C27F389EA32518E9A24283D (void);
extern void ExceptionResultSource_Finalize_m55BBC52F3ACE83A060FDE47AD1F2DF8E1FC9507C (void);
extern void CanceledResultSource__ctor_m2EAB6D4A1ADC45B7D902453F249A893CE12B445C (void);
extern void CanceledResultSource_GetResult_m18DCE2E7233718125EF31D547AB1ADEF1DED6474 (void);
extern void CanceledResultSource_GetStatus_mCFE66D2217C82444BC4194FF8551EE3E2D29A615 (void);
extern void CanceledResultSource_UnsafeGetStatus_m1D5E9C9762A95DC7830D3CAF87AEC2B80250C136 (void);
extern void CanceledResultSource_OnCompleted_m8ED55915D9776A6948B5C7AF1E6E283194D4B56F (void);
extern void WaitUntilPromise_get_NextNode_m25D42E4145EDB98482ED8CD0F5C99AE1A244BC7D (void);
extern void WaitUntilPromise__cctor_m43C487202B723888E0A42F1311FDD007794F3420 (void);
extern void WaitUntilPromise__ctor_m56149FCB2DBF5C76BC47CEF57317B29C03943F5D (void);
extern void WaitUntilPromise_Create_mACC38596A21B0A893C342B08C92E55CC63FFB9FE (void);
extern void WaitUntilPromise_GetResult_mC51A7E70F36B6602CBCD5F1B835B638A15EA77D3 (void);
extern void WaitUntilPromise_GetStatus_m214277160B9506ADF2CF2F1770796925726A7AC5 (void);
extern void WaitUntilPromise_UnsafeGetStatus_m204A9B69EECC18F33733EF42E85F9F7675EC990F (void);
extern void WaitUntilPromise_OnCompleted_m7845F07B26A7F6B07BD42BF9ABF451390937C0EC (void);
extern void WaitUntilPromise_MoveNext_mE00AD4E09916079CBA7B31B7333653EA84B29465 (void);
extern void WaitUntilPromise_TryReturn_m6B6D551CDB0D63135C8C1D3396FB3DE48F1DEEE9 (void);
extern void U3CU3Ec__cctor_m9A00EF80F4ACEBB30ECE861D97321E232993EE5A (void);
extern void U3CU3Ec__ctor_m64C709F50C2DB563CAF7751E8BF833BA7EB902CD (void);
extern void U3CU3Ec_U3C_cctorU3Eb__4_0_mF63DBFB68593D90197816181FE4ACD6921FE2309 (void);
extern void U3CU3Ec_U3CCreateU3Eb__11_0_mFEE8ADB9105F36D6995C6AC8355BA0FC13D614A1 (void);
extern void WhenAllPromise__ctor_mA01F0BE4F12F8AD4EF8EE4B1B943255A6F3063B5 (void);
extern void WhenAllPromise_TryInvokeContinuation_mBCA9E88FEF6A26EFB2FC7461FCBCE32D5FBBCAF9 (void);
extern void WhenAllPromise_GetResult_mB0BC0BC864A13172B67DA27F4CE9DE3982C41CA9 (void);
extern void WhenAllPromise_GetStatus_mB2009FB8A026269059469C3843D4E01AFA3E008F (void);
extern void WhenAllPromise_UnsafeGetStatus_m249F29D526C9BE6214D898A6846DB6684CC33D41 (void);
extern void WhenAllPromise_OnCompleted_m709CB570957A5114148641268661446E5E3A662B (void);
extern void U3CU3Ec__cctor_mBA016B6C62F33805289E4FC1CDEF1464E8FB37B6 (void);
extern void U3CU3Ec__ctor_m5DA3340D7019CD399E8C409787EE7F7EA123B1A1 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__3_0_m2739BAEECD92195E370F1F4CA628035FDDFEDB16 (void);
extern void U3CU3Ec__cctor_m51CCA72AEF541D67CAD60DB7D0636F332CA62F1C (void);
extern void U3CU3Ec__ctor_m9D18525225D440AAFA30EE17A72109811ECD8CB3 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__202_0_mCBD49FC3A2A646CF0EE526EDA48F53118E207CC9 (void);
extern void U3CRunOnThreadPoolU3Ed__98__ctor_m9A03B46096EB54FF79706D80077E5E12C8E4B508 (void);
extern void U3CRunOnThreadPoolU3Ed__98_MoveNext_mB935ABABB0065A5C75EE10417214E7E7EC8ABF5B (void);
extern void U3CRunOnThreadPoolU3Ed__98_SetStateMachine_m76E011AB8C050521A3FDA6108BADEBC7CB005FC6 (void);
extern void AwaiterActions_Continuation_m0EA21AB9CAA150F08C0928928D6F0BA3B6A0D819 (void);
extern void AwaiterActions__cctor_mCA4E63B7EB17E13FAF457077BC304C9E9863F22D (void);
extern void YieldAwaitable__ctor_m18D31312C0A37CDC6345D803A3FC55BEDDB7DB8E (void);
extern void YieldAwaitable_GetAwaiter_m56B2A4754DC798098A8ABD5DE2E6780BCEB64C7C (void);
extern void Awaiter__ctor_m11CEEE91D477321445760BC30E485E54A319E665 (void);
extern void Awaiter_get_IsCompleted_m380AD66439FAC48688A6348C575A8122F672D61A (void);
extern void Awaiter_GetResult_m65B26AEE969C14E1A7010BCDF845982D6EE1A0AB (void);
extern void Awaiter_UnsafeOnCompleted_m449B902ED39DDF273B1DC642244CA8B4D3831BE5 (void);
extern void CompletedTasks__cctor_m9606900578020736B05C78CBC0C93B0D4E574232 (void);
extern void SwitchToMainThreadAwaitable__ctor_mFCEC101B3A72BE6FEC732DA2B7E7DD98DA6959D1 (void);
extern void SwitchToMainThreadAwaitable_GetAwaiter_m66A0F6F462885727A38028656D11B51CEA32BD29 (void);
extern void Awaiter__ctor_m09E7F426390A927742186C32D32668BD079D8D73 (void);
extern void Awaiter_get_IsCompleted_m8B4E3723A3CC1B1E8359E40C0766081C55A5BC37 (void);
extern void Awaiter_GetResult_m8383C6B4850150162ECAA6464603B95FD2231CB2 (void);
extern void Awaiter_UnsafeOnCompleted_m0F8D6A7007F0397899FB1444B09D14912BB48E67 (void);
extern void SwitchToThreadPoolAwaitable_GetAwaiter_m8720CC348C2B3A1A6CF1317042436D3F8307BD29 (void);
extern void Awaiter_get_IsCompleted_mC1677DC7707E92D236DF2BA1144FFABF6E45D92E (void);
extern void Awaiter_GetResult_mF089C80E7D6C1B3FFA735B7F5B28098339127021 (void);
extern void Awaiter_UnsafeOnCompleted_mB6FEEBBADB2DEE87A1578C836F09D56A648F1A61 (void);
extern void Awaiter_Callback_m59E042793983D119D33FED28E23C4F336B0B2904 (void);
extern void Awaiter__cctor_mED2436D115A1C61C7F5402E1436DAB6DC74C7318 (void);
extern void ExceptionHolder__ctor_m6DD4EE0F80BC7D72FFAD053891227F046154EC79 (void);
extern void ExceptionHolder_GetException_m362D89AE3B04CF58E6567EEB499A88B9E6F62B38 (void);
extern void ExceptionHolder_Finalize_m5D7B937A4BDAA2DAE58B962B84BA69DB8C2BEBBF (void);
extern void UniTaskCompletionSourceCoreShared_CompletionSentinel_m5F39E3B8DD297E1DE863AA3054199A4DE848FB10 (void);
extern void UniTaskCompletionSourceCoreShared__cctor_mB49EAFB5151515948642A58C02085755F3BE151D (void);
extern void AutoResetUniTaskCompletionSource_get_NextNode_mA074D19C548EA16E1FFD257EB6F5EA01933513C6 (void);
extern void AutoResetUniTaskCompletionSource__cctor_mA8C85E9ADFC98D8071F5A150215BE0B4BA617EE4 (void);
extern void AutoResetUniTaskCompletionSource__ctor_m023A15C924FC18E1730265379F5887CE0D91CE60 (void);
extern void AutoResetUniTaskCompletionSource_Create_m8E2EEAAFD5A50FE30B7F559EF5CC90CF05F675C5 (void);
extern void AutoResetUniTaskCompletionSource_CreateFromCanceled_mD7DED295D1BC420D4A43D221FB1FF79C87C498A5 (void);
extern void AutoResetUniTaskCompletionSource_TrySetCanceled_m4E687B95F7447FE8BCC0E639B06106BDC05067F1 (void);
extern void AutoResetUniTaskCompletionSource_GetResult_m16900E2D7F26185D55213CB06AF2033117551745 (void);
extern void AutoResetUniTaskCompletionSource_GetStatus_m2F0CBECD04F1D780F9561E3A90B7A3A2BA301D6C (void);
extern void AutoResetUniTaskCompletionSource_UnsafeGetStatus_m0204913D767FBA63D0A5D3AEF290283A0AA76AC4 (void);
extern void AutoResetUniTaskCompletionSource_OnCompleted_m83CC163076722B065F59180B63E78B5C3A362AD3 (void);
extern void AutoResetUniTaskCompletionSource_TryReturn_mA1B01E7DCA312F6328B8963CD281DFB875517D94 (void);
extern void U3CU3Ec__cctor_m40EBABC43AF2045B6707DC5DBD8BFCD4F8D9C6CF (void);
extern void U3CU3Ec__ctor_mACB75D08D1EB22C42486B215C92917D1CA519700 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__4_0_m41E50932DCCA162736E8412E858A6116F0CB5075 (void);
extern void UniTaskCompletionSource__ctor_m7C8099F88D8C67FBB3D06EB3C12D852869862FF5 (void);
extern void UniTaskCompletionSource_MarkHandled_m3B29A0420CA63F7C0590149965C3C107D28F4E5A (void);
extern void UniTaskCompletionSource_get_Task_m25CA1E2D00C877FB6266595D70A2A3035DF89EFC (void);
extern void UniTaskCompletionSource_TrySetResult_m5C9F347F58580B4D85076121691E0474A1366F84 (void);
extern void UniTaskCompletionSource_GetResult_m0560DCA9C768570A7AB6B83287922EACAF2FB357 (void);
extern void UniTaskCompletionSource_GetStatus_m857C8A01E67333FDDB80A3ED56BFE29A281B06CA (void);
extern void UniTaskCompletionSource_UnsafeGetStatus_mF4C9873F8275CBCDF281C26A1A9B05BCBBAD4756 (void);
extern void UniTaskCompletionSource_OnCompleted_mB9171D8E4DF467FCDB0FBE017E0B79C955F83948 (void);
extern void UniTaskCompletionSource_TrySignalCompletion_m75DFB8A3BAEDCF994432A71268490F1B4BDEE4EE (void);
extern void UniTaskExtensions_AttachExternalCancellation_mD10FFC9C2946A7D3686FCAD013866A1A30B7CF72 (void);
extern void UniTaskExtensions_Forget_m8F82202C3DB2020AAE7F874AE049DA711A01DF13 (void);
extern void AttachExternalCancellationSource__ctor_m318C6ED196B206B316C3B6033BEB0283EE7A0A1B (void);
extern void AttachExternalCancellationSource_RunTask_mE2FC8F9F1D5A27A9D927C288441A6E82941108F7 (void);
extern void AttachExternalCancellationSource_CancellationCallback_m61F958A919D7C5C2D95864D8D4E29BB545E14ADC (void);
extern void AttachExternalCancellationSource_GetResult_m1A15200BA02682C399DAE41AF75EE42C2D700A2D (void);
extern void AttachExternalCancellationSource_GetStatus_mA44D7922AB8940EC110E63501E126630F0C47143 (void);
extern void AttachExternalCancellationSource_OnCompleted_m9C74F7923E34369E6978AC5752916F0EE26BBDEF (void);
extern void AttachExternalCancellationSource_UnsafeGetStatus_m34D5DCE09C05E6D40275C08C97951AD452782145 (void);
extern void AttachExternalCancellationSource__cctor_m3D5727BCD3BEB56FAC130E662BF8A7563E199BCE (void);
extern void U3CRunTaskU3Ed__5__ctor_m91CDB14104909B839645DCB3F1238BDE8282ABA0 (void);
extern void U3CRunTaskU3Ed__5_MoveNext_m3344B6864F5B1648AE534EB51AB375A63929322D (void);
extern void U3CRunTaskU3Ed__5_SetStateMachine_m676E99B99C852FB1BF9157A3B730FEDCB61D5FE6 (void);
extern void U3CU3Ec__cctor_m08A505E47B79ABCEDFF5C2D25D80D4CB40940E7D (void);
extern void U3CU3Ec__ctor_m6BFBD27DD778E7CC6F178AD0A4ABFB48B7384EC7 (void);
extern void U3CU3Ec_U3CForgetU3Eb__16_0_mF73782801449F4A8448C6C4AF7CC23058BDE4A11 (void);
extern void UniTaskScheduler_InvokeUnobservedTaskException_m5E53B569EC1EAAC323B73D8C74A0715DD7FC176C (void);
extern void UniTaskScheduler_PublishUnobservedTaskException_m9F6352174ED5654B0A9FB5562B6FF25A788B27D3 (void);
extern void UniTaskScheduler__cctor_m9D0CDCBB94462B5956764B2DCCAD4D8D654EB2E3 (void);
extern void UniTaskSynchronizationContext_Run_mC1E290BA942708DC1B8CC4410DFD3FADB8BE720F (void);
extern void UniTaskSynchronizationContext__cctor_m2DDCDC914885EDD75D09EA40D7E0EE58BDA79974 (void);
extern void Callback_Invoke_mCAC6841169B5A04BE53DFD6D11385386EEB0D094 (void);
extern void UniTaskVoid_Forget_mE4FC2CCCEFD822A5D63FFE97EE209FEE949CC255 (void);
extern void UnityAsyncExtensions_GetAwaiter_m49ED60AD5B61EF872A63966540B602D59A42EB16 (void);
extern void UnityAsyncExtensions_ToUniTask_mCC109FEDABBA122F82E454625FAE7303B79DA7B2 (void);
extern void UnityAsyncExtensions_GetAwaiter_m3B39088BD34AE418D7EA4041E478AF061505F30A (void);
extern void UnityAsyncExtensions_GetAwaiter_mA6B6F84DE48BC5EFE2F2BDB1A0011C8A2CD7A920 (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource_get_NextNode_mB5940E33081DC719B9E58824DD48A8F0406E3937 (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource__cctor_m5FE6FD13D6D5C57E6933273C60B5F1AFCBFAE8A4 (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource__ctor_mDA116A552AF93C617A5BDF082EDEC997CA3D9A9D (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource_Create_m793EC1215A2924C72AE0A29CE40A0C59BA28C4C1 (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource_GetResult_m513CAFCF1BA15ED84A503A637F55E3078B331061 (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource_Cysharp_Threading_Tasks_IUniTaskSource_GetResult_mEF69721A498837775E45A2AA2E4FE9640144E2E0 (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource_GetStatus_m3C71594800097EBA439E73EEA654BD34050D527E (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource_UnsafeGetStatus_mBF9211E24A21E1721B55F98C7DA4ACC242140438 (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource_OnCompleted_m36FEEAB848812233E33B44B41A4D7FEEE702AC94 (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource_MoveNext_mC61DFD69CB3382EA7E42F4760C94B7732FA8B09B (void);
extern void AsyncGPUReadbackRequestAwaiterConfiguredSource_TryReturn_mBAE97F824E02D72FE274A26BAC0936B1E6844FF8 (void);
extern void U3CU3Ec__cctor_m79CEE3359A844ED3C9B9DC7BE8F308F3634EE197 (void);
extern void U3CU3Ec__ctor_m1DEE312956A401007B8CC702277F4B3A39C6EAD4 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__4_0_m6FD31AB2173D92C38272B40DE10A46A35C1E3CA1 (void);
extern void U3CU3Ec_U3CCreateU3Eb__11_0_mCFCAA74B1FF46401F058E273B044C802F79BDF95 (void);
extern void ResourceRequestAwaiter__ctor_m9F612DDCFE50A3CF17BDA678DAA52E7DA5923F86 (void);
extern void ResourceRequestAwaiter_get_IsCompleted_m797B9071AB9AB8B33167896D6EF9ED10B1C73A10 (void);
extern void ResourceRequestAwaiter_GetResult_m648D2E45FFE2E55A3FA3175FDF1BCD993CF04068 (void);
extern void ResourceRequestAwaiter_UnsafeOnCompleted_mA1C3EDBC89B73759B2EBFBD27C81BEE9AA06B8BE (void);
extern void UnityWebRequestAsyncOperationAwaiter__ctor_m03AA09B7CDCE9B8CB5534497C41E571370C2164E (void);
extern void UnityWebRequestAsyncOperationAwaiter_get_IsCompleted_m7662537975C6DDA7D485E25793703401EA8D04B2 (void);
extern void UnityWebRequestAsyncOperationAwaiter_GetResult_m96EA9963C39AC42C04D9E35E1D5CF778D5BE8AF5 (void);
extern void UnityWebRequestAsyncOperationAwaiter_UnsafeOnCompleted_m6137E393610929F40829FD31FAF86196BADE2EDD (void);
extern void UnityWebRequestException_get_UnityWebRequest_m48414E7877C61EE4F0F14D78A9624EAFB8BDBF7F (void);
extern void UnityWebRequestException_get_Error_mB55A76B44C4D829F7D7FE796C9360B4D56669012 (void);
extern void UnityWebRequestException_get_Text_m76D36C8F27A1A9D2DADD0EB776C559018E1EC0C5 (void);
extern void UnityWebRequestException__ctor_m7ED0F69F1E92954B06F391B203CD3045C916E6BC (void);
extern void UnityWebRequestException_get_Message_mFB10697207F89F9F5E1EA078602684A14DEDC68C (void);
extern void AsyncAwakeTrigger_AwakeAsync_mA879106A5928E69410C51D4C51D056064D0808B4 (void);
extern void AsyncAwakeTrigger__ctor_m13CF4F7D5B3A40F4B749FEF76710FF7E73D42F97 (void);
extern void AsyncDestroyTrigger_get_CancellationToken_m279C90D70CAB912D1A8B9B375E3D824FE68F0386 (void);
extern void AsyncDestroyTrigger_Awake_mCEB1CA097CCF1C5FA59A07E12309FE874C5F0FA0 (void);
extern void AsyncDestroyTrigger_OnDestroy_m370C10B3F7230D1DFDAA5CDDA4E3757898D4E01D (void);
extern void AsyncDestroyTrigger_OnDestroyAsync_mCBF4866C30E2378EB1D8B19F9E1533BCED03B521 (void);
extern void AsyncDestroyTrigger__ctor_mA31DF14DD9FBA5472DDCF4057CF83B0750CEB6F2 (void);
extern void AwakeMonitor__ctor_mC99505C3BAD69AB2E58E6652E57AAD4D388F9368 (void);
extern void AwakeMonitor_MoveNext_mA118D7593A1BC426A3552B5AA6D2CF77D6BBC0B2 (void);
extern void U3CU3Ec__cctor_mC7BC6D6E13ABC800AED3E133A7A1E3EEF3031F8C (void);
extern void U3CU3Ec__ctor_mB92C06E254CDAE1D24DF9A71830865602F90C46B (void);
extern void U3CU3Ec_U3COnDestroyAsyncU3Eb__7_0_m6E29C6722B1144387C019ABB11520CF0659E02CE (void);
extern void AsyncStartTrigger_Start_m983BE686DB4DB41A5E6FA24DB3966904D23B99FB (void);
extern void AsyncStartTrigger_StartAsync_mA31E7C4DD6E90086D49A18B21421AD3A77D0B8F8 (void);
extern void AsyncStartTrigger__ctor_mE3257932A213645A410A170C9C9E9D2D0F5DB119 (void);
extern void AsyncFixedUpdateTrigger_FixedUpdate_mF6A14CCA456E3B9D7A947BF9813F7C95776D30C5 (void);
extern void AsyncFixedUpdateTrigger_GetFixedUpdateAsyncHandler_m8A745FAEA32A417B456A6CADA7766DA866681AC5 (void);
extern void AsyncFixedUpdateTrigger_GetFixedUpdateAsyncHandler_m58788C9C42556CED2BA76D98749945A1ADEDDCC9 (void);
extern void AsyncFixedUpdateTrigger_FixedUpdateAsync_m5D979BCD337E59EC9FE1FB39B605187895BBE545 (void);
extern void AsyncFixedUpdateTrigger_FixedUpdateAsync_m96830240B6D94358E752F9CFAA4BE8E2D03EFDD1 (void);
extern void AsyncFixedUpdateTrigger__ctor_m575B8AF32205F9CFDB158FA9EAD1A1B5BB5F1A79 (void);
extern void AsyncLateUpdateTrigger_LateUpdate_mA6B8F24DAF03C998920A0D7C200553323110BF1F (void);
extern void AsyncLateUpdateTrigger_GetLateUpdateAsyncHandler_m9D707C3809A57961FA4FE524B8AE7D01541490EA (void);
extern void AsyncLateUpdateTrigger_GetLateUpdateAsyncHandler_mAB7C4E9D5F3B6F0496C79E6EF7FBB06EBB6184E0 (void);
extern void AsyncLateUpdateTrigger_LateUpdateAsync_m5E85A71A744AA9301D1C62EFB53CC0ADA025542B (void);
extern void AsyncLateUpdateTrigger_LateUpdateAsync_mA0B535B7DB4CA03E645D12FDFC47FA2A355E2D41 (void);
extern void AsyncLateUpdateTrigger__ctor_mBC6DDF7E599727B32881652E32CBA73CBF9115A4 (void);
extern void AsyncAnimatorIKTrigger_OnAnimatorIK_m47E70C74254095C9E5E88F7697946A68E515B04B (void);
extern void AsyncAnimatorIKTrigger_GetOnAnimatorIKAsyncHandler_m4101EB6425B98841504FECEE8CB8CB764CCE8DA3 (void);
extern void AsyncAnimatorIKTrigger_GetOnAnimatorIKAsyncHandler_m7115604BA4FBD6F2F250E0B895780C4F69EB83C5 (void);
extern void AsyncAnimatorIKTrigger_OnAnimatorIKAsync_m6537E9A3E4D35502AC4FF407C134040C03713425 (void);
extern void AsyncAnimatorIKTrigger_OnAnimatorIKAsync_mCF26414C4D2FE79D00DED9FE9522C46764A158BF (void);
extern void AsyncAnimatorIKTrigger__ctor_m1B63E1A5CBDB3267EBD90F7979B1070FA903541D (void);
extern void AsyncAnimatorMoveTrigger_OnAnimatorMove_mBA6C81A5C56A10695B4481B945BB64D8872C38B4 (void);
extern void AsyncAnimatorMoveTrigger_GetOnAnimatorMoveAsyncHandler_m22F8BFBAC4C044ECF0E88D016C2E9CED34D1851D (void);
extern void AsyncAnimatorMoveTrigger_GetOnAnimatorMoveAsyncHandler_m9305424D878CEE3DB6D7B2381E1071451C3F7713 (void);
extern void AsyncAnimatorMoveTrigger_OnAnimatorMoveAsync_m41A7B0AF73557A1173C57608FE397604FAEAD7D7 (void);
extern void AsyncAnimatorMoveTrigger_OnAnimatorMoveAsync_m9F87CA88EF991EED8B8337CAB7CE81D3EF5014F0 (void);
extern void AsyncAnimatorMoveTrigger__ctor_mBF01F13DAC18B4ABE22CE693D0B3B8EF1F60B380 (void);
extern void AsyncApplicationFocusTrigger_OnApplicationFocus_mEAA684300913CF9F1C5D32A6504D8E76A847D054 (void);
extern void AsyncApplicationFocusTrigger_GetOnApplicationFocusAsyncHandler_m793FFF9249D44EC1F3C1112A8015C911FD2A533A (void);
extern void AsyncApplicationFocusTrigger_GetOnApplicationFocusAsyncHandler_mC13600F0A2E02097EDE223C31152BCF9BD3E3E20 (void);
extern void AsyncApplicationFocusTrigger_OnApplicationFocusAsync_m98C9F34D23B0D2CA3F35EF201D6AF2939492DD35 (void);
extern void AsyncApplicationFocusTrigger_OnApplicationFocusAsync_m3A0B444A40E6BCE3A45CA93F9E149A6A624B6E2F (void);
extern void AsyncApplicationFocusTrigger__ctor_m56399C6629DBB6218C1DD53C6E79506EECC31C70 (void);
extern void AsyncApplicationPauseTrigger_OnApplicationPause_m0E27E49834F357B643DCCB470514079E80AA985F (void);
extern void AsyncApplicationPauseTrigger_GetOnApplicationPauseAsyncHandler_m1E8430BBA86DE783F1FB748AAFFF31E8CA2AEB6A (void);
extern void AsyncApplicationPauseTrigger_GetOnApplicationPauseAsyncHandler_m1D042739988163B8E313E4E51C3EFE4DF6778484 (void);
extern void AsyncApplicationPauseTrigger_OnApplicationPauseAsync_m654318E90645304D0520F0EE392974DFAFD2214F (void);
extern void AsyncApplicationPauseTrigger_OnApplicationPauseAsync_m4AB59F5883BFD7BBD1FA4F9A1CAE7730E5F6607C (void);
extern void AsyncApplicationPauseTrigger__ctor_m537FFBFCAF1794AE3EE4A6B4F5FEE15B90776DB3 (void);
extern void AsyncApplicationQuitTrigger_OnApplicationQuit_mA2548998DD2748EBA66904362DD74A44F7BC047B (void);
extern void AsyncApplicationQuitTrigger_GetOnApplicationQuitAsyncHandler_m94C94CC41BC238A0FC0004CA30D1757ABBCA988C (void);
extern void AsyncApplicationQuitTrigger_GetOnApplicationQuitAsyncHandler_mBB3126503B25EFAF3B1BD7431D9A895A3CE0ADAA (void);
extern void AsyncApplicationQuitTrigger_OnApplicationQuitAsync_mA574A29DCA41E38FF455F3ABDC0DA4ADB32031AA (void);
extern void AsyncApplicationQuitTrigger_OnApplicationQuitAsync_mBAF1DC0DF0BB9F334FF52F4B454CA8CE63D705D1 (void);
extern void AsyncApplicationQuitTrigger__ctor_mAB3AA2971E4F1AC0F498BB36991B4BC89DFDAAC3 (void);
extern void AsyncAudioFilterReadTrigger_OnAudioFilterRead_mEE6E70D75AA076C52394DC62BA2D52DBEA800F46 (void);
extern void AsyncAudioFilterReadTrigger_GetOnAudioFilterReadAsyncHandler_mCBE6ECB315423D811983CEA1429EC91FE0183BD0 (void);
extern void AsyncAudioFilterReadTrigger_GetOnAudioFilterReadAsyncHandler_m874FD21CF399F59ACB9D926C78BAFF33C55AE91A (void);
extern void AsyncAudioFilterReadTrigger_OnAudioFilterReadAsync_m7395B4A1AA7F8B4F69AC49A9105780F673A4E7A5 (void);
extern void AsyncAudioFilterReadTrigger_OnAudioFilterReadAsync_m3245EF3204F44449CFD8A46957BB18921D90576B (void);
extern void AsyncAudioFilterReadTrigger__ctor_mE79734E5B64F8E8C293E7846D05802983437C849 (void);
extern void AsyncBecameInvisibleTrigger_OnBecameInvisible_m77D5B938F2A9CAEF0487C46CC7FF61EA8A59DBCA (void);
extern void AsyncBecameInvisibleTrigger_GetOnBecameInvisibleAsyncHandler_mF9098EA8CAC854198EF0CFE65903A28AAF308049 (void);
extern void AsyncBecameInvisibleTrigger_GetOnBecameInvisibleAsyncHandler_m7B35A1825361105BEF7DBC2277192CC197A51266 (void);
extern void AsyncBecameInvisibleTrigger_OnBecameInvisibleAsync_m751BBA1D3287C5F3A6E3B63139B7CB17F03CF501 (void);
extern void AsyncBecameInvisibleTrigger_OnBecameInvisibleAsync_m67E44297887780FABB45AA245559ED41831B5E5F (void);
extern void AsyncBecameInvisibleTrigger__ctor_m1A90CFB1E58FADF3A9E59E917853B6B68489B6EA (void);
extern void AsyncBecameVisibleTrigger_OnBecameVisible_m39B4D78730D757FC1D75C1C52644CC70941FB7DA (void);
extern void AsyncBecameVisibleTrigger_GetOnBecameVisibleAsyncHandler_mFA03783E5988CE433AB87B45F7E0D57CEAC057B0 (void);
extern void AsyncBecameVisibleTrigger_GetOnBecameVisibleAsyncHandler_mC262B84DAABA5688D3294B12CDBCD1B75CCE9ABE (void);
extern void AsyncBecameVisibleTrigger_OnBecameVisibleAsync_m0D25209BEEA861B7E2222C2C19C29B26D9D99ADE (void);
extern void AsyncBecameVisibleTrigger_OnBecameVisibleAsync_m026198068FA68BF75268F4F9C7571AEA96E52F91 (void);
extern void AsyncBecameVisibleTrigger__ctor_mDE949643FE2B5C8648944869AF20C5DDF4CA6ACD (void);
extern void AsyncBeforeTransformParentChangedTrigger_OnBeforeTransformParentChanged_m942FD6A248C0927849A612B3B9D58CACCB438639 (void);
extern void AsyncBeforeTransformParentChangedTrigger_GetOnBeforeTransformParentChangedAsyncHandler_m7E1FA6B2D412FFD8F344ADCEA07A43BA96DA1DB5 (void);
extern void AsyncBeforeTransformParentChangedTrigger_GetOnBeforeTransformParentChangedAsyncHandler_m0FDD80932B47559427028463EE203CA404A374C5 (void);
extern void AsyncBeforeTransformParentChangedTrigger_OnBeforeTransformParentChangedAsync_m20B316BD908DA444566B5F5762FC6E973E6D45DC (void);
extern void AsyncBeforeTransformParentChangedTrigger_OnBeforeTransformParentChangedAsync_mA39BE8BAFB0B77072BB69C4BEE775913058FAF64 (void);
extern void AsyncBeforeTransformParentChangedTrigger__ctor_m595EB1C1D5EDF1BCD5579E1A39A52B7C212A6B91 (void);
extern void AsyncOnCanvasGroupChangedTrigger_OnCanvasGroupChanged_m5E5AA51E0A79586D22BC53978B422B60C29F4CF9 (void);
extern void AsyncOnCanvasGroupChangedTrigger_GetOnCanvasGroupChangedAsyncHandler_m253EF25FCC5505D9120B711BA05B3E7F966E2110 (void);
extern void AsyncOnCanvasGroupChangedTrigger_GetOnCanvasGroupChangedAsyncHandler_m86882E8DE4E19CE03EBE1FB58D21CD524F5A22CF (void);
extern void AsyncOnCanvasGroupChangedTrigger_OnCanvasGroupChangedAsync_mE0612E5A7386D353E12BA7EEB487FC2F3395B984 (void);
extern void AsyncOnCanvasGroupChangedTrigger_OnCanvasGroupChangedAsync_m6735815EAA4A4DCA91A893DE72AF1BA9F89E578A (void);
extern void AsyncOnCanvasGroupChangedTrigger__ctor_m11E656E5FE00E33305782C5E06E1E57CD207015E (void);
extern void AsyncCollisionEnterTrigger_OnCollisionEnter_mC90290A1150995FB6D95E50AB4ABB6B7F52D719C (void);
extern void AsyncCollisionEnterTrigger_GetOnCollisionEnterAsyncHandler_m247C45C5942DB8A12C63564F051EC8AD078308B4 (void);
extern void AsyncCollisionEnterTrigger_GetOnCollisionEnterAsyncHandler_m4EF839F8F2C3607541B05E3F9848E0EB55C84F0B (void);
extern void AsyncCollisionEnterTrigger_OnCollisionEnterAsync_m741C3CD52C0B86EF4E1DB3C0EC396BA8935C7C75 (void);
extern void AsyncCollisionEnterTrigger_OnCollisionEnterAsync_m42619646397F338CF53BAD8BB6518E09C83E535A (void);
extern void AsyncCollisionEnterTrigger__ctor_mAAB0AA09DB91E5B8EB3AD84B236D5FD49987E3E2 (void);
extern void AsyncCollisionEnter2DTrigger_OnCollisionEnter2D_m3E85B55BF8C72C63A0202D62724484CB3FF631CE (void);
extern void AsyncCollisionEnter2DTrigger_GetOnCollisionEnter2DAsyncHandler_m067D70D1CFB3EF5298D3E83D225F26C7EFFE0EED (void);
extern void AsyncCollisionEnter2DTrigger_GetOnCollisionEnter2DAsyncHandler_m20E0140CE385B90D078E99E6D5E86F379E428B6C (void);
extern void AsyncCollisionEnter2DTrigger_OnCollisionEnter2DAsync_m3D9DDA2184719C8C03A500AE27EE19B58032549A (void);
extern void AsyncCollisionEnter2DTrigger_OnCollisionEnter2DAsync_m2146E39FE5E0A2614BED849635D567F8AF8CB248 (void);
extern void AsyncCollisionEnter2DTrigger__ctor_m13FEAC1E66AC89B539761206D5A7916823136415 (void);
extern void AsyncCollisionExitTrigger_OnCollisionExit_m97EC4F1E0B393C79995209073ACD9A091E60781B (void);
extern void AsyncCollisionExitTrigger_GetOnCollisionExitAsyncHandler_m8186D12CFB44F748C65208825A3FA261DB66C253 (void);
extern void AsyncCollisionExitTrigger_GetOnCollisionExitAsyncHandler_m7C3EF37619A4431570D6C78ABC46749A167ECA99 (void);
extern void AsyncCollisionExitTrigger_OnCollisionExitAsync_mCDD9563AEE1F5FC9EC72640C023DE1AB33533D6B (void);
extern void AsyncCollisionExitTrigger_OnCollisionExitAsync_mA3937BEE0AA24AF4630DB6972BD4D2FFA57530C2 (void);
extern void AsyncCollisionExitTrigger__ctor_m97AE8C6FD5376D4F213B5D4632D2E35BDB0A9224 (void);
extern void AsyncCollisionExit2DTrigger_OnCollisionExit2D_m144153AF5996E1F82BEF1FC16B45CC3754DDA0EF (void);
extern void AsyncCollisionExit2DTrigger_GetOnCollisionExit2DAsyncHandler_mA76718CBFB6194D5D8F297BE027D5FBC9612B427 (void);
extern void AsyncCollisionExit2DTrigger_GetOnCollisionExit2DAsyncHandler_m74907EB9D54FB94FC016C8D98D09BC45E96AAF37 (void);
extern void AsyncCollisionExit2DTrigger_OnCollisionExit2DAsync_m36442794A71DDD50892B524BE15CBA351CE0285F (void);
extern void AsyncCollisionExit2DTrigger_OnCollisionExit2DAsync_m65FB90CFCCA3C5B55B9653F1734EF15E5B0442DB (void);
extern void AsyncCollisionExit2DTrigger__ctor_m17D198C09FE6D957B2CA49752ABA20D6CBF1FDC1 (void);
extern void AsyncCollisionStayTrigger_OnCollisionStay_mF1B9365D81D464CC568094771EC2F75F5C9EAAFB (void);
extern void AsyncCollisionStayTrigger_GetOnCollisionStayAsyncHandler_m0741AA483305011B34A7B387581463F9ECB06F03 (void);
extern void AsyncCollisionStayTrigger_GetOnCollisionStayAsyncHandler_mA9D6F085925A1E54FBBF4E67035169CCBE7D3ECC (void);
extern void AsyncCollisionStayTrigger_OnCollisionStayAsync_m4BC9AADF0B2E3378DFD82378C4CDB2EDDE406ABA (void);
extern void AsyncCollisionStayTrigger_OnCollisionStayAsync_m5AEE2CF8B95F133E9C60BE2CC1B36BDCF767CBB9 (void);
extern void AsyncCollisionStayTrigger__ctor_mD8947EFFDD3E218ABF2699AB5E988E88AD178FF5 (void);
extern void AsyncCollisionStay2DTrigger_OnCollisionStay2D_m6D15572002ADFFFE16609BA6DD079BBBB7790528 (void);
extern void AsyncCollisionStay2DTrigger_GetOnCollisionStay2DAsyncHandler_m3B12C82B8B157C5D8DB00637D8EA3090EFE82019 (void);
extern void AsyncCollisionStay2DTrigger_GetOnCollisionStay2DAsyncHandler_m7CE1B531488691AA819C0521DB49753BD15D76E0 (void);
extern void AsyncCollisionStay2DTrigger_OnCollisionStay2DAsync_mC8C964A3BA9F5DDBD9FAF1C9694B804A590ABE27 (void);
extern void AsyncCollisionStay2DTrigger_OnCollisionStay2DAsync_m7815ABF102448305DC42A8510DC8264CFEA60156 (void);
extern void AsyncCollisionStay2DTrigger__ctor_mD1982A72AF07E99DD6AEF257961027DA768B3815 (void);
extern void AsyncControllerColliderHitTrigger_OnControllerColliderHit_mCF1DCD15C9B4EA74064F10AE8E1FA624CE2AD37D (void);
extern void AsyncControllerColliderHitTrigger_GetOnControllerColliderHitAsyncHandler_m378F060116622AD37BD08CA3198556CD781712EF (void);
extern void AsyncControllerColliderHitTrigger_GetOnControllerColliderHitAsyncHandler_m39873EC5A5480262CEC387C010232D683CE14D1B (void);
extern void AsyncControllerColliderHitTrigger_OnControllerColliderHitAsync_mC09AC7E1BE67B026FEBB3ACEAEA86270871712C4 (void);
extern void AsyncControllerColliderHitTrigger_OnControllerColliderHitAsync_m1942830ECFEF930FDB87F0841C46F0E5EC002725 (void);
extern void AsyncControllerColliderHitTrigger__ctor_mFF59DB3B7D54032EBCDF102E92EC971696BD44EF (void);
extern void AsyncDisableTrigger_OnDisable_m94534C41131C1669F1426602DA337AA238896EFE (void);
extern void AsyncDisableTrigger_GetOnDisableAsyncHandler_m794E325B17DA7DA1FF1A7CAD87B70E30D3077E49 (void);
extern void AsyncDisableTrigger_GetOnDisableAsyncHandler_mC3877B53EB7F03676209A3E4CCC4B4E97D1498A0 (void);
extern void AsyncDisableTrigger_OnDisableAsync_mFA82D467093DFAC6B62B99ABD57E0574FC6886E0 (void);
extern void AsyncDisableTrigger_OnDisableAsync_m91BA82DDCEDA62211BA3DFE33301196CEB040A68 (void);
extern void AsyncDisableTrigger__ctor_mFB504DA992CF40D2552F35FBE01F948B68F756F0 (void);
extern void AsyncDrawGizmosTrigger_OnDrawGizmos_m9EB22D9F78522D9068B32A20A113EB14DF9C36DD (void);
extern void AsyncDrawGizmosTrigger_GetOnDrawGizmosAsyncHandler_m63D2B35D915532D716701BA49FB0226B94DAC646 (void);
extern void AsyncDrawGizmosTrigger_GetOnDrawGizmosAsyncHandler_m9413AA7E3EBD18D3518AAD2DDB9BE015CE92229C (void);
extern void AsyncDrawGizmosTrigger_OnDrawGizmosAsync_m995EC266C7D1015C514059494BE85CBEDA5B620C (void);
extern void AsyncDrawGizmosTrigger_OnDrawGizmosAsync_mFD0BC0AB40B9D221979EE4581A48C18DE4EFA7CE (void);
extern void AsyncDrawGizmosTrigger__ctor_mD0FC881341E8CBA94B1041F048A1B68D3E6F816C (void);
extern void AsyncDrawGizmosSelectedTrigger_OnDrawGizmosSelected_m4FB477B43EF05422CCD791ACF7A55EDECD4AA9B8 (void);
extern void AsyncDrawGizmosSelectedTrigger_GetOnDrawGizmosSelectedAsyncHandler_m45C986C8B39D54C6C9E7419B73B17164BBBC8C2E (void);
extern void AsyncDrawGizmosSelectedTrigger_GetOnDrawGizmosSelectedAsyncHandler_m09F6EFC1D44144B50955D9E11FD76BD38AAD0352 (void);
extern void AsyncDrawGizmosSelectedTrigger_OnDrawGizmosSelectedAsync_mC6E55A356EFB75D71D1F4456B7801350B3F82604 (void);
extern void AsyncDrawGizmosSelectedTrigger_OnDrawGizmosSelectedAsync_m5756ECE28154E97B36C22294646C49B6E5F9F4BC (void);
extern void AsyncDrawGizmosSelectedTrigger__ctor_mA0CD6BF6BB129C0A97496821BBF026E8DBA9E29C (void);
extern void AsyncEnableTrigger_OnEnable_m908E830B7C692EFE4B54B9C832A54E31607F986D (void);
extern void AsyncEnableTrigger_GetOnEnableAsyncHandler_mF61E39D0BF56E15BFA8BF8C27E7E2003F53BF5C0 (void);
extern void AsyncEnableTrigger_GetOnEnableAsyncHandler_m5B0348F73BA312A50881087B82AD79EFEB32E326 (void);
extern void AsyncEnableTrigger_OnEnableAsync_mBC631AE12F5E7AD6F8DEACF51268DD4BDDFFE402 (void);
extern void AsyncEnableTrigger_OnEnableAsync_mD48C8ADF74405FD0CC02B01C12BFEE344DB6DEDC (void);
extern void AsyncEnableTrigger__ctor_m0C149280874A22EC2196AB8620219809AB63C2A5 (void);
extern void AsyncGUITrigger_OnGUI_mC92580E78E76C2E8D426A17872B82FC1BC5C60C5 (void);
extern void AsyncGUITrigger_GetOnGUIAsyncHandler_m6062771EF6E369735A64964B7DAFFFB7F4517440 (void);
extern void AsyncGUITrigger_GetOnGUIAsyncHandler_mAA4F841E9AEAD1D79F46660FA9F8CFA65B3D6A23 (void);
extern void AsyncGUITrigger_OnGUIAsync_m7DA5A1A31E09F09AB38C601843BCF02F47064844 (void);
extern void AsyncGUITrigger_OnGUIAsync_mA5894CD3F7E18CF46D69BE1EC7B9DCC9C729EE38 (void);
extern void AsyncGUITrigger__ctor_mBB359CF9E8B6DDA04A26620EA12AF31FC92B4897 (void);
extern void AsyncJointBreakTrigger_OnJointBreak_m9F0E2F76C2ADF9F4CDCDB02AD5A463EB768735F4 (void);
extern void AsyncJointBreakTrigger_GetOnJointBreakAsyncHandler_m4FE53D3C6C604311F08BB08A662A610B68998B10 (void);
extern void AsyncJointBreakTrigger_GetOnJointBreakAsyncHandler_m45DC4532B62A3A1D63423788546D6431E7735822 (void);
extern void AsyncJointBreakTrigger_OnJointBreakAsync_m009D08CB70D48911C64EB66EFDFF0F76BDDD76BB (void);
extern void AsyncJointBreakTrigger_OnJointBreakAsync_m1A612A9FE46594C7DAD22A9B414DAD775DE79030 (void);
extern void AsyncJointBreakTrigger__ctor_m0909A04E06E82056B2C1341186AE81DB9C39B6FE (void);
extern void AsyncJointBreak2DTrigger_OnJointBreak2D_m67D95AF80CC0BF148CF0F8BEF88F2D8E6A054CCD (void);
extern void AsyncJointBreak2DTrigger_GetOnJointBreak2DAsyncHandler_mA8052F3A35DDD34096E7717EDFB7995EC76A9C0E (void);
extern void AsyncJointBreak2DTrigger_GetOnJointBreak2DAsyncHandler_m44EF7F400E734AEA3F6D3A1A39D56660E2D6C702 (void);
extern void AsyncJointBreak2DTrigger_OnJointBreak2DAsync_mEBC2E12DB7B6E809EB4722CEEA53F0FD41A2C9EB (void);
extern void AsyncJointBreak2DTrigger_OnJointBreak2DAsync_m6F7E688EC0FFFB5BBD1516D40259DEB9142EAB73 (void);
extern void AsyncJointBreak2DTrigger__ctor_mF3D16096AFA1D3230787198561D692046D932544 (void);
extern void AsyncParticleCollisionTrigger_OnParticleCollision_mAB88BF25AF4A07E16A30BB77F515A84916A67F41 (void);
extern void AsyncParticleCollisionTrigger_GetOnParticleCollisionAsyncHandler_m3B2DC4C2A129E82A2562E054CBA4535529F77E65 (void);
extern void AsyncParticleCollisionTrigger_GetOnParticleCollisionAsyncHandler_mD6AEC2E8160F2EA131EA13B4E3876D1EB6E1DE32 (void);
extern void AsyncParticleCollisionTrigger_OnParticleCollisionAsync_m977ED1879F90F1F3312C9FA331C6D931D8DAF5B3 (void);
extern void AsyncParticleCollisionTrigger_OnParticleCollisionAsync_mB4EE87AFCA7DE53A9430877ABA0B3B14E8F8552F (void);
extern void AsyncParticleCollisionTrigger__ctor_m09D7736615C59A039C72CF98DDC64B0B633EB212 (void);
extern void AsyncParticleSystemStoppedTrigger_OnParticleSystemStopped_m1F15241D8C179E3D910BC60B303DD84C258D5078 (void);
extern void AsyncParticleSystemStoppedTrigger_GetOnParticleSystemStoppedAsyncHandler_m94B0E325DB6EA714418A87CB3C42F6B1071901FA (void);
extern void AsyncParticleSystemStoppedTrigger_GetOnParticleSystemStoppedAsyncHandler_m9DCA68CB73F0DF57D66B67DC7BE5FEFB1BB0A442 (void);
extern void AsyncParticleSystemStoppedTrigger_OnParticleSystemStoppedAsync_m06CF53CFA48C269D7F80A4BF7722350FC39F6BA3 (void);
extern void AsyncParticleSystemStoppedTrigger_OnParticleSystemStoppedAsync_mA461791DBBEF11CCD034DBBD8BD65BF4D35699FE (void);
extern void AsyncParticleSystemStoppedTrigger__ctor_m904E62BA6E4518A27C3A61763793F4A986687237 (void);
extern void AsyncParticleTriggerTrigger_OnParticleTrigger_mC208F719B283C4C88E5E3D0482002AECE60839A6 (void);
extern void AsyncParticleTriggerTrigger_GetOnParticleTriggerAsyncHandler_m1B5E3D0C2ED8646A0DEA6157F5D23F8661D59868 (void);
extern void AsyncParticleTriggerTrigger_GetOnParticleTriggerAsyncHandler_m9A51E8F1188B8BF257C24DE7FDE8E1C1E58F1DED (void);
extern void AsyncParticleTriggerTrigger_OnParticleTriggerAsync_m47DD183AE5AAF8A99A00B2DE0CC2A586CF108EA3 (void);
extern void AsyncParticleTriggerTrigger_OnParticleTriggerAsync_m7046FA3D6B4C6515C363F5E9D47E96B85E6D6235 (void);
extern void AsyncParticleTriggerTrigger__ctor_m8269A35ABC71140B7ED5723DE6B568474D057D59 (void);
extern void AsyncParticleUpdateJobScheduledTrigger_OnParticleUpdateJobScheduled_m74A99E942E46AF80E4AB4C5D1A157F6BB51712C9 (void);
extern void AsyncParticleUpdateJobScheduledTrigger_GetOnParticleUpdateJobScheduledAsyncHandler_m819FB3F20036AA9189B0CFA747FFB650A57446D1 (void);
extern void AsyncParticleUpdateJobScheduledTrigger_GetOnParticleUpdateJobScheduledAsyncHandler_m95774A8666C72D0C828F1FE08F1852FBDCB3A9EF (void);
extern void AsyncParticleUpdateJobScheduledTrigger_OnParticleUpdateJobScheduledAsync_m5EEE4CF5485107ECAC80DB74A4381EADE5286BDD (void);
extern void AsyncParticleUpdateJobScheduledTrigger_OnParticleUpdateJobScheduledAsync_m058A419713F55D7889713AB79C086D583B0D8A06 (void);
extern void AsyncParticleUpdateJobScheduledTrigger__ctor_mBDC5B1EF5960A431B5C7856FE2736C10B7EA523D (void);
extern void AsyncPostRenderTrigger_OnPostRender_mBB072ADCE5F2C449BA8B3F6C8D484AAE1E17CE7E (void);
extern void AsyncPostRenderTrigger_GetOnPostRenderAsyncHandler_m743EC696BF3015D8CA610F472266FF15A72BFF61 (void);
extern void AsyncPostRenderTrigger_GetOnPostRenderAsyncHandler_m0BBA36AF26E020488A4E2ECDE93D628C7823A73F (void);
extern void AsyncPostRenderTrigger_OnPostRenderAsync_mE0663A68C7806C983847686964623C26EA9F6E3C (void);
extern void AsyncPostRenderTrigger_OnPostRenderAsync_m9CE060A1050D16FB7A719A3A33991DD6B4AF2A15 (void);
extern void AsyncPostRenderTrigger__ctor_mB9C91EFA8FD7309B8DADF3C2090E4881367D48BC (void);
extern void AsyncPreCullTrigger_OnPreCull_m8BB8ACA22D72CB75007C5654A7E4FCD2CB5C9734 (void);
extern void AsyncPreCullTrigger_GetOnPreCullAsyncHandler_mBCC91746CF122BAC372FD27DCA0D4EA0165F9652 (void);
extern void AsyncPreCullTrigger_GetOnPreCullAsyncHandler_mE4F6D965AC6FFBF05F6C20203DF56F6AAA2E5242 (void);
extern void AsyncPreCullTrigger_OnPreCullAsync_m66EE45765FFE039BA71E16A52EE0C464AA45D2D0 (void);
extern void AsyncPreCullTrigger_OnPreCullAsync_m01A58406C404E6E55C241DA3BE5710A2A16CCB02 (void);
extern void AsyncPreCullTrigger__ctor_mAA11EB2F80B462E9201611C9D15AD0E8A043D2A6 (void);
extern void AsyncPreRenderTrigger_OnPreRender_m1079D7D822CF19494538DEF22ECE2B81634F41EC (void);
extern void AsyncPreRenderTrigger_GetOnPreRenderAsyncHandler_m1D65A52A4D45E69417D37C2692821BA8E1B89C72 (void);
extern void AsyncPreRenderTrigger_GetOnPreRenderAsyncHandler_mECD1216D87565C260D8C61A0C43774F65334B7DD (void);
extern void AsyncPreRenderTrigger_OnPreRenderAsync_mB0AC4368868514881C7149CA779F08A8C493A1E4 (void);
extern void AsyncPreRenderTrigger_OnPreRenderAsync_mC67896394616B2796F138221AD88EB64BDBF707F (void);
extern void AsyncPreRenderTrigger__ctor_m58745C27B119C73D861348FA50F2A17FB9954779 (void);
extern void AsyncRectTransformDimensionsChangeTrigger_OnRectTransformDimensionsChange_mCEF7EEA734C490FF4F183ECC88426EBB60E0E895 (void);
extern void AsyncRectTransformDimensionsChangeTrigger_GetOnRectTransformDimensionsChangeAsyncHandler_m335E5F74ED9D1861F3E4941A7571BA6834C26A57 (void);
extern void AsyncRectTransformDimensionsChangeTrigger_GetOnRectTransformDimensionsChangeAsyncHandler_mF2C56261FF948B05C2B1190532CF5D0E0BCE8BBF (void);
extern void AsyncRectTransformDimensionsChangeTrigger_OnRectTransformDimensionsChangeAsync_m302946011A50A6C701DB03FBFC432A417242DF76 (void);
extern void AsyncRectTransformDimensionsChangeTrigger_OnRectTransformDimensionsChangeAsync_mFDE718FC057B3AAECE4C52CE347B34834C90CCCA (void);
extern void AsyncRectTransformDimensionsChangeTrigger__ctor_m1C4CC310B246DDE5A43CB79B23FAC9FADABE39BF (void);
extern void AsyncRectTransformRemovedTrigger_OnRectTransformRemoved_mBD06842C7B34DEDA6C64DC7903FB43ADE3E9B85F (void);
extern void AsyncRectTransformRemovedTrigger_GetOnRectTransformRemovedAsyncHandler_mD12641FE032A87B2ED5A831357FC37F8AC27432E (void);
extern void AsyncRectTransformRemovedTrigger_GetOnRectTransformRemovedAsyncHandler_m127633818EC8250B74CA466B200398E4D6A1475D (void);
extern void AsyncRectTransformRemovedTrigger_OnRectTransformRemovedAsync_m3EC5F120E02890FB4B5988379B80AFB98CE84328 (void);
extern void AsyncRectTransformRemovedTrigger_OnRectTransformRemovedAsync_m7F129D367F5850EFB5D71B755C0335C9C6F12222 (void);
extern void AsyncRectTransformRemovedTrigger__ctor_m0F2DE34A26744D66C7DB55CE0229E268BFDD88FF (void);
extern void AsyncRenderImageTrigger_OnRenderImage_mDCDB76BCE6EBFF2096A7EAC73E4F72D04C8E3350 (void);
extern void AsyncRenderImageTrigger_GetOnRenderImageAsyncHandler_m9D537F7B59F54A3E3BE60EFC59C76958754B3252 (void);
extern void AsyncRenderImageTrigger_GetOnRenderImageAsyncHandler_mE3D255982895A770D66E08000126D277B359AC6B (void);
extern void AsyncRenderImageTrigger_OnRenderImageAsync_m91CE6C7DF51D427CCF19CD19ADD13EFA9499017C (void);
extern void AsyncRenderImageTrigger_OnRenderImageAsync_m139CF203844E7CFBD54334949DC18E12BF30B33E (void);
extern void AsyncRenderImageTrigger__ctor_mFED9370CF549990CEF799FECF0D4C7323FE55D00 (void);
extern void AsyncRenderObjectTrigger_OnRenderObject_mE0614C8F30A97D4FA5DB7CE04ACEAA0DA7875D2A (void);
extern void AsyncRenderObjectTrigger_GetOnRenderObjectAsyncHandler_m616C95392EB27750CA278FE63F260C9F818FA406 (void);
extern void AsyncRenderObjectTrigger_GetOnRenderObjectAsyncHandler_m1E01EEC9E77863DDC00CA301D90B319C8B1539D4 (void);
extern void AsyncRenderObjectTrigger_OnRenderObjectAsync_m2AFD6381ED39F073EB4E7F50EA51EE086CE01FA4 (void);
extern void AsyncRenderObjectTrigger_OnRenderObjectAsync_m89DAA18E4F271AFE4B0624A2B0387399EB43F96F (void);
extern void AsyncRenderObjectTrigger__ctor_m02F4B33BE8DE504FB664BB90953AEA86FFE5A7C1 (void);
extern void AsyncServerInitializedTrigger_OnServerInitialized_mE7C5A6B7CA9DD4A9621C1D7D4A0AA5502D94BFDC (void);
extern void AsyncServerInitializedTrigger_GetOnServerInitializedAsyncHandler_mF9892ACE15C075BD1B64100FAE38CF05F6B15ADE (void);
extern void AsyncServerInitializedTrigger_GetOnServerInitializedAsyncHandler_m230CF255A8EE1403BCF8F749DD60CDA1095DF9FE (void);
extern void AsyncServerInitializedTrigger_OnServerInitializedAsync_mBD83933D204A9ABD56169E7A2304DB00F228D952 (void);
extern void AsyncServerInitializedTrigger_OnServerInitializedAsync_mB94CCF570EEAC3E75874E715CFBE103E5B938AED (void);
extern void AsyncServerInitializedTrigger__ctor_m4903A1D3EDFA9556D5071D691C86DCDF55A0BAD4 (void);
extern void AsyncTransformChildrenChangedTrigger_OnTransformChildrenChanged_mB766B3AAE2AC504FAE655FBAAE91DBA5C60E5A72 (void);
extern void AsyncTransformChildrenChangedTrigger_GetOnTransformChildrenChangedAsyncHandler_mAC29370E010951C99D0005DE0FBD845C9B27E8B2 (void);
extern void AsyncTransformChildrenChangedTrigger_GetOnTransformChildrenChangedAsyncHandler_mFA04E5A68C238110A9965CBDFE44754E81052048 (void);
extern void AsyncTransformChildrenChangedTrigger_OnTransformChildrenChangedAsync_m9A6C0B58EB7B02D87BE90EE1F38AD776AB98E3F5 (void);
extern void AsyncTransformChildrenChangedTrigger_OnTransformChildrenChangedAsync_m05B67060C03105BCD88618E48F83EF87757B9FE4 (void);
extern void AsyncTransformChildrenChangedTrigger__ctor_m6487135C4F4E451F55010BBB53427F684253E40B (void);
extern void AsyncTransformParentChangedTrigger_OnTransformParentChanged_m9F023397C4145C57ADDF27B30C56591447862AF7 (void);
extern void AsyncTransformParentChangedTrigger_GetOnTransformParentChangedAsyncHandler_m792911E5D33ACD6F6D8F39342FD052E1063F3A6E (void);
extern void AsyncTransformParentChangedTrigger_GetOnTransformParentChangedAsyncHandler_mFD20DFC05628802928961500AC716CF15EC01F28 (void);
extern void AsyncTransformParentChangedTrigger_OnTransformParentChangedAsync_m2A042FDD8DA120540E678D620910F18754F21BB6 (void);
extern void AsyncTransformParentChangedTrigger_OnTransformParentChangedAsync_m3400D30019581CC110E95B9BE88786A57BF8EC06 (void);
extern void AsyncTransformParentChangedTrigger__ctor_m08BA52ADB268DB9B1047B6D0A10EFFC62E216216 (void);
extern void AsyncTriggerEnterTrigger_OnTriggerEnter_m987B0631561DADCF50ADAB4025D0DA37F044BF0D (void);
extern void AsyncTriggerEnterTrigger_GetOnTriggerEnterAsyncHandler_mDC42AE523AD77E05A9781A7DB689B4F47E521A9F (void);
extern void AsyncTriggerEnterTrigger_GetOnTriggerEnterAsyncHandler_mF9D3C729AB281CFFDD286CDDBCE64796D1D4C8CE (void);
extern void AsyncTriggerEnterTrigger_OnTriggerEnterAsync_m70124C83C761D35FDCA94667EEA1E460F5F02E9E (void);
extern void AsyncTriggerEnterTrigger_OnTriggerEnterAsync_mFA17FE0A71CA365A213934E9781F61A043AAF9EC (void);
extern void AsyncTriggerEnterTrigger__ctor_m94291F492F7F5FC615902E4843F0C12309930A88 (void);
extern void AsyncTriggerEnter2DTrigger_OnTriggerEnter2D_m20C6705138B9F2021D39EB3B19F2974E0B6254D7 (void);
extern void AsyncTriggerEnter2DTrigger_GetOnTriggerEnter2DAsyncHandler_mE5922E4095D7E338378986A873F216A26652AEF4 (void);
extern void AsyncTriggerEnter2DTrigger_GetOnTriggerEnter2DAsyncHandler_m364D016D0754472AC213AFF988075A26AD92FE5C (void);
extern void AsyncTriggerEnter2DTrigger_OnTriggerEnter2DAsync_m648D60D922B491745048C6AF56F91E6457A82410 (void);
extern void AsyncTriggerEnter2DTrigger_OnTriggerEnter2DAsync_m053C5553A5DDAFE6E2B48E150CB8E01EF59CB859 (void);
extern void AsyncTriggerEnter2DTrigger__ctor_m2C867B03B0CC792FC2D0A4429B4B69B7F3CBFDBB (void);
extern void AsyncTriggerExitTrigger_OnTriggerExit_mC9140851D54908E8587502C7093FC0E9478E5F0A (void);
extern void AsyncTriggerExitTrigger_GetOnTriggerExitAsyncHandler_mD62C3917B65B910FCCD1A28C49C01987641F37D5 (void);
extern void AsyncTriggerExitTrigger_GetOnTriggerExitAsyncHandler_m450CE5E9C1698DD90AB559FE25DA709C2F840CDB (void);
extern void AsyncTriggerExitTrigger_OnTriggerExitAsync_mF399CA1A0F46E3406260F520E9C0CB9273E719B1 (void);
extern void AsyncTriggerExitTrigger_OnTriggerExitAsync_m25707BB67EADA30383AB48021BF2BC0DB74FC6D7 (void);
extern void AsyncTriggerExitTrigger__ctor_m02D41654EAA534FEC8206D55AB3B916CE3A975D9 (void);
extern void AsyncTriggerExit2DTrigger_OnTriggerExit2D_mD2347A86823FA31E88FD0EB0A2FE9A194C11FBE3 (void);
extern void AsyncTriggerExit2DTrigger_GetOnTriggerExit2DAsyncHandler_m15A74FF40FCCEF701589E5005613915384BE3139 (void);
extern void AsyncTriggerExit2DTrigger_GetOnTriggerExit2DAsyncHandler_m62886134FFCA01612A5559BEE2DA14A253C38FCA (void);
extern void AsyncTriggerExit2DTrigger_OnTriggerExit2DAsync_mF4AFE6532D7E39B0649E2CFFE24F562A56D8EB14 (void);
extern void AsyncTriggerExit2DTrigger_OnTriggerExit2DAsync_m8FDF4429E6483153FC537DE0E107D59F05A7250F (void);
extern void AsyncTriggerExit2DTrigger__ctor_m6C158090D92C5521AAB46AF22CE7D5A249628F29 (void);
extern void AsyncTriggerStayTrigger_OnTriggerStay_mECE592D3892C2113D169D009C149F9F14821752B (void);
extern void AsyncTriggerStayTrigger_GetOnTriggerStayAsyncHandler_mF28681278F440C6BBF1FEA5654C7BC17C30E5354 (void);
extern void AsyncTriggerStayTrigger_GetOnTriggerStayAsyncHandler_m20F92E48CFAF36C1750D7A8F5BD901736EEA4CBE (void);
extern void AsyncTriggerStayTrigger_OnTriggerStayAsync_m00C665C6B369753660BBA9081F8183DB34ECE2A0 (void);
extern void AsyncTriggerStayTrigger_OnTriggerStayAsync_mE2FDCCC2D53DD23037BDC52303B103DDB1944481 (void);
extern void AsyncTriggerStayTrigger__ctor_mA8262B94799FEDAC48D3A5D67F3A7EAE6D17F5F2 (void);
extern void AsyncTriggerStay2DTrigger_OnTriggerStay2D_m1F524CE082AA4BD8DF7344C4A7B544900875F591 (void);
extern void AsyncTriggerStay2DTrigger_GetOnTriggerStay2DAsyncHandler_m84FBB5668D312A20B079DB05E82F8D7861839BD9 (void);
extern void AsyncTriggerStay2DTrigger_GetOnTriggerStay2DAsyncHandler_m6CABA94980326BCFC2E8C0FC5D22CF2B0F162DC8 (void);
extern void AsyncTriggerStay2DTrigger_OnTriggerStay2DAsync_m4BFC56AB458AE530CE94324E0ABF30DCBACA80DB (void);
extern void AsyncTriggerStay2DTrigger_OnTriggerStay2DAsync_mB378ACE64845B39578647EA254C7CB721AB53615 (void);
extern void AsyncTriggerStay2DTrigger__ctor_m0C2F47161B6AB02BB517175952CE5073D768C8BB (void);
extern void AsyncValidateTrigger_OnValidate_m6D87C365C451119F965AE3C3B26A5BEF18D64F28 (void);
extern void AsyncValidateTrigger_GetOnValidateAsyncHandler_m93B61B438CAEC00A57683BE865F3037C7AAB18D1 (void);
extern void AsyncValidateTrigger_GetOnValidateAsyncHandler_m8ECBBB8FDB07F67B8ECA8DE67535E4A1617D1082 (void);
extern void AsyncValidateTrigger_OnValidateAsync_m5B4930FBBC3B5415906EE63F2AF118E985271BE6 (void);
extern void AsyncValidateTrigger_OnValidateAsync_mDC784A5D55B69D304DAE67DCD4FEAC7BB05AEC56 (void);
extern void AsyncValidateTrigger__ctor_m8E06D8B7DF5CEFAFFE794462F1DE83062242B16A (void);
extern void AsyncWillRenderObjectTrigger_OnWillRenderObject_m000349C12C77A792F5F0E03B28BAB5163AD2621C (void);
extern void AsyncWillRenderObjectTrigger_GetOnWillRenderObjectAsyncHandler_m21C0854CAFDAD22FA260F242FDA37EEF0864C37B (void);
extern void AsyncWillRenderObjectTrigger_GetOnWillRenderObjectAsyncHandler_m5A987A306765AA901E6BBB4C65FCE2D015CD8052 (void);
extern void AsyncWillRenderObjectTrigger_OnWillRenderObjectAsync_mE9FE145E988136CB40D676F0491AF3BA419A22C0 (void);
extern void AsyncWillRenderObjectTrigger_OnWillRenderObjectAsync_mA5CA68CD354E1B84275BB203B343B60E46F4B87A (void);
extern void AsyncWillRenderObjectTrigger__ctor_m24D430A90CF1A74E75C0EE37DDF463A827E69036 (void);
extern void AsyncResetTrigger_Reset_m198B4882AE86300DBC5B07C9AF4CF49BAE6152E0 (void);
extern void AsyncResetTrigger_GetResetAsyncHandler_mF8F9B51C0C643F4F5BFA1229AF9EDA31BDC849F5 (void);
extern void AsyncResetTrigger_GetResetAsyncHandler_mAB5FFC1283DEC76FBE41C065FC98BBCB53B90A33 (void);
extern void AsyncResetTrigger_ResetAsync_mC38F95492FF08D8253115CC0D2D33217CAA601C7 (void);
extern void AsyncResetTrigger_ResetAsync_m1763D6AF5E377D44258B6489EC23145DFA710E16 (void);
extern void AsyncResetTrigger__ctor_mDA674611F8438A002072FD58EAA5F47782697BCF (void);
extern void AsyncUpdateTrigger_Update_m5321BA60C550332ECB74B05D2D26DD2017095123 (void);
extern void AsyncUpdateTrigger_GetUpdateAsyncHandler_m90503A9E45D8228631B2D988799F3FDCD041E6E7 (void);
extern void AsyncUpdateTrigger_GetUpdateAsyncHandler_m3B4493D31EB552A7FE6FFF7AE24E4EA241009E7B (void);
extern void AsyncUpdateTrigger_UpdateAsync_m41D031BFCC576B4606888C87DF3217C1F63CD659 (void);
extern void AsyncUpdateTrigger_UpdateAsync_mD324C9094A568ECCFCB9DBBF3A351699CC5E853E (void);
extern void AsyncUpdateTrigger__ctor_m154BA59C03623508B967B4A7428889E7AAFF06BD (void);
extern void AsyncBeginDragTrigger_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mA03ABC0CE73F6A81EAB2A2DC01314DE1C75017D0 (void);
extern void AsyncBeginDragTrigger_GetOnBeginDragAsyncHandler_mCC84469272DB1F26A36D79D4D2B17C70A5F51B4C (void);
extern void AsyncBeginDragTrigger_GetOnBeginDragAsyncHandler_m622C2389C34EA474603912204A1D889951B82B3C (void);
extern void AsyncBeginDragTrigger_OnBeginDragAsync_m4539156944A7986CF33AF89BAE8836828D67344A (void);
extern void AsyncBeginDragTrigger_OnBeginDragAsync_mFF453D3CFFF6F6C27944FA69AEFEA22B4A0C085B (void);
extern void AsyncBeginDragTrigger__ctor_m93C1E7BF275BF2740582CDD00D5273EA7890A97D (void);
extern void AsyncCancelTrigger_UnityEngine_EventSystems_ICancelHandler_OnCancel_m7F923EAA95A3805F736D193A81F94EBEB009E85C (void);
extern void AsyncCancelTrigger_GetOnCancelAsyncHandler_m5222CED3A8988662208821CCE5FC48C624234073 (void);
extern void AsyncCancelTrigger_GetOnCancelAsyncHandler_m18ED65769DE2FD370AA935E0E80B03403AFCBEF4 (void);
extern void AsyncCancelTrigger_OnCancelAsync_m5274E06A1AF76DA4CE71A076F86604D9ED0933B4 (void);
extern void AsyncCancelTrigger_OnCancelAsync_m3DCBAE1F4D50620EBB5436AFECD94FAB2249C9A0 (void);
extern void AsyncCancelTrigger__ctor_m91A3511B78449BDF1C667C6D033116499D2B6F01 (void);
extern void AsyncDeselectTrigger_UnityEngine_EventSystems_IDeselectHandler_OnDeselect_mF0115B94B991F030AC4065B62EE18DCF858BBB80 (void);
extern void AsyncDeselectTrigger_GetOnDeselectAsyncHandler_m6F292094CE1EFB741AF4359044A81B3B2118EA99 (void);
extern void AsyncDeselectTrigger_GetOnDeselectAsyncHandler_mCAA51997A06FB65D34E552530879C37FD132CE4F (void);
extern void AsyncDeselectTrigger_OnDeselectAsync_m5F99FAC049CD6D4BBD119AF594ADCBE5BB8AAB0F (void);
extern void AsyncDeselectTrigger_OnDeselectAsync_mACA85079A579109733B7CBD73C373B9F106AF043 (void);
extern void AsyncDeselectTrigger__ctor_m88193D405ABE43B9A86FA98574B267D7050B09D8 (void);
extern void AsyncDragTrigger_UnityEngine_EventSystems_IDragHandler_OnDrag_mE6EE89FD4B8C55B6CAD8CDD583E98085803CA5BB (void);
extern void AsyncDragTrigger_GetOnDragAsyncHandler_mE9A088371AED15D78320931AE777F175EC267B3E (void);
extern void AsyncDragTrigger_GetOnDragAsyncHandler_mC62B15B6DB1524F512DF26ED66E4630D0B594FDB (void);
extern void AsyncDragTrigger_OnDragAsync_mFE1C88E879C8620A7411C2AFDF2A7CB656F92C17 (void);
extern void AsyncDragTrigger_OnDragAsync_mDE183DAF1CB58E17D55FEFDC418043A73D8B93D9 (void);
extern void AsyncDragTrigger__ctor_m4246DA4A79FC5B4EE7AF2D40D8E61C2C557BC3BB (void);
extern void AsyncDropTrigger_UnityEngine_EventSystems_IDropHandler_OnDrop_m6EC469C5DDE8F5DFA56DC574CB30A0C899A3BE79 (void);
extern void AsyncDropTrigger_GetOnDropAsyncHandler_mC7BC09849A0A3EEBCC9A49992C3F85CBC99D40F9 (void);
extern void AsyncDropTrigger_GetOnDropAsyncHandler_mD131FC037DAD9C1375DC8B6338B3F7DE488B1592 (void);
extern void AsyncDropTrigger_OnDropAsync_m24E0BFD04C65108867E6C2DDCB2E0C77316FFBD3 (void);
extern void AsyncDropTrigger_OnDropAsync_m667E704E1DC358927FF27D820DBCA73F48A8E860 (void);
extern void AsyncDropTrigger__ctor_m52B026578FEC5CADE8EC07012219195372042C75 (void);
extern void AsyncEndDragTrigger_UnityEngine_EventSystems_IEndDragHandler_OnEndDrag_mA6E208817F267F75434FB20CC4A2C1656FA72CDA (void);
extern void AsyncEndDragTrigger_GetOnEndDragAsyncHandler_m8DD5DC103EF92681BA60966BCCF3C0ED2022E8F1 (void);
extern void AsyncEndDragTrigger_GetOnEndDragAsyncHandler_m85F4AFDCE4CEC2FBCE4E33BB482C499EAFCEE29C (void);
extern void AsyncEndDragTrigger_OnEndDragAsync_m3FA7DFA354A3AFF9179AC97017A153AF33CB9674 (void);
extern void AsyncEndDragTrigger_OnEndDragAsync_m33CD963F2D8E25C6631DC5D3B570A369146AD160 (void);
extern void AsyncEndDragTrigger__ctor_m6AD8D6387589C775E66148AC3C327418FC273B79 (void);
extern void AsyncInitializePotentialDragTrigger_UnityEngine_EventSystems_IInitializePotentialDragHandler_OnInitializePotentialDrag_m29C011CBE259A2772D34FED167FDE9B5F19E3E7E (void);
extern void AsyncInitializePotentialDragTrigger_GetOnInitializePotentialDragAsyncHandler_m79E90E8346E05A21414B2F052BDD2624BBA5214E (void);
extern void AsyncInitializePotentialDragTrigger_GetOnInitializePotentialDragAsyncHandler_mD09078E1E80CEAFD30E5EB8054FF9F02BD3A21BE (void);
extern void AsyncInitializePotentialDragTrigger_OnInitializePotentialDragAsync_mB8A6588663519DE5E66BF691194393CE7F9C3301 (void);
extern void AsyncInitializePotentialDragTrigger_OnInitializePotentialDragAsync_m2670ACE4FA88A59573E089FF56FEEB9F6DD6AAEC (void);
extern void AsyncInitializePotentialDragTrigger__ctor_mA856ED893AE01C6799E3BBBDBDCF32C80F692952 (void);
extern void AsyncMoveTrigger_UnityEngine_EventSystems_IMoveHandler_OnMove_m106A6DE0A40BF1992095328994E9B25266C07A19 (void);
extern void AsyncMoveTrigger_GetOnMoveAsyncHandler_m4DB3879B5B3F1A9DA814A7D9789137FFB11F76AF (void);
extern void AsyncMoveTrigger_GetOnMoveAsyncHandler_m4CCAD0773D4AE9E657E196A3A6FD66E7B3EDCEE9 (void);
extern void AsyncMoveTrigger_OnMoveAsync_m2ABBCFD97CD0ECF6D3E6220D87F67281E50DD278 (void);
extern void AsyncMoveTrigger_OnMoveAsync_m0488637FE9AA347498D6060A49FE93E3A501AF2F (void);
extern void AsyncMoveTrigger__ctor_m45E3B69860382856E11BA62527F019FE069804B5 (void);
extern void AsyncPointerClickTrigger_UnityEngine_EventSystems_IPointerClickHandler_OnPointerClick_mE04300FDF07CDE0FE4428BDE950A72AC000EF30B (void);
extern void AsyncPointerClickTrigger_GetOnPointerClickAsyncHandler_mA566665DF7714C32E9BD7B1DF530F57BAC6F26E0 (void);
extern void AsyncPointerClickTrigger_GetOnPointerClickAsyncHandler_m73077567C1FAA022B7A090B98E6781CADAAB1F61 (void);
extern void AsyncPointerClickTrigger_OnPointerClickAsync_mFAB255C3C4797C7B60655C035B2938C8FDCCA33F (void);
extern void AsyncPointerClickTrigger_OnPointerClickAsync_m5B4E2978071A425B3229AC1AD264DA796396E9B1 (void);
extern void AsyncPointerClickTrigger__ctor_m0BC216A191100A4553FB7C5E5CB02283EF95AFF1 (void);
extern void AsyncPointerDownTrigger_UnityEngine_EventSystems_IPointerDownHandler_OnPointerDown_m538BA1B2A4BC460886C9BA40233C3D8CF718FF9E (void);
extern void AsyncPointerDownTrigger_GetOnPointerDownAsyncHandler_m688F1EEF4459FBB17E2EE3A94E90B8B44F3939A3 (void);
extern void AsyncPointerDownTrigger_GetOnPointerDownAsyncHandler_mE523D00A201D7AF98DFE605A1A5CAC33724913F7 (void);
extern void AsyncPointerDownTrigger_OnPointerDownAsync_m940E470426C0E2CDF91094036B4DABD1A903FB1D (void);
extern void AsyncPointerDownTrigger_OnPointerDownAsync_m97F16BD60F67D92B5558B54048C6C8F6AAB8BDDC (void);
extern void AsyncPointerDownTrigger__ctor_m47AC62324159AC99D02E8F2C03DC532249DFA205 (void);
extern void AsyncPointerEnterTrigger_UnityEngine_EventSystems_IPointerEnterHandler_OnPointerEnter_m9CC1764FFE744F7DA3CD0948EEB78F853F1DA263 (void);
extern void AsyncPointerEnterTrigger_GetOnPointerEnterAsyncHandler_m71BA530662B463CB6E38A453CF5B7C8F08A7D8F6 (void);
extern void AsyncPointerEnterTrigger_GetOnPointerEnterAsyncHandler_mFD5431F7A7A6F56B58C28C80A3B29B659B6B4F0B (void);
extern void AsyncPointerEnterTrigger_OnPointerEnterAsync_mB2D312F1E56A2A670BBA8C588A92A557881C6E8A (void);
extern void AsyncPointerEnterTrigger_OnPointerEnterAsync_mA69CAFE10318A3360E997B8B9F8095FDA6D8F4A4 (void);
extern void AsyncPointerEnterTrigger__ctor_mCD8B949EDDFBCAB60C7BBCD7B3B14126E077004A (void);
extern void AsyncPointerExitTrigger_UnityEngine_EventSystems_IPointerExitHandler_OnPointerExit_m21FF9CEAB6518F47CA763B4359549C60BD121545 (void);
extern void AsyncPointerExitTrigger_GetOnPointerExitAsyncHandler_mB9F1108DBFF5AAB0607750BFC5E30F7C72D1D678 (void);
extern void AsyncPointerExitTrigger_GetOnPointerExitAsyncHandler_mA6A4970B7FD37ED5AA4860FDDF229D100B44AF24 (void);
extern void AsyncPointerExitTrigger_OnPointerExitAsync_m05F7D5E6C6642002650ADF4048B312642167023B (void);
extern void AsyncPointerExitTrigger_OnPointerExitAsync_mCA9B7E46E233C86EED631B0999DC5DF6341190E7 (void);
extern void AsyncPointerExitTrigger__ctor_m4402450531D2F20D61BEEC8817CCAE3F32953F5D (void);
extern void AsyncPointerUpTrigger_UnityEngine_EventSystems_IPointerUpHandler_OnPointerUp_m70F177ACD1FDED11A2B3EB06F5FEF5E91C181286 (void);
extern void AsyncPointerUpTrigger_GetOnPointerUpAsyncHandler_mEE2547442026FD5C03D78557B257DBC4B3F4AD16 (void);
extern void AsyncPointerUpTrigger_GetOnPointerUpAsyncHandler_m5F89720EA1F24FFE6C3309394CC2232D7C0DE006 (void);
extern void AsyncPointerUpTrigger_OnPointerUpAsync_mB8C833F75C04322D0640D6C7D81B62BF62F4755C (void);
extern void AsyncPointerUpTrigger_OnPointerUpAsync_m018A9D9AD3D0E676FBC6C0527968EE819731F495 (void);
extern void AsyncPointerUpTrigger__ctor_m45B519521DCA4CD711890353781ACE67B46D0901 (void);
extern void AsyncScrollTrigger_UnityEngine_EventSystems_IScrollHandler_OnScroll_m8945007EA695538E62EBFBFFC76BEB3184D847ED (void);
extern void AsyncScrollTrigger_GetOnScrollAsyncHandler_mEDD87166B0C11F3111B0465DA7BAF874F82F77B6 (void);
extern void AsyncScrollTrigger_GetOnScrollAsyncHandler_m04D9951AFA1FF263081D2A2F60707519121183AA (void);
extern void AsyncScrollTrigger_OnScrollAsync_mDE615A9D7E7C7BA53FA03A3143A11DA1641A7467 (void);
extern void AsyncScrollTrigger_OnScrollAsync_m20AC4D84B89B0375ACC0EA9411C9840CAF738EC6 (void);
extern void AsyncScrollTrigger__ctor_m6C7411E7DE5ABE67A459D5BEE2C1233ACCC81CE7 (void);
extern void AsyncSelectTrigger_UnityEngine_EventSystems_ISelectHandler_OnSelect_m2E2DE234586ECA1CE685D841BEBCB702159C398D (void);
extern void AsyncSelectTrigger_GetOnSelectAsyncHandler_mF048D4E0FB6FCC1B396D4266FC51AEA684077C12 (void);
extern void AsyncSelectTrigger_GetOnSelectAsyncHandler_m617C2B56B8A4EC8FCFFB1D60C6C36708CD4A9796 (void);
extern void AsyncSelectTrigger_OnSelectAsync_mA234129DF821D8705973E57C1F1B817AB844F3FA (void);
extern void AsyncSelectTrigger_OnSelectAsync_m58D691420266A3E01918667F41D35239011CDC60 (void);
extern void AsyncSelectTrigger__ctor_m9961CCBC188ED442EF801485D0A070950BDB9242 (void);
extern void AsyncSubmitTrigger_UnityEngine_EventSystems_ISubmitHandler_OnSubmit_m13EE18C4B9DDA7E0AD97CD2B0AF83538C7A62480 (void);
extern void AsyncSubmitTrigger_GetOnSubmitAsyncHandler_m5048EDDE331533E46A0FD4D74F4AFF3E4B0D3527 (void);
extern void AsyncSubmitTrigger_GetOnSubmitAsyncHandler_mD358FE71567F3E4E158B0875F0DBD2CA78BE5047 (void);
extern void AsyncSubmitTrigger_OnSubmitAsync_mC989E95E688D41DB759E34C9D01AF6E85142A9A9 (void);
extern void AsyncSubmitTrigger_OnSubmitAsync_m499D26BBA1CCA12C56EAB4AC2CBE850AF26D63FC (void);
extern void AsyncSubmitTrigger__ctor_mBEFC6E37237735EE8E1A4D077C64F09EB0B7F83F (void);
extern void AsyncUpdateSelectedTrigger_UnityEngine_EventSystems_IUpdateSelectedHandler_OnUpdateSelected_m751BCA6DC0D3FB832F14A44DCC7F08FA36B83403 (void);
extern void AsyncUpdateSelectedTrigger_GetOnUpdateSelectedAsyncHandler_m91B0AD3DBC4A601FFEA58763B6B5FC676E380B76 (void);
extern void AsyncUpdateSelectedTrigger_GetOnUpdateSelectedAsyncHandler_mD0E63D5FED30DEAAD37F4224036EBB11B6A2A0CF (void);
extern void AsyncUpdateSelectedTrigger_OnUpdateSelectedAsync_mB5E2B84F5C14E42D61801287414C513AF9306F1E (void);
extern void AsyncUpdateSelectedTrigger_OnUpdateSelectedAsync_m348EF605036779545BF1F5393BB5AB74A5C01D77 (void);
extern void AsyncUpdateSelectedTrigger__ctor_m8F8F7CEBEE75BD238702F928EE7D434AE11F7448 (void);
extern void ContinuationQueue__ctor_m2691F2E47B45071E1D748B12BFCEA7003A4A2349 (void);
extern void ContinuationQueue_Enqueue_m802882C7B05D37A530F9E81453259CD2A8FAAF16 (void);
extern void ContinuationQueue_Run_mAF0D9BE1DE591406CF6E9E7AE723BFF6FB248027 (void);
extern void ContinuationQueue_Initialization_mCEE8AC1829A03B2346961EF03F5A6CF781D49CCA (void);
extern void ContinuationQueue_LastInitialization_m0A14A82C3FC05EAB16A2F53B67FE20F7A5AA9BA9 (void);
extern void ContinuationQueue_EarlyUpdate_mE2D343C81C97676F58EBB14AD0D0166BD342C90E (void);
extern void ContinuationQueue_LastEarlyUpdate_m3B9F214C3BA142D1C09865F15C5A85D637121DAA (void);
extern void ContinuationQueue_FixedUpdate_m31E37797C020DC852FC8493DBAC90A9C46EE1CBC (void);
extern void ContinuationQueue_LastFixedUpdate_mCC4FC1FD84E95AA48F4200F4A3EE5FF8E92AAF94 (void);
extern void ContinuationQueue_PreUpdate_mE93C2E72DC0FA847BD8310F67FCEDFEDBE43BE33 (void);
extern void ContinuationQueue_LastPreUpdate_m970552416F8418461AF2707BB9FA52B764B6D632 (void);
extern void ContinuationQueue_Update_mF437F847D02FB8DE1BFB309D8C6A04C824FD198F (void);
extern void ContinuationQueue_LastUpdate_mE0155228A6BCC874D777A668FF642C72AD2B8A6B (void);
extern void ContinuationQueue_PreLateUpdate_m8DF6791BC638D689D4C37ED4221A0FFCCAAD3B71 (void);
extern void ContinuationQueue_LastPreLateUpdate_m4DE23C52241C21064CB12C63C9F23F19A9A7942B (void);
extern void ContinuationQueue_PostLateUpdate_m55D35665E9FA975C9B2EB4CEA9B5D1E3E802D7F0 (void);
extern void ContinuationQueue_LastPostLateUpdate_mBC3FDF1E9B650F49F9DE9A58C3B0E67CCD676DCD (void);
extern void ContinuationQueue_TimeUpdate_m95DC65E6A08364B3326E2B0D1E2007EA218942C6 (void);
extern void ContinuationQueue_LastTimeUpdate_m2FFF647992655828559CD332BD4BFD7893941CDE (void);
extern void ContinuationQueue_RunCore_m3CF0C93FCEC17B9E879B2A31FE46A9846C77EFEC (void);
extern void Error_ThrowArgumentNullExceptionCore_m3FF7A528380B19AF3E6A08B2CD103556B0A94779 (void);
extern void Error_ThrowInvalidOperationExceptionCore_m5BEDE591893014DF989C54DFB3D8B060252C1A31 (void);
extern void PlayerLoopRunner__ctor_m2D6695A577F21178AB92E93FDF465CE37ECA28E1 (void);
extern void PlayerLoopRunner_AddAction_mC0968BDEF4B70D3CBF59AF574D87DDB51982236D (void);
extern void PlayerLoopRunner_Run_m33748216201A3D6575D14D23450C03068FD1729B (void);
extern void PlayerLoopRunner_Initialization_m261886B5EE4FE447AC0D9970A2611CB15F30A3C0 (void);
extern void PlayerLoopRunner_LastInitialization_mD60239EE16381604AF1F898F321BD9B5DBFF5BF7 (void);
extern void PlayerLoopRunner_EarlyUpdate_mA17D05AE2D41AC0AC8FBA6A6F887022B5919E181 (void);
extern void PlayerLoopRunner_LastEarlyUpdate_mF015AACB9D25CF340986B968F77384A0A2975DFD (void);
extern void PlayerLoopRunner_FixedUpdate_m46665E9C1FDA461847D4EED290F11C8704631F67 (void);
extern void PlayerLoopRunner_LastFixedUpdate_mB77A68F437A2A4CA9152CB80F638A12195DEB08E (void);
extern void PlayerLoopRunner_PreUpdate_m346C48450A6B63EE2147DF3A902008BF932FD649 (void);
extern void PlayerLoopRunner_LastPreUpdate_mE6D4A4211F5D699CA52F229DEF02D14C435CBB49 (void);
extern void PlayerLoopRunner_Update_mE9FAAF206931C3575A7D84FEFC50CE119EEC9E9E (void);
extern void PlayerLoopRunner_LastUpdate_mA6E740086E42031FAEDF12D6D29F5ECFCA8E36B8 (void);
extern void PlayerLoopRunner_PreLateUpdate_m9A5A64617B89F735E29217E1DFA546E5F058BF14 (void);
extern void PlayerLoopRunner_LastPreLateUpdate_m46D83DE50CE91101E177611F90491B2C97FB94F1 (void);
extern void PlayerLoopRunner_PostLateUpdate_mC2235D8AA5C890A2DED85B058A8226284CD0F03C (void);
extern void PlayerLoopRunner_LastPostLateUpdate_mB141860C1D02EE72D1BA5FAD550A9EEBB3A2A583 (void);
extern void PlayerLoopRunner_TimeUpdate_m78113C468F4D9542AF991E290672ABF89DA0B8FD (void);
extern void PlayerLoopRunner_LastTimeUpdate_m34D7531369689AE25CBC54466A711086336EE94F (void);
extern void PlayerLoopRunner_RunCore_m053B5E8652893AA94B9CD56C64484CA49D01153A (void);
extern void U3CU3Ec__cctor_mE23C6BAF80DDC97AC0299B07D5E5EBEDAD7AE1C3 (void);
extern void U3CU3Ec__ctor_mEC8E674F3A7E554B6C09782BE4023E8DDA0F7E9E (void);
extern void U3CU3Ec_U3C_ctorU3Eb__9_0_m7FD5E476CB2AB74C2AA83C5A0A68796DF976B7A9 (void);
extern void RuntimeHelpersAbstraction_WellKnownNoReferenceContainsTypeInitialize_mD3611FAEF828BA7D67B3B447050F697AA89CB364 (void);
extern void UnityWebRequestResultExtensions_IsError_m1E1CC890AF2527FABD6B6AD5BD6A25FEF0604310 (void);
extern void ValueStopwatch_StartNew_m0E86DE08F588C60DCA8982B16DB6949C4A93233F (void);
extern void ValueStopwatch__ctor_mF4323A30F6F82FAAA7C2C943EE2E708A2E482D68 (void);
extern void ValueStopwatch_get_IsInvalid_mE2AFBDC1DEB255E72DE87B2BD2C7EA8BFDCDBDF1 (void);
extern void ValueStopwatch_get_ElapsedTicks_mF4B713799E06F9F82066A6AB557F419F21C18E40 (void);
extern void ValueStopwatch__cctor_m57CAAF73C5CED4A7B8A02C11494C2D632AD7DAC1 (void);
extern void AsyncUniTaskMethodBuilder_Create_m882DFD5EE004FF6F84F182A3AC170AEDCA8C5327 (void);
extern void AsyncUniTaskMethodBuilder_get_Task_m11E43D69C4F85EB23AAC58143DE695309D1022EC (void);
extern void AsyncUniTaskMethodBuilder_SetException_m0D772D62D01CC371F4AB0F6943BBBE0FEAB19643 (void);
extern void AsyncUniTaskMethodBuilder_SetResult_mBBA527F0F21E04D65A269C0D02597CE5B2D1E9CB (void);
extern void AsyncUniTaskVoidMethodBuilder_Create_m06E63ABF318CBA8C8DE8BA460E94C6E9CDB5ED64 (void);
extern void AsyncUniTaskVoidMethodBuilder_get_Task_mA5D58CCC3268985A789B48F67492123752CE7A98 (void);
extern void AsyncUniTaskVoidMethodBuilder_SetException_m7492DE9DAE6EEC3DA2D1792A7B937F6CF61C6EB5 (void);
extern void AsyncUniTaskVoidMethodBuilder_SetResult_m12336C298D2F8CED07AAB85C3DD67815E8343947 (void);
static Il2CppMethodPointer s_methodPointers[1166] = 
{
	AsyncMethodBuilderAttribute__ctor_m362B0044366C29B2E6E2626F1FB61D9CF443111C,
	AsyncUnit_GetHashCode_mEA99722071116F872F4FD9397FCE42319B9C3407,
	AsyncUnit_Equals_m4FA66A2168F60FBACE2254A0D6F1513BC8111389,
	AsyncUnit_ToString_m41E4FD5E5F3F3FCBC1C00EB15CDFA98EC9CDE3DF,
	AsyncUnit__cctor_mEA0725B246A2EBC4E49EABD1D296D90295019593,
	CancellationTokenExtensions_Callback_m5DB0ABF0185CBBA5D111D208800188E29C17F22A,
	CancellationTokenExtensions_RegisterWithoutCaptureExecutionContext_mA5A09F053F6E50AD047E0DB08666E4270863AC69,
	CancellationTokenExtensions_DisposeCallback_mE3FAA9A576B2269AA49E437E9D1E4655634E65EF,
	CancellationTokenExtensions__cctor_m0A79502349C53CA61E0A731D51B22BA93FB57292,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	IUniTaskSource_System_Threading_Tasks_Sources_IValueTaskSource_GetStatus_mC63A1B0702E6297684613D702BDF4BAB87191E8E,
	IUniTaskSource_System_Threading_Tasks_Sources_IValueTaskSource_GetResult_m15B83960847D64A05AF0445C43CE6A81006CB9F0,
	IUniTaskSource_System_Threading_Tasks_Sources_IValueTaskSource_OnCompleted_m57AEB63BFA408747FDE3415104C2EC542DF832B0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UniTaskStatusExtensions_IsCompleted_mF43C41C9CEB640E381D1F7A8B40142843AED87AE,
	MoveNextSource_GetResult_mFCD01A5925FA4577700231792E6394FBE1C32EE6,
	MoveNextSource_GetStatus_m460FE5EF11578BF85CF6A857D3197049FAD4DF47,
	MoveNextSource_OnCompleted_mA6A8C6E7CB4D8BDFC06A30DA370D4DB88F41F9F2,
	MoveNextSource_UnsafeGetStatus_mC553AFFB2B851CB0C8EF12D1168F4D28CD40856F,
	MoveNextSource_Cysharp_Threading_Tasks_IUniTaskSource_GetResult_m086008DCD8B258C9616511263B0E6FC57AD766E8,
	MoveNextSource__ctor_mAD2FB060DAA5FB4AC333260F502B4FD563133640,
	NULL,
	PlayerLoopHelper_get_UnitySynchronizationContext_mBE814607A95340320B3074E622431CEFC29765EE,
	PlayerLoopHelper_get_MainThreadId_m187CD1091ABFC2F5638C9FD630F89364D9BE8A1D,
	PlayerLoopHelper_get_IsMainThread_mC32C7D62B20488CD0BA2F337EBFEC8128086DD5A,
	PlayerLoopHelper_InsertRunner_m8855844A2F4F8B434E2ACE94CDFC53F7F7D6F0B2,
	PlayerLoopHelper_RemoveRunner_m8B2228E19BA6B7DEEDF70A3A10EF8B475489B3E7,
	PlayerLoopHelper_InsertUniTaskSynchronizationContext_mF47A1A4A57B9D526D4138B6D125DCB71A7FA3ECC,
	PlayerLoopHelper_Init_m4CF7695E03702F0AED776686D060A45D5F09386B,
	PlayerLoopHelper_FindLoopSystemIndex_mFC2B12111D667F039EAA9795EC34E2F73815A52C,
	PlayerLoopHelper_InsertLoop_m0A3AD5C2A5DCF56368E328838B001156ECBC5EAD,
	PlayerLoopHelper_Initialize_m68A88284085A80341C8083E9131443DB8416B3B0,
	PlayerLoopHelper_AddAction_mAD5D5DA039BE42ECF032A1835BD95C318D6ED22D,
	PlayerLoopHelper_ThrowInvalidLoopTiming_m0B0B6B47071024DA6FE9DE246EA177C1DA34EBDB,
	PlayerLoopHelper_AddContinuation_m5B8F03E45A820D91B17CFA56AA93E7444BEC17E0,
	PlayerLoopHelper__cctor_mFFCB195045BCACFAEE25BB6877D59E792278F265,
	U3CU3Ec__cctor_mD96B9FBDAA89B459C19B3D7908FAA783D962110D,
	U3CU3Ec__ctor_m7B9FF6BEE6BE46908D89E71C6B4F5AD5A2C866E3,
	U3CU3Ec_U3CInsertUniTaskSynchronizationContextU3Eb__21_0_m8EF75464EEE2DD9BFC71B913EC7969CC18D6F03E,
	U3CU3Ec_U3CInsertUniTaskSynchronizationContextU3Eb__21_1_m78CF9CAEE453AFC76EF7B62208F56F07C87A3F2F,
	U3CU3Ec_U3CInsertUniTaskSynchronizationContextU3Eb__21_2_mF2DF172BE816640569D6A246A04A4697DA9869C4,
	U3CU3Ec__DisplayClass20_0__ctor_mB403A5464295A5CBC610678A8252E2EE95D894B1,
	U3CU3Ec__DisplayClass20_0_U3CRemoveRunnerU3Eb__0_m2E763B97ED0D87CE029D0D0D452340D7DD144248,
	TaskPool__cctor_m11E9B67729C2AB10470F79030676B1C32F16B88F,
	TaskPool_RegisterSizeGetter_m9CB7F337911201F7B7012F0CA8B35D697D8AC9F6,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UniTask__ctor_m5CBDA4598A89633256BF98AB40B3BA549A4CF84F,
	UniTask_get_Status_mA15B0F13DE3CE36730357CF50F65AE99ADF564DA,
	UniTask_GetAwaiter_mF05A09B81913BECFD58FC67A16C0251FFCCAC939,
	UniTask_ToString_m40AB733888F35A7079CDF8B5399A7DD5AC4AC214,
	UniTask_Yield_m3362988BC281D8FBE6DE9D42C3123214920954DE,
	UniTask_NextFrame_mEB306E0E08DD5AFB4841953D92825A43C80B95F4,
	UniTask_DelayFrame_m8D556B751B8A1CF275BDADBDB4B2C7839657FBB7,
	UniTask_Delay_m0F6B9A8CE95AB6C59ACCCF49E96369AB9FAD4960,
	UniTask_Delay_m446244C96F6C971AA44C9F458176F25FCA708F2F,
	UniTask_Delay_mDBE9F54246D1E057EF8528601E2A86E451FB4462,
	UniTask_FromException_mC6AC508C727D591C6BE908F2014570963CE9E5EE,
	NULL,
	NULL,
	UniTask_FromCanceled_mCB25269CC2BD34F255BC5DC67D83C09140C49458,
	NULL,
	UniTask_Create_mEF4619EAAEFE6AC75B95375CA558D14CB0FBDE3B,
	NULL,
	UniTask_Void_m75F7036399F769EA171E65F9635A74220B7E7B29,
	UniTask_Void_m1BD846B7FC6BD19E7BE013D12B8A51237B5C2757,
	UniTask_RunOnThreadPool_m95F4F71334B98E9F18743AAEC7B32342FF7E460D,
	UniTask_SwitchToMainThread_mBC162C95F0ED605F8E5416A60CCA153E0993CBF1,
	UniTask_SwitchToThreadPool_m4A79CBAD5A44C5037CD0A366CA801875F5206010,
	UniTask_WaitUntil_m3CC6559258C98FE64A372B1AE9B7CAEA077D7795,
	NULL,
	UniTask_WhenAll_m9C68DE3D712A9EA42B6711E0D28EE324056137E1,
	UniTask__cctor_m17B980F936F60071A3DD10AD8366298D2886E62A,
	Awaiter__ctor_m4154A3A6D62BB1657D17A8106633CD9E1CE51F04,
	Awaiter_get_IsCompleted_m2D01E3AB8A7C5AA8AA1E1EF58D92A6A095C00B9A,
	Awaiter_GetResult_mC439993563D3BD49CEC67AAF6AFB3AEF72E916BD,
	Awaiter_UnsafeOnCompleted_m6C4775F8F4F2BEFB8118EBBA1EA621440CE84D62,
	Awaiter_SourceOnCompleted_m7A5A045E2ED6F2B0729D11CAC4E18F2BC5B758EB,
	NextFramePromise_get_NextNode_mEBE190751ED87E6ED0EDFF69F341E968AA24DB78,
	NextFramePromise__cctor_mC59005174F368748DA23910F8F585FD33DC09DD3,
	NextFramePromise__ctor_m1A527C69C686250659A816FC4805494B45A451EC,
	NextFramePromise_Create_m33567EC8A25A6FAF09B870BD5086595F57537094,
	NextFramePromise_GetResult_mE381CAAF08DB307E632D789ABB32C9F741840B98,
	NextFramePromise_GetStatus_mC17DE65A641EC520FDD1D794434D7721F11FF046,
	NextFramePromise_UnsafeGetStatus_mB83C781946E1D779D64CEA80E57B5F0949FEB371,
	NextFramePromise_OnCompleted_m9F4A6D33004DB8626614F48665425A8C20693225,
	NextFramePromise_MoveNext_m18BC7278D7DA0160538B12DC8C2F719967D5BE5A,
	NextFramePromise_TryReturn_m9837FA0455BAC3CC26BBBFBAF6CE27A29401AB44,
	U3CU3Ec__cctor_m0E4C35CBFCA70900634AD2C33811BAAF22C02A0D,
	U3CU3Ec__ctor_m293F2D26E7857D0E1954C5AE63C1944A0179F2CA,
	U3CU3Ec_U3C_cctorU3Eb__4_0_m5F46A495AE0371145399A0FAD5D06FBD542DDB2C,
	U3CU3Ec_U3CCreateU3Eb__11_0_mB8D6D0A000F028998510A3BEAAFA346F106AF97D,
	DelayFramePromise_get_NextNode_m5E39E63DFA2B973B50EBEE216FA14EDB2B12CD8B,
	DelayFramePromise__cctor_m370D1F2F63B63E002B9A0B3ECADF2388D682B26F,
	DelayFramePromise__ctor_m654FCF4355DEF52EA1D39E37B55E1456C6BAFE37,
	DelayFramePromise_Create_m1EFF3370EB1B19B6C153C9E5F0FC82179A255D9E,
	DelayFramePromise_GetResult_m47A37A2092283E62B12E3759E6A4CB900C8E95E6,
	DelayFramePromise_GetStatus_m9053C231AC019F2DDDDA4C8B313B35305C6700D2,
	DelayFramePromise_UnsafeGetStatus_m1D921F426F932B27009E65877F4D62A75B9CD0D0,
	DelayFramePromise_OnCompleted_m129580852FA8EA6401095482F31C6A8D0F5D1073,
	DelayFramePromise_MoveNext_mD075C9B35C34B139ECA9E3C02B7F92DF21E50E1A,
	DelayFramePromise_TryReturn_m1AC8FB8392C2A95D8AF55EFB0BD1BCE07EF947FF,
	U3CU3Ec__cctor_mEBB3223756C9BB1FF1ED2B2DD4D705CB2E7AFB89,
	U3CU3Ec__ctor_m6A7F789ECB7D4E3CACD77EE4B335EA08581D097A,
	U3CU3Ec_U3C_cctorU3Eb__4_0_mA2680085CAC8308E14A11913EBB5B4BD317F866C,
	U3CU3Ec_U3CCreateU3Eb__13_0_m921D671755F3675AFD35D98D512D2C79F693933C,
	DelayPromise_get_NextNode_m052F74D1010B856461FFE289BBCE397C23309424,
	DelayPromise__cctor_mB615EC27C3FAB986A9170F6C16AD17090CEBD04E,
	DelayPromise__ctor_m4F76D103946E1BF73889E5176769AEC683C559B0,
	DelayPromise_Create_m891B4E88F71B0718965855E39342115543A9C218,
	DelayPromise_GetResult_m0E215D75987A62E367C1EC94A8D7646FF5422675,
	DelayPromise_GetStatus_mCBF2239EB5E8A1B4D2F2F2A37329CCCA82B8A3C0,
	DelayPromise_UnsafeGetStatus_mBCEED523B78BB889EEE752754011EA847B8CB4C4,
	DelayPromise_OnCompleted_m9E6029B1E08DFC77527BE7977C5AE722D0A822C1,
	DelayPromise_MoveNext_mE40C8EAC8E59FB421A260131A2211A32D18CA297,
	DelayPromise_TryReturn_m5F9BFE6413F89D571880334FD38AE8751BE7D0F8,
	U3CU3Ec__cctor_mB174830645DD5169EFB16D295F5F1C6BF46530D8,
	U3CU3Ec__ctor_mE9D5F68457F1916EC15D9E6655A2DB2EF294FE84,
	U3CU3Ec_U3C_cctorU3Eb__4_0_m46F5B3B989A9FD3E3ACBF47368DD1745B1362394,
	U3CU3Ec_U3CCreateU3Eb__13_0_m51A539073FF4FD5619A020951E05D41039954749,
	DelayIgnoreTimeScalePromise_get_NextNode_m843C0B86113D4572E8A581D19E9A1E681E3378D5,
	DelayIgnoreTimeScalePromise__cctor_m1C95CA9701159FE24C1393FCABD1BE0AF9A2B1A0,
	DelayIgnoreTimeScalePromise__ctor_mA52BF17839EB6AA55F60B1372C8A97BE98807D3B,
	DelayIgnoreTimeScalePromise_Create_mF528197D8221A96419847E0647A31334E04FAA40,
	DelayIgnoreTimeScalePromise_GetResult_mBBAB4F9DDEA466CD879B2FA3B11A52C15A8467E2,
	DelayIgnoreTimeScalePromise_GetStatus_mF1A430774D5AB51EBBC84D6F86D9B6AFC0C5E3AF,
	DelayIgnoreTimeScalePromise_UnsafeGetStatus_m247A75DF579B04129E29E8A3C727290C9D5E696C,
	DelayIgnoreTimeScalePromise_OnCompleted_mB3D8642120A8AE7142FEA205F5207FE5EB89D878,
	DelayIgnoreTimeScalePromise_MoveNext_m219A29F83B062357AE56861AE3450938775A821D,
	DelayIgnoreTimeScalePromise_TryReturn_mC21F9BE7A738D73E622BE6CB44AE1262811B295D,
	U3CU3Ec__cctor_mBDD2DA3503A97010FE47380FD165B1CD288136CB,
	U3CU3Ec__ctor_m03FD2027D47FFA1F391213F7884F7E46DFD8CA0C,
	U3CU3Ec_U3C_cctorU3Eb__4_0_mA9F8A6C82393773CC14D351BDD7A4CF1799B2A18,
	U3CU3Ec_U3CCreateU3Eb__13_0_m1E462B91FD3F600F567F13481C47703F27B33806,
	DelayRealtimePromise_get_NextNode_m930A50FBE32B638FE5C2E52F907A257E4AE8589D,
	DelayRealtimePromise__cctor_m6FAE6BD5140883FB8040431545B92712B3161D6B,
	DelayRealtimePromise__ctor_m03F34287DE1C842E9696417787D446E3C4AD5C51,
	DelayRealtimePromise_Create_m5ACD0D119D883A0D470F3621E983292C96425439,
	DelayRealtimePromise_GetResult_mB593C0EC23F51A291E4534BFFC26736069BC0DF4,
	DelayRealtimePromise_GetStatus_mDAF74B7067AF460C849BE14725B8AD5A33832656,
	DelayRealtimePromise_UnsafeGetStatus_m863E28053457D66C7AE2A26545E6CB206409DAC1,
	DelayRealtimePromise_OnCompleted_m9A69A6B6DEC33B8F10BC758B64F734D51C6AF0A0,
	DelayRealtimePromise_MoveNext_m5C14410A376AC58B3B2807E1F0FDE08C3FC463E7,
	DelayRealtimePromise_TryReturn_mE24471EAFBC0BB4A3E549376E01091C638AAC6FA,
	U3CU3Ec__cctor_m98DFB32E0E467B914148A6A74BE2C8D8DFC32BAE,
	U3CU3Ec__ctor_m9FA833DBE7208035DB659BADDDBDC124731D3B57,
	U3CU3Ec_U3C_cctorU3Eb__4_0_mEB2B3297429136CF4F3D96A546B441B9A657E2E6,
	U3CU3Ec_U3CCreateU3Eb__12_0_mF1E414CDC4C8249C2E062C327E6B567E40E5EAC2,
	NULL,
	ExceptionResultSource__ctor_m73B2CEF2430F981844640DCE7012B4132E24DBE0,
	ExceptionResultSource_GetResult_mB8192735CBF28EBC6F4F7488C5DCAB46BE1A1C17,
	ExceptionResultSource_GetStatus_m250E761F8FE12938E51F82579B3F68D4BA96E8C6,
	ExceptionResultSource_UnsafeGetStatus_mC7E9976510C2EDFAF6DF21DE0B9DE94F2C9D2197,
	ExceptionResultSource_OnCompleted_m3C73A0A4CEC3A10F0C27F389EA32518E9A24283D,
	ExceptionResultSource_Finalize_m55BBC52F3ACE83A060FDE47AD1F2DF8E1FC9507C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CanceledResultSource__ctor_m2EAB6D4A1ADC45B7D902453F249A893CE12B445C,
	CanceledResultSource_GetResult_m18DCE2E7233718125EF31D547AB1ADEF1DED6474,
	CanceledResultSource_GetStatus_mCFE66D2217C82444BC4194FF8551EE3E2D29A615,
	CanceledResultSource_UnsafeGetStatus_m1D5E9C9762A95DC7830D3CAF87AEC2B80250C136,
	CanceledResultSource_OnCompleted_m8ED55915D9776A6948B5C7AF1E6E283194D4B56F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	WaitUntilPromise_get_NextNode_m25D42E4145EDB98482ED8CD0F5C99AE1A244BC7D,
	WaitUntilPromise__cctor_m43C487202B723888E0A42F1311FDD007794F3420,
	WaitUntilPromise__ctor_m56149FCB2DBF5C76BC47CEF57317B29C03943F5D,
	WaitUntilPromise_Create_mACC38596A21B0A893C342B08C92E55CC63FFB9FE,
	WaitUntilPromise_GetResult_mC51A7E70F36B6602CBCD5F1B835B638A15EA77D3,
	WaitUntilPromise_GetStatus_m214277160B9506ADF2CF2F1770796925726A7AC5,
	WaitUntilPromise_UnsafeGetStatus_m204A9B69EECC18F33733EF42E85F9F7675EC990F,
	WaitUntilPromise_OnCompleted_m7845F07B26A7F6B07BD42BF9ABF451390937C0EC,
	WaitUntilPromise_MoveNext_mE00AD4E09916079CBA7B31B7333653EA84B29465,
	WaitUntilPromise_TryReturn_m6B6D551CDB0D63135C8C1D3396FB3DE48F1DEEE9,
	U3CU3Ec__cctor_m9A00EF80F4ACEBB30ECE861D97321E232993EE5A,
	U3CU3Ec__ctor_m64C709F50C2DB563CAF7751E8BF833BA7EB902CD,
	U3CU3Ec_U3C_cctorU3Eb__4_0_mF63DBFB68593D90197816181FE4ACD6921FE2309,
	U3CU3Ec_U3CCreateU3Eb__11_0_mFEE8ADB9105F36D6995C6AC8355BA0FC13D614A1,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	WhenAllPromise__ctor_mA01F0BE4F12F8AD4EF8EE4B1B943255A6F3063B5,
	WhenAllPromise_TryInvokeContinuation_mBCA9E88FEF6A26EFB2FC7461FCBCE32D5FBBCAF9,
	WhenAllPromise_GetResult_mB0BC0BC864A13172B67DA27F4CE9DE3982C41CA9,
	WhenAllPromise_GetStatus_mB2009FB8A026269059469C3843D4E01AFA3E008F,
	WhenAllPromise_UnsafeGetStatus_m249F29D526C9BE6214D898A6846DB6684CC33D41,
	WhenAllPromise_OnCompleted_m709CB570957A5114148641268661446E5E3A662B,
	U3CU3Ec__cctor_mBA016B6C62F33805289E4FC1CDEF1464E8FB37B6,
	U3CU3Ec__ctor_m5DA3340D7019CD399E8C409787EE7F7EA123B1A1,
	U3CU3Ec_U3C_ctorU3Eb__3_0_m2739BAEECD92195E370F1F4CA628035FDDFEDB16,
	U3CU3Ec__cctor_m51CCA72AEF541D67CAD60DB7D0636F332CA62F1C,
	U3CU3Ec__ctor_m9D18525225D440AAFA30EE17A72109811ECD8CB3,
	U3CU3Ec_U3C_cctorU3Eb__202_0_mCBD49FC3A2A646CF0EE526EDA48F53118E207CC9,
	U3CRunOnThreadPoolU3Ed__98__ctor_m9A03B46096EB54FF79706D80077E5E12C8E4B508,
	U3CRunOnThreadPoolU3Ed__98_MoveNext_mB935ABABB0065A5C75EE10417214E7E7EC8ABF5B,
	U3CRunOnThreadPoolU3Ed__98_SetStateMachine_m76E011AB8C050521A3FDA6108BADEBC7CB005FC6,
	AwaiterActions_Continuation_m0EA21AB9CAA150F08C0928928D6F0BA3B6A0D819,
	AwaiterActions__cctor_mCA4E63B7EB17E13FAF457077BC304C9E9863F22D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	YieldAwaitable__ctor_m18D31312C0A37CDC6345D803A3FC55BEDDB7DB8E,
	YieldAwaitable_GetAwaiter_m56B2A4754DC798098A8ABD5DE2E6780BCEB64C7C,
	Awaiter__ctor_m11CEEE91D477321445760BC30E485E54A319E665,
	Awaiter_get_IsCompleted_m380AD66439FAC48688A6348C575A8122F672D61A,
	Awaiter_GetResult_m65B26AEE969C14E1A7010BCDF845982D6EE1A0AB,
	Awaiter_UnsafeOnCompleted_m449B902ED39DDF273B1DC642244CA8B4D3831BE5,
	CompletedTasks__cctor_m9606900578020736B05C78CBC0C93B0D4E574232,
	SwitchToMainThreadAwaitable__ctor_mFCEC101B3A72BE6FEC732DA2B7E7DD98DA6959D1,
	SwitchToMainThreadAwaitable_GetAwaiter_m66A0F6F462885727A38028656D11B51CEA32BD29,
	Awaiter__ctor_m09E7F426390A927742186C32D32668BD079D8D73,
	Awaiter_get_IsCompleted_m8B4E3723A3CC1B1E8359E40C0766081C55A5BC37,
	Awaiter_GetResult_m8383C6B4850150162ECAA6464603B95FD2231CB2,
	Awaiter_UnsafeOnCompleted_m0F8D6A7007F0397899FB1444B09D14912BB48E67,
	SwitchToThreadPoolAwaitable_GetAwaiter_m8720CC348C2B3A1A6CF1317042436D3F8307BD29,
	Awaiter_get_IsCompleted_mC1677DC7707E92D236DF2BA1144FFABF6E45D92E,
	Awaiter_GetResult_mF089C80E7D6C1B3FFA735B7F5B28098339127021,
	Awaiter_UnsafeOnCompleted_mB6FEEBBADB2DEE87A1578C836F09D56A648F1A61,
	Awaiter_Callback_m59E042793983D119D33FED28E23C4F336B0B2904,
	Awaiter__cctor_mED2436D115A1C61C7F5402E1436DAB6DC74C7318,
	ExceptionHolder__ctor_m6DD4EE0F80BC7D72FFAD053891227F046154EC79,
	ExceptionHolder_GetException_m362D89AE3B04CF58E6567EEB499A88B9E6F62B38,
	ExceptionHolder_Finalize_m5D7B937A4BDAA2DAE58B962B84BA69DB8C2BEBBF,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UniTaskCompletionSourceCoreShared_CompletionSentinel_m5F39E3B8DD297E1DE863AA3054199A4DE848FB10,
	UniTaskCompletionSourceCoreShared__cctor_mB49EAFB5151515948642A58C02085755F3BE151D,
	AutoResetUniTaskCompletionSource_get_NextNode_mA074D19C548EA16E1FFD257EB6F5EA01933513C6,
	AutoResetUniTaskCompletionSource__cctor_mA8C85E9ADFC98D8071F5A150215BE0B4BA617EE4,
	AutoResetUniTaskCompletionSource__ctor_m023A15C924FC18E1730265379F5887CE0D91CE60,
	AutoResetUniTaskCompletionSource_Create_m8E2EEAAFD5A50FE30B7F559EF5CC90CF05F675C5,
	AutoResetUniTaskCompletionSource_CreateFromCanceled_mD7DED295D1BC420D4A43D221FB1FF79C87C498A5,
	AutoResetUniTaskCompletionSource_TrySetCanceled_m4E687B95F7447FE8BCC0E639B06106BDC05067F1,
	AutoResetUniTaskCompletionSource_GetResult_m16900E2D7F26185D55213CB06AF2033117551745,
	AutoResetUniTaskCompletionSource_GetStatus_m2F0CBECD04F1D780F9561E3A90B7A3A2BA301D6C,
	AutoResetUniTaskCompletionSource_UnsafeGetStatus_m0204913D767FBA63D0A5D3AEF290283A0AA76AC4,
	AutoResetUniTaskCompletionSource_OnCompleted_m83CC163076722B065F59180B63E78B5C3A362AD3,
	AutoResetUniTaskCompletionSource_TryReturn_mA1B01E7DCA312F6328B8963CD281DFB875517D94,
	U3CU3Ec__cctor_m40EBABC43AF2045B6707DC5DBD8BFCD4F8D9C6CF,
	U3CU3Ec__ctor_mACB75D08D1EB22C42486B215C92917D1CA519700,
	U3CU3Ec_U3C_cctorU3Eb__4_0_m41E50932DCCA162736E8412E858A6116F0CB5075,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UniTaskCompletionSource__ctor_m7C8099F88D8C67FBB3D06EB3C12D852869862FF5,
	UniTaskCompletionSource_MarkHandled_m3B29A0420CA63F7C0590149965C3C107D28F4E5A,
	UniTaskCompletionSource_get_Task_m25CA1E2D00C877FB6266595D70A2A3035DF89EFC,
	UniTaskCompletionSource_TrySetResult_m5C9F347F58580B4D85076121691E0474A1366F84,
	UniTaskCompletionSource_GetResult_m0560DCA9C768570A7AB6B83287922EACAF2FB357,
	UniTaskCompletionSource_GetStatus_m857C8A01E67333FDDB80A3ED56BFE29A281B06CA,
	UniTaskCompletionSource_UnsafeGetStatus_mF4C9873F8275CBCDF281C26A1A9B05BCBBAD4756,
	UniTaskCompletionSource_OnCompleted_mB9171D8E4DF467FCDB0FBE017E0B79C955F83948,
	UniTaskCompletionSource_TrySignalCompletion_m75DFB8A3BAEDCF994432A71268490F1B4BDEE4EE,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UniTaskExtensions_AttachExternalCancellation_mD10FFC9C2946A7D3686FCAD013866A1A30B7CF72,
	UniTaskExtensions_Forget_m8F82202C3DB2020AAE7F874AE049DA711A01DF13,
	AttachExternalCancellationSource__ctor_m318C6ED196B206B316C3B6033BEB0283EE7A0A1B,
	AttachExternalCancellationSource_RunTask_mE2FC8F9F1D5A27A9D927C288441A6E82941108F7,
	AttachExternalCancellationSource_CancellationCallback_m61F958A919D7C5C2D95864D8D4E29BB545E14ADC,
	AttachExternalCancellationSource_GetResult_m1A15200BA02682C399DAE41AF75EE42C2D700A2D,
	AttachExternalCancellationSource_GetStatus_mA44D7922AB8940EC110E63501E126630F0C47143,
	AttachExternalCancellationSource_OnCompleted_m9C74F7923E34369E6978AC5752916F0EE26BBDEF,
	AttachExternalCancellationSource_UnsafeGetStatus_m34D5DCE09C05E6D40275C08C97951AD452782145,
	AttachExternalCancellationSource__cctor_m3D5727BCD3BEB56FAC130E662BF8A7563E199BCE,
	U3CRunTaskU3Ed__5__ctor_m91CDB14104909B839645DCB3F1238BDE8282ABA0,
	U3CRunTaskU3Ed__5_MoveNext_m3344B6864F5B1648AE534EB51AB375A63929322D,
	U3CRunTaskU3Ed__5_SetStateMachine_m676E99B99C852FB1BF9157A3B730FEDCB61D5FE6,
	U3CU3Ec__cctor_m08A505E47B79ABCEDFF5C2D25D80D4CB40940E7D,
	U3CU3Ec__ctor_m6BFBD27DD778E7CC6F178AD0A4ABFB48B7384EC7,
	U3CU3Ec_U3CForgetU3Eb__16_0_mF73782801449F4A8448C6C4AF7CC23058BDE4A11,
	UniTaskScheduler_InvokeUnobservedTaskException_m5E53B569EC1EAAC323B73D8C74A0715DD7FC176C,
	UniTaskScheduler_PublishUnobservedTaskException_m9F6352174ED5654B0A9FB5562B6FF25A788B27D3,
	UniTaskScheduler__cctor_m9D0CDCBB94462B5956764B2DCCAD4D8D654EB2E3,
	UniTaskSynchronizationContext_Run_mC1E290BA942708DC1B8CC4410DFD3FADB8BE720F,
	UniTaskSynchronizationContext__cctor_m2DDCDC914885EDD75D09EA40D7E0EE58BDA79974,
	Callback_Invoke_mCAC6841169B5A04BE53DFD6D11385386EEB0D094,
	UniTaskVoid_Forget_mE4FC2CCCEFD822A5D63FFE97EE209FEE949CC255,
	UnityAsyncExtensions_GetAwaiter_m49ED60AD5B61EF872A63966540B602D59A42EB16,
	UnityAsyncExtensions_ToUniTask_mCC109FEDABBA122F82E454625FAE7303B79DA7B2,
	UnityAsyncExtensions_GetAwaiter_m3B39088BD34AE418D7EA4041E478AF061505F30A,
	UnityAsyncExtensions_GetAwaiter_mA6B6F84DE48BC5EFE2F2BDB1A0011C8A2CD7A920,
	AsyncGPUReadbackRequestAwaiterConfiguredSource_get_NextNode_mB5940E33081DC719B9E58824DD48A8F0406E3937,
	AsyncGPUReadbackRequestAwaiterConfiguredSource__cctor_m5FE6FD13D6D5C57E6933273C60B5F1AFCBFAE8A4,
	AsyncGPUReadbackRequestAwaiterConfiguredSource__ctor_mDA116A552AF93C617A5BDF082EDEC997CA3D9A9D,
	AsyncGPUReadbackRequestAwaiterConfiguredSource_Create_m793EC1215A2924C72AE0A29CE40A0C59BA28C4C1,
	AsyncGPUReadbackRequestAwaiterConfiguredSource_GetResult_m513CAFCF1BA15ED84A503A637F55E3078B331061,
	AsyncGPUReadbackRequestAwaiterConfiguredSource_Cysharp_Threading_Tasks_IUniTaskSource_GetResult_mEF69721A498837775E45A2AA2E4FE9640144E2E0,
	AsyncGPUReadbackRequestAwaiterConfiguredSource_GetStatus_m3C71594800097EBA439E73EEA654BD34050D527E,
	AsyncGPUReadbackRequestAwaiterConfiguredSource_UnsafeGetStatus_mBF9211E24A21E1721B55F98C7DA4ACC242140438,
	AsyncGPUReadbackRequestAwaiterConfiguredSource_OnCompleted_m36FEEAB848812233E33B44B41A4D7FEEE702AC94,
	AsyncGPUReadbackRequestAwaiterConfiguredSource_MoveNext_mC61DFD69CB3382EA7E42F4760C94B7732FA8B09B,
	AsyncGPUReadbackRequestAwaiterConfiguredSource_TryReturn_mBAE97F824E02D72FE274A26BAC0936B1E6844FF8,
	U3CU3Ec__cctor_m79CEE3359A844ED3C9B9DC7BE8F308F3634EE197,
	U3CU3Ec__ctor_m1DEE312956A401007B8CC702277F4B3A39C6EAD4,
	U3CU3Ec_U3C_cctorU3Eb__4_0_m6FD31AB2173D92C38272B40DE10A46A35C1E3CA1,
	U3CU3Ec_U3CCreateU3Eb__11_0_mCFCAA74B1FF46401F058E273B044C802F79BDF95,
	ResourceRequestAwaiter__ctor_m9F612DDCFE50A3CF17BDA678DAA52E7DA5923F86,
	ResourceRequestAwaiter_get_IsCompleted_m797B9071AB9AB8B33167896D6EF9ED10B1C73A10,
	ResourceRequestAwaiter_GetResult_m648D2E45FFE2E55A3FA3175FDF1BCD993CF04068,
	ResourceRequestAwaiter_UnsafeOnCompleted_mA1C3EDBC89B73759B2EBFBD27C81BEE9AA06B8BE,
	UnityWebRequestAsyncOperationAwaiter__ctor_m03AA09B7CDCE9B8CB5534497C41E571370C2164E,
	UnityWebRequestAsyncOperationAwaiter_get_IsCompleted_m7662537975C6DDA7D485E25793703401EA8D04B2,
	UnityWebRequestAsyncOperationAwaiter_GetResult_m96EA9963C39AC42C04D9E35E1D5CF778D5BE8AF5,
	UnityWebRequestAsyncOperationAwaiter_UnsafeOnCompleted_m6137E393610929F40829FD31FAF86196BADE2EDD,
	UnityWebRequestException_get_UnityWebRequest_m48414E7877C61EE4F0F14D78A9624EAFB8BDBF7F,
	UnityWebRequestException_get_Error_mB55A76B44C4D829F7D7FE796C9360B4D56669012,
	UnityWebRequestException_get_Text_m76D36C8F27A1A9D2DADD0EB776C559018E1EC0C5,
	UnityWebRequestException__ctor_m7ED0F69F1E92954B06F391B203CD3045C916E6BC,
	UnityWebRequestException_get_Message_mFB10697207F89F9F5E1EA078602684A14DEDC68C,
	AsyncAwakeTrigger_AwakeAsync_mA879106A5928E69410C51D4C51D056064D0808B4,
	AsyncAwakeTrigger__ctor_m13CF4F7D5B3A40F4B749FEF76710FF7E73D42F97,
	AsyncDestroyTrigger_get_CancellationToken_m279C90D70CAB912D1A8B9B375E3D824FE68F0386,
	AsyncDestroyTrigger_Awake_mCEB1CA097CCF1C5FA59A07E12309FE874C5F0FA0,
	AsyncDestroyTrigger_OnDestroy_m370C10B3F7230D1DFDAA5CDDA4E3757898D4E01D,
	AsyncDestroyTrigger_OnDestroyAsync_mCBF4866C30E2378EB1D8B19F9E1533BCED03B521,
	AsyncDestroyTrigger__ctor_mA31DF14DD9FBA5472DDCF4057CF83B0750CEB6F2,
	AwakeMonitor__ctor_mC99505C3BAD69AB2E58E6652E57AAD4D388F9368,
	AwakeMonitor_MoveNext_mA118D7593A1BC426A3552B5AA6D2CF77D6BBC0B2,
	U3CU3Ec__cctor_mC7BC6D6E13ABC800AED3E133A7A1E3EEF3031F8C,
	U3CU3Ec__ctor_mB92C06E254CDAE1D24DF9A71830865602F90C46B,
	U3CU3Ec_U3COnDestroyAsyncU3Eb__7_0_m6E29C6722B1144387C019ABB11520CF0659E02CE,
	AsyncStartTrigger_Start_m983BE686DB4DB41A5E6FA24DB3966904D23B99FB,
	AsyncStartTrigger_StartAsync_mA31E7C4DD6E90086D49A18B21421AD3A77D0B8F8,
	AsyncStartTrigger__ctor_mE3257932A213645A410A170C9C9E9D2D0F5DB119,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AsyncFixedUpdateTrigger_FixedUpdate_mF6A14CCA456E3B9D7A947BF9813F7C95776D30C5,
	AsyncFixedUpdateTrigger_GetFixedUpdateAsyncHandler_m8A745FAEA32A417B456A6CADA7766DA866681AC5,
	AsyncFixedUpdateTrigger_GetFixedUpdateAsyncHandler_m58788C9C42556CED2BA76D98749945A1ADEDDCC9,
	AsyncFixedUpdateTrigger_FixedUpdateAsync_m5D979BCD337E59EC9FE1FB39B605187895BBE545,
	AsyncFixedUpdateTrigger_FixedUpdateAsync_m96830240B6D94358E752F9CFAA4BE8E2D03EFDD1,
	AsyncFixedUpdateTrigger__ctor_m575B8AF32205F9CFDB158FA9EAD1A1B5BB5F1A79,
	NULL,
	AsyncLateUpdateTrigger_LateUpdate_mA6B8F24DAF03C998920A0D7C200553323110BF1F,
	AsyncLateUpdateTrigger_GetLateUpdateAsyncHandler_m9D707C3809A57961FA4FE524B8AE7D01541490EA,
	AsyncLateUpdateTrigger_GetLateUpdateAsyncHandler_mAB7C4E9D5F3B6F0496C79E6EF7FBB06EBB6184E0,
	AsyncLateUpdateTrigger_LateUpdateAsync_m5E85A71A744AA9301D1C62EFB53CC0ADA025542B,
	AsyncLateUpdateTrigger_LateUpdateAsync_mA0B535B7DB4CA03E645D12FDFC47FA2A355E2D41,
	AsyncLateUpdateTrigger__ctor_mBC6DDF7E599727B32881652E32CBA73CBF9115A4,
	NULL,
	AsyncAnimatorIKTrigger_OnAnimatorIK_m47E70C74254095C9E5E88F7697946A68E515B04B,
	AsyncAnimatorIKTrigger_GetOnAnimatorIKAsyncHandler_m4101EB6425B98841504FECEE8CB8CB764CCE8DA3,
	AsyncAnimatorIKTrigger_GetOnAnimatorIKAsyncHandler_m7115604BA4FBD6F2F250E0B895780C4F69EB83C5,
	AsyncAnimatorIKTrigger_OnAnimatorIKAsync_m6537E9A3E4D35502AC4FF407C134040C03713425,
	AsyncAnimatorIKTrigger_OnAnimatorIKAsync_mCF26414C4D2FE79D00DED9FE9522C46764A158BF,
	AsyncAnimatorIKTrigger__ctor_m1B63E1A5CBDB3267EBD90F7979B1070FA903541D,
	NULL,
	AsyncAnimatorMoveTrigger_OnAnimatorMove_mBA6C81A5C56A10695B4481B945BB64D8872C38B4,
	AsyncAnimatorMoveTrigger_GetOnAnimatorMoveAsyncHandler_m22F8BFBAC4C044ECF0E88D016C2E9CED34D1851D,
	AsyncAnimatorMoveTrigger_GetOnAnimatorMoveAsyncHandler_m9305424D878CEE3DB6D7B2381E1071451C3F7713,
	AsyncAnimatorMoveTrigger_OnAnimatorMoveAsync_m41A7B0AF73557A1173C57608FE397604FAEAD7D7,
	AsyncAnimatorMoveTrigger_OnAnimatorMoveAsync_m9F87CA88EF991EED8B8337CAB7CE81D3EF5014F0,
	AsyncAnimatorMoveTrigger__ctor_mBF01F13DAC18B4ABE22CE693D0B3B8EF1F60B380,
	NULL,
	AsyncApplicationFocusTrigger_OnApplicationFocus_mEAA684300913CF9F1C5D32A6504D8E76A847D054,
	AsyncApplicationFocusTrigger_GetOnApplicationFocusAsyncHandler_m793FFF9249D44EC1F3C1112A8015C911FD2A533A,
	AsyncApplicationFocusTrigger_GetOnApplicationFocusAsyncHandler_mC13600F0A2E02097EDE223C31152BCF9BD3E3E20,
	AsyncApplicationFocusTrigger_OnApplicationFocusAsync_m98C9F34D23B0D2CA3F35EF201D6AF2939492DD35,
	AsyncApplicationFocusTrigger_OnApplicationFocusAsync_m3A0B444A40E6BCE3A45CA93F9E149A6A624B6E2F,
	AsyncApplicationFocusTrigger__ctor_m56399C6629DBB6218C1DD53C6E79506EECC31C70,
	NULL,
	AsyncApplicationPauseTrigger_OnApplicationPause_m0E27E49834F357B643DCCB470514079E80AA985F,
	AsyncApplicationPauseTrigger_GetOnApplicationPauseAsyncHandler_m1E8430BBA86DE783F1FB748AAFFF31E8CA2AEB6A,
	AsyncApplicationPauseTrigger_GetOnApplicationPauseAsyncHandler_m1D042739988163B8E313E4E51C3EFE4DF6778484,
	AsyncApplicationPauseTrigger_OnApplicationPauseAsync_m654318E90645304D0520F0EE392974DFAFD2214F,
	AsyncApplicationPauseTrigger_OnApplicationPauseAsync_m4AB59F5883BFD7BBD1FA4F9A1CAE7730E5F6607C,
	AsyncApplicationPauseTrigger__ctor_m537FFBFCAF1794AE3EE4A6B4F5FEE15B90776DB3,
	NULL,
	AsyncApplicationQuitTrigger_OnApplicationQuit_mA2548998DD2748EBA66904362DD74A44F7BC047B,
	AsyncApplicationQuitTrigger_GetOnApplicationQuitAsyncHandler_m94C94CC41BC238A0FC0004CA30D1757ABBCA988C,
	AsyncApplicationQuitTrigger_GetOnApplicationQuitAsyncHandler_mBB3126503B25EFAF3B1BD7431D9A895A3CE0ADAA,
	AsyncApplicationQuitTrigger_OnApplicationQuitAsync_mA574A29DCA41E38FF455F3ABDC0DA4ADB32031AA,
	AsyncApplicationQuitTrigger_OnApplicationQuitAsync_mBAF1DC0DF0BB9F334FF52F4B454CA8CE63D705D1,
	AsyncApplicationQuitTrigger__ctor_mAB3AA2971E4F1AC0F498BB36991B4BC89DFDAAC3,
	NULL,
	AsyncAudioFilterReadTrigger_OnAudioFilterRead_mEE6E70D75AA076C52394DC62BA2D52DBEA800F46,
	AsyncAudioFilterReadTrigger_GetOnAudioFilterReadAsyncHandler_mCBE6ECB315423D811983CEA1429EC91FE0183BD0,
	AsyncAudioFilterReadTrigger_GetOnAudioFilterReadAsyncHandler_m874FD21CF399F59ACB9D926C78BAFF33C55AE91A,
	AsyncAudioFilterReadTrigger_OnAudioFilterReadAsync_m7395B4A1AA7F8B4F69AC49A9105780F673A4E7A5,
	AsyncAudioFilterReadTrigger_OnAudioFilterReadAsync_m3245EF3204F44449CFD8A46957BB18921D90576B,
	AsyncAudioFilterReadTrigger__ctor_mE79734E5B64F8E8C293E7846D05802983437C849,
	NULL,
	AsyncBecameInvisibleTrigger_OnBecameInvisible_m77D5B938F2A9CAEF0487C46CC7FF61EA8A59DBCA,
	AsyncBecameInvisibleTrigger_GetOnBecameInvisibleAsyncHandler_mF9098EA8CAC854198EF0CFE65903A28AAF308049,
	AsyncBecameInvisibleTrigger_GetOnBecameInvisibleAsyncHandler_m7B35A1825361105BEF7DBC2277192CC197A51266,
	AsyncBecameInvisibleTrigger_OnBecameInvisibleAsync_m751BBA1D3287C5F3A6E3B63139B7CB17F03CF501,
	AsyncBecameInvisibleTrigger_OnBecameInvisibleAsync_m67E44297887780FABB45AA245559ED41831B5E5F,
	AsyncBecameInvisibleTrigger__ctor_m1A90CFB1E58FADF3A9E59E917853B6B68489B6EA,
	NULL,
	AsyncBecameVisibleTrigger_OnBecameVisible_m39B4D78730D757FC1D75C1C52644CC70941FB7DA,
	AsyncBecameVisibleTrigger_GetOnBecameVisibleAsyncHandler_mFA03783E5988CE433AB87B45F7E0D57CEAC057B0,
	AsyncBecameVisibleTrigger_GetOnBecameVisibleAsyncHandler_mC262B84DAABA5688D3294B12CDBCD1B75CCE9ABE,
	AsyncBecameVisibleTrigger_OnBecameVisibleAsync_m0D25209BEEA861B7E2222C2C19C29B26D9D99ADE,
	AsyncBecameVisibleTrigger_OnBecameVisibleAsync_m026198068FA68BF75268F4F9C7571AEA96E52F91,
	AsyncBecameVisibleTrigger__ctor_mDE949643FE2B5C8648944869AF20C5DDF4CA6ACD,
	NULL,
	AsyncBeforeTransformParentChangedTrigger_OnBeforeTransformParentChanged_m942FD6A248C0927849A612B3B9D58CACCB438639,
	AsyncBeforeTransformParentChangedTrigger_GetOnBeforeTransformParentChangedAsyncHandler_m7E1FA6B2D412FFD8F344ADCEA07A43BA96DA1DB5,
	AsyncBeforeTransformParentChangedTrigger_GetOnBeforeTransformParentChangedAsyncHandler_m0FDD80932B47559427028463EE203CA404A374C5,
	AsyncBeforeTransformParentChangedTrigger_OnBeforeTransformParentChangedAsync_m20B316BD908DA444566B5F5762FC6E973E6D45DC,
	AsyncBeforeTransformParentChangedTrigger_OnBeforeTransformParentChangedAsync_mA39BE8BAFB0B77072BB69C4BEE775913058FAF64,
	AsyncBeforeTransformParentChangedTrigger__ctor_m595EB1C1D5EDF1BCD5579E1A39A52B7C212A6B91,
	NULL,
	AsyncOnCanvasGroupChangedTrigger_OnCanvasGroupChanged_m5E5AA51E0A79586D22BC53978B422B60C29F4CF9,
	AsyncOnCanvasGroupChangedTrigger_GetOnCanvasGroupChangedAsyncHandler_m253EF25FCC5505D9120B711BA05B3E7F966E2110,
	AsyncOnCanvasGroupChangedTrigger_GetOnCanvasGroupChangedAsyncHandler_m86882E8DE4E19CE03EBE1FB58D21CD524F5A22CF,
	AsyncOnCanvasGroupChangedTrigger_OnCanvasGroupChangedAsync_mE0612E5A7386D353E12BA7EEB487FC2F3395B984,
	AsyncOnCanvasGroupChangedTrigger_OnCanvasGroupChangedAsync_m6735815EAA4A4DCA91A893DE72AF1BA9F89E578A,
	AsyncOnCanvasGroupChangedTrigger__ctor_m11E656E5FE00E33305782C5E06E1E57CD207015E,
	NULL,
	AsyncCollisionEnterTrigger_OnCollisionEnter_mC90290A1150995FB6D95E50AB4ABB6B7F52D719C,
	AsyncCollisionEnterTrigger_GetOnCollisionEnterAsyncHandler_m247C45C5942DB8A12C63564F051EC8AD078308B4,
	AsyncCollisionEnterTrigger_GetOnCollisionEnterAsyncHandler_m4EF839F8F2C3607541B05E3F9848E0EB55C84F0B,
	AsyncCollisionEnterTrigger_OnCollisionEnterAsync_m741C3CD52C0B86EF4E1DB3C0EC396BA8935C7C75,
	AsyncCollisionEnterTrigger_OnCollisionEnterAsync_m42619646397F338CF53BAD8BB6518E09C83E535A,
	AsyncCollisionEnterTrigger__ctor_mAAB0AA09DB91E5B8EB3AD84B236D5FD49987E3E2,
	NULL,
	AsyncCollisionEnter2DTrigger_OnCollisionEnter2D_m3E85B55BF8C72C63A0202D62724484CB3FF631CE,
	AsyncCollisionEnter2DTrigger_GetOnCollisionEnter2DAsyncHandler_m067D70D1CFB3EF5298D3E83D225F26C7EFFE0EED,
	AsyncCollisionEnter2DTrigger_GetOnCollisionEnter2DAsyncHandler_m20E0140CE385B90D078E99E6D5E86F379E428B6C,
	AsyncCollisionEnter2DTrigger_OnCollisionEnter2DAsync_m3D9DDA2184719C8C03A500AE27EE19B58032549A,
	AsyncCollisionEnter2DTrigger_OnCollisionEnter2DAsync_m2146E39FE5E0A2614BED849635D567F8AF8CB248,
	AsyncCollisionEnter2DTrigger__ctor_m13FEAC1E66AC89B539761206D5A7916823136415,
	NULL,
	AsyncCollisionExitTrigger_OnCollisionExit_m97EC4F1E0B393C79995209073ACD9A091E60781B,
	AsyncCollisionExitTrigger_GetOnCollisionExitAsyncHandler_m8186D12CFB44F748C65208825A3FA261DB66C253,
	AsyncCollisionExitTrigger_GetOnCollisionExitAsyncHandler_m7C3EF37619A4431570D6C78ABC46749A167ECA99,
	AsyncCollisionExitTrigger_OnCollisionExitAsync_mCDD9563AEE1F5FC9EC72640C023DE1AB33533D6B,
	AsyncCollisionExitTrigger_OnCollisionExitAsync_mA3937BEE0AA24AF4630DB6972BD4D2FFA57530C2,
	AsyncCollisionExitTrigger__ctor_m97AE8C6FD5376D4F213B5D4632D2E35BDB0A9224,
	NULL,
	AsyncCollisionExit2DTrigger_OnCollisionExit2D_m144153AF5996E1F82BEF1FC16B45CC3754DDA0EF,
	AsyncCollisionExit2DTrigger_GetOnCollisionExit2DAsyncHandler_mA76718CBFB6194D5D8F297BE027D5FBC9612B427,
	AsyncCollisionExit2DTrigger_GetOnCollisionExit2DAsyncHandler_m74907EB9D54FB94FC016C8D98D09BC45E96AAF37,
	AsyncCollisionExit2DTrigger_OnCollisionExit2DAsync_m36442794A71DDD50892B524BE15CBA351CE0285F,
	AsyncCollisionExit2DTrigger_OnCollisionExit2DAsync_m65FB90CFCCA3C5B55B9653F1734EF15E5B0442DB,
	AsyncCollisionExit2DTrigger__ctor_m17D198C09FE6D957B2CA49752ABA20D6CBF1FDC1,
	NULL,
	AsyncCollisionStayTrigger_OnCollisionStay_mF1B9365D81D464CC568094771EC2F75F5C9EAAFB,
	AsyncCollisionStayTrigger_GetOnCollisionStayAsyncHandler_m0741AA483305011B34A7B387581463F9ECB06F03,
	AsyncCollisionStayTrigger_GetOnCollisionStayAsyncHandler_mA9D6F085925A1E54FBBF4E67035169CCBE7D3ECC,
	AsyncCollisionStayTrigger_OnCollisionStayAsync_m4BC9AADF0B2E3378DFD82378C4CDB2EDDE406ABA,
	AsyncCollisionStayTrigger_OnCollisionStayAsync_m5AEE2CF8B95F133E9C60BE2CC1B36BDCF767CBB9,
	AsyncCollisionStayTrigger__ctor_mD8947EFFDD3E218ABF2699AB5E988E88AD178FF5,
	NULL,
	AsyncCollisionStay2DTrigger_OnCollisionStay2D_m6D15572002ADFFFE16609BA6DD079BBBB7790528,
	AsyncCollisionStay2DTrigger_GetOnCollisionStay2DAsyncHandler_m3B12C82B8B157C5D8DB00637D8EA3090EFE82019,
	AsyncCollisionStay2DTrigger_GetOnCollisionStay2DAsyncHandler_m7CE1B531488691AA819C0521DB49753BD15D76E0,
	AsyncCollisionStay2DTrigger_OnCollisionStay2DAsync_mC8C964A3BA9F5DDBD9FAF1C9694B804A590ABE27,
	AsyncCollisionStay2DTrigger_OnCollisionStay2DAsync_m7815ABF102448305DC42A8510DC8264CFEA60156,
	AsyncCollisionStay2DTrigger__ctor_mD1982A72AF07E99DD6AEF257961027DA768B3815,
	NULL,
	AsyncControllerColliderHitTrigger_OnControllerColliderHit_mCF1DCD15C9B4EA74064F10AE8E1FA624CE2AD37D,
	AsyncControllerColliderHitTrigger_GetOnControllerColliderHitAsyncHandler_m378F060116622AD37BD08CA3198556CD781712EF,
	AsyncControllerColliderHitTrigger_GetOnControllerColliderHitAsyncHandler_m39873EC5A5480262CEC387C010232D683CE14D1B,
	AsyncControllerColliderHitTrigger_OnControllerColliderHitAsync_mC09AC7E1BE67B026FEBB3ACEAEA86270871712C4,
	AsyncControllerColliderHitTrigger_OnControllerColliderHitAsync_m1942830ECFEF930FDB87F0841C46F0E5EC002725,
	AsyncControllerColliderHitTrigger__ctor_mFF59DB3B7D54032EBCDF102E92EC971696BD44EF,
	NULL,
	AsyncDisableTrigger_OnDisable_m94534C41131C1669F1426602DA337AA238896EFE,
	AsyncDisableTrigger_GetOnDisableAsyncHandler_m794E325B17DA7DA1FF1A7CAD87B70E30D3077E49,
	AsyncDisableTrigger_GetOnDisableAsyncHandler_mC3877B53EB7F03676209A3E4CCC4B4E97D1498A0,
	AsyncDisableTrigger_OnDisableAsync_mFA82D467093DFAC6B62B99ABD57E0574FC6886E0,
	AsyncDisableTrigger_OnDisableAsync_m91BA82DDCEDA62211BA3DFE33301196CEB040A68,
	AsyncDisableTrigger__ctor_mFB504DA992CF40D2552F35FBE01F948B68F756F0,
	NULL,
	AsyncDrawGizmosTrigger_OnDrawGizmos_m9EB22D9F78522D9068B32A20A113EB14DF9C36DD,
	AsyncDrawGizmosTrigger_GetOnDrawGizmosAsyncHandler_m63D2B35D915532D716701BA49FB0226B94DAC646,
	AsyncDrawGizmosTrigger_GetOnDrawGizmosAsyncHandler_m9413AA7E3EBD18D3518AAD2DDB9BE015CE92229C,
	AsyncDrawGizmosTrigger_OnDrawGizmosAsync_m995EC266C7D1015C514059494BE85CBEDA5B620C,
	AsyncDrawGizmosTrigger_OnDrawGizmosAsync_mFD0BC0AB40B9D221979EE4581A48C18DE4EFA7CE,
	AsyncDrawGizmosTrigger__ctor_mD0FC881341E8CBA94B1041F048A1B68D3E6F816C,
	NULL,
	AsyncDrawGizmosSelectedTrigger_OnDrawGizmosSelected_m4FB477B43EF05422CCD791ACF7A55EDECD4AA9B8,
	AsyncDrawGizmosSelectedTrigger_GetOnDrawGizmosSelectedAsyncHandler_m45C986C8B39D54C6C9E7419B73B17164BBBC8C2E,
	AsyncDrawGizmosSelectedTrigger_GetOnDrawGizmosSelectedAsyncHandler_m09F6EFC1D44144B50955D9E11FD76BD38AAD0352,
	AsyncDrawGizmosSelectedTrigger_OnDrawGizmosSelectedAsync_mC6E55A356EFB75D71D1F4456B7801350B3F82604,
	AsyncDrawGizmosSelectedTrigger_OnDrawGizmosSelectedAsync_m5756ECE28154E97B36C22294646C49B6E5F9F4BC,
	AsyncDrawGizmosSelectedTrigger__ctor_mA0CD6BF6BB129C0A97496821BBF026E8DBA9E29C,
	NULL,
	AsyncEnableTrigger_OnEnable_m908E830B7C692EFE4B54B9C832A54E31607F986D,
	AsyncEnableTrigger_GetOnEnableAsyncHandler_mF61E39D0BF56E15BFA8BF8C27E7E2003F53BF5C0,
	AsyncEnableTrigger_GetOnEnableAsyncHandler_m5B0348F73BA312A50881087B82AD79EFEB32E326,
	AsyncEnableTrigger_OnEnableAsync_mBC631AE12F5E7AD6F8DEACF51268DD4BDDFFE402,
	AsyncEnableTrigger_OnEnableAsync_mD48C8ADF74405FD0CC02B01C12BFEE344DB6DEDC,
	AsyncEnableTrigger__ctor_m0C149280874A22EC2196AB8620219809AB63C2A5,
	NULL,
	AsyncGUITrigger_OnGUI_mC92580E78E76C2E8D426A17872B82FC1BC5C60C5,
	AsyncGUITrigger_GetOnGUIAsyncHandler_m6062771EF6E369735A64964B7DAFFFB7F4517440,
	AsyncGUITrigger_GetOnGUIAsyncHandler_mAA4F841E9AEAD1D79F46660FA9F8CFA65B3D6A23,
	AsyncGUITrigger_OnGUIAsync_m7DA5A1A31E09F09AB38C601843BCF02F47064844,
	AsyncGUITrigger_OnGUIAsync_mA5894CD3F7E18CF46D69BE1EC7B9DCC9C729EE38,
	AsyncGUITrigger__ctor_mBB359CF9E8B6DDA04A26620EA12AF31FC92B4897,
	NULL,
	AsyncJointBreakTrigger_OnJointBreak_m9F0E2F76C2ADF9F4CDCDB02AD5A463EB768735F4,
	AsyncJointBreakTrigger_GetOnJointBreakAsyncHandler_m4FE53D3C6C604311F08BB08A662A610B68998B10,
	AsyncJointBreakTrigger_GetOnJointBreakAsyncHandler_m45DC4532B62A3A1D63423788546D6431E7735822,
	AsyncJointBreakTrigger_OnJointBreakAsync_m009D08CB70D48911C64EB66EFDFF0F76BDDD76BB,
	AsyncJointBreakTrigger_OnJointBreakAsync_m1A612A9FE46594C7DAD22A9B414DAD775DE79030,
	AsyncJointBreakTrigger__ctor_m0909A04E06E82056B2C1341186AE81DB9C39B6FE,
	NULL,
	AsyncJointBreak2DTrigger_OnJointBreak2D_m67D95AF80CC0BF148CF0F8BEF88F2D8E6A054CCD,
	AsyncJointBreak2DTrigger_GetOnJointBreak2DAsyncHandler_mA8052F3A35DDD34096E7717EDFB7995EC76A9C0E,
	AsyncJointBreak2DTrigger_GetOnJointBreak2DAsyncHandler_m44EF7F400E734AEA3F6D3A1A39D56660E2D6C702,
	AsyncJointBreak2DTrigger_OnJointBreak2DAsync_mEBC2E12DB7B6E809EB4722CEEA53F0FD41A2C9EB,
	AsyncJointBreak2DTrigger_OnJointBreak2DAsync_m6F7E688EC0FFFB5BBD1516D40259DEB9142EAB73,
	AsyncJointBreak2DTrigger__ctor_mF3D16096AFA1D3230787198561D692046D932544,
	NULL,
	AsyncParticleCollisionTrigger_OnParticleCollision_mAB88BF25AF4A07E16A30BB77F515A84916A67F41,
	AsyncParticleCollisionTrigger_GetOnParticleCollisionAsyncHandler_m3B2DC4C2A129E82A2562E054CBA4535529F77E65,
	AsyncParticleCollisionTrigger_GetOnParticleCollisionAsyncHandler_mD6AEC2E8160F2EA131EA13B4E3876D1EB6E1DE32,
	AsyncParticleCollisionTrigger_OnParticleCollisionAsync_m977ED1879F90F1F3312C9FA331C6D931D8DAF5B3,
	AsyncParticleCollisionTrigger_OnParticleCollisionAsync_mB4EE87AFCA7DE53A9430877ABA0B3B14E8F8552F,
	AsyncParticleCollisionTrigger__ctor_m09D7736615C59A039C72CF98DDC64B0B633EB212,
	NULL,
	AsyncParticleSystemStoppedTrigger_OnParticleSystemStopped_m1F15241D8C179E3D910BC60B303DD84C258D5078,
	AsyncParticleSystemStoppedTrigger_GetOnParticleSystemStoppedAsyncHandler_m94B0E325DB6EA714418A87CB3C42F6B1071901FA,
	AsyncParticleSystemStoppedTrigger_GetOnParticleSystemStoppedAsyncHandler_m9DCA68CB73F0DF57D66B67DC7BE5FEFB1BB0A442,
	AsyncParticleSystemStoppedTrigger_OnParticleSystemStoppedAsync_m06CF53CFA48C269D7F80A4BF7722350FC39F6BA3,
	AsyncParticleSystemStoppedTrigger_OnParticleSystemStoppedAsync_mA461791DBBEF11CCD034DBBD8BD65BF4D35699FE,
	AsyncParticleSystemStoppedTrigger__ctor_m904E62BA6E4518A27C3A61763793F4A986687237,
	NULL,
	AsyncParticleTriggerTrigger_OnParticleTrigger_mC208F719B283C4C88E5E3D0482002AECE60839A6,
	AsyncParticleTriggerTrigger_GetOnParticleTriggerAsyncHandler_m1B5E3D0C2ED8646A0DEA6157F5D23F8661D59868,
	AsyncParticleTriggerTrigger_GetOnParticleTriggerAsyncHandler_m9A51E8F1188B8BF257C24DE7FDE8E1C1E58F1DED,
	AsyncParticleTriggerTrigger_OnParticleTriggerAsync_m47DD183AE5AAF8A99A00B2DE0CC2A586CF108EA3,
	AsyncParticleTriggerTrigger_OnParticleTriggerAsync_m7046FA3D6B4C6515C363F5E9D47E96B85E6D6235,
	AsyncParticleTriggerTrigger__ctor_m8269A35ABC71140B7ED5723DE6B568474D057D59,
	NULL,
	AsyncParticleUpdateJobScheduledTrigger_OnParticleUpdateJobScheduled_m74A99E942E46AF80E4AB4C5D1A157F6BB51712C9,
	AsyncParticleUpdateJobScheduledTrigger_GetOnParticleUpdateJobScheduledAsyncHandler_m819FB3F20036AA9189B0CFA747FFB650A57446D1,
	AsyncParticleUpdateJobScheduledTrigger_GetOnParticleUpdateJobScheduledAsyncHandler_m95774A8666C72D0C828F1FE08F1852FBDCB3A9EF,
	AsyncParticleUpdateJobScheduledTrigger_OnParticleUpdateJobScheduledAsync_m5EEE4CF5485107ECAC80DB74A4381EADE5286BDD,
	AsyncParticleUpdateJobScheduledTrigger_OnParticleUpdateJobScheduledAsync_m058A419713F55D7889713AB79C086D583B0D8A06,
	AsyncParticleUpdateJobScheduledTrigger__ctor_mBDC5B1EF5960A431B5C7856FE2736C10B7EA523D,
	NULL,
	AsyncPostRenderTrigger_OnPostRender_mBB072ADCE5F2C449BA8B3F6C8D484AAE1E17CE7E,
	AsyncPostRenderTrigger_GetOnPostRenderAsyncHandler_m743EC696BF3015D8CA610F472266FF15A72BFF61,
	AsyncPostRenderTrigger_GetOnPostRenderAsyncHandler_m0BBA36AF26E020488A4E2ECDE93D628C7823A73F,
	AsyncPostRenderTrigger_OnPostRenderAsync_mE0663A68C7806C983847686964623C26EA9F6E3C,
	AsyncPostRenderTrigger_OnPostRenderAsync_m9CE060A1050D16FB7A719A3A33991DD6B4AF2A15,
	AsyncPostRenderTrigger__ctor_mB9C91EFA8FD7309B8DADF3C2090E4881367D48BC,
	NULL,
	AsyncPreCullTrigger_OnPreCull_m8BB8ACA22D72CB75007C5654A7E4FCD2CB5C9734,
	AsyncPreCullTrigger_GetOnPreCullAsyncHandler_mBCC91746CF122BAC372FD27DCA0D4EA0165F9652,
	AsyncPreCullTrigger_GetOnPreCullAsyncHandler_mE4F6D965AC6FFBF05F6C20203DF56F6AAA2E5242,
	AsyncPreCullTrigger_OnPreCullAsync_m66EE45765FFE039BA71E16A52EE0C464AA45D2D0,
	AsyncPreCullTrigger_OnPreCullAsync_m01A58406C404E6E55C241DA3BE5710A2A16CCB02,
	AsyncPreCullTrigger__ctor_mAA11EB2F80B462E9201611C9D15AD0E8A043D2A6,
	NULL,
	AsyncPreRenderTrigger_OnPreRender_m1079D7D822CF19494538DEF22ECE2B81634F41EC,
	AsyncPreRenderTrigger_GetOnPreRenderAsyncHandler_m1D65A52A4D45E69417D37C2692821BA8E1B89C72,
	AsyncPreRenderTrigger_GetOnPreRenderAsyncHandler_mECD1216D87565C260D8C61A0C43774F65334B7DD,
	AsyncPreRenderTrigger_OnPreRenderAsync_mB0AC4368868514881C7149CA779F08A8C493A1E4,
	AsyncPreRenderTrigger_OnPreRenderAsync_mC67896394616B2796F138221AD88EB64BDBF707F,
	AsyncPreRenderTrigger__ctor_m58745C27B119C73D861348FA50F2A17FB9954779,
	NULL,
	AsyncRectTransformDimensionsChangeTrigger_OnRectTransformDimensionsChange_mCEF7EEA734C490FF4F183ECC88426EBB60E0E895,
	AsyncRectTransformDimensionsChangeTrigger_GetOnRectTransformDimensionsChangeAsyncHandler_m335E5F74ED9D1861F3E4941A7571BA6834C26A57,
	AsyncRectTransformDimensionsChangeTrigger_GetOnRectTransformDimensionsChangeAsyncHandler_mF2C56261FF948B05C2B1190532CF5D0E0BCE8BBF,
	AsyncRectTransformDimensionsChangeTrigger_OnRectTransformDimensionsChangeAsync_m302946011A50A6C701DB03FBFC432A417242DF76,
	AsyncRectTransformDimensionsChangeTrigger_OnRectTransformDimensionsChangeAsync_mFDE718FC057B3AAECE4C52CE347B34834C90CCCA,
	AsyncRectTransformDimensionsChangeTrigger__ctor_m1C4CC310B246DDE5A43CB79B23FAC9FADABE39BF,
	NULL,
	AsyncRectTransformRemovedTrigger_OnRectTransformRemoved_mBD06842C7B34DEDA6C64DC7903FB43ADE3E9B85F,
	AsyncRectTransformRemovedTrigger_GetOnRectTransformRemovedAsyncHandler_mD12641FE032A87B2ED5A831357FC37F8AC27432E,
	AsyncRectTransformRemovedTrigger_GetOnRectTransformRemovedAsyncHandler_m127633818EC8250B74CA466B200398E4D6A1475D,
	AsyncRectTransformRemovedTrigger_OnRectTransformRemovedAsync_m3EC5F120E02890FB4B5988379B80AFB98CE84328,
	AsyncRectTransformRemovedTrigger_OnRectTransformRemovedAsync_m7F129D367F5850EFB5D71B755C0335C9C6F12222,
	AsyncRectTransformRemovedTrigger__ctor_m0F2DE34A26744D66C7DB55CE0229E268BFDD88FF,
	NULL,
	AsyncRenderImageTrigger_OnRenderImage_mDCDB76BCE6EBFF2096A7EAC73E4F72D04C8E3350,
	AsyncRenderImageTrigger_GetOnRenderImageAsyncHandler_m9D537F7B59F54A3E3BE60EFC59C76958754B3252,
	AsyncRenderImageTrigger_GetOnRenderImageAsyncHandler_mE3D255982895A770D66E08000126D277B359AC6B,
	AsyncRenderImageTrigger_OnRenderImageAsync_m91CE6C7DF51D427CCF19CD19ADD13EFA9499017C,
	AsyncRenderImageTrigger_OnRenderImageAsync_m139CF203844E7CFBD54334949DC18E12BF30B33E,
	AsyncRenderImageTrigger__ctor_mFED9370CF549990CEF799FECF0D4C7323FE55D00,
	NULL,
	AsyncRenderObjectTrigger_OnRenderObject_mE0614C8F30A97D4FA5DB7CE04ACEAA0DA7875D2A,
	AsyncRenderObjectTrigger_GetOnRenderObjectAsyncHandler_m616C95392EB27750CA278FE63F260C9F818FA406,
	AsyncRenderObjectTrigger_GetOnRenderObjectAsyncHandler_m1E01EEC9E77863DDC00CA301D90B319C8B1539D4,
	AsyncRenderObjectTrigger_OnRenderObjectAsync_m2AFD6381ED39F073EB4E7F50EA51EE086CE01FA4,
	AsyncRenderObjectTrigger_OnRenderObjectAsync_m89DAA18E4F271AFE4B0624A2B0387399EB43F96F,
	AsyncRenderObjectTrigger__ctor_m02F4B33BE8DE504FB664BB90953AEA86FFE5A7C1,
	NULL,
	AsyncServerInitializedTrigger_OnServerInitialized_mE7C5A6B7CA9DD4A9621C1D7D4A0AA5502D94BFDC,
	AsyncServerInitializedTrigger_GetOnServerInitializedAsyncHandler_mF9892ACE15C075BD1B64100FAE38CF05F6B15ADE,
	AsyncServerInitializedTrigger_GetOnServerInitializedAsyncHandler_m230CF255A8EE1403BCF8F749DD60CDA1095DF9FE,
	AsyncServerInitializedTrigger_OnServerInitializedAsync_mBD83933D204A9ABD56169E7A2304DB00F228D952,
	AsyncServerInitializedTrigger_OnServerInitializedAsync_mB94CCF570EEAC3E75874E715CFBE103E5B938AED,
	AsyncServerInitializedTrigger__ctor_m4903A1D3EDFA9556D5071D691C86DCDF55A0BAD4,
	NULL,
	AsyncTransformChildrenChangedTrigger_OnTransformChildrenChanged_mB766B3AAE2AC504FAE655FBAAE91DBA5C60E5A72,
	AsyncTransformChildrenChangedTrigger_GetOnTransformChildrenChangedAsyncHandler_mAC29370E010951C99D0005DE0FBD845C9B27E8B2,
	AsyncTransformChildrenChangedTrigger_GetOnTransformChildrenChangedAsyncHandler_mFA04E5A68C238110A9965CBDFE44754E81052048,
	AsyncTransformChildrenChangedTrigger_OnTransformChildrenChangedAsync_m9A6C0B58EB7B02D87BE90EE1F38AD776AB98E3F5,
	AsyncTransformChildrenChangedTrigger_OnTransformChildrenChangedAsync_m05B67060C03105BCD88618E48F83EF87757B9FE4,
	AsyncTransformChildrenChangedTrigger__ctor_m6487135C4F4E451F55010BBB53427F684253E40B,
	NULL,
	AsyncTransformParentChangedTrigger_OnTransformParentChanged_m9F023397C4145C57ADDF27B30C56591447862AF7,
	AsyncTransformParentChangedTrigger_GetOnTransformParentChangedAsyncHandler_m792911E5D33ACD6F6D8F39342FD052E1063F3A6E,
	AsyncTransformParentChangedTrigger_GetOnTransformParentChangedAsyncHandler_mFD20DFC05628802928961500AC716CF15EC01F28,
	AsyncTransformParentChangedTrigger_OnTransformParentChangedAsync_m2A042FDD8DA120540E678D620910F18754F21BB6,
	AsyncTransformParentChangedTrigger_OnTransformParentChangedAsync_m3400D30019581CC110E95B9BE88786A57BF8EC06,
	AsyncTransformParentChangedTrigger__ctor_m08BA52ADB268DB9B1047B6D0A10EFFC62E216216,
	NULL,
	AsyncTriggerEnterTrigger_OnTriggerEnter_m987B0631561DADCF50ADAB4025D0DA37F044BF0D,
	AsyncTriggerEnterTrigger_GetOnTriggerEnterAsyncHandler_mDC42AE523AD77E05A9781A7DB689B4F47E521A9F,
	AsyncTriggerEnterTrigger_GetOnTriggerEnterAsyncHandler_mF9D3C729AB281CFFDD286CDDBCE64796D1D4C8CE,
	AsyncTriggerEnterTrigger_OnTriggerEnterAsync_m70124C83C761D35FDCA94667EEA1E460F5F02E9E,
	AsyncTriggerEnterTrigger_OnTriggerEnterAsync_mFA17FE0A71CA365A213934E9781F61A043AAF9EC,
	AsyncTriggerEnterTrigger__ctor_m94291F492F7F5FC615902E4843F0C12309930A88,
	NULL,
	AsyncTriggerEnter2DTrigger_OnTriggerEnter2D_m20C6705138B9F2021D39EB3B19F2974E0B6254D7,
	AsyncTriggerEnter2DTrigger_GetOnTriggerEnter2DAsyncHandler_mE5922E4095D7E338378986A873F216A26652AEF4,
	AsyncTriggerEnter2DTrigger_GetOnTriggerEnter2DAsyncHandler_m364D016D0754472AC213AFF988075A26AD92FE5C,
	AsyncTriggerEnter2DTrigger_OnTriggerEnter2DAsync_m648D60D922B491745048C6AF56F91E6457A82410,
	AsyncTriggerEnter2DTrigger_OnTriggerEnter2DAsync_m053C5553A5DDAFE6E2B48E150CB8E01EF59CB859,
	AsyncTriggerEnter2DTrigger__ctor_m2C867B03B0CC792FC2D0A4429B4B69B7F3CBFDBB,
	NULL,
	AsyncTriggerExitTrigger_OnTriggerExit_mC9140851D54908E8587502C7093FC0E9478E5F0A,
	AsyncTriggerExitTrigger_GetOnTriggerExitAsyncHandler_mD62C3917B65B910FCCD1A28C49C01987641F37D5,
	AsyncTriggerExitTrigger_GetOnTriggerExitAsyncHandler_m450CE5E9C1698DD90AB559FE25DA709C2F840CDB,
	AsyncTriggerExitTrigger_OnTriggerExitAsync_mF399CA1A0F46E3406260F520E9C0CB9273E719B1,
	AsyncTriggerExitTrigger_OnTriggerExitAsync_m25707BB67EADA30383AB48021BF2BC0DB74FC6D7,
	AsyncTriggerExitTrigger__ctor_m02D41654EAA534FEC8206D55AB3B916CE3A975D9,
	NULL,
	AsyncTriggerExit2DTrigger_OnTriggerExit2D_mD2347A86823FA31E88FD0EB0A2FE9A194C11FBE3,
	AsyncTriggerExit2DTrigger_GetOnTriggerExit2DAsyncHandler_m15A74FF40FCCEF701589E5005613915384BE3139,
	AsyncTriggerExit2DTrigger_GetOnTriggerExit2DAsyncHandler_m62886134FFCA01612A5559BEE2DA14A253C38FCA,
	AsyncTriggerExit2DTrigger_OnTriggerExit2DAsync_mF4AFE6532D7E39B0649E2CFFE24F562A56D8EB14,
	AsyncTriggerExit2DTrigger_OnTriggerExit2DAsync_m8FDF4429E6483153FC537DE0E107D59F05A7250F,
	AsyncTriggerExit2DTrigger__ctor_m6C158090D92C5521AAB46AF22CE7D5A249628F29,
	NULL,
	AsyncTriggerStayTrigger_OnTriggerStay_mECE592D3892C2113D169D009C149F9F14821752B,
	AsyncTriggerStayTrigger_GetOnTriggerStayAsyncHandler_mF28681278F440C6BBF1FEA5654C7BC17C30E5354,
	AsyncTriggerStayTrigger_GetOnTriggerStayAsyncHandler_m20F92E48CFAF36C1750D7A8F5BD901736EEA4CBE,
	AsyncTriggerStayTrigger_OnTriggerStayAsync_m00C665C6B369753660BBA9081F8183DB34ECE2A0,
	AsyncTriggerStayTrigger_OnTriggerStayAsync_mE2FDCCC2D53DD23037BDC52303B103DDB1944481,
	AsyncTriggerStayTrigger__ctor_mA8262B94799FEDAC48D3A5D67F3A7EAE6D17F5F2,
	NULL,
	AsyncTriggerStay2DTrigger_OnTriggerStay2D_m1F524CE082AA4BD8DF7344C4A7B544900875F591,
	AsyncTriggerStay2DTrigger_GetOnTriggerStay2DAsyncHandler_m84FBB5668D312A20B079DB05E82F8D7861839BD9,
	AsyncTriggerStay2DTrigger_GetOnTriggerStay2DAsyncHandler_m6CABA94980326BCFC2E8C0FC5D22CF2B0F162DC8,
	AsyncTriggerStay2DTrigger_OnTriggerStay2DAsync_m4BFC56AB458AE530CE94324E0ABF30DCBACA80DB,
	AsyncTriggerStay2DTrigger_OnTriggerStay2DAsync_mB378ACE64845B39578647EA254C7CB721AB53615,
	AsyncTriggerStay2DTrigger__ctor_m0C2F47161B6AB02BB517175952CE5073D768C8BB,
	NULL,
	AsyncValidateTrigger_OnValidate_m6D87C365C451119F965AE3C3B26A5BEF18D64F28,
	AsyncValidateTrigger_GetOnValidateAsyncHandler_m93B61B438CAEC00A57683BE865F3037C7AAB18D1,
	AsyncValidateTrigger_GetOnValidateAsyncHandler_m8ECBBB8FDB07F67B8ECA8DE67535E4A1617D1082,
	AsyncValidateTrigger_OnValidateAsync_m5B4930FBBC3B5415906EE63F2AF118E985271BE6,
	AsyncValidateTrigger_OnValidateAsync_mDC784A5D55B69D304DAE67DCD4FEAC7BB05AEC56,
	AsyncValidateTrigger__ctor_m8E06D8B7DF5CEFAFFE794462F1DE83062242B16A,
	NULL,
	AsyncWillRenderObjectTrigger_OnWillRenderObject_m000349C12C77A792F5F0E03B28BAB5163AD2621C,
	AsyncWillRenderObjectTrigger_GetOnWillRenderObjectAsyncHandler_m21C0854CAFDAD22FA260F242FDA37EEF0864C37B,
	AsyncWillRenderObjectTrigger_GetOnWillRenderObjectAsyncHandler_m5A987A306765AA901E6BBB4C65FCE2D015CD8052,
	AsyncWillRenderObjectTrigger_OnWillRenderObjectAsync_mE9FE145E988136CB40D676F0491AF3BA419A22C0,
	AsyncWillRenderObjectTrigger_OnWillRenderObjectAsync_mA5CA68CD354E1B84275BB203B343B60E46F4B87A,
	AsyncWillRenderObjectTrigger__ctor_m24D430A90CF1A74E75C0EE37DDF463A827E69036,
	NULL,
	AsyncResetTrigger_Reset_m198B4882AE86300DBC5B07C9AF4CF49BAE6152E0,
	AsyncResetTrigger_GetResetAsyncHandler_mF8F9B51C0C643F4F5BFA1229AF9EDA31BDC849F5,
	AsyncResetTrigger_GetResetAsyncHandler_mAB5FFC1283DEC76FBE41C065FC98BBCB53B90A33,
	AsyncResetTrigger_ResetAsync_mC38F95492FF08D8253115CC0D2D33217CAA601C7,
	AsyncResetTrigger_ResetAsync_m1763D6AF5E377D44258B6489EC23145DFA710E16,
	AsyncResetTrigger__ctor_mDA674611F8438A002072FD58EAA5F47782697BCF,
	NULL,
	AsyncUpdateTrigger_Update_m5321BA60C550332ECB74B05D2D26DD2017095123,
	AsyncUpdateTrigger_GetUpdateAsyncHandler_m90503A9E45D8228631B2D988799F3FDCD041E6E7,
	AsyncUpdateTrigger_GetUpdateAsyncHandler_m3B4493D31EB552A7FE6FFF7AE24E4EA241009E7B,
	AsyncUpdateTrigger_UpdateAsync_m41D031BFCC576B4606888C87DF3217C1F63CD659,
	AsyncUpdateTrigger_UpdateAsync_mD324C9094A568ECCFCB9DBBF3A351699CC5E853E,
	AsyncUpdateTrigger__ctor_m154BA59C03623508B967B4A7428889E7AAFF06BD,
	NULL,
	AsyncBeginDragTrigger_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mA03ABC0CE73F6A81EAB2A2DC01314DE1C75017D0,
	AsyncBeginDragTrigger_GetOnBeginDragAsyncHandler_mCC84469272DB1F26A36D79D4D2B17C70A5F51B4C,
	AsyncBeginDragTrigger_GetOnBeginDragAsyncHandler_m622C2389C34EA474603912204A1D889951B82B3C,
	AsyncBeginDragTrigger_OnBeginDragAsync_m4539156944A7986CF33AF89BAE8836828D67344A,
	AsyncBeginDragTrigger_OnBeginDragAsync_mFF453D3CFFF6F6C27944FA69AEFEA22B4A0C085B,
	AsyncBeginDragTrigger__ctor_m93C1E7BF275BF2740582CDD00D5273EA7890A97D,
	NULL,
	AsyncCancelTrigger_UnityEngine_EventSystems_ICancelHandler_OnCancel_m7F923EAA95A3805F736D193A81F94EBEB009E85C,
	AsyncCancelTrigger_GetOnCancelAsyncHandler_m5222CED3A8988662208821CCE5FC48C624234073,
	AsyncCancelTrigger_GetOnCancelAsyncHandler_m18ED65769DE2FD370AA935E0E80B03403AFCBEF4,
	AsyncCancelTrigger_OnCancelAsync_m5274E06A1AF76DA4CE71A076F86604D9ED0933B4,
	AsyncCancelTrigger_OnCancelAsync_m3DCBAE1F4D50620EBB5436AFECD94FAB2249C9A0,
	AsyncCancelTrigger__ctor_m91A3511B78449BDF1C667C6D033116499D2B6F01,
	NULL,
	AsyncDeselectTrigger_UnityEngine_EventSystems_IDeselectHandler_OnDeselect_mF0115B94B991F030AC4065B62EE18DCF858BBB80,
	AsyncDeselectTrigger_GetOnDeselectAsyncHandler_m6F292094CE1EFB741AF4359044A81B3B2118EA99,
	AsyncDeselectTrigger_GetOnDeselectAsyncHandler_mCAA51997A06FB65D34E552530879C37FD132CE4F,
	AsyncDeselectTrigger_OnDeselectAsync_m5F99FAC049CD6D4BBD119AF594ADCBE5BB8AAB0F,
	AsyncDeselectTrigger_OnDeselectAsync_mACA85079A579109733B7CBD73C373B9F106AF043,
	AsyncDeselectTrigger__ctor_m88193D405ABE43B9A86FA98574B267D7050B09D8,
	NULL,
	AsyncDragTrigger_UnityEngine_EventSystems_IDragHandler_OnDrag_mE6EE89FD4B8C55B6CAD8CDD583E98085803CA5BB,
	AsyncDragTrigger_GetOnDragAsyncHandler_mE9A088371AED15D78320931AE777F175EC267B3E,
	AsyncDragTrigger_GetOnDragAsyncHandler_mC62B15B6DB1524F512DF26ED66E4630D0B594FDB,
	AsyncDragTrigger_OnDragAsync_mFE1C88E879C8620A7411C2AFDF2A7CB656F92C17,
	AsyncDragTrigger_OnDragAsync_mDE183DAF1CB58E17D55FEFDC418043A73D8B93D9,
	AsyncDragTrigger__ctor_m4246DA4A79FC5B4EE7AF2D40D8E61C2C557BC3BB,
	NULL,
	AsyncDropTrigger_UnityEngine_EventSystems_IDropHandler_OnDrop_m6EC469C5DDE8F5DFA56DC574CB30A0C899A3BE79,
	AsyncDropTrigger_GetOnDropAsyncHandler_mC7BC09849A0A3EEBCC9A49992C3F85CBC99D40F9,
	AsyncDropTrigger_GetOnDropAsyncHandler_mD131FC037DAD9C1375DC8B6338B3F7DE488B1592,
	AsyncDropTrigger_OnDropAsync_m24E0BFD04C65108867E6C2DDCB2E0C77316FFBD3,
	AsyncDropTrigger_OnDropAsync_m667E704E1DC358927FF27D820DBCA73F48A8E860,
	AsyncDropTrigger__ctor_m52B026578FEC5CADE8EC07012219195372042C75,
	NULL,
	AsyncEndDragTrigger_UnityEngine_EventSystems_IEndDragHandler_OnEndDrag_mA6E208817F267F75434FB20CC4A2C1656FA72CDA,
	AsyncEndDragTrigger_GetOnEndDragAsyncHandler_m8DD5DC103EF92681BA60966BCCF3C0ED2022E8F1,
	AsyncEndDragTrigger_GetOnEndDragAsyncHandler_m85F4AFDCE4CEC2FBCE4E33BB482C499EAFCEE29C,
	AsyncEndDragTrigger_OnEndDragAsync_m3FA7DFA354A3AFF9179AC97017A153AF33CB9674,
	AsyncEndDragTrigger_OnEndDragAsync_m33CD963F2D8E25C6631DC5D3B570A369146AD160,
	AsyncEndDragTrigger__ctor_m6AD8D6387589C775E66148AC3C327418FC273B79,
	NULL,
	AsyncInitializePotentialDragTrigger_UnityEngine_EventSystems_IInitializePotentialDragHandler_OnInitializePotentialDrag_m29C011CBE259A2772D34FED167FDE9B5F19E3E7E,
	AsyncInitializePotentialDragTrigger_GetOnInitializePotentialDragAsyncHandler_m79E90E8346E05A21414B2F052BDD2624BBA5214E,
	AsyncInitializePotentialDragTrigger_GetOnInitializePotentialDragAsyncHandler_mD09078E1E80CEAFD30E5EB8054FF9F02BD3A21BE,
	AsyncInitializePotentialDragTrigger_OnInitializePotentialDragAsync_mB8A6588663519DE5E66BF691194393CE7F9C3301,
	AsyncInitializePotentialDragTrigger_OnInitializePotentialDragAsync_m2670ACE4FA88A59573E089FF56FEEB9F6DD6AAEC,
	AsyncInitializePotentialDragTrigger__ctor_mA856ED893AE01C6799E3BBBDBDCF32C80F692952,
	NULL,
	AsyncMoveTrigger_UnityEngine_EventSystems_IMoveHandler_OnMove_m106A6DE0A40BF1992095328994E9B25266C07A19,
	AsyncMoveTrigger_GetOnMoveAsyncHandler_m4DB3879B5B3F1A9DA814A7D9789137FFB11F76AF,
	AsyncMoveTrigger_GetOnMoveAsyncHandler_m4CCAD0773D4AE9E657E196A3A6FD66E7B3EDCEE9,
	AsyncMoveTrigger_OnMoveAsync_m2ABBCFD97CD0ECF6D3E6220D87F67281E50DD278,
	AsyncMoveTrigger_OnMoveAsync_m0488637FE9AA347498D6060A49FE93E3A501AF2F,
	AsyncMoveTrigger__ctor_m45E3B69860382856E11BA62527F019FE069804B5,
	NULL,
	AsyncPointerClickTrigger_UnityEngine_EventSystems_IPointerClickHandler_OnPointerClick_mE04300FDF07CDE0FE4428BDE950A72AC000EF30B,
	AsyncPointerClickTrigger_GetOnPointerClickAsyncHandler_mA566665DF7714C32E9BD7B1DF530F57BAC6F26E0,
	AsyncPointerClickTrigger_GetOnPointerClickAsyncHandler_m73077567C1FAA022B7A090B98E6781CADAAB1F61,
	AsyncPointerClickTrigger_OnPointerClickAsync_mFAB255C3C4797C7B60655C035B2938C8FDCCA33F,
	AsyncPointerClickTrigger_OnPointerClickAsync_m5B4E2978071A425B3229AC1AD264DA796396E9B1,
	AsyncPointerClickTrigger__ctor_m0BC216A191100A4553FB7C5E5CB02283EF95AFF1,
	NULL,
	AsyncPointerDownTrigger_UnityEngine_EventSystems_IPointerDownHandler_OnPointerDown_m538BA1B2A4BC460886C9BA40233C3D8CF718FF9E,
	AsyncPointerDownTrigger_GetOnPointerDownAsyncHandler_m688F1EEF4459FBB17E2EE3A94E90B8B44F3939A3,
	AsyncPointerDownTrigger_GetOnPointerDownAsyncHandler_mE523D00A201D7AF98DFE605A1A5CAC33724913F7,
	AsyncPointerDownTrigger_OnPointerDownAsync_m940E470426C0E2CDF91094036B4DABD1A903FB1D,
	AsyncPointerDownTrigger_OnPointerDownAsync_m97F16BD60F67D92B5558B54048C6C8F6AAB8BDDC,
	AsyncPointerDownTrigger__ctor_m47AC62324159AC99D02E8F2C03DC532249DFA205,
	NULL,
	AsyncPointerEnterTrigger_UnityEngine_EventSystems_IPointerEnterHandler_OnPointerEnter_m9CC1764FFE744F7DA3CD0948EEB78F853F1DA263,
	AsyncPointerEnterTrigger_GetOnPointerEnterAsyncHandler_m71BA530662B463CB6E38A453CF5B7C8F08A7D8F6,
	AsyncPointerEnterTrigger_GetOnPointerEnterAsyncHandler_mFD5431F7A7A6F56B58C28C80A3B29B659B6B4F0B,
	AsyncPointerEnterTrigger_OnPointerEnterAsync_mB2D312F1E56A2A670BBA8C588A92A557881C6E8A,
	AsyncPointerEnterTrigger_OnPointerEnterAsync_mA69CAFE10318A3360E997B8B9F8095FDA6D8F4A4,
	AsyncPointerEnterTrigger__ctor_mCD8B949EDDFBCAB60C7BBCD7B3B14126E077004A,
	NULL,
	AsyncPointerExitTrigger_UnityEngine_EventSystems_IPointerExitHandler_OnPointerExit_m21FF9CEAB6518F47CA763B4359549C60BD121545,
	AsyncPointerExitTrigger_GetOnPointerExitAsyncHandler_mB9F1108DBFF5AAB0607750BFC5E30F7C72D1D678,
	AsyncPointerExitTrigger_GetOnPointerExitAsyncHandler_mA6A4970B7FD37ED5AA4860FDDF229D100B44AF24,
	AsyncPointerExitTrigger_OnPointerExitAsync_m05F7D5E6C6642002650ADF4048B312642167023B,
	AsyncPointerExitTrigger_OnPointerExitAsync_mCA9B7E46E233C86EED631B0999DC5DF6341190E7,
	AsyncPointerExitTrigger__ctor_m4402450531D2F20D61BEEC8817CCAE3F32953F5D,
	NULL,
	AsyncPointerUpTrigger_UnityEngine_EventSystems_IPointerUpHandler_OnPointerUp_m70F177ACD1FDED11A2B3EB06F5FEF5E91C181286,
	AsyncPointerUpTrigger_GetOnPointerUpAsyncHandler_mEE2547442026FD5C03D78557B257DBC4B3F4AD16,
	AsyncPointerUpTrigger_GetOnPointerUpAsyncHandler_m5F89720EA1F24FFE6C3309394CC2232D7C0DE006,
	AsyncPointerUpTrigger_OnPointerUpAsync_mB8C833F75C04322D0640D6C7D81B62BF62F4755C,
	AsyncPointerUpTrigger_OnPointerUpAsync_m018A9D9AD3D0E676FBC6C0527968EE819731F495,
	AsyncPointerUpTrigger__ctor_m45B519521DCA4CD711890353781ACE67B46D0901,
	NULL,
	AsyncScrollTrigger_UnityEngine_EventSystems_IScrollHandler_OnScroll_m8945007EA695538E62EBFBFFC76BEB3184D847ED,
	AsyncScrollTrigger_GetOnScrollAsyncHandler_mEDD87166B0C11F3111B0465DA7BAF874F82F77B6,
	AsyncScrollTrigger_GetOnScrollAsyncHandler_m04D9951AFA1FF263081D2A2F60707519121183AA,
	AsyncScrollTrigger_OnScrollAsync_mDE615A9D7E7C7BA53FA03A3143A11DA1641A7467,
	AsyncScrollTrigger_OnScrollAsync_m20AC4D84B89B0375ACC0EA9411C9840CAF738EC6,
	AsyncScrollTrigger__ctor_m6C7411E7DE5ABE67A459D5BEE2C1233ACCC81CE7,
	NULL,
	AsyncSelectTrigger_UnityEngine_EventSystems_ISelectHandler_OnSelect_m2E2DE234586ECA1CE685D841BEBCB702159C398D,
	AsyncSelectTrigger_GetOnSelectAsyncHandler_mF048D4E0FB6FCC1B396D4266FC51AEA684077C12,
	AsyncSelectTrigger_GetOnSelectAsyncHandler_m617C2B56B8A4EC8FCFFB1D60C6C36708CD4A9796,
	AsyncSelectTrigger_OnSelectAsync_mA234129DF821D8705973E57C1F1B817AB844F3FA,
	AsyncSelectTrigger_OnSelectAsync_m58D691420266A3E01918667F41D35239011CDC60,
	AsyncSelectTrigger__ctor_m9961CCBC188ED442EF801485D0A070950BDB9242,
	NULL,
	AsyncSubmitTrigger_UnityEngine_EventSystems_ISubmitHandler_OnSubmit_m13EE18C4B9DDA7E0AD97CD2B0AF83538C7A62480,
	AsyncSubmitTrigger_GetOnSubmitAsyncHandler_m5048EDDE331533E46A0FD4D74F4AFF3E4B0D3527,
	AsyncSubmitTrigger_GetOnSubmitAsyncHandler_mD358FE71567F3E4E158B0875F0DBD2CA78BE5047,
	AsyncSubmitTrigger_OnSubmitAsync_mC989E95E688D41DB759E34C9D01AF6E85142A9A9,
	AsyncSubmitTrigger_OnSubmitAsync_m499D26BBA1CCA12C56EAB4AC2CBE850AF26D63FC,
	AsyncSubmitTrigger__ctor_mBEFC6E37237735EE8E1A4D077C64F09EB0B7F83F,
	NULL,
	AsyncUpdateSelectedTrigger_UnityEngine_EventSystems_IUpdateSelectedHandler_OnUpdateSelected_m751BCA6DC0D3FB832F14A44DCC7F08FA36B83403,
	AsyncUpdateSelectedTrigger_GetOnUpdateSelectedAsyncHandler_m91B0AD3DBC4A601FFEA58763B6B5FC676E380B76,
	AsyncUpdateSelectedTrigger_GetOnUpdateSelectedAsyncHandler_mD0E63D5FED30DEAAD37F4224036EBB11B6A2A0CF,
	AsyncUpdateSelectedTrigger_OnUpdateSelectedAsync_mB5E2B84F5C14E42D61801287414C513AF9306F1E,
	AsyncUpdateSelectedTrigger_OnUpdateSelectedAsync_m348EF605036779545BF1F5393BB5AB74A5C01D77,
	AsyncUpdateSelectedTrigger__ctor_m8F8F7CEBEE75BD238702F928EE7D434AE11F7448,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ContinuationQueue__ctor_m2691F2E47B45071E1D748B12BFCEA7003A4A2349,
	ContinuationQueue_Enqueue_m802882C7B05D37A530F9E81453259CD2A8FAAF16,
	ContinuationQueue_Run_mAF0D9BE1DE591406CF6E9E7AE723BFF6FB248027,
	ContinuationQueue_Initialization_mCEE8AC1829A03B2346961EF03F5A6CF781D49CCA,
	ContinuationQueue_LastInitialization_m0A14A82C3FC05EAB16A2F53B67FE20F7A5AA9BA9,
	ContinuationQueue_EarlyUpdate_mE2D343C81C97676F58EBB14AD0D0166BD342C90E,
	ContinuationQueue_LastEarlyUpdate_m3B9F214C3BA142D1C09865F15C5A85D637121DAA,
	ContinuationQueue_FixedUpdate_m31E37797C020DC852FC8493DBAC90A9C46EE1CBC,
	ContinuationQueue_LastFixedUpdate_mCC4FC1FD84E95AA48F4200F4A3EE5FF8E92AAF94,
	ContinuationQueue_PreUpdate_mE93C2E72DC0FA847BD8310F67FCEDFEDBE43BE33,
	ContinuationQueue_LastPreUpdate_m970552416F8418461AF2707BB9FA52B764B6D632,
	ContinuationQueue_Update_mF437F847D02FB8DE1BFB309D8C6A04C824FD198F,
	ContinuationQueue_LastUpdate_mE0155228A6BCC874D777A668FF642C72AD2B8A6B,
	ContinuationQueue_PreLateUpdate_m8DF6791BC638D689D4C37ED4221A0FFCCAAD3B71,
	ContinuationQueue_LastPreLateUpdate_m4DE23C52241C21064CB12C63C9F23F19A9A7942B,
	ContinuationQueue_PostLateUpdate_m55D35665E9FA975C9B2EB4CEA9B5D1E3E802D7F0,
	ContinuationQueue_LastPostLateUpdate_mBC3FDF1E9B650F49F9DE9A58C3B0E67CCD676DCD,
	ContinuationQueue_TimeUpdate_m95DC65E6A08364B3326E2B0D1E2007EA218942C6,
	ContinuationQueue_LastTimeUpdate_m2FFF647992655828559CD332BD4BFD7893941CDE,
	ContinuationQueue_RunCore_m3CF0C93FCEC17B9E879B2A31FE46A9846C77EFEC,
	NULL,
	Error_ThrowArgumentNullExceptionCore_m3FF7A528380B19AF3E6A08B2CD103556B0A94779,
	NULL,
	Error_ThrowInvalidOperationExceptionCore_m5BEDE591893014DF989C54DFB3D8B060252C1A31,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PlayerLoopRunner__ctor_m2D6695A577F21178AB92E93FDF465CE37ECA28E1,
	PlayerLoopRunner_AddAction_mC0968BDEF4B70D3CBF59AF574D87DDB51982236D,
	PlayerLoopRunner_Run_m33748216201A3D6575D14D23450C03068FD1729B,
	PlayerLoopRunner_Initialization_m261886B5EE4FE447AC0D9970A2611CB15F30A3C0,
	PlayerLoopRunner_LastInitialization_mD60239EE16381604AF1F898F321BD9B5DBFF5BF7,
	PlayerLoopRunner_EarlyUpdate_mA17D05AE2D41AC0AC8FBA6A6F887022B5919E181,
	PlayerLoopRunner_LastEarlyUpdate_mF015AACB9D25CF340986B968F77384A0A2975DFD,
	PlayerLoopRunner_FixedUpdate_m46665E9C1FDA461847D4EED290F11C8704631F67,
	PlayerLoopRunner_LastFixedUpdate_mB77A68F437A2A4CA9152CB80F638A12195DEB08E,
	PlayerLoopRunner_PreUpdate_m346C48450A6B63EE2147DF3A902008BF932FD649,
	PlayerLoopRunner_LastPreUpdate_mE6D4A4211F5D699CA52F229DEF02D14C435CBB49,
	PlayerLoopRunner_Update_mE9FAAF206931C3575A7D84FEFC50CE119EEC9E9E,
	PlayerLoopRunner_LastUpdate_mA6E740086E42031FAEDF12D6D29F5ECFCA8E36B8,
	PlayerLoopRunner_PreLateUpdate_m9A5A64617B89F735E29217E1DFA546E5F058BF14,
	PlayerLoopRunner_LastPreLateUpdate_m46D83DE50CE91101E177611F90491B2C97FB94F1,
	PlayerLoopRunner_PostLateUpdate_mC2235D8AA5C890A2DED85B058A8226284CD0F03C,
	PlayerLoopRunner_LastPostLateUpdate_mB141860C1D02EE72D1BA5FAD550A9EEBB3A2A583,
	PlayerLoopRunner_TimeUpdate_m78113C468F4D9542AF991E290672ABF89DA0B8FD,
	PlayerLoopRunner_LastTimeUpdate_m34D7531369689AE25CBC54466A711086336EE94F,
	PlayerLoopRunner_RunCore_m053B5E8652893AA94B9CD56C64484CA49D01153A,
	U3CU3Ec__cctor_mE23C6BAF80DDC97AC0299B07D5E5EBEDAD7AE1C3,
	U3CU3Ec__ctor_mEC8E674F3A7E554B6C09782BE4023E8DDA0F7E9E,
	U3CU3Ec_U3C_ctorU3Eb__9_0_m7FD5E476CB2AB74C2AA83C5A0A68796DF976B7A9,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	RuntimeHelpersAbstraction_WellKnownNoReferenceContainsTypeInitialize_mD3611FAEF828BA7D67B3B447050F697AA89CB364,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnityWebRequestResultExtensions_IsError_m1E1CC890AF2527FABD6B6AD5BD6A25FEF0604310,
	ValueStopwatch_StartNew_m0E86DE08F588C60DCA8982B16DB6949C4A93233F,
	ValueStopwatch__ctor_mF4323A30F6F82FAAA7C2C943EE2E708A2E482D68,
	ValueStopwatch_get_IsInvalid_mE2AFBDC1DEB255E72DE87B2BD2C7EA8BFDCDBDF1,
	ValueStopwatch_get_ElapsedTicks_mF4B713799E06F9F82066A6AB557F419F21C18E40,
	ValueStopwatch__cctor_m57CAAF73C5CED4A7B8A02C11494C2D632AD7DAC1,
	AsyncUniTaskMethodBuilder_Create_m882DFD5EE004FF6F84F182A3AC170AEDCA8C5327,
	AsyncUniTaskMethodBuilder_get_Task_m11E43D69C4F85EB23AAC58143DE695309D1022EC,
	AsyncUniTaskMethodBuilder_SetException_m0D772D62D01CC371F4AB0F6943BBBE0FEAB19643,
	AsyncUniTaskMethodBuilder_SetResult_mBBA527F0F21E04D65A269C0D02597CE5B2D1E9CB,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AsyncUniTaskVoidMethodBuilder_Create_m06E63ABF318CBA8C8DE8BA460E94C6E9CDB5ED64,
	AsyncUniTaskVoidMethodBuilder_get_Task_mA5D58CCC3268985A789B48F67492123752CE7A98,
	AsyncUniTaskVoidMethodBuilder_SetException_m7492DE9DAE6EEC3DA2D1792A7B937F6CF61C6EB5,
	AsyncUniTaskVoidMethodBuilder_SetResult_m12336C298D2F8CED07AAB85C3DD67815E8343947,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
extern void AsyncUnit_GetHashCode_mEA99722071116F872F4FD9397FCE42319B9C3407_AdjustorThunk (void);
extern void AsyncUnit_Equals_m4FA66A2168F60FBACE2254A0D6F1513BC8111389_AdjustorThunk (void);
extern void AsyncUnit_ToString_m41E4FD5E5F3F3FCBC1C00EB15CDFA98EC9CDE3DF_AdjustorThunk (void);
extern void UniTask__ctor_m5CBDA4598A89633256BF98AB40B3BA549A4CF84F_AdjustorThunk (void);
extern void UniTask_get_Status_mA15B0F13DE3CE36730357CF50F65AE99ADF564DA_AdjustorThunk (void);
extern void UniTask_GetAwaiter_mF05A09B81913BECFD58FC67A16C0251FFCCAC939_AdjustorThunk (void);
extern void UniTask_ToString_m40AB733888F35A7079CDF8B5399A7DD5AC4AC214_AdjustorThunk (void);
extern void Awaiter__ctor_m4154A3A6D62BB1657D17A8106633CD9E1CE51F04_AdjustorThunk (void);
extern void Awaiter_get_IsCompleted_m2D01E3AB8A7C5AA8AA1E1EF58D92A6A095C00B9A_AdjustorThunk (void);
extern void Awaiter_GetResult_mC439993563D3BD49CEC67AAF6AFB3AEF72E916BD_AdjustorThunk (void);
extern void Awaiter_UnsafeOnCompleted_m6C4775F8F4F2BEFB8118EBBA1EA621440CE84D62_AdjustorThunk (void);
extern void Awaiter_SourceOnCompleted_m7A5A045E2ED6F2B0729D11CAC4E18F2BC5B758EB_AdjustorThunk (void);
extern void YieldAwaitable__ctor_m18D31312C0A37CDC6345D803A3FC55BEDDB7DB8E_AdjustorThunk (void);
extern void YieldAwaitable_GetAwaiter_m56B2A4754DC798098A8ABD5DE2E6780BCEB64C7C_AdjustorThunk (void);
extern void Awaiter__ctor_m11CEEE91D477321445760BC30E485E54A319E665_AdjustorThunk (void);
extern void Awaiter_get_IsCompleted_m380AD66439FAC48688A6348C575A8122F672D61A_AdjustorThunk (void);
extern void Awaiter_GetResult_m65B26AEE969C14E1A7010BCDF845982D6EE1A0AB_AdjustorThunk (void);
extern void Awaiter_UnsafeOnCompleted_m449B902ED39DDF273B1DC642244CA8B4D3831BE5_AdjustorThunk (void);
extern void SwitchToMainThreadAwaitable__ctor_mFCEC101B3A72BE6FEC732DA2B7E7DD98DA6959D1_AdjustorThunk (void);
extern void SwitchToMainThreadAwaitable_GetAwaiter_m66A0F6F462885727A38028656D11B51CEA32BD29_AdjustorThunk (void);
extern void Awaiter__ctor_m09E7F426390A927742186C32D32668BD079D8D73_AdjustorThunk (void);
extern void Awaiter_get_IsCompleted_m8B4E3723A3CC1B1E8359E40C0766081C55A5BC37_AdjustorThunk (void);
extern void Awaiter_GetResult_m8383C6B4850150162ECAA6464603B95FD2231CB2_AdjustorThunk (void);
extern void Awaiter_UnsafeOnCompleted_m0F8D6A7007F0397899FB1444B09D14912BB48E67_AdjustorThunk (void);
extern void SwitchToThreadPoolAwaitable_GetAwaiter_m8720CC348C2B3A1A6CF1317042436D3F8307BD29_AdjustorThunk (void);
extern void Awaiter_get_IsCompleted_mC1677DC7707E92D236DF2BA1144FFABF6E45D92E_AdjustorThunk (void);
extern void Awaiter_GetResult_mF089C80E7D6C1B3FFA735B7F5B28098339127021_AdjustorThunk (void);
extern void Awaiter_UnsafeOnCompleted_mB6FEEBBADB2DEE87A1578C836F09D56A648F1A61_AdjustorThunk (void);
extern void Callback_Invoke_mCAC6841169B5A04BE53DFD6D11385386EEB0D094_AdjustorThunk (void);
extern void UniTaskVoid_Forget_mE4FC2CCCEFD822A5D63FFE97EE209FEE949CC255_AdjustorThunk (void);
extern void ResourceRequestAwaiter__ctor_m9F612DDCFE50A3CF17BDA678DAA52E7DA5923F86_AdjustorThunk (void);
extern void ResourceRequestAwaiter_get_IsCompleted_m797B9071AB9AB8B33167896D6EF9ED10B1C73A10_AdjustorThunk (void);
extern void ResourceRequestAwaiter_GetResult_m648D2E45FFE2E55A3FA3175FDF1BCD993CF04068_AdjustorThunk (void);
extern void ResourceRequestAwaiter_UnsafeOnCompleted_mA1C3EDBC89B73759B2EBFBD27C81BEE9AA06B8BE_AdjustorThunk (void);
extern void UnityWebRequestAsyncOperationAwaiter__ctor_m03AA09B7CDCE9B8CB5534497C41E571370C2164E_AdjustorThunk (void);
extern void UnityWebRequestAsyncOperationAwaiter_get_IsCompleted_m7662537975C6DDA7D485E25793703401EA8D04B2_AdjustorThunk (void);
extern void UnityWebRequestAsyncOperationAwaiter_GetResult_m96EA9963C39AC42C04D9E35E1D5CF778D5BE8AF5_AdjustorThunk (void);
extern void UnityWebRequestAsyncOperationAwaiter_UnsafeOnCompleted_m6137E393610929F40829FD31FAF86196BADE2EDD_AdjustorThunk (void);
extern void ValueStopwatch__ctor_mF4323A30F6F82FAAA7C2C943EE2E708A2E482D68_AdjustorThunk (void);
extern void ValueStopwatch_get_IsInvalid_mE2AFBDC1DEB255E72DE87B2BD2C7EA8BFDCDBDF1_AdjustorThunk (void);
extern void ValueStopwatch_get_ElapsedTicks_mF4B713799E06F9F82066A6AB557F419F21C18E40_AdjustorThunk (void);
extern void AsyncUniTaskMethodBuilder_get_Task_m11E43D69C4F85EB23AAC58143DE695309D1022EC_AdjustorThunk (void);
extern void AsyncUniTaskMethodBuilder_SetException_m0D772D62D01CC371F4AB0F6943BBBE0FEAB19643_AdjustorThunk (void);
extern void AsyncUniTaskMethodBuilder_SetResult_mBBA527F0F21E04D65A269C0D02597CE5B2D1E9CB_AdjustorThunk (void);
extern void AsyncUniTaskVoidMethodBuilder_get_Task_mA5D58CCC3268985A789B48F67492123752CE7A98_AdjustorThunk (void);
extern void AsyncUniTaskVoidMethodBuilder_SetException_m7492DE9DAE6EEC3DA2D1792A7B937F6CF61C6EB5_AdjustorThunk (void);
extern void AsyncUniTaskVoidMethodBuilder_SetResult_m12336C298D2F8CED07AAB85C3DD67815E8343947_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[47] = 
{
	{ 0x06000002, AsyncUnit_GetHashCode_mEA99722071116F872F4FD9397FCE42319B9C3407_AdjustorThunk },
	{ 0x06000003, AsyncUnit_Equals_m4FA66A2168F60FBACE2254A0D6F1513BC8111389_AdjustorThunk },
	{ 0x06000004, AsyncUnit_ToString_m41E4FD5E5F3F3FCBC1C00EB15CDFA98EC9CDE3DF_AdjustorThunk },
	{ 0x06000051, UniTask__ctor_m5CBDA4598A89633256BF98AB40B3BA549A4CF84F_AdjustorThunk },
	{ 0x06000052, UniTask_get_Status_mA15B0F13DE3CE36730357CF50F65AE99ADF564DA_AdjustorThunk },
	{ 0x06000053, UniTask_GetAwaiter_mF05A09B81913BECFD58FC67A16C0251FFCCAC939_AdjustorThunk },
	{ 0x06000054, UniTask_ToString_m40AB733888F35A7079CDF8B5399A7DD5AC4AC214_AdjustorThunk },
	{ 0x0600006B, Awaiter__ctor_m4154A3A6D62BB1657D17A8106633CD9E1CE51F04_AdjustorThunk },
	{ 0x0600006C, Awaiter_get_IsCompleted_m2D01E3AB8A7C5AA8AA1E1EF58D92A6A095C00B9A_AdjustorThunk },
	{ 0x0600006D, Awaiter_GetResult_mC439993563D3BD49CEC67AAF6AFB3AEF72E916BD_AdjustorThunk },
	{ 0x0600006E, Awaiter_UnsafeOnCompleted_m6C4775F8F4F2BEFB8118EBBA1EA621440CE84D62_AdjustorThunk },
	{ 0x0600006F, Awaiter_SourceOnCompleted_m7A5A045E2ED6F2B0729D11CAC4E18F2BC5B758EB_AdjustorThunk },
	{ 0x06000102, YieldAwaitable__ctor_m18D31312C0A37CDC6345D803A3FC55BEDDB7DB8E_AdjustorThunk },
	{ 0x06000103, YieldAwaitable_GetAwaiter_m56B2A4754DC798098A8ABD5DE2E6780BCEB64C7C_AdjustorThunk },
	{ 0x06000104, Awaiter__ctor_m11CEEE91D477321445760BC30E485E54A319E665_AdjustorThunk },
	{ 0x06000105, Awaiter_get_IsCompleted_m380AD66439FAC48688A6348C575A8122F672D61A_AdjustorThunk },
	{ 0x06000106, Awaiter_GetResult_m65B26AEE969C14E1A7010BCDF845982D6EE1A0AB_AdjustorThunk },
	{ 0x06000107, Awaiter_UnsafeOnCompleted_m449B902ED39DDF273B1DC642244CA8B4D3831BE5_AdjustorThunk },
	{ 0x06000109, SwitchToMainThreadAwaitable__ctor_mFCEC101B3A72BE6FEC732DA2B7E7DD98DA6959D1_AdjustorThunk },
	{ 0x0600010A, SwitchToMainThreadAwaitable_GetAwaiter_m66A0F6F462885727A38028656D11B51CEA32BD29_AdjustorThunk },
	{ 0x0600010B, Awaiter__ctor_m09E7F426390A927742186C32D32668BD079D8D73_AdjustorThunk },
	{ 0x0600010C, Awaiter_get_IsCompleted_m8B4E3723A3CC1B1E8359E40C0766081C55A5BC37_AdjustorThunk },
	{ 0x0600010D, Awaiter_GetResult_m8383C6B4850150162ECAA6464603B95FD2231CB2_AdjustorThunk },
	{ 0x0600010E, Awaiter_UnsafeOnCompleted_m0F8D6A7007F0397899FB1444B09D14912BB48E67_AdjustorThunk },
	{ 0x0600010F, SwitchToThreadPoolAwaitable_GetAwaiter_m8720CC348C2B3A1A6CF1317042436D3F8307BD29_AdjustorThunk },
	{ 0x06000110, Awaiter_get_IsCompleted_mC1677DC7707E92D236DF2BA1144FFABF6E45D92E_AdjustorThunk },
	{ 0x06000111, Awaiter_GetResult_mF089C80E7D6C1B3FFA735B7F5B28098339127021_AdjustorThunk },
	{ 0x06000112, Awaiter_UnsafeOnCompleted_mB6FEEBBADB2DEE87A1578C836F09D56A648F1A61_AdjustorThunk },
	{ 0x0600016A, Callback_Invoke_mCAC6841169B5A04BE53DFD6D11385386EEB0D094_AdjustorThunk },
	{ 0x0600016B, UniTaskVoid_Forget_mE4FC2CCCEFD822A5D63FFE97EE209FEE949CC255_AdjustorThunk },
	{ 0x0600017F, ResourceRequestAwaiter__ctor_m9F612DDCFE50A3CF17BDA678DAA52E7DA5923F86_AdjustorThunk },
	{ 0x06000180, ResourceRequestAwaiter_get_IsCompleted_m797B9071AB9AB8B33167896D6EF9ED10B1C73A10_AdjustorThunk },
	{ 0x06000181, ResourceRequestAwaiter_GetResult_m648D2E45FFE2E55A3FA3175FDF1BCD993CF04068_AdjustorThunk },
	{ 0x06000182, ResourceRequestAwaiter_UnsafeOnCompleted_mA1C3EDBC89B73759B2EBFBD27C81BEE9AA06B8BE_AdjustorThunk },
	{ 0x06000183, UnityWebRequestAsyncOperationAwaiter__ctor_m03AA09B7CDCE9B8CB5534497C41E571370C2164E_AdjustorThunk },
	{ 0x06000184, UnityWebRequestAsyncOperationAwaiter_get_IsCompleted_m7662537975C6DDA7D485E25793703401EA8D04B2_AdjustorThunk },
	{ 0x06000185, UnityWebRequestAsyncOperationAwaiter_GetResult_m96EA9963C39AC42C04D9E35E1D5CF778D5BE8AF5_AdjustorThunk },
	{ 0x06000186, UnityWebRequestAsyncOperationAwaiter_UnsafeOnCompleted_m6137E393610929F40829FD31FAF86196BADE2EDD_AdjustorThunk },
	{ 0x0600043D, ValueStopwatch__ctor_mF4323A30F6F82FAAA7C2C943EE2E708A2E482D68_AdjustorThunk },
	{ 0x0600043E, ValueStopwatch_get_IsInvalid_mE2AFBDC1DEB255E72DE87B2BD2C7EA8BFDCDBDF1_AdjustorThunk },
	{ 0x0600043F, ValueStopwatch_get_ElapsedTicks_mF4B713799E06F9F82066A6AB557F419F21C18E40_AdjustorThunk },
	{ 0x06000442, AsyncUniTaskMethodBuilder_get_Task_m11E43D69C4F85EB23AAC58143DE695309D1022EC_AdjustorThunk },
	{ 0x06000443, AsyncUniTaskMethodBuilder_SetException_m0D772D62D01CC371F4AB0F6943BBBE0FEAB19643_AdjustorThunk },
	{ 0x06000444, AsyncUniTaskMethodBuilder_SetResult_mBBA527F0F21E04D65A269C0D02597CE5B2D1E9CB_AdjustorThunk },
	{ 0x0600044E, AsyncUniTaskVoidMethodBuilder_get_Task_mA5D58CCC3268985A789B48F67492123752CE7A98_AdjustorThunk },
	{ 0x0600044F, AsyncUniTaskVoidMethodBuilder_SetException_m7492DE9DAE6EEC3DA2D1792A7B937F6CF61C6EB5_AdjustorThunk },
	{ 0x06000450, AsyncUniTaskVoidMethodBuilder_SetResult_m12336C298D2F8CED07AAB85C3DD67815E8343947_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1166] = 
{
	3881,
	4216,
	3092,
	4250,
	9089,
	8887,
	6421,
	8887,
	9089,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3414,
	3851,
	1446,
	0,
	0,
	0,
	0,
	0,
	0,
	8216,
	3165,
	3414,
	2080,
	4216,
	3851,
	4364,
	0,
	9031,
	9018,
	8993,
	5024,
	6710,
	8506,
	9089,
	7489,
	4582,
	7897,
	7932,
	8880,
	7932,
	9089,
	9089,
	4364,
	3196,
	3196,
	3196,
	4364,
	3196,
	9089,
	7975,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	2795,
	4216,
	4426,
	4250,
	9091,
	9082,
	6050,
	5438,
	5439,
	5440,
	8818,
	0,
	0,
	8817,
	0,
	8818,
	0,
	8887,
	7964,
	6819,
	8747,
	9069,
	6051,
	0,
	8818,
	9089,
	3788,
	4168,
	4364,
	3881,
	2802,
	4151,
	9089,
	4364,
	5910,
	3851,
	3414,
	4216,
	2080,
	4168,
	4168,
	9089,
	4364,
	4216,
	3881,
	4151,
	9089,
	4364,
	5335,
	3851,
	3414,
	4216,
	2080,
	4168,
	4168,
	9089,
	4364,
	4216,
	3881,
	4151,
	9089,
	4364,
	5395,
	3851,
	3414,
	4216,
	2080,
	4168,
	4168,
	9089,
	4364,
	4216,
	3881,
	4151,
	9089,
	4364,
	5395,
	3851,
	3414,
	4216,
	2080,
	4168,
	4168,
	9089,
	4364,
	4216,
	3881,
	4151,
	9089,
	4364,
	5395,
	3851,
	3414,
	4216,
	2080,
	4168,
	4168,
	9089,
	4364,
	4216,
	3881,
	0,
	3881,
	3851,
	3414,
	4216,
	2080,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3810,
	3851,
	3414,
	4216,
	2080,
	0,
	0,
	0,
	0,
	0,
	0,
	4151,
	9089,
	4364,
	5353,
	3851,
	3414,
	4216,
	2080,
	4168,
	4168,
	9089,
	4364,
	4216,
	3881,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	2796,
	7961,
	3851,
	3414,
	4216,
	2080,
	9089,
	4364,
	3881,
	9089,
	4364,
	4352,
	4364,
	4364,
	3881,
	8887,
	9089,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3852,
	4430,
	3852,
	4168,
	4364,
	3881,
	9089,
	2730,
	4417,
	2730,
	4168,
	4364,
	3881,
	4418,
	4168,
	4364,
	3881,
	8887,
	9089,
	3881,
	4250,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8887,
	9089,
	4151,
	9089,
	4364,
	9031,
	7579,
	3117,
	3851,
	3414,
	4216,
	2080,
	4168,
	9089,
	4364,
	4216,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	4364,
	4352,
	4168,
	3851,
	3414,
	4216,
	2080,
	3166,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7831,
	8906,
	2879,
	3650,
	8887,
	3851,
	3414,
	2080,
	4216,
	9089,
	4364,
	4364,
	3881,
	9089,
	4364,
	3881,
	8887,
	8887,
	9089,
	9089,
	9089,
	4364,
	4364,
	8082,
	5606,
	8954,
	8955,
	4151,
	9089,
	4364,
	5328,
	3034,
	3851,
	3414,
	4216,
	2080,
	4168,
	4168,
	9089,
	4364,
	4216,
	3881,
	3881,
	4168,
	4250,
	3881,
	3881,
	4168,
	4250,
	3881,
	4250,
	4250,
	4250,
	3881,
	4250,
	4352,
	4364,
	4170,
	4364,
	4364,
	4352,
	4364,
	3881,
	4168,
	9089,
	4364,
	3881,
	4364,
	4352,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	3852,
	4250,
	3504,
	4135,
	2995,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	3807,
	4250,
	3504,
	4127,
	2983,
	4364,
	0,
	3807,
	4250,
	3504,
	4127,
	2983,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	2796,
	4250,
	3504,
	4123,
	2978,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	3881,
	4250,
	3504,
	4130,
	2988,
	4364,
	0,
	3881,
	4250,
	3504,
	4131,
	2989,
	4364,
	0,
	3881,
	4250,
	3504,
	4130,
	2988,
	4364,
	0,
	3881,
	4250,
	3504,
	4131,
	2989,
	4364,
	0,
	3881,
	4250,
	3504,
	4130,
	2988,
	4364,
	0,
	3881,
	4250,
	3504,
	4131,
	2989,
	4364,
	0,
	3881,
	4250,
	3504,
	4132,
	2991,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	3928,
	4250,
	3504,
	4140,
	3003,
	4364,
	0,
	3881,
	4250,
	3504,
	4136,
	2996,
	4364,
	0,
	3881,
	4250,
	3504,
	4134,
	2994,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	3884,
	4250,
	3504,
	4137,
	2999,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	2802,
	4250,
	3504,
	4124,
	2979,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	3881,
	4250,
	3504,
	4128,
	2986,
	4364,
	0,
	3881,
	4250,
	3504,
	4129,
	2987,
	4364,
	0,
	3881,
	4250,
	3504,
	4128,
	2986,
	4364,
	0,
	3881,
	4250,
	3504,
	4129,
	2987,
	4364,
	0,
	3881,
	4250,
	3504,
	4128,
	2986,
	4364,
	0,
	3881,
	4250,
	3504,
	4129,
	2987,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	4364,
	4250,
	3504,
	4352,
	3642,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4126,
	2982,
	4364,
	0,
	3881,
	4250,
	3504,
	4126,
	2982,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4125,
	2981,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4138,
	3000,
	4364,
	0,
	3881,
	4250,
	3504,
	4126,
	2982,
	4364,
	0,
	3881,
	4250,
	3504,
	4126,
	2982,
	4364,
	0,
	3881,
	4250,
	3504,
	4126,
	2982,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3852,
	3881,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	0,
	8887,
	0,
	8887,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3852,
	3881,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	9089,
	4364,
	3881,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8220,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8220,
	9083,
	3853,
	4168,
	4217,
	9089,
	8982,
	4352,
	3881,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8983,
	4353,
	3881,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
};
static const Il2CppTokenRangePair s_rgctxIndices[61] = 
{
	{ 0x0200000A, { 7, 6 } },
	{ 0x0200000B, { 13, 5 } },
	{ 0x0200000E, { 18, 3 } },
	{ 0x0200003A, { 21, 5 } },
	{ 0x0200003C, { 26, 13 } },
	{ 0x02000049, { 68, 7 } },
	{ 0x0200004B, { 75, 2 } },
	{ 0x0200004D, { 77, 2 } },
	{ 0x02000050, { 79, 31 } },
	{ 0x02000051, { 110, 9 } },
	{ 0x02000057, { 119, 8 } },
	{ 0x02000058, { 127, 9 } },
	{ 0x02000062, { 136, 5 } },
	{ 0x02000066, { 141, 26 } },
	{ 0x02000067, { 167, 8 } },
	{ 0x02000069, { 175, 9 } },
	{ 0x0200007D, { 184, 14 } },
	{ 0x0200007E, { 198, 10 } },
	{ 0x0200007F, { 208, 3 } },
	{ 0x02000081, { 211, 21 } },
	{ 0x02000108, { 232, 14 } },
	{ 0x0200010A, { 274, 7 } },
	{ 0x0200010D, { 283, 8 } },
	{ 0x02000110, { 291, 16 } },
	{ 0x02000111, { 307, 8 } },
	{ 0x02000113, { 317, 3 } },
	{ 0x02000115, { 335, 3 } },
	{ 0x02000116, { 338, 10 } },
	{ 0x02000117, { 348, 3 } },
	{ 0x02000118, { 351, 11 } },
	{ 0x02000119, { 362, 3 } },
	{ 0x0200011A, { 365, 12 } },
	{ 0x0200011E, { 386, 11 } },
	{ 0x02000123, { 415, 17 } },
	{ 0x02000124, { 432, 8 } },
	{ 0x02000125, { 440, 17 } },
	{ 0x02000126, { 457, 8 } },
	{ 0x02000127, { 465, 33 } },
	{ 0x02000128, { 498, 8 } },
	{ 0x0600000A, { 0, 4 } },
	{ 0x0600000F, { 4, 3 } },
	{ 0x0600005C, { 39, 6 } },
	{ 0x0600005D, { 45, 3 } },
	{ 0x0600005F, { 48, 7 } },
	{ 0x06000061, { 55, 3 } },
	{ 0x06000068, { 58, 10 } },
	{ 0x060003E1, { 246, 3 } },
	{ 0x060003E2, { 249, 6 } },
	{ 0x060003E3, { 255, 19 } },
	{ 0x060003FB, { 281, 1 } },
	{ 0x060003FD, { 282, 1 } },
	{ 0x06000426, { 315, 2 } },
	{ 0x06000429, { 320, 4 } },
	{ 0x0600042A, { 324, 5 } },
	{ 0x0600042B, { 329, 6 } },
	{ 0x06000445, { 377, 6 } },
	{ 0x06000446, { 383, 3 } },
	{ 0x0600044B, { 397, 6 } },
	{ 0x0600044C, { 403, 3 } },
	{ 0x06000451, { 406, 6 } },
	{ 0x06000452, { 412, 3 } },
};
extern const uint32_t g_rgctx_IEnumerable_1_t0D1A987D5B5884390D47B04D1ED80E51A6F267B0;
extern const uint32_t g_rgctx_Func_2_t686668CB40516B20CFE1036C41CDD75334EB5FFD;
extern const uint32_t g_rgctx_Enumerable_Select_TisT_t25F45AA2D7ACFE844C509A95817FD8D8ADCCA165_TisUniTask_1_tE4A02CE4738828686B9403397C82AA4359354E28_m70D2003E90B853141FFA6FFB278B530D1E61D1FD;
extern const uint32_t g_rgctx_IEnumerable_1_t0DC6818ACF249C1755CC5B97DCEC686E05BD6A62;
extern const uint32_t g_rgctx_IUniTaskAsyncEnumerable_1_t1DBE9C5E7B150234B4E0EB9C277951860555041A;
extern const uint32_t g_rgctx_UniTaskCancelableAsyncEnumerable_1_t5D8589C5C9857B3A31B1278C258A1A54E2F22B3A;
extern const uint32_t g_rgctx_UniTaskCancelableAsyncEnumerable_1__ctor_m2D65D80DE349D4C638EC56C60C4F3B3EB5F3D787;
extern const uint32_t g_rgctx_IUniTaskAsyncEnumerable_1_tFD64D58DD9DC2AF76E9A2A12CB99213A4BE7579B;
extern const uint32_t g_rgctx_UniTaskCancelableAsyncEnumerable_1_t4C46EDCC215D7FB93F12BE62BD9890A22F9C7620;
extern const uint32_t g_rgctx_IUniTaskAsyncEnumerable_1_GetAsyncEnumerator_m9A70D4C5D8A62E7B09A5551171F76B192498D990;
extern const uint32_t g_rgctx_IUniTaskAsyncEnumerator_1_t64E4603380DADC44F3ACDB0AC31982D33C454812;
extern const uint32_t g_rgctx_Enumerator_tEAF63C30B656677A731AFB73BD7916A14F944457;
extern const uint32_t g_rgctx_Enumerator__ctor_mBC56450380DF20E050EBAEE756BD30065F081E6B;
extern const uint32_t g_rgctx_IUniTaskAsyncEnumerator_1_t104202E8AE1BC2B32E07E06892485989157910AF;
extern const uint32_t g_rgctx_Enumerator_t00B2FAB0E19C4C1F72832304B27A60DC776FB8FA;
extern const uint32_t g_rgctx_IUniTaskAsyncEnumerator_1_get_Current_m7D525F73C66A60295C0916B44E6C5B77EEA6BC60;
extern const uint32_t g_rgctx_T_t3FE4E3207CCFB89B0C54F73B0B84AC95F4006CCD;
extern const uint32_t g_rgctx_IUniTaskAsyncEnumerator_1_MoveNextAsync_m256E074E9AE405EC4152676D28B67A1C0386A582;
extern const uint32_t g_rgctx_IUniTaskSource_1_t0FD61CC024821E5431A48B37A6F4544299E3CDD0;
extern const uint32_t g_rgctx_IUniTaskSource_1_GetResult_mF490F0D773FCC53DCE5A1023ABAE38DCD134E77A;
extern const uint32_t g_rgctx_T_t79A51A6E7E685B277DE33D5A283ED56D86D9D568;
extern const uint32_t g_rgctx_TaskPool_1_t42F0C4C01D5AE5A6D55DF60DDD0EC3EAB82198BE;
extern const uint32_t g_rgctx_T_tEF13046172EA0188CCC6CA32632B425D9860C7E3;
extern const uint32_t g_rgctx_ITaskPoolNode_1_t71CB674394D8751B56D9A538CFE28BAC42AFFBE4;
extern const uint32_t g_rgctx_ITaskPoolNode_1_get_NextNode_m908C3FA45700590083D82950CA147455A2FFCC08;
extern const uint32_t g_rgctx_TU26_t8CB822FD18FB480FC71B022CACDB1DBD2757B48B;
extern const uint32_t g_rgctx_TriggerEvent_1_t010F2A2D1392C1F6B17DFEECC4ACA5442E8D3D0B;
extern const uint32_t g_rgctx_ITriggerHandler_1_t52D41DE39AD82F37DB02F76DD54488806432726C;
extern const uint32_t g_rgctx_T_t445B9610901336D8278B01DC231E41B4C314F64E;
extern const uint32_t g_rgctx_ITriggerHandler_1_OnNext_mBDFFC611B8098C8E687AB79AB1869044B3D5101D;
extern const uint32_t g_rgctx_TriggerEvent_1_LogError_m66D8E9BA223D2749399C88851D06F3CFF7652A29;
extern const uint32_t g_rgctx_TriggerEvent_1_t010F2A2D1392C1F6B17DFEECC4ACA5442E8D3D0B;
extern const uint32_t g_rgctx_TriggerEvent_1_Remove_mC02D5D56011146055BD3095712E24F8BA547AC91;
extern const uint32_t g_rgctx_ITriggerHandler_1_get_Next_mEE03996D499D636D68C737207DF56DDBB0C978F3;
extern const uint32_t g_rgctx_TriggerEvent_1_Add_m987E70DFDCA138E153E5D776BA2F19FA308834CF;
extern const uint32_t g_rgctx_ITriggerHandler_1_OnCompleted_mBED34A752159AD9585FE71D9E1AD24377D36FBA6;
extern const uint32_t g_rgctx_ITriggerHandler_1_get_Prev_m4813EE0134B336C780375F2FCCFDE6A1FABC3939;
extern const uint32_t g_rgctx_ITriggerHandler_1_set_Prev_m3B2FAAF6684E4368D7EEB4FEBE95FD029B5298E8;
extern const uint32_t g_rgctx_ITriggerHandler_1_set_Next_m3776CDFFBBB2DB10E0F1093A19E58E6BA9814598;
extern const uint32_t g_rgctx_UniTask_FromCanceled_TisT_tA9FC278366683DE8BF6DB5F8A5B808AABBED91E1_m05985B1063AAB087599A817B5365CA1645A91A5B;
extern const uint32_t g_rgctx_UniTask_1_tB3F0C72BE0B88A89A0EB25D2440DF06DA5F059DD;
extern const uint32_t g_rgctx_ExceptionResultSource_1_tEE5F4EC88263C651F729603709B333E8640BA3F5;
extern const uint32_t g_rgctx_ExceptionResultSource_1__ctor_m16E6FC7FFD7D415EC5EE2CAEFD173347E2F0252E;
extern const uint32_t g_rgctx_UniTask_1__ctor_m0D5942405CDF52814251F5F097CFD90F66D37889;
extern const uint32_t g_rgctx_IUniTaskSource_1_tC24C95477DA694DF15AE0BD9FA0A9D24EE167B35;
extern const uint32_t g_rgctx_T_t88446E585C41D6E05834DD0FA984ED3700366132;
extern const uint32_t g_rgctx_UniTask_1_t7F96E3493E8F5EC6228114EC9FFAF3F7F20C87D2;
extern const uint32_t g_rgctx_UniTask_1__ctor_m0A824C2067A86A79CB357C047232B94C3CE9EB11;
extern const uint32_t g_rgctx_CanceledUniTaskCache_1_t661400D5EFBCE3B3E767AD7408637D01853D93C6;
extern const uint32_t g_rgctx_UniTask_1_t442BBB941CC3EF584FB3ACAEB86F4FC8B3DEF4BB;
extern const uint32_t g_rgctx_CanceledUniTaskCache_1_t661400D5EFBCE3B3E767AD7408637D01853D93C6;
extern const uint32_t g_rgctx_CanceledResultSource_1_t6181B0F18DBEFE4B392432917824550886C77466;
extern const uint32_t g_rgctx_CanceledResultSource_1__ctor_mC05BF42766EB244EFC7F04B1C321DFB8AB0FCCB5;
extern const uint32_t g_rgctx_UniTask_1__ctor_mDA3E81ADC0C37E298CC8B3E0ED13948641F7E2FA;
extern const uint32_t g_rgctx_IUniTaskSource_1_t3C00616703C12F2B59724099C6A54B213B6ACF8E;
extern const uint32_t g_rgctx_Func_1_t3273C78962591AD33725D68D66F62BB937A063CD;
extern const uint32_t g_rgctx_Func_1_Invoke_mF230B8AF8F8267648CD925397AF03551D950FDDD;
extern const uint32_t g_rgctx_UniTask_1_t8AE0C55FA2848C36EA64429D1D3B101F45E6642E;
extern const uint32_t g_rgctx_IEnumerable_1_t9E0D53A8564579FD2DCD86744D699C08098C62BF;
extern const uint32_t g_rgctx_ArrayPoolUtil_Materialize_TisUniTask_1_t49538C1007986E3955B609F44C4B5FF46A0E5B30_m56054D97BC2DFC11B4C5AA3A4493B0E277968182;
extern const uint32_t g_rgctx_RentArray_1_t2A29A77B4969023BDB3180A778A301F02E175ACD;
extern const uint32_t g_rgctx_UniTask_1U5BU5D_t8490E5B1FC898B3A6E15009801B7F235832BA316;
extern const uint32_t g_rgctx_WhenAllPromise_1_t5C992E0C72C0A7D6C69CD840B921968C911F63EF;
extern const uint32_t g_rgctx_WhenAllPromise_1__ctor_mB66AD647B45A7E1554FB8F2DA27807A4E235B9C6;
extern const uint32_t g_rgctx_UniTask_1_t7A6D2873C212E90D4BC9A1AA96F1015F1688817E;
extern const uint32_t g_rgctx_UniTask_1__ctor_m2F1259383A67FCB96D8C5D35A09836B4411464E1;
extern const uint32_t g_rgctx_IUniTaskSource_1_t73A879AC2C8692F31064DB5C65ADAEA919BCA752;
extern const Il2CppRGCTXConstrainedData g_rgctx_RentArray_1_t2A29A77B4969023BDB3180A778A301F02E175ACD_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_CanceledResultSource_1_tBBBD440A0B4218E30186C155241847117171CA85;
extern const uint32_t g_rgctx_CanceledResultSource_1__ctor_m6313747598A72947A6AAF417D40F67490DCBA0B0;
extern const uint32_t g_rgctx_UniTask_1_t7D9DC1B69E4CE4647FA69DCB84DE787F5E88E003;
extern const uint32_t g_rgctx_UniTask_1__ctor_mC6A58E3498E6DC7120ACA0B8CDEDD72269C0D659;
extern const uint32_t g_rgctx_IUniTaskSource_1_t5D03624EB782A2759E35E3F3872E8FA0327B84B6;
extern const uint32_t g_rgctx_CanceledUniTaskCache_1_tF99BA2F61400E44457663B09C7A4FEB897A8C495;
extern const uint32_t g_rgctx_CanceledUniTaskCache_1_tF99BA2F61400E44457663B09C7A4FEB897A8C495;
extern const uint32_t g_rgctx_ExceptionResultSource_1_t8747BEC7373B22C82BFE06126B0369BAC336DEF5;
extern const uint32_t g_rgctx_T_t7B2920B77EEEE92A4A6D0CD7B023D46295248BAD;
extern const uint32_t g_rgctx_CanceledResultSource_1_t46857C01B833467EF5D277E019F185790335C6CF;
extern const uint32_t g_rgctx_T_tD7F66045D0A8E18EFDBDA23C9283B19D00DE7F38;
extern const uint32_t g_rgctx_WhenAllPromise_1_t1110C154F19510A5E197DB5B54E4D43FD76E6F15;
extern const uint32_t g_rgctx_Array_Empty_TisT_t60B6CF8C28203B48D068E6B7E3E03FC76C2C7E77_m63B316625CC216AE62EA7D11AB8CD8CE4CE33172;
extern const uint32_t g_rgctx_TU5BU5D_t144FEA7681CF5D806BC2DD54DD2B96D70FFCD2D0;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_t82E70054AC27671C6C1EC99D380929FEE306CBD4;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_TrySetResult_m17D75942DBFDBD0BD646383740E02D705B345A03;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_t82E70054AC27671C6C1EC99D380929FEE306CBD4;
extern const uint32_t g_rgctx_TU5BU5D_t144FEA7681CF5D806BC2DD54DD2B96D70FFCD2D0;
extern const uint32_t g_rgctx_UniTask_1U5BU5D_tC238C92282C01FAB88BDDD30F7C2AE928C603B0B;
extern const uint32_t g_rgctx_UniTask_1_t5746BAFA2E793CC4F829EDACD87EE9BC4464C24C;
extern const uint32_t g_rgctx_UniTask_1_GetAwaiter_mDB16A3FE5D774D23E69D424971036470B49F8243;
extern const uint32_t g_rgctx_UniTask_1_t5746BAFA2E793CC4F829EDACD87EE9BC4464C24C;
extern const uint32_t g_rgctx_Awaiter_tB0FA08A07DF5D618D52B44E304E7060E02E2A151;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_TrySetException_mB4C8C215864D008534F39C14D99FA1B241582E77;
extern const uint32_t g_rgctx_Awaiter_get_IsCompleted_mCB3EF58C746F7B85EF90C60C18EEBC4A6BAB6D1F;
extern const uint32_t g_rgctx_Awaiter_tB0FA08A07DF5D618D52B44E304E7060E02E2A151;
extern const uint32_t g_rgctx_WhenAllPromise_1_TryInvokeContinuation_m3EA2BE6A85A839B4316F303ABEA75DFCCFFCA9B6;
extern const uint32_t g_rgctx_WhenAllPromise_1_t1110C154F19510A5E197DB5B54E4D43FD76E6F15;
extern const uint32_t g_rgctx_AwaiterU26_tFDC89CB493C97DFA81325C83F9B3F6B56319EA08;
extern const uint32_t g_rgctx_U3CU3Ec_t9DE031F134C93716DF9CEA73A2E5AC661ECE6CED;
extern const uint32_t g_rgctx_U3CU3Ec_t9DE031F134C93716DF9CEA73A2E5AC661ECE6CED;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_ctorU3Eb__3_0_mA3C27ECAA98EE1CAC8B27AAF7957E84B6E51E359;
extern const uint32_t g_rgctx_StateTuple_Create_TisWhenAllPromise_1_t1110C154F19510A5E197DB5B54E4D43FD76E6F15_TisAwaiter_tB0FA08A07DF5D618D52B44E304E7060E02E2A151_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m2D26648D9A5D53D42CA9A9EE0D10228988F9A249;
extern const uint32_t g_rgctx_StateTuple_3_t8314A4163F9D084D06529346E2F034B93C0C49EB;
extern const uint32_t g_rgctx_Awaiter_SourceOnCompleted_m1FE38E982572EBEFC5F7F72EE695AD34AD233738;
extern const uint32_t g_rgctx_Awaiter_GetResult_mE7FAFDD3146CD26937D5FDD49D5F604AA96A2B68;
extern const uint32_t g_rgctx_T_t60B6CF8C28203B48D068E6B7E3E03FC76C2C7E77;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_GetResult_m8D1B354208A2FB761BE95F4EA83E34651228FA4B;
extern const uint32_t g_rgctx_WhenAllPromise_1_GetResult_mF272164A7AD40BD01EB8CC541CC77074D34836EB;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_GetStatus_m5EE3E187E8AF6B37AA0FA4F2E17CBFFAF98EE4D0;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_UnsafeGetStatus_m6F2F56A39A9C0C0501EDE7D4EB2862AA79A66A2C;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_OnCompleted_mDCFE062A0E83610FC3BB34E91943D11DCE77FFD8;
extern const uint32_t g_rgctx_U3CU3Ec_tD45809F830386B036560E581844B35915B9A1A8D;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mAD007F9975E2AB3855D59959BB758DD3D941DAE7;
extern const uint32_t g_rgctx_U3CU3Ec_tD45809F830386B036560E581844B35915B9A1A8D;
extern const uint32_t g_rgctx_StateTuple_3_tE34F6CADAEF69B98C3ECFC0E10586520B1BFA606;
extern const uint32_t g_rgctx_WhenAllPromise_1_tA7F492788B8800275A5D79665B0861CD8A3C6FD2;
extern const uint32_t g_rgctx_Awaiter_tE9586E0097E130AC326DC69630930F81A355575C;
extern const uint32_t g_rgctx_WhenAllPromise_1_TryInvokeContinuation_m5127020040F88DCE8756C983EDE21321D3C526C8;
extern const uint32_t g_rgctx_WhenAllPromise_1_tA7F492788B8800275A5D79665B0861CD8A3C6FD2;
extern const uint32_t g_rgctx_AwaiterU26_tD70EFF1E918006467F027BE017E61317E538545F;
extern const uint32_t g_rgctx_UniTask_1_tEB59AFFFA85AA8B066D23E2900B771C00B8E64F0;
extern const uint32_t g_rgctx_IUniTaskSource_1_tB0198B9E7857907EC6C1304D56629B16AF190BD5;
extern const uint32_t g_rgctx_T_tFF5A4BDB19D1BCCEC9E3BFEC979335AAEF368430;
extern const uint32_t g_rgctx_IUniTaskSource_1_GetStatus_m9A8B1FE52684695A11A1F410906F64D63C5A0B71;
extern const uint32_t g_rgctx_Awaiter_t12412F44D0A1D4C6573C6EDD4F933E1089C86DFC;
extern const uint32_t g_rgctx_Awaiter__ctor_m6C11E745E474B2896035A9AD17327C659C66A372;
extern const uint32_t g_rgctx_UniTask_1U26_tAB23634523E0A46C0AD458B9872EF6A88783307B;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tFF5A4BDB19D1BCCEC9E3BFEC979335AAEF368430_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F;
extern const uint32_t g_rgctx_UniTask_1U26_t2E3A445F06B855768183F49CBDF2869B0DC96233;
extern const uint32_t g_rgctx_UniTask_1_tD8AB010F422D5AFB8EE7B134A25B47A5D55F7990;
extern const uint32_t g_rgctx_Awaiter_t456F33FED0549DFBFB206F47154716115A2D4743;
extern const uint32_t g_rgctx_UniTask_1_get_Status_mDCEF5E82A62E56A8861B8891A9EBA1FD4F655A9F;
extern const uint32_t g_rgctx_UniTask_1_tD8AB010F422D5AFB8EE7B134A25B47A5D55F7990;
extern const uint32_t g_rgctx_IUniTaskSource_1_tAE79514CEA48BB1902DDD9EB893A14408BF095E3;
extern const uint32_t g_rgctx_T_t51191FDA2C225152DFBE7756A21D31A184B705B0;
extern const uint32_t g_rgctx_IUniTaskSource_1_GetResult_m24C7C1EDF8D0E0C2BF5E34D906A7F176D9E335B2;
extern const uint32_t g_rgctx_IUniTaskSource_1_OnCompleted_m51F313ABFDBFF3F9CECBDFE6BF6FD20EE8C6D52F;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_ReportUnhandledError_m5482D76A6989087D5B8A909DD942BFA8ADDA835C;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_tDD9850FBEA4B9979B4280A64F124CBF3019FC8BF;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_tDD9850FBEA4B9979B4280A64F124CBF3019FC8BF;
extern const uint32_t g_rgctx_TResult_tFF5B28A72089716BF74B3DB0EB8ACECAFC2E102E;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_ValidateToken_m67B5C0384AFA0A3A909F62311BFE1864CE7E0F13;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1_t3C604AF18625B9E3164784AFC7BB09279C54F371;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1U26_t43AB8658BEE9752AAB5B0E29A6E8CD4CFEF4EAA4;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1_t3C604AF18625B9E3164784AFC7BB09279C54F371;
extern const uint32_t g_rgctx_U3CU3Ec_t833E5A06AE5510DB5A7BB67E1ACC3F624E3A7715;
extern const uint32_t g_rgctx_U3CU3Ec_t833E5A06AE5510DB5A7BB67E1ACC3F624E3A7715;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__4_0_m1A2CD3F66BE5ADAC3AA153A84B2B013F660D7E4A;
extern const uint32_t g_rgctx_TaskPool_1_tC6E37C899BB2A5699AC4709B0C568820BBFF13E5;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1_t3C604AF18625B9E3164784AFC7BB09279C54F371;
extern const uint32_t g_rgctx_TaskPool_1_TryPop_m91AA17CD3A6F907C8EE5C1EE6CC41479A48CF0CE;
extern const uint32_t g_rgctx_TaskPool_1_tC6E37C899BB2A5699AC4709B0C568820BBFF13E5;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1__ctor_m857513E8EF03682A0AA338B29CD048C1F3312B15;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_t84D8D71591E04FAE458DBA8940EA93416EC18BFE;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_get_Version_mF3C4C3A410B3467A3184C33D1D8990D8494554FD;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_t84D8D71591E04FAE458DBA8940EA93416EC18BFE;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1_Create_mAD1121675A6F54A8AC3EC3A4F2A6E80AA704FFCF;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1_TrySetCanceled_m2FB6DAC0855A440D19A03F069FA350D08B9BDD26;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_TrySetCanceled_m48BC55CB26B09E48D4E238ED74CC2963392F2F6F;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_GetResult_mBC334DC198E0830FEA064CFC03EA9548FC37318B;
extern const uint32_t g_rgctx_T_t0DDC8581344971C14D77D8F311A3B27F803B0701;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1_TryReturn_m1DC0C14AFE333690A92A3139AC0A43DF10B5668E;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1_GetResult_m006344F246FD11DF8B66EFEDE66FE08EE511F012;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_GetStatus_m7ACC929E51A93C37225DE5FA83CA7F8495E09F04;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_UnsafeGetStatus_m58D7255E5C3825F0DA353FB859A159A6EA79668E;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_OnCompleted_m8C9B8B67BED3BFD04BCCBBE822092953FE020BB7;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_Reset_m742E0DC4DD4DB325C8EE0C8910D1ABA2A51385AA;
extern const uint32_t g_rgctx_TaskPool_1_TryPush_m6D4CC90D9BF26FA6E9BBB065B557A99E8307C6C3;
extern const uint32_t g_rgctx_U3CU3Ec_t1E86A046B6EAF2684FFDAF50F62203D36DC767DA;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mB541A16B553C77B0AFA9702546173782392AA3A0;
extern const uint32_t g_rgctx_U3CU3Ec_t1E86A046B6EAF2684FFDAF50F62203D36DC767DA;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1_t4B6C9C2AA7FCBE4A3A391146C5A0F13B9D20ECEB;
extern const uint32_t g_rgctx_TaskPool_1_tC5D9BB34915E773EE546C71382440CA5DE40FCDF;
extern const uint32_t g_rgctx_AutoResetUniTaskCompletionSource_1_t4B6C9C2AA7FCBE4A3A391146C5A0F13B9D20ECEB;
extern const uint32_t g_rgctx_TaskPool_1_get_Size_mE46D95D7E78FB229A4B9A207304B272E41F4346D;
extern const uint32_t g_rgctx_TaskPool_1_tC5D9BB34915E773EE546C71382440CA5DE40FCDF;
extern const uint32_t g_rgctx_UniTaskCompletionSource_1_tD602B014F01E56925DF5A9C5C4DBE5C9DC09F574;
extern const uint32_t g_rgctx_UniTask_1_t25D1EDF6628FDDB8A50A5158D002B0866FD73BF3;
extern const uint32_t g_rgctx_UniTask_1__ctor_mDDD55352E696D8BE7D26405D29D1B301A854B15B;
extern const uint32_t g_rgctx_IUniTaskSource_1_tFAE9805A7EC70BB26CDD8B4535E6CBB567906229;
extern const uint32_t g_rgctx_UniTaskCompletionSource_1_UnsafeGetStatus_m5C6AEB26492202A4A5CE0940E8467F0C274D6492;
extern const uint32_t g_rgctx_T_t817F087709DFE1FAD5E336877C268687B2696CE8;
extern const uint32_t g_rgctx_UniTaskCompletionSource_1_TrySignalCompletion_m7B4C27786379DF1F2F4DEDBBEA8E3F4C0DCD364D;
extern const uint32_t g_rgctx_UniTaskCompletionSource_1_MarkHandled_mA8FEB8494AD8CEECDC3FE87D3225DA5BC4977C92;
extern const uint32_t g_rgctx_UniTaskCompletionSource_1_GetResult_m54543A02A614A48EB567DD7F54A02186FF657773;
extern const uint32_t g_rgctx_AsyncTriggerBase_1_tEDA7873489E20B961A34EA1CEECC30FD5BD4ED10;
extern const uint32_t g_rgctx_TriggerEvent_1_t3E087463A2048A0FE777889514456C02A61B390B;
extern const uint32_t g_rgctx_TriggerEvent_1_SetCompleted_m0A5A0BBB4F569AD992CE4D48EA4AF723AA73A427;
extern const uint32_t g_rgctx_TriggerEvent_1_t3E087463A2048A0FE777889514456C02A61B390B;
extern const uint32_t g_rgctx_AwakeMonitor_t047632621825C2065E436E6BE9E5893CB2B060AF;
extern const uint32_t g_rgctx_AwakeMonitor__ctor_m3EE617FD03320290D158A82DAE500576BE5862E3;
extern const uint32_t g_rgctx_ITriggerHandler_1_tB74D4CAF404506DCE33C217F4F8C749A01DF6B8D;
extern const uint32_t g_rgctx_TriggerEvent_1_Add_mAA918657D63DAF361EEDB912FD8E5E3AD6DBA3AD;
extern const uint32_t g_rgctx_TriggerEvent_1_Remove_m948FCF70219D54743F35DB44BA76A3A2292FFD5A;
extern const uint32_t g_rgctx_T_t670FFF1354E0A266BDC3591646E01F2C372AA6E4;
extern const uint32_t g_rgctx_TriggerEvent_1_SetResult_m496A5D555EE04F7A8CF2CB514B0083AD730584A8;
extern const uint32_t g_rgctx_AsyncTriggerEnumerator_tC449EA3D72513ED478564E4E875A0D2580A1EBEC;
extern const uint32_t g_rgctx_AsyncTriggerEnumerator__ctor_m178113C81038829F68ABA7FA36DFF335437A8CFF;
extern const uint32_t g_rgctx_IUniTaskAsyncEnumerator_1_t6EBD209A80FDCE0590DDD493B5DF3936554073DB;
extern const uint32_t g_rgctx_AsyncTriggerBase_1_t437CF1A5DB0D688A8DD14AD09F2A538388047C82;
extern const uint32_t g_rgctx_AsyncTriggerEnumerator_t7249198289B65AB5C1483B6CEB0180DA727B9D96;
extern const uint32_t g_rgctx_T_t8D5813A798A27605A635E9FA61B53505378D0340;
extern const uint32_t g_rgctx_AsyncTriggerEnumerator_set_Current_mB18402D3B634EB5B676A8D08CFE285FA7D3B0E17;
extern const uint32_t g_rgctx_AsyncTriggerEnumerator_DisposeAsync_mC668EA9CE4345812365E7EBF36FC657884C5DC9B;
extern const uint32_t g_rgctx_ITriggerHandler_1_t1D533B54D5D40486E780463AD4F70A61F25EB2FD;
extern const uint32_t g_rgctx_AsyncTriggerBase_1_AddHandler_mE89364847D6C9ABA23EE6799508741C3F6E0CB64;
extern const uint32_t g_rgctx_AsyncTriggerEnumerator_t7249198289B65AB5C1483B6CEB0180DA727B9D96;
extern const uint32_t g_rgctx_AsyncTriggerBase_1_RemoveHandler_mE3E9AF8909FEA4DC34499CC75382DEEF66AC64DA;
extern const uint32_t g_rgctx_AsyncTriggerEnumerator_CancellationCallback_mBD49DBC0CDBB9BDDF781A6A61D8687A2371C2CB6;
extern const uint32_t g_rgctx_AsyncTriggerBase_1_tFCE6DB48A2369526332DFFF3FAB652F5A50F29CE;
extern const uint32_t g_rgctx_AwakeMonitor_tFE3AE8A412EE5595BB66FE61C355840D39D8C8B4;
extern const uint32_t g_rgctx_AsyncTriggerBase_1_OnDestroy_m6BF311C76F7A0B0C63A7EE67ADA27487D89C4196;
extern const uint32_t g_rgctx_AsyncTriggerHandler_1_tBEFD5DBC560056C538F7465205DD4C3A5E2E4CC2;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_t5AF574FB7ADB19F7465BABE71DF5EA5C481136F6;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_Reset_mA3D234EB9D372F51EE97743938D5F4122524E6F2;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_t5AF574FB7ADB19F7465BABE71DF5EA5C481136F6;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_get_Version_m315BC0186C87013B4990F5C2BCB35839FF33AC5E;
extern const uint32_t g_rgctx_ITriggerHandler_1_t22A753D10854BD1AFB42407F32C8F0478A6167CA;
extern const uint32_t g_rgctx_AsyncTriggerBase_1_t3EE0A624E3B2A6F01B5B587DD38A169CF9199311;
extern const uint32_t g_rgctx_AsyncTriggerBase_1_AddHandler_mC32965FE69F5BAC3C377F7BA59F91B5DE682C320;
extern const uint32_t g_rgctx_AsyncTriggerHandler_1_tBEFD5DBC560056C538F7465205DD4C3A5E2E4CC2;
extern const uint32_t g_rgctx_AsyncTriggerHandler_1_Dispose_mC7E4B8BF4B894FCC7F962770139C4DD39445D35B;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_TrySetCanceled_mAE5360D768BD854C2BF98BEDC403B6B44E72ECD9;
extern const uint32_t g_rgctx_AsyncTriggerBase_1_RemoveHandler_m3786277294CBA28166F205A06013DDD6B5FD63F7;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_GetResult_m4DD655E9F6BAB190AE04F7590A00D6A153E53D5A;
extern const uint32_t g_rgctx_T_t08270E63CB32F6E28BBD0849175774F469D9FEB5;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_TrySetResult_mFB6E06FC36B90F6E600E5ABFC8E0094DEA1182D3;
extern const uint32_t g_rgctx_IUniTaskSource_1_tC0226A55B6BDFEC90DD694F13FEDF68B43F61845;
extern const uint32_t g_rgctx_IUniTaskSource_1_GetResult_m7A879765F6766C94DF793E8B06D0136AC3E88FFD;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_GetStatus_m5B0EF792364FC896849FA626121E7B2765767DDF;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_UnsafeGetStatus_m0F54E4BB0969BE257B0230EFB3AB5EB0A635B421;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_OnCompleted_m743F138BDD1EA571B4B56D0DDE901A22350120CC;
extern const uint32_t g_rgctx_AsyncTriggerHandler_1_CancellationCallback_m2052B714C14C75D9B691F6F1C96E2268134CE9C4;
extern const uint32_t g_rgctx_MinimumQueue_1U5BU5D_tAC46D497979D9C2B1528E5DD4F45A28E151432CD;
extern const uint32_t g_rgctx_ArrayPool_1_tEAC60EBC56FA39A47D82CF520CF3F0E4465DA612;
extern const uint32_t g_rgctx_MinimumQueue_1U5BU5D_tAC46D497979D9C2B1528E5DD4F45A28E151432CD;
extern const uint32_t g_rgctx_MinimumQueue_1_t3ABDED1048C64AAC761336650137D377A2CB44B1;
extern const uint32_t g_rgctx_MinimumQueue_1__ctor_mDA9CAE81C47A9FF51EA23BF98F2F48167BB9242C;
extern const uint32_t g_rgctx_TU5BU5D_t0561E4853D3F3ED88D37A9CC6631443BBD68643C;
extern const uint32_t g_rgctx_ArrayPool_1_tEAC60EBC56FA39A47D82CF520CF3F0E4465DA612;
extern const uint32_t g_rgctx_ArrayPool_1_CalculateSize_m5E9E3008415DD7FF974D012732AD048D78F544FE;
extern const uint32_t g_rgctx_ArrayPool_1_GetQueueIndex_m131E21588BC6CAB6A1D768AB6B8E9C665A3F2361;
extern const uint32_t g_rgctx_MinimumQueue_1_get_Count_mAA4651E2A7E205CB8DA53B25BE7C1A4EF284EE90;
extern const uint32_t g_rgctx_MinimumQueue_1_Dequeue_mAF6D06F5F3CC55854E74140C0C6564D4DC30AACC;
extern const uint32_t g_rgctx_TU5BU5D_t0561E4853D3F3ED88D37A9CC6631443BBD68643C;
extern const uint32_t g_rgctx_MinimumQueue_1_Enqueue_mC9D594466B86871EEF85C35EFAB5DFFF27EFF9B3;
extern const uint32_t g_rgctx_ArrayPool_1__ctor_m9174D8BD5C70A3961126F8724A64C1795A5D81D6;
extern const uint32_t g_rgctx_TU5BU5DU26_tD2D10643DFFB23BEE0B9B443F2FC4C28F26698B3;
extern const uint32_t g_rgctx_ArrayPool_1_t47548AC30F0D307EE1C45DBF502A60EE53BAFD6B;
extern const uint32_t g_rgctx_ArrayPoolUtil_EnsureCapacityCore_TisT_t462D1E6B1659F56BDF88759C4886E6735FCD4056_m970A54254377E6FED8BDB9E8A6A8A464525B7530;
extern const uint32_t g_rgctx_TU5BU5DU26_tD1F2AC66B8AF924EE97AFDF48655D9A045BAB2D4;
extern const uint32_t g_rgctx_ArrayPool_1_t7878629DFDD19E27503B383C18453CA9B01C7453;
extern const uint32_t g_rgctx_ArrayPool_1_Rent_m3BEDB108AFAFB71DFAF81BACCBD82328C109319F;
extern const uint32_t g_rgctx_TU5BU5D_t4030640B5EACEC2E24850A063E58604705551BA5;
extern const uint32_t g_rgctx_RuntimeHelpersAbstraction_IsWellKnownNoReferenceContainsType_TisT_tFA3BFD656B7C1AF7B85FF067A7C0194E80D8FAA6_m7BC465E20B06E12FC8DF3B90E018E9C0974C6327;
extern const uint32_t g_rgctx_ArrayPool_1_Return_m928ED805CD5349EB5E2F9033D519004F955E4644;
extern const uint32_t g_rgctx_IEnumerable_1_t1FC05323E4E2C683D14114B4C49B81F8096DB58E;
extern const uint32_t g_rgctx_TU5BU5D_t02C2F3B6FE25A3842371A0A95F27B4C749FFA515;
extern const uint32_t g_rgctx_RentArray_1_t212AF77FBA14B01F4FC74C7C0F76DCFE6BF488E5;
extern const uint32_t g_rgctx_RentArray_1__ctor_mC1486BEDBAE117829444ACA6560BAD772A183607;
extern const uint32_t g_rgctx_ArrayPool_1_tBA6CE4D8CD875BCF50DD37080D0DBE8CEFB88655;
extern const uint32_t g_rgctx_ICollection_1_tA6A02772870E6B52E82143C30D41C992E8D4475A;
extern const uint32_t g_rgctx_ICollection_1_get_Count_mDFF06279697C8E713ABEA547A0187AE0689674BE;
extern const uint32_t g_rgctx_Array_Empty_TisT_tF392A294880B3667DF7E2A8AC900FF2BD91BD36A_mC11C21DDC93215CC4EAC26EE7B129B4C422FB262;
extern const uint32_t g_rgctx_ArrayPool_1_tBA6CE4D8CD875BCF50DD37080D0DBE8CEFB88655;
extern const uint32_t g_rgctx_ArrayPool_1_Rent_m34F877D31F3474FB364E0A2549809B5C9C5F53B3;
extern const uint32_t g_rgctx_ICollection_1_CopyTo_m7098A3C1B663ABE951ED4E7469767D38544A73DE;
extern const uint32_t g_rgctx_IReadOnlyCollection_1_tB4E75D3A623ADB7555DD0933ABD3B4241FD7C444;
extern const uint32_t g_rgctx_IReadOnlyCollection_1_get_Count_mB7D9CEFE605BEC01956585475E07D26533C6D7E6;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m8BA50B8582D12A69881BC15784DD0DBA0A083C46;
extern const uint32_t g_rgctx_IEnumerator_1_tB11F00945A35CF1527CEF944CF6C6D9AEF96380E;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_mBB07DA0C2937EC7BEC10A5B500FF270359ECE1FF;
extern const uint32_t g_rgctx_T_tF392A294880B3667DF7E2A8AC900FF2BD91BD36A;
extern const uint32_t g_rgctx_ArrayPoolUtil_EnsureCapacity_TisT_tF392A294880B3667DF7E2A8AC900FF2BD91BD36A_m8C33D6ED68CD36346A870006967B738833EC606C;
extern const uint32_t g_rgctx_TU5BU5DU26_t55F1B763700598FA0FE0F40210AB4E2F0FE8F76E;
extern const uint32_t g_rgctx_TU5BU5D_tF8338F8EA7EF039709E5C2DDE33FF0A1BDFDE006;
extern const uint32_t g_rgctx_RentArray_1_tC8603A0D669621EE68C673BE661EF13D57D1D62C;
extern const uint32_t g_rgctx_ArrayPool_1_t14E311D71A5B0AC4F2D1CBB3398B948A121B15B9;
extern const uint32_t g_rgctx_RuntimeHelpersAbstraction_IsWellKnownNoReferenceContainsType_TisT_t53531E4F8898ADF8FBA91425866F13BDF5C6A89B_m283F0513F5BFC2A32CAF25122B969F273CAEFC72;
extern const uint32_t g_rgctx_RentArray_1_DisposeManually_m77D729BFA124B72D630FD127260525DDAACE2CB8;
extern const uint32_t g_rgctx_RentArray_1_tC8603A0D669621EE68C673BE661EF13D57D1D62C;
extern const uint32_t g_rgctx_ArrayPool_1_Return_m055CEA17FD79DCEA1A2B16B1A532DA45B854C54D;
extern const uint32_t g_rgctx_T_t944AFB1614D28C7AF409C51588C58D6ABF02F6A7;
extern const uint32_t g_rgctx_T_tB029863504A8441A8EC4387B933B84CF6C7CFCAD;
extern const uint32_t g_rgctx_TU5BU5D_t2B176C91D05FE374AA3F2FC3687C06A1300B6FEF;
extern const uint32_t g_rgctx_MinimumQueue_1_t52BA5756E63A75433F15EAF13DDAF2C80B52EE1F;
extern const uint32_t g_rgctx_TU5BU5D_t2B176C91D05FE374AA3F2FC3687C06A1300B6FEF;
extern const uint32_t g_rgctx_MinimumQueue_1_Grow_mB8434C25203AE977F656D410880FF5C608BF6D07;
extern const uint32_t g_rgctx_T_t04131A8E473FE0839BDD6400A2659A9A9C8A7072;
extern const uint32_t g_rgctx_MinimumQueue_1_MoveNext_m9FA08F459599E03AF643F0EE4AA1B6AFEA11E2AB;
extern const uint32_t g_rgctx_MinimumQueue_1_ThrowForEmptyQueue_m9073B946F5777B6718A7E09EA4F6A1E3BBB877A0;
extern const uint32_t g_rgctx_MinimumQueue_1_SetCapacity_m0166E47126ED51C0CD0B0805DE9F9796AE5C0AC1;
extern const uint32_t g_rgctx_PooledDelegate_1_t58A21864D5BCAF81521ADA5219CC93B0294C3B72;
extern const uint32_t g_rgctx_PooledDelegate_1U26_tBAD91125E80C8723B9813396F1D770CE7A59B20B;
extern const uint32_t g_rgctx_PooledDelegate_1_t58A21864D5BCAF81521ADA5219CC93B0294C3B72;
extern const uint32_t g_rgctx_U3CU3Ec_t6FEFC77E1487EF255CA9758F47CB334003335D18;
extern const uint32_t g_rgctx_U3CU3Ec_t6FEFC77E1487EF255CA9758F47CB334003335D18;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__4_0_m888558D1C315BF3900626F2F4F09EB5CE9BD0B25;
extern const uint32_t g_rgctx_PooledDelegate_1_Run_mE9937A4E119D2B208328573576FE832E68573A0B;
extern const uint32_t g_rgctx_Action_1_t06BF52E84469BF9FBB43339B8B5996A4945B00B5;
extern const uint32_t g_rgctx_Action_1__ctor_m1A7B0EC8533820EA1C816F2B8164E4F842C31FE2;
extern const uint32_t g_rgctx_TaskPool_1_t47C064D0A9F16D3DB615AC0641A12E9B050EDF2C;
extern const uint32_t g_rgctx_PooledDelegate_1_t58A21864D5BCAF81521ADA5219CC93B0294C3B72;
extern const uint32_t g_rgctx_TaskPool_1_TryPop_mAB7CA2EBECF11CC759D63D08E0E983C5E58067F6;
extern const uint32_t g_rgctx_TaskPool_1_t47C064D0A9F16D3DB615AC0641A12E9B050EDF2C;
extern const uint32_t g_rgctx_PooledDelegate_1__ctor_m9890DC55DB4DED622CF05343D25555F93B43766E;
extern const uint32_t g_rgctx_TaskPool_1_TryPush_m85AADC03378AE91336DE7301A3E98F18F7349B79;
extern const uint32_t g_rgctx_T_tDAECF6AB8E78CFB2184242E7143A92D1D25B422B;
extern const uint32_t g_rgctx_U3CU3Ec_t49269800EED0CB2AA7E97958550C637BE307B589;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mA50C01EF65624E6192DA751512E07B600C79D7A7;
extern const uint32_t g_rgctx_U3CU3Ec_t49269800EED0CB2AA7E97958550C637BE307B589;
extern const uint32_t g_rgctx_PooledDelegate_1_t53DE61D40DFCE4551713AA7A1CDDF54EA8E0D20A;
extern const uint32_t g_rgctx_TaskPool_1_t4154B80AC5C532D1DB0F28BD229B5640284C5F33;
extern const uint32_t g_rgctx_PooledDelegate_1_t53DE61D40DFCE4551713AA7A1CDDF54EA8E0D20A;
extern const uint32_t g_rgctx_TaskPool_1_get_Size_m8769DD97F3123C2C96504DB40B38CBDFA481CBBB;
extern const uint32_t g_rgctx_TaskPool_1_t4154B80AC5C532D1DB0F28BD229B5640284C5F33;
extern const uint32_t g_rgctx_WellKnownNoReferenceContainsType_1_tA057C55C5DB0DBB75037E59317C56315C56578C0;
extern const uint32_t g_rgctx_WellKnownNoReferenceContainsType_1_tA057C55C5DB0DBB75037E59317C56315C56578C0;
extern const uint32_t g_rgctx_T_t3D448D571AD9B5B361AFA2CA60C8E7EBB81B23E3;
extern const uint32_t g_rgctx_WellKnownNoReferenceContainsType_1_t40B9DC7D99DF47BAD74AFB7312CC5726EE61B58E;
extern const uint32_t g_rgctx_WellKnownNoReferenceContainsType_1_t40B9DC7D99DF47BAD74AFB7312CC5726EE61B58E;
extern const uint32_t g_rgctx_T1_tA519426369CEC336FD0D430FB1AD91DC379D86BE;
extern const uint32_t g_rgctx_StatePool_1_Create_m766FECDB726DBEAE58B246C68E6C304913CFC452;
extern const uint32_t g_rgctx_StatePool_1_t330C5CB64A5E66A1D940139C886C1A19587FFC4A;
extern const uint32_t g_rgctx_StateTuple_1_tF62C182C34A430C6665876937A7416330E6791CB;
extern const uint32_t g_rgctx_T1_t1D01B7F31030A07CCAED2A89B08ACECFEF02E917;
extern const uint32_t g_rgctx_T2_t7BC53660A65338D580A336E60B5D20D6E68635FB;
extern const uint32_t g_rgctx_StatePool_2_Create_m1832F6EC3634012444B99C3A0BB489274A708ABF;
extern const uint32_t g_rgctx_StatePool_2_t11DCCDE8450A6E3857C68E88835103A69CF2A315;
extern const uint32_t g_rgctx_StateTuple_2_tAB587F05004A24E84DDE4D855E7D3014D124E68A;
extern const uint32_t g_rgctx_T1_t0B72D1B80F7D3C71A0BCE98137ECACE4BCDA749D;
extern const uint32_t g_rgctx_T2_t958897145B9EEACC83B1CE14AFE4E8D6E10F2026;
extern const uint32_t g_rgctx_T3_t343451D3CAA0D4B748CC818AE8AA09DFD28BDF46;
extern const uint32_t g_rgctx_StatePool_3_Create_mB19C5A35370FF82CEAAA9DF9FB0A082D92A4DFB2;
extern const uint32_t g_rgctx_StatePool_3_t480CACA3DF75909104A8F1597C062325F6369C61;
extern const uint32_t g_rgctx_StateTuple_3_tE996AD215D6BF6554A5C696FFA97DE9602F5F64D;
extern const uint32_t g_rgctx_StatePool_1_Return_m3F6825A1329AAFDFCEC2B968E72540D9F760C647;
extern const uint32_t g_rgctx_StatePool_1_tFB2B01ED23AF54F47A1A03D6FCD894A6367E94D6;
extern const uint32_t g_rgctx_StateTuple_1_tA5F9C4E47278414E7943AE6509A075506181136E;
extern const uint32_t g_rgctx_StatePool_1_t36A7570E6B305FB4C452226FF5B57F92C4FB1461;
extern const uint32_t g_rgctx_ConcurrentQueue_1_t421A5F586919D644AA760058878DFF2A085293D8;
extern const uint32_t g_rgctx_StatePool_1_t36A7570E6B305FB4C452226FF5B57F92C4FB1461;
extern const uint32_t g_rgctx_ConcurrentQueue_1_TryDequeue_m1017CCE4D54E7DFDEF4C7BBC7B055B310DFC1552;
extern const uint32_t g_rgctx_StateTuple_1U26_t2F86B433E9ED733F5D6F5D280C9969D70844FC9D;
extern const uint32_t g_rgctx_T1_t51CFE2605B86B56A2421F975C11C825B7387729C;
extern const uint32_t g_rgctx_StateTuple_1_tF3111A3829CB6F50A7061A4412C00523D18DE157;
extern const uint32_t g_rgctx_StateTuple_1__ctor_mC9E61D79711C5D2173B0ED7BE23A0BBA9B82358C;
extern const uint32_t g_rgctx_ConcurrentQueue_1_Enqueue_m4A038AC67594B96AA582D5ABE169CBCAFAEFD7F7;
extern const uint32_t g_rgctx_ConcurrentQueue_1__ctor_m39F27F58BDA941ED2B4D1B53D9592D7813A5C89A;
extern const uint32_t g_rgctx_StatePool_2_Return_m1202DEDE0EDE2653357704A5B5FAC6D17DBE6788;
extern const uint32_t g_rgctx_StatePool_2_tF003B75461B4E4F35752503683AE3B342179A922;
extern const uint32_t g_rgctx_StateTuple_2_t6A9B6CDA18E5C76D42BD528191288F8AC318991F;
extern const uint32_t g_rgctx_StatePool_2_tC70DDB4DE613E4C63E27EB30F18F981465ED7425;
extern const uint32_t g_rgctx_ConcurrentQueue_1_t219121BCEB8FEA61BD8EC605BFFEA1B6CCE8946B;
extern const uint32_t g_rgctx_StatePool_2_tC70DDB4DE613E4C63E27EB30F18F981465ED7425;
extern const uint32_t g_rgctx_ConcurrentQueue_1_TryDequeue_m4870BD1F8B9BC1A79482BCC6F9EC2EE6DDB9D01C;
extern const uint32_t g_rgctx_StateTuple_2U26_t6177ABC01EE1146349CC88DFD2AFD579D92662A6;
extern const uint32_t g_rgctx_T1_tE9322E0318F5FBD75E16C0D6D467BD89AFEC4AD9;
extern const uint32_t g_rgctx_StateTuple_2_tDE2270E3EF9B27E638CD82F45C46A9CC961D9818;
extern const uint32_t g_rgctx_T2_t89B6553AD9FE17950E023CAAAED19A0BA17E1347;
extern const uint32_t g_rgctx_StateTuple_2__ctor_m21A6C568D17F768AC970AAAE253BC08A11706579;
extern const uint32_t g_rgctx_ConcurrentQueue_1_Enqueue_m44FAE83B8BA90F53665EB6D4F02AAB4A51C54A4F;
extern const uint32_t g_rgctx_ConcurrentQueue_1__ctor_m662BE23571F85AA01440B4094957C8B3A161C3AC;
extern const uint32_t g_rgctx_StatePool_3_Return_m2699E748F87ED4692B2B14D0256BEBF943AE715D;
extern const uint32_t g_rgctx_StatePool_3_tAC1E0B3E4DB3BB6C0016A0722AC595184B6D6896;
extern const uint32_t g_rgctx_StateTuple_3_t1C98FD1B16AC5A9EFC118BE93407DDF9A5D163A1;
extern const uint32_t g_rgctx_StatePool_3_tE766EC840FCB71ABE80C031A8C7CC786B1CFEF7E;
extern const uint32_t g_rgctx_ConcurrentQueue_1_t9024E65A6B68CC7A12AFC0110C0C273E87B2F64E;
extern const uint32_t g_rgctx_StatePool_3_tE766EC840FCB71ABE80C031A8C7CC786B1CFEF7E;
extern const uint32_t g_rgctx_ConcurrentQueue_1_TryDequeue_m69BA6D4B64FD1B1ED1F54AA830E4D3872D523698;
extern const uint32_t g_rgctx_StateTuple_3U26_tF5C4EAACD77E7440E990E30CEA142EDE24CDF833;
extern const uint32_t g_rgctx_T1_t899F401FAA2128951380171F4EB6ED85D3EAA2FC;
extern const uint32_t g_rgctx_StateTuple_3_t458A0ADE756B87D410265479AFA9E2437FB5BE23;
extern const uint32_t g_rgctx_T2_t0F0ABEFE350A1561886458236A2990757EC8C02C;
extern const uint32_t g_rgctx_T3_tF058B5DD9291DE0D635B3FCCF6E267E0E0B92A1D;
extern const uint32_t g_rgctx_StateTuple_3__ctor_m226EDC85C78A03D07540AF7E8F2F6E439310DDA6;
extern const uint32_t g_rgctx_ConcurrentQueue_1_Enqueue_m624BFF72F0CA66FCBB9B6017478CB14315D4EA6A;
extern const uint32_t g_rgctx_ConcurrentQueue_1__ctor_mF8D177FE3BBB79FADEAB92AAB3BDB1C640616200;
extern const uint32_t g_rgctx_TStateMachineU26_t7E595B604DD907CE9F0BEA030468D9615DE6E8CD;
extern const uint32_t g_rgctx_AsyncUniTask_1_SetStateMachine_mD4F3960BEFDA44DC672042EDC37042AC088DBDDB;
extern const uint32_t g_rgctx_AsyncUniTask_1_t2C06823C7B86C4DAA5FD5DC1AFFEA22316790DF2;
extern const uint32_t g_rgctx_TAwaiterU26_t8796FC98BE52792DF9834A87F3A5B63EAF21AE1D;
extern const uint32_t g_rgctx_TAwaiter_tFE66B7F561EDB03E27BEFD39140B18AAA045B55E;
extern const Il2CppRGCTXConstrainedData g_rgctx_TAwaiter_tFE66B7F561EDB03E27BEFD39140B18AAA045B55E_ICriticalNotifyCompletion_UnsafeOnCompleted_m09734062A0D335816D9511A291F23F4483E67219;
extern const uint32_t g_rgctx_TStateMachineU26_t5B52C5D174CC1DFEA9FC87F4E93832D8E9BAF51E;
extern const uint32_t g_rgctx_TStateMachine_t6AFAEEFDBDF37C2AAA352D2523BB2E5C1C3110E7;
extern const Il2CppRGCTXConstrainedData g_rgctx_TStateMachine_t6AFAEEFDBDF37C2AAA352D2523BB2E5C1C3110E7_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384;
extern const uint32_t g_rgctx_AsyncUniTaskMethodBuilder_1_t4A206AF7A38D216DBB4742CAE359E8CCEA1A3F50;
extern const uint32_t g_rgctx_IStateMachineRunnerPromise_1_tEF77604B8BAB812E6C3EAEC71A3457AD755627F1;
extern const uint32_t g_rgctx_IStateMachineRunnerPromise_1_get_Task_mFDC52D6BF30A2AF0E1677106DEDAC8D501EB123C;
extern const uint32_t g_rgctx_UniTask_1_t21857807D52B0D42492371AEA605C8D31B640C92;
extern const uint32_t g_rgctx_UniTask_FromException_TisT_tFD8DB5F523A69F458964B5BC1138077595EF73A7_m7AF24110AFE711450E2B7E35EE142D01FD1378F9;
extern const uint32_t g_rgctx_T_tFD8DB5F523A69F458964B5BC1138077595EF73A7;
extern const uint32_t g_rgctx_UniTask_FromResult_TisT_tFD8DB5F523A69F458964B5BC1138077595EF73A7_m662B8E8D159A25BD16081416241CB2C99853066A;
extern const uint32_t g_rgctx_IStateMachineRunnerPromise_1_SetException_m3EF50C8F1E01E1CFEEF7A7C3518C9685B81EA70E;
extern const uint32_t g_rgctx_IStateMachineRunnerPromise_1_SetResult_m0BE6BA7412A9D21443D0774C8F61C1096B02A75D;
extern const uint32_t g_rgctx_IStateMachineRunnerPromise_1U26_tF52B5B0483EB33D15A5E2B7CA68F896CE2896DCD;
extern const uint32_t g_rgctx_IStateMachineRunnerPromise_1_get_MoveNext_mDC5CE79508C5FBFFAEEAEF088A953DEAC6AC8186;
extern const uint32_t g_rgctx_TStateMachineU26_t01F3917053002BC1605B1CF916E1641CF3C70C98;
extern const uint32_t g_rgctx_AsyncUniTask_2_SetStateMachine_m22AB6B2B4FAC4E69F630C879AAE4B6D44FC29E39;
extern const uint32_t g_rgctx_AsyncUniTask_2_t352026692240B87108BE37E5F261F6A967EB7189;
extern const uint32_t g_rgctx_TAwaiterU26_tB3D81A91C4B0B2381C8C04918F9D955F5449D33D;
extern const uint32_t g_rgctx_TAwaiter_t015830761D949D160F916F43B0B5EB454646739F;
extern const Il2CppRGCTXConstrainedData g_rgctx_TAwaiter_t015830761D949D160F916F43B0B5EB454646739F_ICriticalNotifyCompletion_UnsafeOnCompleted_m09734062A0D335816D9511A291F23F4483E67219;
extern const uint32_t g_rgctx_TStateMachineU26_t5F7430DCCB1B012A3D0AE295A8C327EDE971B006;
extern const uint32_t g_rgctx_TStateMachine_t1F20CA5AFE89239F6D44E6BCF4775C6C02982766;
extern const Il2CppRGCTXConstrainedData g_rgctx_TStateMachine_t1F20CA5AFE89239F6D44E6BCF4775C6C02982766_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384;
extern const uint32_t g_rgctx_TStateMachineU26_tBD669B5515AC892AA4A661083303E51AA072B5E0;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1_SetStateMachine_mAEE08CC14213290D6A0DDFBC4BC629B498C2AF5B;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1_t4908E6BDEFF2E6C289E37D01D73EC9864A1C1696;
extern const uint32_t g_rgctx_TAwaiterU26_tB0189391064E425D0970D2A7AF168C7CD878A17E;
extern const uint32_t g_rgctx_TAwaiter_tE0E5FF0494AA0E527224994045013FBB3CA4431C;
extern const Il2CppRGCTXConstrainedData g_rgctx_TAwaiter_tE0E5FF0494AA0E527224994045013FBB3CA4431C_ICriticalNotifyCompletion_UnsafeOnCompleted_m09734062A0D335816D9511A291F23F4483E67219;
extern const uint32_t g_rgctx_TStateMachineU26_tC33A41449FC97FA6184B95A42C43DBBD30577AB2;
extern const uint32_t g_rgctx_TStateMachine_tF1DA3C63E6FDD47892B8E74DD16D3FBC1B22849B;
extern const Il2CppRGCTXConstrainedData g_rgctx_TStateMachine_tF1DA3C63E6FDD47892B8E74DD16D3FBC1B22849B_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1_t7984D032432607DFF92ED70A3EA13751E8D7702C;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1_Run_m45D5761EDCB5D3B87D6C51DE82E1A068297D5F47;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1_Return_mE65BD9FBC6FC1E4B2D9A528C51262108D8A30CEE;
extern const uint32_t g_rgctx_TaskPool_1_tD8D0F0BEE5FD84BD452D08BAEF797705A949C79F;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1_t7984D032432607DFF92ED70A3EA13751E8D7702C;
extern const uint32_t g_rgctx_TaskPool_1_TryPop_m168F5D5D9BCC4C23460E6C748EB110E3B1CC836D;
extern const uint32_t g_rgctx_TaskPool_1_tD8D0F0BEE5FD84BD452D08BAEF797705A949C79F;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1U26_tD9CA511DE542C25ECA2007247EEE876577FAC2DD;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1__ctor_m3BB7BECC617FEB915AC0702128F18DEF7B5AD7FD;
extern const uint32_t g_rgctx_TStateMachineU26_t6B378C63C194B525769C87701167FA98D061E9D4;
extern const uint32_t g_rgctx_TStateMachine_t843F5C43B21984AAB95482D56A515769C983A7CE;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1_t7984D032432607DFF92ED70A3EA13751E8D7702C;
extern const uint32_t g_rgctx_U3CU3Ec_t8740916623686F5B17219AD9821FCD0B4F58642B;
extern const uint32_t g_rgctx_U3CU3Ec_t8740916623686F5B17219AD9821FCD0B4F58642B;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__10_0_m164ABA587E23EF24ADA5B3C29A2FF20801A5CAAF;
extern const uint32_t g_rgctx_TaskPool_1_TryPush_m0490FBFCCDED1A6C3CAF0E33E21DD959E73239BB;
extern const Il2CppRGCTXConstrainedData g_rgctx_TStateMachine_t843F5C43B21984AAB95482D56A515769C983A7CE_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384;
extern const uint32_t g_rgctx_U3CU3Ec_t7C3D0A128C7FE7A08181B8169D0ED86340782ED4;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mBE016CF007510DD35CCD481702E2EC22584AD027;
extern const uint32_t g_rgctx_U3CU3Ec_t7C3D0A128C7FE7A08181B8169D0ED86340782ED4;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1_tD19B1701052592CE793BB477BD6C21F47E0C80CF;
extern const uint32_t g_rgctx_TaskPool_1_tE0047B09FFB52FA6431ACFAD6AAA416EE2C79609;
extern const uint32_t g_rgctx_AsyncUniTaskVoid_1_tD19B1701052592CE793BB477BD6C21F47E0C80CF;
extern const uint32_t g_rgctx_TaskPool_1_get_Size_mE0311C9568AA49DDCA88E18F0762D8237A7D676C;
extern const uint32_t g_rgctx_TaskPool_1_tE0047B09FFB52FA6431ACFAD6AAA416EE2C79609;
extern const uint32_t g_rgctx_AsyncUniTask_1_tDD6084428025FD70996F7827C60986262CC9594C;
extern const uint32_t g_rgctx_AsyncUniTask_1_Run_mF71267C6AFAE8A8D4E94B3FDC38BAEB1188C23E8;
extern const uint32_t g_rgctx_AsyncUniTask_1_Return_m69A245CFFE13D0BC3DE83B9A893AA5DC19E3BCEF;
extern const uint32_t g_rgctx_TaskPool_1_t0410F15CE4EF9956E4EB4363351BDCEB4D2FD254;
extern const uint32_t g_rgctx_AsyncUniTask_1_tDD6084428025FD70996F7827C60986262CC9594C;
extern const uint32_t g_rgctx_TaskPool_1_TryPop_m2ECD0CA2B8DE9A6220E235E3791918BD0DAD0546;
extern const uint32_t g_rgctx_TaskPool_1_t0410F15CE4EF9956E4EB4363351BDCEB4D2FD254;
extern const uint32_t g_rgctx_AsyncUniTask_1U26_t31B7C5EC9AF8084BE22A77EA707F4FBA762FEE74;
extern const uint32_t g_rgctx_AsyncUniTask_1__ctor_m730A5A721808231F7D89A54D2F71B98C05BBFA16;
extern const uint32_t g_rgctx_TStateMachineU26_tD00C9C3D0F99BEAC52F572F24B8163A6C94ACF07;
extern const uint32_t g_rgctx_TStateMachine_t0D09B53055D860A62F0546A5C17023488C041D2D;
extern const uint32_t g_rgctx_AsyncUniTask_1_tDD6084428025FD70996F7827C60986262CC9594C;
extern const uint32_t g_rgctx_U3CU3Ec_t9D07E4FBAA7C93CE294D37897737B2C1695F3CE0;
extern const uint32_t g_rgctx_U3CU3Ec_t9D07E4FBAA7C93CE294D37897737B2C1695F3CE0;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__12_0_m577191942F043CE03C13ED914178579C63DAEEB5;
extern const uint32_t g_rgctx_TaskPool_1_TryPush_mB5717C2429923E30B195AC597EC558E0A63C0B32;
extern const Il2CppRGCTXConstrainedData g_rgctx_TStateMachine_t0D09B53055D860A62F0546A5C17023488C041D2D_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384;
extern const uint32_t g_rgctx_U3CU3Ec_t9E066FE481CA201B66D9ACD562012C19113CA913;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mBB156042F841D1C10149B7A40C2D774CAFB48217;
extern const uint32_t g_rgctx_U3CU3Ec_t9E066FE481CA201B66D9ACD562012C19113CA913;
extern const uint32_t g_rgctx_AsyncUniTask_1_t13424CD1A21A4B977A8A022594FF5E961ADF0286;
extern const uint32_t g_rgctx_TaskPool_1_t36DA7C86718C057329BDB51A71240119D125C97F;
extern const uint32_t g_rgctx_AsyncUniTask_1_t13424CD1A21A4B977A8A022594FF5E961ADF0286;
extern const uint32_t g_rgctx_TaskPool_1_get_Size_m5D25419EAE5B23A3B65C3DABFF4A5AE03C6F846F;
extern const uint32_t g_rgctx_TaskPool_1_t36DA7C86718C057329BDB51A71240119D125C97F;
extern const uint32_t g_rgctx_AsyncUniTask_2_tA8840F6D588821E1499C557C05C33B90D01A82FB;
extern const uint32_t g_rgctx_AsyncUniTask_2_Run_m2F5BDEB598285DEF7DA0E2308B6958CB11A550FE;
extern const uint32_t g_rgctx_AsyncUniTask_2_Return_mF6C4E3F12EE579AA6FAB8EA5DCF203D6EBD35A94;
extern const uint32_t g_rgctx_TaskPool_1_tD2C090CF1B7F4F5C770A8A8073147D8FF6C04C1E;
extern const uint32_t g_rgctx_AsyncUniTask_2_tA8840F6D588821E1499C557C05C33B90D01A82FB;
extern const uint32_t g_rgctx_TaskPool_1_TryPop_m349D6C6D2927D95CC5231382494686F98B2941A7;
extern const uint32_t g_rgctx_TaskPool_1_tD2C090CF1B7F4F5C770A8A8073147D8FF6C04C1E;
extern const uint32_t g_rgctx_AsyncUniTask_2U26_tABC89439609B1F2A02454A3637AA5646ACA4D3E5;
extern const uint32_t g_rgctx_AsyncUniTask_2__ctor_m38667112D8D6DC6C38D2FB064AE379B33ED97A79;
extern const uint32_t g_rgctx_IStateMachineRunnerPromise_1U26_tC5D97B516CC113E0D8385B9F46A52DCDEA01D3B7;
extern const uint32_t g_rgctx_TStateMachineU26_t7F3AAAED139AF61896DC9690BDFBC9FFA57B368E;
extern const uint32_t g_rgctx_TStateMachine_tC9472562EC226DE66196D44306CDB1C78EC41B82;
extern const uint32_t g_rgctx_AsyncUniTask_2_tA8840F6D588821E1499C557C05C33B90D01A82FB;
extern const uint32_t g_rgctx_U3CU3Ec_t365BBF1BD6B5919B946F6D6443AE3720B514D02E;
extern const uint32_t g_rgctx_U3CU3Ec_t365BBF1BD6B5919B946F6D6443AE3720B514D02E;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__12_0_m81F8BE96255235518B2C17771FAC67BA44E899F5;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_t16A484564CCCB3C7DC87FFA78D17927369AB0554;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_Reset_m924DC413A244A36213E9BFD6721992562AEB58AF;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_t16A484564CCCB3C7DC87FFA78D17927369AB0554;
extern const uint32_t g_rgctx_TaskPool_1_TryPush_m4034B6F640560BFA07013C4023C17A6F307FF716;
extern const Il2CppRGCTXConstrainedData g_rgctx_TStateMachine_tC9472562EC226DE66196D44306CDB1C78EC41B82_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_get_Version_m34DB7D22FB467DC202756F0EC3283197ED8C39A5;
extern const uint32_t g_rgctx_UniTask_1_t74863FBF1771A0E03403FF56827125CC13D50509;
extern const uint32_t g_rgctx_UniTask_1__ctor_mB0FE5A591E19C6E66AFBC07326F34E50081719C8;
extern const uint32_t g_rgctx_IUniTaskSource_1_tCEC6B5F0253989EA19AE98054CDFD04A9E304A5D;
extern const uint32_t g_rgctx_T_t5C1B451A94816A671DDB175A4E3C5A2526E676FE;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_TrySetResult_m3BAFF20FF693A8B6D5C2373FB1EA864D8B3320D7;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_TrySetException_m34533880284BBA652A3AB2EB091038952C4445EE;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_GetResult_m86C4A3A7CE7F509B4A94E1EDDA26F9C10E576B61;
extern const uint32_t g_rgctx_AsyncUniTask_2_GetResult_m827570FACF4BC61AE8E3BD1BDCD43D8E611415DE;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_GetStatus_m0309C49060FE61D929AF39784B98743715CFBA84;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_UnsafeGetStatus_m9683B66882531158BD8C6261DCD529C7979AECAD;
extern const uint32_t g_rgctx_UniTaskCompletionSourceCore_1_OnCompleted_m002678C705AA45C5D93E07C1C958BA3B69CCC6BC;
extern const uint32_t g_rgctx_U3CU3Ec_t2B5EDDA82A511CFEEE59D9149BD958E4BF1178D4;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_m9E410E202B901545A65EEFDD1E8C1E3563BE10D1;
extern const uint32_t g_rgctx_U3CU3Ec_t2B5EDDA82A511CFEEE59D9149BD958E4BF1178D4;
extern const uint32_t g_rgctx_AsyncUniTask_2_t484FAC6CC888435FC7EAE43D73A2D30F8B283B23;
extern const uint32_t g_rgctx_TaskPool_1_t787A5A7F82B188E5893376B4FD807AA4B912EBAB;
extern const uint32_t g_rgctx_AsyncUniTask_2_t484FAC6CC888435FC7EAE43D73A2D30F8B283B23;
extern const uint32_t g_rgctx_TaskPool_1_get_Size_mE5B7C31AB112CD2E5D1A0EAEB5EC8052F69E9E8E;
extern const uint32_t g_rgctx_TaskPool_1_t787A5A7F82B188E5893376B4FD807AA4B912EBAB;
static const Il2CppRGCTXDefinition s_rgctxValues[506] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t0D1A987D5B5884390D47B04D1ED80E51A6F267B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t686668CB40516B20CFE1036C41CDD75334EB5FFD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Select_TisT_t25F45AA2D7ACFE844C509A95817FD8D8ADCCA165_TisUniTask_1_tE4A02CE4738828686B9403397C82AA4359354E28_m70D2003E90B853141FFA6FFB278B530D1E61D1FD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t0DC6818ACF249C1755CC5B97DCEC686E05BD6A62 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskAsyncEnumerable_1_t1DBE9C5E7B150234B4E0EB9C277951860555041A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCancelableAsyncEnumerable_1_t5D8589C5C9857B3A31B1278C258A1A54E2F22B3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCancelableAsyncEnumerable_1__ctor_m2D65D80DE349D4C638EC56C60C4F3B3EB5F3D787 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskAsyncEnumerable_1_tFD64D58DD9DC2AF76E9A2A12CB99213A4BE7579B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCancelableAsyncEnumerable_1_t4C46EDCC215D7FB93F12BE62BD9890A22F9C7620 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IUniTaskAsyncEnumerable_1_GetAsyncEnumerator_m9A70D4C5D8A62E7B09A5551171F76B192498D990 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskAsyncEnumerator_1_t64E4603380DADC44F3ACDB0AC31982D33C454812 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tEAF63C30B656677A731AFB73BD7916A14F944457 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator__ctor_mBC56450380DF20E050EBAEE756BD30065F081E6B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskAsyncEnumerator_1_t104202E8AE1BC2B32E07E06892485989157910AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t00B2FAB0E19C4C1F72832304B27A60DC776FB8FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IUniTaskAsyncEnumerator_1_get_Current_m7D525F73C66A60295C0916B44E6C5B77EEA6BC60 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3FE4E3207CCFB89B0C54F73B0B84AC95F4006CCD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IUniTaskAsyncEnumerator_1_MoveNextAsync_m256E074E9AE405EC4152676D28B67A1C0386A582 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_t0FD61CC024821E5431A48B37A6F4544299E3CDD0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IUniTaskSource_1_GetResult_mF490F0D773FCC53DCE5A1023ABAE38DCD134E77A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t79A51A6E7E685B277DE33D5A283ED56D86D9D568 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t42F0C4C01D5AE5A6D55DF60DDD0EC3EAB82198BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEF13046172EA0188CCC6CA32632B425D9860C7E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITaskPoolNode_1_t71CB674394D8751B56D9A538CFE28BAC42AFFBE4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITaskPoolNode_1_get_NextNode_m908C3FA45700590083D82950CA147455A2FFCC08 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t8CB822FD18FB480FC71B022CACDB1DBD2757B48B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TriggerEvent_1_t010F2A2D1392C1F6B17DFEECC4ACA5442E8D3D0B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITriggerHandler_1_t52D41DE39AD82F37DB02F76DD54488806432726C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t445B9610901336D8278B01DC231E41B4C314F64E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITriggerHandler_1_OnNext_mBDFFC611B8098C8E687AB79AB1869044B3D5101D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TriggerEvent_1_LogError_m66D8E9BA223D2749399C88851D06F3CFF7652A29 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TriggerEvent_1_t010F2A2D1392C1F6B17DFEECC4ACA5442E8D3D0B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TriggerEvent_1_Remove_mC02D5D56011146055BD3095712E24F8BA547AC91 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITriggerHandler_1_get_Next_mEE03996D499D636D68C737207DF56DDBB0C978F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TriggerEvent_1_Add_m987E70DFDCA138E153E5D776BA2F19FA308834CF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITriggerHandler_1_OnCompleted_mBED34A752159AD9585FE71D9E1AD24377D36FBA6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITriggerHandler_1_get_Prev_m4813EE0134B336C780375F2FCCFDE6A1FABC3939 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITriggerHandler_1_set_Prev_m3B2FAAF6684E4368D7EEB4FEBE95FD029B5298E8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITriggerHandler_1_set_Next_m3776CDFFBBB2DB10E0F1093A19E58E6BA9814598 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_FromCanceled_TisT_tA9FC278366683DE8BF6DB5F8A5B808AABBED91E1_m05985B1063AAB087599A817B5365CA1645A91A5B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_tB3F0C72BE0B88A89A0EB25D2440DF06DA5F059DD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExceptionResultSource_1_tEE5F4EC88263C651F729603709B333E8640BA3F5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExceptionResultSource_1__ctor_m16E6FC7FFD7D415EC5EE2CAEFD173347E2F0252E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_1__ctor_m0D5942405CDF52814251F5F097CFD90F66D37889 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_tC24C95477DA694DF15AE0BD9FA0A9D24EE167B35 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t88446E585C41D6E05834DD0FA984ED3700366132 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t7F96E3493E8F5EC6228114EC9FFAF3F7F20C87D2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_1__ctor_m0A824C2067A86A79CB357C047232B94C3CE9EB11 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CanceledUniTaskCache_1_t661400D5EFBCE3B3E767AD7408637D01853D93C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t442BBB941CC3EF584FB3ACAEB86F4FC8B3DEF4BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CanceledUniTaskCache_1_t661400D5EFBCE3B3E767AD7408637D01853D93C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CanceledResultSource_1_t6181B0F18DBEFE4B392432917824550886C77466 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CanceledResultSource_1__ctor_mC05BF42766EB244EFC7F04B1C321DFB8AB0FCCB5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_1__ctor_mDA3E81ADC0C37E298CC8B3E0ED13948641F7E2FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_t3C00616703C12F2B59724099C6A54B213B6ACF8E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_t3273C78962591AD33725D68D66F62BB937A063CD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1_Invoke_mF230B8AF8F8267648CD925397AF03551D950FDDD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t8AE0C55FA2848C36EA64429D1D3B101F45E6642E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t9E0D53A8564579FD2DCD86744D699C08098C62BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPoolUtil_Materialize_TisUniTask_1_t49538C1007986E3955B609F44C4B5FF46A0E5B30_m56054D97BC2DFC11B4C5AA3A4493B0E277968182 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RentArray_1_t2A29A77B4969023BDB3180A778A301F02E175ACD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1U5BU5D_t8490E5B1FC898B3A6E15009801B7F235832BA316 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WhenAllPromise_1_t5C992E0C72C0A7D6C69CD840B921968C911F63EF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WhenAllPromise_1__ctor_mB66AD647B45A7E1554FB8F2DA27807A4E235B9C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t7A6D2873C212E90D4BC9A1AA96F1015F1688817E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_1__ctor_m2F1259383A67FCB96D8C5D35A09836B4411464E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_t73A879AC2C8692F31064DB5C65ADAEA919BCA752 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_RentArray_1_t2A29A77B4969023BDB3180A778A301F02E175ACD_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CanceledResultSource_1_tBBBD440A0B4218E30186C155241847117171CA85 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CanceledResultSource_1__ctor_m6313747598A72947A6AAF417D40F67490DCBA0B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t7D9DC1B69E4CE4647FA69DCB84DE787F5E88E003 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_1__ctor_mC6A58E3498E6DC7120ACA0B8CDEDD72269C0D659 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_t5D03624EB782A2759E35E3F3872E8FA0327B84B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CanceledUniTaskCache_1_tF99BA2F61400E44457663B09C7A4FEB897A8C495 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CanceledUniTaskCache_1_tF99BA2F61400E44457663B09C7A4FEB897A8C495 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExceptionResultSource_1_t8747BEC7373B22C82BFE06126B0369BAC336DEF5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7B2920B77EEEE92A4A6D0CD7B023D46295248BAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CanceledResultSource_1_t46857C01B833467EF5D277E019F185790335C6CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD7F66045D0A8E18EFDBDA23C9283B19D00DE7F38 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WhenAllPromise_1_t1110C154F19510A5E197DB5B54E4D43FD76E6F15 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Empty_TisT_t60B6CF8C28203B48D068E6B7E3E03FC76C2C7E77_m63B316625CC216AE62EA7D11AB8CD8CE4CE33172 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t144FEA7681CF5D806BC2DD54DD2B96D70FFCD2D0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_t82E70054AC27671C6C1EC99D380929FEE306CBD4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_TrySetResult_m17D75942DBFDBD0BD646383740E02D705B345A03 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_t82E70054AC27671C6C1EC99D380929FEE306CBD4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t144FEA7681CF5D806BC2DD54DD2B96D70FFCD2D0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1U5BU5D_tC238C92282C01FAB88BDDD30F7C2AE928C603B0B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t5746BAFA2E793CC4F829EDACD87EE9BC4464C24C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_1_GetAwaiter_mDB16A3FE5D774D23E69D424971036470B49F8243 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t5746BAFA2E793CC4F829EDACD87EE9BC4464C24C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Awaiter_tB0FA08A07DF5D618D52B44E304E7060E02E2A151 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_TrySetException_mB4C8C215864D008534F39C14D99FA1B241582E77 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Awaiter_get_IsCompleted_mCB3EF58C746F7B85EF90C60C18EEBC4A6BAB6D1F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Awaiter_tB0FA08A07DF5D618D52B44E304E7060E02E2A151 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WhenAllPromise_1_TryInvokeContinuation_m3EA2BE6A85A839B4316F303ABEA75DFCCFFCA9B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WhenAllPromise_1_t1110C154F19510A5E197DB5B54E4D43FD76E6F15 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AwaiterU26_tFDC89CB493C97DFA81325C83F9B3F6B56319EA08 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t9DE031F134C93716DF9CEA73A2E5AC661ECE6CED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t9DE031F134C93716DF9CEA73A2E5AC661ECE6CED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_ctorU3Eb__3_0_mA3C27ECAA98EE1CAC8B27AAF7957E84B6E51E359 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StateTuple_Create_TisWhenAllPromise_1_t1110C154F19510A5E197DB5B54E4D43FD76E6F15_TisAwaiter_tB0FA08A07DF5D618D52B44E304E7060E02E2A151_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m2D26648D9A5D53D42CA9A9EE0D10228988F9A249 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_3_t8314A4163F9D084D06529346E2F034B93C0C49EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Awaiter_SourceOnCompleted_m1FE38E982572EBEFC5F7F72EE695AD34AD233738 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Awaiter_GetResult_mE7FAFDD3146CD26937D5FDD49D5F604AA96A2B68 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t60B6CF8C28203B48D068E6B7E3E03FC76C2C7E77 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_GetResult_m8D1B354208A2FB761BE95F4EA83E34651228FA4B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WhenAllPromise_1_GetResult_mF272164A7AD40BD01EB8CC541CC77074D34836EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_GetStatus_m5EE3E187E8AF6B37AA0FA4F2E17CBFFAF98EE4D0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_UnsafeGetStatus_m6F2F56A39A9C0C0501EDE7D4EB2862AA79A66A2C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_OnCompleted_mDCFE062A0E83610FC3BB34E91943D11DCE77FFD8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tD45809F830386B036560E581844B35915B9A1A8D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mAD007F9975E2AB3855D59959BB758DD3D941DAE7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tD45809F830386B036560E581844B35915B9A1A8D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_3_tE34F6CADAEF69B98C3ECFC0E10586520B1BFA606 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WhenAllPromise_1_tA7F492788B8800275A5D79665B0861CD8A3C6FD2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Awaiter_tE9586E0097E130AC326DC69630930F81A355575C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WhenAllPromise_1_TryInvokeContinuation_m5127020040F88DCE8756C983EDE21321D3C526C8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WhenAllPromise_1_tA7F492788B8800275A5D79665B0861CD8A3C6FD2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AwaiterU26_tD70EFF1E918006467F027BE017E61317E538545F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_tEB59AFFFA85AA8B066D23E2900B771C00B8E64F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_tB0198B9E7857907EC6C1304D56629B16AF190BD5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFF5A4BDB19D1BCCEC9E3BFEC979335AAEF368430 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IUniTaskSource_1_GetStatus_m9A8B1FE52684695A11A1F410906F64D63C5A0B71 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Awaiter_t12412F44D0A1D4C6573C6EDD4F933E1089C86DFC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Awaiter__ctor_m6C11E745E474B2896035A9AD17327C659C66A372 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1U26_tAB23634523E0A46C0AD458B9872EF6A88783307B },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tFF5A4BDB19D1BCCEC9E3BFEC979335AAEF368430_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1U26_t2E3A445F06B855768183F49CBDF2869B0DC96233 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_tD8AB010F422D5AFB8EE7B134A25B47A5D55F7990 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Awaiter_t456F33FED0549DFBFB206F47154716115A2D4743 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_1_get_Status_mDCEF5E82A62E56A8861B8891A9EBA1FD4F655A9F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_tD8AB010F422D5AFB8EE7B134A25B47A5D55F7990 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_tAE79514CEA48BB1902DDD9EB893A14408BF095E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t51191FDA2C225152DFBE7756A21D31A184B705B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IUniTaskSource_1_GetResult_m24C7C1EDF8D0E0C2BF5E34D906A7F176D9E335B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IUniTaskSource_1_OnCompleted_m51F313ABFDBFF3F9CECBDFE6BF6FD20EE8C6D52F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_ReportUnhandledError_m5482D76A6989087D5B8A909DD942BFA8ADDA835C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_tDD9850FBEA4B9979B4280A64F124CBF3019FC8BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_tDD9850FBEA4B9979B4280A64F124CBF3019FC8BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_tFF5B28A72089716BF74B3DB0EB8ACECAFC2E102E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_ValidateToken_m67B5C0384AFA0A3A909F62311BFE1864CE7E0F13 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1_t3C604AF18625B9E3164784AFC7BB09279C54F371 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1U26_t43AB8658BEE9752AAB5B0E29A6E8CD4CFEF4EAA4 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1_t3C604AF18625B9E3164784AFC7BB09279C54F371 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t833E5A06AE5510DB5A7BB67E1ACC3F624E3A7715 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t833E5A06AE5510DB5A7BB67E1ACC3F624E3A7715 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__4_0_m1A2CD3F66BE5ADAC3AA153A84B2B013F660D7E4A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tC6E37C899BB2A5699AC4709B0C568820BBFF13E5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1_t3C604AF18625B9E3164784AFC7BB09279C54F371 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPop_m91AA17CD3A6F907C8EE5C1EE6CC41479A48CF0CE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tC6E37C899BB2A5699AC4709B0C568820BBFF13E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1__ctor_m857513E8EF03682A0AA338B29CD048C1F3312B15 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_t84D8D71591E04FAE458DBA8940EA93416EC18BFE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_get_Version_mF3C4C3A410B3467A3184C33D1D8990D8494554FD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_t84D8D71591E04FAE458DBA8940EA93416EC18BFE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1_Create_mAD1121675A6F54A8AC3EC3A4F2A6E80AA704FFCF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1_TrySetCanceled_m2FB6DAC0855A440D19A03F069FA350D08B9BDD26 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_TrySetCanceled_m48BC55CB26B09E48D4E238ED74CC2963392F2F6F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_GetResult_mBC334DC198E0830FEA064CFC03EA9548FC37318B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0DDC8581344971C14D77D8F311A3B27F803B0701 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1_TryReturn_m1DC0C14AFE333690A92A3139AC0A43DF10B5668E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1_GetResult_m006344F246FD11DF8B66EFEDE66FE08EE511F012 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_GetStatus_m7ACC929E51A93C37225DE5FA83CA7F8495E09F04 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_UnsafeGetStatus_m58D7255E5C3825F0DA353FB859A159A6EA79668E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_OnCompleted_m8C9B8B67BED3BFD04BCCBBE822092953FE020BB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_Reset_m742E0DC4DD4DB325C8EE0C8910D1ABA2A51385AA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPush_m6D4CC90D9BF26FA6E9BBB065B557A99E8307C6C3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t1E86A046B6EAF2684FFDAF50F62203D36DC767DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mB541A16B553C77B0AFA9702546173782392AA3A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t1E86A046B6EAF2684FFDAF50F62203D36DC767DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1_t4B6C9C2AA7FCBE4A3A391146C5A0F13B9D20ECEB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tC5D9BB34915E773EE546C71382440CA5DE40FCDF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AutoResetUniTaskCompletionSource_1_t4B6C9C2AA7FCBE4A3A391146C5A0F13B9D20ECEB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_get_Size_mE46D95D7E78FB229A4B9A207304B272E41F4346D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tC5D9BB34915E773EE546C71382440CA5DE40FCDF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSource_1_tD602B014F01E56925DF5A9C5C4DBE5C9DC09F574 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t25D1EDF6628FDDB8A50A5158D002B0866FD73BF3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_1__ctor_mDDD55352E696D8BE7D26405D29D1B301A854B15B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_tFAE9805A7EC70BB26CDD8B4535E6CBB567906229 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSource_1_UnsafeGetStatus_m5C6AEB26492202A4A5CE0940E8467F0C274D6492 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t817F087709DFE1FAD5E336877C268687B2696CE8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSource_1_TrySignalCompletion_m7B4C27786379DF1F2F4DEDBBEA8E3F4C0DCD364D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSource_1_MarkHandled_mA8FEB8494AD8CEECDC3FE87D3225DA5BC4977C92 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSource_1_GetResult_m54543A02A614A48EB567DD7F54A02186FF657773 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncTriggerBase_1_tEDA7873489E20B961A34EA1CEECC30FD5BD4ED10 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TriggerEvent_1_t3E087463A2048A0FE777889514456C02A61B390B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TriggerEvent_1_SetCompleted_m0A5A0BBB4F569AD992CE4D48EA4AF723AA73A427 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TriggerEvent_1_t3E087463A2048A0FE777889514456C02A61B390B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AwakeMonitor_t047632621825C2065E436E6BE9E5893CB2B060AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AwakeMonitor__ctor_m3EE617FD03320290D158A82DAE500576BE5862E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITriggerHandler_1_tB74D4CAF404506DCE33C217F4F8C749A01DF6B8D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TriggerEvent_1_Add_mAA918657D63DAF361EEDB912FD8E5E3AD6DBA3AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TriggerEvent_1_Remove_m948FCF70219D54743F35DB44BA76A3A2292FFD5A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t670FFF1354E0A266BDC3591646E01F2C372AA6E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TriggerEvent_1_SetResult_m496A5D555EE04F7A8CF2CB514B0083AD730584A8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncTriggerEnumerator_tC449EA3D72513ED478564E4E875A0D2580A1EBEC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerEnumerator__ctor_m178113C81038829F68ABA7FA36DFF335437A8CFF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskAsyncEnumerator_1_t6EBD209A80FDCE0590DDD493B5DF3936554073DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncTriggerBase_1_t437CF1A5DB0D688A8DD14AD09F2A538388047C82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncTriggerEnumerator_t7249198289B65AB5C1483B6CEB0180DA727B9D96 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8D5813A798A27605A635E9FA61B53505378D0340 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerEnumerator_set_Current_mB18402D3B634EB5B676A8D08CFE285FA7D3B0E17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerEnumerator_DisposeAsync_mC668EA9CE4345812365E7EBF36FC657884C5DC9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITriggerHandler_1_t1D533B54D5D40486E780463AD4F70A61F25EB2FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerBase_1_AddHandler_mE89364847D6C9ABA23EE6799508741C3F6E0CB64 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncTriggerEnumerator_t7249198289B65AB5C1483B6CEB0180DA727B9D96 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerBase_1_RemoveHandler_mE3E9AF8909FEA4DC34499CC75382DEEF66AC64DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerEnumerator_CancellationCallback_mBD49DBC0CDBB9BDDF781A6A61D8687A2371C2CB6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncTriggerBase_1_tFCE6DB48A2369526332DFFF3FAB652F5A50F29CE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AwakeMonitor_tFE3AE8A412EE5595BB66FE61C355840D39D8C8B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerBase_1_OnDestroy_m6BF311C76F7A0B0C63A7EE67ADA27487D89C4196 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncTriggerHandler_1_tBEFD5DBC560056C538F7465205DD4C3A5E2E4CC2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_t5AF574FB7ADB19F7465BABE71DF5EA5C481136F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_Reset_mA3D234EB9D372F51EE97743938D5F4122524E6F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_t5AF574FB7ADB19F7465BABE71DF5EA5C481136F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_get_Version_m315BC0186C87013B4990F5C2BCB35839FF33AC5E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITriggerHandler_1_t22A753D10854BD1AFB42407F32C8F0478A6167CA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncTriggerBase_1_t3EE0A624E3B2A6F01B5B587DD38A169CF9199311 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerBase_1_AddHandler_mC32965FE69F5BAC3C377F7BA59F91B5DE682C320 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncTriggerHandler_1_tBEFD5DBC560056C538F7465205DD4C3A5E2E4CC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerHandler_1_Dispose_mC7E4B8BF4B894FCC7F962770139C4DD39445D35B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_TrySetCanceled_mAE5360D768BD854C2BF98BEDC403B6B44E72ECD9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerBase_1_RemoveHandler_m3786277294CBA28166F205A06013DDD6B5FD63F7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_GetResult_m4DD655E9F6BAB190AE04F7590A00D6A153E53D5A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t08270E63CB32F6E28BBD0849175774F469D9FEB5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_TrySetResult_mFB6E06FC36B90F6E600E5ABFC8E0094DEA1182D3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_tC0226A55B6BDFEC90DD694F13FEDF68B43F61845 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IUniTaskSource_1_GetResult_m7A879765F6766C94DF793E8B06D0136AC3E88FFD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_GetStatus_m5B0EF792364FC896849FA626121E7B2765767DDF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_UnsafeGetStatus_m0F54E4BB0969BE257B0230EFB3AB5EB0A635B421 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_OnCompleted_m743F138BDD1EA571B4B56D0DDE901A22350120CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncTriggerHandler_1_CancellationCallback_m2052B714C14C75D9B691F6F1C96E2268134CE9C4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MinimumQueue_1U5BU5D_tAC46D497979D9C2B1528E5DD4F45A28E151432CD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPool_1_tEAC60EBC56FA39A47D82CF520CF3F0E4465DA612 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MinimumQueue_1U5BU5D_tAC46D497979D9C2B1528E5DD4F45A28E151432CD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MinimumQueue_1_t3ABDED1048C64AAC761336650137D377A2CB44B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumQueue_1__ctor_mDA9CAE81C47A9FF51EA23BF98F2F48167BB9242C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t0561E4853D3F3ED88D37A9CC6631443BBD68643C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPool_1_tEAC60EBC56FA39A47D82CF520CF3F0E4465DA612 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1_CalculateSize_m5E9E3008415DD7FF974D012732AD048D78F544FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1_GetQueueIndex_m131E21588BC6CAB6A1D768AB6B8E9C665A3F2361 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumQueue_1_get_Count_mAA4651E2A7E205CB8DA53B25BE7C1A4EF284EE90 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumQueue_1_Dequeue_mAF6D06F5F3CC55854E74140C0C6564D4DC30AACC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t0561E4853D3F3ED88D37A9CC6631443BBD68643C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumQueue_1_Enqueue_mC9D594466B86871EEF85C35EFAB5DFFF27EFF9B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1__ctor_m9174D8BD5C70A3961126F8724A64C1795A5D81D6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tD2D10643DFFB23BEE0B9B443F2FC4C28F26698B3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPool_1_t47548AC30F0D307EE1C45DBF502A60EE53BAFD6B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPoolUtil_EnsureCapacityCore_TisT_t462D1E6B1659F56BDF88759C4886E6735FCD4056_m970A54254377E6FED8BDB9E8A6A8A464525B7530 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tD1F2AC66B8AF924EE97AFDF48655D9A045BAB2D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPool_1_t7878629DFDD19E27503B383C18453CA9B01C7453 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1_Rent_m3BEDB108AFAFB71DFAF81BACCBD82328C109319F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t4030640B5EACEC2E24850A063E58604705551BA5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_RuntimeHelpersAbstraction_IsWellKnownNoReferenceContainsType_TisT_tFA3BFD656B7C1AF7B85FF067A7C0194E80D8FAA6_m7BC465E20B06E12FC8DF3B90E018E9C0974C6327 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1_Return_m928ED805CD5349EB5E2F9033D519004F955E4644 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t1FC05323E4E2C683D14114B4C49B81F8096DB58E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t02C2F3B6FE25A3842371A0A95F27B4C749FFA515 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RentArray_1_t212AF77FBA14B01F4FC74C7C0F76DCFE6BF488E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_RentArray_1__ctor_mC1486BEDBAE117829444ACA6560BAD772A183607 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPool_1_tBA6CE4D8CD875BCF50DD37080D0DBE8CEFB88655 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_tA6A02772870E6B52E82143C30D41C992E8D4475A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_mDFF06279697C8E713ABEA547A0187AE0689674BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Empty_TisT_tF392A294880B3667DF7E2A8AC900FF2BD91BD36A_mC11C21DDC93215CC4EAC26EE7B129B4C422FB262 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPool_1_tBA6CE4D8CD875BCF50DD37080D0DBE8CEFB88655 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1_Rent_m34F877D31F3474FB364E0A2549809B5C9C5F53B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_CopyTo_m7098A3C1B663ABE951ED4E7469767D38544A73DE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IReadOnlyCollection_1_tB4E75D3A623ADB7555DD0933ABD3B4241FD7C444 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyCollection_1_get_Count_mB7D9CEFE605BEC01956585475E07D26533C6D7E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m8BA50B8582D12A69881BC15784DD0DBA0A083C46 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tB11F00945A35CF1527CEF944CF6C6D9AEF96380E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_mBB07DA0C2937EC7BEC10A5B500FF270359ECE1FF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF392A294880B3667DF7E2A8AC900FF2BD91BD36A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPoolUtil_EnsureCapacity_TisT_tF392A294880B3667DF7E2A8AC900FF2BD91BD36A_m8C33D6ED68CD36346A870006967B738833EC606C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t55F1B763700598FA0FE0F40210AB4E2F0FE8F76E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF8338F8EA7EF039709E5C2DDE33FF0A1BDFDE006 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RentArray_1_tC8603A0D669621EE68C673BE661EF13D57D1D62C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPool_1_t14E311D71A5B0AC4F2D1CBB3398B948A121B15B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_RuntimeHelpersAbstraction_IsWellKnownNoReferenceContainsType_TisT_t53531E4F8898ADF8FBA91425866F13BDF5C6A89B_m283F0513F5BFC2A32CAF25122B969F273CAEFC72 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_RentArray_1_DisposeManually_m77D729BFA124B72D630FD127260525DDAACE2CB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RentArray_1_tC8603A0D669621EE68C673BE661EF13D57D1D62C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1_Return_m055CEA17FD79DCEA1A2B16B1A532DA45B854C54D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t944AFB1614D28C7AF409C51588C58D6ABF02F6A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB029863504A8441A8EC4387B933B84CF6C7CFCAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2B176C91D05FE374AA3F2FC3687C06A1300B6FEF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MinimumQueue_1_t52BA5756E63A75433F15EAF13DDAF2C80B52EE1F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2B176C91D05FE374AA3F2FC3687C06A1300B6FEF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumQueue_1_Grow_mB8434C25203AE977F656D410880FF5C608BF6D07 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t04131A8E473FE0839BDD6400A2659A9A9C8A7072 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumQueue_1_MoveNext_m9FA08F459599E03AF643F0EE4AA1B6AFEA11E2AB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumQueue_1_ThrowForEmptyQueue_m9073B946F5777B6718A7E09EA4F6A1E3BBB877A0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumQueue_1_SetCapacity_m0166E47126ED51C0CD0B0805DE9F9796AE5C0AC1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PooledDelegate_1_t58A21864D5BCAF81521ADA5219CC93B0294C3B72 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PooledDelegate_1U26_tBAD91125E80C8723B9813396F1D770CE7A59B20B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_PooledDelegate_1_t58A21864D5BCAF81521ADA5219CC93B0294C3B72 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t6FEFC77E1487EF255CA9758F47CB334003335D18 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t6FEFC77E1487EF255CA9758F47CB334003335D18 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__4_0_m888558D1C315BF3900626F2F4F09EB5CE9BD0B25 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PooledDelegate_1_Run_mE9937A4E119D2B208328573576FE832E68573A0B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t06BF52E84469BF9FBB43339B8B5996A4945B00B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_m1A7B0EC8533820EA1C816F2B8164E4F842C31FE2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t47C064D0A9F16D3DB615AC0641A12E9B050EDF2C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PooledDelegate_1_t58A21864D5BCAF81521ADA5219CC93B0294C3B72 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPop_mAB7CA2EBECF11CC759D63D08E0E983C5E58067F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t47C064D0A9F16D3DB615AC0641A12E9B050EDF2C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PooledDelegate_1__ctor_m9890DC55DB4DED622CF05343D25555F93B43766E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPush_m85AADC03378AE91336DE7301A3E98F18F7349B79 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDAECF6AB8E78CFB2184242E7143A92D1D25B422B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t49269800EED0CB2AA7E97958550C637BE307B589 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mA50C01EF65624E6192DA751512E07B600C79D7A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t49269800EED0CB2AA7E97958550C637BE307B589 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PooledDelegate_1_t53DE61D40DFCE4551713AA7A1CDDF54EA8E0D20A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t4154B80AC5C532D1DB0F28BD229B5640284C5F33 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PooledDelegate_1_t53DE61D40DFCE4551713AA7A1CDDF54EA8E0D20A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_get_Size_m8769DD97F3123C2C96504DB40B38CBDFA481CBBB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t4154B80AC5C532D1DB0F28BD229B5640284C5F33 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WellKnownNoReferenceContainsType_1_tA057C55C5DB0DBB75037E59317C56315C56578C0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WellKnownNoReferenceContainsType_1_tA057C55C5DB0DBB75037E59317C56315C56578C0 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t3D448D571AD9B5B361AFA2CA60C8E7EBB81B23E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WellKnownNoReferenceContainsType_1_t40B9DC7D99DF47BAD74AFB7312CC5726EE61B58E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WellKnownNoReferenceContainsType_1_t40B9DC7D99DF47BAD74AFB7312CC5726EE61B58E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_tA519426369CEC336FD0D430FB1AD91DC379D86BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StatePool_1_Create_m766FECDB726DBEAE58B246C68E6C304913CFC452 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_1_t330C5CB64A5E66A1D940139C886C1A19587FFC4A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_1_tF62C182C34A430C6665876937A7416330E6791CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t1D01B7F31030A07CCAED2A89B08ACECFEF02E917 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t7BC53660A65338D580A336E60B5D20D6E68635FB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StatePool_2_Create_m1832F6EC3634012444B99C3A0BB489274A708ABF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_2_t11DCCDE8450A6E3857C68E88835103A69CF2A315 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_2_tAB587F05004A24E84DDE4D855E7D3014D124E68A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t0B72D1B80F7D3C71A0BCE98137ECACE4BCDA749D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t958897145B9EEACC83B1CE14AFE4E8D6E10F2026 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T3_t343451D3CAA0D4B748CC818AE8AA09DFD28BDF46 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StatePool_3_Create_mB19C5A35370FF82CEAAA9DF9FB0A082D92A4DFB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_3_t480CACA3DF75909104A8F1597C062325F6369C61 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_3_tE996AD215D6BF6554A5C696FFA97DE9602F5F64D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StatePool_1_Return_m3F6825A1329AAFDFCEC2B968E72540D9F760C647 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_1_tFB2B01ED23AF54F47A1A03D6FCD894A6367E94D6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_1_tA5F9C4E47278414E7943AE6509A075506181136E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_1_t36A7570E6B305FB4C452226FF5B57F92C4FB1461 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ConcurrentQueue_1_t421A5F586919D644AA760058878DFF2A085293D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_1_t36A7570E6B305FB4C452226FF5B57F92C4FB1461 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ConcurrentQueue_1_TryDequeue_m1017CCE4D54E7DFDEF4C7BBC7B055B310DFC1552 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_1U26_t2F86B433E9ED733F5D6F5D280C9969D70844FC9D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t51CFE2605B86B56A2421F975C11C825B7387729C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_1_tF3111A3829CB6F50A7061A4412C00523D18DE157 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StateTuple_1__ctor_mC9E61D79711C5D2173B0ED7BE23A0BBA9B82358C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ConcurrentQueue_1_Enqueue_m4A038AC67594B96AA582D5ABE169CBCAFAEFD7F7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ConcurrentQueue_1__ctor_m39F27F58BDA941ED2B4D1B53D9592D7813A5C89A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StatePool_2_Return_m1202DEDE0EDE2653357704A5B5FAC6D17DBE6788 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_2_tF003B75461B4E4F35752503683AE3B342179A922 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_2_t6A9B6CDA18E5C76D42BD528191288F8AC318991F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_2_tC70DDB4DE613E4C63E27EB30F18F981465ED7425 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ConcurrentQueue_1_t219121BCEB8FEA61BD8EC605BFFEA1B6CCE8946B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_2_tC70DDB4DE613E4C63E27EB30F18F981465ED7425 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ConcurrentQueue_1_TryDequeue_m4870BD1F8B9BC1A79482BCC6F9EC2EE6DDB9D01C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_2U26_t6177ABC01EE1146349CC88DFD2AFD579D92662A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_tE9322E0318F5FBD75E16C0D6D467BD89AFEC4AD9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_2_tDE2270E3EF9B27E638CD82F45C46A9CC961D9818 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t89B6553AD9FE17950E023CAAAED19A0BA17E1347 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StateTuple_2__ctor_m21A6C568D17F768AC970AAAE253BC08A11706579 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ConcurrentQueue_1_Enqueue_m44FAE83B8BA90F53665EB6D4F02AAB4A51C54A4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ConcurrentQueue_1__ctor_m662BE23571F85AA01440B4094957C8B3A161C3AC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StatePool_3_Return_m2699E748F87ED4692B2B14D0256BEBF943AE715D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_3_tAC1E0B3E4DB3BB6C0016A0722AC595184B6D6896 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_3_t1C98FD1B16AC5A9EFC118BE93407DDF9A5D163A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_3_tE766EC840FCB71ABE80C031A8C7CC786B1CFEF7E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ConcurrentQueue_1_t9024E65A6B68CC7A12AFC0110C0C273E87B2F64E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StatePool_3_tE766EC840FCB71ABE80C031A8C7CC786B1CFEF7E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ConcurrentQueue_1_TryDequeue_m69BA6D4B64FD1B1ED1F54AA830E4D3872D523698 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_3U26_tF5C4EAACD77E7440E990E30CEA142EDE24CDF833 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t899F401FAA2128951380171F4EB6ED85D3EAA2FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StateTuple_3_t458A0ADE756B87D410265479AFA9E2437FB5BE23 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t0F0ABEFE350A1561886458236A2990757EC8C02C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T3_tF058B5DD9291DE0D635B3FCCF6E267E0E0B92A1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StateTuple_3__ctor_m226EDC85C78A03D07540AF7E8F2F6E439310DDA6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ConcurrentQueue_1_Enqueue_m624BFF72F0CA66FCBB9B6017478CB14315D4EA6A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ConcurrentQueue_1__ctor_mF8D177FE3BBB79FADEAB92AAB3BDB1C640616200 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachineU26_t7E595B604DD907CE9F0BEA030468D9615DE6E8CD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTask_1_SetStateMachine_mD4F3960BEFDA44DC672042EDC37042AC088DBDDB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_1_t2C06823C7B86C4DAA5FD5DC1AFFEA22316790DF2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAwaiterU26_t8796FC98BE52792DF9834A87F3A5B63EAF21AE1D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAwaiter_tFE66B7F561EDB03E27BEFD39140B18AAA045B55E },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TAwaiter_tFE66B7F561EDB03E27BEFD39140B18AAA045B55E_ICriticalNotifyCompletion_UnsafeOnCompleted_m09734062A0D335816D9511A291F23F4483E67219 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachineU26_t5B52C5D174CC1DFEA9FC87F4E93832D8E9BAF51E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachine_t6AFAEEFDBDF37C2AAA352D2523BB2E5C1C3110E7 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TStateMachine_t6AFAEEFDBDF37C2AAA352D2523BB2E5C1C3110E7_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTaskMethodBuilder_1_t4A206AF7A38D216DBB4742CAE359E8CCEA1A3F50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IStateMachineRunnerPromise_1_tEF77604B8BAB812E6C3EAEC71A3457AD755627F1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IStateMachineRunnerPromise_1_get_Task_mFDC52D6BF30A2AF0E1677106DEDAC8D501EB123C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t21857807D52B0D42492371AEA605C8D31B640C92 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_FromException_TisT_tFD8DB5F523A69F458964B5BC1138077595EF73A7_m7AF24110AFE711450E2B7E35EE142D01FD1378F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFD8DB5F523A69F458964B5BC1138077595EF73A7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_FromResult_TisT_tFD8DB5F523A69F458964B5BC1138077595EF73A7_m662B8E8D159A25BD16081416241CB2C99853066A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IStateMachineRunnerPromise_1_SetException_m3EF50C8F1E01E1CFEEF7A7C3518C9685B81EA70E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IStateMachineRunnerPromise_1_SetResult_m0BE6BA7412A9D21443D0774C8F61C1096B02A75D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IStateMachineRunnerPromise_1U26_tF52B5B0483EB33D15A5E2B7CA68F896CE2896DCD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IStateMachineRunnerPromise_1_get_MoveNext_mDC5CE79508C5FBFFAEEAEF088A953DEAC6AC8186 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachineU26_t01F3917053002BC1605B1CF916E1641CF3C70C98 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTask_2_SetStateMachine_m22AB6B2B4FAC4E69F630C879AAE4B6D44FC29E39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_2_t352026692240B87108BE37E5F261F6A967EB7189 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAwaiterU26_tB3D81A91C4B0B2381C8C04918F9D955F5449D33D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAwaiter_t015830761D949D160F916F43B0B5EB454646739F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TAwaiter_t015830761D949D160F916F43B0B5EB454646739F_ICriticalNotifyCompletion_UnsafeOnCompleted_m09734062A0D335816D9511A291F23F4483E67219 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachineU26_t5F7430DCCB1B012A3D0AE295A8C327EDE971B006 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachine_t1F20CA5AFE89239F6D44E6BCF4775C6C02982766 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TStateMachine_t1F20CA5AFE89239F6D44E6BCF4775C6C02982766_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachineU26_tBD669B5515AC892AA4A661083303E51AA072B5E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTaskVoid_1_SetStateMachine_mAEE08CC14213290D6A0DDFBC4BC629B498C2AF5B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTaskVoid_1_t4908E6BDEFF2E6C289E37D01D73EC9864A1C1696 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAwaiterU26_tB0189391064E425D0970D2A7AF168C7CD878A17E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAwaiter_tE0E5FF0494AA0E527224994045013FBB3CA4431C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TAwaiter_tE0E5FF0494AA0E527224994045013FBB3CA4431C_ICriticalNotifyCompletion_UnsafeOnCompleted_m09734062A0D335816D9511A291F23F4483E67219 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachineU26_tC33A41449FC97FA6184B95A42C43DBBD30577AB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachine_tF1DA3C63E6FDD47892B8E74DD16D3FBC1B22849B },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TStateMachine_tF1DA3C63E6FDD47892B8E74DD16D3FBC1B22849B_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTaskVoid_1_t7984D032432607DFF92ED70A3EA13751E8D7702C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTaskVoid_1_Run_m45D5761EDCB5D3B87D6C51DE82E1A068297D5F47 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTaskVoid_1_Return_mE65BD9FBC6FC1E4B2D9A528C51262108D8A30CEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tD8D0F0BEE5FD84BD452D08BAEF797705A949C79F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTaskVoid_1_t7984D032432607DFF92ED70A3EA13751E8D7702C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPop_m168F5D5D9BCC4C23460E6C748EB110E3B1CC836D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tD8D0F0BEE5FD84BD452D08BAEF797705A949C79F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTaskVoid_1U26_tD9CA511DE542C25ECA2007247EEE876577FAC2DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTaskVoid_1__ctor_m3BB7BECC617FEB915AC0702128F18DEF7B5AD7FD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachineU26_t6B378C63C194B525769C87701167FA98D061E9D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachine_t843F5C43B21984AAB95482D56A515769C983A7CE },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_AsyncUniTaskVoid_1_t7984D032432607DFF92ED70A3EA13751E8D7702C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t8740916623686F5B17219AD9821FCD0B4F58642B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t8740916623686F5B17219AD9821FCD0B4F58642B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__10_0_m164ABA587E23EF24ADA5B3C29A2FF20801A5CAAF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPush_m0490FBFCCDED1A6C3CAF0E33E21DD959E73239BB },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TStateMachine_t843F5C43B21984AAB95482D56A515769C983A7CE_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t7C3D0A128C7FE7A08181B8169D0ED86340782ED4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mBE016CF007510DD35CCD481702E2EC22584AD027 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t7C3D0A128C7FE7A08181B8169D0ED86340782ED4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTaskVoid_1_tD19B1701052592CE793BB477BD6C21F47E0C80CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tE0047B09FFB52FA6431ACFAD6AAA416EE2C79609 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTaskVoid_1_tD19B1701052592CE793BB477BD6C21F47E0C80CF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_get_Size_mE0311C9568AA49DDCA88E18F0762D8237A7D676C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tE0047B09FFB52FA6431ACFAD6AAA416EE2C79609 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_1_tDD6084428025FD70996F7827C60986262CC9594C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTask_1_Run_mF71267C6AFAE8A8D4E94B3FDC38BAEB1188C23E8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTask_1_Return_m69A245CFFE13D0BC3DE83B9A893AA5DC19E3BCEF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t0410F15CE4EF9956E4EB4363351BDCEB4D2FD254 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_1_tDD6084428025FD70996F7827C60986262CC9594C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPop_m2ECD0CA2B8DE9A6220E235E3791918BD0DAD0546 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t0410F15CE4EF9956E4EB4363351BDCEB4D2FD254 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_1U26_t31B7C5EC9AF8084BE22A77EA707F4FBA762FEE74 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTask_1__ctor_m730A5A721808231F7D89A54D2F71B98C05BBFA16 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachineU26_tD00C9C3D0F99BEAC52F572F24B8163A6C94ACF07 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachine_t0D09B53055D860A62F0546A5C17023488C041D2D },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_AsyncUniTask_1_tDD6084428025FD70996F7827C60986262CC9594C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t9D07E4FBAA7C93CE294D37897737B2C1695F3CE0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t9D07E4FBAA7C93CE294D37897737B2C1695F3CE0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__12_0_m577191942F043CE03C13ED914178579C63DAEEB5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPush_mB5717C2429923E30B195AC597EC558E0A63C0B32 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TStateMachine_t0D09B53055D860A62F0546A5C17023488C041D2D_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t9E066FE481CA201B66D9ACD562012C19113CA913 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mBB156042F841D1C10149B7A40C2D774CAFB48217 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t9E066FE481CA201B66D9ACD562012C19113CA913 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_1_t13424CD1A21A4B977A8A022594FF5E961ADF0286 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t36DA7C86718C057329BDB51A71240119D125C97F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_1_t13424CD1A21A4B977A8A022594FF5E961ADF0286 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_get_Size_m5D25419EAE5B23A3B65C3DABFF4A5AE03C6F846F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t36DA7C86718C057329BDB51A71240119D125C97F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_2_tA8840F6D588821E1499C557C05C33B90D01A82FB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTask_2_Run_m2F5BDEB598285DEF7DA0E2308B6958CB11A550FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTask_2_Return_mF6C4E3F12EE579AA6FAB8EA5DCF203D6EBD35A94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tD2C090CF1B7F4F5C770A8A8073147D8FF6C04C1E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_2_tA8840F6D588821E1499C557C05C33B90D01A82FB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPop_m349D6C6D2927D95CC5231382494686F98B2941A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tD2C090CF1B7F4F5C770A8A8073147D8FF6C04C1E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_2U26_tABC89439609B1F2A02454A3637AA5646ACA4D3E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTask_2__ctor_m38667112D8D6DC6C38D2FB064AE379B33ED97A79 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IStateMachineRunnerPromise_1U26_tC5D97B516CC113E0D8385B9F46A52DCDEA01D3B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachineU26_t7F3AAAED139AF61896DC9690BDFBC9FFA57B368E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TStateMachine_tC9472562EC226DE66196D44306CDB1C78EC41B82 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_AsyncUniTask_2_tA8840F6D588821E1499C557C05C33B90D01A82FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t365BBF1BD6B5919B946F6D6443AE3720B514D02E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t365BBF1BD6B5919B946F6D6443AE3720B514D02E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__12_0_m81F8BE96255235518B2C17771FAC67BA44E899F5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_t16A484564CCCB3C7DC87FFA78D17927369AB0554 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_Reset_m924DC413A244A36213E9BFD6721992562AEB58AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_t16A484564CCCB3C7DC87FFA78D17927369AB0554 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_TryPush_m4034B6F640560BFA07013C4023C17A6F307FF716 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TStateMachine_tC9472562EC226DE66196D44306CDB1C78EC41B82_IAsyncStateMachine_MoveNext_m787AB6611D2DA2682C08AE038BBA9096C7C44384 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_get_Version_m34DB7D22FB467DC202756F0EC3283197ED8C39A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UniTask_1_t74863FBF1771A0E03403FF56827125CC13D50509 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTask_1__ctor_mB0FE5A591E19C6E66AFBC07326F34E50081719C8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IUniTaskSource_1_tCEC6B5F0253989EA19AE98054CDFD04A9E304A5D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5C1B451A94816A671DDB175A4E3C5A2526E676FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_TrySetResult_m3BAFF20FF693A8B6D5C2373FB1EA864D8B3320D7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_TrySetException_m34533880284BBA652A3AB2EB091038952C4445EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_GetResult_m86C4A3A7CE7F509B4A94E1EDDA26F9C10E576B61 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncUniTask_2_GetResult_m827570FACF4BC61AE8E3BD1BDCD43D8E611415DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_GetStatus_m0309C49060FE61D929AF39784B98743715CFBA84 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_UnsafeGetStatus_m9683B66882531158BD8C6261DCD529C7979AECAD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UniTaskCompletionSourceCore_1_OnCompleted_m002678C705AA45C5D93E07C1C958BA3B69CCC6BC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t2B5EDDA82A511CFEEE59D9149BD958E4BF1178D4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_m9E410E202B901545A65EEFDD1E8C1E3563BE10D1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t2B5EDDA82A511CFEEE59D9149BD958E4BF1178D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_2_t484FAC6CC888435FC7EAE43D73A2D30F8B283B23 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t787A5A7F82B188E5893376B4FD807AA4B912EBAB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncUniTask_2_t484FAC6CC888435FC7EAE43D73A2D30F8B283B23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_get_Size_mE5B7C31AB112CD2E5D1A0EAEB5EC8052F69E9E8E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t787A5A7F82B188E5893376B4FD807AA4B912EBAB },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUniTask;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UniTask_CodeGenModule;
const Il2CppCodeGenModule g_UniTask_CodeGenModule = 
{
	"UniTask.dll",
	1166,
	s_methodPointers,
	47,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	61,
	s_rgctxIndices,
	506,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUniTask,
	NULL,
	NULL,
	NULL,
	NULL,
};
