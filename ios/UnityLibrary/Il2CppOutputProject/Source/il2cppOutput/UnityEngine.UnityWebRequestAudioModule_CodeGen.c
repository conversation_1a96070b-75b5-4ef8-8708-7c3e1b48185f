﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void DownloadHandlerAudioClip_Create_mF088040A27BB328CB057563702E95B57418F1B71 (void);
extern void DownloadHandlerAudioClip_InternalCreateAudioClip_m789B76138CF7B91F510EE1936A63A07C08975098 (void);
extern void DownloadHandlerAudioClip__ctor_mB8C00870FDA938440C2E89BCAC682E636641B61F (void);
extern void DownloadHandlerAudioClip__ctor_mFF61AA21FD2F3655A0E5DE811467FD7A2426592F (void);
extern void DownloadHandlerAudioClip_GetNativeData_mE7E3D8ED446BE6E5ACAC630F1C2E99687E605766 (void);
extern void DownloadHandlerAudioClip_Dispose_mFE45B0B7E69D9AF774D2FDD484041FA374B112A6 (void);
extern void DownloadHandlerAudioClip_GetText_m1A79A62E53F03F61A4FB28A3A95AA0267FC52025 (void);
extern void DownloadHandlerAudioClip_get_audioClip_mBBA8B1C847780D3442649A0E52C822471F87C262 (void);
extern void DownloadHandlerAudioClip_get_streamAudio_mB344F3E284AA34786BD46D7DDC8E82D2BA8606FC (void);
extern void DownloadHandlerAudioClip_set_streamAudio_mD760F3FC0A37AE1DD889DDA0B3C0B39504B23E30 (void);
extern void DownloadHandlerAudioClip_get_compressed_mAD7D67BFC9343B4398F4968E34AF682D0FE20F56 (void);
extern void DownloadHandlerAudioClip_set_compressed_m212FF53AE2E93AC9029E459101F95E51155359A5 (void);
extern void DownloadHandlerAudioClip_GetContent_mF38CB6EDB5CE453D7C2A783560BCBEA924FA2EC9 (void);
extern void DownloadHandlerMovieTexture__ctor_mAE123B13DD01E28ACF1569FC59527D5A9E1CD44B (void);
extern void UnityWebRequestMultimedia_GetAudioClip_mADB031D0B0B718A0459E69114922128C35846F7B (void);
extern void UnityWebRequestMultimedia_GetAudioClip_mD6D1C733AA205FC721325D1FCBACBD40A788353F (void);
static Il2CppMethodPointer s_methodPointers[16] = 
{
	DownloadHandlerAudioClip_Create_mF088040A27BB328CB057563702E95B57418F1B71,
	DownloadHandlerAudioClip_InternalCreateAudioClip_m789B76138CF7B91F510EE1936A63A07C08975098,
	DownloadHandlerAudioClip__ctor_mB8C00870FDA938440C2E89BCAC682E636641B61F,
	DownloadHandlerAudioClip__ctor_mFF61AA21FD2F3655A0E5DE811467FD7A2426592F,
	DownloadHandlerAudioClip_GetNativeData_mE7E3D8ED446BE6E5ACAC630F1C2E99687E605766,
	DownloadHandlerAudioClip_Dispose_mFE45B0B7E69D9AF774D2FDD484041FA374B112A6,
	DownloadHandlerAudioClip_GetText_m1A79A62E53F03F61A4FB28A3A95AA0267FC52025,
	DownloadHandlerAudioClip_get_audioClip_mBBA8B1C847780D3442649A0E52C822471F87C262,
	DownloadHandlerAudioClip_get_streamAudio_mB344F3E284AA34786BD46D7DDC8E82D2BA8606FC,
	DownloadHandlerAudioClip_set_streamAudio_mD760F3FC0A37AE1DD889DDA0B3C0B39504B23E30,
	DownloadHandlerAudioClip_get_compressed_mAD7D67BFC9343B4398F4968E34AF682D0FE20F56,
	DownloadHandlerAudioClip_set_compressed_m212FF53AE2E93AC9029E459101F95E51155359A5,
	DownloadHandlerAudioClip_GetContent_mF38CB6EDB5CE453D7C2A783560BCBEA924FA2EC9,
	DownloadHandlerMovieTexture__ctor_mAE123B13DD01E28ACF1569FC59527D5A9E1CD44B,
	UnityWebRequestMultimedia_GetAudioClip_mADB031D0B0B718A0459E69114922128C35846F7B,
	UnityWebRequestMultimedia_GetAudioClip_mD6D1C733AA205FC721325D1FCBACBD40A788353F,
};
static const int32_t s_InvokerIndices[16] = 
{
	6596,
	2796,
	2796,
	2796,
	4057,
	4364,
	4250,
	4250,
	4168,
	3807,
	4168,
	3807,
	8505,
	4364,
	7626,
	7626,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestAudioModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestAudioModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestAudioModule_CodeGenModule = 
{
	"UnityEngine.UnityWebRequestAudioModule.dll",
	16,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestAudioModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
