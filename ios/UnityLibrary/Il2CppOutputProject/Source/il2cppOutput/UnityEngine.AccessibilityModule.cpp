﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7;
struct Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA;
struct Func_2_tEFC1389121F1596F8379542754E284666154ABE8;
struct Func_2_t19E50C11C3E1F20B5A8FDB85D7DD353B6DFF868B;
struct Func_2_t7F5F5324CE2DDB7001B68FFE29A5D9F907139FB0;
struct IEnumerable_1_t30FBA3F0775777FD15679FBAE44B692629C5E6BB;
struct IEnumerable_1_tCE758D940790D6D0D56B457E522C195F8C413AF2;
struct IEnumerable_1_t352FDDEA001ABE8E1D67849D2E2F3D1D75B03D41;
struct IEnumerable_1_t29E7244AE33B71FA0981E50D5BC73B7938F35C66;
struct ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389;
struct Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct __Il2CppFullySharedGenericTypeU5BU5D_tCAB6D060972DD49223A834B7EEFEB9FE2D003BEC;
struct ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct MethodInfo_t;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291;
struct U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_AccessibilityModule[];
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_CoreModule[];
IL2CPP_EXTERN_C RuntimeClass* ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_2_tEFC1389121F1596F8379542754E284666154ABE8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral0D1F73736024E4DD05D97B15EAE3E607BF36E045;
IL2CPP_EXTERN_C String_t* _stringLiteralD85AE571B2CC7B3EA11D6D2EC99F5079CA01B744;
IL2CPP_EXTERN_C String_t* _stringLiteralF3A84C2DF7FFC2CF877AE9B8AEB76E8BB09FB19D;
IL2CPP_EXTERN_C const RuntimeMethod* Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerable_Select_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m8A1F1B764744946375D34857343580B92FB5537B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1C8E75F759A3916414E26D8C09E580773AEE9A33_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerable_ToArray_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_mB0C7089AB5A1BF174DA8146DF8BA7A685C4B8AE3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerable_ToArray_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mF0AED1993946FF775115C47514B29636472220A1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerable_Where_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m6ED7DDC420D3C584B082D9D90C6655E11E9E514E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3C_cctorU3Eb__7_0_m00C6C9C42D59EEF7ECCA2A2E896F679157689D00_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass6_0__ctor_m27E74D627F1FED046499F34EDEA5EA5E89E0B0E0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__cctor_m706C606C247042784FA728C5CFDC047D23FC4397_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__ctor_m375D9FA45BBCEEC94E1321DE73848F78861ABF23_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VisionUtility_ComputePerceivedLuminance_mA222FB3202943AB9537487D3D35BF02EA2BA0C61_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VisionUtility_GetColorBlindSafePalette_m5F26D6E833D631BD408C92252C820E35E90CF5B8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VisionUtility_GetColorBlindSafePalette_m9040BE9D231EC79FC3EB7B7260C52171F5D7D073_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VisionUtility_GetLuminanceValuesForPalette_m4562F0DB7287E02F2F5A49C232BAD595AB12909A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VisionUtility__cctor_m35662172C08EDF4AD2EAEF1C163AD895D9680887_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Color32U2A_t50CF56EABA15A383FD17F2A668326FF4D249D250_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ColorU2A_t47A168D22622BB9B81B243B151AFBF5552C61C25_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389;
struct Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t6ADC7EC6D9EFCC82440E98136352618CA1F72735 
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04  : public RuntimeObject
{
};
struct U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291  : public RuntimeObject
{
};
struct U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5  : public RuntimeObject
{
	float ___minimumLuminance;
	float ___maximumLuminance;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			int32_t ___rgba;
		};
		#pragma pack(pop, tp)
		struct
		{
			int32_t ___rgba_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			uint8_t ___r;
		};
		#pragma pack(pop, tp)
		struct
		{
			uint8_t ___r_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___g_OffsetPadding[1];
			uint8_t ___g;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___g_OffsetPadding_forAlignmentOnly[1];
			uint8_t ___g_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___b_OffsetPadding[2];
			uint8_t ___b;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___b_OffsetPadding_forAlignmentOnly[2];
			uint8_t ___b_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___a_OffsetPadding[3];
			uint8_t ___a;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___a_OffsetPadding_forAlignmentOnly[3];
			uint8_t ___a_forAlignmentOnly;
		};
	};
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7  : public MulticastDelegate_t
{
};
struct Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA  : public MulticastDelegate_t
{
};
struct Func_2_tEFC1389121F1596F8379542754E284666154ABE8  : public MulticastDelegate_t
{
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129  : public ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263
{
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields
{
	ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* ___s_ColorBlindSafePalette;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___s_ColorBlindSafePaletteLuminanceValues;
};
struct U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields
{
	U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291* ___U3CU3E9;
	Func_2_tEFC1389121F1596F8379542754E284666154ABE8* ___U3CU3E9__6_1;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389  : public RuntimeArray
{
	ALIGN_FIELD (8) Color_tD001788D726C3A7F1379BEED0260B9591F440C1F m_Items[1];

	inline Color_tD001788D726C3A7F1379BEED0260B9591F440C1F GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Color_tD001788D726C3A7F1379BEED0260B9591F440C1F GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F value)
	{
		m_Items[index] = value;
	}
};
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C  : public RuntimeArray
{
	ALIGN_FIELD (8) float m_Items[1];

	inline float GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline float* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, float value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline float GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline float* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, float value)
	{
		m_Items[index] = value;
	}
};
struct Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259  : public RuntimeArray
{
	ALIGN_FIELD (8) Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B m_Items[1];

	inline Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B value)
	{
		m_Items[index] = value;
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Func_2__ctor_m7F5DD19B4170C027D5367001F7BC95A0658A2169_gshared (Func_2_t7F5F5324CE2DDB7001B68FFE29A5D9F907139FB0* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Enumerable_Where_TisIl2CppFullySharedGenericAny_m3F416675DC7A5572025B02ED373C41AD9ED8F6C4_gshared (RuntimeObject* ___0_source, Func_2_t19E50C11C3E1F20B5A8FDB85D7DD353B6DFF868B* ___1_predicate, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Enumerable_Select_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_m5BBCA2BC02448D0B77D2201BB08B13BAB3BABA53_gshared (RuntimeObject* ___0_source, Func_2_t7F5F5324CE2DDB7001B68FFE29A5D9F907139FB0* ___1_selector, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR __Il2CppFullySharedGenericTypeU5BU5D_tCAB6D060972DD49223A834B7EEFEB9FE2D003BEC* Enumerable_ToArray_TisIl2CppFullySharedGenericAny_mE1571336F171A560849F9D7CB38E3A9A4B43B1C6_gshared (RuntimeObject* ___0_source, const RuntimeMethod* method) ;

IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Mathf_LinearToGammaSpace_m768087E2BF400DB5FD77FD5AF4EAF6694E9F34F1 (float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_Assert_mA460392021AC0A8210C9081E3C1C9652DBF32BF6 (bool ___0_condition, String_t* ___1_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VisionUtility_ComputePerceivedLuminance_mA222FB3202943AB9537487D3D35BF02EA2BA0C61 (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_color, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* __this, String_t* ___0_paramName, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_inline (void* ___0_palette, int32_t ___1_paletteLength, float ___2_minimumLuminance, float ___3_maximumLuminance, bool ___4_useColor32, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass6_0__ctor_m27E74D627F1FED046499F34EDEA5EA5E89E0B0E0 (U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Enumerable_Range_m4FB9BBBA09BEF5177C13506DB385CF4467C15FD8 (int32_t ___0_start, int32_t ___1_count, const RuntimeMethod* method) ;
inline void Func_2__ctor_mF0ABAE563501FB4B795991365F3011147BE07E2E (Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA*, RuntimeObject*, intptr_t, const RuntimeMethod*))Func_2__ctor_m7F5DD19B4170C027D5367001F7BC95A0658A2169_gshared)(__this, ___0_object, ___1_method, method);
}
inline RuntimeObject* Enumerable_Where_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m6ED7DDC420D3C584B082D9D90C6655E11E9E514E (RuntimeObject* ___0_source, Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA* ___1_predicate, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (RuntimeObject*, Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA*, const RuntimeMethod*))Enumerable_Where_TisIl2CppFullySharedGenericAny_m3F416675DC7A5572025B02ED373C41AD9ED8F6C4_gshared)(___0_source, ___1_predicate, method);
}
inline void Func_2__ctor_m6FD3C79B61A43F098E9DE858C7F8C53E155D32F6 (Func_2_tEFC1389121F1596F8379542754E284666154ABE8* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Func_2_tEFC1389121F1596F8379542754E284666154ABE8*, RuntimeObject*, intptr_t, const RuntimeMethod*))Func_2__ctor_m7F5DD19B4170C027D5367001F7BC95A0658A2169_gshared)(__this, ___0_object, ___1_method, method);
}
inline RuntimeObject* Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1C8E75F759A3916414E26D8C09E580773AEE9A33 (RuntimeObject* ___0_source, Func_2_tEFC1389121F1596F8379542754E284666154ABE8* ___1_selector, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (RuntimeObject*, Func_2_tEFC1389121F1596F8379542754E284666154ABE8*, const RuntimeMethod*))Enumerable_Select_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_m5BBCA2BC02448D0B77D2201BB08B13BAB3BABA53_gshared)(___0_source, ___1_selector, method);
}
inline ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* Enumerable_ToArray_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_mB0C7089AB5A1BF174DA8146DF8BA7A685C4B8AE3 (RuntimeObject* ___0_source, const RuntimeMethod* method)
{
	return ((  ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* (*) (RuntimeObject*, const RuntimeMethod*))Enumerable_ToArray_TisIl2CppFullySharedGenericAny_mE1571336F171A560849F9D7CB38E3A9A4B43B1C6_gshared)(___0_source, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_inline (int32_t ___0_a, int32_t ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_c, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* __this, uint8_t ___0_r, uint8_t ___1_g, uint8_t ___2_b, uint8_t ___3_a, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___0_c, const RuntimeMethod* method) ;
inline void Func_2__ctor_m6CF9A938F35387B533637F9076B0BBD220190CB7 (Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7*, RuntimeObject*, intptr_t, const RuntimeMethod*))Func_2__ctor_m7F5DD19B4170C027D5367001F7BC95A0658A2169_gshared)(__this, ___0_object, ___1_method, method);
}
inline RuntimeObject* Enumerable_Select_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m8A1F1B764744946375D34857343580B92FB5537B (RuntimeObject* ___0_source, Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7* ___1_selector, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (RuntimeObject*, Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7*, const RuntimeMethod*))Enumerable_Select_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_m5BBCA2BC02448D0B77D2201BB08B13BAB3BABA53_gshared)(___0_source, ___1_selector, method);
}
inline SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* Enumerable_ToArray_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mF0AED1993946FF775115C47514B29636472220A1 (RuntimeObject* ___0_source, const RuntimeMethod* method)
{
	return ((  SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* (*) (RuntimeObject*, const RuntimeMethod*))Enumerable_ToArray_TisIl2CppFullySharedGenericAny_mE1571336F171A560849F9D7CB38E3A9A4B43B1C6_gshared)(___0_source, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m375D9FA45BBCEEC94E1321DE73848F78861ABF23 (U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Mathf_GammaToLinearSpace_mEF9E26BAD322E55448B286ABDCDF4A2CC236547F (float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VisionUtility_ComputePerceivedLuminance_mA222FB3202943AB9537487D3D35BF02EA2BA0C61 (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_color, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_ComputePerceivedLuminance_mA222FB3202943AB9537487D3D35BF02EA2BA0C61_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_color));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VisionUtility_ComputePerceivedLuminance_mA222FB3202943AB9537487D3D35BF02EA2BA0C61_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 3));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 4));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		L_0 = Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C_inline((&___0_color), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 4));
		___0_color = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 5));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = ___0_color;
		float L_2 = L_1.___r;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_3 = ___0_color;
		float L_4 = L_3.___g;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5 = ___0_color;
		float L_6 = L_5.___b;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 6));
		float L_7;
		L_7 = Mathf_LinearToGammaSpace_m768087E2BF400DB5FD77FD5AF4EAF6694E9F34F1(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply((0.212599993f), L_2)), ((float)il2cpp_codegen_multiply((0.715200007f), L_4)))), ((float)il2cpp_codegen_multiply((0.0722000003f), L_6)))), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 6));
		V_0 = L_7;
		goto IL_0038;
	}

IL_0038:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 7));
		float L_8 = V_0;
		return L_8;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VisionUtility_GetLuminanceValuesForPalette_m4562F0DB7287E02F2F5A49C232BAD595AB12909A (ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* ___0_palette, SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C** ___1_outLuminanceValues, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_GetLuminanceValuesForPalette_m4562F0DB7287E02F2F5A49C232BAD595AB12909A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0D1F73736024E4DD05D97B15EAE3E607BF36E045);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF3A84C2DF7FFC2CF877AE9B8AEB76E8BB09FB19D);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	bool V_1 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_palette), (&___1_outLuminanceValues));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VisionUtility_GetLuminanceValuesForPalette_m4562F0DB7287E02F2F5A49C232BAD595AB12909A_RuntimeMethod_var, NULL, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 8));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 9));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 10));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 11));
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_0 = ___0_palette;
		if (!L_0)
		{
			goto IL_000b;
		}
	}
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C** L_1 = ___1_outLuminanceValues;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_2 = *((SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C**)L_1);
		G_B3_0 = ((!(((RuntimeObject*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)L_2) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		goto IL_000c;
	}

IL_000b:
	{
		G_B3_0 = 0;
	}

IL_000c:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 12));
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_Assert_mA460392021AC0A8210C9081E3C1C9652DBF32BF6((bool)G_B3_0, _stringLiteralF3A84C2DF7FFC2CF877AE9B8AEB76E8BB09FB19D, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 12));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 13));
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_3 = ___0_palette;
		NullCheck(L_3);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C** L_4 = ___1_outLuminanceValues;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_5 = *((SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C**)L_4);
		NullCheck(L_5);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 14));
		Debug_Assert_mA460392021AC0A8210C9081E3C1C9652DBF32BF6((bool)((((int32_t)((int32_t)(((RuntimeArray*)L_3)->max_length))) == ((int32_t)((int32_t)(((RuntimeArray*)L_5)->max_length))))? 1 : 0), _stringLiteral0D1F73736024E4DD05D97B15EAE3E607BF36E045, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 14));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 15));
		V_0 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 16));
		goto IL_0045;
	}

IL_002f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 17));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 18));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C** L_6 = ___1_outLuminanceValues;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_7 = *((SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C**)L_6);
		int32_t L_8 = V_0;
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_9 = ___0_palette;
		int32_t L_10 = V_0;
		NullCheck(L_9);
		int32_t L_11 = L_10;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12 = (L_9)->GetAt(static_cast<il2cpp_array_size_t>(L_11));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 19));
		il2cpp_codegen_runtime_class_init_inline(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		float L_13;
		L_13 = VisionUtility_ComputePerceivedLuminance_mA222FB3202943AB9537487D3D35BF02EA2BA0C61(L_12, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 19));
		NullCheck(L_7);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(L_8), (float)L_13);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 20));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 21));
		int32_t L_14 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_14, 1));
	}

IL_0045:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 22));
		int32_t L_15 = V_0;
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_16 = ___0_palette;
		NullCheck(L_16);
		V_1 = (bool)((((int32_t)L_15) < ((int32_t)((int32_t)(((RuntimeArray*)L_16)->max_length))))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 23));
		bool L_17 = V_1;
		if (L_17)
		{
			goto IL_002f;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 24));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VisionUtility_GetColorBlindSafePalette_m5F26D6E833D631BD408C92252C820E35E90CF5B8 (ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* ___0_palette, float ___1_minimumLuminance, float ___2_maximumLuminance, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorU2A_t47A168D22622BB9B81B243B151AFBF5552C61C25_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_GetColorBlindSafePalette_m5F26D6E833D631BD408C92252C820E35E90CF5B8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* V_1 = NULL;
	ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* V_2 = NULL;
	int32_t V_3 = 0;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_palette), (&___1_minimumLuminance), (&___2_maximumLuminance));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_1));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VisionUtility_GetColorBlindSafePalette_m5F26D6E833D631BD408C92252C820E35E90CF5B8_RuntimeMethod_var, NULL, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 25));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 26));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 27));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 28));
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_0 = ___0_palette;
		V_0 = (bool)((((RuntimeObject*)(ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389*)L_0) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 29));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0014;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 30));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 31));
		ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* L_2 = (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD85AE571B2CC7B3EA11D6D2EC99F5079CA01B744)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 31));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VisionUtility_GetColorBlindSafePalette_m5F26D6E833D631BD408C92252C820E35E90CF5B8_RuntimeMethod_var)));
	}

IL_0014:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 32));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 33));
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_3 = ___0_palette;
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_4 = L_3;
		V_2 = L_4;
		if (!L_4)
		{
			goto IL_001f;
		}
	}
	{
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_5 = V_2;
		NullCheck(L_5);
		if (((int32_t)(((RuntimeArray*)L_5)->max_length)))
		{
			goto IL_0024;
		}
	}

IL_001f:
	{
		V_1 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)((uintptr_t)0);
		goto IL_002d;
	}

IL_0024:
	{
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_6 = V_2;
		NullCheck(L_6);
		V_1 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)((uintptr_t)((L_6)->GetAddressAt(static_cast<il2cpp_array_size_t>(0))));
	}

IL_002d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 34));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 35));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_7 = V_1;
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_8 = ___0_palette;
		NullCheck(L_8);
		float L_9 = ___1_minimumLuminance;
		float L_10 = ___2_maximumLuminance;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 36));
		il2cpp_codegen_runtime_class_init_inline(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		int32_t L_11;
		L_11 = VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_inline((void*)L_7, ((int32_t)(((RuntimeArray*)L_8)->max_length)), L_9, L_10, (bool)0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 36));
		V_3 = L_11;
		goto IL_003d;
	}

IL_003d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 37));
		int32_t L_12 = V_3;
		return L_12;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VisionUtility_GetColorBlindSafePalette_m9040BE9D231EC79FC3EB7B7260C52171F5D7D073 (Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* ___0_palette, float ___1_minimumLuminance, float ___2_maximumLuminance, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32U2A_t50CF56EABA15A383FD17F2A668326FF4D249D250_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_GetColorBlindSafePalette_m9040BE9D231EC79FC3EB7B7260C52171F5D7D073_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* V_1 = NULL;
	Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* V_2 = NULL;
	int32_t V_3 = 0;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_palette), (&___1_minimumLuminance), (&___2_maximumLuminance));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_1));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VisionUtility_GetColorBlindSafePalette_m9040BE9D231EC79FC3EB7B7260C52171F5D7D073_RuntimeMethod_var, NULL, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 38));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 39));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 40));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 41));
		Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* L_0 = ___0_palette;
		V_0 = (bool)((((RuntimeObject*)(Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259*)L_0) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 42));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0014;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 43));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 44));
		ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* L_2 = (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD85AE571B2CC7B3EA11D6D2EC99F5079CA01B744)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 44));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VisionUtility_GetColorBlindSafePalette_m9040BE9D231EC79FC3EB7B7260C52171F5D7D073_RuntimeMethod_var)));
	}

IL_0014:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 45));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 46));
		Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* L_3 = ___0_palette;
		Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* L_4 = L_3;
		V_2 = L_4;
		if (!L_4)
		{
			goto IL_001f;
		}
	}
	{
		Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* L_5 = V_2;
		NullCheck(L_5);
		if (((int32_t)(((RuntimeArray*)L_5)->max_length)))
		{
			goto IL_0024;
		}
	}

IL_001f:
	{
		V_1 = (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)((uintptr_t)0);
		goto IL_002d;
	}

IL_0024:
	{
		Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* L_6 = V_2;
		NullCheck(L_6);
		V_1 = (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)((uintptr_t)((L_6)->GetAddressAt(static_cast<il2cpp_array_size_t>(0))));
	}

IL_002d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 47));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 48));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* L_7 = V_1;
		Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* L_8 = ___0_palette;
		NullCheck(L_8);
		float L_9 = ___1_minimumLuminance;
		float L_10 = ___2_maximumLuminance;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 49));
		il2cpp_codegen_runtime_class_init_inline(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		int32_t L_11;
		L_11 = VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_inline((void*)L_7, ((int32_t)(((RuntimeArray*)L_8)->max_length)), L_9, L_10, (bool)1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 49));
		V_3 = L_11;
		goto IL_003d;
	}

IL_003d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 50));
		int32_t L_12 = V_3;
		return L_12;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3 (void* ___0_palette, int32_t ___1_paletteLength, float ___2_minimumLuminance, float ___3_maximumLuminance, bool ___4_useColor32, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1C8E75F759A3916414E26D8C09E580773AEE9A33_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_ToArray_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_mB0C7089AB5A1BF174DA8146DF8BA7A685C4B8AE3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_Where_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m6ED7DDC420D3C584B082D9D90C6655E11E9E514E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_2_tEFC1389121F1596F8379542754E284666154ABE8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* V_0 = NULL;
	ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* V_1 = NULL;
	int32_t V_2 = 0;
	bool V_3 = false;
	bool V_4 = false;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	bool V_7 = false;
	bool V_8 = false;
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	bool V_11 = false;
	bool V_12 = false;
	int32_t V_13 = 0;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_palette), (&___1_paletteLength), (&___2_minimumLuminance), (&___3_maximumLuminance), (&___4_useColor32));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0), (&V_1), (&V_2), (&V_5), (&V_6), (&V_9), (&V_10));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_RuntimeMethod_var, NULL, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 51));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 52));
	Func_2_tEFC1389121F1596F8379542754E284666154ABE8* G_B4_0 = NULL;
	RuntimeObject* G_B4_1 = NULL;
	Func_2_tEFC1389121F1596F8379542754E284666154ABE8* G_B3_0 = NULL;
	RuntimeObject* G_B3_1 = NULL;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 53));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 54));
		U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* L_0 = (U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass6_0__ctor_m27E74D627F1FED046499F34EDEA5EA5E89E0B0E0(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 54));
		V_0 = L_0;
		U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* L_1 = V_0;
		float L_2 = ___2_minimumLuminance;
		NullCheck(L_1);
		L_1->___minimumLuminance = L_2;
		U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* L_3 = V_0;
		float L_4 = ___3_maximumLuminance;
		NullCheck(L_3);
		L_3->___maximumLuminance = L_4;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 55));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 56));
		void* L_5 = ___0_palette;
		V_3 = (bool)((((intptr_t)L_5) == ((intptr_t)((uintptr_t)0)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 57));
		bool L_6 = V_3;
		if (!L_6)
		{
			goto IL_0029;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 58));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 59));
		ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* L_7 = (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B(L_7, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD85AE571B2CC7B3EA11D6D2EC99F5079CA01B744)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 59));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_7, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_RuntimeMethod_var)));
	}

IL_0029:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 60));
		il2cpp_codegen_runtime_class_init_inline(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_8 = ((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePalette;
		NullCheck(L_8);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 61));
		RuntimeObject* L_9;
		L_9 = Enumerable_Range_m4FB9BBBA09BEF5177C13506DB385CF4467C15FD8(0, ((int32_t)(((RuntimeArray*)L_8)->max_length)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 61));
		U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* L_10 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 62));
		Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA* L_11 = (Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA*)il2cpp_codegen_object_new(Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA_il2cpp_TypeInfo_var);
		Func_2__ctor_mF0ABAE563501FB4B795991365F3011147BE07E2E(L_11, L_10, (intptr_t)((void*)U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 62));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 63));
		RuntimeObject* L_12;
		L_12 = Enumerable_Where_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m6ED7DDC420D3C584B082D9D90C6655E11E9E514E(L_9, L_11, Enumerable_Where_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m6ED7DDC420D3C584B082D9D90C6655E11E9E514E_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 63));
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		Func_2_tEFC1389121F1596F8379542754E284666154ABE8* L_13 = ((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9__6_1;
		Func_2_tEFC1389121F1596F8379542754E284666154ABE8* L_14 = L_13;
		if (L_14)
		{
			G_B4_0 = L_14;
			G_B4_1 = L_12;
			goto IL_0066;
		}
		G_B3_0 = L_14;
		G_B3_1 = L_12;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291* L_15 = ((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 64));
		Func_2_tEFC1389121F1596F8379542754E284666154ABE8* L_16 = (Func_2_tEFC1389121F1596F8379542754E284666154ABE8*)il2cpp_codegen_object_new(Func_2_tEFC1389121F1596F8379542754E284666154ABE8_il2cpp_TypeInfo_var);
		Func_2__ctor_m6FD3C79B61A43F098E9DE858C7F8C53E155D32F6(L_16, L_15, (intptr_t)((void*)U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 64));
		Func_2_tEFC1389121F1596F8379542754E284666154ABE8* L_17 = L_16;
		((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9__6_1 = L_17;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9__6_1), (void*)L_17);
		G_B4_0 = L_17;
		G_B4_1 = G_B3_1;
	}

IL_0066:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 65));
		RuntimeObject* L_18;
		L_18 = Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1C8E75F759A3916414E26D8C09E580773AEE9A33(G_B4_1, G_B4_0, Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1C8E75F759A3916414E26D8C09E580773AEE9A33_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 65));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 66));
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_19;
		L_19 = Enumerable_ToArray_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_mB0C7089AB5A1BF174DA8146DF8BA7A685C4B8AE3(L_18, Enumerable_ToArray_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_mB0C7089AB5A1BF174DA8146DF8BA7A685C4B8AE3_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 66));
		V_1 = L_19;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 67));
		int32_t L_20 = ___1_paletteLength;
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_21 = V_1;
		NullCheck(L_21);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 68));
		int32_t L_22;
		L_22 = Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_inline(L_20, ((int32_t)(((RuntimeArray*)L_21)->max_length)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 68));
		V_2 = L_22;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 69));
		int32_t L_23 = V_2;
		V_4 = (bool)((((int32_t)L_23) > ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 70));
		bool L_24 = V_4;
		if (!L_24)
		{
			goto IL_00ea;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 71));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 72));
		V_5 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 73));
		int32_t L_25 = ___1_paletteLength;
		V_6 = L_25;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 74));
		goto IL_00db;
	}

IL_008e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 75));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 76));
		bool L_26 = ___4_useColor32;
		V_7 = L_26;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 77));
		bool L_27 = V_7;
		if (!L_27)
		{
			goto IL_00b9;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 78));
		void* L_28 = ___0_palette;
		int32_t L_29 = V_5;
		uint32_t L_30 = sizeof(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_31 = V_1;
		int32_t L_32 = V_5;
		int32_t L_33 = V_2;
		NullCheck(L_31);
		int32_t L_34 = ((int32_t)(L_32%L_33));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_35 = (L_31)->GetAt(static_cast<il2cpp_array_size_t>(L_34));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 79));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_36;
		L_36 = Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_inline(L_35, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 79));
		*(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)((void*)il2cpp_codegen_add((intptr_t)L_28, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_29), (int32_t)L_30)))) = L_36;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 80));
		goto IL_00d4;
	}

IL_00b9:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 81));
		void* L_37 = ___0_palette;
		int32_t L_38 = V_5;
		uint32_t L_39 = sizeof(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_40 = V_1;
		int32_t L_41 = V_5;
		int32_t L_42 = V_2;
		NullCheck(L_40);
		int32_t L_43 = ((int32_t)(L_41%L_42));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_44 = (L_40)->GetAt(static_cast<il2cpp_array_size_t>(L_43));
		*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)((void*)il2cpp_codegen_add((intptr_t)L_37, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_38), (int32_t)L_39)))) = L_44;
	}

IL_00d4:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 82));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 83));
		int32_t L_45 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_45, 1));
	}

IL_00db:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 84));
		int32_t L_46 = V_5;
		int32_t L_47 = V_6;
		V_8 = (bool)((((int32_t)L_46) < ((int32_t)L_47))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 85));
		bool L_48 = V_8;
		if (L_48)
		{
			goto IL_008e;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 86));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 87));
		goto IL_0136;
	}

IL_00ea:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 88));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 89));
		V_9 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 90));
		int32_t L_49 = ___1_paletteLength;
		V_10 = L_49;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 91));
		goto IL_0129;
	}

IL_00f3:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 92));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 93));
		bool L_50 = ___4_useColor32;
		V_11 = L_50;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 94));
		bool L_51 = V_11;
		if (!L_51)
		{
			goto IL_0110;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 95));
		void* L_52 = ___0_palette;
		int32_t L_53 = V_9;
		uint32_t L_54 = sizeof(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B);
		il2cpp_codegen_initobj(((void*)il2cpp_codegen_add((intptr_t)L_52, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_53), (int32_t)L_54)))), sizeof(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 96));
		goto IL_0122;
	}

IL_0110:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 97));
		void* L_55 = ___0_palette;
		int32_t L_56 = V_9;
		uint32_t L_57 = sizeof(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F);
		il2cpp_codegen_initobj(((void*)il2cpp_codegen_add((intptr_t)L_55, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_56), (int32_t)L_57)))), sizeof(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F));
	}

IL_0122:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 98));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 99));
		int32_t L_58 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_add(L_58, 1));
	}

IL_0129:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 100));
		int32_t L_59 = V_9;
		int32_t L_60 = V_10;
		V_12 = (bool)((((int32_t)L_59) < ((int32_t)L_60))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 101));
		bool L_61 = V_12;
		if (L_61)
		{
			goto IL_00f3;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 102));
	}

IL_0136:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 103));
		int32_t L_62 = V_2;
		V_13 = L_62;
		goto IL_013b;
	}

IL_013b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 104));
		int32_t L_63 = V_13;
		return L_63;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VisionUtility__cctor_m35662172C08EDF4AD2EAEF1C163AD895D9680887 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_Select_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m8A1F1B764744946375D34857343580B92FB5537B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_ToArray_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mF0AED1993946FF775115C47514B29636472220A1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_cctorU3Eb__7_0_m00C6C9C42D59EEF7ECCA2A2E896F679157689D00_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility__cctor_m35662172C08EDF4AD2EAEF1C163AD895D9680887_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VisionUtility__cctor_m35662172C08EDF4AD2EAEF1C163AD895D9680887_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 105));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 106));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 107));
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_0 = (ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389*)(ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389*)SZArrayNew(ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389_il2cpp_TypeInfo_var, (uint32_t)((int32_t)19));
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_1 = L_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 108));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_2;
		memset((&L_2), 0, sizeof(L_2));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_2), (uint8_t)0, (uint8_t)0, (uint8_t)0, (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 108));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 109));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_3;
		L_3 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 109));
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_3);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_4 = L_1;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 110));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_5;
		memset((&L_5), 0, sizeof(L_5));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_5), (uint8_t)((int32_t)73), (uint8_t)0, (uint8_t)((int32_t)146), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 110));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 111));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_6;
		L_6 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 111));
		NullCheck(L_4);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(1), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_6);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_7 = L_4;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 112));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_8;
		memset((&L_8), 0, sizeof(L_8));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_8), (uint8_t)7, (uint8_t)((int32_t)71), (uint8_t)((int32_t)81), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 112));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 113));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_9;
		L_9 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_8, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 113));
		NullCheck(L_7);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(2), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_9);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_10 = L_7;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 114));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_11;
		memset((&L_11), 0, sizeof(L_11));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_11), (uint8_t)0, (uint8_t)((int32_t)146), (uint8_t)((int32_t)146), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 114));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 115));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12;
		L_12 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_11, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 115));
		NullCheck(L_10);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(3), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_12);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_13 = L_10;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 116));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_14;
		memset((&L_14), 0, sizeof(L_14));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_14), (uint8_t)((int32_t)182), (uint8_t)((int32_t)109), (uint8_t)((int32_t)255), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 116));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 117));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_15;
		L_15 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_14, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 117));
		NullCheck(L_13);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(4), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_15);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_16 = L_13;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 118));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_17;
		memset((&L_17), 0, sizeof(L_17));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_17), (uint8_t)((int32_t)255), (uint8_t)((int32_t)109), (uint8_t)((int32_t)182), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 118));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 119));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_18;
		L_18 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_17, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 119));
		NullCheck(L_16);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(5), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_18);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_19 = L_16;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 120));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_20;
		memset((&L_20), 0, sizeof(L_20));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_20), (uint8_t)((int32_t)109), (uint8_t)((int32_t)182), (uint8_t)((int32_t)255), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 120));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 121));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_21;
		L_21 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_20, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 121));
		NullCheck(L_19);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(6), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_21);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_22 = L_19;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 122));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_23;
		memset((&L_23), 0, sizeof(L_23));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_23), (uint8_t)((int32_t)36), (uint8_t)((int32_t)255), (uint8_t)((int32_t)36), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 122));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 123));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_24;
		L_24 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_23, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 123));
		NullCheck(L_22);
		(L_22)->SetAt(static_cast<il2cpp_array_size_t>(7), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_24);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_25 = L_22;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 124));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_26;
		memset((&L_26), 0, sizeof(L_26));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_26), (uint8_t)((int32_t)255), (uint8_t)((int32_t)182), (uint8_t)((int32_t)219), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 124));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 125));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_27;
		L_27 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_26, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 125));
		NullCheck(L_25);
		(L_25)->SetAt(static_cast<il2cpp_array_size_t>(8), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_27);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_28 = L_25;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 126));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_29;
		memset((&L_29), 0, sizeof(L_29));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_29), (uint8_t)((int32_t)182), (uint8_t)((int32_t)219), (uint8_t)((int32_t)255), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 126));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 127));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_30;
		L_30 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_29, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 127));
		NullCheck(L_28);
		(L_28)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)9)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_30);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_31 = L_28;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 128));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_32;
		memset((&L_32), 0, sizeof(L_32));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_32), (uint8_t)((int32_t)255), (uint8_t)((int32_t)255), (uint8_t)((int32_t)109), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 128));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 129));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_33;
		L_33 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_32, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 129));
		NullCheck(L_31);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)10)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_33);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_34 = L_31;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 130));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_35;
		memset((&L_35), 0, sizeof(L_35));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_35), (uint8_t)((int32_t)30), (uint8_t)((int32_t)92), (uint8_t)((int32_t)92), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 130));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 131));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_36;
		L_36 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_35, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 131));
		NullCheck(L_34);
		(L_34)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)11)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_36);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_37 = L_34;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 132));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_38;
		memset((&L_38), 0, sizeof(L_38));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_38), (uint8_t)((int32_t)74), (uint8_t)((int32_t)154), (uint8_t)((int32_t)87), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 132));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 133));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_39;
		L_39 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_38, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 133));
		NullCheck(L_37);
		(L_37)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)12)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_39);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_40 = L_37;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 134));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_41;
		memset((&L_41), 0, sizeof(L_41));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_41), (uint8_t)((int32_t)113), (uint8_t)((int32_t)66), (uint8_t)((int32_t)183), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 134));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 135));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_42;
		L_42 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_41, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 135));
		NullCheck(L_40);
		(L_40)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)13)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_42);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_43 = L_40;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 136));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_44;
		memset((&L_44), 0, sizeof(L_44));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_44), (uint8_t)((int32_t)162), (uint8_t)((int32_t)66), (uint8_t)((int32_t)183), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 136));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 137));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_45;
		L_45 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_44, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 137));
		NullCheck(L_43);
		(L_43)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)14)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_45);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_46 = L_43;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 138));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_47;
		memset((&L_47), 0, sizeof(L_47));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_47), (uint8_t)((int32_t)178), (uint8_t)((int32_t)92), (uint8_t)((int32_t)25), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 138));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 139));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_48;
		L_48 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_47, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 139));
		NullCheck(L_46);
		(L_46)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)15)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_48);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_49 = L_46;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 140));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_50;
		memset((&L_50), 0, sizeof(L_50));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_50), (uint8_t)((int32_t)100), (uint8_t)((int32_t)100), (uint8_t)((int32_t)100), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 140));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 141));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_51;
		L_51 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_50, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 141));
		NullCheck(L_49);
		(L_49)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)16)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_51);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_52 = L_49;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 142));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_53;
		memset((&L_53), 0, sizeof(L_53));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_53), (uint8_t)((int32_t)80), (uint8_t)((int32_t)203), (uint8_t)((int32_t)181), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 142));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 143));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_54;
		L_54 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_53, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 143));
		NullCheck(L_52);
		(L_52)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)17)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_54);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_55 = L_52;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 144));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_56;
		memset((&L_56), 0, sizeof(L_56));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_56), (uint8_t)((int32_t)82), (uint8_t)((int32_t)205), (uint8_t)((int32_t)242), (uint8_t)((int32_t)255), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 144));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 145));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_57;
		L_57 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(L_56, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 145));
		NullCheck(L_55);
		(L_55)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)18)), (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F)L_57);
		((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePalette = L_55;
		Il2CppCodeGenWriteBarrier((void**)(&((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePalette), (void*)L_55);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 146));
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_58 = ((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePalette;
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291* L_59 = ((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 147));
		Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7* L_60 = (Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7*)il2cpp_codegen_object_new(Func_2_t03EB8A6DD7343AF31D6B50AF3D53584446BE38E7_il2cpp_TypeInfo_var);
		Func_2__ctor_m6CF9A938F35387B533637F9076B0BBD220190CB7(L_60, L_59, (intptr_t)((void*)U3CU3Ec_U3C_cctorU3Eb__7_0_m00C6C9C42D59EEF7ECCA2A2E896F679157689D00_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 147));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 148));
		RuntimeObject* L_61;
		L_61 = Enumerable_Select_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m8A1F1B764744946375D34857343580B92FB5537B((RuntimeObject*)L_58, L_60, Enumerable_Select_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m8A1F1B764744946375D34857343580B92FB5537B_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 148));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 149));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_62;
		L_62 = Enumerable_ToArray_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mF0AED1993946FF775115C47514B29636472220A1(L_61, Enumerable_ToArray_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mF0AED1993946FF775115C47514B29636472220A1_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 149));
		((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePaletteLuminanceValues = L_62;
		Il2CppCodeGenWriteBarrier((void**)(&((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePaletteLuminanceValues), (void*)L_62);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_m706C606C247042784FA728C5CFDC047D23FC4397 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__cctor_m706C606C247042784FA728C5CFDC047D23FC4397_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec__cctor_m706C606C247042784FA728C5CFDC047D23FC4397_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291* L_0 = (U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291*)il2cpp_codegen_object_new(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_m375D9FA45BBCEEC94E1321DE73848F78861ABF23(L_0, NULL);
		((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m375D9FA45BBCEEC94E1321DE73848F78861ABF23 (U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__ctor_m375D9FA45BBCEEC94E1321DE73848F78861ABF23_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec__ctor_m375D9FA45BBCEEC94E1321DE73848F78861ABF23_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3 (U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291* __this, int32_t ___0_i, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_i));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 150));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 151));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 152));
		il2cpp_codegen_runtime_class_init_inline(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_0 = ((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePalette;
		int32_t L_1 = ___0_i;
		NullCheck(L_0);
		int32_t L_2 = L_1;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_3 = (L_0)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3C_cctorU3Eb__7_0_m00C6C9C42D59EEF7ECCA2A2E896F679157689D00 (U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_c, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_cctorU3Eb__7_0_m00C6C9C42D59EEF7ECCA2A2E896F679157689D00_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_c));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec_U3C_cctorU3Eb__7_0_m00C6C9C42D59EEF7ECCA2A2E896F679157689D00_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 153));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 154));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 155));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = ___0_c;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 156));
		il2cpp_codegen_runtime_class_init_inline(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		float L_1;
		L_1 = VisionUtility_ComputePerceivedLuminance_mA222FB3202943AB9537487D3D35BF02EA2BA0C61(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 156));
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass6_0__ctor_m27E74D627F1FED046499F34EDEA5EA5E89E0B0E0 (U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass6_0__ctor_m27E74D627F1FED046499F34EDEA5EA5E89E0B0E0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec__DisplayClass6_0__ctor_m27E74D627F1FED046499F34EDEA5EA5E89E0B0E0_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419 (U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* __this, int32_t ___0_i, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_i));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 157));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 158));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 159));
		il2cpp_codegen_runtime_class_init_inline(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_0 = ((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePaletteLuminanceValues;
		int32_t L_1 = ___0_i;
		NullCheck(L_0);
		int32_t L_2 = L_1;
		float L_3 = (L_0)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		float L_4 = __this->___minimumLuminance;
		if ((!(((float)L_3) >= ((float)L_4))))
		{
			goto IL_0023;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_5 = ((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePaletteLuminanceValues;
		int32_t L_6 = ___0_i;
		NullCheck(L_5);
		int32_t L_7 = L_6;
		float L_8 = (L_5)->GetAt(static_cast<il2cpp_array_size_t>(L_7));
		float L_9 = __this->___maximumLuminance;
		G_B3_0 = ((((int32_t)((!(((float)L_8) <= ((float)L_9)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_0024;
	}

IL_0023:
	{
		G_B3_0 = 0;
	}

IL_0024:
	{
		return (bool)G_B3_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_get_linear_m76EB88E15DA4E00D615DF33D1CEE51092683117C_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24114));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24115));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24116));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24117));
		float L_0 = __this->___r;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24118));
		float L_1;
		L_1 = Mathf_GammaToLinearSpace_mEF9E26BAD322E55448B286ABDCDF4A2CC236547F(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24118));
		float L_2 = __this->___g;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24119));
		float L_3;
		L_3 = Mathf_GammaToLinearSpace_mEF9E26BAD322E55448B286ABDCDF4A2CC236547F(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24119));
		float L_4 = __this->___b;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24120));
		float L_5;
		L_5 = Mathf_GammaToLinearSpace_mEF9E26BAD322E55448B286ABDCDF4A2CC236547F(L_4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24120));
		float L_6 = __this->___a;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24121));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_7;
		memset((&L_7), 0, sizeof(L_7));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_7), L_1, L_3, L_5, L_6, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24121));
		V_0 = L_7;
		goto IL_0030;
	}

IL_0030:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24122));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8 = V_0;
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_inline (void* ___0_palette, int32_t ___1_paletteLength, float ___2_minimumLuminance, float ___3_maximumLuminance, bool ___4_useColor32, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1C8E75F759A3916414E26D8C09E580773AEE9A33_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_ToArray_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_mB0C7089AB5A1BF174DA8146DF8BA7A685C4B8AE3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_Where_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m6ED7DDC420D3C584B082D9D90C6655E11E9E514E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_2_tEFC1389121F1596F8379542754E284666154ABE8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* V_0 = NULL;
	ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* V_1 = NULL;
	int32_t V_2 = 0;
	bool V_3 = false;
	bool V_4 = false;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	bool V_7 = false;
	bool V_8 = false;
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	bool V_11 = false;
	bool V_12 = false;
	int32_t V_13 = 0;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_palette), (&___1_paletteLength), (&___2_minimumLuminance), (&___3_maximumLuminance), (&___4_useColor32));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0), (&V_1), (&V_2), (&V_5), (&V_6), (&V_9), (&V_10));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_RuntimeMethod_var, NULL, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 51));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 52));
	Func_2_tEFC1389121F1596F8379542754E284666154ABE8* G_B4_0 = NULL;
	RuntimeObject* G_B4_1 = NULL;
	Func_2_tEFC1389121F1596F8379542754E284666154ABE8* G_B3_0 = NULL;
	RuntimeObject* G_B3_1 = NULL;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 53));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 54));
		U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* L_0 = (U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass6_0__ctor_m27E74D627F1FED046499F34EDEA5EA5E89E0B0E0(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 54));
		V_0 = L_0;
		U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* L_1 = V_0;
		float L_2 = ___2_minimumLuminance;
		NullCheck(L_1);
		L_1->___minimumLuminance = L_2;
		U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* L_3 = V_0;
		float L_4 = ___3_maximumLuminance;
		NullCheck(L_3);
		L_3->___maximumLuminance = L_4;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 55));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 56));
		void* L_5 = ___0_palette;
		V_3 = (bool)((((intptr_t)L_5) == ((intptr_t)((uintptr_t)0)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 57));
		bool L_6 = V_3;
		if (!L_6)
		{
			goto IL_0029;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 58));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 59));
		ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* L_7 = (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B(L_7, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD85AE571B2CC7B3EA11D6D2EC99F5079CA01B744)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 59));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_7, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3_RuntimeMethod_var)));
	}

IL_0029:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 60));
		il2cpp_codegen_runtime_class_init_inline(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_8 = ((VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_StaticFields*)il2cpp_codegen_static_fields_for(VisionUtility_t1366922F1A5EF11598C2CAA34B1D1CF2F9163B04_il2cpp_TypeInfo_var))->___s_ColorBlindSafePalette;
		NullCheck(L_8);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 61));
		RuntimeObject* L_9;
		L_9 = Enumerable_Range_m4FB9BBBA09BEF5177C13506DB385CF4467C15FD8(0, ((int32_t)(((RuntimeArray*)L_8)->max_length)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 61));
		U3CU3Ec__DisplayClass6_0_tD23568D1583F04FA435A11F1B88111960BA905B5* L_10 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 62));
		Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA* L_11 = (Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA*)il2cpp_codegen_object_new(Func_2_t63A057E8762189D8C22BF71360D00C1047680DFA_il2cpp_TypeInfo_var);
		Func_2__ctor_mF0ABAE563501FB4B795991365F3011147BE07E2E(L_11, L_10, (intptr_t)((void*)U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 62));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 63));
		RuntimeObject* L_12;
		L_12 = Enumerable_Where_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m6ED7DDC420D3C584B082D9D90C6655E11E9E514E(L_9, L_11, Enumerable_Where_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m6ED7DDC420D3C584B082D9D90C6655E11E9E514E_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 63));
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		Func_2_tEFC1389121F1596F8379542754E284666154ABE8* L_13 = ((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9__6_1;
		Func_2_tEFC1389121F1596F8379542754E284666154ABE8* L_14 = L_13;
		if (L_14)
		{
			G_B4_0 = L_14;
			G_B4_1 = L_12;
			goto IL_0066;
		}
		G_B3_0 = L_14;
		G_B3_1 = L_12;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var);
		U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291* L_15 = ((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 64));
		Func_2_tEFC1389121F1596F8379542754E284666154ABE8* L_16 = (Func_2_tEFC1389121F1596F8379542754E284666154ABE8*)il2cpp_codegen_object_new(Func_2_tEFC1389121F1596F8379542754E284666154ABE8_il2cpp_TypeInfo_var);
		Func_2__ctor_m6FD3C79B61A43F098E9DE858C7F8C53E155D32F6(L_16, L_15, (intptr_t)((void*)U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3_RuntimeMethod_var), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 64));
		Func_2_tEFC1389121F1596F8379542754E284666154ABE8* L_17 = L_16;
		((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9__6_1 = L_17;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t90696BDEE13A740B9BB34685355F2A7E2DCD8291_il2cpp_TypeInfo_var))->___U3CU3E9__6_1), (void*)L_17);
		G_B4_0 = L_17;
		G_B4_1 = G_B3_1;
	}

IL_0066:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 65));
		RuntimeObject* L_18;
		L_18 = Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1C8E75F759A3916414E26D8C09E580773AEE9A33(G_B4_1, G_B4_0, Enumerable_Select_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1C8E75F759A3916414E26D8C09E580773AEE9A33_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 65));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 66));
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_19;
		L_19 = Enumerable_ToArray_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_mB0C7089AB5A1BF174DA8146DF8BA7A685C4B8AE3(L_18, Enumerable_ToArray_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_mB0C7089AB5A1BF174DA8146DF8BA7A685C4B8AE3_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 66));
		V_1 = L_19;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 67));
		int32_t L_20 = ___1_paletteLength;
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_21 = V_1;
		NullCheck(L_21);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 68));
		int32_t L_22;
		L_22 = Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_inline(L_20, ((int32_t)(((RuntimeArray*)L_21)->max_length)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 68));
		V_2 = L_22;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 69));
		int32_t L_23 = V_2;
		V_4 = (bool)((((int32_t)L_23) > ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 70));
		bool L_24 = V_4;
		if (!L_24)
		{
			goto IL_00ea;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 71));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 72));
		V_5 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 73));
		int32_t L_25 = ___1_paletteLength;
		V_6 = L_25;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 74));
		goto IL_00db;
	}

IL_008e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 75));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 76));
		bool L_26 = ___4_useColor32;
		V_7 = L_26;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 77));
		bool L_27 = V_7;
		if (!L_27)
		{
			goto IL_00b9;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 78));
		void* L_28 = ___0_palette;
		int32_t L_29 = V_5;
		uint32_t L_30 = sizeof(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_31 = V_1;
		int32_t L_32 = V_5;
		int32_t L_33 = V_2;
		NullCheck(L_31);
		int32_t L_34 = ((int32_t)(L_32%L_33));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_35 = (L_31)->GetAt(static_cast<il2cpp_array_size_t>(L_34));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 79));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_36;
		L_36 = Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_inline(L_35, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 79));
		*(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)((void*)il2cpp_codegen_add((intptr_t)L_28, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_29), (int32_t)L_30)))) = L_36;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 80));
		goto IL_00d4;
	}

IL_00b9:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 81));
		void* L_37 = ___0_palette;
		int32_t L_38 = V_5;
		uint32_t L_39 = sizeof(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_40 = V_1;
		int32_t L_41 = V_5;
		int32_t L_42 = V_2;
		NullCheck(L_40);
		int32_t L_43 = ((int32_t)(L_41%L_42));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_44 = (L_40)->GetAt(static_cast<il2cpp_array_size_t>(L_43));
		*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)((void*)il2cpp_codegen_add((intptr_t)L_37, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_38), (int32_t)L_39)))) = L_44;
	}

IL_00d4:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 82));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 83));
		int32_t L_45 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_45, 1));
	}

IL_00db:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 84));
		int32_t L_46 = V_5;
		int32_t L_47 = V_6;
		V_8 = (bool)((((int32_t)L_46) < ((int32_t)L_47))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 85));
		bool L_48 = V_8;
		if (L_48)
		{
			goto IL_008e;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 86));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 87));
		goto IL_0136;
	}

IL_00ea:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 88));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 89));
		V_9 = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 90));
		int32_t L_49 = ___1_paletteLength;
		V_10 = L_49;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 91));
		goto IL_0129;
	}

IL_00f3:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 92));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 93));
		bool L_50 = ___4_useColor32;
		V_11 = L_50;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 94));
		bool L_51 = V_11;
		if (!L_51)
		{
			goto IL_0110;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 95));
		void* L_52 = ___0_palette;
		int32_t L_53 = V_9;
		uint32_t L_54 = sizeof(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B);
		il2cpp_codegen_initobj(((void*)il2cpp_codegen_add((intptr_t)L_52, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_53), (int32_t)L_54)))), sizeof(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 96));
		goto IL_0122;
	}

IL_0110:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 97));
		void* L_55 = ___0_palette;
		int32_t L_56 = V_9;
		uint32_t L_57 = sizeof(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F);
		il2cpp_codegen_initobj(((void*)il2cpp_codegen_add((intptr_t)L_55, ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_56), (int32_t)L_57)))), sizeof(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F));
	}

IL_0122:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 98));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 99));
		int32_t L_58 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_add(L_58, 1));
	}

IL_0129:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 100));
		int32_t L_59 = V_9;
		int32_t L_60 = V_10;
		V_12 = (bool)((((int32_t)L_59) < ((int32_t)L_60))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 101));
		bool L_61 = V_12;
		if (L_61)
		{
			goto IL_00f3;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 102));
	}

IL_0136:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 103));
		int32_t L_62 = V_2;
		V_13 = L_62;
		goto IL_013b;
	}

IL_013b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AccessibilityModule + 104));
		int32_t L_63 = V_13;
		return L_63;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_inline (int32_t ___0_a, int32_t ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_a), (&___1_b));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Mathf_Min_m888083F74FF5655778F0403BB5E9608BEFDEA8CB_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26076));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26077));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26078));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26079));
		int32_t L_0 = ___0_a;
		int32_t L_1 = ___1_b;
		if ((((int32_t)L_0) < ((int32_t)L_1)))
		{
			goto IL_0008;
		}
	}
	{
		int32_t L_2 = ___1_b;
		G_B3_0 = L_2;
		goto IL_0009;
	}

IL_0008:
	{
		int32_t L_3 = ___0_a;
		G_B3_0 = L_3;
	}

IL_0009:
	{
		V_0 = G_B3_0;
		goto IL_000c;
	}

IL_000c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26080));
		int32_t L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_c, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_c));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24334));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24335));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24336));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24337));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = ___0_c;
		float L_1 = L_0.___r;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24338));
		float L_2;
		L_2 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24338));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24339));
		float L_3;
		L_3 = bankers_roundf(((float)il2cpp_codegen_multiply(L_2, (255.0f))));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24339));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_4 = ___0_c;
		float L_5 = L_4.___g;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24340));
		float L_6;
		L_6 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24340));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24341));
		float L_7;
		L_7 = bankers_roundf(((float)il2cpp_codegen_multiply(L_6, (255.0f))));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24341));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8 = ___0_c;
		float L_9 = L_8.___b;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24342));
		float L_10;
		L_10 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_9, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24342));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24343));
		float L_11;
		L_11 = bankers_roundf(((float)il2cpp_codegen_multiply(L_10, (255.0f))));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24343));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12 = ___0_c;
		float L_13 = L_12.___a;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24344));
		float L_14;
		L_14 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_13, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24344));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24345));
		float L_15;
		L_15 = bankers_roundf(((float)il2cpp_codegen_multiply(L_14, (255.0f))));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24345));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24346));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_16;
		memset((&L_16), 0, sizeof(L_16));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_16), (uint8_t)il2cpp_codegen_cast_floating_point<uint8_t, int32_t, float>(L_3), (uint8_t)il2cpp_codegen_cast_floating_point<uint8_t, int32_t, float>(L_7), (uint8_t)il2cpp_codegen_cast_floating_point<uint8_t, int32_t, float>(L_11), (uint8_t)il2cpp_codegen_cast_floating_point<uint8_t, int32_t, float>(L_15), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24346));
		V_0 = L_16;
		goto IL_0065;
	}

IL_0065:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24347));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_17 = V_0;
		return L_17;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* __this, uint8_t ___0_r, uint8_t ___1_g, uint8_t ___2_b, uint8_t ___3_a, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_r), (&___1_g), (&___2_b), (&___3_a));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24325));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24326));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24327));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24328));
		__this->___rgba = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24329));
		uint8_t L_0 = ___0_r;
		__this->___r = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24330));
		uint8_t L_1 = ___1_g;
		__this->___g = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24331));
		uint8_t L_2 = ___2_b;
		__this->___b = L_2;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24332));
		uint8_t L_3 = ___3_a;
		__this->___a = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24333));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___0_c, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_c));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24348));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24349));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24350));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24351));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_0 = ___0_c;
		uint8_t L_1 = L_0.___r;
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_2 = ___0_c;
		uint8_t L_3 = L_2.___g;
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_4 = ___0_c;
		uint8_t L_5 = L_4.___b;
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_6 = ___0_c;
		uint8_t L_7 = L_6.___a;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24352));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8;
		memset((&L_8), 0, sizeof(L_8));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_8), ((float)(((float)L_1)/(255.0f))), ((float)(((float)L_3)/(255.0f))), ((float)(((float)L_5)/(255.0f))), ((float)(((float)L_7)/(255.0f))), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24352));
		V_0 = L_8;
		goto IL_003d;
	}

IL_003d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 24353));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_9 = V_0;
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_r), (&___1_g), (&___2_b), (&___3_a));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 23889));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 23890));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 23891));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 23892));
		float L_0 = ___0_r;
		__this->___r = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 23893));
		float L_1 = ___1_g;
		__this->___g = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 23894));
		float L_2 = ___2_b;
		__this->___b = L_2;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 23895));
		float L_3 = ___3_a;
		__this->___a = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 23896));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	float V_1 = 0.0f;
	bool V_2 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26246));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26247));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26248));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26249));
		float L_0 = ___0_value;
		V_0 = (bool)((((float)L_0) < ((float)(0.0f)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26250));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26251));
		V_1 = (0.0f);
		goto IL_002d;
	}

IL_0015:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26252));
		float L_2 = ___0_value;
		V_2 = (bool)((((float)L_2) > ((float)(1.0f)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26253));
		bool L_3 = V_2;
		if (!L_3)
		{
			goto IL_0029;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26254));
		V_1 = (1.0f);
		goto IL_002d;
	}

IL_0029:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26255));
		float L_4 = ___0_value;
		V_1 = L_4;
		goto IL_002d;
	}

IL_002d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 26256));
		float L_5 = V_1;
		return L_5;
	}
}
