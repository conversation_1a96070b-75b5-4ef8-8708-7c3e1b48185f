﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[26] = 
{
	{ 24489, 0,  41 },
	{ 17319, 1,  41 },
	{ 24489, 2,  42 },
	{ 24489, 0,  43 },
	{ 16614, 3,  43 },
	{ 24489, 2,  44 },
	{ 10343, 4,  46 },
	{ 31459, 5,  47 },
	{ 24489, 6,  47 },
	{ 24489, 7,  48 },
	{ 19079, 8,  49 },
	{ 24489, 9,  50 },
	{ 31482, 10,  50 },
	{ 27785, 11,  50 },
	{ 27693, 12,  50 },
	{ 28902, 13,  50 },
	{ 28902, 14,  50 },
	{ 22831, 15,  50 },
	{ 22831, 16,  50 },
	{ 24489, 17,  51 },
	{ 28902, 18,  51 },
	{ 28902, 19,  51 },
	{ 31459, 20,  51 },
	{ 24489, 7,  52 },
	{ 18823, 21,  53 },
	{ 18823, 22,  53 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[23] = 
{
	"count",
	"touches",
	"q",
	"events",
	"state",
	"mousePosition",
	"camerasCount",
	"hitIndex",
	"camera",
	"displayIndex",
	"eventPosition",
	"rect",
	"screenProjectionRay",
	"projectionDirection",
	"distanceToClipPlane",
	"hit3D",
	"hit2D",
	"eventDisplayIndex",
	"w",
	"h",
	"pos",
	"mouseDownThisFrame",
	"mousePressed",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[194] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 3 },
	{ 3, 3 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 6, 1 },
	{ 0, 0 },
	{ 7, 17 },
	{ 24, 2 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_InputLegacyModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_InputLegacyModule[723] = 
{
	{ 108324, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 108324, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 108324, 1, 51, 51, 35, 36, 0, kSequencePointKind_Normal, 0, 2 },
	{ 108324, 1, 51, 51, 37, 55, 1, kSequencePointKind_Normal, 0, 3 },
	{ 108324, 1, 51, 51, 56, 57, 10, kSequencePointKind_Normal, 0, 4 },
	{ 108325, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 5 },
	{ 108325, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 6 },
	{ 108325, 1, 51, 51, 62, 63, 0, kSequencePointKind_Normal, 0, 7 },
	{ 108325, 1, 51, 51, 64, 83, 1, kSequencePointKind_Normal, 0, 8 },
	{ 108325, 1, 51, 51, 84, 85, 8, kSequencePointKind_Normal, 0, 9 },
	{ 108326, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 10 },
	{ 108326, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 11 },
	{ 108326, 1, 52, 52, 39, 40, 0, kSequencePointKind_Normal, 0, 12 },
	{ 108326, 1, 52, 52, 41, 59, 1, kSequencePointKind_Normal, 0, 13 },
	{ 108326, 1, 52, 52, 60, 61, 10, kSequencePointKind_Normal, 0, 14 },
	{ 108327, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 15 },
	{ 108327, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 16 },
	{ 108327, 1, 52, 52, 66, 67, 0, kSequencePointKind_Normal, 0, 17 },
	{ 108327, 1, 52, 52, 68, 87, 1, kSequencePointKind_Normal, 0, 18 },
	{ 108327, 1, 52, 52, 88, 89, 8, kSequencePointKind_Normal, 0, 19 },
	{ 108328, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 20 },
	{ 108328, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 21 },
	{ 108328, 1, 53, 53, 42, 43, 0, kSequencePointKind_Normal, 0, 22 },
	{ 108328, 1, 53, 53, 44, 65, 1, kSequencePointKind_Normal, 0, 23 },
	{ 108328, 1, 53, 53, 66, 67, 10, kSequencePointKind_Normal, 0, 24 },
	{ 108329, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 25 },
	{ 108329, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 26 },
	{ 108329, 1, 53, 53, 72, 73, 0, kSequencePointKind_Normal, 0, 27 },
	{ 108329, 1, 53, 53, 74, 96, 1, kSequencePointKind_Normal, 0, 28 },
	{ 108329, 1, 53, 53, 97, 98, 8, kSequencePointKind_Normal, 0, 29 },
	{ 108330, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 30 },
	{ 108330, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 31 },
	{ 108330, 1, 54, 54, 44, 45, 0, kSequencePointKind_Normal, 0, 32 },
	{ 108330, 1, 54, 54, 46, 69, 1, kSequencePointKind_Normal, 0, 33 },
	{ 108330, 1, 54, 54, 70, 71, 10, kSequencePointKind_Normal, 0, 34 },
	{ 108331, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 35 },
	{ 108331, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 36 },
	{ 108331, 1, 54, 54, 76, 77, 0, kSequencePointKind_Normal, 0, 37 },
	{ 108331, 1, 54, 54, 78, 102, 1, kSequencePointKind_Normal, 0, 38 },
	{ 108331, 1, 54, 54, 103, 104, 8, kSequencePointKind_Normal, 0, 39 },
	{ 108332, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 40 },
	{ 108332, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 41 },
	{ 108332, 1, 55, 55, 38, 39, 0, kSequencePointKind_Normal, 0, 42 },
	{ 108332, 1, 55, 55, 40, 59, 1, kSequencePointKind_Normal, 0, 43 },
	{ 108332, 1, 55, 55, 60, 61, 10, kSequencePointKind_Normal, 0, 44 },
	{ 108333, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 45 },
	{ 108333, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 46 },
	{ 108333, 1, 55, 55, 66, 67, 0, kSequencePointKind_Normal, 0, 47 },
	{ 108333, 1, 55, 55, 68, 88, 1, kSequencePointKind_Normal, 0, 48 },
	{ 108333, 1, 55, 55, 89, 90, 8, kSequencePointKind_Normal, 0, 49 },
	{ 108334, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 50 },
	{ 108334, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 51 },
	{ 108334, 1, 56, 56, 35, 36, 0, kSequencePointKind_Normal, 0, 52 },
	{ 108334, 1, 56, 56, 37, 55, 1, kSequencePointKind_Normal, 0, 53 },
	{ 108334, 1, 56, 56, 56, 57, 10, kSequencePointKind_Normal, 0, 54 },
	{ 108335, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 55 },
	{ 108335, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 56 },
	{ 108335, 1, 56, 56, 62, 63, 0, kSequencePointKind_Normal, 0, 57 },
	{ 108335, 1, 56, 56, 64, 83, 1, kSequencePointKind_Normal, 0, 58 },
	{ 108335, 1, 56, 56, 84, 85, 8, kSequencePointKind_Normal, 0, 59 },
	{ 108336, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 60 },
	{ 108336, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 61 },
	{ 108336, 1, 57, 57, 39, 40, 0, kSequencePointKind_Normal, 0, 62 },
	{ 108336, 1, 57, 57, 41, 56, 1, kSequencePointKind_Normal, 0, 63 },
	{ 108336, 1, 57, 57, 57, 58, 10, kSequencePointKind_Normal, 0, 64 },
	{ 108337, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 65 },
	{ 108337, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 66 },
	{ 108337, 1, 57, 57, 63, 64, 0, kSequencePointKind_Normal, 0, 67 },
	{ 108337, 1, 57, 57, 65, 81, 1, kSequencePointKind_Normal, 0, 68 },
	{ 108337, 1, 57, 57, 82, 83, 8, kSequencePointKind_Normal, 0, 69 },
	{ 108338, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 70 },
	{ 108338, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 71 },
	{ 108338, 1, 58, 58, 37, 38, 0, kSequencePointKind_Normal, 0, 72 },
	{ 108338, 1, 58, 58, 39, 57, 1, kSequencePointKind_Normal, 0, 73 },
	{ 108338, 1, 58, 58, 58, 59, 10, kSequencePointKind_Normal, 0, 74 },
	{ 108339, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 75 },
	{ 108339, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 76 },
	{ 108339, 1, 58, 58, 64, 65, 0, kSequencePointKind_Normal, 0, 77 },
	{ 108339, 1, 58, 58, 66, 85, 1, kSequencePointKind_Normal, 0, 78 },
	{ 108339, 1, 58, 58, 86, 87, 8, kSequencePointKind_Normal, 0, 79 },
	{ 108340, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 80 },
	{ 108340, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 81 },
	{ 108340, 1, 59, 59, 52, 53, 0, kSequencePointKind_Normal, 0, 82 },
	{ 108340, 1, 59, 59, 54, 87, 1, kSequencePointKind_Normal, 0, 83 },
	{ 108340, 1, 59, 59, 88, 89, 10, kSequencePointKind_Normal, 0, 84 },
	{ 108341, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 85 },
	{ 108341, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 86 },
	{ 108341, 1, 59, 59, 94, 95, 0, kSequencePointKind_Normal, 0, 87 },
	{ 108341, 1, 59, 59, 96, 130, 1, kSequencePointKind_Normal, 0, 88 },
	{ 108341, 1, 59, 59, 131, 132, 8, kSequencePointKind_Normal, 0, 89 },
	{ 108342, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 90 },
	{ 108342, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 91 },
	{ 108342, 1, 61, 61, 37, 38, 0, kSequencePointKind_Normal, 0, 92 },
	{ 108342, 1, 61, 61, 39, 53, 1, kSequencePointKind_Normal, 0, 93 },
	{ 108342, 1, 61, 61, 54, 55, 10, kSequencePointKind_Normal, 0, 94 },
	{ 108343, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 95 },
	{ 108343, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 96 },
	{ 108343, 1, 61, 61, 60, 61, 0, kSequencePointKind_Normal, 0, 97 },
	{ 108343, 1, 61, 61, 62, 77, 1, kSequencePointKind_Normal, 0, 98 },
	{ 108343, 1, 61, 61, 78, 79, 8, kSequencePointKind_Normal, 0, 99 },
	{ 108344, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 100 },
	{ 108344, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 101 },
	{ 108344, 1, 62, 62, 42, 43, 0, kSequencePointKind_Normal, 0, 102 },
	{ 108344, 1, 62, 62, 44, 67, 1, kSequencePointKind_Normal, 0, 103 },
	{ 108344, 1, 62, 62, 68, 69, 10, kSequencePointKind_Normal, 0, 104 },
	{ 108345, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 105 },
	{ 108345, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 106 },
	{ 108345, 1, 62, 62, 74, 75, 0, kSequencePointKind_Normal, 0, 107 },
	{ 108345, 1, 62, 62, 76, 100, 1, kSequencePointKind_Normal, 0, 108 },
	{ 108345, 1, 62, 62, 101, 102, 8, kSequencePointKind_Normal, 0, 109 },
	{ 108346, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 110 },
	{ 108346, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 111 },
	{ 108346, 1, 63, 63, 41, 42, 0, kSequencePointKind_Normal, 0, 112 },
	{ 108346, 1, 63, 63, 43, 65, 1, kSequencePointKind_Normal, 0, 113 },
	{ 108346, 1, 63, 63, 66, 67, 10, kSequencePointKind_Normal, 0, 114 },
	{ 108347, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 115 },
	{ 108347, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 116 },
	{ 108347, 1, 63, 63, 72, 73, 0, kSequencePointKind_Normal, 0, 117 },
	{ 108347, 1, 63, 63, 74, 97, 1, kSequencePointKind_Normal, 0, 118 },
	{ 108347, 1, 63, 63, 98, 99, 8, kSequencePointKind_Normal, 0, 119 },
	{ 108348, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 120 },
	{ 108348, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 121 },
	{ 108348, 1, 64, 64, 35, 36, 0, kSequencePointKind_Normal, 0, 122 },
	{ 108348, 1, 64, 64, 37, 53, 1, kSequencePointKind_Normal, 0, 123 },
	{ 108348, 1, 64, 64, 54, 55, 10, kSequencePointKind_Normal, 0, 124 },
	{ 108349, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 125 },
	{ 108349, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 126 },
	{ 108349, 1, 64, 64, 60, 61, 0, kSequencePointKind_Normal, 0, 127 },
	{ 108349, 1, 64, 64, 62, 79, 1, kSequencePointKind_Normal, 0, 128 },
	{ 108349, 1, 64, 64, 80, 81, 8, kSequencePointKind_Normal, 0, 129 },
	{ 108350, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 130 },
	{ 108350, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 131 },
	{ 108350, 1, 65, 65, 43, 44, 0, kSequencePointKind_Normal, 0, 132 },
	{ 108350, 1, 65, 65, 45, 69, 1, kSequencePointKind_Normal, 0, 133 },
	{ 108350, 1, 65, 65, 70, 71, 10, kSequencePointKind_Normal, 0, 134 },
	{ 108351, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 135 },
	{ 108351, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 136 },
	{ 108351, 1, 65, 65, 76, 77, 0, kSequencePointKind_Normal, 0, 137 },
	{ 108351, 1, 65, 65, 78, 103, 1, kSequencePointKind_Normal, 0, 138 },
	{ 108351, 1, 65, 65, 104, 105, 8, kSequencePointKind_Normal, 0, 139 },
	{ 108352, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 140 },
	{ 108352, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 141 },
	{ 108352, 1, 113, 113, 43, 44, 0, kSequencePointKind_Normal, 0, 142 },
	{ 108352, 1, 113, 113, 45, 73, 1, kSequencePointKind_Normal, 0, 143 },
	{ 108352, 1, 113, 113, 45, 73, 19, kSequencePointKind_StepOut, 0, 144 },
	{ 108352, 1, 113, 113, 74, 75, 27, kSequencePointKind_Normal, 0, 145 },
	{ 108353, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 146 },
	{ 108353, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 147 },
	{ 108353, 1, 114, 114, 38, 39, 0, kSequencePointKind_Normal, 0, 148 },
	{ 108353, 1, 114, 114, 40, 59, 1, kSequencePointKind_Normal, 0, 149 },
	{ 108353, 1, 114, 114, 60, 61, 10, kSequencePointKind_Normal, 0, 150 },
	{ 108354, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 151 },
	{ 108354, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 152 },
	{ 108354, 1, 120, 120, 9, 38, 0, kSequencePointKind_Normal, 0, 153 },
	{ 108354, 1, 120, 120, 9, 38, 1, kSequencePointKind_StepOut, 0, 154 },
	{ 108354, 1, 121, 121, 9, 10, 7, kSequencePointKind_Normal, 0, 155 },
	{ 108354, 1, 122, 122, 13, 33, 8, kSequencePointKind_Normal, 0, 156 },
	{ 108354, 1, 123, 123, 9, 10, 15, kSequencePointKind_Normal, 0, 157 },
	{ 108364, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 158 },
	{ 108364, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 159 },
	{ 108364, 1, 146, 146, 43, 44, 0, kSequencePointKind_Normal, 0, 160 },
	{ 108364, 1, 146, 146, 45, 87, 1, kSequencePointKind_Normal, 0, 161 },
	{ 108364, 1, 146, 146, 45, 87, 7, kSequencePointKind_StepOut, 0, 162 },
	{ 108364, 1, 146, 146, 88, 89, 15, kSequencePointKind_Normal, 0, 163 },
	{ 108365, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 164 },
	{ 108365, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 165 },
	{ 108365, 1, 147, 147, 51, 52, 0, kSequencePointKind_Normal, 0, 166 },
	{ 108365, 1, 147, 147, 53, 103, 1, kSequencePointKind_Normal, 0, 167 },
	{ 108365, 1, 147, 147, 53, 103, 7, kSequencePointKind_StepOut, 0, 168 },
	{ 108365, 1, 147, 147, 104, 105, 15, kSequencePointKind_Normal, 0, 169 },
	{ 108366, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 170 },
	{ 108366, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 171 },
	{ 108366, 1, 148, 148, 38, 39, 0, kSequencePointKind_Normal, 0, 172 },
	{ 108366, 1, 148, 148, 40, 77, 1, kSequencePointKind_Normal, 0, 173 },
	{ 108366, 1, 148, 148, 40, 77, 7, kSequencePointKind_StepOut, 0, 174 },
	{ 108366, 1, 148, 148, 78, 79, 15, kSequencePointKind_Normal, 0, 175 },
	{ 108367, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 176 },
	{ 108367, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 177 },
	{ 108367, 1, 149, 149, 47, 48, 0, kSequencePointKind_Normal, 0, 178 },
	{ 108367, 1, 149, 149, 49, 95, 1, kSequencePointKind_Normal, 0, 179 },
	{ 108367, 1, 149, 149, 49, 95, 7, kSequencePointKind_StepOut, 0, 180 },
	{ 108367, 1, 149, 149, 96, 97, 15, kSequencePointKind_Normal, 0, 181 },
	{ 108368, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 182 },
	{ 108368, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 183 },
	{ 108368, 1, 150, 150, 42, 43, 0, kSequencePointKind_Normal, 0, 184 },
	{ 108368, 1, 150, 150, 44, 82, 1, kSequencePointKind_Normal, 0, 185 },
	{ 108368, 1, 150, 150, 44, 82, 7, kSequencePointKind_StepOut, 0, 186 },
	{ 108368, 1, 150, 150, 83, 84, 15, kSequencePointKind_Normal, 0, 187 },
	{ 108369, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 188 },
	{ 108369, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 189 },
	{ 108369, 1, 151, 151, 35, 36, 0, kSequencePointKind_Normal, 0, 190 },
	{ 108369, 1, 151, 151, 37, 77, 1, kSequencePointKind_Normal, 0, 191 },
	{ 108369, 1, 151, 151, 37, 77, 7, kSequencePointKind_StepOut, 0, 192 },
	{ 108369, 1, 151, 151, 78, 79, 15, kSequencePointKind_Normal, 0, 193 },
	{ 108370, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 194 },
	{ 108370, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 195 },
	{ 108370, 1, 151, 151, 84, 85, 0, kSequencePointKind_Normal, 0, 196 },
	{ 108370, 1, 151, 151, 86, 126, 1, kSequencePointKind_Normal, 0, 197 },
	{ 108370, 1, 151, 151, 86, 126, 8, kSequencePointKind_StepOut, 0, 198 },
	{ 108370, 1, 151, 151, 127, 128, 14, kSequencePointKind_Normal, 0, 199 },
	{ 108371, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 200 },
	{ 108371, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 201 },
	{ 108371, 1, 152, 152, 43, 44, 0, kSequencePointKind_Normal, 0, 202 },
	{ 108371, 1, 152, 152, 45, 92, 1, kSequencePointKind_Normal, 0, 203 },
	{ 108371, 1, 152, 152, 45, 92, 7, kSequencePointKind_StepOut, 0, 204 },
	{ 108371, 1, 152, 152, 93, 94, 15, kSequencePointKind_Normal, 0, 205 },
	{ 108372, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 206 },
	{ 108372, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 207 },
	{ 108372, 1, 152, 152, 99, 100, 0, kSequencePointKind_Normal, 0, 208 },
	{ 108372, 1, 152, 152, 101, 148, 1, kSequencePointKind_Normal, 0, 209 },
	{ 108372, 1, 152, 152, 101, 148, 8, kSequencePointKind_StepOut, 0, 210 },
	{ 108372, 1, 152, 152, 149, 150, 14, kSequencePointKind_Normal, 0, 211 },
	{ 108378, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 212 },
	{ 108378, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 213 },
	{ 108378, 1, 164, 164, 37, 38, 0, kSequencePointKind_Normal, 0, 214 },
	{ 108378, 1, 164, 164, 39, 57, 1, kSequencePointKind_Normal, 0, 215 },
	{ 108378, 1, 164, 164, 58, 59, 10, kSequencePointKind_Normal, 0, 216 },
	{ 108379, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 217 },
	{ 108379, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 218 },
	{ 108379, 1, 165, 165, 38, 39, 0, kSequencePointKind_Normal, 0, 219 },
	{ 108379, 1, 165, 165, 40, 59, 1, kSequencePointKind_Normal, 0, 220 },
	{ 108379, 1, 165, 165, 60, 61, 10, kSequencePointKind_Normal, 0, 221 },
	{ 108380, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 222 },
	{ 108380, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 223 },
	{ 108380, 1, 166, 166, 37, 38, 0, kSequencePointKind_Normal, 0, 224 },
	{ 108380, 1, 166, 166, 39, 57, 1, kSequencePointKind_Normal, 0, 225 },
	{ 108380, 1, 166, 166, 58, 59, 10, kSequencePointKind_Normal, 0, 226 },
	{ 108381, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 227 },
	{ 108381, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 228 },
	{ 108381, 1, 167, 167, 47, 48, 0, kSequencePointKind_Normal, 0, 229 },
	{ 108381, 1, 167, 167, 49, 77, 1, kSequencePointKind_Normal, 0, 230 },
	{ 108381, 1, 167, 167, 78, 79, 10, kSequencePointKind_Normal, 0, 231 },
	{ 108382, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 232 },
	{ 108382, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 233 },
	{ 108382, 1, 168, 168, 45, 46, 0, kSequencePointKind_Normal, 0, 234 },
	{ 108382, 1, 168, 168, 47, 73, 1, kSequencePointKind_Normal, 0, 235 },
	{ 108382, 1, 168, 168, 74, 75, 10, kSequencePointKind_Normal, 0, 236 },
	{ 108383, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 237 },
	{ 108383, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 238 },
	{ 108383, 1, 169, 169, 39, 40, 0, kSequencePointKind_Normal, 0, 239 },
	{ 108383, 1, 169, 169, 41, 60, 1, kSequencePointKind_Normal, 0, 240 },
	{ 108383, 1, 169, 169, 61, 62, 10, kSequencePointKind_Normal, 0, 241 },
	{ 108394, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 242 },
	{ 108394, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 243 },
	{ 108394, 1, 214, 214, 43, 44, 0, kSequencePointKind_Normal, 0, 244 },
	{ 108394, 1, 214, 214, 45, 77, 1, kSequencePointKind_Normal, 0, 245 },
	{ 108394, 1, 214, 214, 45, 77, 1, kSequencePointKind_StepOut, 0, 246 },
	{ 108394, 1, 214, 214, 78, 79, 9, kSequencePointKind_Normal, 0, 247 },
	{ 108395, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 248 },
	{ 108395, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 249 },
	{ 108395, 1, 215, 215, 51, 52, 0, kSequencePointKind_Normal, 0, 250 },
	{ 108395, 1, 215, 215, 53, 80, 1, kSequencePointKind_Normal, 0, 251 },
	{ 108395, 1, 215, 215, 53, 80, 1, kSequencePointKind_StepOut, 0, 252 },
	{ 108395, 1, 215, 215, 81, 82, 9, kSequencePointKind_Normal, 0, 253 },
	{ 108396, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 254 },
	{ 108396, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 255 },
	{ 108396, 1, 219, 219, 13, 14, 0, kSequencePointKind_Normal, 0, 256 },
	{ 108396, 1, 220, 220, 17, 61, 1, kSequencePointKind_Normal, 0, 257 },
	{ 108396, 1, 220, 220, 17, 61, 2, kSequencePointKind_StepOut, 0, 258 },
	{ 108396, 1, 220, 220, 0, 0, 14, kSequencePointKind_Normal, 0, 259 },
	{ 108396, 1, 221, 221, 21, 136, 17, kSequencePointKind_Normal, 0, 260 },
	{ 108396, 1, 221, 221, 21, 136, 22, kSequencePointKind_StepOut, 0, 261 },
	{ 108396, 1, 223, 223, 17, 42, 28, kSequencePointKind_Normal, 0, 262 },
	{ 108396, 1, 223, 223, 17, 42, 28, kSequencePointKind_StepOut, 0, 263 },
	{ 108396, 1, 224, 224, 13, 14, 36, kSequencePointKind_Normal, 0, 264 },
	{ 108397, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 265 },
	{ 108397, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 266 },
	{ 108397, 1, 228, 228, 9, 10, 0, kSequencePointKind_Normal, 0, 267 },
	{ 108397, 1, 229, 229, 13, 57, 1, kSequencePointKind_Normal, 0, 268 },
	{ 108397, 1, 229, 229, 13, 57, 2, kSequencePointKind_StepOut, 0, 269 },
	{ 108397, 1, 230, 230, 13, 55, 8, kSequencePointKind_Normal, 0, 270 },
	{ 108397, 1, 230, 230, 13, 55, 9, kSequencePointKind_StepOut, 0, 271 },
	{ 108397, 1, 231, 231, 13, 37, 15, kSequencePointKind_Normal, 0, 272 },
	{ 108397, 1, 231, 231, 13, 37, 15, kSequencePointKind_StepOut, 0, 273 },
	{ 108397, 1, 232, 232, 9, 10, 21, kSequencePointKind_Normal, 0, 274 },
	{ 108398, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 275 },
	{ 108398, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 276 },
	{ 108398, 1, 235, 235, 9, 10, 0, kSequencePointKind_Normal, 0, 277 },
	{ 108398, 1, 236, 236, 13, 49, 1, kSequencePointKind_Normal, 0, 278 },
	{ 108398, 1, 236, 236, 13, 49, 8, kSequencePointKind_StepOut, 0, 279 },
	{ 108398, 1, 237, 237, 9, 10, 14, kSequencePointKind_Normal, 0, 280 },
	{ 108399, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 281 },
	{ 108399, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 282 },
	{ 108399, 1, 240, 240, 9, 10, 0, kSequencePointKind_Normal, 0, 283 },
	{ 108399, 1, 241, 241, 13, 29, 1, kSequencePointKind_Normal, 0, 284 },
	{ 108399, 1, 241, 241, 13, 29, 12, kSequencePointKind_StepOut, 0, 285 },
	{ 108399, 1, 242, 242, 9, 10, 18, kSequencePointKind_Normal, 0, 286 },
	{ 108400, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 287 },
	{ 108400, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 288 },
	{ 108400, 1, 245, 245, 9, 10, 0, kSequencePointKind_Normal, 0, 289 },
	{ 108400, 1, 246, 246, 13, 36, 1, kSequencePointKind_Normal, 0, 290 },
	{ 108400, 1, 246, 246, 13, 36, 1, kSequencePointKind_StepOut, 0, 291 },
	{ 108400, 1, 247, 247, 9, 10, 7, kSequencePointKind_Normal, 0, 292 },
	{ 108404, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 293 },
	{ 108404, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 294 },
	{ 108404, 1, 254, 254, 17, 18, 0, kSequencePointKind_Normal, 0, 295 },
	{ 108404, 1, 254, 254, 19, 75, 1, kSequencePointKind_Normal, 0, 296 },
	{ 108404, 1, 254, 254, 19, 75, 1, kSequencePointKind_StepOut, 0, 297 },
	{ 108404, 1, 254, 254, 76, 77, 14, kSequencePointKind_Normal, 0, 298 },
	{ 108405, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 299 },
	{ 108405, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 300 },
	{ 108405, 1, 258, 258, 17, 18, 0, kSequencePointKind_Normal, 0, 301 },
	{ 108405, 1, 258, 258, 19, 71, 1, kSequencePointKind_Normal, 0, 302 },
	{ 108405, 1, 258, 258, 19, 71, 1, kSequencePointKind_StepOut, 0, 303 },
	{ 108405, 1, 258, 258, 72, 73, 14, kSequencePointKind_Normal, 0, 304 },
	{ 108406, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 305 },
	{ 108406, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 306 },
	{ 108406, 1, 262, 262, 17, 18, 0, kSequencePointKind_Normal, 0, 307 },
	{ 108406, 1, 262, 262, 19, 75, 1, kSequencePointKind_Normal, 0, 308 },
	{ 108406, 1, 262, 262, 19, 75, 1, kSequencePointKind_StepOut, 0, 309 },
	{ 108406, 1, 262, 262, 76, 77, 14, kSequencePointKind_Normal, 0, 310 },
	{ 108407, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 311 },
	{ 108407, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 312 },
	{ 108407, 1, 266, 266, 17, 18, 0, kSequencePointKind_Normal, 0, 313 },
	{ 108407, 1, 266, 266, 19, 63, 1, kSequencePointKind_Normal, 0, 314 },
	{ 108407, 1, 266, 266, 19, 63, 1, kSequencePointKind_StepOut, 0, 315 },
	{ 108407, 1, 266, 266, 64, 65, 14, kSequencePointKind_Normal, 0, 316 },
	{ 108408, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 317 },
	{ 108408, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 318 },
	{ 108408, 1, 270, 270, 17, 18, 0, kSequencePointKind_Normal, 0, 319 },
	{ 108408, 1, 270, 270, 19, 69, 1, kSequencePointKind_Normal, 0, 320 },
	{ 108408, 1, 270, 270, 19, 69, 1, kSequencePointKind_StepOut, 0, 321 },
	{ 108408, 1, 270, 270, 70, 71, 14, kSequencePointKind_Normal, 0, 322 },
	{ 108409, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 323 },
	{ 108409, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 324 },
	{ 108409, 1, 274, 274, 17, 18, 0, kSequencePointKind_Normal, 0, 325 },
	{ 108409, 1, 274, 274, 19, 68, 1, kSequencePointKind_Normal, 0, 326 },
	{ 108409, 1, 274, 274, 19, 68, 1, kSequencePointKind_StepOut, 0, 327 },
	{ 108409, 1, 274, 274, 69, 70, 9, kSequencePointKind_Normal, 0, 328 },
	{ 108410, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 329 },
	{ 108410, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 330 },
	{ 108410, 1, 275, 275, 17, 18, 0, kSequencePointKind_Normal, 0, 331 },
	{ 108410, 1, 275, 275, 19, 67, 1, kSequencePointKind_Normal, 0, 332 },
	{ 108410, 1, 275, 275, 19, 67, 2, kSequencePointKind_StepOut, 0, 333 },
	{ 108410, 1, 275, 275, 68, 69, 8, kSequencePointKind_Normal, 0, 334 },
	{ 108417, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 335 },
	{ 108417, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 336 },
	{ 108417, 1, 370, 370, 57, 102, 0, kSequencePointKind_Normal, 0, 337 },
	{ 108417, 1, 370, 370, 57, 102, 1, kSequencePointKind_StepOut, 0, 338 },
	{ 108418, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 339 },
	{ 108418, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 340 },
	{ 108418, 1, 371, 371, 60, 108, 0, kSequencePointKind_Normal, 0, 341 },
	{ 108418, 1, 371, 371, 60, 108, 1, kSequencePointKind_StepOut, 0, 342 },
	{ 108419, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 343 },
	{ 108419, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 344 },
	{ 108419, 1, 372, 372, 60, 109, 0, kSequencePointKind_Normal, 0, 345 },
	{ 108419, 1, 372, 372, 60, 109, 1, kSequencePointKind_StepOut, 0, 346 },
	{ 108420, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 347 },
	{ 108420, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 348 },
	{ 108420, 1, 373, 373, 64, 117, 0, kSequencePointKind_Normal, 0, 349 },
	{ 108420, 1, 373, 373, 64, 117, 1, kSequencePointKind_StepOut, 0, 350 },
	{ 108421, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 351 },
	{ 108421, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 352 },
	{ 108421, 1, 374, 374, 62, 113, 0, kSequencePointKind_Normal, 0, 353 },
	{ 108421, 1, 374, 374, 62, 113, 1, kSequencePointKind_StepOut, 0, 354 },
	{ 108436, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 355 },
	{ 108436, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 356 },
	{ 108436, 1, 408, 408, 51, 65, 0, kSequencePointKind_Normal, 0, 357 },
	{ 108436, 1, 408, 408, 51, 65, 1, kSequencePointKind_StepOut, 0, 358 },
	{ 108437, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 359 },
	{ 108437, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 360 },
	{ 108437, 1, 409, 409, 51, 97, 0, kSequencePointKind_Normal, 0, 361 },
	{ 108437, 1, 409, 409, 51, 97, 1, kSequencePointKind_StepOut, 0, 362 },
	{ 108438, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 363 },
	{ 108438, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 364 },
	{ 108438, 1, 410, 410, 53, 69, 0, kSequencePointKind_Normal, 0, 365 },
	{ 108438, 1, 410, 410, 53, 69, 1, kSequencePointKind_StepOut, 0, 366 },
	{ 108439, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 367 },
	{ 108439, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 368 },
	{ 108439, 1, 411, 411, 53, 101, 0, kSequencePointKind_Normal, 0, 369 },
	{ 108439, 1, 411, 411, 53, 101, 1, kSequencePointKind_StepOut, 0, 370 },
	{ 108440, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 371 },
	{ 108440, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 372 },
	{ 108440, 1, 412, 412, 55, 73, 0, kSequencePointKind_Normal, 0, 373 },
	{ 108440, 1, 412, 412, 55, 73, 1, kSequencePointKind_StepOut, 0, 374 },
	{ 108441, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 375 },
	{ 108441, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 376 },
	{ 108441, 1, 413, 413, 55, 105, 0, kSequencePointKind_Normal, 0, 377 },
	{ 108441, 1, 413, 413, 55, 105, 1, kSequencePointKind_StepOut, 0, 378 },
	{ 108442, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 379 },
	{ 108442, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 380 },
	{ 108442, 1, 417, 417, 9, 10, 0, kSequencePointKind_Normal, 0, 381 },
	{ 108442, 1, 419, 419, 9, 10, 1, kSequencePointKind_Normal, 0, 382 },
	{ 108475, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 383 },
	{ 108475, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 384 },
	{ 108475, 1, 523, 523, 13, 14, 0, kSequencePointKind_Normal, 0, 385 },
	{ 108475, 1, 524, 524, 17, 53, 1, kSequencePointKind_Normal, 0, 386 },
	{ 108475, 1, 524, 524, 0, 0, 10, kSequencePointKind_Normal, 0, 387 },
	{ 108475, 1, 525, 525, 21, 69, 13, kSequencePointKind_Normal, 0, 388 },
	{ 108475, 1, 525, 525, 21, 69, 13, kSequencePointKind_StepOut, 0, 389 },
	{ 108475, 1, 526, 526, 17, 48, 23, kSequencePointKind_Normal, 0, 390 },
	{ 108475, 1, 527, 527, 13, 14, 31, kSequencePointKind_Normal, 0, 391 },
	{ 108476, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 392 },
	{ 108476, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 393 },
	{ 108476, 1, 533, 533, 13, 14, 0, kSequencePointKind_Normal, 0, 394 },
	{ 108476, 1, 534, 534, 17, 45, 1, kSequencePointKind_Normal, 0, 395 },
	{ 108476, 1, 534, 534, 0, 0, 10, kSequencePointKind_Normal, 0, 396 },
	{ 108476, 1, 535, 535, 21, 53, 13, kSequencePointKind_Normal, 0, 397 },
	{ 108476, 1, 535, 535, 21, 53, 13, kSequencePointKind_StepOut, 0, 398 },
	{ 108476, 1, 536, 536, 17, 40, 23, kSequencePointKind_Normal, 0, 399 },
	{ 108476, 1, 537, 537, 13, 14, 31, kSequencePointKind_Normal, 0, 400 },
	{ 108478, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 401 },
	{ 108478, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 402 },
	{ 108478, 1, 545, 545, 13, 14, 0, kSequencePointKind_Normal, 0, 403 },
	{ 108478, 1, 546, 546, 17, 40, 1, kSequencePointKind_Normal, 0, 404 },
	{ 108478, 1, 546, 546, 0, 0, 10, kSequencePointKind_Normal, 0, 405 },
	{ 108478, 1, 547, 547, 21, 67, 13, kSequencePointKind_Normal, 0, 406 },
	{ 108478, 1, 547, 547, 21, 67, 13, kSequencePointKind_StepOut, 0, 407 },
	{ 108478, 1, 547, 547, 21, 67, 18, kSequencePointKind_StepOut, 0, 408 },
	{ 108478, 1, 548, 548, 17, 35, 28, kSequencePointKind_Normal, 0, 409 },
	{ 108478, 1, 549, 549, 13, 14, 36, kSequencePointKind_Normal, 0, 410 },
	{ 108479, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 411 },
	{ 108479, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 412 },
	{ 108479, 1, 555, 555, 13, 14, 0, kSequencePointKind_Normal, 0, 413 },
	{ 108479, 1, 556, 556, 17, 40, 1, kSequencePointKind_Normal, 0, 414 },
	{ 108479, 1, 556, 556, 17, 40, 1, kSequencePointKind_StepOut, 0, 415 },
	{ 108479, 1, 557, 557, 17, 52, 7, kSequencePointKind_Normal, 0, 416 },
	{ 108479, 1, 558, 558, 22, 31, 14, kSequencePointKind_Normal, 0, 417 },
	{ 108479, 1, 558, 558, 0, 0, 16, kSequencePointKind_Normal, 0, 418 },
	{ 108479, 1, 559, 559, 21, 46, 18, kSequencePointKind_Normal, 0, 419 },
	{ 108479, 1, 559, 559, 21, 46, 21, kSequencePointKind_StepOut, 0, 420 },
	{ 108479, 1, 558, 558, 44, 47, 31, kSequencePointKind_Normal, 0, 421 },
	{ 108479, 1, 558, 558, 33, 42, 35, kSequencePointKind_Normal, 0, 422 },
	{ 108479, 1, 558, 558, 0, 0, 40, kSequencePointKind_Normal, 0, 423 },
	{ 108479, 1, 560, 560, 17, 32, 43, kSequencePointKind_Normal, 0, 424 },
	{ 108479, 1, 561, 561, 13, 14, 48, kSequencePointKind_Normal, 0, 425 },
	{ 108480, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 426 },
	{ 108480, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 427 },
	{ 108480, 1, 567, 567, 13, 14, 0, kSequencePointKind_Normal, 0, 428 },
	{ 108480, 1, 568, 568, 17, 52, 1, kSequencePointKind_Normal, 0, 429 },
	{ 108480, 1, 568, 568, 17, 52, 1, kSequencePointKind_StepOut, 0, 430 },
	{ 108480, 1, 569, 569, 17, 75, 7, kSequencePointKind_Normal, 0, 431 },
	{ 108480, 1, 570, 570, 22, 31, 14, kSequencePointKind_Normal, 0, 432 },
	{ 108480, 1, 570, 570, 0, 0, 16, kSequencePointKind_Normal, 0, 433 },
	{ 108480, 1, 571, 571, 21, 57, 18, kSequencePointKind_Normal, 0, 434 },
	{ 108480, 1, 571, 571, 21, 57, 21, kSequencePointKind_StepOut, 0, 435 },
	{ 108480, 1, 570, 570, 44, 47, 31, kSequencePointKind_Normal, 0, 436 },
	{ 108480, 1, 570, 570, 33, 42, 35, kSequencePointKind_Normal, 0, 437 },
	{ 108480, 1, 570, 570, 0, 0, 40, kSequencePointKind_Normal, 0, 438 },
	{ 108480, 1, 572, 572, 17, 31, 43, kSequencePointKind_Normal, 0, 439 },
	{ 108480, 1, 573, 573, 13, 14, 48, kSequencePointKind_Normal, 0, 440 },
	{ 108493, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 441 },
	{ 108493, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 442 },
	{ 108493, 2, 57, 57, 9, 10, 0, kSequencePointKind_Normal, 0, 443 },
	{ 108493, 2, 58, 58, 13, 41, 1, kSequencePointKind_Normal, 0, 444 },
	{ 108493, 2, 58, 58, 0, 0, 10, kSequencePointKind_Normal, 0, 445 },
	{ 108493, 2, 59, 59, 13, 14, 13, kSequencePointKind_Normal, 0, 446 },
	{ 108493, 2, 62, 62, 17, 47, 14, kSequencePointKind_Normal, 0, 447 },
	{ 108493, 2, 62, 62, 17, 47, 19, kSequencePointKind_StepOut, 0, 448 },
	{ 108493, 2, 63, 63, 17, 47, 25, kSequencePointKind_Normal, 0, 449 },
	{ 108493, 2, 63, 63, 17, 47, 27, kSequencePointKind_StepOut, 0, 450 },
	{ 108493, 2, 64, 64, 17, 107, 37, kSequencePointKind_Normal, 0, 451 },
	{ 108493, 2, 64, 64, 17, 107, 39, kSequencePointKind_StepOut, 0, 452 },
	{ 108493, 2, 65, 65, 17, 92, 52, kSequencePointKind_Normal, 0, 453 },
	{ 108493, 2, 65, 65, 17, 92, 54, kSequencePointKind_StepOut, 0, 454 },
	{ 108493, 2, 66, 66, 13, 14, 67, kSequencePointKind_Normal, 0, 455 },
	{ 108493, 2, 66, 66, 0, 0, 68, kSequencePointKind_Normal, 0, 456 },
	{ 108493, 2, 67, 67, 18, 57, 70, kSequencePointKind_Normal, 0, 457 },
	{ 108493, 2, 67, 67, 18, 57, 70, kSequencePointKind_StepOut, 0, 458 },
	{ 108493, 2, 67, 67, 0, 0, 79, kSequencePointKind_Normal, 0, 459 },
	{ 108493, 2, 68, 68, 13, 14, 82, kSequencePointKind_Normal, 0, 460 },
	{ 108493, 2, 69, 69, 17, 55, 83, kSequencePointKind_Normal, 0, 461 },
	{ 108493, 2, 69, 69, 17, 55, 83, kSequencePointKind_StepOut, 0, 462 },
	{ 108493, 2, 69, 69, 17, 55, 88, kSequencePointKind_StepOut, 0, 463 },
	{ 108493, 2, 70, 70, 17, 77, 98, kSequencePointKind_Normal, 0, 464 },
	{ 108493, 2, 70, 70, 17, 77, 99, kSequencePointKind_StepOut, 0, 465 },
	{ 108493, 2, 71, 71, 17, 66, 109, kSequencePointKind_Normal, 0, 466 },
	{ 108493, 2, 71, 71, 17, 66, 110, kSequencePointKind_StepOut, 0, 467 },
	{ 108493, 2, 72, 72, 13, 14, 120, kSequencePointKind_Normal, 0, 468 },
	{ 108493, 2, 72, 72, 0, 0, 121, kSequencePointKind_Normal, 0, 469 },
	{ 108493, 2, 74, 74, 13, 14, 123, kSequencePointKind_Normal, 0, 470 },
	{ 108493, 2, 75, 75, 17, 43, 124, kSequencePointKind_Normal, 0, 471 },
	{ 108493, 2, 76, 76, 17, 57, 135, kSequencePointKind_Normal, 0, 472 },
	{ 108493, 2, 77, 77, 17, 50, 141, kSequencePointKind_Normal, 0, 473 },
	{ 108493, 2, 78, 78, 13, 14, 147, kSequencePointKind_Normal, 0, 474 },
	{ 108493, 2, 79, 79, 9, 10, 148, kSequencePointKind_Normal, 0, 475 },
	{ 108494, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 476 },
	{ 108494, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 477 },
	{ 108494, 2, 83, 83, 9, 10, 0, kSequencePointKind_Normal, 0, 478 },
	{ 108494, 2, 84, 84, 13, 32, 1, kSequencePointKind_Normal, 0, 479 },
	{ 108494, 2, 85, 85, 9, 10, 7, kSequencePointKind_Normal, 0, 480 },
	{ 108495, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 481 },
	{ 108495, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 482 },
	{ 108495, 2, 89, 89, 9, 10, 0, kSequencePointKind_Normal, 0, 483 },
	{ 108495, 2, 90, 90, 13, 27, 1, kSequencePointKind_Normal, 0, 484 },
	{ 108495, 2, 90, 90, 13, 27, 1, kSequencePointKind_StepOut, 0, 485 },
	{ 108495, 2, 91, 91, 13, 49, 7, kSequencePointKind_Normal, 0, 486 },
	{ 108495, 2, 93, 93, 13, 55, 13, kSequencePointKind_Normal, 0, 487 },
	{ 108495, 2, 93, 93, 13, 55, 13, kSequencePointKind_StepOut, 0, 488 },
	{ 108495, 2, 94, 94, 13, 71, 19, kSequencePointKind_Normal, 0, 489 },
	{ 108495, 2, 94, 94, 0, 0, 43, kSequencePointKind_Normal, 0, 490 },
	{ 108495, 2, 95, 95, 17, 54, 46, kSequencePointKind_Normal, 0, 491 },
	{ 108495, 2, 98, 98, 13, 45, 57, kSequencePointKind_Normal, 0, 492 },
	{ 108495, 2, 98, 98, 13, 45, 62, kSequencePointKind_StepOut, 0, 493 },
	{ 108495, 2, 101, 101, 18, 34, 68, kSequencePointKind_Normal, 0, 494 },
	{ 108495, 2, 101, 101, 0, 0, 70, kSequencePointKind_Normal, 0, 495 },
	{ 108495, 2, 102, 102, 17, 56, 72, kSequencePointKind_Normal, 0, 496 },
	{ 108495, 2, 101, 101, 68, 78, 89, kSequencePointKind_Normal, 0, 497 },
	{ 108495, 2, 101, 101, 36, 66, 93, kSequencePointKind_Normal, 0, 498 },
	{ 108495, 2, 101, 101, 0, 0, 105, kSequencePointKind_Normal, 0, 499 },
	{ 108495, 2, 106, 106, 13, 30, 109, kSequencePointKind_Normal, 0, 500 },
	{ 108495, 2, 106, 106, 0, 0, 119, kSequencePointKind_Normal, 0, 501 },
	{ 108495, 2, 107, 107, 13, 14, 126, kSequencePointKind_Normal, 0, 502 },
	{ 108495, 2, 108, 108, 17, 24, 127, kSequencePointKind_Normal, 0, 503 },
	{ 108495, 2, 108, 108, 40, 49, 128, kSequencePointKind_Normal, 0, 504 },
	{ 108495, 2, 108, 108, 0, 0, 138, kSequencePointKind_Normal, 0, 505 },
	{ 108495, 2, 108, 108, 26, 36, 143, kSequencePointKind_Normal, 0, 506 },
	{ 108495, 2, 109, 109, 17, 18, 150, kSequencePointKind_Normal, 0, 507 },
	{ 108495, 2, 111, 111, 21, 94, 151, kSequencePointKind_Normal, 0, 508 },
	{ 108495, 2, 111, 111, 21, 94, 154, kSequencePointKind_StepOut, 0, 509 },
	{ 108495, 2, 111, 111, 21, 94, 166, kSequencePointKind_StepOut, 0, 510 },
	{ 108495, 2, 111, 111, 21, 94, 172, kSequencePointKind_StepOut, 0, 511 },
	{ 108495, 2, 111, 111, 0, 0, 185, kSequencePointKind_Normal, 0, 512 },
	{ 108495, 2, 112, 112, 25, 34, 189, kSequencePointKind_Normal, 0, 513 },
	{ 108495, 2, 114, 114, 21, 61, 194, kSequencePointKind_Normal, 0, 514 },
	{ 108495, 2, 114, 114, 21, 61, 196, kSequencePointKind_StepOut, 0, 515 },
	{ 108495, 2, 116, 116, 21, 80, 203, kSequencePointKind_Normal, 0, 516 },
	{ 108495, 2, 116, 116, 21, 80, 204, kSequencePointKind_StepOut, 0, 517 },
	{ 108495, 2, 116, 116, 21, 80, 209, kSequencePointKind_StepOut, 0, 518 },
	{ 108495, 2, 118, 118, 21, 55, 216, kSequencePointKind_Normal, 0, 519 },
	{ 108495, 2, 118, 118, 21, 55, 218, kSequencePointKind_StepOut, 0, 520 },
	{ 108495, 2, 118, 118, 21, 55, 223, kSequencePointKind_StepOut, 0, 521 },
	{ 108495, 2, 118, 118, 0, 0, 230, kSequencePointKind_Normal, 0, 522 },
	{ 108495, 2, 119, 119, 21, 22, 237, kSequencePointKind_Normal, 0, 523 },
	{ 108495, 2, 121, 121, 25, 70, 238, kSequencePointKind_Normal, 0, 524 },
	{ 108495, 2, 124, 124, 25, 63, 248, kSequencePointKind_Normal, 0, 525 },
	{ 108495, 2, 124, 124, 0, 0, 259, kSequencePointKind_Normal, 0, 526 },
	{ 108495, 2, 125, 125, 29, 38, 263, kSequencePointKind_Normal, 0, 527 },
	{ 108495, 2, 130, 130, 25, 48, 268, kSequencePointKind_Normal, 0, 528 },
	{ 108495, 2, 130, 130, 25, 48, 268, kSequencePointKind_StepOut, 0, 529 },
	{ 108495, 2, 131, 131, 25, 49, 276, kSequencePointKind_Normal, 0, 530 },
	{ 108495, 2, 131, 131, 25, 49, 276, kSequencePointKind_StepOut, 0, 531 },
	{ 108495, 2, 132, 132, 25, 88, 284, kSequencePointKind_Normal, 0, 532 },
	{ 108495, 2, 132, 132, 0, 0, 305, kSequencePointKind_Normal, 0, 533 },
	{ 108495, 2, 133, 133, 25, 26, 309, kSequencePointKind_Normal, 0, 534 },
	{ 108495, 2, 134, 134, 29, 76, 310, kSequencePointKind_Normal, 0, 535 },
	{ 108495, 2, 134, 134, 29, 76, 318, kSequencePointKind_StepOut, 0, 536 },
	{ 108495, 2, 135, 135, 29, 77, 326, kSequencePointKind_Normal, 0, 537 },
	{ 108495, 2, 135, 135, 29, 77, 334, kSequencePointKind_StepOut, 0, 538 },
	{ 108495, 2, 136, 136, 25, 26, 342, kSequencePointKind_Normal, 0, 539 },
	{ 108495, 2, 138, 138, 25, 93, 343, kSequencePointKind_Normal, 0, 540 },
	{ 108495, 2, 138, 138, 25, 93, 365, kSequencePointKind_StepOut, 0, 541 },
	{ 108495, 2, 141, 141, 25, 82, 370, kSequencePointKind_Normal, 0, 542 },
	{ 108495, 2, 141, 141, 0, 0, 431, kSequencePointKind_Normal, 0, 543 },
	{ 108495, 2, 142, 142, 29, 38, 435, kSequencePointKind_Normal, 0, 544 },
	{ 108495, 2, 143, 143, 21, 22, 440, kSequencePointKind_Normal, 0, 545 },
	{ 108495, 2, 143, 143, 0, 0, 441, kSequencePointKind_Normal, 0, 546 },
	{ 108495, 2, 145, 145, 21, 22, 443, kSequencePointKind_Normal, 0, 547 },
	{ 108495, 2, 148, 148, 25, 55, 444, kSequencePointKind_Normal, 0, 548 },
	{ 108495, 2, 148, 148, 25, 55, 445, kSequencePointKind_StepOut, 0, 549 },
	{ 108495, 2, 154, 154, 21, 22, 452, kSequencePointKind_Normal, 0, 550 },
	{ 108495, 2, 157, 157, 21, 49, 453, kSequencePointKind_Normal, 0, 551 },
	{ 108495, 2, 157, 157, 21, 49, 455, kSequencePointKind_StepOut, 0, 552 },
	{ 108495, 2, 158, 158, 21, 55, 462, kSequencePointKind_Normal, 0, 553 },
	{ 108495, 2, 158, 158, 21, 55, 466, kSequencePointKind_StepOut, 0, 554 },
	{ 108495, 2, 158, 158, 0, 0, 476, kSequencePointKind_Normal, 0, 555 },
	{ 108495, 2, 159, 159, 25, 34, 480, kSequencePointKind_Normal, 0, 556 },
	{ 108495, 2, 163, 163, 21, 47, 485, kSequencePointKind_Normal, 0, 557 },
	{ 108495, 2, 163, 163, 21, 47, 487, kSequencePointKind_StepOut, 0, 558 },
	{ 108495, 2, 163, 163, 0, 0, 497, kSequencePointKind_Normal, 0, 559 },
	{ 108495, 2, 164, 164, 25, 34, 501, kSequencePointKind_Normal, 0, 560 },
	{ 108495, 2, 167, 167, 21, 86, 506, kSequencePointKind_Normal, 0, 561 },
	{ 108495, 2, 167, 167, 21, 86, 510, kSequencePointKind_StepOut, 0, 562 },
	{ 108495, 2, 168, 168, 21, 79, 517, kSequencePointKind_Normal, 0, 563 },
	{ 108495, 2, 168, 168, 21, 79, 519, kSequencePointKind_StepOut, 0, 564 },
	{ 108495, 2, 169, 169, 21, 191, 531, kSequencePointKind_Normal, 0, 565 },
	{ 108495, 2, 169, 169, 21, 191, 538, kSequencePointKind_StepOut, 0, 566 },
	{ 108495, 2, 169, 169, 21, 191, 547, kSequencePointKind_StepOut, 0, 567 },
	{ 108495, 2, 169, 169, 21, 191, 554, kSequencePointKind_StepOut, 0, 568 },
	{ 108495, 2, 169, 169, 21, 191, 563, kSequencePointKind_StepOut, 0, 569 },
	{ 108495, 2, 172, 172, 21, 153, 577, kSequencePointKind_Normal, 0, 570 },
	{ 108495, 2, 172, 172, 21, 153, 585, kSequencePointKind_StepOut, 0, 571 },
	{ 108495, 2, 172, 172, 21, 153, 592, kSequencePointKind_StepOut, 0, 572 },
	{ 108495, 2, 172, 172, 21, 153, 598, kSequencePointKind_StepOut, 0, 573 },
	{ 108495, 2, 173, 173, 21, 39, 605, kSequencePointKind_Normal, 0, 574 },
	{ 108495, 2, 173, 173, 21, 39, 608, kSequencePointKind_StepOut, 0, 575 },
	{ 108495, 2, 173, 173, 0, 0, 615, kSequencePointKind_Normal, 0, 576 },
	{ 108495, 2, 174, 174, 21, 22, 619, kSequencePointKind_Normal, 0, 577 },
	{ 108495, 2, 175, 175, 25, 74, 620, kSequencePointKind_Normal, 0, 578 },
	{ 108495, 2, 176, 176, 25, 75, 638, kSequencePointKind_Normal, 0, 579 },
	{ 108495, 2, 177, 177, 21, 22, 656, kSequencePointKind_Normal, 0, 580 },
	{ 108495, 2, 177, 177, 0, 0, 657, kSequencePointKind_Normal, 0, 581 },
	{ 108495, 2, 181, 181, 26, 127, 659, kSequencePointKind_Normal, 0, 582 },
	{ 108495, 2, 181, 181, 26, 127, 661, kSequencePointKind_StepOut, 0, 583 },
	{ 108495, 2, 181, 181, 26, 127, 671, kSequencePointKind_StepOut, 0, 584 },
	{ 108495, 2, 181, 181, 0, 0, 684, kSequencePointKind_Normal, 0, 585 },
	{ 108495, 2, 182, 182, 21, 22, 688, kSequencePointKind_Normal, 0, 586 },
	{ 108495, 2, 183, 183, 25, 73, 689, kSequencePointKind_Normal, 0, 587 },
	{ 108495, 2, 184, 184, 25, 73, 706, kSequencePointKind_Normal, 0, 588 },
	{ 108495, 2, 185, 185, 21, 22, 723, kSequencePointKind_Normal, 0, 589 },
	{ 108495, 2, 188, 188, 21, 155, 724, kSequencePointKind_Normal, 0, 590 },
	{ 108495, 2, 188, 188, 21, 155, 732, kSequencePointKind_StepOut, 0, 591 },
	{ 108495, 2, 188, 188, 21, 155, 739, kSequencePointKind_StepOut, 0, 592 },
	{ 108495, 2, 188, 188, 21, 155, 745, kSequencePointKind_StepOut, 0, 593 },
	{ 108495, 2, 189, 189, 21, 39, 752, kSequencePointKind_Normal, 0, 594 },
	{ 108495, 2, 189, 189, 21, 39, 755, kSequencePointKind_StepOut, 0, 595 },
	{ 108495, 2, 189, 189, 0, 0, 762, kSequencePointKind_Normal, 0, 596 },
	{ 108495, 2, 190, 190, 21, 22, 766, kSequencePointKind_Normal, 0, 597 },
	{ 108495, 2, 191, 191, 25, 74, 767, kSequencePointKind_Normal, 0, 598 },
	{ 108495, 2, 192, 192, 25, 75, 785, kSequencePointKind_Normal, 0, 599 },
	{ 108495, 2, 193, 193, 21, 22, 803, kSequencePointKind_Normal, 0, 600 },
	{ 108495, 2, 193, 193, 0, 0, 804, kSequencePointKind_Normal, 0, 601 },
	{ 108495, 2, 197, 197, 26, 127, 806, kSequencePointKind_Normal, 0, 602 },
	{ 108495, 2, 197, 197, 26, 127, 808, kSequencePointKind_StepOut, 0, 603 },
	{ 108495, 2, 197, 197, 26, 127, 818, kSequencePointKind_StepOut, 0, 604 },
	{ 108495, 2, 197, 197, 0, 0, 831, kSequencePointKind_Normal, 0, 605 },
	{ 108495, 2, 198, 198, 21, 22, 835, kSequencePointKind_Normal, 0, 606 },
	{ 108495, 2, 199, 199, 25, 73, 836, kSequencePointKind_Normal, 0, 607 },
	{ 108495, 2, 200, 200, 25, 73, 853, kSequencePointKind_Normal, 0, 608 },
	{ 108495, 2, 201, 201, 21, 22, 870, kSequencePointKind_Normal, 0, 609 },
	{ 108495, 2, 203, 203, 17, 18, 871, kSequencePointKind_Normal, 0, 610 },
	{ 108495, 2, 203, 203, 0, 0, 872, kSequencePointKind_Normal, 0, 611 },
	{ 108495, 2, 108, 108, 37, 39, 878, kSequencePointKind_Normal, 0, 612 },
	{ 108495, 2, 204, 204, 13, 14, 889, kSequencePointKind_Normal, 0, 613 },
	{ 108495, 2, 207, 207, 18, 34, 890, kSequencePointKind_Normal, 0, 614 },
	{ 108495, 2, 207, 207, 0, 0, 893, kSequencePointKind_Normal, 0, 615 },
	{ 108495, 2, 208, 208, 17, 62, 895, kSequencePointKind_Normal, 0, 616 },
	{ 108495, 2, 208, 208, 17, 62, 909, kSequencePointKind_StepOut, 0, 617 },
	{ 108495, 2, 207, 207, 68, 78, 915, kSequencePointKind_Normal, 0, 618 },
	{ 108495, 2, 207, 207, 36, 66, 921, kSequencePointKind_Normal, 0, 619 },
	{ 108495, 2, 207, 207, 0, 0, 934, kSequencePointKind_Normal, 0, 620 },
	{ 108495, 2, 210, 210, 13, 33, 938, kSequencePointKind_Normal, 0, 621 },
	{ 108495, 2, 211, 211, 9, 10, 944, kSequencePointKind_Normal, 0, 622 },
	{ 108496, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 623 },
	{ 108496, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 624 },
	{ 108496, 2, 218, 218, 9, 10, 0, kSequencePointKind_Normal, 0, 625 },
	{ 108496, 2, 220, 220, 13, 69, 1, kSequencePointKind_Normal, 0, 626 },
	{ 108496, 2, 221, 221, 13, 56, 7, kSequencePointKind_Normal, 0, 627 },
	{ 108496, 2, 223, 223, 13, 36, 13, kSequencePointKind_Normal, 0, 628 },
	{ 108496, 2, 223, 223, 0, 0, 15, kSequencePointKind_Normal, 0, 629 },
	{ 108496, 2, 224, 224, 13, 14, 18, kSequencePointKind_Normal, 0, 630 },
	{ 108496, 2, 225, 225, 17, 25, 19, kSequencePointKind_Normal, 0, 631 },
	{ 108496, 2, 225, 225, 17, 25, 20, kSequencePointKind_StepOut, 0, 632 },
	{ 108496, 2, 225, 225, 0, 0, 26, kSequencePointKind_Normal, 0, 633 },
	{ 108496, 2, 226, 226, 17, 18, 29, kSequencePointKind_Normal, 0, 634 },
	{ 108496, 2, 227, 227, 21, 45, 30, kSequencePointKind_Normal, 0, 635 },
	{ 108496, 2, 228, 228, 21, 66, 42, kSequencePointKind_Normal, 0, 636 },
	{ 108496, 2, 228, 228, 21, 66, 58, kSequencePointKind_StepOut, 0, 637 },
	{ 108496, 2, 229, 229, 17, 18, 64, kSequencePointKind_Normal, 0, 638 },
	{ 108496, 2, 230, 230, 13, 14, 65, kSequencePointKind_Normal, 0, 639 },
	{ 108496, 2, 230, 230, 0, 0, 66, kSequencePointKind_Normal, 0, 640 },
	{ 108496, 2, 231, 231, 18, 36, 71, kSequencePointKind_Normal, 0, 641 },
	{ 108496, 2, 231, 231, 0, 0, 77, kSequencePointKind_Normal, 0, 642 },
	{ 108496, 2, 232, 232, 13, 14, 81, kSequencePointKind_Normal, 0, 643 },
	{ 108496, 2, 233, 233, 17, 39, 82, kSequencePointKind_Normal, 0, 644 },
	{ 108496, 2, 233, 233, 17, 39, 93, kSequencePointKind_StepOut, 0, 645 },
	{ 108496, 2, 233, 233, 0, 0, 100, kSequencePointKind_Normal, 0, 646 },
	{ 108496, 2, 234, 234, 17, 18, 104, kSequencePointKind_Normal, 0, 647 },
	{ 108496, 2, 236, 236, 21, 65, 105, kSequencePointKind_Normal, 0, 648 },
	{ 108496, 2, 236, 236, 21, 65, 117, kSequencePointKind_StepOut, 0, 649 },
	{ 108496, 2, 236, 236, 0, 0, 124, kSequencePointKind_Normal, 0, 650 },
	{ 108496, 2, 237, 237, 25, 76, 128, kSequencePointKind_Normal, 0, 651 },
	{ 108496, 2, 237, 237, 25, 76, 144, kSequencePointKind_StepOut, 0, 652 },
	{ 108496, 2, 240, 240, 21, 64, 150, kSequencePointKind_Normal, 0, 653 },
	{ 108496, 2, 240, 240, 21, 64, 166, kSequencePointKind_StepOut, 0, 654 },
	{ 108496, 2, 241, 241, 21, 55, 172, kSequencePointKind_Normal, 0, 655 },
	{ 108496, 2, 242, 242, 17, 18, 189, kSequencePointKind_Normal, 0, 656 },
	{ 108496, 2, 243, 243, 13, 14, 190, kSequencePointKind_Normal, 0, 657 },
	{ 108496, 2, 243, 243, 0, 0, 191, kSequencePointKind_Normal, 0, 658 },
	{ 108496, 2, 244, 244, 18, 40, 193, kSequencePointKind_Normal, 0, 659 },
	{ 108496, 2, 244, 244, 18, 40, 204, kSequencePointKind_StepOut, 0, 660 },
	{ 108496, 2, 244, 244, 0, 0, 211, kSequencePointKind_Normal, 0, 661 },
	{ 108496, 2, 245, 245, 13, 14, 215, kSequencePointKind_Normal, 0, 662 },
	{ 108496, 2, 246, 246, 17, 62, 216, kSequencePointKind_Normal, 0, 663 },
	{ 108496, 2, 246, 246, 17, 62, 232, kSequencePointKind_StepOut, 0, 664 },
	{ 108496, 2, 247, 247, 13, 14, 238, kSequencePointKind_Normal, 0, 665 },
	{ 108496, 2, 251, 251, 13, 52, 239, kSequencePointKind_Normal, 0, 666 },
	{ 108496, 2, 251, 251, 13, 52, 251, kSequencePointKind_StepOut, 0, 667 },
	{ 108496, 2, 251, 251, 0, 0, 258, kSequencePointKind_Normal, 0, 668 },
	{ 108496, 2, 252, 252, 13, 14, 262, kSequencePointKind_Normal, 0, 669 },
	{ 108496, 2, 253, 253, 17, 25, 263, kSequencePointKind_Normal, 0, 670 },
	{ 108496, 2, 253, 253, 17, 25, 264, kSequencePointKind_StepOut, 0, 671 },
	{ 108496, 2, 253, 253, 0, 0, 271, kSequencePointKind_Normal, 0, 672 },
	{ 108496, 2, 254, 254, 21, 52, 275, kSequencePointKind_Normal, 0, 673 },
	{ 108496, 2, 254, 254, 21, 52, 282, kSequencePointKind_StepOut, 0, 674 },
	{ 108496, 2, 255, 255, 13, 14, 288, kSequencePointKind_Normal, 0, 675 },
	{ 108496, 2, 255, 255, 0, 0, 289, kSequencePointKind_Normal, 0, 676 },
	{ 108496, 2, 257, 257, 13, 14, 291, kSequencePointKind_Normal, 0, 677 },
	{ 108496, 2, 258, 258, 17, 34, 292, kSequencePointKind_Normal, 0, 678 },
	{ 108496, 2, 258, 258, 17, 34, 303, kSequencePointKind_StepOut, 0, 679 },
	{ 108496, 2, 258, 258, 0, 0, 310, kSequencePointKind_Normal, 0, 680 },
	{ 108496, 2, 259, 259, 17, 18, 314, kSequencePointKind_Normal, 0, 681 },
	{ 108496, 2, 260, 260, 21, 61, 315, kSequencePointKind_Normal, 0, 682 },
	{ 108496, 2, 260, 260, 21, 61, 331, kSequencePointKind_StepOut, 0, 683 },
	{ 108496, 2, 261, 261, 17, 18, 337, kSequencePointKind_Normal, 0, 684 },
	{ 108496, 2, 263, 263, 17, 25, 338, kSequencePointKind_Normal, 0, 685 },
	{ 108496, 2, 263, 263, 17, 25, 339, kSequencePointKind_StepOut, 0, 686 },
	{ 108496, 2, 263, 263, 0, 0, 346, kSequencePointKind_Normal, 0, 687 },
	{ 108496, 2, 264, 264, 17, 18, 350, kSequencePointKind_Normal, 0, 688 },
	{ 108496, 2, 265, 265, 21, 53, 351, kSequencePointKind_Normal, 0, 689 },
	{ 108496, 2, 265, 265, 21, 53, 358, kSequencePointKind_StepOut, 0, 690 },
	{ 108496, 2, 266, 266, 21, 52, 364, kSequencePointKind_Normal, 0, 691 },
	{ 108496, 2, 266, 266, 21, 52, 371, kSequencePointKind_StepOut, 0, 692 },
	{ 108496, 2, 267, 267, 17, 18, 377, kSequencePointKind_Normal, 0, 693 },
	{ 108496, 2, 268, 268, 13, 14, 378, kSequencePointKind_Normal, 0, 694 },
	{ 108496, 2, 269, 269, 13, 32, 379, kSequencePointKind_Normal, 0, 695 },
	{ 108496, 2, 270, 270, 9, 10, 391, kSequencePointKind_Normal, 0, 696 },
	{ 108498, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 697 },
	{ 108498, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 698 },
	{ 108498, 2, 36, 36, 9, 49, 0, kSequencePointKind_Normal, 0, 699 },
	{ 108498, 2, 38, 38, 9, 95, 6, kSequencePointKind_Normal, 0, 700 },
	{ 108498, 2, 39, 39, 9, 100, 17, kSequencePointKind_Normal, 0, 701 },
	{ 108498, 2, 40, 40, 9, 98, 28, kSequencePointKind_Normal, 0, 702 },
	{ 108499, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 703 },
	{ 108499, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 704 },
	{ 108499, 2, 18, 18, 13, 14, 0, kSequencePointKind_Normal, 0, 705 },
	{ 108499, 2, 19, 19, 17, 88, 1, kSequencePointKind_Normal, 0, 706 },
	{ 108499, 2, 19, 19, 17, 88, 10, kSequencePointKind_StepOut, 0, 707 },
	{ 108499, 2, 20, 20, 13, 14, 16, kSequencePointKind_Normal, 0, 708 },
	{ 108500, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 709 },
	{ 108500, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 710 },
	{ 108500, 2, 23, 23, 13, 14, 0, kSequencePointKind_Normal, 0, 711 },
	{ 108500, 2, 24, 24, 17, 71, 1, kSequencePointKind_Normal, 0, 712 },
	{ 108500, 2, 24, 24, 17, 71, 8, kSequencePointKind_StepOut, 0, 713 },
	{ 108500, 2, 24, 24, 17, 71, 22, kSequencePointKind_StepOut, 0, 714 },
	{ 108500, 2, 25, 25, 13, 14, 33, kSequencePointKind_Normal, 0, 715 },
	{ 108501, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 716 },
	{ 108501, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 717 },
	{ 108501, 2, 28, 28, 13, 14, 0, kSequencePointKind_Normal, 0, 718 },
	{ 108501, 2, 29, 29, 17, 77, 1, kSequencePointKind_Normal, 0, 719 },
	{ 108501, 2, 29, 29, 17, 77, 13, kSequencePointKind_StepOut, 0, 720 },
	{ 108501, 2, 29, 29, 17, 77, 32, kSequencePointKind_StepOut, 0, 721 },
	{ 108501, 2, 30, 30, 13, 14, 43, kSequencePointKind_Normal, 0, 722 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_InputLegacyModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_InputLegacyModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/InputLegacy/Input.bindings.cs", { 97, 62, 246, 130, 192, 78, 193, 170, 118, 230, 201, 187, 233, 213, 208, 131} },
{ "/Users/<USER>/build/output/unity/unity/Modules/InputLegacy/MouseEvents.cs", { 127, 57, 223, 71, 228, 1, 5, 71, 218, 159, 93, 209, 50, 201, 100, 254} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[9] = 
{
	{ 13904, 1 },
	{ 13909, 1 },
	{ 13910, 1 },
	{ 13911, 1 },
	{ 13914, 1 },
	{ 13915, 1 },
	{ 13917, 1 },
	{ 13920, 2 },
	{ 13918, 2 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[56] = 
{
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 29 },
	{ 0, 12 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 11 },
	{ 0, 11 },
	{ 0, 38 },
	{ 0, 16 },
	{ 0, 16 },
	{ 0, 16 },
	{ 0, 16 },
	{ 0, 16 },
	{ 0, 11 },
	{ 0, 33 },
	{ 0, 33 },
	{ 0, 38 },
	{ 0, 51 },
	{ 14, 43 },
	{ 0, 51 },
	{ 14, 43 },
	{ 0, 149 },
	{ 13, 68 },
	{ 0, 945 },
	{ 68, 109 },
	{ 143, 872 },
	{ 150, 872 },
	{ 237, 441 },
	{ 890, 938 },
	{ 0, 392 },
	{ 0, 35 },
	{ 0, 45 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[194] = 
{
	{ 12, 0, 1 },
	{ 0, 0, 0 },
	{ 12, 1, 1 },
	{ 0, 0, 0 },
	{ 12, 2, 1 },
	{ 0, 0, 0 },
	{ 12, 3, 1 },
	{ 0, 0, 0 },
	{ 12, 4, 1 },
	{ 0, 0, 0 },
	{ 12, 5, 1 },
	{ 0, 0, 0 },
	{ 12, 6, 1 },
	{ 0, 0, 0 },
	{ 12, 7, 1 },
	{ 0, 0, 0 },
	{ 12, 8, 1 },
	{ 0, 0, 0 },
	{ 12, 9, 1 },
	{ 0, 0, 0 },
	{ 12, 10, 1 },
	{ 0, 0, 0 },
	{ 12, 11, 1 },
	{ 0, 0, 0 },
	{ 12, 12, 1 },
	{ 0, 0, 0 },
	{ 12, 13, 1 },
	{ 0, 0, 0 },
	{ 29, 14, 1 },
	{ 12, 15, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 17, 16, 1 },
	{ 17, 17, 1 },
	{ 17, 18, 1 },
	{ 17, 19, 1 },
	{ 17, 20, 1 },
	{ 17, 21, 1 },
	{ 0, 0, 0 },
	{ 17, 22, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 12, 23, 1 },
	{ 12, 24, 1 },
	{ 12, 25, 1 },
	{ 12, 26, 1 },
	{ 12, 27, 1 },
	{ 12, 28, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 11, 29, 1 },
	{ 11, 30, 1 },
	{ 38, 31, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 16, 32, 1 },
	{ 16, 33, 1 },
	{ 16, 34, 1 },
	{ 16, 35, 1 },
	{ 16, 36, 1 },
	{ 11, 37, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 33, 38, 1 },
	{ 33, 39, 1 },
	{ 0, 0, 0 },
	{ 38, 40, 1 },
	{ 51, 41, 2 },
	{ 51, 43, 2 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 149, 45, 2 },
	{ 0, 0, 0 },
	{ 945, 47, 6 },
	{ 392, 53, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 35, 54, 1 },
	{ 45, 55, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_InputLegacyModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_InputLegacyModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	723,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_InputLegacyModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	9,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
