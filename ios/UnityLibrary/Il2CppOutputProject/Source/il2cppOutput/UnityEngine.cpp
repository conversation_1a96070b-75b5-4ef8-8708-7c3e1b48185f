﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DownloadHandlerMovieTexture_t047526B1C59FC7C031900A8F819FA88418DB90A0;
struct GUIElement_t374514E36250F54B88A85781BA9219071D387AEA;
struct GUILayer_t595003AC2E9DA25ABF516421FE9D104318070D31;
struct GUIText_t71364ECC27AB4CE947198862A571EB21188CE4A2;
struct GUITexture_tEEB256051FD933786F5D1495130DFF51B27E6067;
struct ObjectSelectorHandlerWithLabelsAttribute_t489C9D6F2A9E335D2C689A309C85C82A240427C9;
struct ObjectSelectorHandlerWithTagsAttribute_tDF94A41CD0C19B25BC6AE2B27B8D1CC6138D9F48;
struct ProceduralMaterial_t342168D8C6E4EA9A9B79CEC626B9CA7F4CCC2E62;
struct ProceduralPropertyDescription_tAA815D24DB124AC8A3EB18CDA93DC00BFCC20A5B;
struct ProceduralTexture_t7D31F136213A423DC9FBB67A703F06D235A1A067;
struct Remote_t44F4BAF0E1B8A05898B547CBFB12C077EC094024;

IL2CPP_EXTERN_C const RuntimeMethod* DownloadHandlerMovieTexture__ctor_mE72C0213C0CD127E537B92823E307E799D100122_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GUIElement__ctor_m88ABD449B0C9E43C02E2468D3224FF091AAF22BC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GUILayer__ctor_m95A0C08183E042AC0AAE44E6464325499E59E076_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GUIText__ctor_m52692E4C69D21D7A45A756E5FA2A8F4EE3C47B58_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GUITexture__ctor_m8FC3C626A502F5F47C5C49DCDB1FF464961654DB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObjectSelectorHandlerWithLabelsAttribute__ctor_m589EEE7FF139A804B82344A4A109CE1DC62B61E8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObjectSelectorHandlerWithTagsAttribute__ctor_mA63903FD6B9D5526AC467F63A7F3245D965014AC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralMaterial__ctor_m58E68BF840CABD1788F5DCD9EE2043C7EDA87801_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralPropertyDescription__ctor_mFBD57770AE3E9EEA492912402BC45B1B8DFE3942_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProceduralTexture__ctor_m347A5BCA76A988CF29E9966EC2481F56457DECF7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Remote__ctor_m0494F2604ECB04C8FFAE16324B17B8C41FC8AFF6_RuntimeMethod_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t853A105E2E1595E463CC860AFEE0FB13A177A12C 
{
};
struct DownloadHandlerMovieTexture_t047526B1C59FC7C031900A8F819FA88418DB90A0  : public RuntimeObject
{
};
struct GUIElement_t374514E36250F54B88A85781BA9219071D387AEA  : public RuntimeObject
{
};
struct GUILayer_t595003AC2E9DA25ABF516421FE9D104318070D31  : public RuntimeObject
{
};
struct GUIText_t71364ECC27AB4CE947198862A571EB21188CE4A2  : public RuntimeObject
{
};
struct GUITexture_tEEB256051FD933786F5D1495130DFF51B27E6067  : public RuntimeObject
{
};
struct ObjectSelectorHandlerWithLabelsAttribute_t489C9D6F2A9E335D2C689A309C85C82A240427C9  : public RuntimeObject
{
};
struct ObjectSelectorHandlerWithTagsAttribute_tDF94A41CD0C19B25BC6AE2B27B8D1CC6138D9F48  : public RuntimeObject
{
};
struct ProceduralMaterial_t342168D8C6E4EA9A9B79CEC626B9CA7F4CCC2E62  : public RuntimeObject
{
};
struct ProceduralPropertyDescription_tAA815D24DB124AC8A3EB18CDA93DC00BFCC20A5B  : public RuntimeObject
{
};
struct ProceduralTexture_t7D31F136213A423DC9FBB67A703F06D235A1A067  : public RuntimeObject
{
};
struct Remote_t44F4BAF0E1B8A05898B547CBFB12C077EC094024  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct IDataWatchHandle_tF3A810B5562D65518BC6FC9CC080299F28C7C6DA 
{
	union
	{
		struct
		{
		};
		uint8_t IDataWatchHandle_tF3A810B5562D65518BC6FC9CC080299F28C7C6DA__padding[1];
	};
};
struct IDataWatchService_t2F064C002CB709819393FE1F4641AA7017D9153C 
{
	union
	{
		struct
		{
		};
		uint8_t IDataWatchService_t2F064C002CB709819393FE1F4641AA7017D9153C__padding[1];
	};
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct BlendWeights_t0B279AA14C0B5D1D93DB7854BA5D64C6DE96180F 
{
	int32_t ___value__;
};
struct D3DHDRDisplayBitDepth_t689799DBB82D32960B072F13D3D86E5DBA30A98A 
{
	int32_t ___value__;
};
struct ProceduralCacheSize_t56112C23E0BC2001F0A98266E778746630E495A2 
{
	int32_t ___value__;
};
struct ProceduralLoadingBehavior_t468A761EC9934B8B55EC2A6CE185FCCF9A06BA8D 
{
	int32_t ___value__;
};
struct ProceduralOutputType_t67A1E7C658221189F9E12F4944CBDD257334943D 
{
	int32_t ___value__;
};
struct ProceduralProcessorUsage_tF4C16D5BDD854EE9F2A94A2B02019D7983DB8295 
{
	int32_t ___value__;
};
struct ProceduralPropertyType_t75D7F02ADEC0DBCC6D17DF592FD78849D108D0B0 
{
	int32_t ___value__;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GUIText__ctor_m52692E4C69D21D7A45A756E5FA2A8F4EE3C47B58 (GUIText_t71364ECC27AB4CE947198862A571EB21188CE4A2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUIText__ctor_m52692E4C69D21D7A45A756E5FA2A8F4EE3C47B58_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GUIText__ctor_m52692E4C69D21D7A45A756E5FA2A8F4EE3C47B58_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GUIElement__ctor_m88ABD449B0C9E43C02E2468D3224FF091AAF22BC (GUIElement_t374514E36250F54B88A85781BA9219071D387AEA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUIElement__ctor_m88ABD449B0C9E43C02E2468D3224FF091AAF22BC_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GUIElement__ctor_m88ABD449B0C9E43C02E2468D3224FF091AAF22BC_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GUILayer__ctor_m95A0C08183E042AC0AAE44E6464325499E59E076 (GUILayer_t595003AC2E9DA25ABF516421FE9D104318070D31* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUILayer__ctor_m95A0C08183E042AC0AAE44E6464325499E59E076_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GUILayer__ctor_m95A0C08183E042AC0AAE44E6464325499E59E076_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GUITexture__ctor_m8FC3C626A502F5F47C5C49DCDB1FF464961654DB (GUITexture_tEEB256051FD933786F5D1495130DFF51B27E6067* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUITexture__ctor_m8FC3C626A502F5F47C5C49DCDB1FF464961654DB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GUITexture__ctor_m8FC3C626A502F5F47C5C49DCDB1FF464961654DB_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralMaterial__ctor_m58E68BF840CABD1788F5DCD9EE2043C7EDA87801 (ProceduralMaterial_t342168D8C6E4EA9A9B79CEC626B9CA7F4CCC2E62* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralMaterial__ctor_m58E68BF840CABD1788F5DCD9EE2043C7EDA87801_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralMaterial__ctor_m58E68BF840CABD1788F5DCD9EE2043C7EDA87801_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralPropertyDescription__ctor_mFBD57770AE3E9EEA492912402BC45B1B8DFE3942 (ProceduralPropertyDescription_tAA815D24DB124AC8A3EB18CDA93DC00BFCC20A5B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralPropertyDescription__ctor_mFBD57770AE3E9EEA492912402BC45B1B8DFE3942_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralPropertyDescription__ctor_mFBD57770AE3E9EEA492912402BC45B1B8DFE3942_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProceduralTexture__ctor_m347A5BCA76A988CF29E9966EC2481F56457DECF7 (ProceduralTexture_t7D31F136213A423DC9FBB67A703F06D235A1A067* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProceduralTexture__ctor_m347A5BCA76A988CF29E9966EC2481F56457DECF7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ProceduralTexture__ctor_m347A5BCA76A988CF29E9966EC2481F56457DECF7_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DownloadHandlerMovieTexture__ctor_mE72C0213C0CD127E537B92823E307E799D100122 (DownloadHandlerMovieTexture_t047526B1C59FC7C031900A8F819FA88418DB90A0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DownloadHandlerMovieTexture__ctor_mE72C0213C0CD127E537B92823E307E799D100122_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DownloadHandlerMovieTexture__ctor_mE72C0213C0CD127E537B92823E307E799D100122_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Remote__ctor_m0494F2604ECB04C8FFAE16324B17B8C41FC8AFF6 (Remote_t44F4BAF0E1B8A05898B547CBFB12C077EC094024* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Remote__ctor_m0494F2604ECB04C8FFAE16324B17B8C41FC8AFF6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Remote__ctor_m0494F2604ECB04C8FFAE16324B17B8C41FC8AFF6_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectSelectorHandlerWithLabelsAttribute__ctor_m589EEE7FF139A804B82344A4A109CE1DC62B61E8 (ObjectSelectorHandlerWithLabelsAttribute_t489C9D6F2A9E335D2C689A309C85C82A240427C9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectSelectorHandlerWithLabelsAttribute__ctor_m589EEE7FF139A804B82344A4A109CE1DC62B61E8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ObjectSelectorHandlerWithLabelsAttribute__ctor_m589EEE7FF139A804B82344A4A109CE1DC62B61E8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectSelectorHandlerWithTagsAttribute__ctor_mA63903FD6B9D5526AC467F63A7F3245D965014AC (ObjectSelectorHandlerWithTagsAttribute_tDF94A41CD0C19B25BC6AE2B27B8D1CC6138D9F48* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectSelectorHandlerWithTagsAttribute__ctor_mA63903FD6B9D5526AC467F63A7F3245D965014AC_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, ObjectSelectorHandlerWithTagsAttribute__ctor_mA63903FD6B9D5526AC467F63A7F3245D965014AC_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
