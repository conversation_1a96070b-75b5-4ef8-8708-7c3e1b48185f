﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[5] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_UnityAnalyticsCommonModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UnityAnalyticsCommonModule[18] = 
{
	{ 110231, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 110231, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 110231, 1, 12, 12, 13, 14, 0, kSequencePointKind_Normal, 0, 2 },
	{ 110231, 1, 13, 13, 17, 52, 1, kSequencePointKind_Normal, 0, 3 },
	{ 110231, 1, 13, 13, 17, 52, 1, kSequencePointKind_StepOut, 0, 4 },
	{ 110231, 1, 14, 14, 13, 14, 9, kSequencePointKind_Normal, 0, 5 },
	{ 110232, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 110232, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 110232, 1, 16, 16, 13, 14, 0, kSequencePointKind_Normal, 0, 8 },
	{ 110232, 1, 17, 17, 17, 53, 1, kSequencePointKind_Normal, 0, 9 },
	{ 110232, 1, 17, 17, 17, 53, 2, kSequencePointKind_StepOut, 0, 10 },
	{ 110232, 1, 18, 18, 13, 14, 8, kSequencePointKind_Normal, 0, 11 },
	{ 110233, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 110233, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 110233, 1, 25, 25, 9, 10, 0, kSequencePointKind_Normal, 0, 14 },
	{ 110233, 1, 26, 26, 13, 58, 1, kSequencePointKind_Normal, 0, 15 },
	{ 110233, 1, 26, 26, 13, 58, 2, kSequencePointKind_StepOut, 0, 16 },
	{ 110233, 1, 27, 27, 9, 10, 8, kSequencePointKind_Normal, 0, 17 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_UnityAnalyticsCommonModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UnityAnalyticsCommonModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UnityAnalyticsCommon/Public/UnityAnalyticsCommon.cs", { 56, 6, 180, 61, 133, 122, 17, 79, 101, 15, 119, 27, 184, 154, 167, 106} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[2] = 
{
	{ 14198, 1 },
	{ 14199, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[1] = 
{
	{ 0, 11 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[5] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 11, 0, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityAnalyticsCommonModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityAnalyticsCommonModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	18,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_UnityAnalyticsCommonModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	2,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
