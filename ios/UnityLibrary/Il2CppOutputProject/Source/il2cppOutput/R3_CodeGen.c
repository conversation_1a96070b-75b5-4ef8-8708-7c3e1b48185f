﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_mCB73FD1AD565DF989815CFF8B0BE3F0F913AD646 (void);
extern void NullableAttribute__ctor_m0AAA19279C972F817DA3A1B02519021CDAD48492 (void);
extern void NullableAttribute__ctor_mACDA6F012D45812E54EC5A8D8849DD78EB26BEDD (void);
extern void NullableContextAttribute__ctor_m7F297CE322146FBC38EEBBE28C57D0286ED68E1F (void);
extern void RefSafetyRulesAttribute__ctor_mEA81559182344058FA22700DEB2EF9A94B8A7441 (void);
extern void StackTraceHiddenAttribute__ctor_mFE7272FA3D31B8CA3F6B88A595678814CE2C795F (void);
extern void MemberNotNullWhenAttribute__ctor_m38664EC6A1F5601991152B954B52B2D3AFB471B1 (void);
extern void CallerArgumentExpressionAttribute__ctor_mF68BAFDECEDFA18D7F970B3DE7107FA20FEDE1E0 (void);
extern void CompilerFeatureRequiredAttribute__ctor_mD46AE2C66912A8F96CF27E9F3E9405C88E02B659 (void);
extern void CancellationTokenExtensions_UnsafeRegister_m01AEF4726F9423C848CB225C506444B13FF46F82 (void);
extern void Disposable_CreateBuilder_mF038D4AFC333B095D3DE0AB5FFB6AF2B9925C7FD (void);
extern void Disposable__cctor_mA00C9B5C3807F231BC023E0E45F3B88D6AD2DC70 (void);
extern void EmptyDisposable_Dispose_mD1232FD91953A0CB30342250EA9D76B35877BED0 (void);
extern void EmptyDisposable__ctor_mB8685AAFEBB84DB3ACEA5C007318871C0B4EC683 (void);
extern void CombinedDisposable2__ctor_m62922E89CBBFC496C2671C0DD10F1F75B13FCA37 (void);
extern void CombinedDisposable2_Dispose_m141A25AD39E1E4180A9A563220CF0F47110FAF57 (void);
extern void CombinedDisposable3__ctor_m5F99F6C899B76108ED6C1EFA23F99479D8D6E1B3 (void);
extern void CombinedDisposable3_Dispose_m79F98A27F3CF622BFD46CDE300361611D5FDCA70 (void);
extern void CombinedDisposable4__ctor_m7C99113603F85126FB38EE3ACBD0CA2E434400CB (void);
extern void CombinedDisposable4_Dispose_m1ECC88E4CC0B48C2439BB7ED09CED7DBC7C6A91F (void);
extern void CombinedDisposable5__ctor_m59D741117CD2BDF81DDD65354E0172AE80AF793E (void);
extern void CombinedDisposable5_Dispose_m32B51D92C752405B2F508F459C953613B76FB3D2 (void);
extern void CombinedDisposable6__ctor_m774834F0F94C20F6A60FB2D2F4BEA1387295937F (void);
extern void CombinedDisposable6_Dispose_m68FC08632764F8F3B7E90929E4C4AD6CBC30F51A (void);
extern void CombinedDisposable7__ctor_m8351D80EB6288A931775206D97F102AB1F12C3D6 (void);
extern void CombinedDisposable7_Dispose_mD9C666ECA25EDF1B47B97F18A8D2B9342079CEA8 (void);
extern void CombinedDisposable8__ctor_m187BFE787801B5259053305AFEE95AAD6C1BEB9F (void);
extern void CombinedDisposable8_Dispose_mA2D7D0CF9A9AD88A2285E867449DDE8EAB34E086 (void);
extern void CombinedDisposable__ctor_m7369B086E969B79CB65B4316D1588F35703B4DB1 (void);
extern void CombinedDisposable_Dispose_m4A81B43FDDA5C277E7C067F914B58A0F8D3DDD57 (void);
extern void DisposableBuilder__ctor_m06E101D467085614C8DC75E4A54CFDA58E271B42 (void);
extern void DisposableBuilder_Add_m6E6F3197AA0437D265D6664DA125195DDCC7A9E4 (void);
extern void DisposableBuilder_AddToArray_m41F96B7CB8C423A338F7125BC453F2DD82BD5274 (void);
extern void DisposableBuilder_Build_m997C4680A0E0F7CD9491ACA8EB97C9EDE695F330 (void);
extern void DisposableBuilder_Dispose_m5422435D1D9F1569E745F880A4787ED74C5DFC5B (void);
extern void DisposableBag_Add_m5F147086642626EE8CD563016841842E6D15D9EB (void);
extern void DisposableBag_Clear_m758D5FE4A891090DA621F02BC1F0ACB09B2FD719 (void);
extern void DisposableBag_Dispose_m283A7E33148B414921582EE6FC0A8AC0C265BFF9 (void);
extern void Observable_Return_m35CA662D6AE9EA4F41E4790BB652FBE409B11FB0 (void);
extern void ReturnUnit__ctor_m69FB0798A8B39452BA3D0FED59C4003789F1C3A5 (void);
extern void ReturnUnit_SubscribeCore_mEB256F7727434CBE944322885CC71827D065C1B8 (void);
extern void ReturnUnit__cctor_mCBBBFD038A2B0BB2E63F2852351ABE977F512C87 (void);
extern void FrameProvider__ctor_mF0E923B850FDE2EEF1BFF65D30B087DE94D10620 (void);
extern void ObservableSystem_set_DefaultTimeProvider_m01295EB078F1B7ED7072E5A7DE463E86333A90D0 (void);
extern void ObservableSystem_get_DefaultFrameProvider_m569E9BA46A9B0D3AB48800B117C9D1BB03E2DD30 (void);
extern void ObservableSystem_set_DefaultFrameProvider_m343FAB01BFEAAD39B1FCF08BF76BD84143547A58 (void);
extern void ObservableSystem_RegisterUnhandledExceptionHandler_mF9E6EEBD9B127BFBF05548E4634F57DD652100C0 (void);
extern void ObservableSystem_GetUnhandledExceptionHandler_mABD5FA2181B4365ED37D6813295F874BFEC6A77F (void);
extern void ObservableSystem_DefaultUnhandledExceptionHandler_m4F9AC20DDA75AFACA2B14B7450FBAB8E64B29E2F (void);
extern void ObservableSystem__cctor_mA4901E003E0722D5314456FD7E1A02672A1C95B3 (void);
extern void NotSupportedFrameProvider_Register_mFBAAFCF623495450840106083B7F61E757686DD7 (void);
extern void NotSupportedFrameProvider__ctor_m97FF41A8BB69FC2858B983E2762CA7D8B90F9BC0 (void);
extern void ObservableTracker_TryTrackActiveSubscription_mF3477DD0714036477326DC1F75F41B5AF012CC75 (void);
extern void ObservableTracker_TryTrackActiveSubscriptionCore_mEAE4A5214C7E2A384985DA115BF8F4CEA8BF85FD (void);
extern void ObservableTracker_RemoveTracking_m04C42FF3DDF67610B26693FEA42EB6415D5DE398 (void);
extern void ObservableTracker_TypeBeautify_m9337BA60F7C0E096303FDE0E6280C0379200152B (void);
extern void ObservableTracker_UnwrapTrackableDisposable_m353A5EBE6CCECE12DA3AF71D64E272405D1FD659 (void);
extern void ObservableTracker__cctor_mD3DF7809A3CEB51A42A0844E01BF65F7E2B2A80A (void);
extern void TrackableDisposable__ctor_m1620EE818DB8E8F3B09BF4A8CC18092BE59D0B2E (void);
extern void TrackableDisposable_get_Disposable_m4E5E85C5524061A30F8D763C7CDC88DDBB126579 (void);
extern void TrackableDisposable_Dispose_mAC79119A8BAEABEB79DCABFF71F837C7E1F29A9C (void);
extern void TrackableDisposable_ToString_m7C25E3BCD07C2D830B3959B0FE09C8840F0E629C (void);
extern void TrackingState__ctor_m28E564D4FD7650F3310D5460901D643E34249D51 (void);
extern void TrackingState_get_TrackingId_m3CF3FDD1B6EB15FD4A0C5A6D40E13D0FA2F705BF (void);
extern void TrackingState_get_FormattedType_m1679E18DBE7D55D9DBBBCAE7B86766236BAF49C4 (void);
extern void TrackingState_get_AddTime_mD37A9A67D5EF5671A9921DE09178C9ADDFB72668 (void);
extern void TrackingState_get_StackTrace_m97A876990FD237353DF68810CF1B8649A146C798 (void);
extern void TrackingState_CompareTo_m1A91E59BC339E3922B8B0A12D3D523F48CEE85EE (void);
extern void TrackingState_ToString_m9E6698FA475B3C26275C218CBBCBC25912A050AF (void);
extern void TrackingState_PrintMembers_m0ED96A6E00832245B581F7A66A8768260F7396E4 (void);
extern void TrackingState_GetHashCode_m2DA5BB00C8EE6A8E27458FC0037C04B844AF3C5C (void);
extern void TrackingState_Equals_m58197FB2842DF73AD989CFF3C722F205934515B1 (void);
extern void TrackingState_Equals_m5C993BDD4DBEA7214FA29CF3A358EECDB802A962 (void);
extern void Result_get_Success_mA0BFC629F0A8FF858F2EE2D242898FE0E037DA0F (void);
extern void Result_Failure_m58D625F7229081E7D6C5D3ECE93852B81959F2C0 (void);
extern void Result_get_Exception_mC2B9CCE126F644085F69B3AFDB88861DC0E610EA (void);
extern void Result_get_IsSuccess_m22304D7C2B985646468642CC45FECCB11E85443A (void);
extern void Result_get_IsFailure_m35719122DF74F6FF4B610747FCFD1A1099B8FE3F (void);
extern void Result__ctor_m2539E323280CCB1B1F8BB80D109C6A715134027B (void);
extern void Result_ToString_mF52AC323574DEF31E40B679A35A593C03B2708F0 (void);
extern void SingleAssignmentDisposableCore_set_Disposable_m512607DE32E18CFB7D692A8EE8C1BFE7DD2BB510 (void);
extern void SingleAssignmentDisposableCore_Dispose_m8FFF38AD98886810D7F28EF2C248F3DFF37513F0 (void);
extern void SingleAssignmentDisposableCore_ThrowAlreadyAssignment_mCB30A12008DFA186136D49EEA2D9196CB073419E (void);
extern void DisposedSentinel__ctor_m70735AD98156101BFC80B4BCFB521321A76D0ABF (void);
extern void DisposedSentinel_Dispose_m3A6D60ECAFE48CC9479BF4950EAD91444C18B8A3 (void);
extern void DisposedSentinel__cctor_mF45526D3A063B5484ACF7512D7DC4494EBDA99D5 (void);
extern void CompleteState_TrySetResult_m30E9483D3A291E309772FC6079BE616AE42133A1 (void);
extern void CompleteState_TrySetDisposed_mA4386747BFE72DE1E8F34A14C9812AA68C8FF9E5 (void);
extern void CompleteState_get_IsCompleted_m0AE59ACE48A88DBD1EF921373D0266A0F2E8BA22 (void);
extern void CompleteState_get_IsCompletedOrDisposed_m0BEA1099B9478A84C974EF29EA95CBE8EE87939C (void);
extern void CompleteState_TryGetResult_mCFBD787981828AB951DB7E83FCDB4BC020C4E558 (void);
extern void CompleteState_GetException_mC54421D0E079B735677865E8528E743A9E8F2F92 (void);
extern void CompleteState_ThrowObjectDiposedException_m78FB2C6E88297B68EF96F74528C1640F0462C519 (void);
extern void Unit_Equals_mF2D7D0DFA5AF7F8B993B472B93199E121BD942B2 (void);
extern void Unit_Equals_m45E01A0D69281C98937B4C7DF48874C6495E7E01 (void);
extern void Unit_GetHashCode_m0C1ADDCC61630206A5F047A3F7D2BEE90BC12FBF (void);
extern void Unit_ToString_m5142695D75D00B5562BF63966A8F9B231DCC992C (void);
extern void Unit__cctor_mCEAE42B1C69B29E3525C551E884B36FB2568FED7 (void);
extern void FrameCountExtensions_NormalizeFrame_mEADD39BE8A5FCFFFFDDC5B2A79EC5F7443096FAC (void);
extern void Stubs__cctor_m4279BED35E323CC3765F63F0E28AF6FA2B9A012E (void);
extern void U3CU3Ec__cctor_mB7168E214ABDF68B46A2CE1559B5F64A11293B4C (void);
extern void U3CU3Ec__ctor_mFF935FB4458754FC0F484426FC06EC1730617ABC (void);
extern void U3CU3Ec_U3C_cctorU3Eb__1_0_m6089D1F28EFFC0290A0FEDDBE60AA02D13F449B8 (void);
extern void ThrowHelper_ThrowArgumentNullIfNull_m3E5FA52B051F87B439B55556A361E8AF68EA2F75 (void);
extern void ThrowHelper_ThrowObjectDisposedIf_m015CE442ACB5C1014F4425387F35BBE1EA21CAC1 (void);
extern void ThrowHelper_ThrowArgumentNullException_m9DA59342C701A8BC6197DDF92C4C486EBAB60A6C (void);
extern void ThrowHelper_ThrowObjectDisposedException_m693B03398F62A1858F07C38AB9001890777A7424 (void);
static Il2CppMethodPointer s_methodPointers[360] = 
{
	EmbeddedAttribute__ctor_mCB73FD1AD565DF989815CFF8B0BE3F0F913AD646,
	NullableAttribute__ctor_m0AAA19279C972F817DA3A1B02519021CDAD48492,
	NullableAttribute__ctor_mACDA6F012D45812E54EC5A8D8849DD78EB26BEDD,
	NullableContextAttribute__ctor_m7F297CE322146FBC38EEBBE28C57D0286ED68E1F,
	RefSafetyRulesAttribute__ctor_mEA81559182344058FA22700DEB2EF9A94B8A7441,
	StackTraceHiddenAttribute__ctor_mFE7272FA3D31B8CA3F6B88A595678814CE2C795F,
	MemberNotNullWhenAttribute__ctor_m38664EC6A1F5601991152B954B52B2D3AFB471B1,
	CallerArgumentExpressionAttribute__ctor_mF68BAFDECEDFA18D7F970B3DE7107FA20FEDE1E0,
	CompilerFeatureRequiredAttribute__ctor_mD46AE2C66912A8F96CF27E9F3E9405C88E02B659,
	CancellationTokenExtensions_UnsafeRegister_m01AEF4726F9423C848CB225C506444B13FF46F82,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Disposable_CreateBuilder_mF038D4AFC333B095D3DE0AB5FFB6AF2B9925C7FD,
	NULL,
	NULL,
	Disposable__cctor_mA00C9B5C3807F231BC023E0E45F3B88D6AD2DC70,
	EmptyDisposable_Dispose_mD1232FD91953A0CB30342250EA9D76B35877BED0,
	EmptyDisposable__ctor_mB8685AAFEBB84DB3ACEA5C007318871C0B4EC683,
	CombinedDisposable2__ctor_m62922E89CBBFC496C2671C0DD10F1F75B13FCA37,
	CombinedDisposable2_Dispose_m141A25AD39E1E4180A9A563220CF0F47110FAF57,
	CombinedDisposable3__ctor_m5F99F6C899B76108ED6C1EFA23F99479D8D6E1B3,
	CombinedDisposable3_Dispose_m79F98A27F3CF622BFD46CDE300361611D5FDCA70,
	CombinedDisposable4__ctor_m7C99113603F85126FB38EE3ACBD0CA2E434400CB,
	CombinedDisposable4_Dispose_m1ECC88E4CC0B48C2439BB7ED09CED7DBC7C6A91F,
	CombinedDisposable5__ctor_m59D741117CD2BDF81DDD65354E0172AE80AF793E,
	CombinedDisposable5_Dispose_m32B51D92C752405B2F508F459C953613B76FB3D2,
	CombinedDisposable6__ctor_m774834F0F94C20F6A60FB2D2F4BEA1387295937F,
	CombinedDisposable6_Dispose_m68FC08632764F8F3B7E90929E4C4AD6CBC30F51A,
	CombinedDisposable7__ctor_m8351D80EB6288A931775206D97F102AB1F12C3D6,
	CombinedDisposable7_Dispose_mD9C666ECA25EDF1B47B97F18A8D2B9342079CEA8,
	CombinedDisposable8__ctor_m187BFE787801B5259053305AFEE95AAD6C1BEB9F,
	CombinedDisposable8_Dispose_mA2D7D0CF9A9AD88A2285E867449DDE8EAB34E086,
	CombinedDisposable__ctor_m7369B086E969B79CB65B4316D1588F35703B4DB1,
	CombinedDisposable_Dispose_m4A81B43FDDA5C277E7C067F914B58A0F8D3DDD57,
	DisposableBuilder__ctor_m06E101D467085614C8DC75E4A54CFDA58E271B42,
	DisposableBuilder_Add_m6E6F3197AA0437D265D6664DA125195DDCC7A9E4,
	DisposableBuilder_AddToArray_m41F96B7CB8C423A338F7125BC453F2DD82BD5274,
	DisposableBuilder_Build_m997C4680A0E0F7CD9491ACA8EB97C9EDE695F330,
	DisposableBuilder_Dispose_m5422435D1D9F1569E745F880A4787ED74C5DFC5B,
	DisposableBag_Add_m5F147086642626EE8CD563016841842E6D15D9EB,
	DisposableBag_Clear_m758D5FE4A891090DA621F02BC1F0ACB09B2FD719,
	DisposableBag_Dispose_m283A7E33148B414921582EE6FC0A8AC0C265BFF9,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Observable_Return_m35CA662D6AE9EA4F41E4790BB652FBE409B11FB0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ReturnUnit__ctor_m69FB0798A8B39452BA3D0FED59C4003789F1C3A5,
	ReturnUnit_SubscribeCore_mEB256F7727434CBE944322885CC71827D065C1B8,
	ReturnUnit__cctor_mCBBBFD038A2B0BB2E63F2852351ABE977F512C87,
	NULL,
	FrameProvider__ctor_mF0E923B850FDE2EEF1BFF65D30B087DE94D10620,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ObservableSystem_set_DefaultTimeProvider_m01295EB078F1B7ED7072E5A7DE463E86333A90D0,
	ObservableSystem_get_DefaultFrameProvider_m569E9BA46A9B0D3AB48800B117C9D1BB03E2DD30,
	ObservableSystem_set_DefaultFrameProvider_m343FAB01BFEAAD39B1FCF08BF76BD84143547A58,
	ObservableSystem_RegisterUnhandledExceptionHandler_mF9E6EEBD9B127BFBF05548E4634F57DD652100C0,
	ObservableSystem_GetUnhandledExceptionHandler_mABD5FA2181B4365ED37D6813295F874BFEC6A77F,
	ObservableSystem_DefaultUnhandledExceptionHandler_m4F9AC20DDA75AFACA2B14B7450FBAB8E64B29E2F,
	ObservableSystem__cctor_mA4901E003E0722D5314456FD7E1A02672A1C95B3,
	NotSupportedFrameProvider_Register_mFBAAFCF623495450840106083B7F61E757686DD7,
	NotSupportedFrameProvider__ctor_m97FF41A8BB69FC2858B983E2762CA7D8B90F9BC0,
	ObservableTracker_TryTrackActiveSubscription_mF3477DD0714036477326DC1F75F41B5AF012CC75,
	ObservableTracker_TryTrackActiveSubscriptionCore_mEAE4A5214C7E2A384985DA115BF8F4CEA8BF85FD,
	ObservableTracker_RemoveTracking_m04C42FF3DDF67610B26693FEA42EB6415D5DE398,
	ObservableTracker_TypeBeautify_m9337BA60F7C0E096303FDE0E6280C0379200152B,
	ObservableTracker_UnwrapTrackableDisposable_m353A5EBE6CCECE12DA3AF71D64E272405D1FD659,
	ObservableTracker__cctor_mD3DF7809A3CEB51A42A0844E01BF65F7E2B2A80A,
	TrackableDisposable__ctor_m1620EE818DB8E8F3B09BF4A8CC18092BE59D0B2E,
	TrackableDisposable_get_Disposable_m4E5E85C5524061A30F8D763C7CDC88DDBB126579,
	TrackableDisposable_Dispose_mAC79119A8BAEABEB79DCABFF71F837C7E1F29A9C,
	TrackableDisposable_ToString_m7C25E3BCD07C2D830B3959B0FE09C8840F0E629C,
	TrackingState__ctor_m28E564D4FD7650F3310D5460901D643E34249D51,
	TrackingState_get_TrackingId_m3CF3FDD1B6EB15FD4A0C5A6D40E13D0FA2F705BF,
	TrackingState_get_FormattedType_m1679E18DBE7D55D9DBBBCAE7B86766236BAF49C4,
	TrackingState_get_AddTime_mD37A9A67D5EF5671A9921DE09178C9ADDFB72668,
	TrackingState_get_StackTrace_m97A876990FD237353DF68810CF1B8649A146C798,
	TrackingState_CompareTo_m1A91E59BC339E3922B8B0A12D3D523F48CEE85EE,
	TrackingState_ToString_m9E6698FA475B3C26275C218CBBCBC25912A050AF,
	TrackingState_PrintMembers_m0ED96A6E00832245B581F7A66A8768260F7396E4,
	TrackingState_GetHashCode_m2DA5BB00C8EE6A8E27458FC0037C04B844AF3C5C,
	TrackingState_Equals_m58197FB2842DF73AD989CFF3C722F205934515B1,
	TrackingState_Equals_m5C993BDD4DBEA7214FA29CF3A358EECDB802A962,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Result_get_Success_mA0BFC629F0A8FF858F2EE2D242898FE0E037DA0F,
	Result_Failure_m58D625F7229081E7D6C5D3ECE93852B81959F2C0,
	Result_get_Exception_mC2B9CCE126F644085F69B3AFDB88861DC0E610EA,
	Result_get_IsSuccess_m22304D7C2B985646468642CC45FECCB11E85443A,
	Result_get_IsFailure_m35719122DF74F6FF4B610747FCFD1A1099B8FE3F,
	Result__ctor_m2539E323280CCB1B1F8BB80D109C6A715134027B,
	Result_ToString_mF52AC323574DEF31E40B679A35A593C03B2708F0,
	SingleAssignmentDisposableCore_set_Disposable_m512607DE32E18CFB7D692A8EE8C1BFE7DD2BB510,
	SingleAssignmentDisposableCore_Dispose_m8FFF38AD98886810D7F28EF2C248F3DFF37513F0,
	SingleAssignmentDisposableCore_ThrowAlreadyAssignment_mCB30A12008DFA186136D49EEA2D9196CB073419E,
	DisposedSentinel__ctor_m70735AD98156101BFC80B4BCFB521321A76D0ABF,
	DisposedSentinel_Dispose_m3A6D60ECAFE48CC9479BF4950EAD91444C18B8A3,
	DisposedSentinel__cctor_mF45526D3A063B5484ACF7512D7DC4494EBDA99D5,
	CompleteState_TrySetResult_m30E9483D3A291E309772FC6079BE616AE42133A1,
	CompleteState_TrySetDisposed_mA4386747BFE72DE1E8F34A14C9812AA68C8FF9E5,
	CompleteState_get_IsCompleted_m0AE59ACE48A88DBD1EF921373D0266A0F2E8BA22,
	CompleteState_get_IsCompletedOrDisposed_m0BEA1099B9478A84C974EF29EA95CBE8EE87939C,
	CompleteState_TryGetResult_mCFBD787981828AB951DB7E83FCDB4BC020C4E558,
	CompleteState_GetException_mC54421D0E079B735677865E8528E743A9E8F2F92,
	CompleteState_ThrowObjectDiposedException_m78FB2C6E88297B68EF96F74528C1640F0462C519,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Unit_Equals_mF2D7D0DFA5AF7F8B993B472B93199E121BD942B2,
	Unit_Equals_m45E01A0D69281C98937B4C7DF48874C6495E7E01,
	Unit_GetHashCode_m0C1ADDCC61630206A5F047A3F7D2BEE90BC12FBF,
	Unit_ToString_m5142695D75D00B5562BF63966A8F9B231DCC992C,
	Unit__cctor_mCEAE42B1C69B29E3525C551E884B36FB2568FED7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FrameCountExtensions_NormalizeFrame_mEADD39BE8A5FCFFFFDDC5B2A79EC5F7443096FAC,
	Stubs__cctor_m4279BED35E323CC3765F63F0E28AF6FA2B9A012E,
	U3CU3Ec__cctor_mB7168E214ABDF68B46A2CE1559B5F64A11293B4C,
	U3CU3Ec__ctor_mFF935FB4458754FC0F484426FC06EC1730617ABC,
	U3CU3Ec_U3C_cctorU3Eb__1_0_m6089D1F28EFFC0290A0FEDDBE60AA02D13F449B8,
	ThrowHelper_ThrowArgumentNullIfNull_m3E5FA52B051F87B439B55556A361E8AF68EA2F75,
	ThrowHelper_ThrowObjectDisposedIf_m015CE442ACB5C1014F4425387F35BBE1EA21CAC1,
	ThrowHelper_ThrowArgumentNullException_m9DA59342C701A8BC6197DDF92C4C486EBAB60A6C,
	ThrowHelper_ThrowObjectDisposedException_m693B03398F62A1858F07C38AB9001890777A7424,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
extern void DisposableBag_Add_m5F147086642626EE8CD563016841842E6D15D9EB_AdjustorThunk (void);
extern void DisposableBag_Clear_m758D5FE4A891090DA621F02BC1F0ACB09B2FD719_AdjustorThunk (void);
extern void DisposableBag_Dispose_m283A7E33148B414921582EE6FC0A8AC0C265BFF9_AdjustorThunk (void);
extern void TrackingState__ctor_m28E564D4FD7650F3310D5460901D643E34249D51_AdjustorThunk (void);
extern void TrackingState_get_TrackingId_m3CF3FDD1B6EB15FD4A0C5A6D40E13D0FA2F705BF_AdjustorThunk (void);
extern void TrackingState_get_FormattedType_m1679E18DBE7D55D9DBBBCAE7B86766236BAF49C4_AdjustorThunk (void);
extern void TrackingState_get_AddTime_mD37A9A67D5EF5671A9921DE09178C9ADDFB72668_AdjustorThunk (void);
extern void TrackingState_get_StackTrace_m97A876990FD237353DF68810CF1B8649A146C798_AdjustorThunk (void);
extern void TrackingState_CompareTo_m1A91E59BC339E3922B8B0A12D3D523F48CEE85EE_AdjustorThunk (void);
extern void TrackingState_ToString_m9E6698FA475B3C26275C218CBBCBC25912A050AF_AdjustorThunk (void);
extern void TrackingState_PrintMembers_m0ED96A6E00832245B581F7A66A8768260F7396E4_AdjustorThunk (void);
extern void TrackingState_GetHashCode_m2DA5BB00C8EE6A8E27458FC0037C04B844AF3C5C_AdjustorThunk (void);
extern void TrackingState_Equals_m58197FB2842DF73AD989CFF3C722F205934515B1_AdjustorThunk (void);
extern void TrackingState_Equals_m5C993BDD4DBEA7214FA29CF3A358EECDB802A962_AdjustorThunk (void);
extern void Result_get_Exception_mC2B9CCE126F644085F69B3AFDB88861DC0E610EA_AdjustorThunk (void);
extern void Result_get_IsSuccess_m22304D7C2B985646468642CC45FECCB11E85443A_AdjustorThunk (void);
extern void Result_get_IsFailure_m35719122DF74F6FF4B610747FCFD1A1099B8FE3F_AdjustorThunk (void);
extern void Result__ctor_m2539E323280CCB1B1F8BB80D109C6A715134027B_AdjustorThunk (void);
extern void Result_ToString_mF52AC323574DEF31E40B679A35A593C03B2708F0_AdjustorThunk (void);
extern void SingleAssignmentDisposableCore_set_Disposable_m512607DE32E18CFB7D692A8EE8C1BFE7DD2BB510_AdjustorThunk (void);
extern void SingleAssignmentDisposableCore_Dispose_m8FFF38AD98886810D7F28EF2C248F3DFF37513F0_AdjustorThunk (void);
extern void CompleteState_TrySetResult_m30E9483D3A291E309772FC6079BE616AE42133A1_AdjustorThunk (void);
extern void CompleteState_TrySetDisposed_mA4386747BFE72DE1E8F34A14C9812AA68C8FF9E5_AdjustorThunk (void);
extern void CompleteState_get_IsCompleted_m0AE59ACE48A88DBD1EF921373D0266A0F2E8BA22_AdjustorThunk (void);
extern void CompleteState_get_IsCompletedOrDisposed_m0BEA1099B9478A84C974EF29EA95CBE8EE87939C_AdjustorThunk (void);
extern void CompleteState_TryGetResult_mCFBD787981828AB951DB7E83FCDB4BC020C4E558_AdjustorThunk (void);
extern void CompleteState_GetException_mC54421D0E079B735677865E8528E743A9E8F2F92_AdjustorThunk (void);
extern void Unit_Equals_mF2D7D0DFA5AF7F8B993B472B93199E121BD942B2_AdjustorThunk (void);
extern void Unit_Equals_m45E01A0D69281C98937B4C7DF48874C6495E7E01_AdjustorThunk (void);
extern void Unit_GetHashCode_m0C1ADDCC61630206A5F047A3F7D2BEE90BC12FBF_AdjustorThunk (void);
extern void Unit_ToString_m5142695D75D00B5562BF63966A8F9B231DCC992C_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[31] = 
{
	{ 0x06000038, DisposableBag_Add_m5F147086642626EE8CD563016841842E6D15D9EB_AdjustorThunk },
	{ 0x06000039, DisposableBag_Clear_m758D5FE4A891090DA621F02BC1F0ACB09B2FD719_AdjustorThunk },
	{ 0x0600003A, DisposableBag_Dispose_m283A7E33148B414921582EE6FC0A8AC0C265BFF9_AdjustorThunk },
	{ 0x060000A5, TrackingState__ctor_m28E564D4FD7650F3310D5460901D643E34249D51_AdjustorThunk },
	{ 0x060000A6, TrackingState_get_TrackingId_m3CF3FDD1B6EB15FD4A0C5A6D40E13D0FA2F705BF_AdjustorThunk },
	{ 0x060000A7, TrackingState_get_FormattedType_m1679E18DBE7D55D9DBBBCAE7B86766236BAF49C4_AdjustorThunk },
	{ 0x060000A8, TrackingState_get_AddTime_mD37A9A67D5EF5671A9921DE09178C9ADDFB72668_AdjustorThunk },
	{ 0x060000A9, TrackingState_get_StackTrace_m97A876990FD237353DF68810CF1B8649A146C798_AdjustorThunk },
	{ 0x060000AA, TrackingState_CompareTo_m1A91E59BC339E3922B8B0A12D3D523F48CEE85EE_AdjustorThunk },
	{ 0x060000AB, TrackingState_ToString_m9E6698FA475B3C26275C218CBBCBC25912A050AF_AdjustorThunk },
	{ 0x060000AC, TrackingState_PrintMembers_m0ED96A6E00832245B581F7A66A8768260F7396E4_AdjustorThunk },
	{ 0x060000AD, TrackingState_GetHashCode_m2DA5BB00C8EE6A8E27458FC0037C04B844AF3C5C_AdjustorThunk },
	{ 0x060000AE, TrackingState_Equals_m58197FB2842DF73AD989CFF3C722F205934515B1_AdjustorThunk },
	{ 0x060000AF, TrackingState_Equals_m5C993BDD4DBEA7214FA29CF3A358EECDB802A962_AdjustorThunk },
	{ 0x0600011D, Result_get_Exception_mC2B9CCE126F644085F69B3AFDB88861DC0E610EA_AdjustorThunk },
	{ 0x0600011E, Result_get_IsSuccess_m22304D7C2B985646468642CC45FECCB11E85443A_AdjustorThunk },
	{ 0x0600011F, Result_get_IsFailure_m35719122DF74F6FF4B610747FCFD1A1099B8FE3F_AdjustorThunk },
	{ 0x06000120, Result__ctor_m2539E323280CCB1B1F8BB80D109C6A715134027B_AdjustorThunk },
	{ 0x06000121, Result_ToString_mF52AC323574DEF31E40B679A35A593C03B2708F0_AdjustorThunk },
	{ 0x06000122, SingleAssignmentDisposableCore_set_Disposable_m512607DE32E18CFB7D692A8EE8C1BFE7DD2BB510_AdjustorThunk },
	{ 0x06000123, SingleAssignmentDisposableCore_Dispose_m8FFF38AD98886810D7F28EF2C248F3DFF37513F0_AdjustorThunk },
	{ 0x06000128, CompleteState_TrySetResult_m30E9483D3A291E309772FC6079BE616AE42133A1_AdjustorThunk },
	{ 0x06000129, CompleteState_TrySetDisposed_mA4386747BFE72DE1E8F34A14C9812AA68C8FF9E5_AdjustorThunk },
	{ 0x0600012A, CompleteState_get_IsCompleted_m0AE59ACE48A88DBD1EF921373D0266A0F2E8BA22_AdjustorThunk },
	{ 0x0600012B, CompleteState_get_IsCompletedOrDisposed_m0BEA1099B9478A84C974EF29EA95CBE8EE87939C_AdjustorThunk },
	{ 0x0600012C, CompleteState_TryGetResult_mCFBD787981828AB951DB7E83FCDB4BC020C4E558_AdjustorThunk },
	{ 0x0600012D, CompleteState_GetException_mC54421D0E079B735677865E8528E743A9E8F2F92_AdjustorThunk },
	{ 0x0600013E, Unit_Equals_mF2D7D0DFA5AF7F8B993B472B93199E121BD942B2_AdjustorThunk },
	{ 0x0600013F, Unit_Equals_m45E01A0D69281C98937B4C7DF48874C6495E7E01_AdjustorThunk },
	{ 0x06000140, Unit_GetHashCode_m0C1ADDCC61630206A5F047A3F7D2BEE90BC12FBF_AdjustorThunk },
	{ 0x06000141, Unit_ToString_m5142695D75D00B5562BF63966A8F9B231DCC992C_AdjustorThunk },
};
static const int32_t s_InvokerIndices[360] = 
{
	4364,
	3807,
	3881,
	3807,
	3852,
	4364,
	2691,
	3881,
	3881,
	6421,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9008,
	0,
	0,
	9089,
	4364,
	4364,
	2802,
	4364,
	2084,
	4364,
	1458,
	4364,
	971,
	4364,
	628,
	4364,
	388,
	4364,
	237,
	4364,
	3881,
	4364,
	4364,
	3881,
	3881,
	4250,
	4364,
	3881,
	4364,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8523,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	3518,
	9089,
	0,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8887,
	9031,
	8887,
	8887,
	9031,
	8887,
	9089,
	3881,
	4364,
	6358,
	6358,
	8887,
	7975,
	8505,
	9089,
	2796,
	4250,
	4364,
	4250,
	1359,
	4216,
	4250,
	4185,
	4250,
	3444,
	4250,
	3185,
	4216,
	3185,
	3265,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9057,
	8592,
	4250,
	4168,
	4168,
	3881,
	4250,
	3881,
	4364,
	9089,
	4364,
	4364,
	9089,
	3425,
	3079,
	4168,
	4168,
	4083,
	4250,
	9089,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3274,
	3185,
	4216,
	4250,
	9089,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8395,
	9089,
	9089,
	4364,
	3914,
	7975,
	7914,
	8887,
	8887,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
};
static const Il2CppTokenRangePair s_rgctxIndices[78] = 
{
	{ 0x0200000B, { 0, 22 } },
	{ 0x0200000C, { 22, 8 } },
	{ 0x0200000E, { 67, 4 } },
	{ 0x0200001C, { 138, 8 } },
	{ 0x0200001D, { 146, 22 } },
	{ 0x0200001E, { 168, 13 } },
	{ 0x0200001F, { 181, 13 } },
	{ 0x02000020, { 194, 12 } },
	{ 0x02000021, { 206, 5 } },
	{ 0x02000022, { 211, 12 } },
	{ 0x02000023, { 223, 5 } },
	{ 0x02000024, { 228, 8 } },
	{ 0x02000025, { 236, 18 } },
	{ 0x02000026, { 254, 5 } },
	{ 0x02000027, { 259, 15 } },
	{ 0x02000028, { 274, 4 } },
	{ 0x02000029, { 278, 10 } },
	{ 0x0200002F, { 290, 4 } },
	{ 0x02000030, { 294, 11 } },
	{ 0x02000032, { 311, 6 } },
	{ 0x02000039, { 324, 9 } },
	{ 0x0200003A, { 333, 11 } },
	{ 0x0200003B, { 344, 9 } },
	{ 0x0200003C, { 353, 7 } },
	{ 0x0200003D, { 360, 10 } },
	{ 0x0200003E, { 370, 27 } },
	{ 0x0200003F, { 397, 12 } },
	{ 0x02000040, { 409, 11 } },
	{ 0x02000041, { 420, 36 } },
	{ 0x02000042, { 456, 12 } },
	{ 0x02000043, { 468, 12 } },
	{ 0x02000044, { 480, 45 } },
	{ 0x02000045, { 525, 12 } },
	{ 0x02000046, { 537, 13 } },
	{ 0x02000047, { 550, 54 } },
	{ 0x02000048, { 604, 12 } },
	{ 0x02000049, { 616, 9 } },
	{ 0x0200004A, { 625, 10 } },
	{ 0x0200004B, { 635, 10 } },
	{ 0x0200004C, { 645, 11 } },
	{ 0x0200004D, { 656, 11 } },
	{ 0x0200004E, { 667, 13 } },
	{ 0x0200004F, { 680, 8 } },
	{ 0x02000050, { 688, 9 } },
	{ 0x02000051, { 697, 9 } },
	{ 0x02000052, { 706, 10 } },
	{ 0x02000058, { 716, 17 } },
	{ 0x02000059, { 733, 11 } },
	{ 0x0200005B, { 744, 12 } },
	{ 0x0200005C, { 756, 7 } },
	{ 0x02000061, { 763, 24 } },
	{ 0x02000062, { 787, 6 } },
	{ 0x02000063, { 793, 22 } },
	{ 0x06000014, { 30, 4 } },
	{ 0x06000015, { 34, 3 } },
	{ 0x06000016, { 37, 6 } },
	{ 0x06000017, { 43, 4 } },
	{ 0x06000018, { 47, 9 } },
	{ 0x06000019, { 56, 3 } },
	{ 0x0600001A, { 59, 8 } },
	{ 0x0600001E, { 71, 1 } },
	{ 0x0600001F, { 72, 1 } },
	{ 0x0600003B, { 73, 4 } },
	{ 0x0600003C, { 77, 7 } },
	{ 0x0600003D, { 84, 6 } },
	{ 0x0600003E, { 90, 4 } },
	{ 0x0600003F, { 94, 5 } },
	{ 0x06000040, { 99, 5 } },
	{ 0x06000041, { 104, 4 } },
	{ 0x06000043, { 108, 6 } },
	{ 0x06000044, { 114, 7 } },
	{ 0x06000045, { 121, 8 } },
	{ 0x06000046, { 129, 9 } },
	{ 0x0600007D, { 288, 2 } },
	{ 0x0600008D, { 305, 6 } },
	{ 0x060000B0, { 317, 2 } },
	{ 0x060000B1, { 319, 2 } },
	{ 0x060000B2, { 321, 3 } },
};
extern const uint32_t g_rgctx_Observable_1__ctor_m9745E4BBEC4CF0FF2D441CC720CAC441AC30842C;
extern const uint32_t g_rgctx_Observable_1_t4BB5AC6D0FE947A5AA1BC13192BE12366C949F62;
extern const uint32_t g_rgctx_FreeListCore_1_t12C0FDE10E20974FB70E7D8B51A91942EDC64391;
extern const uint32_t g_rgctx_FreeListCore_1__ctor_m324199B2DCB9B38B25341A3DB66B2D55BAAE0FFC;
extern const uint32_t g_rgctx_BehaviorSubject_1_tC865840C2AA6F8D7413444C100DF413D21D3E4E7;
extern const uint32_t g_rgctx_T_tC93122D456A72FEC23CECF72C6B2E9EA5F950569;
extern const uint32_t g_rgctx_FreeListCore_1_AsSpan_mCB2970A53239F9541BCD4324B4B1BF0C74630F38;
extern const uint32_t g_rgctx_FreeListCore_1_t12C0FDE10E20974FB70E7D8B51A91942EDC64391;
extern const uint32_t g_rgctx_ReadOnlySpan_1_tE4366B947DB7840E6F76D6F6941B8C2DCBE964FF;
extern const uint32_t g_rgctx_ReadOnlySpan_1_get_Item_m8F9C36130D581B921E84CE47E4B8F1B7A21740E6;
extern const uint32_t g_rgctx_ReadOnlySpan_1_tE4366B947DB7840E6F76D6F6941B8C2DCBE964FF;
extern const uint32_t g_rgctx_SubscriptionU26_t01669BE45AA5AE1D585F3B47556EEB3F5FD7606F;
extern const uint32_t g_rgctx_Subscription_tE78629C45E94050B0247B5AB58ABC2C0F35E0CDA;
extern const uint32_t g_rgctx_Observer_1_tC2356DBF0A473924B8C7544660FBA6CFD5C7BBE8;
extern const uint32_t g_rgctx_Observer_1_OnNext_mCD3A66E339F9B1F32A2F0D0DF5CB8FE939E98996;
extern const uint32_t g_rgctx_ReadOnlySpan_1_get_Length_m8A24C42895022AF91D0BEE712F1D730C97B65164;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_mA2CDF8BCCC44478291FD7598B4A7D15D9D15E71B;
extern const uint32_t g_rgctx_Subscription__ctor_m7F4F78CE8650715DA75C5C5FC8DECA754EF5E984;
extern const uint32_t g_rgctx_Subscription_Dispose_mBB97A673B94CC94E1108AD100C9EAC16180CBCED;
extern const uint32_t g_rgctx_BehaviorSubject_1_Dispose_mCF8432F5B9E56E00E8422EF4A596689D0AA51236;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisT_tC93122D456A72FEC23CECF72C6B2E9EA5F950569_mF63F73CDF5D14573D1C48B1C8A3CE47261C89BA3;
extern const uint32_t g_rgctx_FreeListCore_1_Dispose_m15A515C590DB29D363207A163F3F0E5ABC654D04;
extern const uint32_t g_rgctx_BehaviorSubject_1_t8EEF4F2E7BF811F59B0AB0860D79723D9757C353;
extern const uint32_t g_rgctx_Subscription_tF9ED131B9A46B077AE64D58E359AA79F8E41B900;
extern const uint32_t g_rgctx_Observer_1_t2A3CA879FB474526DF77A918143E7E81707E0256;
extern const uint32_t g_rgctx_FreeListCore_1_tE95C278D1E422215141769C2A9083BEFF2E6DD82;
extern const uint32_t g_rgctx_FreeListCore_1_Add_mC2FDEB491FA97119CC74D7D6E005A730BEA14E6F;
extern const uint32_t g_rgctx_FreeListCore_1_tE95C278D1E422215141769C2A9083BEFF2E6DD82;
extern const uint32_t g_rgctx_BehaviorSubject_1U26_tA9E393B9517957FDE20AEAE582D4B92B3699140D;
extern const uint32_t g_rgctx_FreeListCore_1_Remove_m0FE9065CF289770A282BBEAAE36745ACFFF9B429;
extern const uint32_t g_rgctx_Observable_1_t6FBC0073093A00039A1C5C98840B85B2A0FDB0A3;
extern const uint32_t g_rgctx_T_t58794D133D7BD90B9AB7C6CD1DD0D51EA1B69C96;
extern const uint32_t g_rgctx_AppendPrepend_1_t5A22CC6D48D65F474E8FBC8907EC8C48868A6D0C;
extern const uint32_t g_rgctx_AppendPrepend_1__ctor_mB030392E13671402BFD54946B07F1CF1A69D6A35;
extern const uint32_t g_rgctx_Observable_1_tAA8166938425CA4D1EDCC5DF3EE6DA0A4962C2E5;
extern const uint32_t g_rgctx_AsObservable_1_t5BAED16B36305465DD5B924A9F597749C0F91A81;
extern const uint32_t g_rgctx_AsObservable_1__ctor_m0E2541005C593E25A04F2EA7E8A7580D4976FC28;
extern const uint32_t g_rgctx_Observable_1_t0E6E6D5C61DFECE893D4940B2F2DF158FCEBE4EE;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_m191AF294A65377428994410DD54959EF9747B60A;
extern const uint32_t g_rgctx_EqualityComparer_1_t5B4117369C0149E9FF859C9E2179865781A4B09B;
extern const uint32_t g_rgctx_EqualityComparer_1_t5B4117369C0149E9FF859C9E2179865781A4B09B;
extern const uint32_t g_rgctx_ObservableExtensions_DistinctUntilChanged_TisT_t6C6AD4E7618005B70F3C84DCD4B3BF781CFC71E4_m86A8B63542014D4484FFFAF200CA0375A3637F80;
extern const uint32_t g_rgctx_IEqualityComparer_1_tC6F9C7133DB6D8E8ADBBB044E5090DFF29C77DA6;
extern const uint32_t g_rgctx_Observable_1_tD8A3238BD5D449C19BD35BCD894BAD2C591E2D6A;
extern const uint32_t g_rgctx_IEqualityComparer_1_tE0DC9250D17E925B082F391D29BFDC3C00710F64;
extern const uint32_t g_rgctx_DistinctUntilChanged_1_t422406C3E67DFB474FA700FA63F18D0A6CA92669;
extern const uint32_t g_rgctx_DistinctUntilChanged_1__ctor_m80800E94A29E26FF5C74DFD7F7D83957391980AD;
extern const uint32_t g_rgctx_Observable_1_tA844C6AA4D05FD486C6C950A12803CA3B500F004;
extern const uint32_t g_rgctx_Where_1_t236515CC0764659207713C7AC427659AD3E69E31;
extern const uint32_t g_rgctx_Func_2_t93B78F8CA12CCAFD9A012DDFC8BE6292023FDAFB;
extern const uint32_t g_rgctx_Func_2_tEA49CF7BC9AD0370DB79570A5FBA444457228F81;
extern const uint32_t g_rgctx_WhereSelect_2_tFE820B2E0734C6FA056E87FD9E6A75D935C49A5F;
extern const uint32_t g_rgctx_WhereSelect_2__ctor_mDED15FC79C33A41E68B99CCCC2EC876D458C7549;
extern const uint32_t g_rgctx_Select_2_t6FEC8B075E9E097DF4606D716B749F4778AFDCF1;
extern const uint32_t g_rgctx_Select_2__ctor_mE3C9739829F3ED3680806532FAB2B0783423C13C;
extern const uint32_t g_rgctx_Observable_1_tA16D72F5BCA1538ABF619D1608C8220E42057263;
extern const uint32_t g_rgctx_Observable_1_t28ED85DF5F0A78008EEFDA45D7016820CB55E224;
extern const uint32_t g_rgctx_ThrottleLastFrame_1_t46FF09CAF1010B3C107D3F4017D71C457FE171EB;
extern const uint32_t g_rgctx_ThrottleLastFrame_1__ctor_mFA1AA36FBC7DFB8884853D1676E76AAC7C3F18A7;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass263_0_1_t80C1F8C6E015DBF164DE43D90BAA747B10F66940;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass263_0_1__ctor_m1EF01A76B75FC08551657AC1200F01273CA0C444;
extern const uint32_t g_rgctx_Func_2_tD8556ACDD919D18A2F12F78A894C37866D6D9789;
extern const uint32_t g_rgctx_Observable_1_t8383F1E059B0C5A6C5CC1838D864FE39D04F9ACC;
extern const uint32_t g_rgctx_Where_1_tED1A3C5CB8DA20D2927C3DE5E345798296AE2E64;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass263_0_1_U3CWhereU3Eb__0_mA96BB92CDD0F0C8D2FDD846E0EE68E1956318605;
extern const uint32_t g_rgctx_Func_2__ctor_mD46716680AC692B0672AFC7C461A0E710B5763FB;
extern const uint32_t g_rgctx_Where_1__ctor_m388972750FBFB3205620BD8489233B47864E71FA;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass263_0_1_t22BDC85126E355B50B5C25F8F325CE570B070016;
extern const uint32_t g_rgctx_Func_2_t8B68B4E8A78C084CF6511130966020B8C24F4D29;
extern const uint32_t g_rgctx_T_tFFF5D61E5AA75E16AB0D5225C39E3D11BE33B148;
extern const uint32_t g_rgctx_Func_2_Invoke_mD3EDAF591C8F0C897D1D02C97AF74698516FD77E;
extern const uint32_t g_rgctx_T_t564F49C19CBC288A80A49AB2B94F47864DBB1FEE;
extern const uint32_t g_rgctx_T_tA9121412ECE93DEFB674E8ACBAB814E1CF434EC3;
extern const uint32_t g_rgctx_IEnumerable_1_t96DA1A2EC73D41EBDD185B842FBFE6856C3BF7E1;
extern const uint32_t g_rgctx_CombineLatest_1_t44BA49874C4F7CD6A9C27ED5231EC03AF0836B3D;
extern const uint32_t g_rgctx_CombineLatest_1__ctor_m065D81E891843BF945C39216286F39CEAB8AD7A6;
extern const uint32_t g_rgctx_Observable_1_tDA7DA55014C7D107C0915C15F555617A4BB29CBF;
extern const uint32_t g_rgctx_TSource_tFA2A59B7A0D126293C5DF676A39DB4553584209F;
extern const uint32_t g_rgctx_Func_2_t4911912D6F82E86E3646962AABB26B23C70A67B1;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_mDBDC8E1EEA185161559D12CCD1F76FF50F3B05E7;
extern const uint32_t g_rgctx_EqualityComparer_1_tB82C563A06305896342A1B4D54602F8B8E4CDFD6;
extern const uint32_t g_rgctx_EqualityComparer_1_tB82C563A06305896342A1B4D54602F8B8E4CDFD6;
extern const uint32_t g_rgctx_Observable_EveryValueChanged_TisTSource_tFA2A59B7A0D126293C5DF676A39DB4553584209F_TisTProperty_tE072FDA9856E2A82F5D4456969DC4F121235FE02_m0D9AA4011921DF58A9AA19F17DB5642AD2782A0D;
extern const uint32_t g_rgctx_Observable_1_t75C76B4DFDDD2CEDB867D4AC4151DD5C9845AF0E;
extern const uint32_t g_rgctx_TSource_t29D8DFF342A403276B57C43C36F9C4F0028184CE;
extern const uint32_t g_rgctx_Func_2_t26AF28AB78B4DAA2E0C3AFAECABE74B22C2B456B;
extern const uint32_t g_rgctx_EqualityComparer_1_t6A20FDE0CE48032C722F96AD0F9A18C7A9E46C58;
extern const uint32_t g_rgctx_EveryValueChanged_2_t1524E484F0C699016EF8442D580DBF82B37857F3;
extern const uint32_t g_rgctx_EveryValueChanged_2__ctor_m9DDB9CC52CBD654B870BBE095DC323A3934EF66D;
extern const uint32_t g_rgctx_Observable_1_tDAC2E634A6BDDD52F00B8E1EF6A7D929A1440BC4;
extern const uint32_t g_rgctx_Func_2_t34D82CA2D2C219C88A7E41246CEBE013A584B4D1;
extern const uint32_t g_rgctx_Action_1_t2C46B015168BDB9C5877AC999742645CE60F65F4;
extern const uint32_t g_rgctx_FromEvent_1_t43B9185FCC206411176A621F9731C9137C37B934;
extern const uint32_t g_rgctx_FromEvent_1__ctor_m48A1225EC1AAF19BC69671BCD363CD5F8CCEE9BA;
extern const uint32_t g_rgctx_Func_2_tA76DC0C9CD2AB5D5A43907AFE822142F6FFF6C56;
extern const uint32_t g_rgctx_Action_1_tF0D2688EBBA493F80C5D00F5A7DEC16331C83166;
extern const uint32_t g_rgctx_FromEvent_2_tC74EB6334CECC59C3A167A2DA0F363CEAC22FEA4;
extern const uint32_t g_rgctx_FromEvent_2__ctor_mEC1AAF2C3E55F873E1CFCB40CB5BF23CDBE1C5F7;
extern const uint32_t g_rgctx_Observable_1_t60A9747A84712C69895EE1D579F7BC7C7CCB2D29;
extern const uint32_t g_rgctx_Observable_1U5BU5D_tECEC4A61C5DC27E3D545C1E222749A998EC3A92B;
extern const uint32_t g_rgctx_Merge_1_t701E95B88EE3368002EB4215FF4EA778DE28B541;
extern const uint32_t g_rgctx_Merge_1__ctor_m914F250ECA6CD171387099C091AA01D9F8A96B6A;
extern const uint32_t g_rgctx_IEnumerable_1_t24D50B25FEB5C11B2E169952C69DDB48D6418617;
extern const uint32_t g_rgctx_Observable_1_t3D548A6163BC48E05287FEED2FE4539A2F132358;
extern const uint32_t g_rgctx_IEnumerable_1_t26CC654B5567015639933AEC1011325565AA3159;
extern const uint32_t g_rgctx_Merge_1_t1A59469AAE1716E224BBF7C9EDF94F75F23564C0;
extern const uint32_t g_rgctx_Merge_1__ctor_mEE294DF8785DA32F090AB3E490C23D6D37301888;
extern const uint32_t g_rgctx_Observable_1_t889C26844C3878A9CEF2220F4694FD5D1F5C2658;
extern const uint32_t g_rgctx_Observable_1_tE0911392ADDB854226BA29556FDFE034855D96E8;
extern const uint32_t g_rgctx_Observable_1_tE722B95D17A529B4910968E44238EB47EAEE1ADD;
extern const uint32_t g_rgctx_Func_3_t51D80682FDFA0DDD74B099D9A093E381380B0168;
extern const uint32_t g_rgctx_CombineLatest_3_t34F9B27038A59F02EDE637BFB72E671921B46D66;
extern const uint32_t g_rgctx_CombineLatest_3__ctor_m2AEEBC4915950C5DE7F3688E59F38F228FCFB243;
extern const uint32_t g_rgctx_Observable_1_t58674260EC82716D5E6640A8F769DD3A771F3E2E;
extern const uint32_t g_rgctx_Observable_1_t841120B7979C7A4C2FECDAC728F4278108125EDA;
extern const uint32_t g_rgctx_Observable_1_t1B621C471C00BA6DD9245DFF7A84A070D72AD97B;
extern const uint32_t g_rgctx_Observable_1_tE18A983ED7DE1CF0331987E0E0CAD1456739523A;
extern const uint32_t g_rgctx_Func_4_t08EBC59D6B47BEE2B57C9122763C089379B46318;
extern const uint32_t g_rgctx_CombineLatest_4_tEDB876A356289D4738DD6A992FE535DD78F826E7;
extern const uint32_t g_rgctx_CombineLatest_4__ctor_m32752C5C1A74C4FC2C5960B90FAC95972F187B68;
extern const uint32_t g_rgctx_Observable_1_t7BBE6DB594A16503D2443AE89433EDDF0976C1FA;
extern const uint32_t g_rgctx_Observable_1_t29A032C09F45077C23C2D7D88E09C3F0FD5B3C2E;
extern const uint32_t g_rgctx_Observable_1_tF325DC6DACACA30890A8CFF970B21CE66DC3E446;
extern const uint32_t g_rgctx_Observable_1_tEC20E6B2BDB15CC184B5B0D173E99011B606CB65;
extern const uint32_t g_rgctx_Observable_1_t74FD2896368BF508A6370FFB2B5339281F86DAF9;
extern const uint32_t g_rgctx_Func_5_t8C4A06DEA32E95F2EACEA6EE257A9686375217E7;
extern const uint32_t g_rgctx_CombineLatest_5_t6072ACF25F8731E2A85193839D6F7F66138267B0;
extern const uint32_t g_rgctx_CombineLatest_5__ctor_m7AA90E3184F7496158B9D1E6AB9ECC35689F7D6E;
extern const uint32_t g_rgctx_Observable_1_tD17F5CE5D25D81700A736696ABEACBF5B2DC7DA4;
extern const uint32_t g_rgctx_Observable_1_t15EEEEBA8B60189724B80A96B5F7681175DA1E11;
extern const uint32_t g_rgctx_Observable_1_tC97F54DDF33E103283DBAEC5CB006B3AE8D63747;
extern const uint32_t g_rgctx_Observable_1_t0F0CEA5C00150C461F91E3CD569DAE22A614E793;
extern const uint32_t g_rgctx_Observable_1_t69B3179186436B15F975DC78CEE521257F1C37AC;
extern const uint32_t g_rgctx_Observable_1_t57D792BED9A2B58C6990DBCD4058A782CD5D46FA;
extern const uint32_t g_rgctx_Func_6_tBA5D8250A41453A2337597E359AA10AB4039F077;
extern const uint32_t g_rgctx_CombineLatest_6_t1F4C66F49CDD8CB4462D023C5B0E068B07A91F9B;
extern const uint32_t g_rgctx_CombineLatest_6__ctor_m88B51313D4FDBCE86DDF99F6978B78B0A11ECCB5;
extern const uint32_t g_rgctx_Observable_1_t3E3274877225099C65E3491DAF690DA63CDF8442;
extern const uint32_t g_rgctx_IEnumerable_1_t41E46CAA0E12DB1ACCEF0E93C3066EAD61BF8759;
extern const uint32_t g_rgctx_CombineLatest_1_t2EFD7268570634A82E68ED2BB1C8C76CCE1FEE8D;
extern const uint32_t g_rgctx_Observable_1__ctor_m087FB3381A10E658D7204E83420FBFF7F023D777;
extern const uint32_t g_rgctx_Observable_1_t16855865A8396CC5D69AFAF910EF4217EEC71EA5;
extern const uint32_t g_rgctx_Observer_1_t5D4C178355C1BA096B371DE620056966D5D0DE59;
extern const uint32_t g_rgctx__CombineLatest_t730F4C438D7D8F933576B2D6B2789572480DCEBD;
extern const uint32_t g_rgctx__CombineLatest__ctor_m8F52A3D7EC78008432C325E49581245E92C4B910;
extern const uint32_t g_rgctx__CombineLatest_Run_mF82605AA17169CB8316E4A4C3BB5285119A53D33;
extern const uint32_t g_rgctx_Observer_1_t31B8A4DD60075037B191BF9069EDE179DB0BA09E;
extern const uint32_t g_rgctx__CombineLatest_tC88FB03A3B58E755A93A6A204AB92182536101E7;
extern const uint32_t g_rgctx_IEnumerable_1_t398523CE2F6261975C3731FE430035E89C049436;
extern const uint32_t g_rgctx_Observable_1U5BU5D_tD29F02EC0D3E3AB3F21973FA1E7A60ED93ED27CF;
extern const uint32_t g_rgctx_Enumerable_ToArray_TisObservable_1_tF9760CCFB00E4628E856C84A6B4D711E1937DCF0_m03985307BB26862050AF809934F07AE184771168;
extern const uint32_t g_rgctx_CombineLatestObserverU5BU5D_t6B329112734A1CA06CF1E6609185810EE940D488;
extern const uint32_t g_rgctx_CombineLatestObserver_t2B9795C416E69B4CBB1584AED37AD8C2625CD8C6;
extern const uint32_t g_rgctx_CombineLatestObserver__ctor_mD8E3EED4EB3AC05114EF1CA3B91B588723B78718;
extern const uint32_t g_rgctx_CombineLatestObserverU5BU5D_t6B329112734A1CA06CF1E6609185810EE940D488;
extern const uint32_t g_rgctx_Observable_1_tF9760CCFB00E4628E856C84A6B4D711E1937DCF0;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m4CF5A12EB7E2FD5B45D97BED552B91FB3D321ECC;
extern const uint32_t g_rgctx_Observer_1_t13145601BF5CA9FA621CEC984C3E717EA07C0F83;
extern const uint32_t g_rgctx__CombineLatest_Dispose_m35F7A9843927D8F0FDD9B22FCED0E46ED611BF92;
extern const uint32_t g_rgctx_CombineLatestObserver_get_HasValue_m12B25028049DD156BB7F25C63598BF8BF7C2685F;
extern const uint32_t g_rgctx_TU5BU5D_t63A70AF30CCA50200CC6421975424453281FDAE1;
extern const uint32_t g_rgctx_CombineLatestObserver_get_Value_m93B25143A4FB9BE730E895B73EC1FBF9BBD60D2E;
extern const uint32_t g_rgctx_T_t7A518ED57C502D019FA1CDB9C2119D9F9D4FCD7F;
extern const uint32_t g_rgctx_Observer_1_OnNext_m499ED36793B7D0F55E39A17456A5AF4AA061C9A4;
extern const uint32_t g_rgctx_TU5BU5D_t63A70AF30CCA50200CC6421975424453281FDAE1;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_mEF2CC395CD6E7E7E5BC35D284226077146592FC8;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisTU5BU5D_t63A70AF30CCA50200CC6421975424453281FDAE1_m59E50DEC9AC728535528950031635D9E2671CF7B;
extern const uint32_t g_rgctx_Observer_1_Dispose_mB22B0ECB6DD4391DD56076881BFECE5DF14AABA9;
extern const uint32_t g_rgctx__CombineLatest_tB59E0EB83A5AFA576A88182E04D0DFD547998949;
extern const uint32_t g_rgctx_CombineLatestObserver_t62CB27666443FDEDB557039D3990C2F7F3171D23;
extern const uint32_t g_rgctx_Observer_1__ctor_m96D6CAB80F4DDC1D50ED25C2377070F658BFEACE;
extern const uint32_t g_rgctx_Observer_1_tCB3547D31F50B75431B858FE736AB19FB8976BE8;
extern const uint32_t g_rgctx_T_tEBE3C6456B42636A816BFB61CC4182D0460FCC47;
extern const uint32_t g_rgctx_CombineLatestObserverU5BU5D_t3444CC52224E26E77AA74DBF9DC1BFAFC8FD5C06;
extern const uint32_t g_rgctx_CombineLatestObserver_set_Value_mA8FB9BAA0732C20D3619D283E774A84C60122092;
extern const uint32_t g_rgctx_CombineLatestObserver_set_HasValue_m4F724C0FE88170F055CFF8FEF2E3402F3EAB4DE4;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnNext_mEC9DCE7E7CE13BBA69D5316842B8DF0A68AA8C06;
extern const uint32_t g_rgctx_Observer_1_t0C6785701B0ADB23A45097A125EB5FC02B9D8A16;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m0BA57C96DD6D3DBB30C89D99B8D29C46B856CFEB;
extern const uint32_t g_rgctx_CombineLatestObserver_get_HasValue_mFAEEBF17721550940512DBF1CC4CC3AF02DB106A;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnCompleted_mC0C5D32EA792186E9F2B2EAB7615763DD84BA530;
extern const uint32_t g_rgctx_TSource_t049EA2F3D3018E0DC6B6ABD53625244B7EBEB5D9;
extern const uint32_t g_rgctx_EveryValueChanged_2_t12A379E06ACB7D66E94AAC96C742351498A79EED;
extern const uint32_t g_rgctx_Func_2_tA2F0BAB0EF5E485AE7A45BD1514C32F24D8F0073;
extern const uint32_t g_rgctx_EqualityComparer_1_tA05BC350334153B57C8DEDB1A70EB46A2CED2FF5;
extern const uint32_t g_rgctx_Observable_1__ctor_m2686A96D2A97DEB489C23A764AEA7A826049FD05;
extern const uint32_t g_rgctx_Observable_1_t2730E3950CB8960E855301B2BDA730F7B5D19DBD;
extern const uint32_t g_rgctx_Func_2_Invoke_m5EF9FA0699A5057E9B135F38B18CED7CAC447012;
extern const uint32_t g_rgctx_TProperty_t5632CEA68EE03041E2091CECEFDB3CBAC179CF06;
extern const uint32_t g_rgctx_Observer_1_t76E59C57FC2BB125B3B89232D88CF621900C9B19;
extern const uint32_t g_rgctx_Observer_1_OnNext_mAAF34F1E2D478FFFD7BAAE0ADEBC617C0B8D365D;
extern const uint32_t g_rgctx_Observer_1_get_IsDisposed_mAF9E5722B56F210936FAB6026622F1036EC3B261;
extern const uint32_t g_rgctx_EveryValueChangedRunnerWorkItem_t84C581731665C874FC0585521A73F046610C5672;
extern const uint32_t g_rgctx_EveryValueChangedRunnerWorkItem__ctor_m215F9F1CEEE5B3192F16C09FB2EA54BEC8A8F3F6;
extern const uint32_t g_rgctx_TSource_t3D0A61496EBC7EEFC0F08D37E987FC6307F62C88;
extern const uint32_t g_rgctx_EveryValueChangedRunnerWorkItem_t24C42B67E2ED633071EB6AC6AB8C1FAF7610C8E7;
extern const uint32_t g_rgctx_TProperty_t0BBD2AF541DF191DFD40E7B71CA08C54F6E7F6C6;
extern const uint32_t g_rgctx_Func_2_t98D94DA43C6C8DA74D5F74D10F18651D6D9CB8DF;
extern const uint32_t g_rgctx_EqualityComparer_1_t8905842AEFF176DECD3EC7FF53B895D2F3352E32;
extern const uint32_t g_rgctx_Observer_1_t93F3047602038042FB9F9C773666D9BF4DF7885D;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1__ctor_m9C0C7415A25051B76FFEDF571276EF260137EABD;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1_t2CF91F955EE7B3B253F89AD73E0A3A3C4142050D;
extern const uint32_t g_rgctx_Func_2_Invoke_mC6F86107B462A66DC4BE49C4C44279F380453CC9;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1_PublishOnCompleted_mDF9402055DFF8FAEBEC210A6C3F8DD1B44A08948;
extern const uint32_t g_rgctx_EqualityComparer_1_Equals_mCAD47D9F3387B01C43064A2F60B16C61A2FBA0B0;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1_PublishOnNext_mCAC5482D9D533D795C20C040169F4E44CE090798;
extern const uint32_t g_rgctx_Func_2_t89BC02C16E4132C8F49CE91BEBB3C545C7C28918;
extern const uint32_t g_rgctx_FromEvent_1_t5EFF89B40235AB01F7D5A13F6E322AD2DBC9C041;
extern const uint32_t g_rgctx_Action_1_t18063DC731C923D244F76B3C1165A69F8A156233;
extern const uint32_t g_rgctx__FromEventPattern_tEA78778F396765F82C325982DF0FFABEDD705203;
extern const uint32_t g_rgctx__FromEventPattern__ctor_m6432BB8691FA34BA4574B6907285C0B56E6F1902;
extern const uint32_t g_rgctx__FromEventPattern_tB6A220A04623E115856DC5196BF16291637B7AED;
extern const uint32_t g_rgctx_Action_1_t423974FF2C73E87CE46D8467E33FC4ED2F01EE06;
extern const uint32_t g_rgctx_Func_2_t73B9619C6CC542C5CFCE4E77969F8ADE53B2F676;
extern const uint32_t g_rgctx__FromEventPattern_OnNext_m4186563D1E10F328224B04A00B6252C761E397C0;
extern const uint32_t g_rgctx_Func_2_Invoke_mFBEB78603E6CE895F4A2E439453C764E84A9434B;
extern const uint32_t g_rgctx_TDelegate_tD98693656129DE90EF6F02EC7D63FD0B0C70C591;
extern const uint32_t g_rgctx_Action_1_Invoke_mFEF9A7078CC6C59112D115FBEB6CF3E26DB12917;
extern const uint32_t g_rgctx_U3CU3Ec_t7314A1F1E7B4C0A688D9FA05ABBB2C3370FEF8A6;
extern const uint32_t g_rgctx_U3CU3Ec_t7314A1F1E7B4C0A688D9FA05ABBB2C3370FEF8A6;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_ctorU3Eb__4_0_mEFBD2320E607C3D298C40A810B783DD67667003A;
extern const uint32_t g_rgctx__FromEventPattern_Dispose_m5C42393EFA1B743BC17F7F5D5FD74CB370213FC7;
extern const uint32_t g_rgctx_Action_1U26_tC92A86B53C56A6306FAA2D411AF2C2F6D459D438;
extern const uint32_t g_rgctx_U3CU3Ec_t1F1BE596A7F34912FCAFB47474E1FCC811A11D32;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_m01225CC0E82E7F53AF26BCEA06A778605AD16BEA;
extern const uint32_t g_rgctx_U3CU3Ec_t1F1BE596A7F34912FCAFB47474E1FCC811A11D32;
extern const uint32_t g_rgctx__FromEventPattern_t5DF831A6E58C00FF25B54AFD4292860BB579444C;
extern const uint32_t g_rgctx__FromEventPattern_CompleteDispose_mA9EB54DA3D8295E42198E5C1D5A013118A69A1EB;
extern const uint32_t g_rgctx_Func_2_t850D5F5B80D3FA6EA51FEEBD6ACA88F56538C067;
extern const uint32_t g_rgctx_FromEvent_2_t4CC83D21AB1432FDC19A0CA2CD0B17680301089C;
extern const uint32_t g_rgctx_Action_1_t511A87E91D7B954147E85BE2AF8346D3D6C59CF7;
extern const uint32_t g_rgctx_Observable_1__ctor_m9BC1A4EDA3243D9592F361C7A3A2FEBA5FA2DB7B;
extern const uint32_t g_rgctx_Observable_1_t780C90294A2CDC3E4119F473C98056BFB23544E1;
extern const uint32_t g_rgctx_Observer_1_t3D68615FEA7B1417C750F03BF814D19B063D9F72;
extern const uint32_t g_rgctx__FromEventPattern_t7D0F17E4F7D114E80CAFF5A1D8782402423C80EC;
extern const uint32_t g_rgctx__FromEventPattern__ctor_m8625B7F17B13738BF905BEC28028CF7E2086AA79;
extern const uint32_t g_rgctx_Observer_1_t41DACC6FEB8812BDC60EEC5EAD7CF6C68825343B;
extern const uint32_t g_rgctx__FromEventPattern_tF3B1C67AB7286873397648A40BE689AD36294EDD;
extern const uint32_t g_rgctx_Action_1_tB45E705207BCA280DF00D1E95099FCB978F55296;
extern const uint32_t g_rgctx_Func_2_t10A2ABC6047EBD4B6D182117D42532D4C845AC67;
extern const uint32_t g_rgctx__FromEventPattern_OnNext_mE22254BE1D5CFEC913D9278FF3645506F35777F9;
extern const uint32_t g_rgctx_Action_1_tED00ACEB0622E64157FD7938008CF782B8F4B172;
extern const uint32_t g_rgctx_Action_1__ctor_mFFD446A8FE6242D16A3E7FAF20192EC136F7F9E4;
extern const uint32_t g_rgctx_Func_2_Invoke_m6ACAA10A406E47C202697A21B332D1664A95507D;
extern const uint32_t g_rgctx_TDelegate_t325DE66DFABA289894039A4CEE29B5B8A587FBC5;
extern const uint32_t g_rgctx_Action_1_Invoke_mA7E18B6D83D4BD3E5AF315B805B1DFBE56CBBBCB;
extern const uint32_t g_rgctx_U3CU3Ec_tA76D3D5D83DD7C5CFCCDD70D43B7374A457EB180;
extern const uint32_t g_rgctx_U3CU3Ec_tA76D3D5D83DD7C5CFCCDD70D43B7374A457EB180;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_ctorU3Eb__4_0_m4DAFCCD95F4AACE08FFBD8CB3E5C9ADA4670C978;
extern const uint32_t g_rgctx_T_t325D8EA4CEEFC0899C93ACFE717E9A5C52EC8D5C;
extern const uint32_t g_rgctx_Observer_1_OnNext_mD5620529BCDDC5F63685E69F3E5E96D327380316;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisT_t325D8EA4CEEFC0899C93ACFE717E9A5C52EC8D5C_mDAB21801CD9063DAF19B856A984BB1C878153768;
extern const uint32_t g_rgctx__FromEventPattern_Dispose_m7FB2142D5486D478896C2A4411292471802FD963;
extern const uint32_t g_rgctx_Action_1U26_tE7624D1E98CB2A5D41FE357CABBE059FD71AA2EB;
extern const uint32_t g_rgctx_U3CU3Ec_tC637CF7A7BEB2F62D03702D10CEACB98A876890A;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mFBCCCD50EFEF9955799F9DA8EB4AD247FA399BEE;
extern const uint32_t g_rgctx_U3CU3Ec_tC637CF7A7BEB2F62D03702D10CEACB98A876890A;
extern const uint32_t g_rgctx__FromEventPattern_t422E00235B3D4735DF456C95C3BF700CC02BD740;
extern const uint32_t g_rgctx__FromEventPattern_CompleteDispose_mC57FAB0177D7E0F68770B88D84EE39A9C6D9B700;
extern const uint32_t g_rgctx_IEnumerable_1_t765E3AFD6696964B9D796C185B722A93F35F2A80;
extern const uint32_t g_rgctx_Merge_1_t09942E460803B79BA1FFBC343CF6D6EAF1CBD196;
extern const uint32_t g_rgctx_Observable_1__ctor_mDFDAB1E6EEE9E0D1E97A7081CCB746F9B2D46125;
extern const uint32_t g_rgctx_Observable_1_t12824A151D41C27A72A4832CADBEAD6564199EF4;
extern const uint32_t g_rgctx_Observer_1_t0DF621987B74E5EF5B16C1165222010E58D399E3;
extern const uint32_t g_rgctx__Merge_t165CCED0F0D0EF0CA8DF61495FE9333024408F87;
extern const uint32_t g_rgctx__Merge__ctor_m78E7767B93C12CAF3A230393657AFCB9B9CDA742;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m6C525857A8CBAD7850270EAC8FA29062120961BC;
extern const uint32_t g_rgctx_IEnumerator_1_t8DB6C43190E5CB7E735C148E1FD4BC5CAF8221EA;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m1D8634C92B0FE6699EEA02A7BDF2BB4BFF1DFA59;
extern const uint32_t g_rgctx_Observable_1_t12824A151D41C27A72A4832CADBEAD6564199EF4;
extern const uint32_t g_rgctx__MergeObserver_t95445F1E0CA6AC0B50B7007B78C2CF4F938F9EF6;
extern const uint32_t g_rgctx__MergeObserver__ctor_m781FF6164E8E9447F0A63B34F26D30D5612771A9;
extern const uint32_t g_rgctx_Observable_1_Subscribe_mBD5466295A249678ED3AEEE4907C9D29F6FBE35E;
extern const uint32_t g_rgctx__Merge_SetSourceCount_m4404E29484BD59533C99A5BE3BECF4DBA9878E3D;
extern const uint32_t g_rgctx_Observer_1_t026A78C17567F0A349EB7844AD2C4F1C33FEFF14;
extern const uint32_t g_rgctx__Merge_tB07BCBCE49C4E8BCCF9243B9FF70BFDA2BC0BBF9;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisT_tF4511D497B5AA51476B73DCA38DAD2A5FECBDE90_mAFCEE23B43C60DCD41D447AEA99F8C8637D30DA8;
extern const uint32_t g_rgctx__Merge_Dispose_m4AB91FA8410BDD9E97672E07872CD58DD98859FA;
extern const uint32_t g_rgctx__Merge_t28DBF9AFC7F6FFA6D2A0F457B8FA81CD52A46D0C;
extern const uint32_t g_rgctx__MergeObserver_t4BA323A660E3E56D313D2102AF3EDC9138E0C885;
extern const uint32_t g_rgctx_Observer_1__ctor_mCE96C1B6EB77CB2E521DC087984B0E23E719C431;
extern const uint32_t g_rgctx_Observer_1_t6D07F9F2ACE3AD67C941EC8DDBE2EB62245293F6;
extern const uint32_t g_rgctx_Observer_1_t6D07F9F2ACE3AD67C941EC8DDBE2EB62245293F6;
extern const uint32_t g_rgctx_T_t590A7AF24FFE209D50978E3D297D6BD6E136D3CE;
extern const uint32_t g_rgctx_Observer_1_OnNext_m777811C54A4468E83BD3E88AF159E79EC68C1814;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m4C7BA7DB666D4611CF68590F0910C400DC4F68AD;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_mA30FC509320988990C26AE9186FA19356F764D91;
extern const uint32_t g_rgctx__Merge_TryPublishCompleted_mB6C679CDE522D5739078D1D68889F8D819E4CBE1;
extern const uint32_t g_rgctx_ISubject_1_tEF8AF09F1AAAA6B2F27489C76FE0A499993E6A0E;
extern const uint32_t g_rgctx_ISubject_1_OnCompleted_m0294541A0DEF5DAA9662B761C04FAD5FF34B6282;
extern const uint32_t g_rgctx_Observer_1_t601CE2C28815F5364EE182E024D72B8D882A35E3;
extern const uint32_t g_rgctx_Observable_1_tECE8AF4B6440C0D2F4333FC3E1CC973129666B63;
extern const uint32_t g_rgctx_Observable_1_SubscribeCore_mAD4388A0BECAC79CE155F3A90ED39D38CA9A705B;
extern const uint32_t g_rgctx_Observer_1_Dispose_m065951DAB12A990AC1306BC8EA506C599B3AA445;
extern const uint32_t g_rgctx_Observer_1_tCABCFA8D59DD2AC30F4323DF9B055972E5760226;
extern const uint32_t g_rgctx_Observer_1_get_IsDisposed_m9FF473889E20EC9B9EE6B88694321697936E1B6A;
extern const uint32_t g_rgctx_Observer_1_get_IsCalledCompleted_m43B6211F969929E4A737C44CCD9676BBD6DF1099;
extern const uint32_t g_rgctx_T_t5BCC180D5484A07F1E6E19914985E896B7DC11B4;
extern const uint32_t g_rgctx_Observer_1_OnNextCore_m4AF49341896F94C564AE0F3C2A50901A5A3DBF31;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m3D504F5EBB251CF0867CA6E9FA19CB6FFD196F6C;
extern const uint32_t g_rgctx_Observer_1_OnErrorResumeCore_mD2483017958875E7BB0888F1DF46056D678D71CB;
extern const uint32_t g_rgctx_Observer_1_get_AutoDisposeOnCompleted_m49715596A03A2D91B357D4AD9A3E61697A319707;
extern const uint32_t g_rgctx_Observer_1_OnCompletedCore_mCFCC743FE990865B253354200F8571669CB6F316;
extern const uint32_t g_rgctx_Observer_1_Dispose_mCB8DD7D4857EEF0C0475121D3956D4A6270994AE;
extern const uint32_t g_rgctx_Observer_1_DisposeCore_m92BA7AC405C3906C81FCDEFD34DB65E728BA0FB8;
extern const uint32_t g_rgctx_Observable_1_t5430E62ABAF1E097C320F417313CC43063E3A8BF;
extern const uint32_t g_rgctx_Action_1_t80F6718518A2B825F309197A551064EF9FEA7356;
extern const uint32_t g_rgctx_AnonymousObserver_1_t035CF5C7EEA6CA698C1068D14CD16A73B94746EA;
extern const uint32_t g_rgctx_AnonymousObserver_1__ctor_m2576287D05383BF9D1D51515511B0E3C5BBFE329;
extern const uint32_t g_rgctx_Observable_1_Subscribe_mEC360A87CCD4B037C0109CE1AAC62E022E4D86B3;
extern const uint32_t g_rgctx_Observer_1_tB80D7A56F7641B5F82A51F10E876E2875C4B5BA9;
extern const uint32_t g_rgctx_Action_1_tA67F945E9DA2847BF08D9411652AE6389C21F478;
extern const uint32_t g_rgctx_AnonymousObserver_1_t8575E9AE52FF35FA5223C8FA51C4462C2C979D1D;
extern const uint32_t g_rgctx_Observer_1__ctor_mB235D4C4A12FA475E4B04ADD5091DA887E65E036;
extern const uint32_t g_rgctx_Observer_1_tC1CAB30F2D310FE9E4DAFDB8EE22C69D92CFBF9F;
extern const uint32_t g_rgctx_T_t7AC27C176F7E27E577CB9CBF2FF0B0F5C758DB21;
extern const uint32_t g_rgctx_Action_1_Invoke_m769E1D3755D80C57E28436544A8FD8FD8EA9DD2D;
extern const uint32_t g_rgctx_Observer_1_tE76C3217669A83FE9CF4AA702F08247E390EFB59;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m0C5CD226723987ED02EBE777249173E9AAEC1FF9;
extern const uint32_t g_rgctx_Observer_1_t5DCD1B77CA763F5631A6F538D7053CB5C1AC51BD;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m69490B4C66BBC5575924A00D82CD352A929BA932;
extern const uint32_t g_rgctx_Observer_1_tE30C86957913780E95BD3D03D34B51506C928DB6;
extern const uint32_t g_rgctx_WrappedObserver_1_t6F8E2A6EFE82816153ECA670F5E4630727BC2BD4;
extern const uint32_t g_rgctx_WrappedObserver_1__ctor_m8BDAB944833322677336A3826CADD6EE54AEB0B1;
extern const uint32_t g_rgctx_Observer_1_tE2C12E8EF78A2C4BFD36E4E7C22803780EAC3765;
extern const uint32_t g_rgctx_WrappedObserver_1_tEBEDF0A18239A468BC58B5BB082782ECE4B6DE49;
extern const uint32_t g_rgctx_Observer_1__ctor_m3EF62A397B17BD242929A6ECFB7E84DEDC75F836;
extern const uint32_t g_rgctx_Observer_1_tE2C12E8EF78A2C4BFD36E4E7C22803780EAC3765;
extern const uint32_t g_rgctx_T_tE3A2F6C500B74E3B2C99A2107FC5590302D5FD1F;
extern const uint32_t g_rgctx_Observer_1_OnNext_mE43EEB40854AF8B7833E6551BFF9AA5DDA3A71E7;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_mEB36AFACAA88EBFBB8E6ABED8A8E6AD36ADA63ED;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m45AEC4B56BB0714D9BF0EF0CE293BD9210651199;
extern const uint32_t g_rgctx_Observer_1_Dispose_mB8C474504055A1BD0FFF522EAE0D9C30471853DA;
extern const uint32_t g_rgctx_Observable_1_tE350483A6B46C3049CBCA4403143928B5211C240;
extern const uint32_t g_rgctx_AppendPrepend_1_t011E015F58675CE8646420208D0DE31D781B6862;
extern const uint32_t g_rgctx_T_t6B7772835664CD92F4282C948E41ABB660491ED4;
extern const uint32_t g_rgctx_Observable_1__ctor_m5034CFDB5271305F2FD2E105DE75E940FB015EC9;
extern const uint32_t g_rgctx_Observable_1_tE350483A6B46C3049CBCA4403143928B5211C240;
extern const uint32_t g_rgctx_Observer_1_t3E84FC6F8CF0B5BBF308FE1ACA30674191C9AE24;
extern const uint32_t g_rgctx_Observer_1_OnNext_mDF817169CDDDCB2D03462D0829C1DB0101A649DC;
extern const uint32_t g_rgctx_ObserverExtensions_Wrap_TisT_t6B7772835664CD92F4282C948E41ABB660491ED4_mED72F54969F0971E0C8B0CFF48DC81B162524CED;
extern const uint32_t g_rgctx_Observable_1_Subscribe_mE9571FC83B76DCF8EA20181DCDC53934D0A015B8;
extern const uint32_t g_rgctx__Append_t8B36F3B417F8D73CE3B40E929BE541DF320F12BD;
extern const uint32_t g_rgctx__Append__ctor_m2653341413F87EAFE1EFBEF3C9757D3F2A36EF54;
extern const uint32_t g_rgctx_Observer_1_t11B52F0E61A438CE684645B262D1B7DCC3EA4E4B;
extern const uint32_t g_rgctx__Append_t87963988DF3A0CFE29891DB7E6FCE78E54E0D217;
extern const uint32_t g_rgctx_T_t70F234A21B7868A9DCE3618B8CC1432424482900;
extern const uint32_t g_rgctx_Observer_1__ctor_m38716A244624EDF3C40612343853CD8BE12787D4;
extern const uint32_t g_rgctx_Observer_1_t11B52F0E61A438CE684645B262D1B7DCC3EA4E4B;
extern const uint32_t g_rgctx_Observer_1_OnNext_m7AE41488E68DFE5ADEB63FE5EC52C567B77C7787;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m435779E1EFDC555B55D200EF9D62E8B76B5FEE59;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m582CD717311816F47ECC0C64F3D467605D2DFB28;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisT_t70F234A21B7868A9DCE3618B8CC1432424482900_m137855B6D5C373C6F70D8D6EAD7D01EB72AB5F50;
extern const uint32_t g_rgctx_Observable_1_t4D0CA3FFDC9DE8C0DF47771CFDC5A7E589DBB27E;
extern const uint32_t g_rgctx_AsObservable_1_t9C89913BC6ED443F3FDBB1E645647A0B634B096D;
extern const uint32_t g_rgctx_Observable_1__ctor_mA5798D06064A4B8498A2F58D30BAAABE3CEAE105;
extern const uint32_t g_rgctx_Observable_1_t4D0CA3FFDC9DE8C0DF47771CFDC5A7E589DBB27E;
extern const uint32_t g_rgctx_Observer_1_t54FD8595E9597CFE6A2970C4B020FACBEDD09E82;
extern const uint32_t g_rgctx_ObserverExtensions_Wrap_TisT_t290DBC4DFAD5ACAF60D9C4DC856A1BC4BA7DFE00_mE14417FC34CCCF289F685440032ED11AF1046B22;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m96E4A154CAC2C9965F1E1F5A8BBA50871EED699F;
extern const uint32_t g_rgctx_Observable_1_t4049E325053946E17064B24E983A3D1BF2202671;
extern const uint32_t g_rgctx_CombineLatest_3_t7D1A4212623FE7AF538640B40B1E21051F4D9A94;
extern const uint32_t g_rgctx_Observable_1_t1F2A71E2D18F1B04F7BE379B1E88EB8268A8EBFB;
extern const uint32_t g_rgctx_Func_3_t2F46F91FEADE2B5F2F751352F178E18E189BEA0E;
extern const uint32_t g_rgctx_Observable_1__ctor_mD41EB261C82B5978482E5101CF688DED76BBBA41;
extern const uint32_t g_rgctx_Observable_1_t42C8CEE6185D500EF42BBD6FA99D59BA18B434F6;
extern const uint32_t g_rgctx_Observer_1_t9797A80DE56E780FE4287CD903B3C7F49FD43D27;
extern const uint32_t g_rgctx__CombineLatest_tC450344C766B0990BDEB8FA73E04C0E9BEC2BEB4;
extern const uint32_t g_rgctx__CombineLatest__ctor_m61EB4A017B00C740C3E00930276BF201C082242D;
extern const uint32_t g_rgctx__CombineLatest_Run_m91858613A63C7A8278B112B8D7D93F5AA5AAAA36;
extern const uint32_t g_rgctx__CombineLatest_t3B5FCD4B0B0251708F9D8BD42152D15F034BD07C;
extern const uint32_t g_rgctx_Observer_1_t574F4B4BA3FE435FC9900B21BA29EF6EA1CA869D;
extern const uint32_t g_rgctx_Observable_1_t848AC3011136C9DA7B644A5F6E370331B0D3882D;
extern const uint32_t g_rgctx_Observable_1_t93C602909C201BB94A6ED2E3C8409F73867DA339;
extern const uint32_t g_rgctx_Func_3_t3C1B1A41BEC1F993AD1DA85453F0A6C3667798D7;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t3A09AF3984C8488E3BE217CAF9F466149655D677;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_mF328ADE25A463D80902C360B063096DEAAFE4B76;
extern const uint32_t g_rgctx_CombineLatestObserver_1_tB4BDECFD2B8BF8FC1F456C0FA8695D51CBF8BA2A;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_mDC25122830C6CBC16890D53203A183001E8D0E5C;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m4A2A0B70E25757148BEDF7C90BBD0DD398115B63;
extern const uint32_t g_rgctx_Observer_1_tAD9E733A9435CD40C97B8B52991E7CBBF69FA95B;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m333034545042DE593A1E1646B0342746638DE2C1;
extern const uint32_t g_rgctx_Observer_1_t430874F4AD9C78F40A89AFDCFB0A0AD2FA96F4B3;
extern const uint32_t g_rgctx__CombineLatest_Dispose_m50135166A55EE3C930BB2972EF8F6F7286C75713;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_m0DC2DA034A2B615EC1CB0714186F5BBFED73B855;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_m6DA9EBB7485F13C846C8053E76ACE2C1ABDA8B38;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m294E72AD6315F452A2ECD79B1C2E4418DB0A2A47;
extern const uint32_t g_rgctx_T1_t4ABCA2B082FB78F07D0EC01BB003B73AE07CA638;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_mF00AB49047ADE7E0F04F834C1EA6DCAEC351B47E;
extern const uint32_t g_rgctx_T2_t9C4E83F32F7EC008172570A22ECF79FBE9B1EDBD;
extern const uint32_t g_rgctx_Func_3_Invoke_m683FD46AA151AFF0C3A0096745538EAC4CB861E1;
extern const uint32_t g_rgctx_TResult_t4ACB07D5F9FB876BDEE418F4EC3B274F1C4E9BA0;
extern const uint32_t g_rgctx_Observer_1_OnNext_m52F0CABF9C4EEB78C18A1D31D9552D68CBB3517B;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m2476E290808092163C5B5B1609C5214BDD424C04;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisTResult_t4ACB07D5F9FB876BDEE418F4EC3B274F1C4E9BA0_m3EDBA29046D5BFB1A22F8A15ECB848F419D3C140;
extern const uint32_t g_rgctx_Observer_1_Dispose_mAE1241C1304A63450FB4D2E649A534DD974BE37B;
extern const uint32_t g_rgctx_Observer_1_Dispose_m214E6D481A8F0DE1E8C1C4257928C0B4761AFFF4;
extern const uint32_t g_rgctx__CombineLatest_t7F5D1FE90F45E246A63A8A8298B50582F15654F6;
extern const uint32_t g_rgctx_CombineLatestObserver_1_tED56D9CAD1B3689A74172A46E2B36D665A5BD2E7;
extern const uint32_t g_rgctx_Observer_1__ctor_mF8A7F05D2DCF8104F800AB9C78A000353D7CE5E4;
extern const uint32_t g_rgctx_Observer_1_tFFCCDD4BAB96C322DB2F74585DFEC3D3426DA890;
extern const uint32_t g_rgctx_T_t8E317D57AA9F3824B8D7DAC92C9087B440EC0141;
extern const uint32_t g_rgctx_CombineLatestObserver_1_set_Value_m3897C6C107FC5388AD24A0FAA548626146A94968;
extern const uint32_t g_rgctx_CombineLatestObserver_1_set_HasValue_m3B148622ADD62EF20E34D9BCC845EE04FA048647;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnNext_mC9E9FE4684F9151F13E96F3BE22D0180A510EA62;
extern const uint32_t g_rgctx_Observer_1_tABC10DD9BC4B62213B14088EC46FAF486D97CA06;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_mEB750969C15B7872C81E0028E072EC199B6393EC;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_mA2B5AF3D5086C936CBAC7659BC383C013214F546;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnCompleted_m660905E897DC0B8423DDD56CEC7472ECB7F2DB9F;
extern const uint32_t g_rgctx_Observable_1_t6575CFB8DF4F3E750075B02FBC0877CE23CDE020;
extern const uint32_t g_rgctx_CombineLatest_4_t3F967B440DEB4806E8662DF5860C6D87A9A62FCC;
extern const uint32_t g_rgctx_Observable_1_t7BF0AB92AA79CC453FEF4C573CB1294F688AC01C;
extern const uint32_t g_rgctx_Observable_1_t40183C8E931BB38886CEA682C3A871F842FBF415;
extern const uint32_t g_rgctx_Func_4_t134D3B872BF89A02A573907F49E1E7B1C9965855;
extern const uint32_t g_rgctx_Observable_1__ctor_mE8650E8ECF465BA13090E823842104B3DB79EFAD;
extern const uint32_t g_rgctx_Observable_1_t2F59EFDBD485787314477E918F11C5F57F6D9499;
extern const uint32_t g_rgctx_Observer_1_tB0927386B06F11E293EDECC6D8EB6F1A60B6C82A;
extern const uint32_t g_rgctx__CombineLatest_tF9017227C05723D002BD884F1A898914FD738299;
extern const uint32_t g_rgctx__CombineLatest__ctor_m37F00A902F1DB58A3D54AF521DA3A5C83BE3069D;
extern const uint32_t g_rgctx__CombineLatest_Run_mBDED2B6D29EEE71C36326878FAC4708D3CC48AB4;
extern const uint32_t g_rgctx__CombineLatest_t4E8ADD9E2E88189EDD11C1F814985FF57880F28B;
extern const uint32_t g_rgctx_Observer_1_t643288E8750BA4A5739072E0736204D3836C2D04;
extern const uint32_t g_rgctx_Observable_1_t4A24D3A99B4681B67C9464174E11D2AF5E9AFF1F;
extern const uint32_t g_rgctx_Observable_1_t74DCC75E5809D9799966E268FB65D8BDD1ABB66C;
extern const uint32_t g_rgctx_Observable_1_t37F945B4D9DC85B097C008B94202D6F142D26F7B;
extern const uint32_t g_rgctx_Func_4_tEDE5C135ABC281F59074027431F0CD5626A6003A;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t5B9F56E0564309C20B81E454305D25682C0D92AE;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_mD648C33CF7A1A356286DE7F3B0B66546796E8F5F;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t6754453D7876D0460E678253C5B6B4947A3D776F;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_mCF02DE3234F831E4813A0731511F3907C1C9C83F;
extern const uint32_t g_rgctx_CombineLatestObserver_1_tD57827FFC51A17BDA1511E0C1DDE119C393D3D00;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_m202C15FF188FBC5AF1812572C1CA574C881D516C;
extern const uint32_t g_rgctx_Observable_1_Subscribe_mE6A1845471CC9A87494E569CAB2A708B4F11D3E3;
extern const uint32_t g_rgctx_Observer_1_t8D8D46F21D912B5B3270138FA0336D7432EE0C95;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m8B4CB4BD198C35A9D3496DD4E0F8D24101AF9ADD;
extern const uint32_t g_rgctx_Observer_1_t936CF0460839FD720F87A51F855D9F710DBBBC31;
extern const uint32_t g_rgctx_Observable_1_Subscribe_mD4A972B435123C44298F153F3239F894E81EBD49;
extern const uint32_t g_rgctx_Observer_1_tC269CCCDF00EA734CB7759353582521D6095AD5E;
extern const uint32_t g_rgctx__CombineLatest_Dispose_mB60B3EF127A7A4053D408A2263F3BDF06DB0AA78;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_m0B361ECBB5C985E7490AB92A38AC91C0C37D0052;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_mB56B58BD7CC2FC0A29F3934C13D4E3F45F498F97;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_mED07A113381FDB8DEA3F5DEDD66A5D01BD6C8467;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m2AF922300C4C3BA91B7800DB5A4651C8F933949D;
extern const uint32_t g_rgctx_T1_tD35CEE39B3A51F6F8C0FF4938BB999B69FD188AF;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m854495817C8FF97893032F89FBA1F63C8E0F05D0;
extern const uint32_t g_rgctx_T2_tD92227718C762392390BB4813C242211AEB39051;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_mBA2C041E0A545FE079E2FC75C082F2C10800F17F;
extern const uint32_t g_rgctx_T3_t90010BCDAE52891EA7A3CB9C4B4F842F20C1F200;
extern const uint32_t g_rgctx_Func_4_Invoke_m9DE9045DE01E7C6BC06D5DC98CF3EF2CB84507BD;
extern const uint32_t g_rgctx_TResult_t5A3A1140BD31EC3E7B24658B8F5AF07B257642BF;
extern const uint32_t g_rgctx_Observer_1_OnNext_m79AB2435FB47E90844BD5D822DEC88C3F2EB5488;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m65B9EC7E6C5566B3EE50B1D16EBD017A173311FD;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisTResult_t5A3A1140BD31EC3E7B24658B8F5AF07B257642BF_mFE4558F4BE58B58CB206CB1C10B8E91EFACA4358;
extern const uint32_t g_rgctx_Observer_1_Dispose_m4C4371C776C14A6CE6A7FE3184F737AF58464094;
extern const uint32_t g_rgctx_Observer_1_Dispose_mD4184DEC406689635FE85F79445668F9DE65FE08;
extern const uint32_t g_rgctx_Observer_1_Dispose_mC638D7DA9B4E5AB256D4401CC87E8C36745D8242;
extern const uint32_t g_rgctx__CombineLatest_t51B887C67F8887736C505EC0A2505CCF8E28B610;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t009874039DF373FD5859D42198FDF2F6D73FB1DF;
extern const uint32_t g_rgctx_Observer_1__ctor_m928E131E3A8688924DADA4DC223B0A7F9F619A7E;
extern const uint32_t g_rgctx_Observer_1_t120BA785E3265DAB6CDA49DD51D99B5B3D7EF4B4;
extern const uint32_t g_rgctx_T_t5B75B156E5371B46C293408A0439B1BA86C5E522;
extern const uint32_t g_rgctx_CombineLatestObserver_1_set_Value_mCF9B54D16CE9C411C8C4A96EC70328926ADE41B3;
extern const uint32_t g_rgctx_CombineLatestObserver_1_set_HasValue_mBE190D53343D22C8BF48F9F08EF867503F5539E4;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnNext_mE761629C4CFDFC8185AA14DA93609DDC2474DEF3;
extern const uint32_t g_rgctx_Observer_1_tE665A507822D580620E62E1DA0CAC69DEA015A78;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m933053C2B3F5F75A4AE9FAA3FFFDA7384E3036B9;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_m7F2EFDD35D76E23E0944FC50E850B4399242D7A7;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnCompleted_mAB49ACCA0E91B1DA153F8F42AB9B233605048BC4;
extern const uint32_t g_rgctx_Observable_1_t4BEA50F4D5FAB659824BE35FEA87D27781414F05;
extern const uint32_t g_rgctx_CombineLatest_5_t1377A756E282314FCB72D65CC2A03843511E6F89;
extern const uint32_t g_rgctx_Observable_1_t10659C63AF10E5184E262913A1D57D754B841F2F;
extern const uint32_t g_rgctx_Observable_1_t5F684AB7CDE4542363AA78A3AC5FF19DEFE0B21B;
extern const uint32_t g_rgctx_Observable_1_t84C1C7A993DDDD56CFD8140330FA4535ED6A328B;
extern const uint32_t g_rgctx_Func_5_t7FFB451EC79CE929473F534BF53357656147CB82;
extern const uint32_t g_rgctx_Observable_1__ctor_mDB74222D75F01DF550A6BB63064A7FDD43286205;
extern const uint32_t g_rgctx_Observable_1_t34E9228844EC85782B31C01D1AC20613E7A4F11D;
extern const uint32_t g_rgctx_Observer_1_t07C2FB2B2A0B70815503EFC9160EB0C76F0D099E;
extern const uint32_t g_rgctx__CombineLatest_t9B000DC62895178EE10EBB604584EDCF4B8D6CBF;
extern const uint32_t g_rgctx__CombineLatest__ctor_mC307F0EDB454C623FEEB8F2F04C478061FFE6FA1;
extern const uint32_t g_rgctx__CombineLatest_Run_m9B74233CAC64AB7DD5B87410D2EA0BEFB9084E4F;
extern const uint32_t g_rgctx__CombineLatest_t70F56F289366986B6621879E39E81A6CF73096E3;
extern const uint32_t g_rgctx_Observer_1_tA66C3C1E15B615EC71BFC50B2706A1B70166D90B;
extern const uint32_t g_rgctx_Observable_1_tDC052EFFB3E0DCC58A2A318A02AE42720201A93C;
extern const uint32_t g_rgctx_Observable_1_t3F43474ED56629DB5AC7F4C4D8D0198AC5BDD483;
extern const uint32_t g_rgctx_Observable_1_t78549BC81AC4569125720523E8D345EC5B087D1E;
extern const uint32_t g_rgctx_Observable_1_t58010D490B333AF5DBEA4A849A11F3A9F9AB3020;
extern const uint32_t g_rgctx_Func_5_t35A7D34B04ADDB58D7D710763555FEF44127A3A4;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t205A03D7913A6EBA817F7B657B7D9407A444B04F;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_m1A361B84F8912E8CF51932CB9B633617E33EEC15;
extern const uint32_t g_rgctx_CombineLatestObserver_1_tC355C66049FF50EDD68A6A9D526678BA12281CA7;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_m7EADE3AC11114F9B8FBF730E483617431F5A39FD;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t3118E6A426EBC0B4431F769FD86C15B75457D5C9;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_m0DC62A3EBA5CD6BB8C7A61CD87650F8C6DC9C060;
extern const uint32_t g_rgctx_CombineLatestObserver_1_tD3447521D8AE6B5E2ACF467166829B925AA166B8;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_m9DCE6A98A3D8A7CAB3245206CB7B8C2242752807;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m6E729FF4616965F0B969E0F7685AEB9B76AFF4C9;
extern const uint32_t g_rgctx_Observer_1_tB0F91303A370A2CFBF0623C8301880E030DD6F14;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m417BA6E335B501C3457E5E0E4C419D6A5ABE600C;
extern const uint32_t g_rgctx_Observer_1_t317986F1867C06CB892075924DEEFAFB9AE1A79A;
extern const uint32_t g_rgctx_Observable_1_Subscribe_mDAFE873481B213BE540DB3DD0C172D2237130CC1;
extern const uint32_t g_rgctx_Observer_1_t6C419927DEFAC475196DB91ABCF4317E290DDD34;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m434BB474F782B12117873C880CEBD51517D4D187;
extern const uint32_t g_rgctx_Observer_1_tEB337ABC3FD1324CFC79B1C50D7EFEC91BEB7093;
extern const uint32_t g_rgctx__CombineLatest_Dispose_m8BF228C104C571474D7A9DCC1AB2DBBECA2F8931;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_mC1F2C65A6E7E33C1A3DF1F4FC4CD368AE688DB2F;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_m3B180797CCACBCC6C444694CCE6531BC875C5781;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_m1CDE6A2C7494A362F2DBD90D0FF8D53C65C3AE85;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_mEF90939922EA6F7325413F5BC895D8ED5A22ADF7;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_mB3127E40B33188667A7E3896651AA105D3AE8B22;
extern const uint32_t g_rgctx_T1_t4DF6A5461BB932F99447AA20A5483F811335CCA8;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m4E1854C18231C8757BF26B7BAFC7F818B5D3305A;
extern const uint32_t g_rgctx_T2_tCF667BE39E0187C98EA17C82C558072286D870C9;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m1F6B0AD8759F67809CB8C1D2AED38CA553B37F01;
extern const uint32_t g_rgctx_T3_tCDB786CFA5570518D4402C697A491859AEB86DB7;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m209A61888265B0456ACF19D8FD3E7E92C58E981B;
extern const uint32_t g_rgctx_T4_tAF167C28B753440D7EC40B2929B547CE3E6730B2;
extern const uint32_t g_rgctx_Func_5_Invoke_m0D4BC1BE1CF6ABD569A26BD391AC4A6739A4B004;
extern const uint32_t g_rgctx_TResult_tFD456F356E3E9E077613870926FEA02FC5A76B20;
extern const uint32_t g_rgctx_Observer_1_OnNext_mEB8FF677A2217AF9F2EC25DC55006C1CF41B3570;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_mCD2A67FF845849FC16D497FB9A7C6C00BD5FD679;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisTResult_tFD456F356E3E9E077613870926FEA02FC5A76B20_mED3F39325A8E5F67315F99A827611E8C875CA9A5;
extern const uint32_t g_rgctx_Observer_1_Dispose_mB7100E3A9F3EFBBFA662B9A21E021B690B541DF0;
extern const uint32_t g_rgctx_Observer_1_Dispose_mFE24E4ED2D049E65FDB3CFDCE8916A27B2E5536F;
extern const uint32_t g_rgctx_Observer_1_Dispose_m1F3BB84E7765824A069E5D0B8B10BE0C50F92F2F;
extern const uint32_t g_rgctx_Observer_1_Dispose_mF9E761DAF7B21223DD55E972152D85EF2B3A35F2;
extern const uint32_t g_rgctx__CombineLatest_t78245E13EE248A9CED6D49256B0361AC3623C886;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t1322EF0A75DCD5954F2C2225F5619F2627887759;
extern const uint32_t g_rgctx_Observer_1__ctor_mA81590C134C24E7CD5F996030E1DFA8734C01591;
extern const uint32_t g_rgctx_Observer_1_t41449AB961B38A3C4B1F813408F15812CCD71BD7;
extern const uint32_t g_rgctx_T_t16EEB313FFE6F309C1138DDD079A91F076F88A5D;
extern const uint32_t g_rgctx_CombineLatestObserver_1_set_Value_mCB79F749656F391116CBE5F62D5570495BC2A3B6;
extern const uint32_t g_rgctx_CombineLatestObserver_1_set_HasValue_mC697A000C64CFD01EE275C54A65E48818DF88732;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnNext_m5B7B1DC814693B7A8BC9F2BE49CBB6351FD44258;
extern const uint32_t g_rgctx_Observer_1_tB994EC320BE8AF18FBBD0187245D3A012774FE03;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_mD7F50A81BA0081CEFC0F1480762B1FDEDF8EBBD3;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_mDEB0205CC4117F1FBB2706998E958814981D8802;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnCompleted_mD9F0DA27F0D1AC3654BB73AA1A04D52050749D6F;
extern const uint32_t g_rgctx_Observable_1_tA1380280E1C2C80A37EBF089CAFA56776503526A;
extern const uint32_t g_rgctx_CombineLatest_6_t26332BB2835AB1B78A57EAA673EEEEB66D0094FD;
extern const uint32_t g_rgctx_Observable_1_tE4458285EB409A7BFB513034B3D706A4A2D5C9A6;
extern const uint32_t g_rgctx_Observable_1_tDC21682B7AAA2AF8E401D7DE4C0E9EE28DE2F211;
extern const uint32_t g_rgctx_Observable_1_t27854630A29FD5C399FE9C6D744C5466630D98D0;
extern const uint32_t g_rgctx_Observable_1_t3A20446C6ABE19C569653D5C378C0AAE20BF372D;
extern const uint32_t g_rgctx_Func_6_tD0CE023CFD8454D34AFF936AF777ED8795296180;
extern const uint32_t g_rgctx_Observable_1__ctor_m0AC988518A10FF8EB67AFB39EB86EDEFAA3A5CB8;
extern const uint32_t g_rgctx_Observable_1_t50FD5B15AF045766832502E4E51B0FB041E772C5;
extern const uint32_t g_rgctx_Observer_1_tF4243266EBD87461DB42F6350A6D1C96F94FA01D;
extern const uint32_t g_rgctx__CombineLatest_t77C1B04C538BC02A0A6012B3CA0C61E0C19CF60D;
extern const uint32_t g_rgctx__CombineLatest__ctor_m1AD79D668FEF5F415ABEA0B5D2FE080EDDDA7FFB;
extern const uint32_t g_rgctx__CombineLatest_Run_mD46D7F1C2F7E9D8FC7ECED7AC90EB70688284313;
extern const uint32_t g_rgctx__CombineLatest_t79E116878F7204C96116CCA2A4F2A01FEFF1F167;
extern const uint32_t g_rgctx_Observer_1_t74D316C9B93293287E0F9535E3DC5F7376E10C7C;
extern const uint32_t g_rgctx_Observable_1_tA6692B211515FDC640B7184992A88893911A9CA4;
extern const uint32_t g_rgctx_Observable_1_t44F4B19FFFF81A7E94151DD44AEF9F05510B99E4;
extern const uint32_t g_rgctx_Observable_1_t9A4A2B511C2CEDC1C03FDFAC4ABF2DF9AEE082F3;
extern const uint32_t g_rgctx_Observable_1_tD4A402509F9CF51230B4A2908F94C1292B536AEF;
extern const uint32_t g_rgctx_Observable_1_t2043DC6B7CE7D2306085337ADE3D5F1AA20745AD;
extern const uint32_t g_rgctx_Func_6_t4F09ABE77D95E9158300EA28C5F3998D782DA076;
extern const uint32_t g_rgctx_CombineLatestObserver_1_tA8E28C0FFA5EB4685EDAB09C2BA646CF3269B103;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_mA7CBAFAA71A7B1CE923655AD3C2BCDE1B26F5FC8;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t03333869078481CDCC1DF2A370617BE3C525274D;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_m59F5FA508C0B4F0FA8EE439AEF2A083D787DB624;
extern const uint32_t g_rgctx_CombineLatestObserver_1_tABD55F7E17D6EA75CD3F40C2ADD53FA9A4EB8A79;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_m6079E3A7400F38951E1FFED4F63CB6B02759A38F;
extern const uint32_t g_rgctx_CombineLatestObserver_1_tA9F519DB456E6FA8D9F2F41A9A5CECF6CE27A73C;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_m2E0FD6945BC160BBE34FD9BD68B7004F04A9A2B5;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t406EDC9426B6857B16AB49096C789961736063BD;
extern const uint32_t g_rgctx_CombineLatestObserver_1__ctor_mECB9426CD51DA93D987DFCA331177C5791B7D1EE;
extern const uint32_t g_rgctx_Observable_1_Subscribe_mBFEF0C4B53471680529EA8B95DA76E94437516B8;
extern const uint32_t g_rgctx_Observer_1_t3764E6E53C2E6BFD368E6D1702BF92D467F648F8;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m4CD9642E1498B326445A09AB39DAB89234B00511;
extern const uint32_t g_rgctx_Observer_1_t246155898CD63A957C41BF94E8FD3FBDAA187A96;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m7F1087E5134C881848E3EE66B6DBD9836A55EF47;
extern const uint32_t g_rgctx_Observer_1_t608A394E38174F642F06EFAFF2B991444991B545;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m17CF2C4EA0ABB37008B401874C7D0443AA4CD314;
extern const uint32_t g_rgctx_Observer_1_t772459B9E9E914EB26F74006D515E32759DB3494;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m42757DDE40F85552622CAA9BED14AB2B93794DEA;
extern const uint32_t g_rgctx_Observer_1_t5CB9BAA333229EE6E92793F0E9BE5496DF0A63DD;
extern const uint32_t g_rgctx__CombineLatest_Dispose_mB4759B001C5FD5561FDEAC45EFF102DCE19BB411;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_mD3996759BA4D8EE9828DA27724D0897064B31A9F;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_m029B5541B01890F4170E93132B35BA8E3DF9FAE1;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_m1CB6209F18E653BD0590676A6465C5AD6146B6E2;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_mF4FE11E432ACD4175DEC2266ED208E9AAC5B918E;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_mF620BE3AF24FDFDAD39682DD9B94C89910E86CD1;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m35A3228456D71B9E78DBE93688B90205F90E32A5;
extern const uint32_t g_rgctx_T1_t71385CCC9BB011C0F41023B884B0A0FD62DA3596;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m13B6184EE611C995BA8B83797F1AA7AE76F1D051;
extern const uint32_t g_rgctx_T2_tEF0DFEE91CB77644BB8254628C211D4A2D170224;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m0205914F0026BB3BE9882B684B1F88A39853CA58;
extern const uint32_t g_rgctx_T3_t2456AA0EFDDA2A6D2D8A5FCD7CA9BF8056153894;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_m6B02F8A7B28B12AC06A2725362B98A4386750D8C;
extern const uint32_t g_rgctx_T4_tC511F19CEA2B8E235A0C5BD7CC185B0BE5B6F4F0;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_Value_mE34F0530E908CBC5ECBF3E0B4CEB05F03D62089E;
extern const uint32_t g_rgctx_T5_tE80AFB559C84C291FDAC4EBA053697A63E86BF62;
extern const uint32_t g_rgctx_Func_6_Invoke_m7F20B457ADCFB180A0C4ACE10E54BFF5AB2D7373;
extern const uint32_t g_rgctx_TResult_t6124674803485AEE3644C2B9A30472976B7A2118;
extern const uint32_t g_rgctx_Observer_1_OnNext_m5F4B85C694FBA612E666766AC8E5DC6521608674;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m3353BED85CB8C4306F5DE568764C9BE3C1500B6D;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisTResult_t6124674803485AEE3644C2B9A30472976B7A2118_m8FCFAF8B4AE9524E3D64317E7D908FFF9A54F423;
extern const uint32_t g_rgctx_Observer_1_Dispose_m43B413B2E4D2A234EF1E3C0CA55FB944DC0209FE;
extern const uint32_t g_rgctx_Observer_1_Dispose_mA286717AB42CCDCF6CF11DD92AE1A134E1AB7E94;
extern const uint32_t g_rgctx_Observer_1_Dispose_mA1B4528120B0A765E179B8C0DA83AC059823CFDD;
extern const uint32_t g_rgctx_Observer_1_Dispose_m8C60E3D0E8232BD5FC28772B26C74BDBB098827F;
extern const uint32_t g_rgctx_Observer_1_Dispose_m9E4F70C79247EA0EF87D8E789DDA7CE994652FF7;
extern const uint32_t g_rgctx__CombineLatest_t691E0D1CC93AF657E833F68A7FE3B9AEC9E5BE4B;
extern const uint32_t g_rgctx_CombineLatestObserver_1_t1F22500C1B6D9FB51BA80D8199003BAA1BDA53EA;
extern const uint32_t g_rgctx_Observer_1__ctor_m4D27D08352C2B5458810D53CE793B74C3FB88E39;
extern const uint32_t g_rgctx_Observer_1_tFBBA348EBE4E922B4940E50087D279F206D84DB8;
extern const uint32_t g_rgctx_T_t4191D38AB573101A0899696CCA9CB3570333535E;
extern const uint32_t g_rgctx_CombineLatestObserver_1_set_Value_mEA0B7F2A18826A53E962A3F5756701533D07F375;
extern const uint32_t g_rgctx_CombineLatestObserver_1_set_HasValue_m1FB0770060F4A36D41173D3E4C860BDCD8273055;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnNext_mEE0844BCB963C766658E578E0E3499E2248C2063;
extern const uint32_t g_rgctx_Observer_1_t26DE593E6358A5729F4FEF5BCE25A6758EBF4B3B;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m9FDD3BB4D389FFEE2AE692DFF5A1A4447C2356AA;
extern const uint32_t g_rgctx_CombineLatestObserver_1_get_HasValue_m184D4E73C0EA07FED368E0C9F635637C3A49381D;
extern const uint32_t g_rgctx__CombineLatest_TryPublishOnCompleted_m48AB249911FEC8708CA943D79FE25EF8AAFAC619;
extern const uint32_t g_rgctx_Observable_1_t1AF2F514569FE93FC7DB9AD530C10207BD193B80;
extern const uint32_t g_rgctx_DistinctUntilChanged_1_t6361825B0330507076AD2A98D3144A9973EB8DC7;
extern const uint32_t g_rgctx_IEqualityComparer_1_t7069003127098176743C81A47DFBB01A7539F015;
extern const uint32_t g_rgctx_Observable_1__ctor_m8D51078B2077545E5F86717ED46C9B5712E9C56D;
extern const uint32_t g_rgctx_Observable_1_t1AF2F514569FE93FC7DB9AD530C10207BD193B80;
extern const uint32_t g_rgctx_Observer_1_tAE3577A5E7BD8C43BFED6F5A6D281ECADDCDDD9E;
extern const uint32_t g_rgctx__DistinctUntilChanged_t1D697F9D736266B045F9790DE1D0A4FFDEB4BFF1;
extern const uint32_t g_rgctx__DistinctUntilChanged__ctor_mA3490B62E0DF6A21383B00DA3F4FF1A8BB951A65;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m4887B1477D61074036CCDB605603082D6B1C66F6;
extern const uint32_t g_rgctx_Observer_1__ctor_m826DDAC58D17A36029E6845B15B74A132417310A;
extern const uint32_t g_rgctx_Observer_1_t9C0DDF4E67FEB47A4E56D0585EFB6257AD0940D3;
extern const uint32_t g_rgctx_Observer_1_t9C0DDF4E67FEB47A4E56D0585EFB6257AD0940D3;
extern const uint32_t g_rgctx__DistinctUntilChanged_t6157F25753A8A22BE978623517A1095ADF318CE6;
extern const uint32_t g_rgctx_IEqualityComparer_1_tCE830AEA422D9C801F02D25457788DD6443BE1EC;
extern const uint32_t g_rgctx_T_t2C65BDA191B91404B31B9D02E7BDB85EE18319FC;
extern const uint32_t g_rgctx_IEqualityComparer_1_Equals_m2C426CB7720287E8B2058649D35EAB6EF59B2E4A;
extern const uint32_t g_rgctx_Observer_1_OnNext_mF4B05AEDE32E978186B40C64E2073F50CF8AF71E;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_mE46F4C99F15B95FF1A00AB2B226F8E6443DE3D36;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_mE082566206C2BCF17EE520CD5AAE3972C6E90599;
extern const uint32_t g_rgctx_Observable_1_t83877C58DE3CB57D07064B052F18E302146135A7;
extern const uint32_t g_rgctx_Select_2_t674D8A7083607803A6F606AC23388CCD325C7E06;
extern const uint32_t g_rgctx_Func_2_t229F7EA0A6575EADF592503CE48A4B2F7215EC53;
extern const uint32_t g_rgctx_Observable_1__ctor_m9DD20E0286C3F5E60CBF8589002F5D379AC58A34;
extern const uint32_t g_rgctx_Observable_1_tC3250011CA9CEFB56AEBF0ECE080FC208CC29265;
extern const uint32_t g_rgctx_Observer_1_t40D4646DD8CDD4580C2C2C04FA0F43F0934D2DE2;
extern const uint32_t g_rgctx__Select_t926C05564543F09857663A5396D4A3FC28A79D9D;
extern const uint32_t g_rgctx__Select__ctor_mD31EEC2A72146F768F943A3BB97ADA75446B96E4;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m40701EFEA379B0ACB12FFFD1A840705DCE8E6B91;
extern const uint32_t g_rgctx_Observer_1_tB217447CA593C41EB9D3F7CF803365EE33C551A4;
extern const uint32_t g_rgctx_Observer_1_tC888FF88E69C1539C72380CC77384A4E901C524F;
extern const uint32_t g_rgctx__Select_t75AC76CE95FE6AD80ADD2485F6DD9652D7DDF3D7;
extern const uint32_t g_rgctx_Func_2_t4F0A96C051493053C027283F4064C146E9D20041;
extern const uint32_t g_rgctx_Observer_1__ctor_m093521ECFDE655C66404684E8B877FBCC53FAB4D;
extern const uint32_t g_rgctx_Observer_1_t4775C0F41688B969231F0EE0EF45BF0F98F230B7;
extern const uint32_t g_rgctx_T_tF0ED22EBACB7F8721359FC23AFCADDAD1D7157B5;
extern const uint32_t g_rgctx_Func_2_Invoke_mF5BB4873180F700C4239F84320C7C34614E10806;
extern const uint32_t g_rgctx_TResult_t4B9948CE9AD3725E05B82EBD7BD966A9645A1B21;
extern const uint32_t g_rgctx_Observer_1_OnNext_m0F73DFC17EAFA80B0D5B5AC4C5E4D9A33B20DA77;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m63BD4C4CD60E5A92BF087F7280DF955D92E44EE8;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m9E478DB66DC6E4151A523343AD8B2A0D42C9C43B;
extern const uint32_t g_rgctx_Observable_1_tE2673702083B33FB79393FC07B13361B8F1C7C6E;
extern const uint32_t g_rgctx_WhereSelect_2_tF264C86718AA3F09AB01CAFCF25C9D7D04CED6B5;
extern const uint32_t g_rgctx_Func_2_t5897F4325C75E0419D345E5EAB23B69184FFCC79;
extern const uint32_t g_rgctx_Func_2_tAD4757FFEFFDB194CF707003BB4A5937B1C2C28F;
extern const uint32_t g_rgctx_Observable_1__ctor_m02CAC1E8B8B6D5BEF18F7DD596820A693E67ADD6;
extern const uint32_t g_rgctx_Observable_1_t85B6EEEF4896349CFDA8AA906E8EB0DCB369E3DF;
extern const uint32_t g_rgctx_Observer_1_tC17F6011A21ADC32516A6C0F35B8ED9A50D90AE7;
extern const uint32_t g_rgctx__WhereSelect_tD6E00FD4924CD2B90FEDD42C59533A68812E3C1F;
extern const uint32_t g_rgctx__WhereSelect__ctor_m9E869F8045C3722FF1622B57439EBFECFAD750A9;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m527D21EA5EA5AD65490569AE70975B6317E74738;
extern const uint32_t g_rgctx_Observer_1_t8A4C6557A75F4585C26FB2DFDF7EF1F38339766E;
extern const uint32_t g_rgctx_Observer_1_t97E9BA4EB4CA6A98A0F39A5ED6E1F6AFB9E381E2;
extern const uint32_t g_rgctx__WhereSelect_tC42328B9E769312BFACB10623D6EDB57A79F297B;
extern const uint32_t g_rgctx_Func_2_t2D31DE0B577FA777A0916066093EE293DCD3B70A;
extern const uint32_t g_rgctx_Func_2_t35B85DA5AC6856FE1AA591FE6BB8C7038BC4ABD6;
extern const uint32_t g_rgctx_Observer_1__ctor_m888E416255B728CBF8064C7C12A84610C62FE94B;
extern const uint32_t g_rgctx_Observer_1_t5B639E284E9B1442BF7B955C0A90AC5AA62FCA5B;
extern const uint32_t g_rgctx_T_tA42DE64B82DA9E61799E3941A8A5D8BA395A8F3E;
extern const uint32_t g_rgctx_Func_2_Invoke_mAA861B941BC1EEB3033E86C845FA9F26619BABCB;
extern const uint32_t g_rgctx_Func_2_Invoke_m5264D5ABD1348852164C861A7D3046446C0639CA;
extern const uint32_t g_rgctx_TResult_tE62F8BDC705673C4522B8ACC47E6C2C97842A337;
extern const uint32_t g_rgctx_Observer_1_OnNext_m19A7E0C7F4F92D91D9AB7035E1A2E7317A8D5064;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m04488F9978B598DC773BD7B4BC82D2CF7C633B17;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_mAE1A8DC2CA92D4D5FC148063196E059EC4CA77C5;
extern const uint32_t g_rgctx_Observable_1_t95B6301C8C90B42E86AC5B44AC2459580FF6FD65;
extern const uint32_t g_rgctx_ThrottleLastFrame_1_tEFA78A53F330BA33FFEA626812E76927F43C102A;
extern const uint32_t g_rgctx_Observable_1__ctor_m01398779B1B7BD08C39FDBF1B83E4A4B4E574F26;
extern const uint32_t g_rgctx_Observable_1_t95B6301C8C90B42E86AC5B44AC2459580FF6FD65;
extern const uint32_t g_rgctx_Observer_1_t652430F7AF8A5DCE5838DD90563E96D48362FE6A;
extern const uint32_t g_rgctx__ThrottleLastFrame_t25118469972B49208A0325132B259E11E48DE83B;
extern const uint32_t g_rgctx__ThrottleLastFrame__ctor_m5CC5F67C0BB935D4054AE03F1DD34831FD32EAFB;
extern const uint32_t g_rgctx_Observable_1_Subscribe_m18ECF05921312B1D07773764022E5DEB5C7D74A4;
extern const uint32_t g_rgctx__ThrottleLastFrame_t0321103BAA4715A4AAF8A03A6638F74C7E487D19;
extern const uint32_t g_rgctx_Observer_1__ctor_m3485E980A023FE0E5F10CB92724FE029018C41BE;
extern const uint32_t g_rgctx_Observer_1_tE483A187B87C3EF44E91D83C09F8969C4024B684;
extern const uint32_t g_rgctx_Observer_1_tE483A187B87C3EF44E91D83C09F8969C4024B684;
extern const uint32_t g_rgctx_T_t857015B746E3F926FAC15FEE261319B672CD3FAE;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m96A8AA5F908A7A52DE834B864A4F5F6536DC42B8;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m1783AA2602CC78558CB293A0035E30B4455E4408;
extern const uint32_t g_rgctx_Observer_1_get_IsDisposed_m47AA6E0C6525AF31104999DCAAA442AF5B954E17;
extern const uint32_t g_rgctx_Observer_1_OnNext_mC6FE85AB13086ADEDA4275A59F19B7C5FF59BD07;
extern const uint32_t g_rgctx_Observable_1_t7503D81415AF1FDB4DF5D621B3B1D8CF235E5066;
extern const uint32_t g_rgctx_Where_1_tC65CF742A6195B9A85C295B7BBB27E8EA7D60288;
extern const uint32_t g_rgctx_Func_2_tCCAB330147CD4141C484BAD7AE966EEA89F28CA4;
extern const uint32_t g_rgctx_Observable_1__ctor_mC66F031DDC28C2B1090A1C1CB7AE5AFA927BA3A4;
extern const uint32_t g_rgctx_Observable_1_t7503D81415AF1FDB4DF5D621B3B1D8CF235E5066;
extern const uint32_t g_rgctx_Observer_1_tCD666956E9962E9C2A6AFBF42A269E1AA1DE120B;
extern const uint32_t g_rgctx__Where_t05CF07A3A5DD36A670BA3BF3BFE3D855A3083FFA;
extern const uint32_t g_rgctx__Where__ctor_m11A89A68B0B1B194018BBC58B4C3F13E7B85B99B;
extern const uint32_t g_rgctx_Observable_1_Subscribe_mF354693D50299E0EA54FF8FE566C212D994588DB;
extern const uint32_t g_rgctx_Observer_1_tECC88F4F2E701F89663C5A897178694EEFBF48B9;
extern const uint32_t g_rgctx__Where_t1279CA26C6099767DD849C1BBFE18DAAA009690A;
extern const uint32_t g_rgctx_Func_2_tEA51C33A8CE8AECB23BA9F5304CF84CF26859120;
extern const uint32_t g_rgctx_Observer_1__ctor_m04E00DE4C7E43D2085ADB4581EBF9A43CD78C66A;
extern const uint32_t g_rgctx_Observer_1_tECC88F4F2E701F89663C5A897178694EEFBF48B9;
extern const uint32_t g_rgctx_T_t00E952B4F6CA8D2E1FA07884E364E95B9627D4B3;
extern const uint32_t g_rgctx_Func_2_Invoke_mD1384D0CAA7833151BC2600F5F40013508A5395B;
extern const uint32_t g_rgctx_Observer_1_OnNext_m498C0BCAA2932A63AE82C6243DE0EB096FA7088F;
extern const uint32_t g_rgctx_Observer_1_OnErrorResume_m77826638DAF09FBB38F91A7E78D4A2102791B41B;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m477ADFC57A431E14D1B21482BEB96C51CB031C07;
extern const uint32_t g_rgctx_Subject_1_t6340E78A3E052A7A1B110E3B072FCD6276F3072B;
extern const uint32_t g_rgctx_Subject_1_GetVersion_m8062A496BD3074EA52D1C17D9FBFF5E1B812A274;
extern const uint32_t g_rgctx_ObserverNode_t86B9850248602D0CFFF0F688B8A8C04D7C5307A6;
extern const uint32_t g_rgctx_Observer_1_t708D01385109B3812DA3B28585F2DFE320526A33;
extern const uint32_t g_rgctx_T_tE0C7BE9070BA949F567D012E68A8990F581509CD;
extern const uint32_t g_rgctx_Observer_1_OnNext_m3C155916F4BAD8910EF6FC72A889E0E984950CFA;
extern const uint32_t g_rgctx_ObserverNode_get_Next_m8105832E8531B871FE972D38936FA8E55E5ED0CC;
extern const uint32_t g_rgctx_Observer_1_OnCompleted_m313B407493014D62C89C2283E46DA0F55D9F68C7;
extern const uint32_t g_rgctx_ObserverNode__ctor_m5735252B53BDF608B3C1F2B72744340ED3601843;
extern const uint32_t g_rgctx_ObserverNode_Dispose_m1AA9B6D4D767B293BC575848556BF36FE3CB3445;
extern const uint32_t g_rgctx_Subject_1_Dispose_m0163E3DA8ECA2168F6ECC9C6E38F1FE35A8F9E13;
extern const uint32_t g_rgctx_Volatile_Write_TisObserverNode_t86B9850248602D0CFFF0F688B8A8C04D7C5307A6_m3A093D4C9E03ECB5AA4E06556230B12A4D32CD5F;
extern const uint32_t g_rgctx_ObserverNodeU26_t2B6D3889D0878738B4F675AB74C7E63D624337FF;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisT_tE0C7BE9070BA949F567D012E68A8990F581509CD_m5576A7E1E298FCE9E298C27F436B043453D49A70;
extern const uint32_t g_rgctx_Subject_1_U3CGetVersionU3Eg__ResetAllObserverVersionU7C14_0_m00FFA81B66E67BCCC36959604583A20DECE96B0E;
extern const uint32_t g_rgctx_Observable_1__ctor_m38DF34C266387E05F88673EFC196526DB050231F;
extern const uint32_t g_rgctx_Observable_1_t86925E9765EE57AE87A32547FA8D6310ECE32C39;
extern const uint32_t g_rgctx_ObserverNode_tC8F300063CFD4A2D8C549E8E9011DB795D68881B;
extern const uint32_t g_rgctx_Subject_1_t873E83CB245B545BFA50A10A9C4B980453A765A0;
extern const uint32_t g_rgctx_Observer_1_t7F50CCAA9E7BD6089C48344E814ABB3302BFB0C9;
extern const uint32_t g_rgctx_Volatile_Write_TisObserverNode_tC8F300063CFD4A2D8C549E8E9011DB795D68881B_mF925EB291AE4060ADFBA76B7E1AAC3E797D274C5;
extern const uint32_t g_rgctx_ObserverNodeU26_t0BFBC13C1F133C1D3C4E4A14265BCA222B05A75B;
extern const uint32_t g_rgctx_ObserverNode_get_Previous_mE69E519774EF98F0CF5903FBF9EA65BDA914FA82;
extern const uint32_t g_rgctx_ObserverNode_set_Next_m630C442C28695ECE2749E0C6E224BD3BF3EDE564;
extern const uint32_t g_rgctx_ObserverNode_set_Previous_mB931D59A3E741C435B3A0F386A5C45F0FE1FCC5F;
extern const uint32_t g_rgctx_Subject_1U26_t9C3AF8D01F37CA1A33BFA2DF7E75C610355302DA;
extern const uint32_t g_rgctx_Subject_1_get_IsCompletedOrDisposed_m2A031D51561B6CB3D0228F1A76F3693D85DC61D5;
extern const uint32_t g_rgctx_ObserverNode_get_Next_mAF251235888CE80A6931972D3B23E0C278FDF08D;
extern const uint32_t g_rgctx_Observer_1_t66CE3145318E0F75CC88D6CD090931549281D3DC;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1_tAAD2A1CE0C709FB2EF4B9E3C490F3B8CA2193B82;
extern const uint32_t g_rgctx_U3CU3Ec_tC70154AB2FCFF704053DA28C862ABC08F43F1961;
extern const uint32_t g_rgctx_U3CU3Ec_tC70154AB2FCFF704053DA28C862ABC08F43F1961;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_ctorU3Eb__3_0_m015CCA4F94DA20845E9C11E5E2C01DFC37D26A8C;
extern const uint32_t g_rgctx_Observer_1_get_IsDisposed_mAF13FB10752ACF4C695F2AECCD764E79EBD718DE;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1_Dispose_m239951B6C09C97698A6E6E8DBA1CAB596DB4E000;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1_MoveNextCore_mFFEF326204F44789249B72745B15E3F7907B9202;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1_DisposeCore_m0E6EB46237F368AB08C958748A5225668EC50C7A;
extern const uint32_t g_rgctx_T_t620FB54A285D6E338EDEA4E1869C480755265E83;
extern const uint32_t g_rgctx_Observer_1_OnNext_m994AEBFD5ADE7A5ED0FD080E50F649658C8CA474;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisT_t620FB54A285D6E338EDEA4E1869C480755265E83_mB0536CB1EE9A67414DC0B8EB40DE935A9E002EB5;
extern const uint32_t g_rgctx_U3CU3Ec_t773221C60059073D269591D57949D829D3572B00;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mC68453518D786AF216DBCC4BB0BE8350E3A036EE;
extern const uint32_t g_rgctx_U3CU3Ec_t773221C60059073D269591D57949D829D3572B00;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1_t70C9A04B29AF0284925DC720FDB68BA5D992DBD5;
extern const uint32_t g_rgctx_Observer_1_t4EE6637CEC2BCD3F60446650F7F1E90B5DA7F24E;
extern const uint32_t g_rgctx_ObserverExtensions_OnCompleted_TisT_t728FF2C049333D3E0181F7C0BCBE29572ED4F68E_m6CF5BBB9B63C8445E1D4A96C33F7C36677D052F9;
extern const uint32_t g_rgctx_CancellableFrameRunnerWorkItemBase_1_Dispose_mAC540A539A08650C9ED4C703AD5FA26AF41DD458;
extern const uint32_t g_rgctx_WeakDictionary_2_CalculateCapacity_m42F2F40F93EA2851D263E1ACF3724DAE30F25690;
extern const uint32_t g_rgctx_WeakDictionary_2_t540FF5F91ED85CE58D24A82FF962CF1DFADE98D8;
extern const uint32_t g_rgctx_EntryU5BU5D_tDB69CE4EEA91CB809EEF77106C255EC514812A34;
extern const uint32_t g_rgctx_WeakDictionary_2_t540FF5F91ED85CE58D24A82FF962CF1DFADE98D8;
extern const uint32_t g_rgctx_EntryU5BU5D_tDB69CE4EEA91CB809EEF77106C255EC514812A34;
extern const uint32_t g_rgctx_IEqualityComparer_1_tA0CA9CD4D02C9644B414AE5ECD258FBA6DE4438E;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_mB2DB11BE0DCBAD070F43E353E0EC50111024229E;
extern const uint32_t g_rgctx_EqualityComparer_1_tEB7BB0CFA450290B959D0A4E9DF5DFE124C6BC01;
extern const uint32_t g_rgctx_EqualityComparer_1_tEB7BB0CFA450290B959D0A4E9DF5DFE124C6BC01;
extern const uint32_t g_rgctx_TKey_t84B496F75F509224AD17B1162F70EC702E7615DC;
extern const uint32_t g_rgctx_TValue_t6D0024E6EAFC7F538B6D6D8F35FF1B4D63F9D216;
extern const uint32_t g_rgctx_WeakDictionary_2_TryAddInternal_m764AB622C264DEA6C1D3F4F31994D941E59E78E4;
extern const uint32_t g_rgctx_WeakDictionary_2_TryGetEntry_mEF8A94B937AF7E5B689A2BB19C94D1CB371F2ED2;
extern const uint32_t g_rgctx_EntryU26_t158683BE6BE05DC55DCF3E1A63F1EC401D285D37;
extern const uint32_t g_rgctx_WeakDictionary_2_Remove_m4021E1810528210321A9F076AC6A8C9D326729FB;
extern const uint32_t g_rgctx_Entry_t808E2F2564F4F01A96D31A2FF585B384165978AB;
extern const uint32_t g_rgctx_WeakDictionary_2_AddToBuckets_m0918718E23869CB29BB7C6F141093EE5DB38D719;
extern const uint32_t g_rgctx_IEqualityComparer_1_GetHashCode_mE25A2B7F3AC444383410C63295296AF738B12F69;
extern const uint32_t g_rgctx_Entry__ctor_mF71D709F310C06DB365B2C9897E52D7008849547;
extern const uint32_t g_rgctx_WeakReference_1_t8CCAF22BD38296690DBC8092A053CCFFBD1208E7;
extern const uint32_t g_rgctx_WeakReference_1__ctor_m632C3012D9AFFEB74A889E1C6BB33D3F27A993FD;
extern const uint32_t g_rgctx_WeakReference_1_TryGetTarget_m0053FD412E0EC01AC28B19ADDE2152B27B41AD44;
extern const uint32_t g_rgctx_TKeyU26_t83D752789465DF2905E459C7E7AF8BF93CEE2CEC;
extern const uint32_t g_rgctx_IEqualityComparer_1_Equals_m63319D71723BA7BC17F1ADBA3E3617C2726E83B4;
extern const uint32_t g_rgctx_Entry_tADA07D082A8438FE2E10DE226B68298F5767C7FA;
extern const uint32_t g_rgctx_WeakReference_1_t4141C09069999F31AA962C0CF3034B6DBFE12B40;
extern const uint32_t g_rgctx_WeakReference_1_TryGetTarget_m9CAC34003420661B778ED9BA79E2CCDD06897B85;
extern const uint32_t g_rgctx_TKeyU26_tF46A6EFB6706C8F8CE7D7717D81ECF999BC04042;
extern const uint32_t g_rgctx_TKey_t0DB0AD07607E325090AAF0064CABD2DC81ED3915;
extern const uint32_t g_rgctx_Entry_Count_m95B9E3CC12C858F1FFEB86F820F967B74BA80093;
extern const uint32_t g_rgctx_FreeListCore_1_t7AD56D3C302E5FF7A137A94EBC0C06BC84CCB002;
extern const uint32_t g_rgctx_TU5BU5D_tABF391C9273DB65CAAFAC7C677ECA65FE83D5A7B;
extern const uint32_t g_rgctx_Volatile_Read_TisTU5BU5D_tABF391C9273DB65CAAFAC7C677ECA65FE83D5A7B_m32872C64F7FB728637181F22C51D58D65F5C8150;
extern const uint32_t g_rgctx_TU5BU5DU26_tAA693E00D662F9BF1E29D6DF565B5F9E20D3EF25;
extern const uint32_t g_rgctx_ReadOnlySpan_1_get_Empty_m76510E6586266E2DC852030CEC080255B54327FB;
extern const uint32_t g_rgctx_ReadOnlySpan_1_t1548CB92BA26402DEC654E8A0CBD1BC8E6AA9DD6;
extern const uint32_t g_rgctx_ReadOnlySpan_1_t1548CB92BA26402DEC654E8A0CBD1BC8E6AA9DD6;
extern const uint32_t g_rgctx_MemoryExtensions_AsSpan_TisT_t8C1F2D78C6BBAB31F9A3368826AC30063E708A83_mDED32F9CF0CC25CC4D1D8D62CA89F10122EC9001;
extern const uint32_t g_rgctx_Span_1_tA2C85E6B96D57B3D53B302DC9DD2D0A1060AD71A;
extern const uint32_t g_rgctx_Span_1_op_Implicit_m77783458C05436E50ADA1D9729006C3FF4D7C0A0;
extern const uint32_t g_rgctx_Span_1_tA2C85E6B96D57B3D53B302DC9DD2D0A1060AD71A;
extern const uint32_t g_rgctx_FreeListCore_1_get_IsDisposed_mB5802DF138DD112949FD793679E2C7C985AB086B;
extern const uint32_t g_rgctx_FreeListCore_1_t7AD56D3C302E5FF7A137A94EBC0C06BC84CCB002;
extern const uint32_t g_rgctx_FreeListCore_1_t7AD56D3C302E5FF7A137A94EBC0C06BC84CCB002;
extern const uint32_t g_rgctx_TU5BU5D_tABF391C9273DB65CAAFAC7C677ECA65FE83D5A7B;
extern const uint32_t g_rgctx_FreeListCore_1_FindNullIndex_mFC57A717B11803730CEEB5241D1C146D43D7CACE;
extern const uint32_t g_rgctx_Volatile_Write_TisTU5BU5D_tABF391C9273DB65CAAFAC7C677ECA65FE83D5A7B_mE6ED0024D804BE1908AD6C3F4E9DB7103E96C93C;
extern const uint32_t g_rgctx_T_t8C1F2D78C6BBAB31F9A3368826AC30063E708A83;
extern const uint32_t g_rgctx_FreeListCore_1_FindLastNonNullIndex_mA65D8FAF08BE05DBE1DC2B315DE1A2B851BE9CE7;
extern const uint32_t g_rgctx_MemoryExtensions_AsSpan_TisT_t8C1F2D78C6BBAB31F9A3368826AC30063E708A83_mC5D45802495B98C9F6BE8305CE02507B21E57073;
extern const uint32_t g_rgctx_MemoryMarshal_GetReference_TisT_t8C1F2D78C6BBAB31F9A3368826AC30063E708A83_mFF10F7B61C21DABC0FAE41213A69110F6557B776;
extern const uint32_t g_rgctx_TU26_t15F9665A2DB82A10EB6B851402BDAC1CDA21E59C;
static const Il2CppRGCTXDefinition s_rgctxValues[815] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m9745E4BBEC4CF0FF2D441CC720CAC441AC30842C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t4BB5AC6D0FE947A5AA1BC13192BE12366C949F62 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FreeListCore_1_t12C0FDE10E20974FB70E7D8B51A91942EDC64391 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FreeListCore_1__ctor_m324199B2DCB9B38B25341A3DB66B2D55BAAE0FFC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BehaviorSubject_1_tC865840C2AA6F8D7413444C100DF413D21D3E4E7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC93122D456A72FEC23CECF72C6B2E9EA5F950569 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FreeListCore_1_AsSpan_mCB2970A53239F9541BCD4324B4B1BF0C74630F38 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FreeListCore_1_t12C0FDE10E20974FB70E7D8B51A91942EDC64391 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReadOnlySpan_1_tE4366B947DB7840E6F76D6F6941B8C2DCBE964FF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReadOnlySpan_1_get_Item_m8F9C36130D581B921E84CE47E4B8F1B7A21740E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReadOnlySpan_1_tE4366B947DB7840E6F76D6F6941B8C2DCBE964FF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubscriptionU26_t01669BE45AA5AE1D585F3B47556EEB3F5FD7606F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Subscription_tE78629C45E94050B0247B5AB58ABC2C0F35E0CDA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tC2356DBF0A473924B8C7544660FBA6CFD5C7BBE8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_mCD3A66E339F9B1F32A2F0D0DF5CB8FE939E98996 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReadOnlySpan_1_get_Length_m8A24C42895022AF91D0BEE712F1D730C97B65164 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_mA2CDF8BCCC44478291FD7598B4A7D15D9D15E71B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Subscription__ctor_m7F4F78CE8650715DA75C5C5FC8DECA754EF5E984 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Subscription_Dispose_mBB97A673B94CC94E1108AD100C9EAC16180CBCED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BehaviorSubject_1_Dispose_mCF8432F5B9E56E00E8422EF4A596689D0AA51236 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisT_tC93122D456A72FEC23CECF72C6B2E9EA5F950569_mF63F73CDF5D14573D1C48B1C8A3CE47261C89BA3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FreeListCore_1_Dispose_m15A515C590DB29D363207A163F3F0E5ABC654D04 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BehaviorSubject_1_t8EEF4F2E7BF811F59B0AB0860D79723D9757C353 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Subscription_tF9ED131B9A46B077AE64D58E359AA79F8E41B900 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t2A3CA879FB474526DF77A918143E7E81707E0256 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FreeListCore_1_tE95C278D1E422215141769C2A9083BEFF2E6DD82 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FreeListCore_1_Add_mC2FDEB491FA97119CC74D7D6E005A730BEA14E6F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FreeListCore_1_tE95C278D1E422215141769C2A9083BEFF2E6DD82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BehaviorSubject_1U26_tA9E393B9517957FDE20AEAE582D4B92B3699140D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FreeListCore_1_Remove_m0FE9065CF289770A282BBEAAE36745ACFFF9B429 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t6FBC0073093A00039A1C5C98840B85B2A0FDB0A3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t58794D133D7BD90B9AB7C6CD1DD0D51EA1B69C96 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AppendPrepend_1_t5A22CC6D48D65F474E8FBC8907EC8C48868A6D0C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AppendPrepend_1__ctor_mB030392E13671402BFD54946B07F1CF1A69D6A35 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tAA8166938425CA4D1EDCC5DF3EE6DA0A4962C2E5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsObservable_1_t5BAED16B36305465DD5B924A9F597749C0F91A81 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsObservable_1__ctor_m0E2541005C593E25A04F2EA7E8A7580D4976FC28 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t0E6E6D5C61DFECE893D4940B2F2DF158FCEBE4EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_m191AF294A65377428994410DD54959EF9747B60A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t5B4117369C0149E9FF859C9E2179865781A4B09B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t5B4117369C0149E9FF859C9E2179865781A4B09B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObservableExtensions_DistinctUntilChanged_TisT_t6C6AD4E7618005B70F3C84DCD4B3BF781CFC71E4_m86A8B63542014D4484FFFAF200CA0375A3637F80 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_tC6F9C7133DB6D8E8ADBBB044E5090DFF29C77DA6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tD8A3238BD5D449C19BD35BCD894BAD2C591E2D6A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_tE0DC9250D17E925B082F391D29BFDC3C00710F64 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DistinctUntilChanged_1_t422406C3E67DFB474FA700FA63F18D0A6CA92669 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DistinctUntilChanged_1__ctor_m80800E94A29E26FF5C74DFD7F7D83957391980AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tA844C6AA4D05FD486C6C950A12803CA3B500F004 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Where_1_t236515CC0764659207713C7AC427659AD3E69E31 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t93B78F8CA12CCAFD9A012DDFC8BE6292023FDAFB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tEA49CF7BC9AD0370DB79570A5FBA444457228F81 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WhereSelect_2_tFE820B2E0734C6FA056E87FD9E6A75D935C49A5F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WhereSelect_2__ctor_mDED15FC79C33A41E68B99CCCC2EC876D458C7549 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Select_2_t6FEC8B075E9E097DF4606D716B749F4778AFDCF1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Select_2__ctor_mE3C9739829F3ED3680806532FAB2B0783423C13C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tA16D72F5BCA1538ABF619D1608C8220E42057263 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t28ED85DF5F0A78008EEFDA45D7016820CB55E224 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ThrottleLastFrame_1_t46FF09CAF1010B3C107D3F4017D71C457FE171EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ThrottleLastFrame_1__ctor_mFA1AA36FBC7DFB8884853D1676E76AAC7C3F18A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass263_0_1_t80C1F8C6E015DBF164DE43D90BAA747B10F66940 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass263_0_1__ctor_m1EF01A76B75FC08551657AC1200F01273CA0C444 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tD8556ACDD919D18A2F12F78A894C37866D6D9789 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t8383F1E059B0C5A6C5CC1838D864FE39D04F9ACC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Where_1_tED1A3C5CB8DA20D2927C3DE5E345798296AE2E64 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass263_0_1_U3CWhereU3Eb__0_mA96BB92CDD0F0C8D2FDD846E0EE68E1956318605 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_mD46716680AC692B0672AFC7C461A0E710B5763FB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Where_1__ctor_m388972750FBFB3205620BD8489233B47864E71FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass263_0_1_t22BDC85126E355B50B5C25F8F325CE570B070016 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t8B68B4E8A78C084CF6511130966020B8C24F4D29 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFFF5D61E5AA75E16AB0D5225C39E3D11BE33B148 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mD3EDAF591C8F0C897D1D02C97AF74698516FD77E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t564F49C19CBC288A80A49AB2B94F47864DBB1FEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA9121412ECE93DEFB674E8ACBAB814E1CF434EC3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t96DA1A2EC73D41EBDD185B842FBFE6856C3BF7E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_1_t44BA49874C4F7CD6A9C27ED5231EC03AF0836B3D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatest_1__ctor_m065D81E891843BF945C39216286F39CEAB8AD7A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tDA7DA55014C7D107C0915C15F555617A4BB29CBF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSource_tFA2A59B7A0D126293C5DF676A39DB4553584209F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t4911912D6F82E86E3646962AABB26B23C70A67B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_mDBDC8E1EEA185161559D12CCD1F76FF50F3B05E7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tB82C563A06305896342A1B4D54602F8B8E4CDFD6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tB82C563A06305896342A1B4D54602F8B8E4CDFD6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_EveryValueChanged_TisTSource_tFA2A59B7A0D126293C5DF676A39DB4553584209F_TisTProperty_tE072FDA9856E2A82F5D4456969DC4F121235FE02_m0D9AA4011921DF58A9AA19F17DB5642AD2782A0D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t75C76B4DFDDD2CEDB867D4AC4151DD5C9845AF0E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSource_t29D8DFF342A403276B57C43C36F9C4F0028184CE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t26AF28AB78B4DAA2E0C3AFAECABE74B22C2B456B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t6A20FDE0CE48032C722F96AD0F9A18C7A9E46C58 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EveryValueChanged_2_t1524E484F0C699016EF8442D580DBF82B37857F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EveryValueChanged_2__ctor_m9DDB9CC52CBD654B870BBE095DC323A3934EF66D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tDAC2E634A6BDDD52F00B8E1EF6A7D929A1440BC4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t34D82CA2D2C219C88A7E41246CEBE013A584B4D1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t2C46B015168BDB9C5877AC999742645CE60F65F4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FromEvent_1_t43B9185FCC206411176A621F9731C9137C37B934 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FromEvent_1__ctor_m48A1225EC1AAF19BC69671BCD363CD5F8CCEE9BA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tA76DC0C9CD2AB5D5A43907AFE822142F6FFF6C56 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tF0D2688EBBA493F80C5D00F5A7DEC16331C83166 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FromEvent_2_tC74EB6334CECC59C3A167A2DA0F363CEAC22FEA4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FromEvent_2__ctor_mEC1AAF2C3E55F873E1CFCB40CB5BF23CDBE1C5F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t60A9747A84712C69895EE1D579F7BC7C7CCB2D29 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1U5BU5D_tECEC4A61C5DC27E3D545C1E222749A998EC3A92B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Merge_1_t701E95B88EE3368002EB4215FF4EA778DE28B541 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Merge_1__ctor_m914F250ECA6CD171387099C091AA01D9F8A96B6A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t24D50B25FEB5C11B2E169952C69DDB48D6418617 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t3D548A6163BC48E05287FEED2FE4539A2F132358 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t26CC654B5567015639933AEC1011325565AA3159 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Merge_1_t1A59469AAE1716E224BBF7C9EDF94F75F23564C0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Merge_1__ctor_mEE294DF8785DA32F090AB3E490C23D6D37301888 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t889C26844C3878A9CEF2220F4694FD5D1F5C2658 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tE0911392ADDB854226BA29556FDFE034855D96E8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tE722B95D17A529B4910968E44238EB47EAEE1ADD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t51D80682FDFA0DDD74B099D9A093E381380B0168 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_3_t34F9B27038A59F02EDE637BFB72E671921B46D66 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatest_3__ctor_m2AEEBC4915950C5DE7F3688E59F38F228FCFB243 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t58674260EC82716D5E6640A8F769DD3A771F3E2E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t841120B7979C7A4C2FECDAC728F4278108125EDA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t1B621C471C00BA6DD9245DFF7A84A070D72AD97B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tE18A983ED7DE1CF0331987E0E0CAD1456739523A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_4_t08EBC59D6B47BEE2B57C9122763C089379B46318 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_4_tEDB876A356289D4738DD6A992FE535DD78F826E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatest_4__ctor_m32752C5C1A74C4FC2C5960B90FAC95972F187B68 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t7BBE6DB594A16503D2443AE89433EDDF0976C1FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t29A032C09F45077C23C2D7D88E09C3F0FD5B3C2E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tF325DC6DACACA30890A8CFF970B21CE66DC3E446 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tEC20E6B2BDB15CC184B5B0D173E99011B606CB65 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t74FD2896368BF508A6370FFB2B5339281F86DAF9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_5_t8C4A06DEA32E95F2EACEA6EE257A9686375217E7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_5_t6072ACF25F8731E2A85193839D6F7F66138267B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatest_5__ctor_m7AA90E3184F7496158B9D1E6AB9ECC35689F7D6E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tD17F5CE5D25D81700A736696ABEACBF5B2DC7DA4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t15EEEEBA8B60189724B80A96B5F7681175DA1E11 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tC97F54DDF33E103283DBAEC5CB006B3AE8D63747 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t0F0CEA5C00150C461F91E3CD569DAE22A614E793 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t69B3179186436B15F975DC78CEE521257F1C37AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t57D792BED9A2B58C6990DBCD4058A782CD5D46FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_6_tBA5D8250A41453A2337597E359AA10AB4039F077 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_6_t1F4C66F49CDD8CB4462D023C5B0E068B07A91F9B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatest_6__ctor_m88B51313D4FDBCE86DDF99F6978B78B0A11ECCB5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t3E3274877225099C65E3491DAF690DA63CDF8442 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t41E46CAA0E12DB1ACCEF0E93C3066EAD61BF8759 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_1_t2EFD7268570634A82E68ED2BB1C8C76CCE1FEE8D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m087FB3381A10E658D7204E83420FBFF7F023D777 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t16855865A8396CC5D69AFAF910EF4217EEC71EA5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t5D4C178355C1BA096B371DE620056966D5D0DE59 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t730F4C438D7D8F933576B2D6B2789572480DCEBD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest__ctor_m8F52A3D7EC78008432C325E49581245E92C4B910 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Run_mF82605AA17169CB8316E4A4C3BB5285119A53D33 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t31B8A4DD60075037B191BF9069EDE179DB0BA09E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_tC88FB03A3B58E755A93A6A204AB92182536101E7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t398523CE2F6261975C3731FE430035E89C049436 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1U5BU5D_tD29F02EC0D3E3AB3F21973FA1E7A60ED93ED27CF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_ToArray_TisObservable_1_tF9760CCFB00E4628E856C84A6B4D711E1937DCF0_m03985307BB26862050AF809934F07AE184771168 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserverU5BU5D_t6B329112734A1CA06CF1E6609185810EE940D488 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_t2B9795C416E69B4CBB1584AED37AD8C2625CD8C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver__ctor_mD8E3EED4EB3AC05114EF1CA3B91B588723B78718 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserverU5BU5D_t6B329112734A1CA06CF1E6609185810EE940D488 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tF9760CCFB00E4628E856C84A6B4D711E1937DCF0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m4CF5A12EB7E2FD5B45D97BED552B91FB3D321ECC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t13145601BF5CA9FA621CEC984C3E717EA07C0F83 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Dispose_m35F7A9843927D8F0FDD9B22FCED0E46ED611BF92 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_get_HasValue_m12B25028049DD156BB7F25C63598BF8BF7C2685F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t63A70AF30CCA50200CC6421975424453281FDAE1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_get_Value_m93B25143A4FB9BE730E895B73EC1FBF9BBD60D2E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7A518ED57C502D019FA1CDB9C2119D9F9D4FCD7F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m499ED36793B7D0F55E39A17456A5AF4AA061C9A4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t63A70AF30CCA50200CC6421975424453281FDAE1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_mEF2CC395CD6E7E7E5BC35D284226077146592FC8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisTU5BU5D_t63A70AF30CCA50200CC6421975424453281FDAE1_m59E50DEC9AC728535528950031635D9E2671CF7B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mB22B0ECB6DD4391DD56076881BFECE5DF14AABA9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_tB59E0EB83A5AFA576A88182E04D0DFD547998949 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_t62CB27666443FDEDB557039D3990C2F7F3171D23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m96D6CAB80F4DDC1D50ED25C2377070F658BFEACE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tCB3547D31F50B75431B858FE736AB19FB8976BE8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEBE3C6456B42636A816BFB61CC4182D0460FCC47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserverU5BU5D_t3444CC52224E26E77AA74DBF9DC1BFAFC8FD5C06 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_set_Value_mA8FB9BAA0732C20D3619D283E774A84C60122092 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_set_HasValue_m4F724C0FE88170F055CFF8FEF2E3402F3EAB4DE4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnNext_mEC9DCE7E7CE13BBA69D5316842B8DF0A68AA8C06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t0C6785701B0ADB23A45097A125EB5FC02B9D8A16 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m0BA57C96DD6D3DBB30C89D99B8D29C46B856CFEB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_get_HasValue_mFAEEBF17721550940512DBF1CC4CC3AF02DB106A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnCompleted_mC0C5D32EA792186E9F2B2EAB7615763DD84BA530 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSource_t049EA2F3D3018E0DC6B6ABD53625244B7EBEB5D9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EveryValueChanged_2_t12A379E06ACB7D66E94AAC96C742351498A79EED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tA2F0BAB0EF5E485AE7A45BD1514C32F24D8F0073 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tA05BC350334153B57C8DEDB1A70EB46A2CED2FF5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m2686A96D2A97DEB489C23A764AEA7A826049FD05 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t2730E3950CB8960E855301B2BDA730F7B5D19DBD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m5EF9FA0699A5057E9B135F38B18CED7CAC447012 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TProperty_t5632CEA68EE03041E2091CECEFDB3CBAC179CF06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t76E59C57FC2BB125B3B89232D88CF621900C9B19 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_mAAF34F1E2D478FFFD7BAAE0ADEBC617C0B8D365D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_get_IsDisposed_mAF9E5722B56F210936FAB6026622F1036EC3B261 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EveryValueChangedRunnerWorkItem_t84C581731665C874FC0585521A73F046610C5672 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EveryValueChangedRunnerWorkItem__ctor_m215F9F1CEEE5B3192F16C09FB2EA54BEC8A8F3F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSource_t3D0A61496EBC7EEFC0F08D37E987FC6307F62C88 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EveryValueChangedRunnerWorkItem_t24C42B67E2ED633071EB6AC6AB8C1FAF7610C8E7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TProperty_t0BBD2AF541DF191DFD40E7B71CA08C54F6E7F6C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t98D94DA43C6C8DA74D5F74D10F18651D6D9CB8DF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t8905842AEFF176DECD3EC7FF53B895D2F3352E32 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t93F3047602038042FB9F9C773666D9BF4DF7885D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1__ctor_m9C0C7415A25051B76FFEDF571276EF260137EABD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1_t2CF91F955EE7B3B253F89AD73E0A3A3C4142050D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mC6F86107B462A66DC4BE49C4C44279F380453CC9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1_PublishOnCompleted_mDF9402055DFF8FAEBEC210A6C3F8DD1B44A08948 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_Equals_mCAD47D9F3387B01C43064A2F60B16C61A2FBA0B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1_PublishOnNext_mCAC5482D9D533D795C20C040169F4E44CE090798 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t89BC02C16E4132C8F49CE91BEBB3C545C7C28918 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FromEvent_1_t5EFF89B40235AB01F7D5A13F6E322AD2DBC9C041 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t18063DC731C923D244F76B3C1165A69F8A156233 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__FromEventPattern_tEA78778F396765F82C325982DF0FFABEDD705203 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__FromEventPattern__ctor_m6432BB8691FA34BA4574B6907285C0B56E6F1902 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__FromEventPattern_tB6A220A04623E115856DC5196BF16291637B7AED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t423974FF2C73E87CE46D8467E33FC4ED2F01EE06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t73B9619C6CC542C5CFCE4E77969F8ADE53B2F676 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__FromEventPattern_OnNext_m4186563D1E10F328224B04A00B6252C761E397C0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mFBEB78603E6CE895F4A2E439453C764E84A9434B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TDelegate_tD98693656129DE90EF6F02EC7D63FD0B0C70C591 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mFEF9A7078CC6C59112D115FBEB6CF3E26DB12917 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t7314A1F1E7B4C0A688D9FA05ABBB2C3370FEF8A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t7314A1F1E7B4C0A688D9FA05ABBB2C3370FEF8A6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_ctorU3Eb__4_0_mEFBD2320E607C3D298C40A810B783DD67667003A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__FromEventPattern_Dispose_m5C42393EFA1B743BC17F7F5D5FD74CB370213FC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1U26_tC92A86B53C56A6306FAA2D411AF2C2F6D459D438 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t1F1BE596A7F34912FCAFB47474E1FCC811A11D32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_m01225CC0E82E7F53AF26BCEA06A778605AD16BEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t1F1BE596A7F34912FCAFB47474E1FCC811A11D32 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__FromEventPattern_t5DF831A6E58C00FF25B54AFD4292860BB579444C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__FromEventPattern_CompleteDispose_mA9EB54DA3D8295E42198E5C1D5A013118A69A1EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t850D5F5B80D3FA6EA51FEEBD6ACA88F56538C067 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FromEvent_2_t4CC83D21AB1432FDC19A0CA2CD0B17680301089C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t511A87E91D7B954147E85BE2AF8346D3D6C59CF7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m9BC1A4EDA3243D9592F361C7A3A2FEBA5FA2DB7B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t780C90294A2CDC3E4119F473C98056BFB23544E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t3D68615FEA7B1417C750F03BF814D19B063D9F72 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__FromEventPattern_t7D0F17E4F7D114E80CAFF5A1D8782402423C80EC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__FromEventPattern__ctor_m8625B7F17B13738BF905BEC28028CF7E2086AA79 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t41DACC6FEB8812BDC60EEC5EAD7CF6C68825343B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__FromEventPattern_tF3B1C67AB7286873397648A40BE689AD36294EDD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tB45E705207BCA280DF00D1E95099FCB978F55296 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t10A2ABC6047EBD4B6D182117D42532D4C845AC67 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__FromEventPattern_OnNext_mE22254BE1D5CFEC913D9278FF3645506F35777F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tED00ACEB0622E64157FD7938008CF782B8F4B172 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_mFFD446A8FE6242D16A3E7FAF20192EC136F7F9E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m6ACAA10A406E47C202697A21B332D1664A95507D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TDelegate_t325DE66DFABA289894039A4CEE29B5B8A587FBC5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mA7E18B6D83D4BD3E5AF315B805B1DFBE56CBBBCB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tA76D3D5D83DD7C5CFCCDD70D43B7374A457EB180 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tA76D3D5D83DD7C5CFCCDD70D43B7374A457EB180 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_ctorU3Eb__4_0_m4DAFCCD95F4AACE08FFBD8CB3E5C9ADA4670C978 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t325D8EA4CEEFC0899C93ACFE717E9A5C52EC8D5C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_mD5620529BCDDC5F63685E69F3E5E96D327380316 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisT_t325D8EA4CEEFC0899C93ACFE717E9A5C52EC8D5C_mDAB21801CD9063DAF19B856A984BB1C878153768 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__FromEventPattern_Dispose_m7FB2142D5486D478896C2A4411292471802FD963 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1U26_tE7624D1E98CB2A5D41FE357CABBE059FD71AA2EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tC637CF7A7BEB2F62D03702D10CEACB98A876890A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mFBCCCD50EFEF9955799F9DA8EB4AD247FA399BEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tC637CF7A7BEB2F62D03702D10CEACB98A876890A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__FromEventPattern_t422E00235B3D4735DF456C95C3BF700CC02BD740 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__FromEventPattern_CompleteDispose_mC57FAB0177D7E0F68770B88D84EE39A9C6D9B700 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t765E3AFD6696964B9D796C185B722A93F35F2A80 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Merge_1_t09942E460803B79BA1FFBC343CF6D6EAF1CBD196 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_mDFDAB1E6EEE9E0D1E97A7081CCB746F9B2D46125 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t12824A151D41C27A72A4832CADBEAD6564199EF4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t0DF621987B74E5EF5B16C1165222010E58D399E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__Merge_t165CCED0F0D0EF0CA8DF61495FE9333024408F87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__Merge__ctor_m78E7767B93C12CAF3A230393657AFCB9B9CDA742 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m6C525857A8CBAD7850270EAC8FA29062120961BC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t8DB6C43190E5CB7E735C148E1FD4BC5CAF8221EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m1D8634C92B0FE6699EEA02A7BDF2BB4BFF1DFA59 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t12824A151D41C27A72A4832CADBEAD6564199EF4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__MergeObserver_t95445F1E0CA6AC0B50B7007B78C2CF4F938F9EF6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__MergeObserver__ctor_m781FF6164E8E9447F0A63B34F26D30D5612771A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_mBD5466295A249678ED3AEEE4907C9D29F6FBE35E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__Merge_SetSourceCount_m4404E29484BD59533C99A5BE3BECF4DBA9878E3D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t026A78C17567F0A349EB7844AD2C4F1C33FEFF14 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__Merge_tB07BCBCE49C4E8BCCF9243B9FF70BFDA2BC0BBF9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisT_tF4511D497B5AA51476B73DCA38DAD2A5FECBDE90_mAFCEE23B43C60DCD41D447AEA99F8C8637D30DA8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__Merge_Dispose_m4AB91FA8410BDD9E97672E07872CD58DD98859FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__Merge_t28DBF9AFC7F6FFA6D2A0F457B8FA81CD52A46D0C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__MergeObserver_t4BA323A660E3E56D313D2102AF3EDC9138E0C885 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_mCE96C1B6EB77CB2E521DC087984B0E23E719C431 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t6D07F9F2ACE3AD67C941EC8DDBE2EB62245293F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t6D07F9F2ACE3AD67C941EC8DDBE2EB62245293F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t590A7AF24FFE209D50978E3D297D6BD6E136D3CE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m777811C54A4468E83BD3E88AF159E79EC68C1814 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m4C7BA7DB666D4611CF68590F0910C400DC4F68AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_mA30FC509320988990C26AE9186FA19356F764D91 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__Merge_TryPublishCompleted_mB6C679CDE522D5739078D1D68889F8D819E4CBE1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ISubject_1_tEF8AF09F1AAAA6B2F27489C76FE0A499993E6A0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ISubject_1_OnCompleted_m0294541A0DEF5DAA9662B761C04FAD5FF34B6282 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t601CE2C28815F5364EE182E024D72B8D882A35E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tECE8AF4B6440C0D2F4333FC3E1CC973129666B63 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_SubscribeCore_mAD4388A0BECAC79CE155F3A90ED39D38CA9A705B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_m065951DAB12A990AC1306BC8EA506C599B3AA445 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tCABCFA8D59DD2AC30F4323DF9B055972E5760226 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_get_IsDisposed_m9FF473889E20EC9B9EE6B88694321697936E1B6A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_get_IsCalledCompleted_m43B6211F969929E4A737C44CCD9676BBD6DF1099 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5BCC180D5484A07F1E6E19914985E896B7DC11B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNextCore_m4AF49341896F94C564AE0F3C2A50901A5A3DBF31 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m3D504F5EBB251CF0867CA6E9FA19CB6FFD196F6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResumeCore_mD2483017958875E7BB0888F1DF46056D678D71CB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_get_AutoDisposeOnCompleted_m49715596A03A2D91B357D4AD9A3E61697A319707 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompletedCore_mCFCC743FE990865B253354200F8571669CB6F316 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mCB8DD7D4857EEF0C0475121D3956D4A6270994AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_DisposeCore_m92BA7AC405C3906C81FCDEFD34DB65E728BA0FB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t5430E62ABAF1E097C320F417313CC43063E3A8BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t80F6718518A2B825F309197A551064EF9FEA7356 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AnonymousObserver_1_t035CF5C7EEA6CA698C1068D14CD16A73B94746EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AnonymousObserver_1__ctor_m2576287D05383BF9D1D51515511B0E3C5BBFE329 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_mEC360A87CCD4B037C0109CE1AAC62E022E4D86B3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tB80D7A56F7641B5F82A51F10E876E2875C4B5BA9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tA67F945E9DA2847BF08D9411652AE6389C21F478 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AnonymousObserver_1_t8575E9AE52FF35FA5223C8FA51C4462C2C979D1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_mB235D4C4A12FA475E4B04ADD5091DA887E65E036 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tC1CAB30F2D310FE9E4DAFDB8EE22C69D92CFBF9F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7AC27C176F7E27E577CB9CBF2FF0B0F5C758DB21 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m769E1D3755D80C57E28436544A8FD8FD8EA9DD2D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tE76C3217669A83FE9CF4AA702F08247E390EFB59 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m0C5CD226723987ED02EBE777249173E9AAEC1FF9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t5DCD1B77CA763F5631A6F538D7053CB5C1AC51BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m69490B4C66BBC5575924A00D82CD352A929BA932 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tE30C86957913780E95BD3D03D34B51506C928DB6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WrappedObserver_1_t6F8E2A6EFE82816153ECA670F5E4630727BC2BD4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WrappedObserver_1__ctor_m8BDAB944833322677336A3826CADD6EE54AEB0B1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tE2C12E8EF78A2C4BFD36E4E7C22803780EAC3765 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WrappedObserver_1_tEBEDF0A18239A468BC58B5BB082782ECE4B6DE49 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m3EF62A397B17BD242929A6ECFB7E84DEDC75F836 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tE2C12E8EF78A2C4BFD36E4E7C22803780EAC3765 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE3A2F6C500B74E3B2C99A2107FC5590302D5FD1F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_mE43EEB40854AF8B7833E6551BFF9AA5DDA3A71E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_mEB36AFACAA88EBFBB8E6ABED8A8E6AD36ADA63ED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m45AEC4B56BB0714D9BF0EF0CE293BD9210651199 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mB8C474504055A1BD0FFF522EAE0D9C30471853DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tE350483A6B46C3049CBCA4403143928B5211C240 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AppendPrepend_1_t011E015F58675CE8646420208D0DE31D781B6862 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6B7772835664CD92F4282C948E41ABB660491ED4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m5034CFDB5271305F2FD2E105DE75E940FB015EC9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tE350483A6B46C3049CBCA4403143928B5211C240 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t3E84FC6F8CF0B5BBF308FE1ACA30674191C9AE24 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_mDF817169CDDDCB2D03462D0829C1DB0101A649DC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_Wrap_TisT_t6B7772835664CD92F4282C948E41ABB660491ED4_mED72F54969F0971E0C8B0CFF48DC81B162524CED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_mE9571FC83B76DCF8EA20181DCDC53934D0A015B8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__Append_t8B36F3B417F8D73CE3B40E929BE541DF320F12BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__Append__ctor_m2653341413F87EAFE1EFBEF3C9757D3F2A36EF54 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t11B52F0E61A438CE684645B262D1B7DCC3EA4E4B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__Append_t87963988DF3A0CFE29891DB7E6FCE78E54E0D217 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t70F234A21B7868A9DCE3618B8CC1432424482900 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m38716A244624EDF3C40612343853CD8BE12787D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t11B52F0E61A438CE684645B262D1B7DCC3EA4E4B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m7AE41488E68DFE5ADEB63FE5EC52C567B77C7787 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m435779E1EFDC555B55D200EF9D62E8B76B5FEE59 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m582CD717311816F47ECC0C64F3D467605D2DFB28 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisT_t70F234A21B7868A9DCE3618B8CC1432424482900_m137855B6D5C373C6F70D8D6EAD7D01EB72AB5F50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t4D0CA3FFDC9DE8C0DF47771CFDC5A7E589DBB27E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsObservable_1_t9C89913BC6ED443F3FDBB1E645647A0B634B096D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_mA5798D06064A4B8498A2F58D30BAAABE3CEAE105 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t4D0CA3FFDC9DE8C0DF47771CFDC5A7E589DBB27E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t54FD8595E9597CFE6A2970C4B020FACBEDD09E82 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_Wrap_TisT_t290DBC4DFAD5ACAF60D9C4DC856A1BC4BA7DFE00_mE14417FC34CCCF289F685440032ED11AF1046B22 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m96E4A154CAC2C9965F1E1F5A8BBA50871EED699F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t4049E325053946E17064B24E983A3D1BF2202671 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_3_t7D1A4212623FE7AF538640B40B1E21051F4D9A94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t1F2A71E2D18F1B04F7BE379B1E88EB8268A8EBFB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t2F46F91FEADE2B5F2F751352F178E18E189BEA0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_mD41EB261C82B5978482E5101CF688DED76BBBA41 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t42C8CEE6185D500EF42BBD6FA99D59BA18B434F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t9797A80DE56E780FE4287CD903B3C7F49FD43D27 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_tC450344C766B0990BDEB8FA73E04C0E9BEC2BEB4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest__ctor_m61EB4A017B00C740C3E00930276BF201C082242D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Run_m91858613A63C7A8278B112B8D7D93F5AA5AAAA36 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t3B5FCD4B0B0251708F9D8BD42152D15F034BD07C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t574F4B4BA3FE435FC9900B21BA29EF6EA1CA869D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t848AC3011136C9DA7B644A5F6E370331B0D3882D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t93C602909C201BB94A6ED2E3C8409F73867DA339 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t3C1B1A41BEC1F993AD1DA85453F0A6C3667798D7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t3A09AF3984C8488E3BE217CAF9F466149655D677 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_mF328ADE25A463D80902C360B063096DEAAFE4B76 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_tB4BDECFD2B8BF8FC1F456C0FA8695D51CBF8BA2A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_mDC25122830C6CBC16890D53203A183001E8D0E5C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m4A2A0B70E25757148BEDF7C90BBD0DD398115B63 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tAD9E733A9435CD40C97B8B52991E7CBBF69FA95B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m333034545042DE593A1E1646B0342746638DE2C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t430874F4AD9C78F40A89AFDCFB0A0AD2FA96F4B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Dispose_m50135166A55EE3C930BB2972EF8F6F7286C75713 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_m0DC2DA034A2B615EC1CB0714186F5BBFED73B855 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_m6DA9EBB7485F13C846C8053E76ACE2C1ABDA8B38 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m294E72AD6315F452A2ECD79B1C2E4418DB0A2A47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t4ABCA2B082FB78F07D0EC01BB003B73AE07CA638 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_mF00AB49047ADE7E0F04F834C1EA6DCAEC351B47E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t9C4E83F32F7EC008172570A22ECF79FBE9B1EDBD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_3_Invoke_m683FD46AA151AFF0C3A0096745538EAC4CB861E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_t4ACB07D5F9FB876BDEE418F4EC3B274F1C4E9BA0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m52F0CABF9C4EEB78C18A1D31D9552D68CBB3517B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m2476E290808092163C5B5B1609C5214BDD424C04 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisTResult_t4ACB07D5F9FB876BDEE418F4EC3B274F1C4E9BA0_m3EDBA29046D5BFB1A22F8A15ECB848F419D3C140 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mAE1241C1304A63450FB4D2E649A534DD974BE37B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_m214E6D481A8F0DE1E8C1C4257928C0B4761AFFF4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t7F5D1FE90F45E246A63A8A8298B50582F15654F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_tED56D9CAD1B3689A74172A46E2B36D665A5BD2E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_mF8A7F05D2DCF8104F800AB9C78A000353D7CE5E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tFFCCDD4BAB96C322DB2F74585DFEC3D3426DA890 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8E317D57AA9F3824B8D7DAC92C9087B440EC0141 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_set_Value_m3897C6C107FC5388AD24A0FAA548626146A94968 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_set_HasValue_m3B148622ADD62EF20E34D9BCC845EE04FA048647 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnNext_mC9E9FE4684F9151F13E96F3BE22D0180A510EA62 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tABC10DD9BC4B62213B14088EC46FAF486D97CA06 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_mEB750969C15B7872C81E0028E072EC199B6393EC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_mA2B5AF3D5086C936CBAC7659BC383C013214F546 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnCompleted_m660905E897DC0B8423DDD56CEC7472ECB7F2DB9F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t6575CFB8DF4F3E750075B02FBC0877CE23CDE020 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_4_t3F967B440DEB4806E8662DF5860C6D87A9A62FCC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t7BF0AB92AA79CC453FEF4C573CB1294F688AC01C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t40183C8E931BB38886CEA682C3A871F842FBF415 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_4_t134D3B872BF89A02A573907F49E1E7B1C9965855 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_mE8650E8ECF465BA13090E823842104B3DB79EFAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t2F59EFDBD485787314477E918F11C5F57F6D9499 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tB0927386B06F11E293EDECC6D8EB6F1A60B6C82A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_tF9017227C05723D002BD884F1A898914FD738299 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest__ctor_m37F00A902F1DB58A3D54AF521DA3A5C83BE3069D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Run_mBDED2B6D29EEE71C36326878FAC4708D3CC48AB4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t4E8ADD9E2E88189EDD11C1F814985FF57880F28B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t643288E8750BA4A5739072E0736204D3836C2D04 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t4A24D3A99B4681B67C9464174E11D2AF5E9AFF1F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t74DCC75E5809D9799966E268FB65D8BDD1ABB66C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t37F945B4D9DC85B097C008B94202D6F142D26F7B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_4_tEDE5C135ABC281F59074027431F0CD5626A6003A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t5B9F56E0564309C20B81E454305D25682C0D92AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_mD648C33CF7A1A356286DE7F3B0B66546796E8F5F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t6754453D7876D0460E678253C5B6B4947A3D776F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_mCF02DE3234F831E4813A0731511F3907C1C9C83F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_tD57827FFC51A17BDA1511E0C1DDE119C393D3D00 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_m202C15FF188FBC5AF1812572C1CA574C881D516C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_mE6A1845471CC9A87494E569CAB2A708B4F11D3E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t8D8D46F21D912B5B3270138FA0336D7432EE0C95 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m8B4CB4BD198C35A9D3496DD4E0F8D24101AF9ADD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t936CF0460839FD720F87A51F855D9F710DBBBC31 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_mD4A972B435123C44298F153F3239F894E81EBD49 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tC269CCCDF00EA734CB7759353582521D6095AD5E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Dispose_mB60B3EF127A7A4053D408A2263F3BDF06DB0AA78 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_m0B361ECBB5C985E7490AB92A38AC91C0C37D0052 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_mB56B58BD7CC2FC0A29F3934C13D4E3F45F498F97 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_mED07A113381FDB8DEA3F5DEDD66A5D01BD6C8467 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m2AF922300C4C3BA91B7800DB5A4651C8F933949D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_tD35CEE39B3A51F6F8C0FF4938BB999B69FD188AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m854495817C8FF97893032F89FBA1F63C8E0F05D0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tD92227718C762392390BB4813C242211AEB39051 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_mBA2C041E0A545FE079E2FC75C082F2C10800F17F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T3_t90010BCDAE52891EA7A3CB9C4B4F842F20C1F200 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_4_Invoke_m9DE9045DE01E7C6BC06D5DC98CF3EF2CB84507BD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_t5A3A1140BD31EC3E7B24658B8F5AF07B257642BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m79AB2435FB47E90844BD5D822DEC88C3F2EB5488 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m65B9EC7E6C5566B3EE50B1D16EBD017A173311FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisTResult_t5A3A1140BD31EC3E7B24658B8F5AF07B257642BF_mFE4558F4BE58B58CB206CB1C10B8E91EFACA4358 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_m4C4371C776C14A6CE6A7FE3184F737AF58464094 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mD4184DEC406689635FE85F79445668F9DE65FE08 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mC638D7DA9B4E5AB256D4401CC87E8C36745D8242 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t51B887C67F8887736C505EC0A2505CCF8E28B610 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t009874039DF373FD5859D42198FDF2F6D73FB1DF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m928E131E3A8688924DADA4DC223B0A7F9F619A7E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t120BA785E3265DAB6CDA49DD51D99B5B3D7EF4B4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5B75B156E5371B46C293408A0439B1BA86C5E522 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_set_Value_mCF9B54D16CE9C411C8C4A96EC70328926ADE41B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_set_HasValue_mBE190D53343D22C8BF48F9F08EF867503F5539E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnNext_mE761629C4CFDFC8185AA14DA93609DDC2474DEF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tE665A507822D580620E62E1DA0CAC69DEA015A78 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m933053C2B3F5F75A4AE9FAA3FFFDA7384E3036B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_m7F2EFDD35D76E23E0944FC50E850B4399242D7A7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnCompleted_mAB49ACCA0E91B1DA153F8F42AB9B233605048BC4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t4BEA50F4D5FAB659824BE35FEA87D27781414F05 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_5_t1377A756E282314FCB72D65CC2A03843511E6F89 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t10659C63AF10E5184E262913A1D57D754B841F2F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t5F684AB7CDE4542363AA78A3AC5FF19DEFE0B21B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t84C1C7A993DDDD56CFD8140330FA4535ED6A328B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_5_t7FFB451EC79CE929473F534BF53357656147CB82 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_mDB74222D75F01DF550A6BB63064A7FDD43286205 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t34E9228844EC85782B31C01D1AC20613E7A4F11D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t07C2FB2B2A0B70815503EFC9160EB0C76F0D099E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t9B000DC62895178EE10EBB604584EDCF4B8D6CBF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest__ctor_mC307F0EDB454C623FEEB8F2F04C478061FFE6FA1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Run_m9B74233CAC64AB7DD5B87410D2EA0BEFB9084E4F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t70F56F289366986B6621879E39E81A6CF73096E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tA66C3C1E15B615EC71BFC50B2706A1B70166D90B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tDC052EFFB3E0DCC58A2A318A02AE42720201A93C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t3F43474ED56629DB5AC7F4C4D8D0198AC5BDD483 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t78549BC81AC4569125720523E8D345EC5B087D1E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t58010D490B333AF5DBEA4A849A11F3A9F9AB3020 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_5_t35A7D34B04ADDB58D7D710763555FEF44127A3A4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t205A03D7913A6EBA817F7B657B7D9407A444B04F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_m1A361B84F8912E8CF51932CB9B633617E33EEC15 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_tC355C66049FF50EDD68A6A9D526678BA12281CA7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_m7EADE3AC11114F9B8FBF730E483617431F5A39FD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t3118E6A426EBC0B4431F769FD86C15B75457D5C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_m0DC62A3EBA5CD6BB8C7A61CD87650F8C6DC9C060 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_tD3447521D8AE6B5E2ACF467166829B925AA166B8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_m9DCE6A98A3D8A7CAB3245206CB7B8C2242752807 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m6E729FF4616965F0B969E0F7685AEB9B76AFF4C9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tB0F91303A370A2CFBF0623C8301880E030DD6F14 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m417BA6E335B501C3457E5E0E4C419D6A5ABE600C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t317986F1867C06CB892075924DEEFAFB9AE1A79A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_mDAFE873481B213BE540DB3DD0C172D2237130CC1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t6C419927DEFAC475196DB91ABCF4317E290DDD34 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m434BB474F782B12117873C880CEBD51517D4D187 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tEB337ABC3FD1324CFC79B1C50D7EFEC91BEB7093 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Dispose_m8BF228C104C571474D7A9DCC1AB2DBBECA2F8931 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_mC1F2C65A6E7E33C1A3DF1F4FC4CD368AE688DB2F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_m3B180797CCACBCC6C444694CCE6531BC875C5781 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_m1CDE6A2C7494A362F2DBD90D0FF8D53C65C3AE85 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_mEF90939922EA6F7325413F5BC895D8ED5A22ADF7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_mB3127E40B33188667A7E3896651AA105D3AE8B22 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t4DF6A5461BB932F99447AA20A5483F811335CCA8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m4E1854C18231C8757BF26B7BAFC7F818B5D3305A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tCF667BE39E0187C98EA17C82C558072286D870C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m1F6B0AD8759F67809CB8C1D2AED38CA553B37F01 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T3_tCDB786CFA5570518D4402C697A491859AEB86DB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m209A61888265B0456ACF19D8FD3E7E92C58E981B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T4_tAF167C28B753440D7EC40B2929B547CE3E6730B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_5_Invoke_m0D4BC1BE1CF6ABD569A26BD391AC4A6739A4B004 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_tFD456F356E3E9E077613870926FEA02FC5A76B20 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_mEB8FF677A2217AF9F2EC25DC55006C1CF41B3570 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_mCD2A67FF845849FC16D497FB9A7C6C00BD5FD679 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisTResult_tFD456F356E3E9E077613870926FEA02FC5A76B20_mED3F39325A8E5F67315F99A827611E8C875CA9A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mB7100E3A9F3EFBBFA662B9A21E021B690B541DF0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mFE24E4ED2D049E65FDB3CFDCE8916A27B2E5536F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_m1F3BB84E7765824A069E5D0B8B10BE0C50F92F2F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mF9E761DAF7B21223DD55E972152D85EF2B3A35F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t78245E13EE248A9CED6D49256B0361AC3623C886 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t1322EF0A75DCD5954F2C2225F5619F2627887759 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_mA81590C134C24E7CD5F996030E1DFA8734C01591 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t41449AB961B38A3C4B1F813408F15812CCD71BD7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t16EEB313FFE6F309C1138DDD079A91F076F88A5D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_set_Value_mCB79F749656F391116CBE5F62D5570495BC2A3B6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_set_HasValue_mC697A000C64CFD01EE275C54A65E48818DF88732 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnNext_m5B7B1DC814693B7A8BC9F2BE49CBB6351FD44258 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tB994EC320BE8AF18FBBD0187245D3A012774FE03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_mD7F50A81BA0081CEFC0F1480762B1FDEDF8EBBD3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_mDEB0205CC4117F1FBB2706998E958814981D8802 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnCompleted_mD9F0DA27F0D1AC3654BB73AA1A04D52050749D6F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tA1380280E1C2C80A37EBF089CAFA56776503526A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatest_6_t26332BB2835AB1B78A57EAA673EEEEB66D0094FD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tE4458285EB409A7BFB513034B3D706A4A2D5C9A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tDC21682B7AAA2AF8E401D7DE4C0E9EE28DE2F211 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t27854630A29FD5C399FE9C6D744C5466630D98D0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t3A20446C6ABE19C569653D5C378C0AAE20BF372D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_6_tD0CE023CFD8454D34AFF936AF777ED8795296180 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m0AC988518A10FF8EB67AFB39EB86EDEFAA3A5CB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t50FD5B15AF045766832502E4E51B0FB041E772C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tF4243266EBD87461DB42F6350A6D1C96F94FA01D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t77C1B04C538BC02A0A6012B3CA0C61E0C19CF60D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest__ctor_m1AD79D668FEF5F415ABEA0B5D2FE080EDDDA7FFB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Run_mD46D7F1C2F7E9D8FC7ECED7AC90EB70688284313 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t79E116878F7204C96116CCA2A4F2A01FEFF1F167 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t74D316C9B93293287E0F9535E3DC5F7376E10C7C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tA6692B211515FDC640B7184992A88893911A9CA4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t44F4B19FFFF81A7E94151DD44AEF9F05510B99E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t9A4A2B511C2CEDC1C03FDFAC4ABF2DF9AEE082F3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tD4A402509F9CF51230B4A2908F94C1292B536AEF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t2043DC6B7CE7D2306085337ADE3D5F1AA20745AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_6_t4F09ABE77D95E9158300EA28C5F3998D782DA076 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_tA8E28C0FFA5EB4685EDAB09C2BA646CF3269B103 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_mA7CBAFAA71A7B1CE923655AD3C2BCDE1B26F5FC8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t03333869078481CDCC1DF2A370617BE3C525274D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_m59F5FA508C0B4F0FA8EE439AEF2A083D787DB624 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_tABD55F7E17D6EA75CD3F40C2ADD53FA9A4EB8A79 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_m6079E3A7400F38951E1FFED4F63CB6B02759A38F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_tA9F519DB456E6FA8D9F2F41A9A5CECF6CE27A73C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_m2E0FD6945BC160BBE34FD9BD68B7004F04A9A2B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t406EDC9426B6857B16AB49096C789961736063BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1__ctor_mECB9426CD51DA93D987DFCA331177C5791B7D1EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_mBFEF0C4B53471680529EA8B95DA76E94437516B8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t3764E6E53C2E6BFD368E6D1702BF92D467F648F8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m4CD9642E1498B326445A09AB39DAB89234B00511 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t246155898CD63A957C41BF94E8FD3FBDAA187A96 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m7F1087E5134C881848E3EE66B6DBD9836A55EF47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t608A394E38174F642F06EFAFF2B991444991B545 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m17CF2C4EA0ABB37008B401874C7D0443AA4CD314 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t772459B9E9E914EB26F74006D515E32759DB3494 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m42757DDE40F85552622CAA9BED14AB2B93794DEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t5CB9BAA333229EE6E92793F0E9BE5496DF0A63DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_Dispose_mB4759B001C5FD5561FDEAC45EFF102DCE19BB411 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_mD3996759BA4D8EE9828DA27724D0897064B31A9F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_m029B5541B01890F4170E93132B35BA8E3DF9FAE1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_m1CB6209F18E653BD0590676A6465C5AD6146B6E2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_mF4FE11E432ACD4175DEC2266ED208E9AAC5B918E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_mF620BE3AF24FDFDAD39682DD9B94C89910E86CD1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m35A3228456D71B9E78DBE93688B90205F90E32A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t71385CCC9BB011C0F41023B884B0A0FD62DA3596 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m13B6184EE611C995BA8B83797F1AA7AE76F1D051 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tEF0DFEE91CB77644BB8254628C211D4A2D170224 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m0205914F0026BB3BE9882B684B1F88A39853CA58 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T3_t2456AA0EFDDA2A6D2D8A5FCD7CA9BF8056153894 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_m6B02F8A7B28B12AC06A2725362B98A4386750D8C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T4_tC511F19CEA2B8E235A0C5BD7CC185B0BE5B6F4F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_Value_mE34F0530E908CBC5ECBF3E0B4CEB05F03D62089E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T5_tE80AFB559C84C291FDAC4EBA053697A63E86BF62 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_6_Invoke_m7F20B457ADCFB180A0C4ACE10E54BFF5AB2D7373 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_t6124674803485AEE3644C2B9A30472976B7A2118 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m5F4B85C694FBA612E666766AC8E5DC6521608674 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m3353BED85CB8C4306F5DE568764C9BE3C1500B6D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisTResult_t6124674803485AEE3644C2B9A30472976B7A2118_m8FCFAF8B4AE9524E3D64317E7D908FFF9A54F423 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_m43B413B2E4D2A234EF1E3C0CA55FB944DC0209FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mA286717AB42CCDCF6CF11DD92AE1A134E1AB7E94 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_mA1B4528120B0A765E179B8C0DA83AC059823CFDD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_m8C60E3D0E8232BD5FC28772B26C74BDBB098827F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_Dispose_m9E4F70C79247EA0EF87D8E789DDA7CE994652FF7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__CombineLatest_t691E0D1CC93AF657E833F68A7FE3B9AEC9E5BE4B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CombineLatestObserver_1_t1F22500C1B6D9FB51BA80D8199003BAA1BDA53EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m4D27D08352C2B5458810D53CE793B74C3FB88E39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tFBBA348EBE4E922B4940E50087D279F206D84DB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4191D38AB573101A0899696CCA9CB3570333535E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_set_Value_mEA0B7F2A18826A53E962A3F5756701533D07F375 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_set_HasValue_m1FB0770060F4A36D41173D3E4C860BDCD8273055 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnNext_mEE0844BCB963C766658E578E0E3499E2248C2063 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t26DE593E6358A5729F4FEF5BCE25A6758EBF4B3B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m9FDD3BB4D389FFEE2AE692DFF5A1A4447C2356AA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CombineLatestObserver_1_get_HasValue_m184D4E73C0EA07FED368E0C9F635637C3A49381D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__CombineLatest_TryPublishOnCompleted_m48AB249911FEC8708CA943D79FE25EF8AAFAC619 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t1AF2F514569FE93FC7DB9AD530C10207BD193B80 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DistinctUntilChanged_1_t6361825B0330507076AD2A98D3144A9973EB8DC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_t7069003127098176743C81A47DFBB01A7539F015 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m8D51078B2077545E5F86717ED46C9B5712E9C56D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t1AF2F514569FE93FC7DB9AD530C10207BD193B80 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tAE3577A5E7BD8C43BFED6F5A6D281ECADDCDDD9E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__DistinctUntilChanged_t1D697F9D736266B045F9790DE1D0A4FFDEB4BFF1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__DistinctUntilChanged__ctor_mA3490B62E0DF6A21383B00DA3F4FF1A8BB951A65 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m4887B1477D61074036CCDB605603082D6B1C66F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m826DDAC58D17A36029E6845B15B74A132417310A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t9C0DDF4E67FEB47A4E56D0585EFB6257AD0940D3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t9C0DDF4E67FEB47A4E56D0585EFB6257AD0940D3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__DistinctUntilChanged_t6157F25753A8A22BE978623517A1095ADF318CE6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_tCE830AEA422D9C801F02D25457788DD6443BE1EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2C65BDA191B91404B31B9D02E7BDB85EE18319FC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEqualityComparer_1_Equals_m2C426CB7720287E8B2058649D35EAB6EF59B2E4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_mF4B05AEDE32E978186B40C64E2073F50CF8AF71E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_mE46F4C99F15B95FF1A00AB2B226F8E6443DE3D36 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_mE082566206C2BCF17EE520CD5AAE3972C6E90599 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t83877C58DE3CB57D07064B052F18E302146135A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Select_2_t674D8A7083607803A6F606AC23388CCD325C7E06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t229F7EA0A6575EADF592503CE48A4B2F7215EC53 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m9DD20E0286C3F5E60CBF8589002F5D379AC58A34 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tC3250011CA9CEFB56AEBF0ECE080FC208CC29265 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t40D4646DD8CDD4580C2C2C04FA0F43F0934D2DE2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__Select_t926C05564543F09857663A5396D4A3FC28A79D9D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__Select__ctor_mD31EEC2A72146F768F943A3BB97ADA75446B96E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m40701EFEA379B0ACB12FFFD1A840705DCE8E6B91 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tB217447CA593C41EB9D3F7CF803365EE33C551A4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tC888FF88E69C1539C72380CC77384A4E901C524F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__Select_t75AC76CE95FE6AD80ADD2485F6DD9652D7DDF3D7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t4F0A96C051493053C027283F4064C146E9D20041 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m093521ECFDE655C66404684E8B877FBCC53FAB4D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t4775C0F41688B969231F0EE0EF45BF0F98F230B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF0ED22EBACB7F8721359FC23AFCADDAD1D7157B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mF5BB4873180F700C4239F84320C7C34614E10806 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_t4B9948CE9AD3725E05B82EBD7BD966A9645A1B21 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m0F73DFC17EAFA80B0D5B5AC4C5E4D9A33B20DA77 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m63BD4C4CD60E5A92BF087F7280DF955D92E44EE8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m9E478DB66DC6E4151A523343AD8B2A0D42C9C43B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_tE2673702083B33FB79393FC07B13361B8F1C7C6E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WhereSelect_2_tF264C86718AA3F09AB01CAFCF25C9D7D04CED6B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t5897F4325C75E0419D345E5EAB23B69184FFCC79 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tAD4757FFEFFDB194CF707003BB4A5937B1C2C28F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m02CAC1E8B8B6D5BEF18F7DD596820A693E67ADD6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t85B6EEEF4896349CFDA8AA906E8EB0DCB369E3DF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tC17F6011A21ADC32516A6C0F35B8ED9A50D90AE7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__WhereSelect_tD6E00FD4924CD2B90FEDD42C59533A68812E3C1F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__WhereSelect__ctor_m9E869F8045C3722FF1622B57439EBFECFAD750A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m527D21EA5EA5AD65490569AE70975B6317E74738 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t8A4C6557A75F4585C26FB2DFDF7EF1F38339766E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t97E9BA4EB4CA6A98A0F39A5ED6E1F6AFB9E381E2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__WhereSelect_tC42328B9E769312BFACB10623D6EDB57A79F297B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t2D31DE0B577FA777A0916066093EE293DCD3B70A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t35B85DA5AC6856FE1AA591FE6BB8C7038BC4ABD6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m888E416255B728CBF8064C7C12A84610C62FE94B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t5B639E284E9B1442BF7B955C0A90AC5AA62FCA5B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA42DE64B82DA9E61799E3941A8A5D8BA395A8F3E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mAA861B941BC1EEB3033E86C845FA9F26619BABCB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m5264D5ABD1348852164C861A7D3046446C0639CA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_tE62F8BDC705673C4522B8ACC47E6C2C97842A337 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m19A7E0C7F4F92D91D9AB7035E1A2E7317A8D5064 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m04488F9978B598DC773BD7B4BC82D2CF7C633B17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_mAE1A8DC2CA92D4D5FC148063196E059EC4CA77C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t95B6301C8C90B42E86AC5B44AC2459580FF6FD65 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ThrottleLastFrame_1_tEFA78A53F330BA33FFEA626812E76927F43C102A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m01398779B1B7BD08C39FDBF1B83E4A4B4E574F26 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t95B6301C8C90B42E86AC5B44AC2459580FF6FD65 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t652430F7AF8A5DCE5838DD90563E96D48362FE6A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__ThrottleLastFrame_t25118469972B49208A0325132B259E11E48DE83B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__ThrottleLastFrame__ctor_m5CC5F67C0BB935D4054AE03F1DD34831FD32EAFB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_m18ECF05921312B1D07773764022E5DEB5C7D74A4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__ThrottleLastFrame_t0321103BAA4715A4AAF8A03A6638F74C7E487D19 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m3485E980A023FE0E5F10CB92724FE029018C41BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tE483A187B87C3EF44E91D83C09F8969C4024B684 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tE483A187B87C3EF44E91D83C09F8969C4024B684 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t857015B746E3F926FAC15FEE261319B672CD3FAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m96A8AA5F908A7A52DE834B864A4F5F6536DC42B8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m1783AA2602CC78558CB293A0035E30B4455E4408 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_get_IsDisposed_m47AA6E0C6525AF31104999DCAAA442AF5B954E17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_mC6FE85AB13086ADEDA4275A59F19B7C5FF59BD07 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t7503D81415AF1FDB4DF5D621B3B1D8CF235E5066 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Where_1_tC65CF742A6195B9A85C295B7BBB27E8EA7D60288 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tCCAB330147CD4141C484BAD7AE966EEA89F28CA4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_mC66F031DDC28C2B1090A1C1CB7AE5AFA927BA3A4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t7503D81415AF1FDB4DF5D621B3B1D8CF235E5066 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tCD666956E9962E9C2A6AFBF42A269E1AA1DE120B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__Where_t05CF07A3A5DD36A670BA3BF3BFE3D855A3083FFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__Where__ctor_m11A89A68B0B1B194018BBC58B4C3F13E7B85B99B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1_Subscribe_mF354693D50299E0EA54FF8FE566C212D994588DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tECC88F4F2E701F89663C5A897178694EEFBF48B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx__Where_t1279CA26C6099767DD849C1BBFE18DAAA009690A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tEA51C33A8CE8AECB23BA9F5304CF84CF26859120 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1__ctor_m04E00DE4C7E43D2085ADB4581EBF9A43CD78C66A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_tECC88F4F2E701F89663C5A897178694EEFBF48B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t00E952B4F6CA8D2E1FA07884E364E95B9627D4B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mD1384D0CAA7833151BC2600F5F40013508A5395B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m498C0BCAA2932A63AE82C6243DE0EB096FA7088F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnErrorResume_m77826638DAF09FBB38F91A7E78D4A2102791B41B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m477ADFC57A431E14D1B21482BEB96C51CB031C07 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Subject_1_t6340E78A3E052A7A1B110E3B072FCD6276F3072B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Subject_1_GetVersion_m8062A496BD3074EA52D1C17D9FBFF5E1B812A274 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ObserverNode_t86B9850248602D0CFFF0F688B8A8C04D7C5307A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t708D01385109B3812DA3B28585F2DFE320526A33 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE0C7BE9070BA949F567D012E68A8990F581509CD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m3C155916F4BAD8910EF6FC72A889E0E984950CFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverNode_get_Next_m8105832E8531B871FE972D38936FA8E55E5ED0CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnCompleted_m313B407493014D62C89C2283E46DA0F55D9F68C7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverNode__ctor_m5735252B53BDF608B3C1F2B72744340ED3601843 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverNode_Dispose_m1AA9B6D4D767B293BC575848556BF36FE3CB3445 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Subject_1_Dispose_m0163E3DA8ECA2168F6ECC9C6E38F1FE35A8F9E13 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Volatile_Write_TisObserverNode_t86B9850248602D0CFFF0F688B8A8C04D7C5307A6_m3A093D4C9E03ECB5AA4E06556230B12A4D32CD5F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ObserverNodeU26_t2B6D3889D0878738B4F675AB74C7E63D624337FF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisT_tE0C7BE9070BA949F567D012E68A8990F581509CD_m5576A7E1E298FCE9E298C27F436B043453D49A70 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Subject_1_U3CGetVersionU3Eg__ResetAllObserverVersionU7C14_0_m00FFA81B66E67BCCC36959604583A20DECE96B0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observable_1__ctor_m38DF34C266387E05F88673EFC196526DB050231F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observable_1_t86925E9765EE57AE87A32547FA8D6310ECE32C39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ObserverNode_tC8F300063CFD4A2D8C549E8E9011DB795D68881B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Subject_1_t873E83CB245B545BFA50A10A9C4B980453A765A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t7F50CCAA9E7BD6089C48344E814ABB3302BFB0C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Volatile_Write_TisObserverNode_tC8F300063CFD4A2D8C549E8E9011DB795D68881B_mF925EB291AE4060ADFBA76B7E1AAC3E797D274C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ObserverNodeU26_t0BFBC13C1F133C1D3C4E4A14265BCA222B05A75B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverNode_get_Previous_mE69E519774EF98F0CF5903FBF9EA65BDA914FA82 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverNode_set_Next_m630C442C28695ECE2749E0C6E224BD3BF3EDE564 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverNode_set_Previous_mB931D59A3E741C435B3A0F386A5C45F0FE1FCC5F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Subject_1U26_t9C3AF8D01F37CA1A33BFA2DF7E75C610355302DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Subject_1_get_IsCompletedOrDisposed_m2A031D51561B6CB3D0228F1A76F3693D85DC61D5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverNode_get_Next_mAF251235888CE80A6931972D3B23E0C278FDF08D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t66CE3145318E0F75CC88D6CD090931549281D3DC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1_tAAD2A1CE0C709FB2EF4B9E3C490F3B8CA2193B82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tC70154AB2FCFF704053DA28C862ABC08F43F1961 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tC70154AB2FCFF704053DA28C862ABC08F43F1961 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_ctorU3Eb__3_0_m015CCA4F94DA20845E9C11E5E2C01DFC37D26A8C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_get_IsDisposed_mAF13FB10752ACF4C695F2AECCD764E79EBD718DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1_Dispose_m239951B6C09C97698A6E6E8DBA1CAB596DB4E000 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1_MoveNextCore_mFFEF326204F44789249B72745B15E3F7907B9202 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1_DisposeCore_m0E6EB46237F368AB08C958748A5225668EC50C7A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t620FB54A285D6E338EDEA4E1869C480755265E83 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Observer_1_OnNext_m994AEBFD5ADE7A5ED0FD080E50F649658C8CA474 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisT_t620FB54A285D6E338EDEA4E1869C480755265E83_mB0536CB1EE9A67414DC0B8EB40DE935A9E002EB5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t773221C60059073D269591D57949D829D3572B00 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mC68453518D786AF216DBCC4BB0BE8350E3A036EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t773221C60059073D269591D57949D829D3572B00 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1_t70C9A04B29AF0284925DC720FDB68BA5D992DBD5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Observer_1_t4EE6637CEC2BCD3F60446650F7F1E90B5DA7F24E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObserverExtensions_OnCompleted_TisT_t728FF2C049333D3E0181F7C0BCBE29572ED4F68E_m6CF5BBB9B63C8445E1D4A96C33F7C36677D052F9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CancellableFrameRunnerWorkItemBase_1_Dispose_mAC540A539A08650C9ED4C703AD5FA26AF41DD458 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WeakDictionary_2_CalculateCapacity_m42F2F40F93EA2851D263E1ACF3724DAE30F25690 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WeakDictionary_2_t540FF5F91ED85CE58D24A82FF962CF1DFADE98D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EntryU5BU5D_tDB69CE4EEA91CB809EEF77106C255EC514812A34 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WeakDictionary_2_t540FF5F91ED85CE58D24A82FF962CF1DFADE98D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EntryU5BU5D_tDB69CE4EEA91CB809EEF77106C255EC514812A34 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_tA0CA9CD4D02C9644B414AE5ECD258FBA6DE4438E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_mB2DB11BE0DCBAD070F43E353E0EC50111024229E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tEB7BB0CFA450290B959D0A4E9DF5DFE124C6BC01 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tEB7BB0CFA450290B959D0A4E9DF5DFE124C6BC01 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t84B496F75F509224AD17B1162F70EC702E7615DC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t6D0024E6EAFC7F538B6D6D8F35FF1B4D63F9D216 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WeakDictionary_2_TryAddInternal_m764AB622C264DEA6C1D3F4F31994D941E59E78E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WeakDictionary_2_TryGetEntry_mEF8A94B937AF7E5B689A2BB19C94D1CB371F2ED2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EntryU26_t158683BE6BE05DC55DCF3E1A63F1EC401D285D37 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WeakDictionary_2_Remove_m4021E1810528210321A9F076AC6A8C9D326729FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Entry_t808E2F2564F4F01A96D31A2FF585B384165978AB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WeakDictionary_2_AddToBuckets_m0918718E23869CB29BB7C6F141093EE5DB38D719 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEqualityComparer_1_GetHashCode_mE25A2B7F3AC444383410C63295296AF738B12F69 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Entry__ctor_mF71D709F310C06DB365B2C9897E52D7008849547 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WeakReference_1_t8CCAF22BD38296690DBC8092A053CCFFBD1208E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WeakReference_1__ctor_m632C3012D9AFFEB74A889E1C6BB33D3F27A993FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WeakReference_1_TryGetTarget_m0053FD412E0EC01AC28B19ADDE2152B27B41AD44 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKeyU26_t83D752789465DF2905E459C7E7AF8BF93CEE2CEC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEqualityComparer_1_Equals_m63319D71723BA7BC17F1ADBA3E3617C2726E83B4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Entry_tADA07D082A8438FE2E10DE226B68298F5767C7FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_WeakReference_1_t4141C09069999F31AA962C0CF3034B6DBFE12B40 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_WeakReference_1_TryGetTarget_m9CAC34003420661B778ED9BA79E2CCDD06897B85 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKeyU26_tF46A6EFB6706C8F8CE7D7717D81ECF999BC04042 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t0DB0AD07607E325090AAF0064CABD2DC81ED3915 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Entry_Count_m95B9E3CC12C858F1FFEB86F820F967B74BA80093 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FreeListCore_1_t7AD56D3C302E5FF7A137A94EBC0C06BC84CCB002 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tABF391C9273DB65CAAFAC7C677ECA65FE83D5A7B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Volatile_Read_TisTU5BU5D_tABF391C9273DB65CAAFAC7C677ECA65FE83D5A7B_m32872C64F7FB728637181F22C51D58D65F5C8150 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tAA693E00D662F9BF1E29D6DF565B5F9E20D3EF25 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReadOnlySpan_1_get_Empty_m76510E6586266E2DC852030CEC080255B54327FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReadOnlySpan_1_t1548CB92BA26402DEC654E8A0CBD1BC8E6AA9DD6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReadOnlySpan_1_t1548CB92BA26402DEC654E8A0CBD1BC8E6AA9DD6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MemoryExtensions_AsSpan_TisT_t8C1F2D78C6BBAB31F9A3368826AC30063E708A83_mDED32F9CF0CC25CC4D1D8D62CA89F10122EC9001 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_tA2C85E6B96D57B3D53B302DC9DD2D0A1060AD71A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_op_Implicit_m77783458C05436E50ADA1D9729006C3FF4D7C0A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_tA2C85E6B96D57B3D53B302DC9DD2D0A1060AD71A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FreeListCore_1_get_IsDisposed_mB5802DF138DD112949FD793679E2C7C985AB086B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FreeListCore_1_t7AD56D3C302E5FF7A137A94EBC0C06BC84CCB002 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_FreeListCore_1_t7AD56D3C302E5FF7A137A94EBC0C06BC84CCB002 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tABF391C9273DB65CAAFAC7C677ECA65FE83D5A7B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FreeListCore_1_FindNullIndex_mFC57A717B11803730CEEB5241D1C146D43D7CACE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Volatile_Write_TisTU5BU5D_tABF391C9273DB65CAAFAC7C677ECA65FE83D5A7B_mE6ED0024D804BE1908AD6C3F4E9DB7103E96C93C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8C1F2D78C6BBAB31F9A3368826AC30063E708A83 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FreeListCore_1_FindLastNonNullIndex_mA65D8FAF08BE05DBE1DC2B315DE1A2B851BE9CE7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MemoryExtensions_AsSpan_TisT_t8C1F2D78C6BBAB31F9A3368826AC30063E708A83_mC5D45802495B98C9F6BE8305CE02507B21E57073 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MemoryMarshal_GetReference_TisT_t8C1F2D78C6BBAB31F9A3368826AC30063E708A83_mFF10F7B61C21DABC0FAE41213A69110F6557B776 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t15F9665A2DB82A10EB6B851402BDAC1CDA21E59C },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationR3;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_R3_CodeGenModule;
const Il2CppCodeGenModule g_R3_CodeGenModule = 
{
	"R3.dll",
	360,
	s_methodPointers,
	31,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	78,
	s_rgctxIndices,
	815,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationR3,
	NULL,
	NULL,
	NULL,
	NULL,
};
