﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[17] = 
{
	{ 31323, 0,  0 },
	{ 31323, 0,  3 },
	{ 31329, 1,  22 },
	{ 17932, 2,  23 },
	{ 22975, 3,  24 },
	{ 31348, 4,  38 },
	{ 31323, 5,  46 },
	{ 22975, 3,  48 },
	{ 17932, 6,  49 },
	{ 24541, 7,  50 },
	{ 31348, 8,  51 },
	{ 31653, 9,  52 },
	{ 31653, 9,  53 },
	{ 31653, 9,  54 },
	{ 31653, 9,  55 },
	{ 31653, 9,  56 },
	{ 31338, 10,  92 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[11] = 
{
	"eventAttribute",
	"expressionValue",
	"animationCurve",
	"gradient",
	"spawnerState",
	"vfxEventAttribute",
	"curve",
	"ptr",
	"spawnState",
	"vfxAsset",
	"evt",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[342] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 1, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 2, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 3, 1 },
	{ 0, 0 },
	{ 4, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 5, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 6, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 7, 1 },
	{ 0, 0 },
	{ 8, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 9, 1 },
	{ 10, 1 },
	{ 11, 1 },
	{ 12, 1 },
	{ 13, 1 },
	{ 14, 1 },
	{ 15, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 16, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_VFXModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_VFXModule[1129] = 
{
	{ 106306, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 106306, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 106306, 1, 16, 16, 9, 86, 0, kSequencePointKind_Normal, 0, 2 },
	{ 106306, 1, 16, 16, 9, 86, 1, kSequencePointKind_StepOut, 0, 3 },
	{ 106306, 1, 17, 17, 9, 10, 7, kSequencePointKind_Normal, 0, 4 },
	{ 106306, 1, 18, 18, 13, 25, 8, kSequencePointKind_Normal, 0, 5 },
	{ 106306, 1, 19, 19, 13, 29, 15, kSequencePointKind_Normal, 0, 6 },
	{ 106306, 1, 20, 20, 13, 35, 22, kSequencePointKind_Normal, 0, 7 },
	{ 106306, 1, 21, 21, 9, 10, 29, kSequencePointKind_Normal, 0, 8 },
	{ 106307, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 9 },
	{ 106307, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 10 },
	{ 106307, 1, 23, 23, 39, 69, 0, kSequencePointKind_Normal, 0, 11 },
	{ 106307, 1, 23, 23, 39, 69, 8, kSequencePointKind_StepOut, 0, 12 },
	{ 106307, 1, 24, 24, 9, 10, 14, kSequencePointKind_Normal, 0, 13 },
	{ 106307, 1, 25, 25, 9, 10, 15, kSequencePointKind_Normal, 0, 14 },
	{ 106308, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 15 },
	{ 106308, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 16 },
	{ 106308, 1, 28, 28, 9, 10, 0, kSequencePointKind_Normal, 0, 17 },
	{ 106308, 1, 29, 29, 13, 82, 1, kSequencePointKind_Normal, 0, 18 },
	{ 106308, 1, 29, 29, 13, 82, 8, kSequencePointKind_StepOut, 0, 19 },
	{ 106308, 1, 30, 30, 13, 35, 14, kSequencePointKind_Normal, 0, 20 },
	{ 106308, 1, 31, 31, 9, 10, 18, kSequencePointKind_Normal, 0, 21 },
	{ 106309, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 22 },
	{ 106309, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 23 },
	{ 106309, 1, 34, 34, 9, 10, 0, kSequencePointKind_Normal, 0, 24 },
	{ 106309, 1, 35, 35, 13, 25, 1, kSequencePointKind_Normal, 0, 25 },
	{ 106309, 1, 35, 35, 0, 0, 8, kSequencePointKind_Normal, 0, 26 },
	{ 106309, 1, 36, 36, 17, 107, 11, kSequencePointKind_Normal, 0, 27 },
	{ 106309, 1, 36, 36, 17, 107, 16, kSequencePointKind_StepOut, 0, 28 },
	{ 106309, 1, 37, 37, 13, 41, 22, kSequencePointKind_Normal, 0, 29 },
	{ 106309, 1, 39, 39, 9, 10, 29, kSequencePointKind_Normal, 0, 30 },
	{ 106310, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 31 },
	{ 106310, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 32 },
	{ 106310, 1, 41, 41, 9, 61, 0, kSequencePointKind_Normal, 0, 33 },
	{ 106310, 1, 41, 41, 9, 61, 1, kSequencePointKind_StepOut, 0, 34 },
	{ 106310, 1, 42, 42, 9, 10, 7, kSequencePointKind_Normal, 0, 35 },
	{ 106310, 1, 43, 43, 13, 34, 8, kSequencePointKind_Normal, 0, 36 },
	{ 106310, 1, 43, 43, 0, 0, 13, kSequencePointKind_Normal, 0, 37 },
	{ 106310, 1, 44, 44, 17, 98, 16, kSequencePointKind_Normal, 0, 38 },
	{ 106310, 1, 44, 44, 17, 98, 21, kSequencePointKind_StepOut, 0, 39 },
	{ 106310, 1, 45, 45, 13, 39, 27, kSequencePointKind_Normal, 0, 40 },
	{ 106310, 1, 45, 45, 13, 39, 28, kSequencePointKind_StepOut, 0, 41 },
	{ 106310, 1, 46, 46, 13, 46, 38, kSequencePointKind_Normal, 0, 42 },
	{ 106310, 1, 47, 47, 13, 55, 50, kSequencePointKind_Normal, 0, 43 },
	{ 106310, 1, 47, 47, 13, 55, 52, kSequencePointKind_StepOut, 0, 44 },
	{ 106310, 1, 48, 48, 9, 10, 58, kSequencePointKind_Normal, 0, 45 },
	{ 106312, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 46 },
	{ 106312, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 47 },
	{ 106312, 1, 53, 53, 9, 10, 0, kSequencePointKind_Normal, 0, 48 },
	{ 106312, 1, 54, 54, 13, 91, 1, kSequencePointKind_Normal, 0, 49 },
	{ 106312, 1, 54, 54, 13, 91, 1, kSequencePointKind_StepOut, 0, 50 },
	{ 106312, 1, 54, 54, 13, 91, 8, kSequencePointKind_StepOut, 0, 51 },
	{ 106312, 1, 55, 55, 13, 61, 14, kSequencePointKind_Normal, 0, 52 },
	{ 106312, 1, 55, 55, 13, 61, 16, kSequencePointKind_StepOut, 0, 53 },
	{ 106312, 1, 56, 56, 13, 35, 22, kSequencePointKind_Normal, 0, 54 },
	{ 106312, 1, 57, 57, 9, 10, 26, kSequencePointKind_Normal, 0, 55 },
	{ 106315, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 56 },
	{ 106315, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 57 },
	{ 106315, 1, 62, 62, 51, 52, 0, kSequencePointKind_Normal, 0, 58 },
	{ 106315, 1, 62, 62, 53, 71, 1, kSequencePointKind_Normal, 0, 59 },
	{ 106315, 1, 62, 62, 72, 73, 10, kSequencePointKind_Normal, 0, 60 },
	{ 106316, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 61 },
	{ 106316, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 62 },
	{ 106316, 1, 65, 65, 9, 10, 0, kSequencePointKind_Normal, 0, 63 },
	{ 106316, 1, 66, 66, 13, 49, 1, kSequencePointKind_Normal, 0, 64 },
	{ 106316, 1, 66, 66, 13, 49, 20, kSequencePointKind_StepOut, 0, 65 },
	{ 106316, 1, 66, 66, 0, 0, 29, kSequencePointKind_Normal, 0, 66 },
	{ 106316, 1, 67, 67, 13, 14, 32, kSequencePointKind_Normal, 0, 67 },
	{ 106316, 1, 68, 68, 17, 41, 33, kSequencePointKind_Normal, 0, 68 },
	{ 106316, 1, 68, 68, 17, 41, 39, kSequencePointKind_StepOut, 0, 69 },
	{ 106316, 1, 69, 69, 13, 14, 45, kSequencePointKind_Normal, 0, 70 },
	{ 106316, 1, 70, 70, 13, 33, 46, kSequencePointKind_Normal, 0, 71 },
	{ 106316, 1, 71, 71, 13, 31, 57, kSequencePointKind_Normal, 0, 72 },
	{ 106316, 1, 72, 72, 9, 10, 64, kSequencePointKind_Normal, 0, 73 },
	{ 106317, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 74 },
	{ 106317, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 75 },
	{ 106317, 1, 75, 75, 9, 10, 0, kSequencePointKind_Normal, 0, 76 },
	{ 106317, 1, 75, 75, 9, 10, 1, kSequencePointKind_Normal, 0, 77 },
	{ 106317, 1, 76, 76, 13, 23, 2, kSequencePointKind_Normal, 0, 78 },
	{ 106317, 1, 76, 76, 13, 23, 3, kSequencePointKind_StepOut, 0, 79 },
	{ 106317, 1, 77, 77, 9, 10, 11, kSequencePointKind_Normal, 0, 80 },
	{ 106317, 1, 77, 77, 9, 10, 12, kSequencePointKind_StepOut, 0, 81 },
	{ 106317, 1, 77, 77, 9, 10, 19, kSequencePointKind_Normal, 0, 82 },
	{ 106318, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 83 },
	{ 106318, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 84 },
	{ 106318, 1, 80, 80, 9, 10, 0, kSequencePointKind_Normal, 0, 85 },
	{ 106318, 1, 81, 81, 13, 23, 1, kSequencePointKind_Normal, 0, 86 },
	{ 106318, 1, 81, 81, 13, 23, 2, kSequencePointKind_StepOut, 0, 87 },
	{ 106318, 1, 82, 82, 13, 39, 8, kSequencePointKind_Normal, 0, 88 },
	{ 106318, 1, 82, 82, 13, 39, 9, kSequencePointKind_StepOut, 0, 89 },
	{ 106318, 1, 83, 83, 9, 10, 15, kSequencePointKind_Normal, 0, 90 },
	{ 106344, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 91 },
	{ 106344, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 92 },
	{ 106344, 1, 116, 116, 9, 10, 0, kSequencePointKind_Normal, 0, 93 },
	{ 106344, 1, 117, 117, 13, 55, 1, kSequencePointKind_Normal, 0, 94 },
	{ 106344, 1, 117, 117, 13, 55, 3, kSequencePointKind_StepOut, 0, 95 },
	{ 106344, 1, 117, 117, 13, 55, 8, kSequencePointKind_StepOut, 0, 96 },
	{ 106344, 1, 118, 118, 9, 10, 16, kSequencePointKind_Normal, 0, 97 },
	{ 106345, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 98 },
	{ 106345, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 99 },
	{ 106345, 1, 121, 121, 9, 10, 0, kSequencePointKind_Normal, 0, 100 },
	{ 106345, 1, 122, 122, 13, 54, 1, kSequencePointKind_Normal, 0, 101 },
	{ 106345, 1, 122, 122, 13, 54, 3, kSequencePointKind_StepOut, 0, 102 },
	{ 106345, 1, 122, 122, 13, 54, 8, kSequencePointKind_StepOut, 0, 103 },
	{ 106345, 1, 123, 123, 9, 10, 16, kSequencePointKind_Normal, 0, 104 },
	{ 106346, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 105 },
	{ 106346, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 106 },
	{ 106346, 1, 126, 126, 9, 10, 0, kSequencePointKind_Normal, 0, 107 },
	{ 106346, 1, 127, 127, 13, 55, 1, kSequencePointKind_Normal, 0, 108 },
	{ 106346, 1, 127, 127, 13, 55, 3, kSequencePointKind_StepOut, 0, 109 },
	{ 106346, 1, 127, 127, 13, 55, 8, kSequencePointKind_StepOut, 0, 110 },
	{ 106346, 1, 128, 128, 9, 10, 16, kSequencePointKind_Normal, 0, 111 },
	{ 106347, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 112 },
	{ 106347, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 113 },
	{ 106347, 1, 131, 131, 9, 10, 0, kSequencePointKind_Normal, 0, 114 },
	{ 106347, 1, 132, 132, 13, 56, 1, kSequencePointKind_Normal, 0, 115 },
	{ 106347, 1, 132, 132, 13, 56, 3, kSequencePointKind_StepOut, 0, 116 },
	{ 106347, 1, 132, 132, 13, 56, 8, kSequencePointKind_StepOut, 0, 117 },
	{ 106347, 1, 133, 133, 9, 10, 16, kSequencePointKind_Normal, 0, 118 },
	{ 106348, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 119 },
	{ 106348, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 120 },
	{ 106348, 1, 136, 136, 9, 10, 0, kSequencePointKind_Normal, 0, 121 },
	{ 106348, 1, 137, 137, 13, 58, 1, kSequencePointKind_Normal, 0, 122 },
	{ 106348, 1, 137, 137, 13, 58, 3, kSequencePointKind_StepOut, 0, 123 },
	{ 106348, 1, 137, 137, 13, 58, 8, kSequencePointKind_StepOut, 0, 124 },
	{ 106348, 1, 138, 138, 9, 10, 16, kSequencePointKind_Normal, 0, 125 },
	{ 106349, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 126 },
	{ 106349, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 127 },
	{ 106349, 1, 141, 141, 9, 10, 0, kSequencePointKind_Normal, 0, 128 },
	{ 106349, 1, 142, 142, 13, 58, 1, kSequencePointKind_Normal, 0, 129 },
	{ 106349, 1, 142, 142, 13, 58, 3, kSequencePointKind_StepOut, 0, 130 },
	{ 106349, 1, 142, 142, 13, 58, 8, kSequencePointKind_StepOut, 0, 131 },
	{ 106349, 1, 143, 143, 9, 10, 16, kSequencePointKind_Normal, 0, 132 },
	{ 106350, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 133 },
	{ 106350, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 134 },
	{ 106350, 1, 146, 146, 9, 10, 0, kSequencePointKind_Normal, 0, 135 },
	{ 106350, 1, 147, 147, 13, 58, 1, kSequencePointKind_Normal, 0, 136 },
	{ 106350, 1, 147, 147, 13, 58, 3, kSequencePointKind_StepOut, 0, 137 },
	{ 106350, 1, 147, 147, 13, 58, 8, kSequencePointKind_StepOut, 0, 138 },
	{ 106350, 1, 148, 148, 9, 10, 16, kSequencePointKind_Normal, 0, 139 },
	{ 106351, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 140 },
	{ 106351, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 141 },
	{ 106351, 1, 151, 151, 9, 10, 0, kSequencePointKind_Normal, 0, 142 },
	{ 106351, 1, 152, 152, 13, 60, 1, kSequencePointKind_Normal, 0, 143 },
	{ 106351, 1, 152, 152, 13, 60, 3, kSequencePointKind_StepOut, 0, 144 },
	{ 106351, 1, 152, 152, 13, 60, 8, kSequencePointKind_StepOut, 0, 145 },
	{ 106351, 1, 153, 153, 9, 10, 16, kSequencePointKind_Normal, 0, 146 },
	{ 106352, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 147 },
	{ 106352, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 148 },
	{ 106352, 1, 156, 156, 9, 10, 0, kSequencePointKind_Normal, 0, 149 },
	{ 106352, 1, 157, 157, 13, 51, 1, kSequencePointKind_Normal, 0, 150 },
	{ 106352, 1, 157, 157, 13, 51, 3, kSequencePointKind_StepOut, 0, 151 },
	{ 106352, 1, 157, 157, 13, 51, 9, kSequencePointKind_StepOut, 0, 152 },
	{ 106352, 1, 158, 158, 9, 10, 15, kSequencePointKind_Normal, 0, 153 },
	{ 106353, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 154 },
	{ 106353, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 155 },
	{ 106353, 1, 161, 161, 9, 10, 0, kSequencePointKind_Normal, 0, 156 },
	{ 106353, 1, 162, 162, 13, 50, 1, kSequencePointKind_Normal, 0, 157 },
	{ 106353, 1, 162, 162, 13, 50, 3, kSequencePointKind_StepOut, 0, 158 },
	{ 106353, 1, 162, 162, 13, 50, 9, kSequencePointKind_StepOut, 0, 159 },
	{ 106353, 1, 163, 163, 9, 10, 15, kSequencePointKind_Normal, 0, 160 },
	{ 106354, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 161 },
	{ 106354, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 162 },
	{ 106354, 1, 166, 166, 9, 10, 0, kSequencePointKind_Normal, 0, 163 },
	{ 106354, 1, 167, 167, 13, 51, 1, kSequencePointKind_Normal, 0, 164 },
	{ 106354, 1, 167, 167, 13, 51, 3, kSequencePointKind_StepOut, 0, 165 },
	{ 106354, 1, 167, 167, 13, 51, 9, kSequencePointKind_StepOut, 0, 166 },
	{ 106354, 1, 168, 168, 9, 10, 15, kSequencePointKind_Normal, 0, 167 },
	{ 106355, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 168 },
	{ 106355, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 169 },
	{ 106355, 1, 171, 171, 9, 10, 0, kSequencePointKind_Normal, 0, 170 },
	{ 106355, 1, 172, 172, 13, 52, 1, kSequencePointKind_Normal, 0, 171 },
	{ 106355, 1, 172, 172, 13, 52, 3, kSequencePointKind_StepOut, 0, 172 },
	{ 106355, 1, 172, 172, 13, 52, 9, kSequencePointKind_StepOut, 0, 173 },
	{ 106355, 1, 173, 173, 9, 10, 15, kSequencePointKind_Normal, 0, 174 },
	{ 106356, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 175 },
	{ 106356, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 176 },
	{ 106356, 1, 176, 176, 9, 10, 0, kSequencePointKind_Normal, 0, 177 },
	{ 106356, 1, 177, 177, 13, 54, 1, kSequencePointKind_Normal, 0, 178 },
	{ 106356, 1, 177, 177, 13, 54, 3, kSequencePointKind_StepOut, 0, 179 },
	{ 106356, 1, 177, 177, 13, 54, 9, kSequencePointKind_StepOut, 0, 180 },
	{ 106356, 1, 178, 178, 9, 10, 15, kSequencePointKind_Normal, 0, 181 },
	{ 106357, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 182 },
	{ 106357, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 183 },
	{ 106357, 1, 181, 181, 9, 10, 0, kSequencePointKind_Normal, 0, 184 },
	{ 106357, 1, 182, 182, 13, 54, 1, kSequencePointKind_Normal, 0, 185 },
	{ 106357, 1, 182, 182, 13, 54, 3, kSequencePointKind_StepOut, 0, 186 },
	{ 106357, 1, 182, 182, 13, 54, 9, kSequencePointKind_StepOut, 0, 187 },
	{ 106357, 1, 183, 183, 9, 10, 15, kSequencePointKind_Normal, 0, 188 },
	{ 106358, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 189 },
	{ 106358, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 190 },
	{ 106358, 1, 186, 186, 9, 10, 0, kSequencePointKind_Normal, 0, 191 },
	{ 106358, 1, 187, 187, 13, 54, 1, kSequencePointKind_Normal, 0, 192 },
	{ 106358, 1, 187, 187, 13, 54, 3, kSequencePointKind_StepOut, 0, 193 },
	{ 106358, 1, 187, 187, 13, 54, 9, kSequencePointKind_StepOut, 0, 194 },
	{ 106358, 1, 188, 188, 9, 10, 15, kSequencePointKind_Normal, 0, 195 },
	{ 106359, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 196 },
	{ 106359, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 197 },
	{ 106359, 1, 191, 191, 9, 10, 0, kSequencePointKind_Normal, 0, 198 },
	{ 106359, 1, 192, 192, 13, 56, 1, kSequencePointKind_Normal, 0, 199 },
	{ 106359, 1, 192, 192, 13, 56, 3, kSequencePointKind_StepOut, 0, 200 },
	{ 106359, 1, 192, 192, 13, 56, 9, kSequencePointKind_StepOut, 0, 201 },
	{ 106359, 1, 193, 193, 9, 10, 15, kSequencePointKind_Normal, 0, 202 },
	{ 106360, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 203 },
	{ 106360, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 204 },
	{ 106360, 1, 196, 196, 9, 10, 0, kSequencePointKind_Normal, 0, 205 },
	{ 106360, 1, 197, 197, 13, 55, 1, kSequencePointKind_Normal, 0, 206 },
	{ 106360, 1, 197, 197, 13, 55, 3, kSequencePointKind_StepOut, 0, 207 },
	{ 106360, 1, 197, 197, 13, 55, 8, kSequencePointKind_StepOut, 0, 208 },
	{ 106360, 1, 198, 198, 9, 10, 16, kSequencePointKind_Normal, 0, 209 },
	{ 106361, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 210 },
	{ 106361, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 211 },
	{ 106361, 1, 201, 201, 9, 10, 0, kSequencePointKind_Normal, 0, 212 },
	{ 106361, 1, 202, 202, 13, 54, 1, kSequencePointKind_Normal, 0, 213 },
	{ 106361, 1, 202, 202, 13, 54, 3, kSequencePointKind_StepOut, 0, 214 },
	{ 106361, 1, 202, 202, 13, 54, 8, kSequencePointKind_StepOut, 0, 215 },
	{ 106361, 1, 203, 203, 9, 10, 16, kSequencePointKind_Normal, 0, 216 },
	{ 106362, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 217 },
	{ 106362, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 218 },
	{ 106362, 1, 206, 206, 9, 10, 0, kSequencePointKind_Normal, 0, 219 },
	{ 106362, 1, 207, 207, 13, 55, 1, kSequencePointKind_Normal, 0, 220 },
	{ 106362, 1, 207, 207, 13, 55, 3, kSequencePointKind_StepOut, 0, 221 },
	{ 106362, 1, 207, 207, 13, 55, 8, kSequencePointKind_StepOut, 0, 222 },
	{ 106362, 1, 208, 208, 9, 10, 16, kSequencePointKind_Normal, 0, 223 },
	{ 106363, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 224 },
	{ 106363, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 225 },
	{ 106363, 1, 211, 211, 9, 10, 0, kSequencePointKind_Normal, 0, 226 },
	{ 106363, 1, 212, 212, 13, 56, 1, kSequencePointKind_Normal, 0, 227 },
	{ 106363, 1, 212, 212, 13, 56, 3, kSequencePointKind_StepOut, 0, 228 },
	{ 106363, 1, 212, 212, 13, 56, 8, kSequencePointKind_StepOut, 0, 229 },
	{ 106363, 1, 213, 213, 9, 10, 16, kSequencePointKind_Normal, 0, 230 },
	{ 106364, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 231 },
	{ 106364, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 232 },
	{ 106364, 1, 216, 216, 9, 10, 0, kSequencePointKind_Normal, 0, 233 },
	{ 106364, 1, 217, 217, 13, 58, 1, kSequencePointKind_Normal, 0, 234 },
	{ 106364, 1, 217, 217, 13, 58, 3, kSequencePointKind_StepOut, 0, 235 },
	{ 106364, 1, 217, 217, 13, 58, 8, kSequencePointKind_StepOut, 0, 236 },
	{ 106364, 1, 218, 218, 9, 10, 16, kSequencePointKind_Normal, 0, 237 },
	{ 106365, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 238 },
	{ 106365, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 239 },
	{ 106365, 1, 221, 221, 9, 10, 0, kSequencePointKind_Normal, 0, 240 },
	{ 106365, 1, 222, 222, 13, 58, 1, kSequencePointKind_Normal, 0, 241 },
	{ 106365, 1, 222, 222, 13, 58, 3, kSequencePointKind_StepOut, 0, 242 },
	{ 106365, 1, 222, 222, 13, 58, 8, kSequencePointKind_StepOut, 0, 243 },
	{ 106365, 1, 223, 223, 9, 10, 16, kSequencePointKind_Normal, 0, 244 },
	{ 106366, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 245 },
	{ 106366, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 246 },
	{ 106366, 1, 226, 226, 9, 10, 0, kSequencePointKind_Normal, 0, 247 },
	{ 106366, 1, 227, 227, 13, 58, 1, kSequencePointKind_Normal, 0, 248 },
	{ 106366, 1, 227, 227, 13, 58, 3, kSequencePointKind_StepOut, 0, 249 },
	{ 106366, 1, 227, 227, 13, 58, 8, kSequencePointKind_StepOut, 0, 250 },
	{ 106366, 1, 228, 228, 9, 10, 16, kSequencePointKind_Normal, 0, 251 },
	{ 106367, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 252 },
	{ 106367, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 253 },
	{ 106367, 1, 231, 231, 9, 10, 0, kSequencePointKind_Normal, 0, 254 },
	{ 106367, 1, 232, 232, 13, 60, 1, kSequencePointKind_Normal, 0, 255 },
	{ 106367, 1, 232, 232, 13, 60, 3, kSequencePointKind_StepOut, 0, 256 },
	{ 106367, 1, 232, 232, 13, 60, 8, kSequencePointKind_StepOut, 0, 257 },
	{ 106367, 1, 233, 233, 9, 10, 16, kSequencePointKind_Normal, 0, 258 },
	{ 106377, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 259 },
	{ 106377, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 260 },
	{ 106377, 2, 14, 14, 9, 38, 0, kSequencePointKind_Normal, 0, 261 },
	{ 106377, 2, 14, 14, 9, 38, 1, kSequencePointKind_StepOut, 0, 262 },
	{ 106377, 2, 15, 15, 9, 10, 7, kSequencePointKind_Normal, 0, 263 },
	{ 106377, 2, 16, 16, 9, 10, 8, kSequencePointKind_Normal, 0, 264 },
	{ 106378, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 265 },
	{ 106378, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 266 },
	{ 106378, 2, 20, 20, 9, 10, 0, kSequencePointKind_Normal, 0, 267 },
	{ 106378, 2, 21, 21, 13, 61, 1, kSequencePointKind_Normal, 0, 268 },
	{ 106378, 2, 21, 21, 13, 61, 1, kSequencePointKind_StepOut, 0, 269 },
	{ 106378, 2, 22, 22, 13, 41, 7, kSequencePointKind_Normal, 0, 270 },
	{ 106378, 2, 23, 23, 13, 36, 14, kSequencePointKind_Normal, 0, 271 },
	{ 106378, 2, 24, 24, 9, 10, 18, kSequencePointKind_Normal, 0, 272 },
	{ 106389, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 273 },
	{ 106389, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 274 },
	{ 106389, 2, 38, 38, 9, 10, 0, kSequencePointKind_Normal, 0, 275 },
	{ 106389, 2, 39, 39, 13, 55, 1, kSequencePointKind_Normal, 0, 276 },
	{ 106389, 2, 39, 39, 13, 55, 1, kSequencePointKind_StepOut, 0, 277 },
	{ 106389, 2, 40, 40, 13, 74, 7, kSequencePointKind_Normal, 0, 278 },
	{ 106389, 2, 40, 40, 13, 74, 10, kSequencePointKind_StepOut, 0, 279 },
	{ 106389, 2, 41, 41, 13, 35, 16, kSequencePointKind_Normal, 0, 280 },
	{ 106389, 2, 42, 42, 9, 10, 20, kSequencePointKind_Normal, 0, 281 },
	{ 106391, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 282 },
	{ 106391, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 283 },
	{ 106391, 2, 47, 47, 9, 10, 0, kSequencePointKind_Normal, 0, 284 },
	{ 106391, 2, 48, 48, 13, 43, 1, kSequencePointKind_Normal, 0, 285 },
	{ 106391, 2, 48, 48, 13, 43, 1, kSequencePointKind_StepOut, 0, 286 },
	{ 106391, 2, 49, 49, 13, 62, 7, kSequencePointKind_Normal, 0, 287 },
	{ 106391, 2, 49, 49, 13, 62, 10, kSequencePointKind_StepOut, 0, 288 },
	{ 106391, 2, 50, 50, 13, 29, 16, kSequencePointKind_Normal, 0, 289 },
	{ 106391, 2, 51, 51, 9, 10, 20, kSequencePointKind_Normal, 0, 290 },
	{ 106393, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 291 },
	{ 106393, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 292 },
	{ 106393, 2, 57, 57, 9, 10, 0, kSequencePointKind_Normal, 0, 293 },
	{ 106393, 2, 58, 58, 13, 55, 1, kSequencePointKind_Normal, 0, 294 },
	{ 106393, 2, 58, 58, 13, 55, 3, kSequencePointKind_StepOut, 0, 295 },
	{ 106393, 2, 58, 58, 13, 55, 8, kSequencePointKind_StepOut, 0, 296 },
	{ 106393, 2, 59, 59, 9, 10, 16, kSequencePointKind_Normal, 0, 297 },
	{ 106394, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 298 },
	{ 106394, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 299 },
	{ 106394, 2, 62, 62, 9, 10, 0, kSequencePointKind_Normal, 0, 300 },
	{ 106394, 2, 63, 63, 13, 54, 1, kSequencePointKind_Normal, 0, 301 },
	{ 106394, 2, 63, 63, 13, 54, 3, kSequencePointKind_StepOut, 0, 302 },
	{ 106394, 2, 63, 63, 13, 54, 8, kSequencePointKind_StepOut, 0, 303 },
	{ 106394, 2, 64, 64, 9, 10, 16, kSequencePointKind_Normal, 0, 304 },
	{ 106395, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 305 },
	{ 106395, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 306 },
	{ 106395, 2, 67, 67, 9, 10, 0, kSequencePointKind_Normal, 0, 307 },
	{ 106395, 2, 68, 68, 13, 55, 1, kSequencePointKind_Normal, 0, 308 },
	{ 106395, 2, 68, 68, 13, 55, 3, kSequencePointKind_StepOut, 0, 309 },
	{ 106395, 2, 68, 68, 13, 55, 8, kSequencePointKind_StepOut, 0, 310 },
	{ 106395, 2, 69, 69, 9, 10, 16, kSequencePointKind_Normal, 0, 311 },
	{ 106396, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 312 },
	{ 106396, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 313 },
	{ 106396, 2, 72, 72, 9, 10, 0, kSequencePointKind_Normal, 0, 314 },
	{ 106396, 2, 73, 73, 13, 56, 1, kSequencePointKind_Normal, 0, 315 },
	{ 106396, 2, 73, 73, 13, 56, 3, kSequencePointKind_StepOut, 0, 316 },
	{ 106396, 2, 73, 73, 13, 56, 8, kSequencePointKind_StepOut, 0, 317 },
	{ 106396, 2, 74, 74, 9, 10, 16, kSequencePointKind_Normal, 0, 318 },
	{ 106397, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 319 },
	{ 106397, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 320 },
	{ 106397, 2, 77, 77, 9, 10, 0, kSequencePointKind_Normal, 0, 321 },
	{ 106397, 2, 78, 78, 13, 58, 1, kSequencePointKind_Normal, 0, 322 },
	{ 106397, 2, 78, 78, 13, 58, 3, kSequencePointKind_StepOut, 0, 323 },
	{ 106397, 2, 78, 78, 13, 58, 8, kSequencePointKind_StepOut, 0, 324 },
	{ 106397, 2, 79, 79, 9, 10, 16, kSequencePointKind_Normal, 0, 325 },
	{ 106398, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 326 },
	{ 106398, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 327 },
	{ 106398, 2, 82, 82, 9, 10, 0, kSequencePointKind_Normal, 0, 328 },
	{ 106398, 2, 83, 83, 13, 58, 1, kSequencePointKind_Normal, 0, 329 },
	{ 106398, 2, 83, 83, 13, 58, 3, kSequencePointKind_StepOut, 0, 330 },
	{ 106398, 2, 83, 83, 13, 58, 8, kSequencePointKind_StepOut, 0, 331 },
	{ 106398, 2, 84, 84, 9, 10, 16, kSequencePointKind_Normal, 0, 332 },
	{ 106399, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 333 },
	{ 106399, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 334 },
	{ 106399, 2, 87, 87, 9, 10, 0, kSequencePointKind_Normal, 0, 335 },
	{ 106399, 2, 88, 88, 13, 58, 1, kSequencePointKind_Normal, 0, 336 },
	{ 106399, 2, 88, 88, 13, 58, 3, kSequencePointKind_StepOut, 0, 337 },
	{ 106399, 2, 88, 88, 13, 58, 8, kSequencePointKind_StepOut, 0, 338 },
	{ 106399, 2, 89, 89, 9, 10, 16, kSequencePointKind_Normal, 0, 339 },
	{ 106400, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 340 },
	{ 106400, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 341 },
	{ 106400, 2, 92, 92, 9, 10, 0, kSequencePointKind_Normal, 0, 342 },
	{ 106400, 2, 93, 93, 13, 60, 1, kSequencePointKind_Normal, 0, 343 },
	{ 106400, 2, 93, 93, 13, 60, 3, kSequencePointKind_StepOut, 0, 344 },
	{ 106400, 2, 93, 93, 13, 60, 8, kSequencePointKind_StepOut, 0, 345 },
	{ 106400, 2, 94, 94, 9, 10, 16, kSequencePointKind_Normal, 0, 346 },
	{ 106401, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 347 },
	{ 106401, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 348 },
	{ 106401, 2, 97, 97, 9, 10, 0, kSequencePointKind_Normal, 0, 349 },
	{ 106401, 2, 98, 98, 13, 58, 1, kSequencePointKind_Normal, 0, 350 },
	{ 106401, 2, 98, 98, 13, 58, 3, kSequencePointKind_StepOut, 0, 351 },
	{ 106401, 2, 98, 98, 13, 58, 8, kSequencePointKind_StepOut, 0, 352 },
	{ 106401, 2, 99, 99, 9, 10, 16, kSequencePointKind_Normal, 0, 353 },
	{ 106402, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 354 },
	{ 106402, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 355 },
	{ 106402, 2, 102, 102, 9, 10, 0, kSequencePointKind_Normal, 0, 356 },
	{ 106402, 2, 103, 103, 13, 65, 1, kSequencePointKind_Normal, 0, 357 },
	{ 106402, 2, 103, 103, 13, 65, 3, kSequencePointKind_StepOut, 0, 358 },
	{ 106402, 2, 103, 103, 13, 65, 8, kSequencePointKind_StepOut, 0, 359 },
	{ 106402, 2, 104, 104, 9, 10, 16, kSequencePointKind_Normal, 0, 360 },
	{ 106403, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 361 },
	{ 106403, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 362 },
	{ 106403, 2, 107, 107, 9, 10, 0, kSequencePointKind_Normal, 0, 363 },
	{ 106403, 2, 108, 108, 13, 59, 1, kSequencePointKind_Normal, 0, 364 },
	{ 106403, 2, 108, 108, 13, 59, 3, kSequencePointKind_StepOut, 0, 365 },
	{ 106403, 2, 108, 108, 13, 59, 8, kSequencePointKind_StepOut, 0, 366 },
	{ 106403, 2, 109, 109, 9, 10, 16, kSequencePointKind_Normal, 0, 367 },
	{ 106404, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 368 },
	{ 106404, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 369 },
	{ 106404, 2, 112, 112, 9, 10, 0, kSequencePointKind_Normal, 0, 370 },
	{ 106404, 2, 113, 113, 13, 55, 1, kSequencePointKind_Normal, 0, 371 },
	{ 106404, 2, 113, 113, 13, 55, 3, kSequencePointKind_StepOut, 0, 372 },
	{ 106404, 2, 113, 113, 13, 55, 8, kSequencePointKind_StepOut, 0, 373 },
	{ 106404, 2, 114, 114, 9, 10, 16, kSequencePointKind_Normal, 0, 374 },
	{ 106421, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 375 },
	{ 106421, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 376 },
	{ 106421, 3, 73, 73, 9, 10, 0, kSequencePointKind_Normal, 0, 377 },
	{ 106421, 3, 74, 74, 13, 39, 1, kSequencePointKind_Normal, 0, 378 },
	{ 106421, 3, 74, 74, 13, 39, 2, kSequencePointKind_StepOut, 0, 379 },
	{ 106421, 3, 75, 75, 9, 10, 8, kSequencePointKind_Normal, 0, 380 },
	{ 106425, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 381 },
	{ 106425, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 382 },
	{ 106425, 3, 88, 88, 9, 10, 0, kSequencePointKind_Normal, 0, 383 },
	{ 106425, 3, 89, 89, 13, 58, 1, kSequencePointKind_Normal, 0, 384 },
	{ 106425, 3, 89, 89, 13, 58, 7, kSequencePointKind_StepOut, 0, 385 },
	{ 106425, 3, 90, 90, 13, 93, 13, kSequencePointKind_Normal, 0, 386 },
	{ 106425, 3, 90, 90, 13, 93, 25, kSequencePointKind_StepOut, 0, 387 },
	{ 106425, 3, 91, 91, 9, 10, 31, kSequencePointKind_Normal, 0, 388 },
	{ 106426, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 389 },
	{ 106426, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 390 },
	{ 106426, 3, 94, 94, 9, 10, 0, kSequencePointKind_Normal, 0, 391 },
	{ 106426, 3, 95, 95, 13, 58, 1, kSequencePointKind_Normal, 0, 392 },
	{ 106426, 3, 95, 95, 13, 58, 7, kSequencePointKind_StepOut, 0, 393 },
	{ 106426, 3, 96, 96, 9, 10, 13, kSequencePointKind_Normal, 0, 394 },
	{ 106428, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 395 },
	{ 106428, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 396 },
	{ 106428, 3, 102, 102, 9, 10, 0, kSequencePointKind_Normal, 0, 397 },
	{ 106428, 3, 103, 103, 13, 92, 1, kSequencePointKind_Normal, 0, 398 },
	{ 106428, 3, 103, 103, 13, 92, 13, kSequencePointKind_StepOut, 0, 399 },
	{ 106428, 3, 104, 104, 9, 10, 19, kSequencePointKind_Normal, 0, 400 },
	{ 106429, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 401 },
	{ 106429, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 402 },
	{ 106429, 3, 108, 108, 9, 10, 0, kSequencePointKind_Normal, 0, 403 },
	{ 106429, 3, 109, 109, 13, 81, 1, kSequencePointKind_Normal, 0, 404 },
	{ 106429, 3, 109, 109, 13, 81, 9, kSequencePointKind_StepOut, 0, 405 },
	{ 106429, 3, 110, 110, 9, 10, 15, kSequencePointKind_Normal, 0, 406 },
	{ 106430, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 407 },
	{ 106430, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 408 },
	{ 106430, 3, 113, 113, 9, 10, 0, kSequencePointKind_Normal, 0, 409 },
	{ 106430, 3, 114, 114, 13, 81, 1, kSequencePointKind_Normal, 0, 410 },
	{ 106430, 3, 114, 114, 13, 81, 10, kSequencePointKind_StepOut, 0, 411 },
	{ 106430, 3, 115, 115, 9, 10, 16, kSequencePointKind_Normal, 0, 412 },
	{ 106434, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 413 },
	{ 106434, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 414 },
	{ 106434, 3, 84, 84, 9, 153, 0, kSequencePointKind_Normal, 0, 415 },
	{ 106443, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 416 },
	{ 106443, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 417 },
	{ 106443, 4, 25, 25, 36, 65, 0, kSequencePointKind_Normal, 0, 418 },
	{ 106443, 4, 25, 25, 36, 65, 1, kSequencePointKind_StepOut, 0, 419 },
	{ 106443, 4, 25, 25, 36, 65, 7, kSequencePointKind_StepOut, 0, 420 },
	{ 106443, 4, 26, 26, 9, 10, 13, kSequencePointKind_Normal, 0, 421 },
	{ 106443, 4, 27, 27, 9, 10, 14, kSequencePointKind_Normal, 0, 422 },
	{ 106444, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 423 },
	{ 106444, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 424 },
	{ 106444, 4, 29, 29, 9, 57, 0, kSequencePointKind_Normal, 0, 425 },
	{ 106444, 4, 29, 29, 9, 57, 1, kSequencePointKind_StepOut, 0, 426 },
	{ 106444, 4, 30, 30, 9, 10, 7, kSequencePointKind_Normal, 0, 427 },
	{ 106444, 4, 31, 31, 13, 25, 8, kSequencePointKind_Normal, 0, 428 },
	{ 106444, 4, 32, 32, 13, 29, 15, kSequencePointKind_Normal, 0, 429 },
	{ 106444, 4, 33, 33, 9, 10, 22, kSequencePointKind_Normal, 0, 430 },
	{ 106446, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 431 },
	{ 106446, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 432 },
	{ 106446, 4, 39, 39, 9, 10, 0, kSequencePointKind_Normal, 0, 433 },
	{ 106446, 4, 40, 40, 13, 72, 1, kSequencePointKind_Normal, 0, 434 },
	{ 106446, 4, 40, 40, 13, 72, 7, kSequencePointKind_StepOut, 0, 435 },
	{ 106446, 4, 41, 41, 13, 43, 13, kSequencePointKind_Normal, 0, 436 },
	{ 106446, 4, 41, 41, 13, 43, 14, kSequencePointKind_StepOut, 0, 437 },
	{ 106446, 4, 42, 42, 13, 33, 20, kSequencePointKind_Normal, 0, 438 },
	{ 106446, 4, 43, 43, 9, 10, 24, kSequencePointKind_Normal, 0, 439 },
	{ 106447, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 440 },
	{ 106447, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 441 },
	{ 106447, 4, 46, 46, 9, 10, 0, kSequencePointKind_Normal, 0, 442 },
	{ 106447, 4, 47, 47, 13, 25, 1, kSequencePointKind_Normal, 0, 443 },
	{ 106447, 4, 47, 47, 0, 0, 8, kSequencePointKind_Normal, 0, 444 },
	{ 106447, 4, 48, 48, 17, 107, 11, kSequencePointKind_Normal, 0, 445 },
	{ 106447, 4, 48, 48, 17, 107, 16, kSequencePointKind_StepOut, 0, 446 },
	{ 106447, 4, 50, 50, 13, 46, 22, kSequencePointKind_Normal, 0, 447 },
	{ 106447, 4, 50, 50, 0, 0, 32, kSequencePointKind_Normal, 0, 448 },
	{ 106447, 4, 51, 51, 17, 99, 35, kSequencePointKind_Normal, 0, 449 },
	{ 106447, 4, 51, 51, 17, 99, 40, kSequencePointKind_StepOut, 0, 450 },
	{ 106447, 4, 53, 53, 13, 84, 46, kSequencePointKind_Normal, 0, 451 },
	{ 106447, 4, 53, 53, 13, 84, 47, kSequencePointKind_StepOut, 0, 452 },
	{ 106447, 4, 54, 54, 9, 10, 57, kSequencePointKind_Normal, 0, 453 },
	{ 106448, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 454 },
	{ 106448, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 455 },
	{ 106448, 4, 58, 58, 9, 10, 0, kSequencePointKind_Normal, 0, 456 },
	{ 106448, 4, 59, 59, 13, 25, 1, kSequencePointKind_Normal, 0, 457 },
	{ 106448, 4, 59, 59, 0, 0, 8, kSequencePointKind_Normal, 0, 458 },
	{ 106448, 4, 60, 60, 17, 107, 11, kSequencePointKind_Normal, 0, 459 },
	{ 106448, 4, 60, 60, 17, 107, 16, kSequencePointKind_StepOut, 0, 460 },
	{ 106448, 4, 62, 62, 13, 46, 22, kSequencePointKind_Normal, 0, 461 },
	{ 106448, 4, 62, 62, 0, 0, 32, kSequencePointKind_Normal, 0, 462 },
	{ 106448, 4, 63, 63, 17, 81, 35, kSequencePointKind_Normal, 0, 463 },
	{ 106448, 4, 63, 63, 17, 81, 40, kSequencePointKind_StepOut, 0, 464 },
	{ 106448, 4, 65, 65, 13, 39, 46, kSequencePointKind_Normal, 0, 465 },
	{ 106448, 4, 66, 66, 13, 68, 53, kSequencePointKind_Normal, 0, 466 },
	{ 106448, 4, 66, 66, 13, 68, 60, kSequencePointKind_StepOut, 0, 467 },
	{ 106448, 4, 67, 67, 9, 10, 66, kSequencePointKind_Normal, 0, 468 },
	{ 106449, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 469 },
	{ 106449, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 470 },
	{ 106449, 4, 70, 70, 9, 10, 0, kSequencePointKind_Normal, 0, 471 },
	{ 106449, 4, 71, 71, 13, 26, 1, kSequencePointKind_Normal, 0, 472 },
	{ 106449, 4, 72, 72, 9, 10, 10, kSequencePointKind_Normal, 0, 473 },
	{ 106450, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 474 },
	{ 106450, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 475 },
	{ 106450, 4, 75, 75, 9, 10, 0, kSequencePointKind_Normal, 0, 476 },
	{ 106450, 4, 76, 76, 13, 49, 1, kSequencePointKind_Normal, 0, 477 },
	{ 106450, 4, 76, 76, 13, 49, 12, kSequencePointKind_StepOut, 0, 478 },
	{ 106450, 4, 76, 76, 0, 0, 29, kSequencePointKind_Normal, 0, 479 },
	{ 106450, 4, 77, 77, 13, 14, 32, kSequencePointKind_Normal, 0, 480 },
	{ 106450, 4, 78, 78, 17, 41, 33, kSequencePointKind_Normal, 0, 481 },
	{ 106450, 4, 78, 78, 17, 41, 39, kSequencePointKind_StepOut, 0, 482 },
	{ 106450, 4, 79, 79, 13, 14, 45, kSequencePointKind_Normal, 0, 483 },
	{ 106450, 4, 80, 80, 13, 33, 46, kSequencePointKind_Normal, 0, 484 },
	{ 106450, 4, 81, 81, 13, 41, 57, kSequencePointKind_Normal, 0, 485 },
	{ 106450, 4, 82, 82, 9, 10, 64, kSequencePointKind_Normal, 0, 486 },
	{ 106451, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 487 },
	{ 106451, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 488 },
	{ 106451, 4, 85, 85, 9, 10, 0, kSequencePointKind_Normal, 0, 489 },
	{ 106451, 4, 85, 85, 9, 10, 1, kSequencePointKind_Normal, 0, 490 },
	{ 106451, 4, 86, 86, 13, 23, 2, kSequencePointKind_Normal, 0, 491 },
	{ 106451, 4, 86, 86, 13, 23, 3, kSequencePointKind_StepOut, 0, 492 },
	{ 106451, 4, 87, 87, 9, 10, 11, kSequencePointKind_Normal, 0, 493 },
	{ 106451, 4, 87, 87, 9, 10, 12, kSequencePointKind_StepOut, 0, 494 },
	{ 106451, 4, 87, 87, 9, 10, 19, kSequencePointKind_Normal, 0, 495 },
	{ 106452, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 496 },
	{ 106452, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 497 },
	{ 106452, 4, 90, 90, 9, 10, 0, kSequencePointKind_Normal, 0, 498 },
	{ 106452, 4, 91, 91, 13, 23, 1, kSequencePointKind_Normal, 0, 499 },
	{ 106452, 4, 91, 91, 13, 23, 2, kSequencePointKind_StepOut, 0, 500 },
	{ 106452, 4, 92, 92, 13, 39, 8, kSequencePointKind_Normal, 0, 501 },
	{ 106452, 4, 92, 92, 13, 39, 9, kSequencePointKind_StepOut, 0, 502 },
	{ 106452, 4, 93, 93, 9, 10, 15, kSequencePointKind_Normal, 0, 503 },
	{ 106454, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 504 },
	{ 106454, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 505 },
	{ 106454, 4, 101, 101, 13, 14, 0, kSequencePointKind_Normal, 0, 506 },
	{ 106454, 4, 102, 102, 17, 65, 1, kSequencePointKind_Normal, 0, 507 },
	{ 106454, 4, 102, 102, 17, 65, 2, kSequencePointKind_StepOut, 0, 508 },
	{ 106454, 4, 103, 103, 13, 14, 13, kSequencePointKind_Normal, 0, 509 },
	{ 106455, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 510 },
	{ 106455, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 511 },
	{ 106455, 4, 105, 105, 13, 14, 0, kSequencePointKind_Normal, 0, 512 },
	{ 106455, 4, 106, 106, 17, 96, 1, kSequencePointKind_Normal, 0, 513 },
	{ 106455, 4, 106, 106, 17, 96, 9, kSequencePointKind_StepOut, 0, 514 },
	{ 106455, 4, 107, 107, 13, 14, 15, kSequencePointKind_Normal, 0, 515 },
	{ 106476, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 516 },
	{ 106476, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 517 },
	{ 106476, 4, 125, 125, 13, 14, 0, kSequencePointKind_Normal, 0, 518 },
	{ 106476, 4, 126, 126, 17, 62, 1, kSequencePointKind_Normal, 0, 519 },
	{ 106476, 4, 126, 126, 0, 0, 22, kSequencePointKind_Normal, 0, 520 },
	{ 106476, 4, 127, 127, 21, 49, 25, kSequencePointKind_Normal, 0, 521 },
	{ 106476, 4, 130, 130, 17, 56, 34, kSequencePointKind_Normal, 0, 522 },
	{ 106476, 4, 130, 130, 17, 56, 35, kSequencePointKind_StepOut, 0, 523 },
	{ 106476, 4, 131, 131, 13, 14, 43, kSequencePointKind_Normal, 0, 524 },
	{ 106486, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 525 },
	{ 106486, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 526 },
	{ 106486, 5, 53, 53, 9, 10, 0, kSequencePointKind_Normal, 0, 527 },
	{ 106486, 5, 54, 54, 13, 67, 1, kSequencePointKind_Normal, 0, 528 },
	{ 106486, 5, 54, 54, 13, 67, 3, kSequencePointKind_StepOut, 0, 529 },
	{ 106486, 5, 54, 54, 13, 67, 8, kSequencePointKind_StepOut, 0, 530 },
	{ 106486, 5, 55, 55, 9, 10, 16, kSequencePointKind_Normal, 0, 531 },
	{ 106488, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 532 },
	{ 106488, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 533 },
	{ 106488, 5, 36, 36, 9, 85, 0, kSequencePointKind_Normal, 0, 534 },
	{ 106488, 5, 36, 36, 9, 85, 5, kSequencePointKind_StepOut, 0, 535 },
	{ 106488, 5, 37, 37, 9, 85, 15, kSequencePointKind_Normal, 0, 536 },
	{ 106488, 5, 37, 37, 9, 85, 20, kSequencePointKind_StepOut, 0, 537 },
	{ 106489, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 538 },
	{ 106489, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 539 },
	{ 106489, 5, 60, 60, 29, 33, 0, kSequencePointKind_Normal, 0, 540 },
	{ 106490, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 541 },
	{ 106490, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 542 },
	{ 106490, 5, 61, 61, 51, 55, 0, kSequencePointKind_Normal, 0, 543 },
	{ 106491, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 544 },
	{ 106491, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 545 },
	{ 106491, 5, 64, 64, 9, 10, 0, kSequencePointKind_Normal, 0, 546 },
	{ 106491, 5, 65, 65, 13, 34, 1, kSequencePointKind_Normal, 0, 547 },
	{ 106491, 5, 66, 66, 13, 50, 8, kSequencePointKind_Normal, 0, 548 },
	{ 106491, 5, 67, 67, 9, 10, 15, kSequencePointKind_Normal, 0, 549 },
	{ 106507, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 550 },
	{ 106507, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 551 },
	{ 106507, 5, 100, 100, 9, 10, 0, kSequencePointKind_Normal, 0, 552 },
	{ 106507, 5, 101, 101, 13, 43, 1, kSequencePointKind_Normal, 0, 553 },
	{ 106507, 5, 101, 101, 13, 43, 2, kSequencePointKind_StepOut, 0, 554 },
	{ 106507, 5, 101, 101, 13, 43, 8, kSequencePointKind_StepOut, 0, 555 },
	{ 106507, 5, 101, 101, 0, 0, 14, kSequencePointKind_Normal, 0, 556 },
	{ 106507, 5, 102, 102, 17, 29, 17, kSequencePointKind_Normal, 0, 557 },
	{ 106507, 5, 103, 103, 13, 112, 21, kSequencePointKind_Normal, 0, 558 },
	{ 106507, 5, 103, 103, 13, 112, 22, kSequencePointKind_StepOut, 0, 559 },
	{ 106507, 5, 103, 103, 13, 112, 27, kSequencePointKind_StepOut, 0, 560 },
	{ 106507, 5, 104, 104, 13, 38, 33, kSequencePointKind_Normal, 0, 561 },
	{ 106507, 5, 105, 105, 9, 10, 37, kSequencePointKind_Normal, 0, 562 },
	{ 106508, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 563 },
	{ 106508, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 564 },
	{ 106508, 5, 108, 108, 9, 10, 0, kSequencePointKind_Normal, 0, 565 },
	{ 106508, 5, 109, 109, 13, 88, 1, kSequencePointKind_Normal, 0, 566 },
	{ 106508, 5, 109, 109, 13, 88, 5, kSequencePointKind_StepOut, 0, 567 },
	{ 106508, 5, 109, 109, 13, 88, 11, kSequencePointKind_StepOut, 0, 568 },
	{ 106508, 5, 109, 109, 13, 88, 16, kSequencePointKind_StepOut, 0, 569 },
	{ 106508, 5, 109, 109, 0, 0, 25, kSequencePointKind_Normal, 0, 570 },
	{ 106508, 5, 110, 110, 13, 14, 28, kSequencePointKind_Normal, 0, 571 },
	{ 106508, 5, 111, 111, 17, 189, 29, kSequencePointKind_Normal, 0, 572 },
	{ 106508, 5, 111, 111, 17, 189, 34, kSequencePointKind_StepOut, 0, 573 },
	{ 106508, 5, 113, 113, 9, 10, 40, kSequencePointKind_Normal, 0, 574 },
	{ 106510, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 575 },
	{ 106510, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 576 },
	{ 106510, 5, 119, 119, 9, 10, 0, kSequencePointKind_Normal, 0, 577 },
	{ 106510, 5, 120, 120, 13, 57, 1, kSequencePointKind_Normal, 0, 578 },
	{ 106510, 5, 120, 120, 13, 57, 3, kSequencePointKind_StepOut, 0, 579 },
	{ 106510, 5, 121, 121, 13, 62, 9, kSequencePointKind_Normal, 0, 580 },
	{ 106510, 5, 121, 121, 13, 62, 12, kSequencePointKind_StepOut, 0, 581 },
	{ 106510, 5, 122, 122, 9, 10, 18, kSequencePointKind_Normal, 0, 582 },
	{ 106511, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 583 },
	{ 106511, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 584 },
	{ 106511, 5, 125, 125, 9, 10, 0, kSequencePointKind_Normal, 0, 585 },
	{ 106511, 5, 126, 126, 13, 71, 1, kSequencePointKind_Normal, 0, 586 },
	{ 106511, 5, 126, 126, 13, 71, 3, kSequencePointKind_StepOut, 0, 587 },
	{ 106511, 5, 126, 126, 13, 71, 9, kSequencePointKind_StepOut, 0, 588 },
	{ 106511, 5, 127, 127, 9, 10, 15, kSequencePointKind_Normal, 0, 589 },
	{ 106512, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 590 },
	{ 106512, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 591 },
	{ 106512, 5, 130, 130, 9, 10, 0, kSequencePointKind_Normal, 0, 592 },
	{ 106512, 5, 131, 131, 13, 52, 1, kSequencePointKind_Normal, 0, 593 },
	{ 106512, 5, 131, 131, 13, 52, 4, kSequencePointKind_StepOut, 0, 594 },
	{ 106512, 5, 132, 132, 9, 10, 10, kSequencePointKind_Normal, 0, 595 },
	{ 106513, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 596 },
	{ 106513, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 597 },
	{ 106513, 5, 135, 135, 9, 10, 0, kSequencePointKind_Normal, 0, 598 },
	{ 106513, 5, 136, 136, 13, 61, 1, kSequencePointKind_Normal, 0, 599 },
	{ 106513, 5, 136, 136, 13, 61, 3, kSequencePointKind_StepOut, 0, 600 },
	{ 106513, 5, 136, 136, 13, 61, 9, kSequencePointKind_StepOut, 0, 601 },
	{ 106513, 5, 137, 137, 9, 10, 15, kSequencePointKind_Normal, 0, 602 },
	{ 106514, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 603 },
	{ 106514, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 604 },
	{ 106514, 5, 140, 140, 9, 10, 0, kSequencePointKind_Normal, 0, 605 },
	{ 106514, 5, 141, 141, 13, 70, 1, kSequencePointKind_Normal, 0, 606 },
	{ 106514, 5, 141, 141, 13, 70, 8, kSequencePointKind_StepOut, 0, 607 },
	{ 106514, 5, 142, 142, 9, 10, 14, kSequencePointKind_Normal, 0, 608 },
	{ 106515, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 609 },
	{ 106515, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 610 },
	{ 106515, 5, 145, 145, 9, 10, 0, kSequencePointKind_Normal, 0, 611 },
	{ 106515, 5, 146, 146, 13, 54, 1, kSequencePointKind_Normal, 0, 612 },
	{ 106515, 5, 146, 146, 13, 54, 7, kSequencePointKind_StepOut, 0, 613 },
	{ 106515, 5, 147, 147, 9, 10, 13, kSequencePointKind_Normal, 0, 614 },
	{ 106516, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 615 },
	{ 106516, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 616 },
	{ 106516, 5, 150, 150, 9, 10, 0, kSequencePointKind_Normal, 0, 617 },
	{ 106516, 5, 151, 151, 13, 70, 1, kSequencePointKind_Normal, 0, 618 },
	{ 106516, 5, 151, 151, 13, 70, 8, kSequencePointKind_StepOut, 0, 619 },
	{ 106516, 5, 152, 152, 9, 10, 14, kSequencePointKind_Normal, 0, 620 },
	{ 106517, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 621 },
	{ 106517, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 622 },
	{ 106517, 5, 155, 155, 9, 10, 0, kSequencePointKind_Normal, 0, 623 },
	{ 106517, 5, 156, 156, 13, 54, 1, kSequencePointKind_Normal, 0, 624 },
	{ 106517, 5, 156, 156, 13, 54, 7, kSequencePointKind_StepOut, 0, 625 },
	{ 106517, 5, 157, 157, 9, 10, 13, kSequencePointKind_Normal, 0, 626 },
	{ 106518, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 627 },
	{ 106518, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 628 },
	{ 106518, 5, 160, 160, 9, 10, 0, kSequencePointKind_Normal, 0, 629 },
	{ 106518, 5, 161, 161, 13, 26, 1, kSequencePointKind_Normal, 0, 630 },
	{ 106518, 5, 161, 161, 13, 26, 3, kSequencePointKind_StepOut, 0, 631 },
	{ 106518, 5, 162, 162, 9, 10, 9, kSequencePointKind_Normal, 0, 632 },
	{ 106564, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 633 },
	{ 106564, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 634 },
	{ 106564, 5, 224, 224, 9, 10, 0, kSequencePointKind_Normal, 0, 635 },
	{ 106564, 5, 225, 225, 13, 43, 1, kSequencePointKind_Normal, 0, 636 },
	{ 106564, 5, 225, 225, 13, 43, 1, kSequencePointKind_StepOut, 0, 637 },
	{ 106564, 5, 226, 226, 13, 52, 7, kSequencePointKind_Normal, 0, 638 },
	{ 106564, 5, 226, 226, 13, 52, 10, kSequencePointKind_StepOut, 0, 639 },
	{ 106564, 5, 227, 227, 13, 29, 16, kSequencePointKind_Normal, 0, 640 },
	{ 106564, 5, 228, 228, 9, 10, 20, kSequencePointKind_Normal, 0, 641 },
	{ 106566, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 642 },
	{ 106566, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 643 },
	{ 106566, 5, 233, 233, 9, 10, 0, kSequencePointKind_Normal, 0, 644 },
	{ 106566, 5, 234, 234, 13, 46, 1, kSequencePointKind_Normal, 0, 645 },
	{ 106566, 5, 234, 234, 13, 46, 1, kSequencePointKind_StepOut, 0, 646 },
	{ 106566, 5, 235, 235, 13, 55, 7, kSequencePointKind_Normal, 0, 647 },
	{ 106566, 5, 235, 235, 13, 55, 10, kSequencePointKind_StepOut, 0, 648 },
	{ 106566, 5, 236, 236, 13, 26, 16, kSequencePointKind_Normal, 0, 649 },
	{ 106566, 5, 237, 237, 9, 10, 20, kSequencePointKind_Normal, 0, 650 },
	{ 106573, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 651 },
	{ 106573, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 652 },
	{ 106573, 5, 249, 249, 9, 10, 0, kSequencePointKind_Normal, 0, 653 },
	{ 106573, 5, 250, 250, 13, 36, 1, kSequencePointKind_Normal, 0, 654 },
	{ 106573, 5, 250, 250, 0, 0, 6, kSequencePointKind_Normal, 0, 655 },
	{ 106573, 5, 251, 251, 17, 108, 9, kSequencePointKind_Normal, 0, 656 },
	{ 106573, 5, 251, 251, 17, 108, 14, kSequencePointKind_StepOut, 0, 657 },
	{ 106573, 5, 252, 252, 13, 46, 20, kSequencePointKind_Normal, 0, 658 },
	{ 106573, 5, 252, 252, 13, 46, 21, kSequencePointKind_StepOut, 0, 659 },
	{ 106573, 5, 253, 253, 13, 36, 27, kSequencePointKind_Normal, 0, 660 },
	{ 106573, 5, 253, 253, 13, 36, 33, kSequencePointKind_StepOut, 0, 661 },
	{ 106573, 5, 253, 253, 0, 0, 39, kSequencePointKind_Normal, 0, 662 },
	{ 106573, 5, 254, 254, 17, 117, 42, kSequencePointKind_Normal, 0, 663 },
	{ 106573, 5, 254, 254, 17, 117, 47, kSequencePointKind_StepOut, 0, 664 },
	{ 106573, 5, 255, 255, 13, 45, 53, kSequencePointKind_Normal, 0, 665 },
	{ 106573, 5, 255, 255, 13, 45, 56, kSequencePointKind_StepOut, 0, 666 },
	{ 106573, 5, 256, 256, 9, 10, 62, kSequencePointKind_Normal, 0, 667 },
	{ 106574, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 668 },
	{ 106574, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 669 },
	{ 106574, 5, 259, 259, 9, 10, 0, kSequencePointKind_Normal, 0, 670 },
	{ 106574, 5, 260, 260, 13, 52, 1, kSequencePointKind_Normal, 0, 671 },
	{ 106574, 5, 260, 260, 13, 52, 1, kSequencePointKind_StepOut, 0, 672 },
	{ 106574, 5, 261, 261, 13, 52, 7, kSequencePointKind_Normal, 0, 673 },
	{ 106574, 5, 261, 261, 13, 52, 10, kSequencePointKind_StepOut, 0, 674 },
	{ 106574, 5, 262, 262, 13, 31, 16, kSequencePointKind_Normal, 0, 675 },
	{ 106574, 5, 263, 263, 9, 10, 20, kSequencePointKind_Normal, 0, 676 },
	{ 106575, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 677 },
	{ 106575, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 678 },
	{ 106575, 5, 266, 266, 9, 10, 0, kSequencePointKind_Normal, 0, 679 },
	{ 106575, 5, 267, 267, 13, 46, 1, kSequencePointKind_Normal, 0, 680 },
	{ 106575, 5, 267, 267, 13, 46, 2, kSequencePointKind_StepOut, 0, 681 },
	{ 106575, 5, 268, 268, 13, 67, 8, kSequencePointKind_Normal, 0, 682 },
	{ 106575, 5, 268, 268, 13, 67, 10, kSequencePointKind_StepOut, 0, 683 },
	{ 106575, 5, 268, 268, 13, 67, 19, kSequencePointKind_StepOut, 0, 684 },
	{ 106575, 5, 269, 269, 9, 10, 30, kSequencePointKind_Normal, 0, 685 },
	{ 106576, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 686 },
	{ 106576, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 687 },
	{ 106576, 5, 272, 272, 9, 10, 0, kSequencePointKind_Normal, 0, 688 },
	{ 106576, 5, 273, 273, 13, 31, 1, kSequencePointKind_Normal, 0, 689 },
	{ 106576, 5, 273, 273, 0, 0, 6, kSequencePointKind_Normal, 0, 690 },
	{ 106576, 5, 274, 274, 17, 64, 9, kSequencePointKind_Normal, 0, 691 },
	{ 106576, 5, 274, 274, 17, 64, 14, kSequencePointKind_StepOut, 0, 692 },
	{ 106576, 5, 276, 276, 13, 46, 20, kSequencePointKind_Normal, 0, 693 },
	{ 106576, 5, 276, 276, 13, 46, 21, kSequencePointKind_StepOut, 0, 694 },
	{ 106576, 5, 277, 277, 13, 26, 27, kSequencePointKind_Normal, 0, 695 },
	{ 106576, 5, 277, 277, 13, 26, 28, kSequencePointKind_StepOut, 0, 696 },
	{ 106576, 5, 277, 277, 0, 0, 34, kSequencePointKind_Normal, 0, 697 },
	{ 106576, 5, 278, 278, 17, 48, 37, kSequencePointKind_Normal, 0, 698 },
	{ 106576, 5, 278, 278, 17, 48, 39, kSequencePointKind_StepOut, 0, 699 },
	{ 106576, 5, 278, 278, 0, 0, 45, kSequencePointKind_Normal, 0, 700 },
	{ 106576, 5, 280, 280, 17, 31, 47, kSequencePointKind_Normal, 0, 701 },
	{ 106576, 5, 280, 280, 17, 31, 48, kSequencePointKind_StepOut, 0, 702 },
	{ 106576, 5, 281, 281, 9, 10, 54, kSequencePointKind_Normal, 0, 703 },
	{ 106577, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 704 },
	{ 106577, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 705 },
	{ 106577, 5, 284, 284, 9, 10, 0, kSequencePointKind_Normal, 0, 706 },
	{ 106577, 5, 285, 285, 13, 31, 1, kSequencePointKind_Normal, 0, 707 },
	{ 106577, 5, 285, 285, 0, 0, 6, kSequencePointKind_Normal, 0, 708 },
	{ 106577, 5, 286, 286, 17, 64, 9, kSequencePointKind_Normal, 0, 709 },
	{ 106577, 5, 286, 286, 17, 64, 14, kSequencePointKind_StepOut, 0, 710 },
	{ 106577, 5, 288, 288, 13, 46, 20, kSequencePointKind_Normal, 0, 711 },
	{ 106577, 5, 288, 288, 13, 46, 21, kSequencePointKind_StepOut, 0, 712 },
	{ 106577, 5, 289, 289, 13, 26, 27, kSequencePointKind_Normal, 0, 713 },
	{ 106577, 5, 289, 289, 13, 26, 28, kSequencePointKind_StepOut, 0, 714 },
	{ 106577, 5, 289, 289, 0, 0, 34, kSequencePointKind_Normal, 0, 715 },
	{ 106577, 5, 290, 290, 17, 56, 37, kSequencePointKind_Normal, 0, 716 },
	{ 106577, 5, 290, 290, 17, 56, 39, kSequencePointKind_StepOut, 0, 717 },
	{ 106577, 5, 290, 290, 0, 0, 45, kSequencePointKind_Normal, 0, 718 },
	{ 106577, 5, 292, 292, 17, 31, 47, kSequencePointKind_Normal, 0, 719 },
	{ 106577, 5, 292, 292, 17, 31, 48, kSequencePointKind_StepOut, 0, 720 },
	{ 106577, 5, 293, 293, 9, 10, 54, kSequencePointKind_Normal, 0, 721 },
	{ 106578, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 722 },
	{ 106578, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 723 },
	{ 106578, 5, 296, 296, 9, 10, 0, kSequencePointKind_Normal, 0, 724 },
	{ 106578, 5, 297, 297, 13, 31, 1, kSequencePointKind_Normal, 0, 725 },
	{ 106578, 5, 297, 297, 0, 0, 6, kSequencePointKind_Normal, 0, 726 },
	{ 106578, 5, 298, 298, 17, 64, 9, kSequencePointKind_Normal, 0, 727 },
	{ 106578, 5, 298, 298, 17, 64, 14, kSequencePointKind_StepOut, 0, 728 },
	{ 106578, 5, 300, 300, 13, 46, 20, kSequencePointKind_Normal, 0, 729 },
	{ 106578, 5, 300, 300, 13, 46, 21, kSequencePointKind_StepOut, 0, 730 },
	{ 106578, 5, 301, 301, 13, 26, 27, kSequencePointKind_Normal, 0, 731 },
	{ 106578, 5, 301, 301, 13, 26, 28, kSequencePointKind_StepOut, 0, 732 },
	{ 106578, 5, 301, 301, 0, 0, 34, kSequencePointKind_Normal, 0, 733 },
	{ 106578, 5, 302, 302, 17, 53, 37, kSequencePointKind_Normal, 0, 734 },
	{ 106578, 5, 302, 302, 17, 53, 39, kSequencePointKind_StepOut, 0, 735 },
	{ 106578, 5, 302, 302, 0, 0, 45, kSequencePointKind_Normal, 0, 736 },
	{ 106578, 5, 304, 304, 17, 31, 47, kSequencePointKind_Normal, 0, 737 },
	{ 106578, 5, 304, 304, 17, 31, 48, kSequencePointKind_StepOut, 0, 738 },
	{ 106578, 5, 305, 305, 9, 10, 54, kSequencePointKind_Normal, 0, 739 },
	{ 106579, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 740 },
	{ 106579, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 741 },
	{ 106579, 5, 308, 308, 9, 10, 0, kSequencePointKind_Normal, 0, 742 },
	{ 106579, 5, 309, 309, 13, 31, 1, kSequencePointKind_Normal, 0, 743 },
	{ 106579, 5, 309, 309, 0, 0, 6, kSequencePointKind_Normal, 0, 744 },
	{ 106579, 5, 310, 310, 17, 64, 9, kSequencePointKind_Normal, 0, 745 },
	{ 106579, 5, 310, 310, 17, 64, 14, kSequencePointKind_StepOut, 0, 746 },
	{ 106579, 5, 312, 312, 13, 46, 20, kSequencePointKind_Normal, 0, 747 },
	{ 106579, 5, 312, 312, 13, 46, 21, kSequencePointKind_StepOut, 0, 748 },
	{ 106579, 5, 313, 313, 13, 26, 27, kSequencePointKind_Normal, 0, 749 },
	{ 106579, 5, 313, 313, 13, 26, 28, kSequencePointKind_StepOut, 0, 750 },
	{ 106579, 5, 313, 313, 0, 0, 34, kSequencePointKind_Normal, 0, 751 },
	{ 106579, 5, 314, 314, 17, 53, 37, kSequencePointKind_Normal, 0, 752 },
	{ 106579, 5, 314, 314, 17, 53, 39, kSequencePointKind_StepOut, 0, 753 },
	{ 106579, 5, 314, 314, 0, 0, 45, kSequencePointKind_Normal, 0, 754 },
	{ 106579, 5, 316, 316, 17, 31, 47, kSequencePointKind_Normal, 0, 755 },
	{ 106579, 5, 316, 316, 17, 31, 48, kSequencePointKind_StepOut, 0, 756 },
	{ 106579, 5, 317, 317, 9, 10, 54, kSequencePointKind_Normal, 0, 757 },
	{ 106580, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 758 },
	{ 106580, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 759 },
	{ 106580, 5, 320, 320, 9, 10, 0, kSequencePointKind_Normal, 0, 760 },
	{ 106580, 5, 321, 321, 13, 54, 1, kSequencePointKind_Normal, 0, 761 },
	{ 106580, 5, 321, 321, 13, 54, 3, kSequencePointKind_StepOut, 0, 762 },
	{ 106580, 5, 321, 321, 13, 54, 8, kSequencePointKind_StepOut, 0, 763 },
	{ 106580, 5, 322, 322, 9, 10, 14, kSequencePointKind_Normal, 0, 764 },
	{ 106581, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 765 },
	{ 106581, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 766 },
	{ 106581, 5, 326, 326, 9, 10, 0, kSequencePointKind_Normal, 0, 767 },
	{ 106581, 5, 327, 327, 13, 54, 1, kSequencePointKind_Normal, 0, 768 },
	{ 106581, 5, 327, 327, 13, 54, 3, kSequencePointKind_StepOut, 0, 769 },
	{ 106581, 5, 327, 327, 13, 54, 8, kSequencePointKind_StepOut, 0, 770 },
	{ 106581, 5, 328, 328, 9, 10, 16, kSequencePointKind_Normal, 0, 771 },
	{ 106582, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 772 },
	{ 106582, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 773 },
	{ 106582, 5, 331, 331, 9, 10, 0, kSequencePointKind_Normal, 0, 774 },
	{ 106582, 5, 332, 332, 13, 55, 1, kSequencePointKind_Normal, 0, 775 },
	{ 106582, 5, 332, 332, 13, 55, 3, kSequencePointKind_StepOut, 0, 776 },
	{ 106582, 5, 332, 332, 13, 55, 8, kSequencePointKind_StepOut, 0, 777 },
	{ 106582, 5, 333, 333, 9, 10, 16, kSequencePointKind_Normal, 0, 778 },
	{ 106583, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 779 },
	{ 106583, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 780 },
	{ 106583, 5, 336, 336, 9, 10, 0, kSequencePointKind_Normal, 0, 781 },
	{ 106583, 5, 337, 337, 13, 56, 1, kSequencePointKind_Normal, 0, 782 },
	{ 106583, 5, 337, 337, 13, 56, 3, kSequencePointKind_StepOut, 0, 783 },
	{ 106583, 5, 337, 337, 13, 56, 8, kSequencePointKind_StepOut, 0, 784 },
	{ 106583, 5, 338, 338, 9, 10, 16, kSequencePointKind_Normal, 0, 785 },
	{ 106584, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 786 },
	{ 106584, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 787 },
	{ 106584, 5, 341, 341, 9, 10, 0, kSequencePointKind_Normal, 0, 788 },
	{ 106584, 5, 342, 342, 13, 58, 1, kSequencePointKind_Normal, 0, 789 },
	{ 106584, 5, 342, 342, 13, 58, 3, kSequencePointKind_StepOut, 0, 790 },
	{ 106584, 5, 342, 342, 13, 58, 8, kSequencePointKind_StepOut, 0, 791 },
	{ 106584, 5, 343, 343, 9, 10, 16, kSequencePointKind_Normal, 0, 792 },
	{ 106585, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 793 },
	{ 106585, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 794 },
	{ 106585, 5, 346, 346, 9, 10, 0, kSequencePointKind_Normal, 0, 795 },
	{ 106585, 5, 347, 347, 13, 58, 1, kSequencePointKind_Normal, 0, 796 },
	{ 106585, 5, 347, 347, 13, 58, 3, kSequencePointKind_StepOut, 0, 797 },
	{ 106585, 5, 347, 347, 13, 58, 8, kSequencePointKind_StepOut, 0, 798 },
	{ 106585, 5, 348, 348, 9, 10, 16, kSequencePointKind_Normal, 0, 799 },
	{ 106586, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 800 },
	{ 106586, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 801 },
	{ 106586, 5, 351, 351, 9, 10, 0, kSequencePointKind_Normal, 0, 802 },
	{ 106586, 5, 352, 352, 13, 58, 1, kSequencePointKind_Normal, 0, 803 },
	{ 106586, 5, 352, 352, 13, 58, 3, kSequencePointKind_StepOut, 0, 804 },
	{ 106586, 5, 352, 352, 13, 58, 8, kSequencePointKind_StepOut, 0, 805 },
	{ 106586, 5, 353, 353, 9, 10, 16, kSequencePointKind_Normal, 0, 806 },
	{ 106587, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 807 },
	{ 106587, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 808 },
	{ 106587, 5, 356, 356, 9, 10, 0, kSequencePointKind_Normal, 0, 809 },
	{ 106587, 5, 357, 357, 13, 60, 1, kSequencePointKind_Normal, 0, 810 },
	{ 106587, 5, 357, 357, 13, 60, 3, kSequencePointKind_StepOut, 0, 811 },
	{ 106587, 5, 357, 357, 13, 60, 8, kSequencePointKind_StepOut, 0, 812 },
	{ 106587, 5, 358, 358, 9, 10, 16, kSequencePointKind_Normal, 0, 813 },
	{ 106588, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 814 },
	{ 106588, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 815 },
	{ 106588, 5, 361, 361, 9, 10, 0, kSequencePointKind_Normal, 0, 816 },
	{ 106588, 5, 362, 362, 13, 58, 1, kSequencePointKind_Normal, 0, 817 },
	{ 106588, 5, 362, 362, 13, 58, 3, kSequencePointKind_StepOut, 0, 818 },
	{ 106588, 5, 362, 362, 13, 58, 8, kSequencePointKind_StepOut, 0, 819 },
	{ 106588, 5, 363, 363, 9, 10, 16, kSequencePointKind_Normal, 0, 820 },
	{ 106589, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 821 },
	{ 106589, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 822 },
	{ 106589, 5, 366, 366, 9, 10, 0, kSequencePointKind_Normal, 0, 823 },
	{ 106589, 5, 367, 367, 13, 67, 1, kSequencePointKind_Normal, 0, 824 },
	{ 106589, 5, 367, 367, 13, 67, 3, kSequencePointKind_StepOut, 0, 825 },
	{ 106589, 5, 367, 367, 13, 67, 8, kSequencePointKind_StepOut, 0, 826 },
	{ 106589, 5, 368, 368, 9, 10, 16, kSequencePointKind_Normal, 0, 827 },
	{ 106590, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 828 },
	{ 106590, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 829 },
	{ 106590, 5, 371, 371, 9, 10, 0, kSequencePointKind_Normal, 0, 830 },
	{ 106590, 5, 372, 372, 13, 65, 1, kSequencePointKind_Normal, 0, 831 },
	{ 106590, 5, 372, 372, 13, 65, 3, kSequencePointKind_StepOut, 0, 832 },
	{ 106590, 5, 372, 372, 13, 65, 8, kSequencePointKind_StepOut, 0, 833 },
	{ 106590, 5, 373, 373, 9, 10, 16, kSequencePointKind_Normal, 0, 834 },
	{ 106591, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 835 },
	{ 106591, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 836 },
	{ 106591, 5, 376, 376, 9, 10, 0, kSequencePointKind_Normal, 0, 837 },
	{ 106591, 5, 377, 377, 13, 59, 1, kSequencePointKind_Normal, 0, 838 },
	{ 106591, 5, 377, 377, 13, 59, 3, kSequencePointKind_StepOut, 0, 839 },
	{ 106591, 5, 377, 377, 13, 59, 8, kSequencePointKind_StepOut, 0, 840 },
	{ 106591, 5, 378, 378, 9, 10, 16, kSequencePointKind_Normal, 0, 841 },
	{ 106592, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 842 },
	{ 106592, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 843 },
	{ 106592, 5, 381, 381, 9, 10, 0, kSequencePointKind_Normal, 0, 844 },
	{ 106592, 5, 382, 382, 13, 55, 1, kSequencePointKind_Normal, 0, 845 },
	{ 106592, 5, 382, 382, 13, 55, 3, kSequencePointKind_StepOut, 0, 846 },
	{ 106592, 5, 382, 382, 13, 55, 8, kSequencePointKind_StepOut, 0, 847 },
	{ 106592, 5, 383, 383, 9, 10, 16, kSequencePointKind_Normal, 0, 848 },
	{ 106593, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 849 },
	{ 106593, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 850 },
	{ 106593, 5, 386, 386, 9, 10, 0, kSequencePointKind_Normal, 0, 851 },
	{ 106593, 5, 387, 387, 13, 70, 1, kSequencePointKind_Normal, 0, 852 },
	{ 106593, 5, 387, 387, 13, 70, 3, kSequencePointKind_StepOut, 0, 853 },
	{ 106593, 5, 387, 387, 13, 70, 8, kSequencePointKind_StepOut, 0, 854 },
	{ 106593, 5, 388, 388, 9, 10, 16, kSequencePointKind_Normal, 0, 855 },
	{ 106594, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 856 },
	{ 106594, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 857 },
	{ 106594, 5, 391, 391, 9, 10, 0, kSequencePointKind_Normal, 0, 858 },
	{ 106594, 5, 392, 392, 13, 65, 1, kSequencePointKind_Normal, 0, 859 },
	{ 106594, 5, 392, 392, 13, 65, 3, kSequencePointKind_StepOut, 0, 860 },
	{ 106594, 5, 392, 392, 13, 65, 8, kSequencePointKind_StepOut, 0, 861 },
	{ 106594, 5, 393, 393, 9, 10, 16, kSequencePointKind_Normal, 0, 862 },
	{ 106595, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 863 },
	{ 106595, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 864 },
	{ 106595, 5, 396, 396, 9, 10, 0, kSequencePointKind_Normal, 0, 865 },
	{ 106595, 5, 397, 397, 13, 55, 1, kSequencePointKind_Normal, 0, 866 },
	{ 106595, 5, 397, 397, 13, 55, 3, kSequencePointKind_StepOut, 0, 867 },
	{ 106595, 5, 397, 397, 13, 55, 8, kSequencePointKind_StepOut, 0, 868 },
	{ 106595, 5, 398, 398, 9, 10, 16, kSequencePointKind_Normal, 0, 869 },
	{ 106596, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 870 },
	{ 106596, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 871 },
	{ 106596, 5, 402, 402, 9, 10, 0, kSequencePointKind_Normal, 0, 872 },
	{ 106596, 5, 403, 403, 13, 50, 1, kSequencePointKind_Normal, 0, 873 },
	{ 106596, 5, 403, 403, 13, 50, 3, kSequencePointKind_StepOut, 0, 874 },
	{ 106596, 5, 403, 403, 13, 50, 9, kSequencePointKind_StepOut, 0, 875 },
	{ 106596, 5, 404, 404, 9, 10, 15, kSequencePointKind_Normal, 0, 876 },
	{ 106597, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 877 },
	{ 106597, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 878 },
	{ 106597, 5, 407, 407, 9, 10, 0, kSequencePointKind_Normal, 0, 879 },
	{ 106597, 5, 408, 408, 13, 51, 1, kSequencePointKind_Normal, 0, 880 },
	{ 106597, 5, 408, 408, 13, 51, 3, kSequencePointKind_StepOut, 0, 881 },
	{ 106597, 5, 408, 408, 13, 51, 9, kSequencePointKind_StepOut, 0, 882 },
	{ 106597, 5, 409, 409, 9, 10, 15, kSequencePointKind_Normal, 0, 883 },
	{ 106598, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 884 },
	{ 106598, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 885 },
	{ 106598, 5, 412, 412, 9, 10, 0, kSequencePointKind_Normal, 0, 886 },
	{ 106598, 5, 413, 413, 13, 52, 1, kSequencePointKind_Normal, 0, 887 },
	{ 106598, 5, 413, 413, 13, 52, 3, kSequencePointKind_StepOut, 0, 888 },
	{ 106598, 5, 413, 413, 13, 52, 9, kSequencePointKind_StepOut, 0, 889 },
	{ 106598, 5, 414, 414, 9, 10, 15, kSequencePointKind_Normal, 0, 890 },
	{ 106599, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 891 },
	{ 106599, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 892 },
	{ 106599, 5, 417, 417, 9, 10, 0, kSequencePointKind_Normal, 0, 893 },
	{ 106599, 5, 418, 418, 13, 54, 1, kSequencePointKind_Normal, 0, 894 },
	{ 106599, 5, 418, 418, 13, 54, 3, kSequencePointKind_StepOut, 0, 895 },
	{ 106599, 5, 418, 418, 13, 54, 9, kSequencePointKind_StepOut, 0, 896 },
	{ 106599, 5, 419, 419, 9, 10, 15, kSequencePointKind_Normal, 0, 897 },
	{ 106600, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 898 },
	{ 106600, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 899 },
	{ 106600, 5, 422, 422, 9, 10, 0, kSequencePointKind_Normal, 0, 900 },
	{ 106600, 5, 423, 423, 13, 54, 1, kSequencePointKind_Normal, 0, 901 },
	{ 106600, 5, 423, 423, 13, 54, 3, kSequencePointKind_StepOut, 0, 902 },
	{ 106600, 5, 423, 423, 13, 54, 9, kSequencePointKind_StepOut, 0, 903 },
	{ 106600, 5, 424, 424, 9, 10, 15, kSequencePointKind_Normal, 0, 904 },
	{ 106601, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 905 },
	{ 106601, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 906 },
	{ 106601, 5, 427, 427, 9, 10, 0, kSequencePointKind_Normal, 0, 907 },
	{ 106601, 5, 428, 428, 13, 54, 1, kSequencePointKind_Normal, 0, 908 },
	{ 106601, 5, 428, 428, 13, 54, 3, kSequencePointKind_StepOut, 0, 909 },
	{ 106601, 5, 428, 428, 13, 54, 9, kSequencePointKind_StepOut, 0, 910 },
	{ 106601, 5, 429, 429, 9, 10, 15, kSequencePointKind_Normal, 0, 911 },
	{ 106602, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 912 },
	{ 106602, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 913 },
	{ 106602, 5, 432, 432, 9, 10, 0, kSequencePointKind_Normal, 0, 914 },
	{ 106602, 5, 433, 433, 13, 56, 1, kSequencePointKind_Normal, 0, 915 },
	{ 106602, 5, 433, 433, 13, 56, 3, kSequencePointKind_StepOut, 0, 916 },
	{ 106602, 5, 433, 433, 13, 56, 9, kSequencePointKind_StepOut, 0, 917 },
	{ 106602, 5, 434, 434, 9, 10, 15, kSequencePointKind_Normal, 0, 918 },
	{ 106603, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 919 },
	{ 106603, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 920 },
	{ 106603, 5, 437, 437, 9, 10, 0, kSequencePointKind_Normal, 0, 921 },
	{ 106603, 5, 438, 438, 13, 54, 1, kSequencePointKind_Normal, 0, 922 },
	{ 106603, 5, 438, 438, 13, 54, 3, kSequencePointKind_StepOut, 0, 923 },
	{ 106603, 5, 438, 438, 13, 54, 9, kSequencePointKind_StepOut, 0, 924 },
	{ 106603, 5, 439, 439, 9, 10, 15, kSequencePointKind_Normal, 0, 925 },
	{ 106604, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 926 },
	{ 106604, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 927 },
	{ 106604, 5, 442, 442, 9, 10, 0, kSequencePointKind_Normal, 0, 928 },
	{ 106604, 5, 443, 443, 13, 61, 1, kSequencePointKind_Normal, 0, 929 },
	{ 106604, 5, 443, 443, 13, 61, 3, kSequencePointKind_StepOut, 0, 930 },
	{ 106604, 5, 443, 443, 13, 61, 9, kSequencePointKind_StepOut, 0, 931 },
	{ 106604, 5, 444, 444, 9, 10, 15, kSequencePointKind_Normal, 0, 932 },
	{ 106605, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 933 },
	{ 106605, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 934 },
	{ 106605, 5, 447, 447, 9, 10, 0, kSequencePointKind_Normal, 0, 935 },
	{ 106605, 5, 448, 448, 13, 55, 1, kSequencePointKind_Normal, 0, 936 },
	{ 106605, 5, 448, 448, 13, 55, 3, kSequencePointKind_StepOut, 0, 937 },
	{ 106605, 5, 448, 448, 13, 55, 9, kSequencePointKind_StepOut, 0, 938 },
	{ 106605, 5, 449, 449, 9, 10, 15, kSequencePointKind_Normal, 0, 939 },
	{ 106606, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 940 },
	{ 106606, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 941 },
	{ 106606, 5, 452, 452, 9, 10, 0, kSequencePointKind_Normal, 0, 942 },
	{ 106606, 5, 453, 453, 13, 51, 1, kSequencePointKind_Normal, 0, 943 },
	{ 106606, 5, 453, 453, 13, 51, 3, kSequencePointKind_StepOut, 0, 944 },
	{ 106606, 5, 453, 453, 13, 51, 9, kSequencePointKind_StepOut, 0, 945 },
	{ 106606, 5, 454, 454, 9, 10, 15, kSequencePointKind_Normal, 0, 946 },
	{ 106607, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 947 },
	{ 106607, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 948 },
	{ 106607, 5, 457, 457, 9, 10, 0, kSequencePointKind_Normal, 0, 949 },
	{ 106607, 5, 458, 458, 13, 66, 1, kSequencePointKind_Normal, 0, 950 },
	{ 106607, 5, 458, 458, 13, 66, 3, kSequencePointKind_StepOut, 0, 951 },
	{ 106607, 5, 458, 458, 13, 66, 9, kSequencePointKind_StepOut, 0, 952 },
	{ 106607, 5, 459, 459, 9, 10, 15, kSequencePointKind_Normal, 0, 953 },
	{ 106608, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 954 },
	{ 106608, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 955 },
	{ 106608, 5, 462, 462, 9, 10, 0, kSequencePointKind_Normal, 0, 956 },
	{ 106608, 5, 463, 463, 13, 61, 1, kSequencePointKind_Normal, 0, 957 },
	{ 106608, 5, 463, 463, 13, 61, 3, kSequencePointKind_StepOut, 0, 958 },
	{ 106608, 5, 463, 463, 13, 61, 9, kSequencePointKind_StepOut, 0, 959 },
	{ 106608, 5, 464, 464, 9, 10, 15, kSequencePointKind_Normal, 0, 960 },
	{ 106609, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 961 },
	{ 106609, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 962 },
	{ 106609, 5, 467, 467, 9, 10, 0, kSequencePointKind_Normal, 0, 963 },
	{ 106609, 5, 468, 468, 13, 51, 1, kSequencePointKind_Normal, 0, 964 },
	{ 106609, 5, 468, 468, 13, 51, 3, kSequencePointKind_StepOut, 0, 965 },
	{ 106609, 5, 468, 468, 13, 51, 9, kSequencePointKind_StepOut, 0, 966 },
	{ 106609, 5, 469, 469, 9, 10, 15, kSequencePointKind_Normal, 0, 967 },
	{ 106610, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 968 },
	{ 106610, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 969 },
	{ 106610, 5, 473, 473, 9, 10, 0, kSequencePointKind_Normal, 0, 970 },
	{ 106610, 5, 474, 474, 13, 54, 1, kSequencePointKind_Normal, 0, 971 },
	{ 106610, 5, 474, 474, 13, 54, 3, kSequencePointKind_StepOut, 0, 972 },
	{ 106610, 5, 474, 474, 13, 54, 8, kSequencePointKind_StepOut, 0, 973 },
	{ 106610, 5, 475, 475, 9, 10, 16, kSequencePointKind_Normal, 0, 974 },
	{ 106611, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 975 },
	{ 106611, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 976 },
	{ 106611, 5, 478, 478, 9, 10, 0, kSequencePointKind_Normal, 0, 977 },
	{ 106611, 5, 479, 479, 13, 55, 1, kSequencePointKind_Normal, 0, 978 },
	{ 106611, 5, 479, 479, 13, 55, 3, kSequencePointKind_StepOut, 0, 979 },
	{ 106611, 5, 479, 479, 13, 55, 8, kSequencePointKind_StepOut, 0, 980 },
	{ 106611, 5, 480, 480, 9, 10, 16, kSequencePointKind_Normal, 0, 981 },
	{ 106612, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 982 },
	{ 106612, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 983 },
	{ 106612, 5, 483, 483, 9, 10, 0, kSequencePointKind_Normal, 0, 984 },
	{ 106612, 5, 484, 484, 13, 56, 1, kSequencePointKind_Normal, 0, 985 },
	{ 106612, 5, 484, 484, 13, 56, 3, kSequencePointKind_StepOut, 0, 986 },
	{ 106612, 5, 484, 484, 13, 56, 8, kSequencePointKind_StepOut, 0, 987 },
	{ 106612, 5, 485, 485, 9, 10, 16, kSequencePointKind_Normal, 0, 988 },
	{ 106613, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 989 },
	{ 106613, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 990 },
	{ 106613, 5, 488, 488, 9, 10, 0, kSequencePointKind_Normal, 0, 991 },
	{ 106613, 5, 489, 489, 13, 58, 1, kSequencePointKind_Normal, 0, 992 },
	{ 106613, 5, 489, 489, 13, 58, 3, kSequencePointKind_StepOut, 0, 993 },
	{ 106613, 5, 489, 489, 13, 58, 8, kSequencePointKind_StepOut, 0, 994 },
	{ 106613, 5, 490, 490, 9, 10, 16, kSequencePointKind_Normal, 0, 995 },
	{ 106614, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 996 },
	{ 106614, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 997 },
	{ 106614, 5, 493, 493, 9, 10, 0, kSequencePointKind_Normal, 0, 998 },
	{ 106614, 5, 494, 494, 13, 58, 1, kSequencePointKind_Normal, 0, 999 },
	{ 106614, 5, 494, 494, 13, 58, 3, kSequencePointKind_StepOut, 0, 1000 },
	{ 106614, 5, 494, 494, 13, 58, 8, kSequencePointKind_StepOut, 0, 1001 },
	{ 106614, 5, 495, 495, 9, 10, 16, kSequencePointKind_Normal, 0, 1002 },
	{ 106615, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1003 },
	{ 106615, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1004 },
	{ 106615, 5, 498, 498, 9, 10, 0, kSequencePointKind_Normal, 0, 1005 },
	{ 106615, 5, 499, 499, 13, 58, 1, kSequencePointKind_Normal, 0, 1006 },
	{ 106615, 5, 499, 499, 13, 58, 3, kSequencePointKind_StepOut, 0, 1007 },
	{ 106615, 5, 499, 499, 13, 58, 8, kSequencePointKind_StepOut, 0, 1008 },
	{ 106615, 5, 500, 500, 9, 10, 16, kSequencePointKind_Normal, 0, 1009 },
	{ 106616, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1010 },
	{ 106616, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1011 },
	{ 106616, 5, 503, 503, 9, 10, 0, kSequencePointKind_Normal, 0, 1012 },
	{ 106616, 5, 504, 504, 13, 60, 1, kSequencePointKind_Normal, 0, 1013 },
	{ 106616, 5, 504, 504, 13, 60, 3, kSequencePointKind_StepOut, 0, 1014 },
	{ 106616, 5, 504, 504, 13, 60, 8, kSequencePointKind_StepOut, 0, 1015 },
	{ 106616, 5, 505, 505, 9, 10, 16, kSequencePointKind_Normal, 0, 1016 },
	{ 106617, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1017 },
	{ 106617, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1018 },
	{ 106617, 5, 508, 508, 9, 10, 0, kSequencePointKind_Normal, 0, 1019 },
	{ 106617, 5, 509, 509, 13, 58, 1, kSequencePointKind_Normal, 0, 1020 },
	{ 106617, 5, 509, 509, 13, 58, 3, kSequencePointKind_StepOut, 0, 1021 },
	{ 106617, 5, 509, 509, 13, 58, 8, kSequencePointKind_StepOut, 0, 1022 },
	{ 106617, 5, 510, 510, 9, 10, 16, kSequencePointKind_Normal, 0, 1023 },
	{ 106618, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1024 },
	{ 106618, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1025 },
	{ 106618, 5, 513, 513, 9, 10, 0, kSequencePointKind_Normal, 0, 1026 },
	{ 106618, 5, 514, 514, 13, 55, 1, kSequencePointKind_Normal, 0, 1027 },
	{ 106618, 5, 514, 514, 13, 55, 3, kSequencePointKind_StepOut, 0, 1028 },
	{ 106618, 5, 514, 514, 13, 55, 8, kSequencePointKind_StepOut, 0, 1029 },
	{ 106618, 5, 515, 515, 9, 10, 16, kSequencePointKind_Normal, 0, 1030 },
	{ 106619, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1031 },
	{ 106619, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1032 },
	{ 106619, 5, 518, 518, 9, 10, 0, kSequencePointKind_Normal, 0, 1033 },
	{ 106619, 5, 519, 519, 13, 70, 1, kSequencePointKind_Normal, 0, 1034 },
	{ 106619, 5, 519, 519, 13, 70, 3, kSequencePointKind_StepOut, 0, 1035 },
	{ 106619, 5, 519, 519, 13, 70, 8, kSequencePointKind_StepOut, 0, 1036 },
	{ 106619, 5, 520, 520, 9, 10, 16, kSequencePointKind_Normal, 0, 1037 },
	{ 106620, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1038 },
	{ 106620, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1039 },
	{ 106620, 5, 523, 523, 9, 10, 0, kSequencePointKind_Normal, 0, 1040 },
	{ 106620, 5, 524, 524, 13, 65, 1, kSequencePointKind_Normal, 0, 1041 },
	{ 106620, 5, 524, 524, 13, 65, 3, kSequencePointKind_StepOut, 0, 1042 },
	{ 106620, 5, 524, 524, 13, 65, 8, kSequencePointKind_StepOut, 0, 1043 },
	{ 106620, 5, 525, 525, 9, 10, 16, kSequencePointKind_Normal, 0, 1044 },
	{ 106621, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1045 },
	{ 106621, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1046 },
	{ 106621, 5, 528, 528, 9, 10, 0, kSequencePointKind_Normal, 0, 1047 },
	{ 106621, 5, 529, 529, 13, 55, 1, kSequencePointKind_Normal, 0, 1048 },
	{ 106621, 5, 529, 529, 13, 55, 3, kSequencePointKind_StepOut, 0, 1049 },
	{ 106621, 5, 529, 529, 13, 55, 8, kSequencePointKind_StepOut, 0, 1050 },
	{ 106621, 5, 530, 530, 9, 10, 16, kSequencePointKind_Normal, 0, 1051 },
	{ 106622, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1052 },
	{ 106622, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1053 },
	{ 106622, 5, 533, 533, 9, 10, 0, kSequencePointKind_Normal, 0, 1054 },
	{ 106622, 5, 534, 534, 13, 65, 1, kSequencePointKind_Normal, 0, 1055 },
	{ 106622, 5, 534, 534, 13, 65, 3, kSequencePointKind_StepOut, 0, 1056 },
	{ 106622, 5, 534, 534, 13, 65, 8, kSequencePointKind_StepOut, 0, 1057 },
	{ 106622, 5, 535, 535, 9, 10, 16, kSequencePointKind_Normal, 0, 1058 },
	{ 106623, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1059 },
	{ 106623, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1060 },
	{ 106623, 5, 538, 538, 9, 10, 0, kSequencePointKind_Normal, 0, 1061 },
	{ 106623, 5, 539, 539, 13, 59, 1, kSequencePointKind_Normal, 0, 1062 },
	{ 106623, 5, 539, 539, 13, 59, 3, kSequencePointKind_StepOut, 0, 1063 },
	{ 106623, 5, 539, 539, 13, 59, 8, kSequencePointKind_StepOut, 0, 1064 },
	{ 106623, 5, 540, 540, 9, 10, 16, kSequencePointKind_Normal, 0, 1065 },
	{ 106624, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1066 },
	{ 106624, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1067 },
	{ 106624, 5, 543, 543, 9, 10, 0, kSequencePointKind_Normal, 0, 1068 },
	{ 106624, 5, 544, 544, 13, 57, 1, kSequencePointKind_Normal, 0, 1069 },
	{ 106624, 5, 544, 544, 13, 57, 3, kSequencePointKind_StepOut, 0, 1070 },
	{ 106624, 5, 544, 544, 13, 57, 8, kSequencePointKind_StepOut, 0, 1071 },
	{ 106624, 5, 545, 545, 9, 10, 16, kSequencePointKind_Normal, 0, 1072 },
	{ 106625, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1073 },
	{ 106625, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1074 },
	{ 106625, 5, 548, 548, 9, 10, 0, kSequencePointKind_Normal, 0, 1075 },
	{ 106625, 5, 549, 549, 13, 69, 1, kSequencePointKind_Normal, 0, 1076 },
	{ 106625, 5, 549, 549, 13, 69, 3, kSequencePointKind_StepOut, 0, 1077 },
	{ 106625, 5, 549, 549, 13, 69, 8, kSequencePointKind_StepOut, 0, 1078 },
	{ 106625, 5, 550, 550, 9, 10, 16, kSequencePointKind_Normal, 0, 1079 },
	{ 106626, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1080 },
	{ 106626, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1081 },
	{ 106626, 5, 553, 553, 9, 10, 0, kSequencePointKind_Normal, 0, 1082 },
	{ 106626, 5, 554, 554, 13, 66, 1, kSequencePointKind_Normal, 0, 1083 },
	{ 106626, 5, 554, 554, 13, 66, 3, kSequencePointKind_StepOut, 0, 1084 },
	{ 106626, 5, 554, 554, 13, 66, 8, kSequencePointKind_StepOut, 0, 1085 },
	{ 106626, 5, 555, 555, 9, 10, 16, kSequencePointKind_Normal, 0, 1086 },
	{ 106627, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1087 },
	{ 106627, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1088 },
	{ 106627, 5, 558, 558, 9, 10, 0, kSequencePointKind_Normal, 0, 1089 },
	{ 106627, 5, 559, 559, 13, 65, 1, kSequencePointKind_Normal, 0, 1090 },
	{ 106627, 5, 559, 559, 13, 65, 3, kSequencePointKind_StepOut, 0, 1091 },
	{ 106627, 5, 559, 559, 13, 65, 8, kSequencePointKind_StepOut, 0, 1092 },
	{ 106627, 5, 560, 560, 9, 10, 16, kSequencePointKind_Normal, 0, 1093 },
	{ 106628, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1094 },
	{ 106628, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1095 },
	{ 106628, 5, 563, 563, 9, 10, 0, kSequencePointKind_Normal, 0, 1096 },
	{ 106628, 5, 564, 564, 13, 71, 1, kSequencePointKind_Normal, 0, 1097 },
	{ 106628, 5, 564, 564, 13, 71, 3, kSequencePointKind_StepOut, 0, 1098 },
	{ 106628, 5, 564, 564, 13, 71, 8, kSequencePointKind_StepOut, 0, 1099 },
	{ 106628, 5, 565, 565, 9, 10, 16, kSequencePointKind_Normal, 0, 1100 },
	{ 106632, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1101 },
	{ 106632, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1102 },
	{ 106632, 5, 579, 579, 9, 10, 0, kSequencePointKind_Normal, 0, 1103 },
	{ 106632, 5, 581, 581, 13, 52, 1, kSequencePointKind_Normal, 0, 1104 },
	{ 106632, 5, 581, 581, 0, 0, 11, kSequencePointKind_Normal, 0, 1105 },
	{ 106632, 5, 582, 582, 17, 29, 14, kSequencePointKind_Normal, 0, 1106 },
	{ 106632, 5, 584, 584, 13, 55, 18, kSequencePointKind_Normal, 0, 1107 },
	{ 106632, 5, 584, 584, 0, 0, 28, kSequencePointKind_Normal, 0, 1108 },
	{ 106632, 5, 585, 585, 17, 82, 31, kSequencePointKind_Normal, 0, 1109 },
	{ 106632, 5, 585, 585, 17, 82, 33, kSequencePointKind_StepOut, 0, 1110 },
	{ 106632, 5, 586, 586, 13, 50, 43, kSequencePointKind_Normal, 0, 1111 },
	{ 106632, 5, 587, 587, 9, 10, 52, kSequencePointKind_Normal, 0, 1112 },
	{ 106633, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1113 },
	{ 106633, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1114 },
	{ 106633, 5, 592, 592, 9, 10, 0, kSequencePointKind_Normal, 0, 1115 },
	{ 106633, 5, 593, 593, 13, 90, 1, kSequencePointKind_Normal, 0, 1116 },
	{ 106633, 5, 593, 593, 13, 90, 10, kSequencePointKind_StepOut, 0, 1117 },
	{ 106633, 5, 594, 594, 13, 52, 15, kSequencePointKind_Normal, 0, 1118 },
	{ 106633, 5, 594, 594, 13, 52, 22, kSequencePointKind_StepOut, 0, 1119 },
	{ 106633, 5, 595, 595, 9, 10, 28, kSequencePointKind_Normal, 0, 1120 },
	{ 106647, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 1121 },
	{ 106647, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1122 },
	{ 106647, 5, 615, 615, 9, 10, 0, kSequencePointKind_Normal, 0, 1123 },
	{ 106647, 5, 616, 616, 13, 42, 1, kSequencePointKind_Normal, 0, 1124 },
	{ 106647, 5, 617, 617, 13, 38, 8, kSequencePointKind_Normal, 0, 1125 },
	{ 106647, 5, 618, 618, 13, 38, 15, kSequencePointKind_Normal, 0, 1126 },
	{ 106647, 5, 619, 619, 13, 34, 22, kSequencePointKind_Normal, 0, 1127 },
	{ 106647, 5, 620, 620, 9, 10, 30, kSequencePointKind_Normal, 0, 1128 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_VFXModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_VFXModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/VFX/Public/ScriptBindings/VFXEventAttribute.bindings.cs", { 85, 240, 140, 59, 226, 24, 230, 200, 194, 228, 102, 200, 59, 62, 252, 109} },
{ "/Users/<USER>/build/output/unity/unity/Modules/VFX/Public/ScriptBindings/VFXExpressionValues.bindings.cs", { 137, 94, 30, 16, 81, 236, 48, 215, 52, 194, 54, 215, 195, 215, 95, 88} },
{ "/Users/<USER>/build/output/unity/unity/Modules/VFX/Public/ScriptBindings/VFXManager.bindings.cs", { 251, 197, 154, 228, 203, 249, 220, 65, 196, 74, 233, 123, 34, 80, 39, 231} },
{ "/Users/<USER>/build/output/unity/unity/Modules/VFX/Public/ScriptBindings/VFXSpawnerState.bindings.cs", { 214, 51, 101, 240, 207, 151, 49, 7, 90, 22, 249, 133, 43, 200, 138, 117} },
{ "/Users/<USER>/build/output/unity/unity/Modules/VFX/Public/ScriptBindings/VisualEffect.bindings.cs", { 80, 158, 92, 34, 193, 63, 151, 135, 167, 10, 100, 134, 40, 123, 149, 219} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[8] = 
{
	{ 13647, 1 },
	{ 13648, 2 },
	{ 13652, 3 },
	{ 13655, 4 },
	{ 13658, 5 },
	{ 13659, 5 },
	{ 13660, 5 },
	{ 13662, 5 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[93] = 
{
	{ 0, 20 },
	{ 0, 30 },
	{ 0, 59 },
	{ 0, 28 },
	{ 0, 12 },
	{ 0, 65 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 20 },
	{ 0, 22 },
	{ 0, 22 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 39 },
	{ 0, 26 },
	{ 0, 58 },
	{ 0, 67 },
	{ 0, 12 },
	{ 0, 65 },
	{ 0, 15 },
	{ 0, 45 },
	{ 0, 18 },
	{ 0, 39 },
	{ 0, 41 },
	{ 0, 22 },
	{ 0, 22 },
	{ 0, 63 },
	{ 0, 22 },
	{ 0, 32 },
	{ 0, 55 },
	{ 0, 55 },
	{ 0, 55 },
	{ 0, 55 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 54 },
	{ 0, 29 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[342] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 20, 0, 1 },
	{ 30, 1, 1 },
	{ 59, 2, 1 },
	{ 0, 0, 0 },
	{ 28, 3, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 12, 4, 1 },
	{ 65, 5, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 18, 6, 1 },
	{ 18, 7, 1 },
	{ 18, 8, 1 },
	{ 18, 9, 1 },
	{ 18, 10, 1 },
	{ 18, 11, 1 },
	{ 18, 12, 1 },
	{ 18, 13, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 18, 14, 1 },
	{ 18, 15, 1 },
	{ 18, 16, 1 },
	{ 18, 17, 1 },
	{ 18, 18, 1 },
	{ 18, 19, 1 },
	{ 18, 20, 1 },
	{ 18, 21, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 20, 22, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 22, 23, 1 },
	{ 0, 0, 0 },
	{ 22, 24, 1 },
	{ 0, 0, 0 },
	{ 18, 25, 1 },
	{ 18, 26, 1 },
	{ 18, 27, 1 },
	{ 18, 28, 1 },
	{ 18, 29, 1 },
	{ 18, 30, 1 },
	{ 18, 31, 1 },
	{ 18, 32, 1 },
	{ 18, 33, 1 },
	{ 18, 34, 1 },
	{ 18, 35, 1 },
	{ 18, 36, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 39, 37, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 26, 38, 1 },
	{ 58, 39, 1 },
	{ 67, 40, 1 },
	{ 12, 41, 1 },
	{ 65, 42, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 15, 43, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 45, 44, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 18, 45, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 39, 46, 1 },
	{ 41, 47, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 22, 48, 1 },
	{ 0, 0, 0 },
	{ 22, 49, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 63, 50, 1 },
	{ 22, 51, 1 },
	{ 32, 52, 1 },
	{ 55, 53, 1 },
	{ 55, 54, 1 },
	{ 55, 55, 1 },
	{ 55, 56, 1 },
	{ 0, 0, 0 },
	{ 18, 57, 1 },
	{ 18, 58, 1 },
	{ 18, 59, 1 },
	{ 18, 60, 1 },
	{ 18, 61, 1 },
	{ 18, 62, 1 },
	{ 18, 63, 1 },
	{ 18, 64, 1 },
	{ 18, 65, 1 },
	{ 18, 66, 1 },
	{ 18, 67, 1 },
	{ 18, 68, 1 },
	{ 18, 69, 1 },
	{ 18, 70, 1 },
	{ 18, 71, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 18, 72, 1 },
	{ 18, 73, 1 },
	{ 18, 74, 1 },
	{ 18, 75, 1 },
	{ 18, 76, 1 },
	{ 18, 77, 1 },
	{ 18, 78, 1 },
	{ 18, 79, 1 },
	{ 18, 80, 1 },
	{ 18, 81, 1 },
	{ 18, 82, 1 },
	{ 18, 83, 1 },
	{ 18, 84, 1 },
	{ 18, 85, 1 },
	{ 18, 86, 1 },
	{ 18, 87, 1 },
	{ 18, 88, 1 },
	{ 18, 89, 1 },
	{ 18, 90, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 54, 91, 1 },
	{ 29, 92, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_VFXModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_VFXModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	1129,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_VFXModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	8,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
