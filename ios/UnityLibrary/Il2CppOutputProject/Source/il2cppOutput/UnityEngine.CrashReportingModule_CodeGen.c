﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void CrashReportHandler__ctor_m66D6B1BA3D0454273476C0B7320BB50EB5345BF0 (void);
extern void CrashReportHandler_get_enableCaptureExceptions_mCDE26F4C646D163DCB61E4A4EA2137F2C3BBF461 (void);
extern void CrashReportHandler_set_enableCaptureExceptions_m43B6DDC35A11833111069353501CDA7EE2D75351 (void);
extern void CrashReportHandler_get_logBufferSize_m6D3487D46A3B7745F128AA25D19EE829DDDB70F2 (void);
extern void CrashReportHandler_set_logBufferSize_mD085CF929DFFDB39F8D9A8CC25E027462FB907B1 (void);
extern void CrashReportHandler_get_installationIdentifier_m83A71077FDCF0D83D7BDF61EC976378C6457BD43 (void);
extern void CrashReportHandler_set_installationIdentifier_m226426172B933FAAAC3FA11183FCCCA1460D2C04 (void);
extern void CrashReportHandler_GetUserMetadata_m9A07B42BE5F86009D5C7AF918CC600F3BF4BF8D0 (void);
extern void CrashReportHandler_SetUserMetadata_mCBED74642C36CA95B6B0C52FAE02EBD0ACBC51FE (void);
static Il2CppMethodPointer s_methodPointers[9] = 
{
	CrashReportHandler__ctor_m66D6B1BA3D0454273476C0B7320BB50EB5345BF0,
	CrashReportHandler_get_enableCaptureExceptions_mCDE26F4C646D163DCB61E4A4EA2137F2C3BBF461,
	CrashReportHandler_set_enableCaptureExceptions_m43B6DDC35A11833111069353501CDA7EE2D75351,
	CrashReportHandler_get_logBufferSize_m6D3487D46A3B7745F128AA25D19EE829DDDB70F2,
	CrashReportHandler_set_logBufferSize_mD085CF929DFFDB39F8D9A8CC25E027462FB907B1,
	CrashReportHandler_get_installationIdentifier_m83A71077FDCF0D83D7BDF61EC976378C6457BD43,
	CrashReportHandler_set_installationIdentifier_m226426172B933FAAAC3FA11183FCCCA1460D2C04,
	CrashReportHandler_GetUserMetadata_m9A07B42BE5F86009D5C7AF918CC600F3BF4BF8D0,
	CrashReportHandler_SetUserMetadata_mCBED74642C36CA95B6B0C52FAE02EBD0ACBC51FE,
};
static const int32_t s_InvokerIndices[9] = 
{
	4364,
	8993,
	8868,
	9079,
	8903,
	9031,
	8887,
	8505,
	7975,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_CrashReportingModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_CrashReportingModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_CrashReportingModule_CodeGenModule = 
{
	"UnityEngine.CrashReportingModule.dll",
	9,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_CrashReportingModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
