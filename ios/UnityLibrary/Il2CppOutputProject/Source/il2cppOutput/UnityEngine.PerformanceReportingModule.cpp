﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct String_t;



IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tBF7725E61EB349A32AABF6F44196FCA419B3CE8B 
{
};
struct PerformanceReporting_t53F95BBFED597D8E54E6FA33DF6897DD4019AB9B  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PerformanceReporting_get_enabled_m13A834E7124C3E52E95FD3DBDA5BE5C9906D436C (const RuntimeMethod* method) 
{
	typedef bool (*PerformanceReporting_get_enabled_m13A834E7124C3E52E95FD3DBDA5BE5C9906D436C_ftn) ();
	static PerformanceReporting_get_enabled_m13A834E7124C3E52E95FD3DBDA5BE5C9906D436C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PerformanceReporting_get_enabled_m13A834E7124C3E52E95FD3DBDA5BE5C9906D436C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Analytics.PerformanceReporting::get_enabled()");
	bool icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PerformanceReporting_set_enabled_mAAC2FBB9CE52F6F37C8456B121DD17C8395F80F5 (bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*PerformanceReporting_set_enabled_mAAC2FBB9CE52F6F37C8456B121DD17C8395F80F5_ftn) (bool);
	static PerformanceReporting_set_enabled_mAAC2FBB9CE52F6F37C8456B121DD17C8395F80F5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PerformanceReporting_set_enabled_mAAC2FBB9CE52F6F37C8456B121DD17C8395F80F5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Analytics.PerformanceReporting::set_enabled(System.Boolean)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t PerformanceReporting_get_graphicsInitializationFinishTime_m14D93A90C929C57074781B64E6889878E593A9C6 (const RuntimeMethod* method) 
{
	typedef int64_t (*PerformanceReporting_get_graphicsInitializationFinishTime_m14D93A90C929C57074781B64E6889878E593A9C6_ftn) ();
	static PerformanceReporting_get_graphicsInitializationFinishTime_m14D93A90C929C57074781B64E6889878E593A9C6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PerformanceReporting_get_graphicsInitializationFinishTime_m14D93A90C929C57074781B64E6889878E593A9C6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Analytics.PerformanceReporting::get_graphicsInitializationFinishTime()");
	int64_t icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
