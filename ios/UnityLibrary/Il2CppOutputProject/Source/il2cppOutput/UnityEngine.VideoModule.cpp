﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263;
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F;
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C;
struct AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2;
struct AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299;
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2;
struct IAsyncResult_t7B9B5A0ECB35DCEC31B8A8122C37D687369253B5;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct InvalidCastException_t47FC62F21A3937E814D20381DDACEF240E95AC2E;
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB;
struct MethodInfo_t;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27;
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct String_t;
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700;
struct VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE;
struct VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F;
struct VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65;
struct VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct ConsumeSampleFramesNativeFunction_tEFEFD4F9990DC0C4806B21E0EB1CAFBE991D4EDE;
struct SampleFramesHandler_tFE84FF9BBCEFB880D46227188F375BEF680AAA30;
struct CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD;
struct Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377;
struct Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD;
struct MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256;
struct ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462;
struct EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411;
struct FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8;
struct TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_AudioModule[];
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_VideoModule[];
IL2CPP_EXTERN_C RuntimeClass* ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GC_t920F9CF6EBB7C787E5010A4352E1B587F356DC58_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidCastException_t47FC62F21A3937E814D20381DDACEF240E95AC2E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* VideoAudioOutputMode_tFDC43E947CD8D421DD2FD53A836A8D72F6AED874_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral0A0B5411174908C28B5CCC520D982C817E714C77;
IL2CPP_EXTERN_C String_t* _stringLiteral0C2C7A9E7436DA4E0013C1BB4B0A6143CE0F38ED;
IL2CPP_EXTERN_C String_t* _stringLiteral1AC2CE4FFFF43A805CDFE9BE8C8AAD052F378505;
IL2CPP_EXTERN_C String_t* _stringLiteral40EC62B3D080AE498D3DBBDFF3905B41B72B0F05;
IL2CPP_EXTERN_C String_t* _stringLiteral46F273EF641E07D271D91E0DC24A4392582671F8;
IL2CPP_EXTERN_C String_t* _stringLiteral4CE89C314A31478981DC58A93CFEF99E409C4C21;
IL2CPP_EXTERN_C String_t* _stringLiteral5087EF58F8E48E468D2251A5AA70E323DD0B7E90;
IL2CPP_EXTERN_C String_t* _stringLiteral51F9E525111CFECB99B457F44ED737A97D78397D;
IL2CPP_EXTERN_C String_t* _stringLiteral573C0D2F167EB38E04F3195A6EE0598846E07DA2;
IL2CPP_EXTERN_C String_t* _stringLiteral6216AB66F68B9B8A0707687070449261B6454D7B;
IL2CPP_EXTERN_C String_t* _stringLiteral67B5383F0EB49A2F8F216C4A65CE0939F33EC2EE;
IL2CPP_EXTERN_C String_t* _stringLiteral6B0259E30386CD045B834B84467B7811AF926D5B;
IL2CPP_EXTERN_C String_t* _stringLiteral702CAC6E5B2A0B5052071E4FF4BD80AFF05EA14C;
IL2CPP_EXTERN_C String_t* _stringLiteral859573AB83EBF64A46D1CD306749946AC7958B89;
IL2CPP_EXTERN_C String_t* _stringLiteral908CB9BB013EDA289F1474C8682D0596003FC2C2;
IL2CPP_EXTERN_C String_t* _stringLiteralB3DB1462630FDD5731404C559A3B2CCF742276C2;
IL2CPP_EXTERN_C String_t* _stringLiteralD8752400A3C2D30011617DFBC0AE2509DCA5885D;
IL2CPP_EXTERN_C String_t* _stringLiteralE3EE3550F0B526D688236CCEB04B5431FDE7CCC1;
IL2CPP_EXTERN_C const RuntimeMethod* AudioSampleProvider_get_owner_mC8461FD790AEC7BACE00C8A3F1DE4D00E08EF1AC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableExtensions_SetDuration_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_m617DB57EFA756799193EF1B395987228560E3D4B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableHandle_IsPlayableOfType_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_mAB0F25BFD867D3DE6F0D5D20BD2810CB50EC80AD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_CreateHandle_m5B27AADC89B727CCE65C43B5E3BDEFA6AFDBAD4E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_Create_m47910B30B05B23D03C42D7FC55CA50CDB4F208C2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_Equals_mDBA2CCE1368B628829B065EA8DEDE84512B2723E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_GetClip_m175841C1ABCDD9AB1E8F2258717D421E99E1FF89_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_GetLooped_m02EBBDA9AD73DE9C267E078B792FFD6EC876EAF8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_GetPauseDelay_mBADC9339A2FA9425DD5FE20DF84E17C59B828ECF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_GetPauseDelay_mF6F942079CC42CDCDDFC0362790A48D02CDC3F82_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_GetStartDelay_m09B80188EFB96906543504B9B3277B154534DF7C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_IsPlaying_mF9DE3992EB3B39DEEF46A53E993AB9997EF36D29_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_Seek_mD487177A7F67B496D60334BE1A3B11C4FACA7DA5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_Seek_mF192F4E0B2DE3BA5CF73555A21B0B4ACC3EBD00D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_SetClip_mD2648224CBC92B679C23A8D0C81F6F095C753E71_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_SetLooped_m2C96F48F2F03468A77CF8488812FB62B3749A346_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_SetStartDelay_m53B0F03F5BBC0F4444AFA37C99D6FE13C4105A12_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_ValidateStartDelayInternal_m444254E07B5760B0A7B7158F36E8039AEAFF9EA5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_op_Explicit_m54D27A3C2591C1A489720A9BC15B72E65A39F2E4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClipPlayable_op_Implicit_m0456F2955443638FD460C316C823E993E0FE5ABC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoClip__ctor_m0D5152E4073E1D970B8A947BC16309696AAF084A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlaybackMgr_Dispose_mA234FB2994C6DD6E729F8CE29032A36A6399090E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlaybackMgr__ctor_mEC819B47D03CDBA188F0758D4E8D045F2E823941_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayback_GetAudioSampleProvider_mBF210B2D053415976E79A1C65D9BE1A1574ADFFA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayback__ctor_mBB89AC180CB8AEC5A22DB6F870272A2830951A65_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayerExtensions_GetAudioSampleProvider_mA27A786A57E95EDB73E1642EB5F1A912BBADF382_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_InvokeClockResyncOccurredCallback_Internal_m743B8421E9538761141364EBDCEBC673BA58D8C6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_InvokeErrorReceivedCallback_Internal_m91E59ED70189824AB2C5D0AEB47742130EE98574_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_InvokeFrameDroppedCallback_Internal_m6E6B6353404AF76AB16D1433B8553659B0744DC0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_InvokeFrameReadyCallback_Internal_mF5F02FE6BBEA70C62108F3FD836E1B378CB9F5E7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_InvokeLoopPointReachedCallback_Internal_m090FD00BB12721EB3CEB337E30687D5DE6B1EBD6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_InvokePrepareCompletedCallback_Internal_mF483B141E9AF0B553FF87B32A0EE54E40BE21EFD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_InvokeSeekCompletedCallback_Internal_m62AEB9826ACE95B88CC60220375C1C7EF5EEEE1F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_InvokeStartedCallback_Internal_mBDD186C95E57DD264CD0957FA46D9F65728C67EF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer__ctor_m309536693DD1F20C0D1E6ABBF5FC48858746C6B6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_add_clockResyncOccurred_m36C7F41F586D5BEA72B624920418EC237EB61A4F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_add_errorReceived_mDB5EB3E78A571249C101BF6A7CB394A8AF7C0B6A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_add_frameDropped_m25B25C5116E12B905B1D4D8F97E918206BD29C25_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_add_frameReady_m8408E7B2AB88FFF43E260EFF139579C2146741CE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_add_loopPointReached_m384F2CD1412D92561E7A489C05D1F78A7EC931DD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_add_prepareCompleted_m4DB8843A3E3E3CFF7E361E70EA51983AF457183B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_add_seekCompleted_m9D3C4EE17B93B83E4D806420003DD66B4AF23C0C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_add_started_m6CE7943E721F775D734462A8CAB8B61519FAA08F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_get_controlledAudioTrackCount_m7D0887B6AFEBCC16925024A63D0D25E4B2060448_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_remove_clockResyncOccurred_m2C1839586C69C24C9B8E5085CF1A98DDC5AF7074_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_remove_errorReceived_m419AC4C19034B872B7DB82B59B4E61302213FA2A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_remove_frameDropped_m8F5E433D91869FFB7E643C612E09D5FB19E5D3B9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_remove_frameReady_mE7D5C9E4A9D8D34EC6186B570348B54EFEF7FE32_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_remove_loopPointReached_mF8DD445E1CE55B1F4F1AB40ED11DBCA8892930C6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_remove_prepareCompleted_mDA77FE81F97195666D407DBB5689F8289A0513DF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_remove_seekCompleted_m9D9A46A6041E3B09C3546F20F05B7BE30A3B73C8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_remove_started_m1CFF6E499642737A44F18EE9D29DADAABA52E906_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VideoPlayer_set_controlledAudioTrackCount_m5B9EAFA1178F1AB06E7C5EE9E53011C4C86CF8AD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* VideoAudioOutputMode_tFDC43E947CD8D421DD2FD53A836A8D72F6AED874_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tD0D1DCFACB6310AA1821892BA557534E78B6AF00 
{
};
struct AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2  : public RuntimeObject
{
	ConsumeSampleFramesNativeFunction_tEFEFD4F9990DC0C4806B21E0EB1CAFBE991D4EDE* ___m_ConsumeSampleFramesNativeFunction;
	uint32_t ___U3CidU3Ek__BackingField;
	uint16_t ___U3CtrackIndexU3Ek__BackingField;
	Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___U3CownerU3Ek__BackingField;
	uint16_t ___U3CchannelCountU3Ek__BackingField;
	uint32_t ___U3CsampleRateU3Ek__BackingField;
	SampleFramesHandler_tFE84FF9BBCEFB880D46227188F375BEF680AAA30* ___sampleFramesAvailable;
	SampleFramesHandler_tFE84FF9BBCEFB880D46227188F375BEF680AAA30* ___sampleFramesOverflow;
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct VideoPlayerExtensions_t58BDEED964DC83D4554DE71250420F3B2430168E  : public RuntimeObject
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455 
{
	uint16_t ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF 
{
	uint64_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct PlayState_tC54D3C31D69D8394457D7EC5712E56B0891A5750 
{
	int32_t ___value__;
};
struct PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E 
{
	intptr_t ___m_Handle;
	uint32_t ___m_Version;
};
struct PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 
{
	intptr_t ___m_Handle;
	uint32_t ___m_Version;
};
struct Video3DLayout_t6E17571466249890F3E4CBC4D5C4C3D7261DFA74 
{
	int32_t ___value__;
};
struct VideoAlphaLayout_tDABA699876959A0E54E27968BC53F5C26238D5CA 
{
	int32_t ___value__;
};
struct VideoAspectRatio_t6D4DCBAAAF4DF8518C7C39EAE75EEA6986AA5A47 
{
	int32_t ___value__;
};
struct VideoAudioOutputMode_tFDC43E947CD8D421DD2FD53A836A8D72F6AED874 
{
	int32_t ___value__;
};
struct VideoError_t0CDF652604926153B5D889A6DA3E25BD6B4EFD47 
{
	int32_t ___value__;
};
struct VideoPixelFormat_t4F833873C924C9CCF797F50FDC7181DF1C3CC563 
{
	int32_t ___value__;
};
struct VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F  : public RuntimeObject
{
	intptr_t ___m_Ptr;
};
struct VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65  : public RuntimeObject
{
	intptr_t ___m_Ptr;
};
struct VideoRenderMode_tB7AF1B303098B26EF56A4D3D30D9FB5BA35C96A1 
{
	int32_t ___value__;
};
struct VideoSource_t1BCC79FEDD081663192AC0AD8A74AE78B5C8E291 
{
	int32_t ___value__;
};
struct VideoTimeReference_tA2E6847CF38D64289E5AF3669C95F35B8864D351 
{
	int32_t ___value__;
};
struct VideoTimeSource_t4C874889018CCB1822AB2CC316A52D8846865958 
{
	int32_t ___value__;
};
struct VideoTimeUpdateMode_tFD5762EC6997152B5285D66EAEA3AC4102EC7882 
{
	int32_t ___value__;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F 
{
	PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 ___m_Handle;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 
{
	PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 ___m_Handle;
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C  : public MulticastDelegate_t
{
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct InvalidCastException_t47FC62F21A3937E814D20381DDACEF240E95AC2E  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27  : public Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700
{
};
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377  : public MulticastDelegate_t
{
};
struct Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD  : public MulticastDelegate_t
{
};
struct MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256  : public MulticastDelegate_t
{
};
struct ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462  : public MulticastDelegate_t
{
};
struct EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411  : public MulticastDelegate_t
{
};
struct FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8  : public MulticastDelegate_t
{
};
struct TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E  : public MulticastDelegate_t
{
};
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F  : public ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263
{
	RuntimeObject* ____actualValue;
};
struct AudioBehaviour_t2DC0BEF7B020C952F3D2DA5AAAC88501C7EEB941  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___prepareCompleted;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___loopPointReached;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___started;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___frameDropped;
	ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* ___errorReceived;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___seekCompleted;
	TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* ___clockResyncOccurred;
	FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* ___frameReady;
};
struct AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299  : public AudioBehaviour_t2DC0BEF7B020C952F3D2DA5AAAC88501C7EEB941
{
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_StaticFields
{
	CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB* ___enumSeperatorCharArray;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_StaticFields
{
	PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 ___m_Null;
};
struct Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F_StaticFields
{
	Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F ___m_NullPlayable;
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700_StaticFields
{
	int32_t ___GenerateAllMips;
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields
{
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreCull;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreRender;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPostRender;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771  : public RuntimeArray
{
	ALIGN_FIELD (8) Delegate_t* m_Items[1];

	inline Delegate_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Delegate_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Delegate_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Delegate_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableExtensions_SetDuration_TisIl2CppFullySharedGenericStruct_m8E76A0F5DA9E4AED0A1C79EBC1C9260EDFA521E6_gshared (Il2CppFullySharedGenericStruct ___0_playable, double ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PlayableHandle_IsPlayableOfType_TisIl2CppFullySharedGenericAny_mA4E158678160F1D9713643D243397452502EE932_gshared (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* __this, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 VideoClipPlayable_CreateHandle_m5B27AADC89B727CCE65C43B5E3BDEFA6AFDBAD4E (PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E ___0_graph, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___1_clip, bool ___2_looping, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClip_get_length_m3A5628F24610F06D4F1E7FBC700722B77942121D (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) ;
inline void PlayableExtensions_SetDuration_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_m617DB57EFA756799193EF1B395987228560E3D4B (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 ___0_playable, double ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Il2CppFullySharedGenericStruct, double, const RuntimeMethod*))PlayableExtensions_SetDuration_TisIl2CppFullySharedGenericStruct_m8E76A0F5DA9E4AED0A1C79EBC1C9260EDFA521E6_gshared)((Il2CppFullySharedGenericStruct)&___0_playable, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 PlayableHandle_get_Null_mF44FE0A71C67054D272F454C91F7E08CBF14A975 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_InternalCreateVideoClipPlayable_mC6E8234F6F3FD32AE36873EFA672D2801A97C2A6 (PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E* ___0_graph, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___1_clip, bool ___2_looping, PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___3_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PlayableHandle_IsValid_m07631D12846BAAF2CC302E69A28A44BFE9EB5098 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* __this, const RuntimeMethod* method) ;
inline bool PlayableHandle_IsPlayableOfType_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_mAB0F25BFD867D3DE6F0D5D20BD2810CB50EC80AD (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*, const RuntimeMethod*))PlayableHandle_IsPlayableOfType_TisIl2CppFullySharedGenericAny_mA4E158678160F1D9713643D243397452502EE932_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidCastException__ctor_mED3F9781E6CA47A92C602C896EDDF2F40ECB8644 (InvalidCastException_t47FC62F21A3937E814D20381DDACEF240E95AC2E* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Playable__ctor_mD2EB35E024816AEED68795D0124EAB30E05BF6C4 (Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F* __this, PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 Playable_GetHandle_m39356D23E849DC5428B262092657662C064E04F8 (Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PlayableHandle_op_Equality_m0E6C48A28F75A870AC22ADE3BD42F7F70A43C99C (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 ___0_x, PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_Equals_mDBA2CCE1368B628829B065EA8DEDE84512B2723E (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* VideoClipPlayable_GetClipInternal_m27C154E1FA8680A00D6DF3325B802CAD50F18670 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* VideoClipPlayable_GetClip_m175841C1ABCDD9AB1E8F2258717D421E99E1FF89 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetClipInternal_m0D8D01F0C80FD264660C7D28D78110B14AB8B82B (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___1_clip, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetClip_mD2648224CBC92B679C23A8D0C81F6F095C753E71 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_GetLoopedInternal_mC1F05420ADD9902153C8FF2772CC70375214F5CE (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_GetLooped_m02EBBDA9AD73DE9C267E078B792FFD6EC876EAF8 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetLoopedInternal_m3B18474AA52151CF68687050197E37F425C17128 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, bool ___1_looped, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetLooped_m2C96F48F2F03468A77CF8488812FB62B3749A346 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_GetIsPlayingInternal_m3638460D1F41D407B5FC0EE55BE6110148FA3019 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_IsPlaying_mF9DE3992EB3B39DEEF46A53E993AB9997EF36D29 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClipPlayable_GetStartDelayInternal_mDF534576256379603C942CA855F0848BE48DCE31 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClipPlayable_GetStartDelay_m09B80188EFB96906543504B9B3277B154534DF7C (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_ValidateStartDelayInternal_m444254E07B5760B0A7B7158F36E8039AEAFF9EA5 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_startDelay, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetStartDelayInternal_m09C29D756A8824E26F62382CBDD63C91A28C5B27 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, double ___1_delay, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetStartDelay_m53B0F03F5BBC0F4444AFA37C99D6FE13C4105A12 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClipPlayable_GetPauseDelayInternal_m97726033B939451F02DC05BBDB954074BA388A1B (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClipPlayable_GetPauseDelay_mBADC9339A2FA9425DD5FE20DF84E17C59B828ECF (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayableHandle_GetPlayState_m14547B804BB9B9B9E2B20B3F0975334CC99E778F (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Double_ToString_m7499A5D792419537DCB9470A3675CEF5117DE339 (double* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetPauseDelayInternal_m639E25CD82711C6C59C52EAC2D11C08E7C4401D1 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, double ___1_delay, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_GetPauseDelay_mF6F942079CC42CDCDDFC0362790A48D02CDC3F82 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_Seek_mD487177A7F67B496D60334BE1A3B11C4FACA7DA5 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_startTime, double ___1_startDelay, double ___2_duration, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_Seek_mF192F4E0B2DE3BA5CF73555A21B0B4ACC3EBD00D (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_startTime, double ___1_startDelay, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableHandle_SetDuration_m4E07F9F3A90B2F3CE10325D9F405993F03CCA08B (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* __this, double ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableHandle_SetTime_m6D255AB6779F3DC278813F1C016FE4EBC9F4B1E4 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* __this, double ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableHandle_Play_m1EF8E1105EB6A4A9B0D6638A57CDCBCEEDC6FB2D (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayer_get_controlledAudioTrackCount_m7D0887B6AFEBCC16925024A63D0D25E4B2060448 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* UInt16_ToString_m57629B7E74D92A54414073D5C27D6827C93A4DD5 (uint16_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentOutOfRangeException__ctor_m60B543A63AC8692C28096003FBF2AD124B9D5B85 (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* __this, String_t* ___0_paramName, RuntimeObject* ___1_actualValue, String_t* ___2_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayer_get_audioOutputMode_m22644BE075B145626BD48015E5948CAF4486B60E (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Enum_ToString_m946B0B83C4470457D0FF555D862022C72BB55741 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162 (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayerExtensions_InternalGetAudioSampleProviderId_mE321D812009B94D0F1E90637E4F06070FF49155E (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_vp, uint16_t ___1_trackIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* AudioSampleProvider_Lookup_mAC9B381D61BBC6504C62B6956FD0CE7BEA989BF7 (uint32_t ___0_providerId, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_ownerObj, uint16_t ___2_trackIndex, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* AudioSampleProvider_get_owner_mC8461FD790AEC7BACE00C8A3F1DE4D00E08EF1AC_inline (AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6_inline (AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m093934F71A9B351911EE46311674ED463B180006 (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, String_t* ___3_str3, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m2149FA40CEC8D82AC20D3508AB40C0D8EFEF68E6 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayer_GetControlledAudioTrackCount_m637055B53DAC8178FA540AB70294DAC090B035EC (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayer_get_controlledAudioTrackMaxCount_mC53CBFD61E4B2DBAA3B296E0FC990064ECC69DA5 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mA8DBB4C2516B9723C5A41E6CB1E2FAF4BBE96DD8 (String_t* ___0_format, RuntimeObject* ___1_arg0, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m8F9D40CE19D19B698A70F9A258640EB52DB39B62 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, String_t* ___0_message, String_t* ___1_paramName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_SetControlledAudioTrackCount_m548AAC0F8B93551FCF9B7037F7E5A1CC079CCEC5 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00 (Delegate_t* ___0_a, Delegate_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3 (Delegate_t* ___0_source, Delegate_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_inline (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235_inline (FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, int64_t ___1_frameIdx, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5_inline (ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, String_t* ___1_message, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D_inline (TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, double ___1_seconds, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8 (Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayback_GetAudioTrackCount_mF02D113890513832B10BF110672541F6F153A9AE (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayback_GetAudioSampleProviderId_m623001A0D78033DA535639A33B2D7F8EC3F5601C (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR intptr_t VideoPlaybackMgr_Internal_Create_mFF19AB8170FAD9831FAEB264CF668869A6292575 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool IntPtr_op_Inequality_m90EFC9C4CAD9A33E309F2DDF98EE4E1DD253637B (intptr_t ___0_value1, intptr_t ___1_value2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlaybackMgr_Internal_Destroy_m2C276421DA1094DE0154D07C63059A489B2C9886 (intptr_t ___0_ptr, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GC_SuppressFinalize_m71815DBD5A0CD2EA1BE43317B08B7A14949EDC65 (RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 VideoClipPlayable_Create_m47910B30B05B23D03C42D7FC55CA50CDB4F208C2 (PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E ___0_graph, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___1_clip, bool ___2_looping, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableExtensions_SetDuration_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_m617DB57EFA756799193EF1B395987228560E3D4B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_Create_m47910B30B05B23D03C42D7FC55CA50CDB4F208C2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 V_0;
	memset((&V_0), 0, sizeof(V_0));
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 V_1;
	memset((&V_1), 0, sizeof(V_1));
	bool V_2 = false;
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 V_3;
	memset((&V_3), 0, sizeof(V_3));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_graph), (&___1_clip), (&___2_looping));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0), (&V_1));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_Create_m47910B30B05B23D03C42D7FC55CA50CDB4F208C2_RuntimeMethod_var, NULL, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 3));
		PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E L_0 = ___0_graph;
		VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* L_1 = ___1_clip;
		bool L_2 = ___2_looping;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 4));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_3;
		L_3 = VideoClipPlayable_CreateHandle_m5B27AADC89B727CCE65C43B5E3BDEFA6AFDBAD4E(L_0, L_1, L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 4));
		V_0 = L_3;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 5));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_4 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 6));
		VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C((&V_1), L_4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 6));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 7));
		VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* L_5 = ___1_clip;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 8));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_6;
		L_6 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_5, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 8));
		V_2 = L_6;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 9));
		bool L_7 = V_2;
		if (!L_7)
		{
			goto IL_002a;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 10));
		VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 L_8 = V_1;
		VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* L_9 = ___1_clip;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 11));
		NullCheck(L_9);
		double L_10;
		L_10 = VideoClip_get_length_m3A5628F24610F06D4F1E7FBC700722B77942121D(L_9, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 11));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 12));
		PlayableExtensions_SetDuration_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_m617DB57EFA756799193EF1B395987228560E3D4B(L_8, L_10, PlayableExtensions_SetDuration_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_m617DB57EFA756799193EF1B395987228560E3D4B_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 12));
	}

IL_002a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 13));
		VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 L_11 = V_1;
		V_3 = L_11;
		goto IL_002e;
	}

IL_002e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 14));
		VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 L_12 = V_3;
		return L_12;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 VideoClipPlayable_CreateHandle_m5B27AADC89B727CCE65C43B5E3BDEFA6AFDBAD4E (PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E ___0_graph, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___1_clip, bool ___2_looping, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_CreateHandle_m5B27AADC89B727CCE65C43B5E3BDEFA6AFDBAD4E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 V_0;
	memset((&V_0), 0, sizeof(V_0));
	bool V_1 = false;
	PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 V_2;
	memset((&V_2), 0, sizeof(V_2));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_graph), (&___1_clip), (&___2_looping));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_CreateHandle_m5B27AADC89B727CCE65C43B5E3BDEFA6AFDBAD4E_RuntimeMethod_var, NULL, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 15));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 16));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 17));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 18));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 19));
		il2cpp_codegen_runtime_class_init_inline(PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_0;
		L_0 = PlayableHandle_get_Null_mF44FE0A71C67054D272F454C91F7E08CBF14A975(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 19));
		V_0 = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 20));
		VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* L_1 = ___1_clip;
		bool L_2 = ___2_looping;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 21));
		bool L_3;
		L_3 = VideoClipPlayable_InternalCreateVideoClipPlayable_mC6E8234F6F3FD32AE36873EFA672D2801A97C2A6((&___0_graph), L_1, L_2, (&V_0), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 21));
		V_1 = (bool)((((int32_t)L_3) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 22));
		bool L_4 = V_1;
		if (!L_4)
		{
			goto IL_0021;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 23));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 24));
		il2cpp_codegen_runtime_class_init_inline(PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_5;
		L_5 = PlayableHandle_get_Null_mF44FE0A71C67054D272F454C91F7E08CBF14A975(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 24));
		V_2 = L_5;
		goto IL_0025;
	}

IL_0021:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 25));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_6 = V_0;
		V_2 = L_6;
		goto IL_0025;
	}

IL_0025:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 26));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_7 = V_2;
		return L_7;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 ___0_handle, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableHandle_IsPlayableOfType_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_mAB0F25BFD867D3DE6F0D5D20BD2810CB50EC80AD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_handle));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 27));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 28));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 29));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 30));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 31));
		il2cpp_codegen_runtime_class_init_inline(PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		bool L_0;
		L_0 = PlayableHandle_IsValid_m07631D12846BAAF2CC302E69A28A44BFE9EB5098((&___0_handle), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 31));
		V_0 = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 32));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0027;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 33));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 34));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 35));
		il2cpp_codegen_runtime_class_init_inline(PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = PlayableHandle_IsPlayableOfType_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_mAB0F25BFD867D3DE6F0D5D20BD2810CB50EC80AD((&___0_handle), PlayableHandle_IsPlayableOfType_TisVideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_mAB0F25BFD867D3DE6F0D5D20BD2810CB50EC80AD_RuntimeMethod_var);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 35));
		V_1 = (bool)((((int32_t)L_2) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 36));
		bool L_3 = V_1;
		if (!L_3)
		{
			goto IL_0026;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 37));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 38));
		InvalidCastException_t47FC62F21A3937E814D20381DDACEF240E95AC2E* L_4 = (InvalidCastException_t47FC62F21A3937E814D20381DDACEF240E95AC2E*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidCastException_t47FC62F21A3937E814D20381DDACEF240E95AC2E_il2cpp_TypeInfo_var)));
		InvalidCastException__ctor_mED3F9781E6CA47A92C602C896EDDF2F40ECB8644(L_4, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral908CB9BB013EDA289F1474C8682D0596003FC2C2)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 38));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_4, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C_RuntimeMethod_var)));
	}

IL_0026:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 39));
	}

IL_0027:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 40));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_5 = ___0_handle;
		__this->___m_Handle = L_5;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 41));
		return;
	}
}
IL2CPP_EXTERN_C  void VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C_AdjustorThunk (RuntimeObject* __this, PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 ___0_handle, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C(_thisAdjusted, ___0_handle, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 42));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 43));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 44));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 45));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_0 = __this->___m_Handle;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 46));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 _returnValue;
	_returnValue = VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F VideoClipPlayable_op_Implicit_m0456F2955443638FD460C316C823E993E0FE5ABC (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 ___0_playable, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_op_Implicit_m0456F2955443638FD460C316C823E993E0FE5ABC_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_playable));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_op_Implicit_m0456F2955443638FD460C316C823E993E0FE5ABC_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 47));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 48));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 49));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 50));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 51));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_0;
		L_0 = VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D((&___0_playable), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 51));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 52));
		Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F L_1;
		memset((&L_1), 0, sizeof(L_1));
		Playable__ctor_mD2EB35E024816AEED68795D0124EAB30E05BF6C4((&L_1), L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 52));
		V_0 = L_1;
		goto IL_0010;
	}

IL_0010:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 53));
		Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 VideoClipPlayable_op_Explicit_m54D27A3C2591C1A489720A9BC15B72E65A39F2E4 (Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F ___0_playable, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_op_Explicit_m54D27A3C2591C1A489720A9BC15B72E65A39F2E4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_playable));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_op_Explicit_m54D27A3C2591C1A489720A9BC15B72E65A39F2E4_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 54));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 55));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 56));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 57));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 58));
		il2cpp_codegen_runtime_class_init_inline(Playable_t95C6B795846BA0C7D96E4DA14897CCCF2554334F_il2cpp_TypeInfo_var);
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_0;
		L_0 = Playable_GetHandle_m39356D23E849DC5428B262092657662C064E04F8((&___0_playable), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 58));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 59));
		VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 L_1;
		memset((&L_1), 0, sizeof(L_1));
		VideoClipPlayable__ctor_m959FA980ACB1F9F296A59C038C308122851B462C((&L_1), L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 59));
		V_0 = L_1;
		goto IL_0010;
	}

IL_0010:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 60));
		VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_Equals_mDBA2CCE1368B628829B065EA8DEDE84512B2723E (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_Equals_mDBA2CCE1368B628829B065EA8DEDE84512B2723E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_other));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_Equals_mDBA2CCE1368B628829B065EA8DEDE84512B2723E_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 61));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 62));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 63));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 64));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 65));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_0;
		L_0 = VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 65));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 66));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4 L_1;
		L_1 = VideoClipPlayable_GetHandle_m1D990903234220E9FE756F2DA895650EBDC1415D((&___0_other), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 66));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 67));
		il2cpp_codegen_runtime_class_init_inline(PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = PlayableHandle_op_Equality_m0E6C48A28F75A870AC22ADE3BD42F7F70A43C99C(L_0, L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 67));
		V_0 = L_2;
		goto IL_0016;
	}

IL_0016:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 68));
		bool L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C  bool VideoClipPlayable_Equals_mDBA2CCE1368B628829B065EA8DEDE84512B2723E_AdjustorThunk (RuntimeObject* __this, VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1 ___0_other, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	bool _returnValue;
	_returnValue = VideoClipPlayable_Equals_mDBA2CCE1368B628829B065EA8DEDE84512B2723E(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* VideoClipPlayable_GetClip_m175841C1ABCDD9AB1E8F2258717D421E99E1FF89 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_GetClip_m175841C1ABCDD9AB1E8F2258717D421E99E1FF89_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* V_0 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_GetClip_m175841C1ABCDD9AB1E8F2258717D421E99E1FF89_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 69));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 70));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 71));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 72));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_0 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 73));
		VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* L_1;
		L_1 = VideoClipPlayable_GetClipInternal_m27C154E1FA8680A00D6DF3325B802CAD50F18670(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 73));
		V_0 = L_1;
		goto IL_000f;
	}

IL_000f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 74));
		VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C  VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* VideoClipPlayable_GetClip_m175841C1ABCDD9AB1E8F2258717D421E99E1FF89_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* _returnValue;
	_returnValue = VideoClipPlayable_GetClip_m175841C1ABCDD9AB1E8F2258717D421E99E1FF89(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetClip_mD2648224CBC92B679C23A8D0C81F6F095C753E71 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_SetClip_mD2648224CBC92B679C23A8D0C81F6F095C753E71_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_SetClip_mD2648224CBC92B679C23A8D0C81F6F095C753E71_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 75));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 76));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 77));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 78));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_0 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* L_1 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 79));
		VideoClipPlayable_SetClipInternal_m0D8D01F0C80FD264660C7D28D78110B14AB8B82B(L_0, L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 79));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 80));
		return;
	}
}
IL2CPP_EXTERN_C  void VideoClipPlayable_SetClip_mD2648224CBC92B679C23A8D0C81F6F095C753E71_AdjustorThunk (RuntimeObject* __this, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___0_value, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	VideoClipPlayable_SetClip_mD2648224CBC92B679C23A8D0C81F6F095C753E71(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_GetLooped_m02EBBDA9AD73DE9C267E078B792FFD6EC876EAF8 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_GetLooped_m02EBBDA9AD73DE9C267E078B792FFD6EC876EAF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_GetLooped_m02EBBDA9AD73DE9C267E078B792FFD6EC876EAF8_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 81));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 82));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 83));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 84));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_0 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 85));
		bool L_1;
		L_1 = VideoClipPlayable_GetLoopedInternal_mC1F05420ADD9902153C8FF2772CC70375214F5CE(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 85));
		V_0 = L_1;
		goto IL_000f;
	}

IL_000f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 86));
		bool L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C  bool VideoClipPlayable_GetLooped_m02EBBDA9AD73DE9C267E078B792FFD6EC876EAF8_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	bool _returnValue;
	_returnValue = VideoClipPlayable_GetLooped_m02EBBDA9AD73DE9C267E078B792FFD6EC876EAF8(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetLooped_m2C96F48F2F03468A77CF8488812FB62B3749A346 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, bool ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_SetLooped_m2C96F48F2F03468A77CF8488812FB62B3749A346_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_SetLooped_m2C96F48F2F03468A77CF8488812FB62B3749A346_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 87));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 88));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 89));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 90));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_0 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		bool L_1 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 91));
		VideoClipPlayable_SetLoopedInternal_m3B18474AA52151CF68687050197E37F425C17128(L_0, L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 91));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 92));
		return;
	}
}
IL2CPP_EXTERN_C  void VideoClipPlayable_SetLooped_m2C96F48F2F03468A77CF8488812FB62B3749A346_AdjustorThunk (RuntimeObject* __this, bool ___0_value, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	VideoClipPlayable_SetLooped_m2C96F48F2F03468A77CF8488812FB62B3749A346(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_IsPlaying_mF9DE3992EB3B39DEEF46A53E993AB9997EF36D29 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_IsPlaying_mF9DE3992EB3B39DEEF46A53E993AB9997EF36D29_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_IsPlaying_mF9DE3992EB3B39DEEF46A53E993AB9997EF36D29_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 93));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 94));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 95));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 96));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_0 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 97));
		bool L_1;
		L_1 = VideoClipPlayable_GetIsPlayingInternal_m3638460D1F41D407B5FC0EE55BE6110148FA3019(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 97));
		V_0 = L_1;
		goto IL_000f;
	}

IL_000f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 98));
		bool L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C  bool VideoClipPlayable_IsPlaying_mF9DE3992EB3B39DEEF46A53E993AB9997EF36D29_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	bool _returnValue;
	_returnValue = VideoClipPlayable_IsPlaying_mF9DE3992EB3B39DEEF46A53E993AB9997EF36D29(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClipPlayable_GetStartDelay_m09B80188EFB96906543504B9B3277B154534DF7C (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_GetStartDelay_m09B80188EFB96906543504B9B3277B154534DF7C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	double V_0 = 0.0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_GetStartDelay_m09B80188EFB96906543504B9B3277B154534DF7C_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 99));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 100));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 101));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 102));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_0 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 103));
		double L_1;
		L_1 = VideoClipPlayable_GetStartDelayInternal_mDF534576256379603C942CA855F0848BE48DCE31(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 103));
		V_0 = L_1;
		goto IL_000f;
	}

IL_000f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 104));
		double L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C  double VideoClipPlayable_GetStartDelay_m09B80188EFB96906543504B9B3277B154534DF7C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	double _returnValue;
	_returnValue = VideoClipPlayable_GetStartDelay_m09B80188EFB96906543504B9B3277B154534DF7C(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetStartDelay_m53B0F03F5BBC0F4444AFA37C99D6FE13C4105A12 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_SetStartDelay_m53B0F03F5BBC0F4444AFA37C99D6FE13C4105A12_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_SetStartDelay_m53B0F03F5BBC0F4444AFA37C99D6FE13C4105A12_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 105));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 106));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 107));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 108));
		double L_0 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 109));
		VideoClipPlayable_ValidateStartDelayInternal_m444254E07B5760B0A7B7158F36E8039AEAFF9EA5(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 109));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 110));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_1 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		double L_2 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 111));
		VideoClipPlayable_SetStartDelayInternal_m09C29D756A8824E26F62382CBDD63C91A28C5B27(L_1, L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 111));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 112));
		return;
	}
}
IL2CPP_EXTERN_C  void VideoClipPlayable_SetStartDelay_m53B0F03F5BBC0F4444AFA37C99D6FE13C4105A12_AdjustorThunk (RuntimeObject* __this, double ___0_value, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	VideoClipPlayable_SetStartDelay_m53B0F03F5BBC0F4444AFA37C99D6FE13C4105A12(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClipPlayable_GetPauseDelay_mBADC9339A2FA9425DD5FE20DF84E17C59B828ECF (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_GetPauseDelay_mBADC9339A2FA9425DD5FE20DF84E17C59B828ECF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	double V_0 = 0.0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_GetPauseDelay_mBADC9339A2FA9425DD5FE20DF84E17C59B828ECF_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 113));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 114));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 115));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 116));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_0 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 117));
		double L_1;
		L_1 = VideoClipPlayable_GetPauseDelayInternal_m97726033B939451F02DC05BBDB954074BA388A1B(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 117));
		V_0 = L_1;
		goto IL_000f;
	}

IL_000f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 118));
		double L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C  double VideoClipPlayable_GetPauseDelay_mBADC9339A2FA9425DD5FE20DF84E17C59B828ECF_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	double _returnValue;
	_returnValue = VideoClipPlayable_GetPauseDelay_mBADC9339A2FA9425DD5FE20DF84E17C59B828ECF(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_GetPauseDelay_mF6F942079CC42CDCDDFC0362790A48D02CDC3F82 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_GetPauseDelay_mF6F942079CC42CDCDDFC0362790A48D02CDC3F82_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	double V_0 = 0.0;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_GetPauseDelay_mF6F942079CC42CDCDDFC0362790A48D02CDC3F82_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 119));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 120));
	int32_t G_B5_0 = 0;
	int32_t G_B7_0 = 0;
	int32_t G_B9_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 121));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 122));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_0 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 123));
		double L_1;
		L_1 = VideoClipPlayable_GetPauseDelayInternal_m97726033B939451F02DC05BBDB954074BA388A1B(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 123));
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 124));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_2 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 125));
		il2cpp_codegen_runtime_class_init_inline(PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		int32_t L_3;
		L_3 = PlayableHandle_GetPlayState_m14547B804BB9B9B9E2B20B3F0975334CC99E778F(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 125));
		if ((!(((uint32_t)L_3) == ((uint32_t)1))))
		{
			goto IL_0047;
		}
	}
	{
		double L_4 = ___0_value;
		if ((((double)L_4) < ((double)(0.050000000000000003))))
		{
			goto IL_0044;
		}
	}
	{
		double L_5 = V_0;
		if ((((double)L_5) == ((double)(0.0))))
		{
			goto IL_0041;
		}
	}
	{
		double L_6 = V_0;
		G_B5_0 = ((((double)L_6) < ((double)(0.050000000000000003)))? 1 : 0);
		goto IL_0042;
	}

IL_0041:
	{
		G_B5_0 = 0;
	}

IL_0042:
	{
		G_B7_0 = G_B5_0;
		goto IL_0045;
	}

IL_0044:
	{
		G_B7_0 = 1;
	}

IL_0045:
	{
		G_B9_0 = G_B7_0;
		goto IL_0048;
	}

IL_0047:
	{
		G_B9_0 = 0;
	}

IL_0048:
	{
		V_1 = (bool)G_B9_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 126));
		bool L_7 = V_1;
		if (!L_7)
		{
			goto IL_0068;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 127));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 128));
		String_t* L_8;
		L_8 = Double_ToString_m7499A5D792419537DCB9470A3675CEF5117DE339((&V_0), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 128));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 129));
		String_t* L_9;
		L_9 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralE3EE3550F0B526D688236CCEB04B5431FDE7CCC1)), L_8, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral40EC62B3D080AE498D3DBBDFF3905B41B72B0F05)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 129));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 130));
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_10 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465(L_10, L_9, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 130));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_10, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoClipPlayable_GetPauseDelay_mF6F942079CC42CDCDDFC0362790A48D02CDC3F82_RuntimeMethod_var)));
	}

IL_0068:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 131));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_11 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		double L_12 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 132));
		VideoClipPlayable_SetPauseDelayInternal_m639E25CD82711C6C59C52EAC2D11C08E7C4401D1(L_11, L_12, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 132));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 133));
		return;
	}
}
IL2CPP_EXTERN_C  void VideoClipPlayable_GetPauseDelay_mF6F942079CC42CDCDDFC0362790A48D02CDC3F82_AdjustorThunk (RuntimeObject* __this, double ___0_value, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	VideoClipPlayable_GetPauseDelay_mF6F942079CC42CDCDDFC0362790A48D02CDC3F82(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_Seek_mF192F4E0B2DE3BA5CF73555A21B0B4ACC3EBD00D (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_startTime, double ___1_startDelay, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_Seek_mF192F4E0B2DE3BA5CF73555A21B0B4ACC3EBD00D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_startTime), (&___1_startDelay));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_Seek_mF192F4E0B2DE3BA5CF73555A21B0B4ACC3EBD00D_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 134));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 135));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 136));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 137));
		double L_0 = ___0_startTime;
		double L_1 = ___1_startDelay;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 138));
		VideoClipPlayable_Seek_mD487177A7F67B496D60334BE1A3B11C4FACA7DA5(__this, L_0, L_1, (0.0), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 138));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 139));
		return;
	}
}
IL2CPP_EXTERN_C  void VideoClipPlayable_Seek_mF192F4E0B2DE3BA5CF73555A21B0B4ACC3EBD00D_AdjustorThunk (RuntimeObject* __this, double ___0_startTime, double ___1_startDelay, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	VideoClipPlayable_Seek_mF192F4E0B2DE3BA5CF73555A21B0B4ACC3EBD00D(_thisAdjusted, ___0_startTime, ___1_startDelay, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_Seek_mD487177A7F67B496D60334BE1A3B11C4FACA7DA5 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_startTime, double ___1_startDelay, double ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_Seek_mD487177A7F67B496D60334BE1A3B11C4FACA7DA5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_startTime), (&___1_startDelay), (&___2_duration));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_Seek_mD487177A7F67B496D60334BE1A3B11C4FACA7DA5_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 140));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 141));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 142));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 143));
		double L_0 = ___1_startDelay;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 144));
		VideoClipPlayable_ValidateStartDelayInternal_m444254E07B5760B0A7B7158F36E8039AEAFF9EA5(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 144));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 145));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_1 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		double L_2 = ___1_startDelay;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 146));
		VideoClipPlayable_SetStartDelayInternal_m09C29D756A8824E26F62382CBDD63C91A28C5B27(L_1, L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 146));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 147));
		double L_3 = ___2_duration;
		V_0 = (bool)((((double)L_3) > ((double)(0.0)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 148));
		bool L_4 = V_0;
		if (!L_4)
		{
			goto IL_0048;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 149));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 150));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_5 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		double L_6 = ___2_duration;
		double L_7 = ___0_startTime;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 151));
		il2cpp_codegen_runtime_class_init_inline(PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		PlayableHandle_SetDuration_m4E07F9F3A90B2F3CE10325D9F405993F03CCA08B(L_5, ((double)il2cpp_codegen_add(L_6, L_7)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 151));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 152));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_8 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		double L_9 = ___1_startDelay;
		double L_10 = ___2_duration;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 153));
		VideoClipPlayable_SetPauseDelayInternal_m639E25CD82711C6C59C52EAC2D11C08E7C4401D1(L_8, ((double)il2cpp_codegen_add(L_9, L_10)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 153));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 154));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 155));
		goto IL_0074;
	}

IL_0048:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 156));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 157));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_11 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 158));
		il2cpp_codegen_runtime_class_init_inline(PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		PlayableHandle_SetDuration_m4E07F9F3A90B2F3CE10325D9F405993F03CCA08B(L_11, (1.7976931348623157E+308), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 158));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 159));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_12 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 160));
		VideoClipPlayable_SetPauseDelayInternal_m639E25CD82711C6C59C52EAC2D11C08E7C4401D1(L_12, (0.0), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 160));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 161));
	}

IL_0074:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 162));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_13 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		double L_14 = ___0_startTime;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 163));
		il2cpp_codegen_runtime_class_init_inline(PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4_il2cpp_TypeInfo_var);
		PlayableHandle_SetTime_m6D255AB6779F3DC278813F1C016FE4EBC9F4B1E4(L_13, L_14, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 163));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 164));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_15 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 165));
		PlayableHandle_Play_m1EF8E1105EB6A4A9B0D6638A57CDCBCEEDC6FB2D(L_15, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 165));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 166));
		return;
	}
}
IL2CPP_EXTERN_C  void VideoClipPlayable_Seek_mD487177A7F67B496D60334BE1A3B11C4FACA7DA5_AdjustorThunk (RuntimeObject* __this, double ___0_startTime, double ___1_startDelay, double ___2_duration, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	VideoClipPlayable_Seek_mD487177A7F67B496D60334BE1A3B11C4FACA7DA5(_thisAdjusted, ___0_startTime, ___1_startDelay, ___2_duration, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_ValidateStartDelayInternal_m444254E07B5760B0A7B7158F36E8039AEAFF9EA5 (VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* __this, double ___0_startDelay, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_ValidateStartDelayInternal_m444254E07B5760B0A7B7158F36E8039AEAFF9EA5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0A0B5411174908C28B5CCC520D982C817E714C77);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral40EC62B3D080AE498D3DBBDFF3905B41B72B0F05);
		s_Il2CppMethodInitialized = true;
	}
	double V_0 = 0.0;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_startDelay));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClipPlayable_ValidateStartDelayInternal_m444254E07B5760B0A7B7158F36E8039AEAFF9EA5_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 167));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 168));
	int32_t G_B5_0 = 0;
	int32_t G_B7_0 = 0;
	int32_t G_B9_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 169));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 170));
		PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* L_0 = (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*)(&__this->___m_Handle);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 171));
		double L_1;
		L_1 = VideoClipPlayable_GetStartDelayInternal_mDF534576256379603C942CA855F0848BE48DCE31(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 171));
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 172));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 173));
		bool L_2;
		L_2 = VideoClipPlayable_IsPlaying_mF9DE3992EB3B39DEEF46A53E993AB9997EF36D29(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 173));
		if (!L_2)
		{
			goto IL_0041;
		}
	}
	{
		double L_3 = ___0_startDelay;
		if ((((double)L_3) < ((double)(0.050000000000000003))))
		{
			goto IL_003e;
		}
	}
	{
		double L_4 = V_0;
		if ((!(((double)L_4) >= ((double)(1.0000000000000001E-05)))))
		{
			goto IL_003b;
		}
	}
	{
		double L_5 = V_0;
		G_B5_0 = ((((double)L_5) < ((double)(0.050000000000000003)))? 1 : 0);
		goto IL_003c;
	}

IL_003b:
	{
		G_B5_0 = 0;
	}

IL_003c:
	{
		G_B7_0 = G_B5_0;
		goto IL_003f;
	}

IL_003e:
	{
		G_B7_0 = 1;
	}

IL_003f:
	{
		G_B9_0 = G_B7_0;
		goto IL_0042;
	}

IL_0041:
	{
		G_B9_0 = 0;
	}

IL_0042:
	{
		V_1 = (bool)G_B9_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 174));
		bool L_6 = V_1;
		if (!L_6)
		{
			goto IL_0064;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 175));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 176));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 177));
		String_t* L_7;
		L_7 = Double_ToString_m7499A5D792419537DCB9470A3675CEF5117DE339((&V_0), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 177));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 178));
		String_t* L_8;
		L_8 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(_stringLiteral0A0B5411174908C28B5CCC520D982C817E714C77, L_7, _stringLiteral40EC62B3D080AE498D3DBBDFF3905B41B72B0F05, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 178));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 179));
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9(L_8, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 179));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 180));
	}

IL_0064:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 181));
		return;
	}
}
IL2CPP_EXTERN_C  void VideoClipPlayable_ValidateStartDelayInternal_m444254E07B5760B0A7B7158F36E8039AEAFF9EA5_AdjustorThunk (RuntimeObject* __this, double ___0_startDelay, const RuntimeMethod* method)
{
	VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VideoClipPlayable_t2AAA6B40D35759730C170CA3124BC2499B50A0F1*>(__this + _offset);
	VideoClipPlayable_ValidateStartDelayInternal_m444254E07B5760B0A7B7158F36E8039AEAFF9EA5(_thisAdjusted, ___0_startDelay, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* VideoClipPlayable_GetClipInternal_m27C154E1FA8680A00D6DF3325B802CAD50F18670 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) 
{
	typedef VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* (*VideoClipPlayable_GetClipInternal_m27C154E1FA8680A00D6DF3325B802CAD50F18670_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*);
	static VideoClipPlayable_GetClipInternal_m27C154E1FA8680A00D6DF3325B802CAD50F18670_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_GetClipInternal_m27C154E1FA8680A00D6DF3325B802CAD50F18670_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::GetClipInternal(UnityEngine.Playables.PlayableHandle&)");
	VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* icallRetVal = _il2cpp_icall_func(___0_hdl);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetClipInternal_m0D8D01F0C80FD264660C7D28D78110B14AB8B82B (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___1_clip, const RuntimeMethod* method) 
{
	typedef void (*VideoClipPlayable_SetClipInternal_m0D8D01F0C80FD264660C7D28D78110B14AB8B82B_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClipPlayable_SetClipInternal_m0D8D01F0C80FD264660C7D28D78110B14AB8B82B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_SetClipInternal_m0D8D01F0C80FD264660C7D28D78110B14AB8B82B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::SetClipInternal(UnityEngine.Playables.PlayableHandle&,UnityEngine.Video.VideoClip)");
	_il2cpp_icall_func(___0_hdl, ___1_clip);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_GetLoopedInternal_mC1F05420ADD9902153C8FF2772CC70375214F5CE (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) 
{
	typedef bool (*VideoClipPlayable_GetLoopedInternal_mC1F05420ADD9902153C8FF2772CC70375214F5CE_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*);
	static VideoClipPlayable_GetLoopedInternal_mC1F05420ADD9902153C8FF2772CC70375214F5CE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_GetLoopedInternal_mC1F05420ADD9902153C8FF2772CC70375214F5CE_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::GetLoopedInternal(UnityEngine.Playables.PlayableHandle&)");
	bool icallRetVal = _il2cpp_icall_func(___0_hdl);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetLoopedInternal_m3B18474AA52151CF68687050197E37F425C17128 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, bool ___1_looped, const RuntimeMethod* method) 
{
	typedef void (*VideoClipPlayable_SetLoopedInternal_m3B18474AA52151CF68687050197E37F425C17128_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*, bool);
	static VideoClipPlayable_SetLoopedInternal_m3B18474AA52151CF68687050197E37F425C17128_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_SetLoopedInternal_m3B18474AA52151CF68687050197E37F425C17128_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::SetLoopedInternal(UnityEngine.Playables.PlayableHandle&,System.Boolean)");
	_il2cpp_icall_func(___0_hdl, ___1_looped);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_GetIsPlayingInternal_m3638460D1F41D407B5FC0EE55BE6110148FA3019 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) 
{
	typedef bool (*VideoClipPlayable_GetIsPlayingInternal_m3638460D1F41D407B5FC0EE55BE6110148FA3019_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*);
	static VideoClipPlayable_GetIsPlayingInternal_m3638460D1F41D407B5FC0EE55BE6110148FA3019_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_GetIsPlayingInternal_m3638460D1F41D407B5FC0EE55BE6110148FA3019_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::GetIsPlayingInternal(UnityEngine.Playables.PlayableHandle&)");
	bool icallRetVal = _il2cpp_icall_func(___0_hdl);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClipPlayable_GetStartDelayInternal_mDF534576256379603C942CA855F0848BE48DCE31 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) 
{
	typedef double (*VideoClipPlayable_GetStartDelayInternal_mDF534576256379603C942CA855F0848BE48DCE31_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*);
	static VideoClipPlayable_GetStartDelayInternal_mDF534576256379603C942CA855F0848BE48DCE31_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_GetStartDelayInternal_mDF534576256379603C942CA855F0848BE48DCE31_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::GetStartDelayInternal(UnityEngine.Playables.PlayableHandle&)");
	double icallRetVal = _il2cpp_icall_func(___0_hdl);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetStartDelayInternal_m09C29D756A8824E26F62382CBDD63C91A28C5B27 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, double ___1_delay, const RuntimeMethod* method) 
{
	typedef void (*VideoClipPlayable_SetStartDelayInternal_m09C29D756A8824E26F62382CBDD63C91A28C5B27_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*, double);
	static VideoClipPlayable_SetStartDelayInternal_m09C29D756A8824E26F62382CBDD63C91A28C5B27_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_SetStartDelayInternal_m09C29D756A8824E26F62382CBDD63C91A28C5B27_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::SetStartDelayInternal(UnityEngine.Playables.PlayableHandle&,System.Double)");
	_il2cpp_icall_func(___0_hdl, ___1_delay);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClipPlayable_GetPauseDelayInternal_m97726033B939451F02DC05BBDB954074BA388A1B (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) 
{
	typedef double (*VideoClipPlayable_GetPauseDelayInternal_m97726033B939451F02DC05BBDB954074BA388A1B_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*);
	static VideoClipPlayable_GetPauseDelayInternal_m97726033B939451F02DC05BBDB954074BA388A1B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_GetPauseDelayInternal_m97726033B939451F02DC05BBDB954074BA388A1B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::GetPauseDelayInternal(UnityEngine.Playables.PlayableHandle&)");
	double icallRetVal = _il2cpp_icall_func(___0_hdl);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClipPlayable_SetPauseDelayInternal_m639E25CD82711C6C59C52EAC2D11C08E7C4401D1 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, double ___1_delay, const RuntimeMethod* method) 
{
	typedef void (*VideoClipPlayable_SetPauseDelayInternal_m639E25CD82711C6C59C52EAC2D11C08E7C4401D1_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*, double);
	static VideoClipPlayable_SetPauseDelayInternal_m639E25CD82711C6C59C52EAC2D11C08E7C4401D1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_SetPauseDelayInternal_m639E25CD82711C6C59C52EAC2D11C08E7C4401D1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::SetPauseDelayInternal(UnityEngine.Playables.PlayableHandle&,System.Double)");
	_il2cpp_icall_func(___0_hdl, ___1_delay);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_InternalCreateVideoClipPlayable_mC6E8234F6F3FD32AE36873EFA672D2801A97C2A6 (PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E* ___0_graph, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___1_clip, bool ___2_looping, PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___3_handle, const RuntimeMethod* method) 
{
	typedef bool (*VideoClipPlayable_InternalCreateVideoClipPlayable_mC6E8234F6F3FD32AE36873EFA672D2801A97C2A6_ftn) (PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E*, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*, bool, PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*);
	static VideoClipPlayable_InternalCreateVideoClipPlayable_mC6E8234F6F3FD32AE36873EFA672D2801A97C2A6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_InternalCreateVideoClipPlayable_mC6E8234F6F3FD32AE36873EFA672D2801A97C2A6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::InternalCreateVideoClipPlayable(UnityEngine.Playables.PlayableGraph&,UnityEngine.Video.VideoClip,System.Boolean,UnityEngine.Playables.PlayableHandle&)");
	bool icallRetVal = _il2cpp_icall_func(___0_graph, ___1_clip, ___2_looping, ___3_handle);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClipPlayable_ValidateType_m257838C6EDD2B2AF16AE84EC9F005F20CE910B39 (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4* ___0_hdl, const RuntimeMethod* method) 
{
	typedef bool (*VideoClipPlayable_ValidateType_m257838C6EDD2B2AF16AE84EC9F005F20CE910B39_ftn) (PlayableHandle_t5D6A01EF94382EFEDC047202F71DF882769654D4*);
	static VideoClipPlayable_ValidateType_m257838C6EDD2B2AF16AE84EC9F005F20CE910B39_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClipPlayable_ValidateType_m257838C6EDD2B2AF16AE84EC9F005F20CE910B39_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoClipPlayable::ValidateType(UnityEngine.Playables.PlayableHandle&)");
	bool icallRetVal = _il2cpp_icall_func(___0_hdl);
	return icallRetVal;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* VideoPlayerExtensions_GetAudioSampleProvider_mA27A786A57E95EDB73E1642EB5F1A912BBADF382 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_vp, uint16_t ___1_trackIndex, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoAudioOutputMode_tFDC43E947CD8D421DD2FD53A836A8D72F6AED874_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayerExtensions_GetAudioSampleProvider_mA27A786A57E95EDB73E1642EB5F1A912BBADF382_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	uint16_t V_0 = 0;
	int32_t V_1 = 0;
	AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* V_2 = NULL;
	bool V_3 = false;
	bool V_4 = false;
	bool V_5 = false;
	bool V_6 = false;
	bool V_7 = false;
	uint16_t V_8 = 0;
	AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* V_9 = NULL;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_vp), (&___1_trackIndex));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0), (&V_1), (&V_2));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayerExtensions_GetAudioSampleProvider_mA27A786A57E95EDB73E1642EB5F1A912BBADF382_RuntimeMethod_var, NULL, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 182));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 183));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 184));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 185));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_0 = ___0_vp;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 186));
		NullCheck(L_0);
		uint16_t L_1;
		L_1 = VideoPlayer_get_controlledAudioTrackCount_m7D0887B6AFEBCC16925024A63D0D25E4B2060448(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 186));
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 187));
		uint16_t L_2 = ___1_trackIndex;
		uint16_t L_3 = V_0;
		V_3 = (bool)((((int32_t)((((int32_t)L_2) < ((int32_t)L_3))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 188));
		bool L_4 = V_3;
		if (!L_4)
		{
			goto IL_003a;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 189));
		uint16_t L_5 = ___1_trackIndex;
		uint16_t L_6 = L_5;
		RuntimeObject* L_7 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_il2cpp_TypeInfo_var)), &L_6);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 190));
		String_t* L_8;
		L_8 = UInt16_ToString_m57629B7E74D92A54414073D5C27D6827C93A4DD5((&V_0), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 190));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 191));
		String_t* L_9;
		L_9 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral4CE89C314A31478981DC58A93CFEF99E409C4C21)), L_8, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral6216AB66F68B9B8A0707687070449261B6454D7B)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 191));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 192));
		ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* L_10 = (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var)));
		ArgumentOutOfRangeException__ctor_m60B543A63AC8692C28096003FBF2AD124B9D5B85(L_10, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral573C0D2F167EB38E04F3195A6EE0598846E07DA2)), L_7, L_9, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 192));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_10, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayerExtensions_GetAudioSampleProvider_mA27A786A57E95EDB73E1642EB5F1A912BBADF382_RuntimeMethod_var)));
	}

IL_003a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 193));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_11 = ___0_vp;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 194));
		NullCheck(L_11);
		int32_t L_12;
		L_12 = VideoPlayer_get_audioOutputMode_m22644BE075B145626BD48015E5948CAF4486B60E(L_11, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 194));
		V_1 = L_12;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 195));
		int32_t L_13 = V_1;
		V_4 = (bool)((((int32_t)((((int32_t)L_13) == ((int32_t)3))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 196));
		bool L_14 = V_4;
		if (!L_14)
		{
			goto IL_006b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 197));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 198));
		Il2CppFakeBox<int32_t> L_15(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoAudioOutputMode_tFDC43E947CD8D421DD2FD53A836A8D72F6AED874_il2cpp_TypeInfo_var)), (&V_1));
		String_t* L_16;
		L_16 = Enum_ToString_m946B0B83C4470457D0FF555D862022C72BB55741((Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2*)(&L_15), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 198));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 199));
		String_t* L_17;
		L_17 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD8752400A3C2D30011617DFBC0AE2509DCA5885D)), L_16, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 199));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 200));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_18 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_18, L_17, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 200));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_18, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayerExtensions_GetAudioSampleProvider_mA27A786A57E95EDB73E1642EB5F1A912BBADF382_RuntimeMethod_var)));
	}

IL_006b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 201));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_19 = ___0_vp;
		uint16_t L_20 = ___1_trackIndex;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 202));
		uint32_t L_21;
		L_21 = VideoPlayerExtensions_InternalGetAudioSampleProviderId_mE321D812009B94D0F1E90637E4F06070FF49155E(L_19, L_20, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 202));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_22 = ___0_vp;
		uint16_t L_23 = ___1_trackIndex;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 203));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_24;
		L_24 = AudioSampleProvider_Lookup_mAC9B381D61BBC6504C62B6956FD0CE7BEA989BF7(L_21, L_22, L_23, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 203));
		V_2 = L_24;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 204));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_25 = V_2;
		V_5 = (bool)((((RuntimeObject*)(AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2*)L_25) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 205));
		bool L_26 = V_5;
		if (!L_26)
		{
			goto IL_008f;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 206));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 207));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_27 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_27, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral67B5383F0EB49A2F8F216C4A65CE0939F33EC2EE)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 207));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_27, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayerExtensions_GetAudioSampleProvider_mA27A786A57E95EDB73E1642EB5F1A912BBADF382_RuntimeMethod_var)));
	}

IL_008f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 208));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_28 = V_2;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 209));
		NullCheck(L_28);
		Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* L_29;
		L_29 = AudioSampleProvider_get_owner_mC8461FD790AEC7BACE00C8A3F1DE4D00E08EF1AC_inline(L_28, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 209));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_30 = ___0_vp;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 210));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_31;
		L_31 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_29, L_30, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 210));
		V_6 = L_31;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 211));
		bool L_32 = V_6;
		if (!L_32)
		{
			goto IL_00ac;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 212));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 213));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_33 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_33, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB3DB1462630FDD5731404C559A3B2CCF742276C2)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 213));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_33, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayerExtensions_GetAudioSampleProvider_mA27A786A57E95EDB73E1642EB5F1A912BBADF382_RuntimeMethod_var)));
	}

IL_00ac:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 214));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_34 = V_2;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 215));
		NullCheck(L_34);
		uint16_t L_35;
		L_35 = AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6_inline(L_34, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 215));
		uint16_t L_36 = ___1_trackIndex;
		V_7 = (bool)((((int32_t)((((int32_t)L_35) == ((int32_t)L_36))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 216));
		bool L_37 = V_7;
		if (!L_37)
		{
			goto IL_00e9;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 217));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_38 = V_2;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 218));
		NullCheck(L_38);
		uint16_t L_39;
		L_39 = AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6_inline(L_38, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 218));
		V_8 = L_39;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 219));
		String_t* L_40;
		L_40 = UInt16_ToString_m57629B7E74D92A54414073D5C27D6827C93A4DD5((&V_8), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 219));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 220));
		String_t* L_41;
		L_41 = UInt16_ToString_m57629B7E74D92A54414073D5C27D6827C93A4DD5((&___1_trackIndex), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 220));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 221));
		String_t* L_42;
		L_42 = String_Concat_m093934F71A9B351911EE46311674ED463B180006(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral6B0259E30386CD045B834B84467B7811AF926D5B)), L_40, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0C2C7A9E7436DA4E0013C1BB4B0A6143CE0F38ED)), L_41, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 221));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 222));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_43 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_43, L_42, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 222));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_43, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayerExtensions_GetAudioSampleProvider_mA27A786A57E95EDB73E1642EB5F1A912BBADF382_RuntimeMethod_var)));
	}

IL_00e9:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 223));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_44 = V_2;
		V_9 = L_44;
		goto IL_00ee;
	}

IL_00ee:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 224));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_45 = V_9;
		return L_45;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayerExtensions_InternalGetAudioSampleProviderId_mE321D812009B94D0F1E90637E4F06070FF49155E (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_vp, uint16_t ___1_trackIndex, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayerExtensions_InternalGetAudioSampleProviderId_mE321D812009B94D0F1E90637E4F06070FF49155E_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t);
	static VideoPlayerExtensions_InternalGetAudioSampleProviderId_mE321D812009B94D0F1E90637E4F06070FF49155E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayerExtensions_InternalGetAudioSampleProviderId_mE321D812009B94D0F1E90637E4F06070FF49155E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Experimental.Video.VideoPlayerExtensions::InternalGetAudioSampleProviderId(UnityEngine.Video.VideoPlayer,System.UInt16)");
	uint32_t icallRetVal = _il2cpp_icall_func(___0_vp, ___1_trackIndex);
	return icallRetVal;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoClip__ctor_m0D5152E4073E1D970B8A947BC16309696AAF084A (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClip__ctor_m0D5152E4073E1D970B8A947BC16309696AAF084A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoClip__ctor_m0D5152E4073E1D970B8A947BC16309696AAF084A_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 225));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 226));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 227));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 228));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object__ctor_m2149FA40CEC8D82AC20D3508AB40C0D8EFEF68E6(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 228));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 229));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 230));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* VideoClip_get_originalPath_mD6216825039DFE12FB0849259B4FE4C5B83DCA03 (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef String_t* (*VideoClip_get_originalPath_mD6216825039DFE12FB0849259B4FE4C5B83DCA03_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_originalPath_mD6216825039DFE12FB0849259B4FE4C5B83DCA03_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_originalPath_mD6216825039DFE12FB0849259B4FE4C5B83DCA03_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_originalPath()");
	String_t* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t VideoClip_get_frameCount_m53E5E8AFB0F9BC2F1E0D0DED42DE4FBAE755FEC8 (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef uint64_t (*VideoClip_get_frameCount_m53E5E8AFB0F9BC2F1E0D0DED42DE4FBAE755FEC8_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_frameCount_m53E5E8AFB0F9BC2F1E0D0DED42DE4FBAE755FEC8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_frameCount_m53E5E8AFB0F9BC2F1E0D0DED42DE4FBAE755FEC8_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_frameCount()");
	uint64_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClip_get_frameRate_m62DBC3E4000A1E8E90F9B6DA3083A567B2F706E2 (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef double (*VideoClip_get_frameRate_m62DBC3E4000A1E8E90F9B6DA3083A567B2F706E2_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_frameRate_m62DBC3E4000A1E8E90F9B6DA3083A567B2F706E2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_frameRate_m62DBC3E4000A1E8E90F9B6DA3083A567B2F706E2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_frameRate()");
	double icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoClip_get_length_m3A5628F24610F06D4F1E7FBC700722B77942121D (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef double (*VideoClip_get_length_m3A5628F24610F06D4F1E7FBC700722B77942121D_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_length_m3A5628F24610F06D4F1E7FBC700722B77942121D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_length_m3A5628F24610F06D4F1E7FBC700722B77942121D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_length()");
	double icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoClip_get_width_mC4F4AB7B71F65B0086F2DD9BFFA6789B50A94DAD (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoClip_get_width_mC4F4AB7B71F65B0086F2DD9BFFA6789B50A94DAD_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_width_mC4F4AB7B71F65B0086F2DD9BFFA6789B50A94DAD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_width_mC4F4AB7B71F65B0086F2DD9BFFA6789B50A94DAD_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_width()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoClip_get_height_m425279B19CB8EB25DD480DC888DFB7B7108788B4 (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoClip_get_height_m425279B19CB8EB25DD480DC888DFB7B7108788B4_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_height_m425279B19CB8EB25DD480DC888DFB7B7108788B4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_height_m425279B19CB8EB25DD480DC888DFB7B7108788B4_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_height()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoClip_get_pixelAspectRatioNumerator_m24C872B9B515CB99C74C75BEDAFE1CF6B212B328 (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoClip_get_pixelAspectRatioNumerator_m24C872B9B515CB99C74C75BEDAFE1CF6B212B328_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_pixelAspectRatioNumerator_m24C872B9B515CB99C74C75BEDAFE1CF6B212B328_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_pixelAspectRatioNumerator_m24C872B9B515CB99C74C75BEDAFE1CF6B212B328_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_pixelAspectRatioNumerator()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoClip_get_pixelAspectRatioDenominator_m35B1E4367C1DAF7CA687EF65754A82EE834B4D37 (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoClip_get_pixelAspectRatioDenominator_m35B1E4367C1DAF7CA687EF65754A82EE834B4D37_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_pixelAspectRatioDenominator_m35B1E4367C1DAF7CA687EF65754A82EE834B4D37_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_pixelAspectRatioDenominator_m35B1E4367C1DAF7CA687EF65754A82EE834B4D37_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_pixelAspectRatioDenominator()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoClip_get_sRGB_m35B67292E328C2F5AE005F8752BA327E90B487C6 (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoClip_get_sRGB_m35B67292E328C2F5AE005F8752BA327E90B487C6_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_sRGB_m35B67292E328C2F5AE005F8752BA327E90B487C6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_sRGB_m35B67292E328C2F5AE005F8752BA327E90B487C6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_sRGB()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoClip_get_audioTrackCount_m82FDC10DC7CD36046EF0B6098DE29B375F85A39B (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, const RuntimeMethod* method) 
{
	typedef uint16_t (*VideoClip_get_audioTrackCount_m82FDC10DC7CD36046EF0B6098DE29B375F85A39B_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoClip_get_audioTrackCount_m82FDC10DC7CD36046EF0B6098DE29B375F85A39B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_get_audioTrackCount_m82FDC10DC7CD36046EF0B6098DE29B375F85A39B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::get_audioTrackCount()");
	uint16_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoClip_GetAudioChannelCount_mE4ABD3522FCE088861E40DBF483D90BC0C85532A (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, uint16_t ___0_audioTrackIdx, const RuntimeMethod* method) 
{
	typedef uint16_t (*VideoClip_GetAudioChannelCount_mE4ABD3522FCE088861E40DBF483D90BC0C85532A_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*, uint16_t);
	static VideoClip_GetAudioChannelCount_mE4ABD3522FCE088861E40DBF483D90BC0C85532A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_GetAudioChannelCount_mE4ABD3522FCE088861E40DBF483D90BC0C85532A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::GetAudioChannelCount(System.UInt16)");
	uint16_t icallRetVal = _il2cpp_icall_func(__this, ___0_audioTrackIdx);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoClip_GetAudioSampleRate_m164193FDC70486F033AB74EEC9C790C8B581A796 (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, uint16_t ___0_audioTrackIdx, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoClip_GetAudioSampleRate_m164193FDC70486F033AB74EEC9C790C8B581A796_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*, uint16_t);
	static VideoClip_GetAudioSampleRate_m164193FDC70486F033AB74EEC9C790C8B581A796_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_GetAudioSampleRate_m164193FDC70486F033AB74EEC9C790C8B581A796_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::GetAudioSampleRate(System.UInt16)");
	uint32_t icallRetVal = _il2cpp_icall_func(__this, ___0_audioTrackIdx);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* VideoClip_GetAudioLanguage_m76B57EDDFD5428A5595A5AAA102800B8888D40DD (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* __this, uint16_t ___0_audioTrackIdx, const RuntimeMethod* method) 
{
	typedef String_t* (*VideoClip_GetAudioLanguage_m76B57EDDFD5428A5595A5AAA102800B8888D40DD_ftn) (VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*, uint16_t);
	static VideoClip_GetAudioLanguage_m76B57EDDFD5428A5595A5AAA102800B8888D40DD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoClip_GetAudioLanguage_m76B57EDDFD5428A5595A5AAA102800B8888D40DD_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoClip::GetAudioLanguage(System.UInt16)");
	String_t* icallRetVal = _il2cpp_icall_func(__this, ___0_audioTrackIdx);
	return icallRetVal;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayer_get_source_m5FE349AB354EC3D72E5BBA8549D53AAEBB42C444 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayer_get_source_m5FE349AB354EC3D72E5BBA8549D53AAEBB42C444_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_source_m5FE349AB354EC3D72E5BBA8549D53AAEBB42C444_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_source_m5FE349AB354EC3D72E5BBA8549D53AAEBB42C444_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_source()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_source_m31B8A16AF9B6FE58D5DCF549614BE6B53DE53052 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_source_m31B8A16AF9B6FE58D5DCF549614BE6B53DE53052_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int32_t);
	static VideoPlayer_set_source_m31B8A16AF9B6FE58D5DCF549614BE6B53DE53052_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_source_m31B8A16AF9B6FE58D5DCF549614BE6B53DE53052_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_source(UnityEngine.Video.VideoSource)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayer_get_timeUpdateMode_m9F3772B82C02ED25F146EAE0C55C5D6819C8C758 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayer_get_timeUpdateMode_m9F3772B82C02ED25F146EAE0C55C5D6819C8C758_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_timeUpdateMode_m9F3772B82C02ED25F146EAE0C55C5D6819C8C758_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_timeUpdateMode_m9F3772B82C02ED25F146EAE0C55C5D6819C8C758_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_timeUpdateMode()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_timeUpdateMode_mF0E47114848B4AA565E45FA235E926C4209E8F9A (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_timeUpdateMode_mF0E47114848B4AA565E45FA235E926C4209E8F9A_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int32_t);
	static VideoPlayer_set_timeUpdateMode_mF0E47114848B4AA565E45FA235E926C4209E8F9A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_timeUpdateMode_mF0E47114848B4AA565E45FA235E926C4209E8F9A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_timeUpdateMode(UnityEngine.Video.VideoTimeUpdateMode)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* VideoPlayer_get_url_mEE83E6049A25789C1B5678B20D9B7400270A1E40 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef String_t* (*VideoPlayer_get_url_mEE83E6049A25789C1B5678B20D9B7400270A1E40_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_url_mEE83E6049A25789C1B5678B20D9B7400270A1E40_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_url_mEE83E6049A25789C1B5678B20D9B7400270A1E40_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_url()");
	String_t* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_url_m02AC2BB229F9441257D62F9ACFD96D09F51B4F14 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_url_m02AC2BB229F9441257D62F9ACFD96D09F51B4F14_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, String_t*);
	static VideoPlayer_set_url_m02AC2BB229F9441257D62F9ACFD96D09F51B4F14_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_url_m02AC2BB229F9441257D62F9ACFD96D09F51B4F14_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_url(System.String)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* VideoPlayer_get_clip_m08BCE17C4E685C87151B418AA3CE88294F6A96E9 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* (*VideoPlayer_get_clip_m08BCE17C4E685C87151B418AA3CE88294F6A96E9_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_clip_m08BCE17C4E685C87151B418AA3CE88294F6A96E9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_clip_m08BCE17C4E685C87151B418AA3CE88294F6A96E9_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_clip()");
	VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_clip_m85C09C15E184E9AAE117C1A534D8AC25175C8DC9 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_clip_m85C09C15E184E9AAE117C1A534D8AC25175C8DC9_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, VideoClip_t603AD809C228739D932819710E70326FE7A9ADDE*);
	static VideoPlayer_set_clip_m85C09C15E184E9AAE117C1A534D8AC25175C8DC9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_clip_m85C09C15E184E9AAE117C1A534D8AC25175C8DC9_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_clip(UnityEngine.Video.VideoClip)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayer_get_renderMode_m0B65B5B6D1ED8F0F00DA0725F8E97DCD8FE75C6E (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayer_get_renderMode_m0B65B5B6D1ED8F0F00DA0725F8E97DCD8FE75C6E_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_renderMode_m0B65B5B6D1ED8F0F00DA0725F8E97DCD8FE75C6E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_renderMode_m0B65B5B6D1ED8F0F00DA0725F8E97DCD8FE75C6E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_renderMode()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_renderMode_m22880E02710AF2C055E27CDE2C96EAC8BA4CEFC0 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_renderMode_m22880E02710AF2C055E27CDE2C96EAC8BA4CEFC0_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int32_t);
	static VideoPlayer_set_renderMode_m22880E02710AF2C055E27CDE2C96EAC8BA4CEFC0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_renderMode_m22880E02710AF2C055E27CDE2C96EAC8BA4CEFC0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_renderMode(UnityEngine.Video.VideoRenderMode)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_canSetTimeUpdateMode_m41EC76AF8ED9B623F1A46582B8FA1F79B53223D7 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_canSetTimeUpdateMode_m41EC76AF8ED9B623F1A46582B8FA1F79B53223D7_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_canSetTimeUpdateMode_m41EC76AF8ED9B623F1A46582B8FA1F79B53223D7_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_canSetTimeUpdateMode_m41EC76AF8ED9B623F1A46582B8FA1F79B53223D7_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_canSetTimeUpdateMode()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* VideoPlayer_get_targetCamera_m1E660EDFC75020508245D84F7959C68DE7E0B70F (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* (*VideoPlayer_get_targetCamera_m1E660EDFC75020508245D84F7959C68DE7E0B70F_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_targetCamera_m1E660EDFC75020508245D84F7959C68DE7E0B70F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_targetCamera_m1E660EDFC75020508245D84F7959C68DE7E0B70F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_targetCamera()");
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_targetCamera_m0B4C68A63EC585A69A87F9EA26DBD5FEF92E3A79 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_targetCamera_m0B4C68A63EC585A69A87F9EA26DBD5FEF92E3A79_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184*);
	static VideoPlayer_set_targetCamera_m0B4C68A63EC585A69A87F9EA26DBD5FEF92E3A79_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_targetCamera_m0B4C68A63EC585A69A87F9EA26DBD5FEF92E3A79_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_targetCamera(UnityEngine.Camera)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* VideoPlayer_get_targetTexture_m1022F791F3DD8FB6BE47E73C2DB25A5B4E768F8D (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* (*VideoPlayer_get_targetTexture_m1022F791F3DD8FB6BE47E73C2DB25A5B4E768F8D_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_targetTexture_m1022F791F3DD8FB6BE47E73C2DB25A5B4E768F8D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_targetTexture_m1022F791F3DD8FB6BE47E73C2DB25A5B4E768F8D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_targetTexture()");
	RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_targetTexture_mCFEE8D0342A11CD20CF51A3C467BA745BEB49E02 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_targetTexture_mCFEE8D0342A11CD20CF51A3C467BA745BEB49E02_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27*);
	static VideoPlayer_set_targetTexture_mCFEE8D0342A11CD20CF51A3C467BA745BEB49E02_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_targetTexture_mCFEE8D0342A11CD20CF51A3C467BA745BEB49E02_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_targetTexture(UnityEngine.RenderTexture)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* VideoPlayer_get_targetMaterialRenderer_m58038137DCFFC358F3CA138F0B9BA6CD5A18642E (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* (*VideoPlayer_get_targetMaterialRenderer_m58038137DCFFC358F3CA138F0B9BA6CD5A18642E_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_targetMaterialRenderer_m58038137DCFFC358F3CA138F0B9BA6CD5A18642E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_targetMaterialRenderer_m58038137DCFFC358F3CA138F0B9BA6CD5A18642E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_targetMaterialRenderer()");
	Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_targetMaterialRenderer_m3616C05D50241F4B5EDBAA55A588A75460801695 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_targetMaterialRenderer_m3616C05D50241F4B5EDBAA55A588A75460801695_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF*);
	static VideoPlayer_set_targetMaterialRenderer_m3616C05D50241F4B5EDBAA55A588A75460801695_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_targetMaterialRenderer_m3616C05D50241F4B5EDBAA55A588A75460801695_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_targetMaterialRenderer(UnityEngine.Renderer)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* VideoPlayer_get_targetMaterialProperty_m0F6B6E42E1C37F76136A149FA233A6E9679F2114 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef String_t* (*VideoPlayer_get_targetMaterialProperty_m0F6B6E42E1C37F76136A149FA233A6E9679F2114_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_targetMaterialProperty_m0F6B6E42E1C37F76136A149FA233A6E9679F2114_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_targetMaterialProperty_m0F6B6E42E1C37F76136A149FA233A6E9679F2114_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_targetMaterialProperty()");
	String_t* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_targetMaterialProperty_m6BB3A64DC8CEDA697FA8A641729FF23EBC76E879 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_targetMaterialProperty_m6BB3A64DC8CEDA697FA8A641729FF23EBC76E879_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, String_t*);
	static VideoPlayer_set_targetMaterialProperty_m6BB3A64DC8CEDA697FA8A641729FF23EBC76E879_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_targetMaterialProperty_m6BB3A64DC8CEDA697FA8A641729FF23EBC76E879_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_targetMaterialProperty(System.String)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayer_get_aspectRatio_mD9F49BCD1476FE992BDADCB241EF69562F65A249 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayer_get_aspectRatio_mD9F49BCD1476FE992BDADCB241EF69562F65A249_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_aspectRatio_mD9F49BCD1476FE992BDADCB241EF69562F65A249_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_aspectRatio_mD9F49BCD1476FE992BDADCB241EF69562F65A249_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_aspectRatio()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_aspectRatio_m4EDDA786C5B90040119F15FB0AC9150321B44AAE (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_aspectRatio_m4EDDA786C5B90040119F15FB0AC9150321B44AAE_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int32_t);
	static VideoPlayer_set_aspectRatio_m4EDDA786C5B90040119F15FB0AC9150321B44AAE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_aspectRatio_m4EDDA786C5B90040119F15FB0AC9150321B44AAE_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_aspectRatio(UnityEngine.Video.VideoAspectRatio)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VideoPlayer_get_targetCameraAlpha_m9B70157D47A032E38EFED765969E79CE40B61B71 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef float (*VideoPlayer_get_targetCameraAlpha_m9B70157D47A032E38EFED765969E79CE40B61B71_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_targetCameraAlpha_m9B70157D47A032E38EFED765969E79CE40B61B71_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_targetCameraAlpha_m9B70157D47A032E38EFED765969E79CE40B61B71_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_targetCameraAlpha()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_targetCameraAlpha_m1A2D5501FDFF2C7328573D31BE39EA67CA1D2491 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_targetCameraAlpha_m1A2D5501FDFF2C7328573D31BE39EA67CA1D2491_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, float);
	static VideoPlayer_set_targetCameraAlpha_m1A2D5501FDFF2C7328573D31BE39EA67CA1D2491_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_targetCameraAlpha_m1A2D5501FDFF2C7328573D31BE39EA67CA1D2491_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_targetCameraAlpha(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayer_get_targetCamera3DLayout_mF4E6DFCF1E9E461E5BFF9A19BB2589333F06A1BA (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayer_get_targetCamera3DLayout_mF4E6DFCF1E9E461E5BFF9A19BB2589333F06A1BA_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_targetCamera3DLayout_mF4E6DFCF1E9E461E5BFF9A19BB2589333F06A1BA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_targetCamera3DLayout_mF4E6DFCF1E9E461E5BFF9A19BB2589333F06A1BA_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_targetCamera3DLayout()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_targetCamera3DLayout_m4D61477F3F20F98AC1E72A66A511D7C5C2E88B10 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_targetCamera3DLayout_m4D61477F3F20F98AC1E72A66A511D7C5C2E88B10_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int32_t);
	static VideoPlayer_set_targetCamera3DLayout_m4D61477F3F20F98AC1E72A66A511D7C5C2E88B10_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_targetCamera3DLayout_m4D61477F3F20F98AC1E72A66A511D7C5C2E88B10_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_targetCamera3DLayout(UnityEngine.Video.Video3DLayout)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* VideoPlayer_get_texture_m1BA7033FEB30FEC507A4605FDD5C4B78BB81C379 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* (*VideoPlayer_get_texture_m1BA7033FEB30FEC507A4605FDD5C4B78BB81C379_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_texture_m1BA7033FEB30FEC507A4605FDD5C4B78BB81C379_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_texture_m1BA7033FEB30FEC507A4605FDD5C4B78BB81C379_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_texture()");
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_Prepare_mC0EF4CC2E5A6B7C04F9F810DD9406A6146BEE6EA (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_Prepare_mC0EF4CC2E5A6B7C04F9F810DD9406A6146BEE6EA_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_Prepare_mC0EF4CC2E5A6B7C04F9F810DD9406A6146BEE6EA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_Prepare_mC0EF4CC2E5A6B7C04F9F810DD9406A6146BEE6EA_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::Prepare()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_isPrepared_mF2F3AD27BE0FD06695EE9DEE762B68F431E15767 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_isPrepared_mF2F3AD27BE0FD06695EE9DEE762B68F431E15767_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_isPrepared_mF2F3AD27BE0FD06695EE9DEE762B68F431E15767_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_isPrepared_mF2F3AD27BE0FD06695EE9DEE762B68F431E15767_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_isPrepared()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_waitForFirstFrame_m140A0691E2689A37B167B8C47A5A9EBBA38B711B (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_waitForFirstFrame_m140A0691E2689A37B167B8C47A5A9EBBA38B711B_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_waitForFirstFrame_m140A0691E2689A37B167B8C47A5A9EBBA38B711B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_waitForFirstFrame_m140A0691E2689A37B167B8C47A5A9EBBA38B711B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_waitForFirstFrame()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_waitForFirstFrame_m43D97D5C3971060018A9BF5D04B9CEDDBA1056AA (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_waitForFirstFrame_m43D97D5C3971060018A9BF5D04B9CEDDBA1056AA_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, bool);
	static VideoPlayer_set_waitForFirstFrame_m43D97D5C3971060018A9BF5D04B9CEDDBA1056AA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_waitForFirstFrame_m43D97D5C3971060018A9BF5D04B9CEDDBA1056AA_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_waitForFirstFrame(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_playOnAwake_m0BB4406F24AE9E15C9C8400D9732D80642213EC3 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_playOnAwake_m0BB4406F24AE9E15C9C8400D9732D80642213EC3_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_playOnAwake_m0BB4406F24AE9E15C9C8400D9732D80642213EC3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_playOnAwake_m0BB4406F24AE9E15C9C8400D9732D80642213EC3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_playOnAwake()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_playOnAwake_m658E574E0109125AD1754D61FE81DD7BB02411A0 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_playOnAwake_m658E574E0109125AD1754D61FE81DD7BB02411A0_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, bool);
	static VideoPlayer_set_playOnAwake_m658E574E0109125AD1754D61FE81DD7BB02411A0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_playOnAwake_m658E574E0109125AD1754D61FE81DD7BB02411A0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_playOnAwake(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_Play_m8E99607F39F1D214BDD704F3E130DD2418787ACE (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_Play_m8E99607F39F1D214BDD704F3E130DD2418787ACE_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_Play_m8E99607F39F1D214BDD704F3E130DD2418787ACE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_Play_m8E99607F39F1D214BDD704F3E130DD2418787ACE_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::Play()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_Pause_m1975BF6BEE27064602EEB8191C963888CF5B226E (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_Pause_m1975BF6BEE27064602EEB8191C963888CF5B226E_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_Pause_m1975BF6BEE27064602EEB8191C963888CF5B226E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_Pause_m1975BF6BEE27064602EEB8191C963888CF5B226E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::Pause()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_Stop_m1F2DE73183E042998140A56FF69432A200430BF1 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_Stop_m1F2DE73183E042998140A56FF69432A200430BF1_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_Stop_m1F2DE73183E042998140A56FF69432A200430BF1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_Stop_m1F2DE73183E042998140A56FF69432A200430BF1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::Stop()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_isPlaying_mC4CCA87F909A3A14209D632D16D7C8FC34F01520 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_isPlaying_mC4CCA87F909A3A14209D632D16D7C8FC34F01520_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_isPlaying_mC4CCA87F909A3A14209D632D16D7C8FC34F01520_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_isPlaying_mC4CCA87F909A3A14209D632D16D7C8FC34F01520_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_isPlaying()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_isPaused_m249D087974D905D874171AE89664A06F70AC9426 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_isPaused_m249D087974D905D874171AE89664A06F70AC9426_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_isPaused_m249D087974D905D874171AE89664A06F70AC9426_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_isPaused_m249D087974D905D874171AE89664A06F70AC9426_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_isPaused()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_canSetTime_m93AFBD45BF1379FCC1703FFC0A0A5F3413B13D62 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_canSetTime_m93AFBD45BF1379FCC1703FFC0A0A5F3413B13D62_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_canSetTime_m93AFBD45BF1379FCC1703FFC0A0A5F3413B13D62_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_canSetTime_m93AFBD45BF1379FCC1703FFC0A0A5F3413B13D62_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_canSetTime()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoPlayer_get_time_mEDEA9BEF22122704F3EC6F4F521A9F2DC4C0CF48 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef double (*VideoPlayer_get_time_mEDEA9BEF22122704F3EC6F4F521A9F2DC4C0CF48_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_time_mEDEA9BEF22122704F3EC6F4F521A9F2DC4C0CF48_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_time_mEDEA9BEF22122704F3EC6F4F521A9F2DC4C0CF48_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_time()");
	double icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_time_m011A5EC42BF4C5DE301346942998F2E889DB9056 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, double ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_time_m011A5EC42BF4C5DE301346942998F2E889DB9056_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, double);
	static VideoPlayer_set_time_m011A5EC42BF4C5DE301346942998F2E889DB9056_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_time_m011A5EC42BF4C5DE301346942998F2E889DB9056_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_time(System.Double)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t VideoPlayer_get_frame_m57EC801B265F40B3D4AB73BE1F1C42EB6B77988A (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef int64_t (*VideoPlayer_get_frame_m57EC801B265F40B3D4AB73BE1F1C42EB6B77988A_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_frame_m57EC801B265F40B3D4AB73BE1F1C42EB6B77988A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_frame_m57EC801B265F40B3D4AB73BE1F1C42EB6B77988A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_frame()");
	int64_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_frame_mDD1CBF9BD07C3720DC8B1798CE434BAD5B75803B (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, int64_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_frame_mDD1CBF9BD07C3720DC8B1798CE434BAD5B75803B_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int64_t);
	static VideoPlayer_set_frame_mDD1CBF9BD07C3720DC8B1798CE434BAD5B75803B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_frame_mDD1CBF9BD07C3720DC8B1798CE434BAD5B75803B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_frame(System.Int64)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoPlayer_get_clockTime_m3FBFBD787D3A0E0F8BEF9F893D90260313DFD83A (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef double (*VideoPlayer_get_clockTime_m3FBFBD787D3A0E0F8BEF9F893D90260313DFD83A_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_clockTime_m3FBFBD787D3A0E0F8BEF9F893D90260313DFD83A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_clockTime_m3FBFBD787D3A0E0F8BEF9F893D90260313DFD83A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_clockTime()");
	double icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_canStep_m0CD17D951D28FBB3EAB2D746ABCE057F8AA76124 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_canStep_m0CD17D951D28FBB3EAB2D746ABCE057F8AA76124_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_canStep_m0CD17D951D28FBB3EAB2D746ABCE057F8AA76124_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_canStep_m0CD17D951D28FBB3EAB2D746ABCE057F8AA76124_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_canStep()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_StepForward_m33AD97E2C078C7BF769C73BA2432DBD05EE2EA1D (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_StepForward_m33AD97E2C078C7BF769C73BA2432DBD05EE2EA1D_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_StepForward_m33AD97E2C078C7BF769C73BA2432DBD05EE2EA1D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_StepForward_m33AD97E2C078C7BF769C73BA2432DBD05EE2EA1D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::StepForward()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_canSetPlaybackSpeed_m1AE1749D4C49C49684106039D46C8460835F2A53 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_canSetPlaybackSpeed_m1AE1749D4C49C49684106039D46C8460835F2A53_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_canSetPlaybackSpeed_m1AE1749D4C49C49684106039D46C8460835F2A53_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_canSetPlaybackSpeed_m1AE1749D4C49C49684106039D46C8460835F2A53_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_canSetPlaybackSpeed()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VideoPlayer_get_playbackSpeed_mCCEAB0BF39D7B9ACC17A67C376E8AFEE4AAB54CB (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef float (*VideoPlayer_get_playbackSpeed_mCCEAB0BF39D7B9ACC17A67C376E8AFEE4AAB54CB_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_playbackSpeed_mCCEAB0BF39D7B9ACC17A67C376E8AFEE4AAB54CB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_playbackSpeed_mCCEAB0BF39D7B9ACC17A67C376E8AFEE4AAB54CB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_playbackSpeed()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_playbackSpeed_mD57C135A94C988D209390B879D84464A13CB91FD (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_playbackSpeed_mD57C135A94C988D209390B879D84464A13CB91FD_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, float);
	static VideoPlayer_set_playbackSpeed_mD57C135A94C988D209390B879D84464A13CB91FD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_playbackSpeed_mD57C135A94C988D209390B879D84464A13CB91FD_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_playbackSpeed(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_isLooping_m9E4671C5136760DDD572FFF425B48B56DC7C3B8B (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_isLooping_m9E4671C5136760DDD572FFF425B48B56DC7C3B8B_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_isLooping_m9E4671C5136760DDD572FFF425B48B56DC7C3B8B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_isLooping_m9E4671C5136760DDD572FFF425B48B56DC7C3B8B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_isLooping()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_isLooping_m37563E7D24110FDBBBB4E48EA90E639BE778264E (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_isLooping_m37563E7D24110FDBBBB4E48EA90E639BE778264E_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, bool);
	static VideoPlayer_set_isLooping_m37563E7D24110FDBBBB4E48EA90E639BE778264E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_isLooping_m37563E7D24110FDBBBB4E48EA90E639BE778264E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_isLooping(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_canSetTimeSource_m88F0763CBC22EC1484A16A7AF61CB3BA6178F2EB (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_canSetTimeSource_m88F0763CBC22EC1484A16A7AF61CB3BA6178F2EB_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_canSetTimeSource_m88F0763CBC22EC1484A16A7AF61CB3BA6178F2EB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_canSetTimeSource_m88F0763CBC22EC1484A16A7AF61CB3BA6178F2EB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_canSetTimeSource()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayer_get_timeSource_mF7617364CDCACD7466E6C16139E38C4E07F83171 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayer_get_timeSource_mF7617364CDCACD7466E6C16139E38C4E07F83171_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_timeSource_mF7617364CDCACD7466E6C16139E38C4E07F83171_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_timeSource_mF7617364CDCACD7466E6C16139E38C4E07F83171_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_timeSource()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_timeSource_mCD127C85C49C8645618959D28C8DB6A23E5ABF6C (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_timeSource_mCD127C85C49C8645618959D28C8DB6A23E5ABF6C_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int32_t);
	static VideoPlayer_set_timeSource_mCD127C85C49C8645618959D28C8DB6A23E5ABF6C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_timeSource_mCD127C85C49C8645618959D28C8DB6A23E5ABF6C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_timeSource(UnityEngine.Video.VideoTimeSource)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayer_get_timeReference_m4CF54B68EECDD8C0B830DF7976B058C3415DA4AF (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayer_get_timeReference_m4CF54B68EECDD8C0B830DF7976B058C3415DA4AF_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_timeReference_m4CF54B68EECDD8C0B830DF7976B058C3415DA4AF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_timeReference_m4CF54B68EECDD8C0B830DF7976B058C3415DA4AF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_timeReference()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_timeReference_mFA6BE31876562F4B2B2008ECCE9A54B225D2E623 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_timeReference_mFA6BE31876562F4B2B2008ECCE9A54B225D2E623_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int32_t);
	static VideoPlayer_set_timeReference_mFA6BE31876562F4B2B2008ECCE9A54B225D2E623_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_timeReference_mFA6BE31876562F4B2B2008ECCE9A54B225D2E623_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_timeReference(UnityEngine.Video.VideoTimeReference)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoPlayer_get_externalReferenceTime_m9A393C66E346ECE745957DF8042316687F347B46 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef double (*VideoPlayer_get_externalReferenceTime_m9A393C66E346ECE745957DF8042316687F347B46_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_externalReferenceTime_m9A393C66E346ECE745957DF8042316687F347B46_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_externalReferenceTime_m9A393C66E346ECE745957DF8042316687F347B46_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_externalReferenceTime()");
	double icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_externalReferenceTime_m400FAEB8D782B595250162AFE10DA0E635AB08F2 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, double ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_externalReferenceTime_m400FAEB8D782B595250162AFE10DA0E635AB08F2_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, double);
	static VideoPlayer_set_externalReferenceTime_m400FAEB8D782B595250162AFE10DA0E635AB08F2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_externalReferenceTime_m400FAEB8D782B595250162AFE10DA0E635AB08F2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_externalReferenceTime(System.Double)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_canSetSkipOnDrop_mB49ACD180488093073E3D3DB23C0A488B1E2E437 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_canSetSkipOnDrop_mB49ACD180488093073E3D3DB23C0A488B1E2E437_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_canSetSkipOnDrop_mB49ACD180488093073E3D3DB23C0A488B1E2E437_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_canSetSkipOnDrop_mB49ACD180488093073E3D3DB23C0A488B1E2E437_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_canSetSkipOnDrop()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_skipOnDrop_m212D6DB4DBC55E74EDEFEE99A6A04E17B2D5DCC3 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_skipOnDrop_m212D6DB4DBC55E74EDEFEE99A6A04E17B2D5DCC3_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_skipOnDrop_m212D6DB4DBC55E74EDEFEE99A6A04E17B2D5DCC3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_skipOnDrop_m212D6DB4DBC55E74EDEFEE99A6A04E17B2D5DCC3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_skipOnDrop()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_skipOnDrop_m3AF58ACDD78E0331942DA2C82075EE09D824B6B9 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_skipOnDrop_m3AF58ACDD78E0331942DA2C82075EE09D824B6B9_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, bool);
	static VideoPlayer_set_skipOnDrop_m3AF58ACDD78E0331942DA2C82075EE09D824B6B9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_skipOnDrop_m3AF58ACDD78E0331942DA2C82075EE09D824B6B9_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_skipOnDrop(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t VideoPlayer_get_frameCount_m23BB0591824D2B1F554775F63D950429C27142F7 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef uint64_t (*VideoPlayer_get_frameCount_m23BB0591824D2B1F554775F63D950429C27142F7_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_frameCount_m23BB0591824D2B1F554775F63D950429C27142F7_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_frameCount_m23BB0591824D2B1F554775F63D950429C27142F7_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_frameCount()");
	uint64_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VideoPlayer_get_frameRate_mA86678C60358E02358BA4D999841A3552FC78FDE (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef float (*VideoPlayer_get_frameRate_mA86678C60358E02358BA4D999841A3552FC78FDE_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_frameRate_mA86678C60358E02358BA4D999841A3552FC78FDE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_frameRate_mA86678C60358E02358BA4D999841A3552FC78FDE_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_frameRate()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double VideoPlayer_get_length_mA0D8C06DB63C48CF5100068387CF0AFC2A0F093E (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef double (*VideoPlayer_get_length_mA0D8C06DB63C48CF5100068387CF0AFC2A0F093E_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_length_mA0D8C06DB63C48CF5100068387CF0AFC2A0F093E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_length_mA0D8C06DB63C48CF5100068387CF0AFC2A0F093E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_length()");
	double icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayer_get_width_m7072FA6D715254FA6D46C1BC5E7F5F02940FF9A1 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayer_get_width_m7072FA6D715254FA6D46C1BC5E7F5F02940FF9A1_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_width_m7072FA6D715254FA6D46C1BC5E7F5F02940FF9A1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_width_m7072FA6D715254FA6D46C1BC5E7F5F02940FF9A1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_width()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayer_get_height_mF12683FC77C7922EC95964AE792A43E8748AC60F (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayer_get_height_mF12683FC77C7922EC95964AE792A43E8748AC60F_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_height_mF12683FC77C7922EC95964AE792A43E8748AC60F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_height_mF12683FC77C7922EC95964AE792A43E8748AC60F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_height()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayer_get_pixelAspectRatioNumerator_m3A598B15AC3026423630663F9DB1F6D44D831205 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayer_get_pixelAspectRatioNumerator_m3A598B15AC3026423630663F9DB1F6D44D831205_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_pixelAspectRatioNumerator_m3A598B15AC3026423630663F9DB1F6D44D831205_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_pixelAspectRatioNumerator_m3A598B15AC3026423630663F9DB1F6D44D831205_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_pixelAspectRatioNumerator()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayer_get_pixelAspectRatioDenominator_mFD303992F4DAA00C7DB09EC7CCFE83B190C1BFB4 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayer_get_pixelAspectRatioDenominator_mFD303992F4DAA00C7DB09EC7CCFE83B190C1BFB4_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_pixelAspectRatioDenominator_mFD303992F4DAA00C7DB09EC7CCFE83B190C1BFB4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_pixelAspectRatioDenominator_mFD303992F4DAA00C7DB09EC7CCFE83B190C1BFB4_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_pixelAspectRatioDenominator()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayer_get_audioTrackCount_m9CFD91CB1912D807F24C991EB458EC37CB91A1DA (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef uint16_t (*VideoPlayer_get_audioTrackCount_m9CFD91CB1912D807F24C991EB458EC37CB91A1DA_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_audioTrackCount_m9CFD91CB1912D807F24C991EB458EC37CB91A1DA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_audioTrackCount_m9CFD91CB1912D807F24C991EB458EC37CB91A1DA_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_audioTrackCount()");
	uint16_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* VideoPlayer_GetAudioLanguageCode_mEA347328928E12E93AB0C188E71FB64ED74DE703 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) 
{
	typedef String_t* (*VideoPlayer_GetAudioLanguageCode_mEA347328928E12E93AB0C188E71FB64ED74DE703_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t);
	static VideoPlayer_GetAudioLanguageCode_mEA347328928E12E93AB0C188E71FB64ED74DE703_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_GetAudioLanguageCode_mEA347328928E12E93AB0C188E71FB64ED74DE703_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::GetAudioLanguageCode(System.UInt16)");
	String_t* icallRetVal = _il2cpp_icall_func(__this, ___0_trackIndex);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayer_GetAudioChannelCount_mF0C207D88EE44AEAB6A6FA0D10FD3C5E1DE4A1D8 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) 
{
	typedef uint16_t (*VideoPlayer_GetAudioChannelCount_mF0C207D88EE44AEAB6A6FA0D10FD3C5E1DE4A1D8_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t);
	static VideoPlayer_GetAudioChannelCount_mF0C207D88EE44AEAB6A6FA0D10FD3C5E1DE4A1D8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_GetAudioChannelCount_mF0C207D88EE44AEAB6A6FA0D10FD3C5E1DE4A1D8_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::GetAudioChannelCount(System.UInt16)");
	uint16_t icallRetVal = _il2cpp_icall_func(__this, ___0_trackIndex);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayer_GetAudioSampleRate_mBCBEE2EEBD1D44479BC92B5FEF858EE61DD9E1E0 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayer_GetAudioSampleRate_mBCBEE2EEBD1D44479BC92B5FEF858EE61DD9E1E0_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t);
	static VideoPlayer_GetAudioSampleRate_mBCBEE2EEBD1D44479BC92B5FEF858EE61DD9E1E0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_GetAudioSampleRate_mBCBEE2EEBD1D44479BC92B5FEF858EE61DD9E1E0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::GetAudioSampleRate(System.UInt16)");
	uint32_t icallRetVal = _il2cpp_icall_func(__this, ___0_trackIndex);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayer_get_controlledAudioTrackMaxCount_mC53CBFD61E4B2DBAA3B296E0FC990064ECC69DA5 (const RuntimeMethod* method) 
{
	typedef uint16_t (*VideoPlayer_get_controlledAudioTrackMaxCount_mC53CBFD61E4B2DBAA3B296E0FC990064ECC69DA5_ftn) ();
	static VideoPlayer_get_controlledAudioTrackMaxCount_mC53CBFD61E4B2DBAA3B296E0FC990064ECC69DA5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_controlledAudioTrackMaxCount_mC53CBFD61E4B2DBAA3B296E0FC990064ECC69DA5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_controlledAudioTrackMaxCount()");
	uint16_t icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayer_get_controlledAudioTrackCount_m7D0887B6AFEBCC16925024A63D0D25E4B2060448 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_get_controlledAudioTrackCount_m7D0887B6AFEBCC16925024A63D0D25E4B2060448_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	uint16_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_get_controlledAudioTrackCount_m7D0887B6AFEBCC16925024A63D0D25E4B2060448_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 231));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 232));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 233));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 234));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 235));
		uint16_t L_0;
		L_0 = VideoPlayer_GetControlledAudioTrackCount_m637055B53DAC8178FA540AB70294DAC090B035EC(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 235));
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 236));
		uint16_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_controlledAudioTrackCount_m5B9EAFA1178F1AB06E7C5EE9E53011C4C86CF8AD (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_set_controlledAudioTrackCount_m5B9EAFA1178F1AB06E7C5EE9E53011C4C86CF8AD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_set_controlledAudioTrackCount_m5B9EAFA1178F1AB06E7C5EE9E53011C4C86CF8AD_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 237));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 238));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 239));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 240));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 241));
		uint16_t L_0;
		L_0 = VideoPlayer_get_controlledAudioTrackMaxCount_mC53CBFD61E4B2DBAA3B296E0FC990064ECC69DA5(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 241));
		V_0 = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 242));
		uint16_t L_1 = ___0_value;
		int32_t L_2 = V_0;
		V_1 = (bool)((((int32_t)L_1) > ((int32_t)L_2))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 243));
		bool L_3 = V_1;
		if (!L_3)
		{
			goto IL_002a;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 244));
		int32_t L_4 = V_0;
		int32_t L_5 = L_4;
		RuntimeObject* L_6 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_5);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 245));
		String_t* L_7;
		L_7 = String_Format_mA8DBB4C2516B9723C5A41E6CB1E2FAF4BBE96DD8(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral702CAC6E5B2A0B5052071E4FF4BD80AFF05EA14C)), L_6, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 245));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 246));
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_8 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m8F9D40CE19D19B698A70F9A258640EB52DB39B62(L_8, L_7, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral46F273EF641E07D271D91E0DC24A4392582671F8)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 246));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_8, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayer_set_controlledAudioTrackCount_m5B9EAFA1178F1AB06E7C5EE9E53011C4C86CF8AD_RuntimeMethod_var)));
	}

IL_002a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 247));
		uint16_t L_9 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 248));
		VideoPlayer_SetControlledAudioTrackCount_m548AAC0F8B93551FCF9B7037F7E5A1CC079CCEC5(__this, L_9, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 248));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 249));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayer_GetControlledAudioTrackCount_m637055B53DAC8178FA540AB70294DAC090B035EC (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef uint16_t (*VideoPlayer_GetControlledAudioTrackCount_m637055B53DAC8178FA540AB70294DAC090B035EC_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_GetControlledAudioTrackCount_m637055B53DAC8178FA540AB70294DAC090B035EC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_GetControlledAudioTrackCount_m637055B53DAC8178FA540AB70294DAC090B035EC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::GetControlledAudioTrackCount()");
	uint16_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_SetControlledAudioTrackCount_m548AAC0F8B93551FCF9B7037F7E5A1CC079CCEC5 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_SetControlledAudioTrackCount_m548AAC0F8B93551FCF9B7037F7E5A1CC079CCEC5_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t);
	static VideoPlayer_SetControlledAudioTrackCount_m548AAC0F8B93551FCF9B7037F7E5A1CC079CCEC5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_SetControlledAudioTrackCount_m548AAC0F8B93551FCF9B7037F7E5A1CC079CCEC5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::SetControlledAudioTrackCount(System.UInt16)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_EnableAudioTrack_mD7FE1C20A0B3FD59AF0FD634A7868FC8335D76BB (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, bool ___1_enabled, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_EnableAudioTrack_mD7FE1C20A0B3FD59AF0FD634A7868FC8335D76BB_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t, bool);
	static VideoPlayer_EnableAudioTrack_mD7FE1C20A0B3FD59AF0FD634A7868FC8335D76BB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_EnableAudioTrack_mD7FE1C20A0B3FD59AF0FD634A7868FC8335D76BB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::EnableAudioTrack(System.UInt16,System.Boolean)");
	_il2cpp_icall_func(__this, ___0_trackIndex, ___1_enabled);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_IsAudioTrackEnabled_m47283ED7403FB90E85EE6FA0F3D06C1DD1619A13 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_IsAudioTrackEnabled_m47283ED7403FB90E85EE6FA0F3D06C1DD1619A13_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t);
	static VideoPlayer_IsAudioTrackEnabled_m47283ED7403FB90E85EE6FA0F3D06C1DD1619A13_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_IsAudioTrackEnabled_m47283ED7403FB90E85EE6FA0F3D06C1DD1619A13_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::IsAudioTrackEnabled(System.UInt16)");
	bool icallRetVal = _il2cpp_icall_func(__this, ___0_trackIndex);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayer_get_audioOutputMode_m22644BE075B145626BD48015E5948CAF4486B60E (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayer_get_audioOutputMode_m22644BE075B145626BD48015E5948CAF4486B60E_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_audioOutputMode_m22644BE075B145626BD48015E5948CAF4486B60E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_audioOutputMode_m22644BE075B145626BD48015E5948CAF4486B60E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_audioOutputMode()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_audioOutputMode_mB6388A630434913121DD37D2E48CF66E81BF8574 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_audioOutputMode_mB6388A630434913121DD37D2E48CF66E81BF8574_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int32_t);
	static VideoPlayer_set_audioOutputMode_mB6388A630434913121DD37D2E48CF66E81BF8574_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_audioOutputMode_mB6388A630434913121DD37D2E48CF66E81BF8574_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_audioOutputMode(UnityEngine.Video.VideoAudioOutputMode)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_canSetDirectAudioVolume_m42D5F68E9D376FDC2C80F52BDB594726E16362BC (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_canSetDirectAudioVolume_m42D5F68E9D376FDC2C80F52BDB594726E16362BC_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_canSetDirectAudioVolume_m42D5F68E9D376FDC2C80F52BDB594726E16362BC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_canSetDirectAudioVolume_m42D5F68E9D376FDC2C80F52BDB594726E16362BC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_canSetDirectAudioVolume()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VideoPlayer_GetDirectAudioVolume_m32C42477953984C6A5408DB9CA0A6CFF9912E1DA (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) 
{
	typedef float (*VideoPlayer_GetDirectAudioVolume_m32C42477953984C6A5408DB9CA0A6CFF9912E1DA_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t);
	static VideoPlayer_GetDirectAudioVolume_m32C42477953984C6A5408DB9CA0A6CFF9912E1DA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_GetDirectAudioVolume_m32C42477953984C6A5408DB9CA0A6CFF9912E1DA_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::GetDirectAudioVolume(System.UInt16)");
	float icallRetVal = _il2cpp_icall_func(__this, ___0_trackIndex);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_SetDirectAudioVolume_m77236AFF5D3EB228B22C41B4A31C3C5DC72A8BE8 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, float ___1_volume, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_SetDirectAudioVolume_m77236AFF5D3EB228B22C41B4A31C3C5DC72A8BE8_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t, float);
	static VideoPlayer_SetDirectAudioVolume_m77236AFF5D3EB228B22C41B4A31C3C5DC72A8BE8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_SetDirectAudioVolume_m77236AFF5D3EB228B22C41B4A31C3C5DC72A8BE8_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::SetDirectAudioVolume(System.UInt16,System.Single)");
	_il2cpp_icall_func(__this, ___0_trackIndex, ___1_volume);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_GetDirectAudioMute_m5567DCDDF04BCD78DAB57990EC6F95D1E669DFD0 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_GetDirectAudioMute_m5567DCDDF04BCD78DAB57990EC6F95D1E669DFD0_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t);
	static VideoPlayer_GetDirectAudioMute_m5567DCDDF04BCD78DAB57990EC6F95D1E669DFD0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_GetDirectAudioMute_m5567DCDDF04BCD78DAB57990EC6F95D1E669DFD0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::GetDirectAudioMute(System.UInt16)");
	bool icallRetVal = _il2cpp_icall_func(__this, ___0_trackIndex);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_SetDirectAudioMute_mA22511B44579744FD391D6EF2DC5C75A3AF97E8A (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, bool ___1_mute, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_SetDirectAudioMute_mA22511B44579744FD391D6EF2DC5C75A3AF97E8A_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t, bool);
	static VideoPlayer_SetDirectAudioMute_mA22511B44579744FD391D6EF2DC5C75A3AF97E8A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_SetDirectAudioMute_mA22511B44579744FD391D6EF2DC5C75A3AF97E8A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::SetDirectAudioMute(System.UInt16,System.Boolean)");
	_il2cpp_icall_func(__this, ___0_trackIndex, ___1_mute);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* VideoPlayer_GetTargetAudioSource_m882BE559DD6C3D04BC5D2BDA3F61E199D7C3EBD2 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) 
{
	typedef AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* (*VideoPlayer_GetTargetAudioSource_m882BE559DD6C3D04BC5D2BDA3F61E199D7C3EBD2_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t);
	static VideoPlayer_GetTargetAudioSource_m882BE559DD6C3D04BC5D2BDA3F61E199D7C3EBD2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_GetTargetAudioSource_m882BE559DD6C3D04BC5D2BDA3F61E199D7C3EBD2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::GetTargetAudioSource(System.UInt16)");
	AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* icallRetVal = _il2cpp_icall_func(__this, ___0_trackIndex);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_SetTargetAudioSource_m07A0CF8814A1A2972347A9123D1088A16E736CA8 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, uint16_t ___0_trackIndex, AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* ___1_source, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_SetTargetAudioSource_m07A0CF8814A1A2972347A9123D1088A16E736CA8_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, uint16_t, AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299*);
	static VideoPlayer_SetTargetAudioSource_m07A0CF8814A1A2972347A9123D1088A16E736CA8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_SetTargetAudioSource_m07A0CF8814A1A2972347A9123D1088A16E736CA8_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::SetTargetAudioSource(System.UInt16,UnityEngine.AudioSource)");
	_il2cpp_icall_func(__this, ___0_trackIndex, ___1_source);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_add_prepareCompleted_m4DB8843A3E3E3CFF7E361E70EA51983AF457183B (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_add_prepareCompleted_m4DB8843A3E3E3CFF7E361E70EA51983AF457183B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_add_prepareCompleted_m4DB8843A3E3E3CFF7E361E70EA51983AF457183B_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___prepareCompleted;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___prepareCompleted);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_remove_prepareCompleted_mDA77FE81F97195666D407DBB5689F8289A0513DF (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_remove_prepareCompleted_mDA77FE81F97195666D407DBB5689F8289A0513DF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_remove_prepareCompleted_mDA77FE81F97195666D407DBB5689F8289A0513DF_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___prepareCompleted;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___prepareCompleted);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_add_loopPointReached_m384F2CD1412D92561E7A489C05D1F78A7EC931DD (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_add_loopPointReached_m384F2CD1412D92561E7A489C05D1F78A7EC931DD_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_add_loopPointReached_m384F2CD1412D92561E7A489C05D1F78A7EC931DD_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___loopPointReached;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___loopPointReached);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_remove_loopPointReached_mF8DD445E1CE55B1F4F1AB40ED11DBCA8892930C6 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_remove_loopPointReached_mF8DD445E1CE55B1F4F1AB40ED11DBCA8892930C6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_remove_loopPointReached_mF8DD445E1CE55B1F4F1AB40ED11DBCA8892930C6_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___loopPointReached;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___loopPointReached);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_add_started_m6CE7943E721F775D734462A8CAB8B61519FAA08F (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_add_started_m6CE7943E721F775D734462A8CAB8B61519FAA08F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_add_started_m6CE7943E721F775D734462A8CAB8B61519FAA08F_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___started;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___started);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_remove_started_m1CFF6E499642737A44F18EE9D29DADAABA52E906 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_remove_started_m1CFF6E499642737A44F18EE9D29DADAABA52E906_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_remove_started_m1CFF6E499642737A44F18EE9D29DADAABA52E906_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___started;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___started);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_add_frameDropped_m25B25C5116E12B905B1D4D8F97E918206BD29C25 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_add_frameDropped_m25B25C5116E12B905B1D4D8F97E918206BD29C25_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_add_frameDropped_m25B25C5116E12B905B1D4D8F97E918206BD29C25_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___frameDropped;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___frameDropped);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_remove_frameDropped_m8F5E433D91869FFB7E643C612E09D5FB19E5D3B9 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_remove_frameDropped_m8F5E433D91869FFB7E643C612E09D5FB19E5D3B9_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_remove_frameDropped_m8F5E433D91869FFB7E643C612E09D5FB19E5D3B9_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___frameDropped;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___frameDropped);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_add_errorReceived_mDB5EB3E78A571249C101BF6A7CB394A8AF7C0B6A (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_add_errorReceived_mDB5EB3E78A571249C101BF6A7CB394A8AF7C0B6A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* V_0 = NULL;
	ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* V_1 = NULL;
	ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_add_errorReceived_mDB5EB3E78A571249C101BF6A7CB394A8AF7C0B6A_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_0 = __this->___errorReceived;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_1 = V_0;
		V_1 = L_1;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_2 = V_1;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*)CastclassSealed((RuntimeObject*)L_4, ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462_il2cpp_TypeInfo_var));
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462** L_5 = (ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462**)(&__this->___errorReceived);
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_6 = V_2;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_7 = V_1;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_8;
		L_8 = InterlockedCompareExchangeImpl<ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*>(L_5, L_6, L_7);
		V_0 = L_8;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_9 = V_0;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_10 = V_1;
		if ((!(((RuntimeObject*)(ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*)L_9) == ((RuntimeObject*)(ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_remove_errorReceived_m419AC4C19034B872B7DB82B59B4E61302213FA2A (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_remove_errorReceived_m419AC4C19034B872B7DB82B59B4E61302213FA2A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* V_0 = NULL;
	ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* V_1 = NULL;
	ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_remove_errorReceived_m419AC4C19034B872B7DB82B59B4E61302213FA2A_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_0 = __this->___errorReceived;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_1 = V_0;
		V_1 = L_1;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_2 = V_1;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*)CastclassSealed((RuntimeObject*)L_4, ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462_il2cpp_TypeInfo_var));
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462** L_5 = (ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462**)(&__this->___errorReceived);
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_6 = V_2;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_7 = V_1;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_8;
		L_8 = InterlockedCompareExchangeImpl<ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*>(L_5, L_6, L_7);
		V_0 = L_8;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_9 = V_0;
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_10 = V_1;
		if ((!(((RuntimeObject*)(ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*)L_9) == ((RuntimeObject*)(ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_add_seekCompleted_m9D3C4EE17B93B83E4D806420003DD66B4AF23C0C (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_add_seekCompleted_m9D3C4EE17B93B83E4D806420003DD66B4AF23C0C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_add_seekCompleted_m9D3C4EE17B93B83E4D806420003DD66B4AF23C0C_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___seekCompleted;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___seekCompleted);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_remove_seekCompleted_m9D9A46A6041E3B09C3546F20F05B7BE30A3B73C8 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_remove_seekCompleted_m9D9A46A6041E3B09C3546F20F05B7BE30A3B73C8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_0 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_1 = NULL;
	EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_remove_seekCompleted_m9D9A46A6041E3B09C3546F20F05B7BE30A3B73C8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_0 = __this->___seekCompleted;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = V_0;
		V_1 = L_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_2 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)CastclassSealed((RuntimeObject*)L_4, EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411_il2cpp_TypeInfo_var));
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411** L_5 = (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411**)(&__this->___seekCompleted);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_6 = V_2;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_7 = V_1;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_8;
		L_8 = InterlockedCompareExchangeImpl<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(L_5, L_6, L_7);
		V_0 = L_8;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_9 = V_0;
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_10 = V_1;
		if ((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_9) == ((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_add_clockResyncOccurred_m36C7F41F586D5BEA72B624920418EC237EB61A4F (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_add_clockResyncOccurred_m36C7F41F586D5BEA72B624920418EC237EB61A4F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* V_0 = NULL;
	TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* V_1 = NULL;
	TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_add_clockResyncOccurred_m36C7F41F586D5BEA72B624920418EC237EB61A4F_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_0 = __this->___clockResyncOccurred;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_1 = V_0;
		V_1 = L_1;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_2 = V_1;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*)CastclassSealed((RuntimeObject*)L_4, TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E_il2cpp_TypeInfo_var));
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E** L_5 = (TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E**)(&__this->___clockResyncOccurred);
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_6 = V_2;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_7 = V_1;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_8;
		L_8 = InterlockedCompareExchangeImpl<TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*>(L_5, L_6, L_7);
		V_0 = L_8;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_9 = V_0;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_10 = V_1;
		if ((!(((RuntimeObject*)(TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*)L_9) == ((RuntimeObject*)(TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_remove_clockResyncOccurred_m2C1839586C69C24C9B8E5085CF1A98DDC5AF7074 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_remove_clockResyncOccurred_m2C1839586C69C24C9B8E5085CF1A98DDC5AF7074_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* V_0 = NULL;
	TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* V_1 = NULL;
	TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_remove_clockResyncOccurred_m2C1839586C69C24C9B8E5085CF1A98DDC5AF7074_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_0 = __this->___clockResyncOccurred;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_1 = V_0;
		V_1 = L_1;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_2 = V_1;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*)CastclassSealed((RuntimeObject*)L_4, TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E_il2cpp_TypeInfo_var));
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E** L_5 = (TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E**)(&__this->___clockResyncOccurred);
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_6 = V_2;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_7 = V_1;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_8;
		L_8 = InterlockedCompareExchangeImpl<TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*>(L_5, L_6, L_7);
		V_0 = L_8;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_9 = V_0;
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_10 = V_1;
		if ((!(((RuntimeObject*)(TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*)L_9) == ((RuntimeObject*)(TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayer_get_sendFrameReadyEvents_m248F4B727D2674717F6F77F7FADF76C1940EFC38 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayer_get_sendFrameReadyEvents_m248F4B727D2674717F6F77F7FADF76C1940EFC38_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*);
	static VideoPlayer_get_sendFrameReadyEvents_m248F4B727D2674717F6F77F7FADF76C1940EFC38_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_get_sendFrameReadyEvents_m248F4B727D2674717F6F77F7FADF76C1940EFC38_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::get_sendFrameReadyEvents()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_set_sendFrameReadyEvents_m21BA78E2E7F3E69B74DE740327F4798374DE7FE3 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayer_set_sendFrameReadyEvents_m21BA78E2E7F3E69B74DE740327F4798374DE7FE3_ftn) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, bool);
	static VideoPlayer_set_sendFrameReadyEvents_m21BA78E2E7F3E69B74DE740327F4798374DE7FE3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayer_set_sendFrameReadyEvents_m21BA78E2E7F3E69B74DE740327F4798374DE7FE3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Video.VideoPlayer::set_sendFrameReadyEvents(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_add_frameReady_m8408E7B2AB88FFF43E260EFF139579C2146741CE (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_add_frameReady_m8408E7B2AB88FFF43E260EFF139579C2146741CE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* V_0 = NULL;
	FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* V_1 = NULL;
	FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_add_frameReady_m8408E7B2AB88FFF43E260EFF139579C2146741CE_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_0 = __this->___frameReady;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_1 = V_0;
		V_1 = L_1;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_2 = V_1;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*)CastclassSealed((RuntimeObject*)L_4, FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8_il2cpp_TypeInfo_var));
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8** L_5 = (FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8**)(&__this->___frameReady);
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_6 = V_2;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_7 = V_1;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_8;
		L_8 = InterlockedCompareExchangeImpl<FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*>(L_5, L_6, L_7);
		V_0 = L_8;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_9 = V_0;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_10 = V_1;
		if ((!(((RuntimeObject*)(FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*)L_9) == ((RuntimeObject*)(FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_remove_frameReady_mE7D5C9E4A9D8D34EC6186B570348B54EFEF7FE32 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_remove_frameReady_mE7D5C9E4A9D8D34EC6186B570348B54EFEF7FE32_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* V_0 = NULL;
	FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* V_1 = NULL;
	FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_remove_frameReady_mE7D5C9E4A9D8D34EC6186B570348B54EFEF7FE32_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_0 = __this->___frameReady;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_1 = V_0;
		V_1 = L_1;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_2 = V_1;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*)CastclassSealed((RuntimeObject*)L_4, FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8_il2cpp_TypeInfo_var));
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8** L_5 = (FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8**)(&__this->___frameReady);
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_6 = V_2;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_7 = V_1;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_8;
		L_8 = InterlockedCompareExchangeImpl<FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*>(L_5, L_6, L_7);
		V_0 = L_8;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_9 = V_0;
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_10 = V_1;
		if ((!(((RuntimeObject*)(FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*)L_9) == ((RuntimeObject*)(FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_InvokePrepareCompletedCallback_Internal_mF483B141E9AF0B553FF87B32A0EE54E40BE21EFD (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_InvokePrepareCompletedCallback_Internal_mF483B141E9AF0B553FF87B32A0EE54E40BE21EFD_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_source));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_InvokePrepareCompletedCallback_Internal_mF483B141E9AF0B553FF87B32A0EE54E40BE21EFD_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 250));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 251));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 252));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 253));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_0 = ___0_source;
		NullCheck(L_0);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = L_0->___prepareCompleted;
		V_0 = (bool)((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_1) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 254));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 255));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_3 = ___0_source;
		NullCheck(L_3);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_4 = L_3->___prepareCompleted;
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_5 = ___0_source;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 256));
		NullCheck(L_4);
		EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_inline(L_4, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 256));
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 257));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_InvokeFrameReadyCallback_Internal_mF5F02FE6BBEA70C62108F3FD836E1B378CB9F5E7 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, int64_t ___1_frameIdx, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_InvokeFrameReadyCallback_Internal_mF5F02FE6BBEA70C62108F3FD836E1B378CB9F5E7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_source), (&___1_frameIdx));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_InvokeFrameReadyCallback_Internal_mF5F02FE6BBEA70C62108F3FD836E1B378CB9F5E7_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 258));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 259));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 260));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 261));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_0 = ___0_source;
		NullCheck(L_0);
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_1 = L_0->___frameReady;
		V_0 = (bool)((!(((RuntimeObject*)(FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*)L_1) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 262));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001c;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 263));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_3 = ___0_source;
		NullCheck(L_3);
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* L_4 = L_3->___frameReady;
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_5 = ___0_source;
		int64_t L_6 = ___1_frameIdx;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 264));
		NullCheck(L_4);
		FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235_inline(L_4, L_5, L_6, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 264));
	}

IL_001c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 265));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_InvokeLoopPointReachedCallback_Internal_m090FD00BB12721EB3CEB337E30687D5DE6B1EBD6 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_InvokeLoopPointReachedCallback_Internal_m090FD00BB12721EB3CEB337E30687D5DE6B1EBD6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_source));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_InvokeLoopPointReachedCallback_Internal_m090FD00BB12721EB3CEB337E30687D5DE6B1EBD6_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 266));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 267));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 268));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 269));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_0 = ___0_source;
		NullCheck(L_0);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = L_0->___loopPointReached;
		V_0 = (bool)((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_1) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 270));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 271));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_3 = ___0_source;
		NullCheck(L_3);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_4 = L_3->___loopPointReached;
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_5 = ___0_source;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 272));
		NullCheck(L_4);
		EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_inline(L_4, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 272));
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 273));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_InvokeStartedCallback_Internal_mBDD186C95E57DD264CD0957FA46D9F65728C67EF (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_InvokeStartedCallback_Internal_mBDD186C95E57DD264CD0957FA46D9F65728C67EF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_source));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_InvokeStartedCallback_Internal_mBDD186C95E57DD264CD0957FA46D9F65728C67EF_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 274));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 275));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 276));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 277));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_0 = ___0_source;
		NullCheck(L_0);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = L_0->___started;
		V_0 = (bool)((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_1) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 278));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 279));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_3 = ___0_source;
		NullCheck(L_3);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_4 = L_3->___started;
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_5 = ___0_source;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 280));
		NullCheck(L_4);
		EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_inline(L_4, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 280));
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 281));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_InvokeFrameDroppedCallback_Internal_m6E6B6353404AF76AB16D1433B8553659B0744DC0 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_InvokeFrameDroppedCallback_Internal_m6E6B6353404AF76AB16D1433B8553659B0744DC0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_source));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_InvokeFrameDroppedCallback_Internal_m6E6B6353404AF76AB16D1433B8553659B0744DC0_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 282));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 283));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 284));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 285));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_0 = ___0_source;
		NullCheck(L_0);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = L_0->___frameDropped;
		V_0 = (bool)((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_1) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 286));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 287));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_3 = ___0_source;
		NullCheck(L_3);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_4 = L_3->___frameDropped;
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_5 = ___0_source;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 288));
		NullCheck(L_4);
		EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_inline(L_4, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 288));
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 289));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_InvokeErrorReceivedCallback_Internal_m91E59ED70189824AB2C5D0AEB47742130EE98574 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, String_t* ___1_errorStr, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_InvokeErrorReceivedCallback_Internal_m91E59ED70189824AB2C5D0AEB47742130EE98574_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_source), (&___1_errorStr));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_InvokeErrorReceivedCallback_Internal_m91E59ED70189824AB2C5D0AEB47742130EE98574_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 290));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 291));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 292));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 293));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_0 = ___0_source;
		NullCheck(L_0);
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_1 = L_0->___errorReceived;
		V_0 = (bool)((!(((RuntimeObject*)(ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*)L_1) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 294));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001c;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 295));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_3 = ___0_source;
		NullCheck(L_3);
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* L_4 = L_3->___errorReceived;
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_5 = ___0_source;
		String_t* L_6 = ___1_errorStr;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 296));
		NullCheck(L_4);
		ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5_inline(L_4, L_5, L_6, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 296));
	}

IL_001c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 297));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_InvokeSeekCompletedCallback_Internal_m62AEB9826ACE95B88CC60220375C1C7EF5EEEE1F (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_InvokeSeekCompletedCallback_Internal_m62AEB9826ACE95B88CC60220375C1C7EF5EEEE1F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_source));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_InvokeSeekCompletedCallback_Internal_m62AEB9826ACE95B88CC60220375C1C7EF5EEEE1F_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 298));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 299));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 300));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 301));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_0 = ___0_source;
		NullCheck(L_0);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_1 = L_0->___seekCompleted;
		V_0 = (bool)((!(((RuntimeObject*)(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*)L_1) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 302));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 303));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_3 = ___0_source;
		NullCheck(L_3);
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* L_4 = L_3->___seekCompleted;
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_5 = ___0_source;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 304));
		NullCheck(L_4);
		EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_inline(L_4, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 304));
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 305));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer_InvokeClockResyncOccurredCallback_Internal_m743B8421E9538761141364EBDCEBC673BA58D8C6 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, double ___1_seconds, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer_InvokeClockResyncOccurredCallback_Internal_m743B8421E9538761141364EBDCEBC673BA58D8C6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_source), (&___1_seconds));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer_InvokeClockResyncOccurredCallback_Internal_m743B8421E9538761141364EBDCEBC673BA58D8C6_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 306));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 307));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 308));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 309));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_0 = ___0_source;
		NullCheck(L_0);
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_1 = L_0->___clockResyncOccurred;
		V_0 = (bool)((!(((RuntimeObject*)(TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*)L_1) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 310));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001c;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 311));
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_3 = ___0_source;
		NullCheck(L_3);
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* L_4 = L_3->___clockResyncOccurred;
		VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* L_5 = ___0_source;
		double L_6 = ___1_seconds;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 312));
		NullCheck(L_4);
		TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D_inline(L_4, L_5, L_6, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 312));
	}

IL_001c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 313));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayer__ctor_m309536693DD1F20C0D1E6ABBF5FC48858746C6B6 (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayer__ctor_m309536693DD1F20C0D1E6ABBF5FC48858746C6B6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayer__ctor_m309536693DD1F20C0D1E6ABBF5FC48858746C6B6_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_Multicast(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* currentDelegate = reinterpret_cast<EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_source, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_OpenInst(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method)
{
	NullCheck(___0_source);
	typedef void (*FunctionPointerType) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_source, method);
}
void EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_OpenStatic(EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_source, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventHandler__ctor_mFCF62E0BB29DD3D2B2F1306BD8657E374CF60903 (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 1;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		bool isOpen = parameterCount == 0;
		if (isOpen)
		{
			__this->___invoke_impl = (intptr_t)&EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_OpenInst;
		}
		else
		{
			if (___0_object == NULL)
				il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
			__this->___invoke_impl = __this->___method_ptr;
			__this->___method_code = (intptr_t)__this->___m_target;
		}
	}
	__this->___extra_arg = (intptr_t)&EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15 (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_source, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EventHandler_BeginInvoke_mA46DD0B3065BF9A2FABBEE1193FB65354BD40A49 (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___1_callback, RuntimeObject* ___2_object, const RuntimeMethod* method) 
{
	void *__d_args[2] = {0};
	__d_args[0] = ___0_source;
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___1_callback, (RuntimeObject*)___2_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventHandler_EndInvoke_m93E7935B0FE5ADF979915151689D0229F1378C8F (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5_Multicast(ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, String_t* ___1_message, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* currentDelegate = reinterpret_cast<ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, String_t*, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_source, ___1_message, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5_OpenInst(ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, String_t* ___1_message, const RuntimeMethod* method)
{
	NullCheck(___0_source);
	typedef void (*FunctionPointerType) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, String_t*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_source, ___1_message, method);
}
void ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5_OpenStatic(ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, String_t* ___1_message, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, String_t*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_source, ___1_message, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ErrorEventHandler__ctor_m78073CD72C64AAFAF54A01702919A847185F4C58 (ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 2;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		bool isOpen = parameterCount == 1;
		if (isOpen)
		{
			__this->___invoke_impl = (intptr_t)&ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5_OpenInst;
		}
		else
		{
			if (___0_object == NULL)
				il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
			__this->___invoke_impl = __this->___method_ptr;
			__this->___method_code = (intptr_t)__this->___m_target;
		}
	}
	__this->___extra_arg = (intptr_t)&ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5 (ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, String_t* ___1_message, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, String_t*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_source, ___1_message, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ErrorEventHandler_BeginInvoke_mE3CCCE015DFD84BC05FE014962212E7697FBDB43 (ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, String_t* ___1_message, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___2_callback, RuntimeObject* ___3_object, const RuntimeMethod* method) 
{
	void *__d_args[3] = {0};
	__d_args[0] = ___0_source;
	__d_args[1] = ___1_message;
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___2_callback, (RuntimeObject*)___3_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ErrorEventHandler_EndInvoke_m72A3233E8D8B0AC07AB5E8290561BAFC0AE86E1E (ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235_Multicast(FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, int64_t ___1_frameIdx, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* currentDelegate = reinterpret_cast<FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int64_t, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_source, ___1_frameIdx, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235_OpenInst(FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, int64_t ___1_frameIdx, const RuntimeMethod* method)
{
	NullCheck(___0_source);
	typedef void (*FunctionPointerType) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int64_t, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_source, ___1_frameIdx, method);
}
void FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235_OpenStatic(FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, int64_t ___1_frameIdx, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int64_t, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_source, ___1_frameIdx, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FrameReadyEventHandler__ctor_mD875F736BCA2A2128427AB0546B5081ED3B22D56 (FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 2;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		bool isOpen = parameterCount == 1;
		if (isOpen)
		{
			__this->___invoke_impl = (intptr_t)&FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235_OpenInst;
		}
		else
		{
			if (___0_object == NULL)
				il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
			__this->___invoke_impl = __this->___method_ptr;
			__this->___method_code = (intptr_t)__this->___m_target;
		}
	}
	__this->___extra_arg = (intptr_t)&FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235 (FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, int64_t ___1_frameIdx, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int64_t, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_source, ___1_frameIdx, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* FrameReadyEventHandler_BeginInvoke_m11DEF99CEA01400ED0DC02B97C9BD728B41DEEFA (FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, int64_t ___1_frameIdx, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___2_callback, RuntimeObject* ___3_object, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	void *__d_args[3] = {0};
	__d_args[0] = ___0_source;
	__d_args[1] = Box(Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_il2cpp_TypeInfo_var, &___1_frameIdx);
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___2_callback, (RuntimeObject*)___3_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FrameReadyEventHandler_EndInvoke_mE83EFE1BFDE5BFEE3948FCDDB4FE78CD575327E6 (FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D_Multicast(TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, double ___1_seconds, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* currentDelegate = reinterpret_cast<TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, double, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_source, ___1_seconds, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D_OpenInst(TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, double ___1_seconds, const RuntimeMethod* method)
{
	NullCheck(___0_source);
	typedef void (*FunctionPointerType) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, double, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_source, ___1_seconds, method);
}
void TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D_OpenStatic(TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, double ___1_seconds, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, double, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_source, ___1_seconds, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TimeEventHandler__ctor_m7BF78A616C8FF71B3A7CA62B1A03446A5C581309 (TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 2;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		bool isOpen = parameterCount == 1;
		if (isOpen)
		{
			__this->___invoke_impl = (intptr_t)&TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D_OpenInst;
		}
		else
		{
			if (___0_object == NULL)
				il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
			__this->___invoke_impl = __this->___method_ptr;
			__this->___method_code = (intptr_t)__this->___m_target;
		}
	}
	__this->___extra_arg = (intptr_t)&TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D (TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, double ___1_seconds, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, double, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_source, ___1_seconds, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* TimeEventHandler_BeginInvoke_m2BB8132BDB2BD62AC18005B2FDA7023FB8A20575 (TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, double ___1_seconds, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___2_callback, RuntimeObject* ___3_object, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	void *__d_args[3] = {0};
	__d_args[0] = ___0_source;
	__d_args[1] = Box(Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_il2cpp_TypeInfo_var, &___1_seconds);
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___2_callback, (RuntimeObject*)___3_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TimeEventHandler_EndInvoke_mC23567F6554D69E68CF5958F278A687ABDEDBEDF (TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_StartPlayback_m2F4D23E10C640A792E75C82669207FF7C8D11489 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_StartPlayback_m2F4D23E10C640A792E75C82669207FF7C8D11489_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_StartPlayback_m2F4D23E10C640A792E75C82669207FF7C8D11489_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_StartPlayback_m2F4D23E10C640A792E75C82669207FF7C8D11489_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::StartPlayback()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_PausePlayback_mBFECFC1194E367173B77C76F062480B26E903B21 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_PausePlayback_mBFECFC1194E367173B77C76F062480B26E903B21_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_PausePlayback_mBFECFC1194E367173B77C76F062480B26E903B21_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_PausePlayback_mBFECFC1194E367173B77C76F062480B26E903B21_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::PausePlayback()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_StopPlayback_mFF20D86D311D7E423BD8DE6B3A9C61915DF8563D (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_StopPlayback_mFF20D86D311D7E423BD8DE6B3A9C61915DF8563D_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_StopPlayback_mFF20D86D311D7E423BD8DE6B3A9C61915DF8563D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_StopPlayback_mFF20D86D311D7E423BD8DE6B3A9C61915DF8563D_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::StopPlayback()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayback_GetStatus_m1B5279A84F136D57EBB781F28F7D9428F3EE750D (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayback_GetStatus_m1B5279A84F136D57EBB781F28F7D9428F3EE750D_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetStatus_m1B5279A84F136D57EBB781F28F7D9428F3EE750D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetStatus_m1B5279A84F136D57EBB781F28F7D9428F3EE750D_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetStatus()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayback_IsReady_mE4E8E049E3A318484836EAD8683198D06B1E9B7B (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayback_IsReady_mE4E8E049E3A318484836EAD8683198D06B1E9B7B_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_IsReady_mE4E8E049E3A318484836EAD8683198D06B1E9B7B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_IsReady_mE4E8E049E3A318484836EAD8683198D06B1E9B7B_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::IsReady()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayback_IsPlaying_m0D1DDB9D7490FC6EA2CB593B70DB5B5F707023B6 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayback_IsPlaying_m0D1DDB9D7490FC6EA2CB593B70DB5B5F707023B6_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_IsPlaying_m0D1DDB9D7490FC6EA2CB593B70DB5B5F707023B6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_IsPlaying_m0D1DDB9D7490FC6EA2CB593B70DB5B5F707023B6_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::IsPlaying()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_Step_mCE64584877B82A8F0CE69FB465E2D742AF2ABC6E (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_Step_mCE64584877B82A8F0CE69FB465E2D742AF2ABC6E_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_Step_mCE64584877B82A8F0CE69FB465E2D742AF2ABC6E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_Step_mCE64584877B82A8F0CE69FB465E2D742AF2ABC6E_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::Step()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayback_CanStep_mE02CB8F93E39DA893490293D9D5BC2FF55B70510 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayback_CanStep_mE02CB8F93E39DA893490293D9D5BC2FF55B70510_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_CanStep_mE02CB8F93E39DA893490293D9D5BC2FF55B70510_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_CanStep_mE02CB8F93E39DA893490293D9D5BC2FF55B70510_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::CanStep()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayback_GetWidth_mFDF19C76E5EC3C1FDF2001230AF0321C32417343 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayback_GetWidth_mFDF19C76E5EC3C1FDF2001230AF0321C32417343_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetWidth_mFDF19C76E5EC3C1FDF2001230AF0321C32417343_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetWidth_mFDF19C76E5EC3C1FDF2001230AF0321C32417343_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetWidth()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayback_GetHeight_m39A7524EDF752547B232BAA04CD7CCCEA2AECCD1 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayback_GetHeight_m39A7524EDF752547B232BAA04CD7CCCEA2AECCD1_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetHeight_m39A7524EDF752547B232BAA04CD7CCCEA2AECCD1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetHeight_m39A7524EDF752547B232BAA04CD7CCCEA2AECCD1_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetHeight()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VideoPlayback_GetFrameRate_m681125AED18DB03641A8ACD75E621E587F01DE76 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef float (*VideoPlayback_GetFrameRate_m681125AED18DB03641A8ACD75E621E587F01DE76_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetFrameRate_m681125AED18DB03641A8ACD75E621E587F01DE76_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetFrameRate_m681125AED18DB03641A8ACD75E621E587F01DE76_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetFrameRate()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VideoPlayback_GetDuration_m2DDB523010F3B97E3059DBB184B3ABC8D14BF762 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef float (*VideoPlayback_GetDuration_m2DDB523010F3B97E3059DBB184B3ABC8D14BF762_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetDuration_m2DDB523010F3B97E3059DBB184B3ABC8D14BF762_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetDuration_m2DDB523010F3B97E3059DBB184B3ABC8D14BF762_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetDuration()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t VideoPlayback_GetFrameCount_m8563A17FF9ECAB3CBFE287533A304A7283B04934 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef uint64_t (*VideoPlayback_GetFrameCount_m8563A17FF9ECAB3CBFE287533A304A7283B04934_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetFrameCount_m8563A17FF9ECAB3CBFE287533A304A7283B04934_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetFrameCount_m8563A17FF9ECAB3CBFE287533A304A7283B04934_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetFrameCount()");
	uint64_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayback_GetPixelAspectRatioNumerator_mB16ADBFF77ED470C7F1DFCA589935F13529354B9 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayback_GetPixelAspectRatioNumerator_mB16ADBFF77ED470C7F1DFCA589935F13529354B9_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetPixelAspectRatioNumerator_mB16ADBFF77ED470C7F1DFCA589935F13529354B9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetPixelAspectRatioNumerator_mB16ADBFF77ED470C7F1DFCA589935F13529354B9_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetPixelAspectRatioNumerator()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayback_GetPixelAspectRatioDenominator_m04E65773988D1AFC6E0B14CEFBBD1DB990177718 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayback_GetPixelAspectRatioDenominator_m04E65773988D1AFC6E0B14CEFBBD1DB990177718_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetPixelAspectRatioDenominator_m04E65773988D1AFC6E0B14CEFBBD1DB990177718_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetPixelAspectRatioDenominator_m04E65773988D1AFC6E0B14CEFBBD1DB990177718_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetPixelAspectRatioDenominator()");
	uint32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VideoPlayback_GetPixelFormat_m82EE0F53679C798A81879BE9F7E34E1D3101809D (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*VideoPlayback_GetPixelFormat_m82EE0F53679C798A81879BE9F7E34E1D3101809D_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetPixelFormat_m82EE0F53679C798A81879BE9F7E34E1D3101809D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetPixelFormat_m82EE0F53679C798A81879BE9F7E34E1D3101809D_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetPixelFormat()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayback_CanNotSkipOnDrop_m0F59131A45ACC34117BABACDB234985601302BEA (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayback_CanNotSkipOnDrop_m0F59131A45ACC34117BABACDB234985601302BEA_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_CanNotSkipOnDrop_m0F59131A45ACC34117BABACDB234985601302BEA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_CanNotSkipOnDrop_m0F59131A45ACC34117BABACDB234985601302BEA_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::CanNotSkipOnDrop()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_SetSkipOnDrop_m307C4A68F2FDDD6AEF468180B126885E54B754CA (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, bool ___0_skipOnDrop, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_SetSkipOnDrop_m307C4A68F2FDDD6AEF468180B126885E54B754CA_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, bool);
	static VideoPlayback_SetSkipOnDrop_m307C4A68F2FDDD6AEF468180B126885E54B754CA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_SetSkipOnDrop_m307C4A68F2FDDD6AEF468180B126885E54B754CA_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::SetSkipOnDrop(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_skipOnDrop);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayback_GetTexture_mAB20E206CCE8D70060C08043E0F1081377BB7686 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___0_texture, int64_t* ___1_outputFrameNum, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayback_GetTexture_mAB20E206CCE8D70060C08043E0F1081377BB7686_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700*, int64_t*);
	static VideoPlayback_GetTexture_mAB20E206CCE8D70060C08043E0F1081377BB7686_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetTexture_mAB20E206CCE8D70060C08043E0F1081377BB7686_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetTexture(UnityEngine.Texture,System.Int64&)");
	bool icallRetVal = _il2cpp_icall_func(__this, ___0_texture, ___1_outputFrameNum);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_SeekToFrame_m3D5AC36A8E8AD16BBC60AEADCF2DDD253424BD14 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, int64_t ___0_frameIndex, Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* ___1_seekCompletedCallback, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_SeekToFrame_m3D5AC36A8E8AD16BBC60AEADCF2DDD253424BD14_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, int64_t, Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377*);
	static VideoPlayback_SeekToFrame_m3D5AC36A8E8AD16BBC60AEADCF2DDD253424BD14_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_SeekToFrame_m3D5AC36A8E8AD16BBC60AEADCF2DDD253424BD14_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::SeekToFrame(System.Int64,UnityEngineInternal.Video.VideoPlayback/Callback)");
	_il2cpp_icall_func(__this, ___0_frameIndex, ___1_seekCompletedCallback);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_SeekToTime_m0DB22B37A6E2BBAB5849A30246B30E9C86417260 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, double ___0_secs, Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* ___1_seekCompletedCallback, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_SeekToTime_m0DB22B37A6E2BBAB5849A30246B30E9C86417260_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, double, Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377*);
	static VideoPlayback_SeekToTime_m0DB22B37A6E2BBAB5849A30246B30E9C86417260_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_SeekToTime_m0DB22B37A6E2BBAB5849A30246B30E9C86417260_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::SeekToTime(System.Double,UnityEngineInternal.Video.VideoPlayback/Callback)");
	_il2cpp_icall_func(__this, ___0_secs, ___1_seekCompletedCallback);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VideoPlayback_GetPlaybackSpeed_mA607B76FB7A9561C4BD52D222A6EEEDCD34AED70 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef float (*VideoPlayback_GetPlaybackSpeed_mA607B76FB7A9561C4BD52D222A6EEEDCD34AED70_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetPlaybackSpeed_mA607B76FB7A9561C4BD52D222A6EEEDCD34AED70_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetPlaybackSpeed_mA607B76FB7A9561C4BD52D222A6EEEDCD34AED70_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetPlaybackSpeed()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_SetPlaybackSpeed_m51C1EB2714CC2AE086913DE22BDBB66D53B0D10A (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_SetPlaybackSpeed_m51C1EB2714CC2AE086913DE22BDBB66D53B0D10A_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, float);
	static VideoPlayback_SetPlaybackSpeed_m51C1EB2714CC2AE086913DE22BDBB66D53B0D10A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_SetPlaybackSpeed_m51C1EB2714CC2AE086913DE22BDBB66D53B0D10A_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::SetPlaybackSpeed(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayback_GetLoop_mDEA458A97E5AFD7CF750C8E235388E5E1F19E3CD (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayback_GetLoop_mDEA458A97E5AFD7CF750C8E235388E5E1F19E3CD_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetLoop_mDEA458A97E5AFD7CF750C8E235388E5E1F19E3CD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetLoop_mDEA458A97E5AFD7CF750C8E235388E5E1F19E3CD_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetLoop()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_SetLoop_m13914AAAC87DB4B69A83CF6B8D39EC89C5326ECA (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_SetLoop_m13914AAAC87DB4B69A83CF6B8D39EC89C5326ECA_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, bool);
	static VideoPlayback_SetLoop_m13914AAAC87DB4B69A83CF6B8D39EC89C5326ECA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_SetLoop_m13914AAAC87DB4B69A83CF6B8D39EC89C5326ECA_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::SetLoop(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_SetAdjustToLinearSpace_mED756F3C1E0EE6519C63D30C38DC6D9B404203DD (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, bool ___0_enable, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_SetAdjustToLinearSpace_mED756F3C1E0EE6519C63D30C38DC6D9B404203DD_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, bool);
	static VideoPlayback_SetAdjustToLinearSpace_mED756F3C1E0EE6519C63D30C38DC6D9B404203DD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_SetAdjustToLinearSpace_mED756F3C1E0EE6519C63D30C38DC6D9B404203DD_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::SetAdjustToLinearSpace(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_enable);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayback_GetAudioTrackCount_mF02D113890513832B10BF110672541F6F153A9AE (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	typedef uint16_t (*VideoPlayback_GetAudioTrackCount_mF02D113890513832B10BF110672541F6F153A9AE_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlayback_GetAudioTrackCount_mF02D113890513832B10BF110672541F6F153A9AE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetAudioTrackCount_mF02D113890513832B10BF110672541F6F153A9AE_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetAudioTrackCount()");
	uint16_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t VideoPlayback_GetAudioChannelCount_m6C17E5BE125A2BB4BEF68CF6BDD0351828C82135 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, uint16_t ___0_trackIdx, const RuntimeMethod* method) 
{
	typedef uint16_t (*VideoPlayback_GetAudioChannelCount_m6C17E5BE125A2BB4BEF68CF6BDD0351828C82135_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, uint16_t);
	static VideoPlayback_GetAudioChannelCount_m6C17E5BE125A2BB4BEF68CF6BDD0351828C82135_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetAudioChannelCount_m6C17E5BE125A2BB4BEF68CF6BDD0351828C82135_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetAudioChannelCount(System.UInt16)");
	uint16_t icallRetVal = _il2cpp_icall_func(__this, ___0_trackIdx);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayback_GetAudioSampleRate_m30F7C583DAD888A31A7EC0DE10E06FBFB6B205BC (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, uint16_t ___0_trackIdx, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayback_GetAudioSampleRate_m30F7C583DAD888A31A7EC0DE10E06FBFB6B205BC_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, uint16_t);
	static VideoPlayback_GetAudioSampleRate_m30F7C583DAD888A31A7EC0DE10E06FBFB6B205BC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetAudioSampleRate_m30F7C583DAD888A31A7EC0DE10E06FBFB6B205BC_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetAudioSampleRate(System.UInt16)");
	uint32_t icallRetVal = _il2cpp_icall_func(__this, ___0_trackIdx);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* VideoPlayback_GetAudioLanguageCode_m6674FB154D4C2159DBE1CF5C434BE30E7CB9728A (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, uint16_t ___0_trackIdx, const RuntimeMethod* method) 
{
	typedef String_t* (*VideoPlayback_GetAudioLanguageCode_m6674FB154D4C2159DBE1CF5C434BE30E7CB9728A_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, uint16_t);
	static VideoPlayback_GetAudioLanguageCode_m6674FB154D4C2159DBE1CF5C434BE30E7CB9728A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetAudioLanguageCode_m6674FB154D4C2159DBE1CF5C434BE30E7CB9728A_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetAudioLanguageCode(System.UInt16)");
	String_t* icallRetVal = _il2cpp_icall_func(__this, ___0_trackIdx);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback_SetAudioTarget_m67C0EDA3DCF451F52FF273E45F5D806403D96F18 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, uint16_t ___0_trackIdx, bool ___1_enabled, bool ___2_softwareOutput, AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* ___3_audioSource, const RuntimeMethod* method) 
{
	typedef void (*VideoPlayback_SetAudioTarget_m67C0EDA3DCF451F52FF273E45F5D806403D96F18_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, uint16_t, bool, bool, AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299*);
	static VideoPlayback_SetAudioTarget_m67C0EDA3DCF451F52FF273E45F5D806403D96F18_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_SetAudioTarget_m67C0EDA3DCF451F52FF273E45F5D806403D96F18_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::SetAudioTarget(System.UInt16,System.Boolean,System.Boolean,UnityEngine.AudioSource)");
	_il2cpp_icall_func(__this, ___0_trackIdx, ___1_enabled, ___2_softwareOutput, ___3_audioSource);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t VideoPlayback_GetAudioSampleProviderId_m623001A0D78033DA535639A33B2D7F8EC3F5601C (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) 
{
	typedef uint32_t (*VideoPlayback_GetAudioSampleProviderId_m623001A0D78033DA535639A33B2D7F8EC3F5601C_ftn) (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*, uint16_t);
	static VideoPlayback_GetAudioSampleProviderId_m623001A0D78033DA535639A33B2D7F8EC3F5601C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_GetAudioSampleProviderId_m623001A0D78033DA535639A33B2D7F8EC3F5601C_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::GetAudioSampleProviderId(System.UInt16)");
	uint32_t icallRetVal = _il2cpp_icall_func(__this, ___0_trackIndex);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* VideoPlayback_GetAudioSampleProvider_mBF210B2D053415976E79A1C65D9BE1A1574ADFFA (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, uint16_t ___0_trackIndex, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayback_GetAudioSampleProvider_mBF210B2D053415976E79A1C65D9BE1A1574ADFFA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* V_0 = NULL;
	bool V_1 = false;
	uint16_t V_2 = 0;
	bool V_3 = false;
	bool V_4 = false;
	bool V_5 = false;
	AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* V_6 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_trackIndex));
	DECLARE_METHOD_LOCALS(methodExecutionContextLocals, (&V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayback_GetAudioSampleProvider_mBF210B2D053415976E79A1C65D9BE1A1574ADFFA_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, methodExecutionContextLocals);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 314));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 315));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 316));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 317));
		uint16_t L_0 = ___0_trackIndex;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 318));
		uint16_t L_1;
		L_1 = VideoPlayback_GetAudioTrackCount_mF02D113890513832B10BF110672541F6F153A9AE(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 318));
		V_1 = (bool)((((int32_t)((((int32_t)L_0) < ((int32_t)L_1))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 319));
		bool L_2 = V_1;
		if (!L_2)
		{
			goto IL_003f;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 320));
		uint16_t L_3 = ___0_trackIndex;
		uint16_t L_4 = L_3;
		RuntimeObject* L_5 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_il2cpp_TypeInfo_var)), &L_4);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 321));
		uint16_t L_6;
		L_6 = VideoPlayback_GetAudioTrackCount_mF02D113890513832B10BF110672541F6F153A9AE(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 321));
		V_2 = L_6;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 322));
		String_t* L_7;
		L_7 = UInt16_ToString_m57629B7E74D92A54414073D5C27D6827C93A4DD5((&V_2), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 322));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 323));
		String_t* L_8;
		L_8 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral1AC2CE4FFFF43A805CDFE9BE8C8AAD052F378505)), L_7, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral6216AB66F68B9B8A0707687070449261B6454D7B)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 323));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 324));
		ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* L_9 = (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var)));
		ArgumentOutOfRangeException__ctor_m60B543A63AC8692C28096003FBF2AD124B9D5B85(L_9, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral573C0D2F167EB38E04F3195A6EE0598846E07DA2)), L_5, L_8, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 324));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_9, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayback_GetAudioSampleProvider_mBF210B2D053415976E79A1C65D9BE1A1574ADFFA_RuntimeMethod_var)));
	}

IL_003f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 325));
		uint16_t L_10 = ___0_trackIndex;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 326));
		uint32_t L_11;
		L_11 = VideoPlayback_GetAudioSampleProviderId_m623001A0D78033DA535639A33B2D7F8EC3F5601C(__this, L_10, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 326));
		uint16_t L_12 = ___0_trackIndex;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 327));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_13;
		L_13 = AudioSampleProvider_Lookup_mAC9B381D61BBC6504C62B6956FD0CE7BEA989BF7(L_11, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, L_12, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 327));
		V_0 = L_13;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 328));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_14 = V_0;
		V_3 = (bool)((((RuntimeObject*)(AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2*)L_14) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 329));
		bool L_15 = V_3;
		if (!L_15)
		{
			goto IL_0061;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 330));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 331));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_16 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_16, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral859573AB83EBF64A46D1CD306749946AC7958B89)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 331));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_16, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayback_GetAudioSampleProvider_mBF210B2D053415976E79A1C65D9BE1A1574ADFFA_RuntimeMethod_var)));
	}

IL_0061:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 332));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_17 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 333));
		NullCheck(L_17);
		Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* L_18;
		L_18 = AudioSampleProvider_get_owner_mC8461FD790AEC7BACE00C8A3F1DE4D00E08EF1AC_inline(L_17, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 333));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 334));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_19;
		L_19 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_18, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 334));
		V_4 = L_19;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 335));
		bool L_20 = V_4;
		if (!L_20)
		{
			goto IL_007e;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 336));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 337));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_21 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_21, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral5087EF58F8E48E468D2251A5AA70E323DD0B7E90)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 337));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_21, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayback_GetAudioSampleProvider_mBF210B2D053415976E79A1C65D9BE1A1574ADFFA_RuntimeMethod_var)));
	}

IL_007e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 338));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_22 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 339));
		NullCheck(L_22);
		uint16_t L_23;
		L_23 = AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6_inline(L_22, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 339));
		uint16_t L_24 = ___0_trackIndex;
		V_5 = (bool)((((int32_t)((((int32_t)L_23) == ((int32_t)L_24))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 340));
		bool L_25 = V_5;
		if (!L_25)
		{
			goto IL_00ba;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 341));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_26 = V_0;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 342));
		NullCheck(L_26);
		uint16_t L_27;
		L_27 = AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6_inline(L_26, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 342));
		V_2 = L_27;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 343));
		String_t* L_28;
		L_28 = UInt16_ToString_m57629B7E74D92A54414073D5C27D6827C93A4DD5((&V_2), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 343));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 344));
		String_t* L_29;
		L_29 = UInt16_ToString_m57629B7E74D92A54414073D5C27D6827C93A4DD5((&___0_trackIndex), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 344));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 345));
		String_t* L_30;
		L_30 = String_Concat_m093934F71A9B351911EE46311674ED463B180006(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral51F9E525111CFECB99B457F44ED737A97D78397D)), L_28, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0C2C7A9E7436DA4E0013C1BB4B0A6143CE0F38ED)), L_29, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 345));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 346));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_31 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_31, L_30, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 346));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_31, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VideoPlayback_GetAudioSampleProvider_mBF210B2D053415976E79A1C65D9BE1A1574ADFFA_RuntimeMethod_var)));
	}

IL_00ba:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 347));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_32 = V_0;
		V_6 = L_32;
		goto IL_00bf;
	}

IL_00bf:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 348));
		AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* L_33 = V_6;
		return L_33;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VideoPlayback_PlatformSupportsH265_mC5F7197B56E6B85349C9266B24531A0704CD4173 (const RuntimeMethod* method) 
{
	typedef bool (*VideoPlayback_PlatformSupportsH265_mC5F7197B56E6B85349C9266B24531A0704CD4173_ftn) ();
	static VideoPlayback_PlatformSupportsH265_mC5F7197B56E6B85349C9266B24531A0704CD4173_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlayback_PlatformSupportsH265_mC5F7197B56E6B85349C9266B24531A0704CD4173_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlayback::PlatformSupportsH265()");
	bool icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlayback__ctor_mBB89AC180CB8AEC5A22DB6F870272A2830951A65 (VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlayback__ctor_mBB89AC180CB8AEC5A22DB6F870272A2830951A65_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlayback__ctor_mBB89AC180CB8AEC5A22DB6F870272A2830951A65_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void Callback_Invoke_mAF4734E85D4D48894115F7AB5C3AB0C32AE9C808_Multicast(Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* __this, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* currentDelegate = reinterpret_cast<Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void Callback_Invoke_mAF4734E85D4D48894115F7AB5C3AB0C32AE9C808_OpenInst(Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* __this, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(method);
}
void Callback_Invoke_mAF4734E85D4D48894115F7AB5C3AB0C32AE9C808_OpenStatic(Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* __this, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(method);
}
IL2CPP_EXTERN_C  void DelegatePInvokeWrapper_Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377 (Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* __this, const RuntimeMethod* method)
{
	typedef void (DEFAULT_CALL *PInvokeFunc)();
	PInvokeFunc il2cppPInvokeFunc = reinterpret_cast<PInvokeFunc>(il2cpp_codegen_get_reverse_pinvoke_function_ptr(__this));
	il2cppPInvokeFunc();

}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Callback__ctor_m0356797B4424F5C13D2962B1E3E1DDD0CD8B9234 (Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 0;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&Callback_Invoke_mAF4734E85D4D48894115F7AB5C3AB0C32AE9C808_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		if (___0_object == NULL)
			il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
		__this->___invoke_impl = __this->___method_ptr;
		__this->___method_code = (intptr_t)__this->___m_target;
	}
	__this->___extra_arg = (intptr_t)&Callback_Invoke_mAF4734E85D4D48894115F7AB5C3AB0C32AE9C808_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Callback_Invoke_mAF4734E85D4D48894115F7AB5C3AB0C32AE9C808 (Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Callback_BeginInvoke_m153250D390B4DD4FD3C9CFC64565AA917D3CAA27 (Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* __this, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___0_callback, RuntimeObject* ___1_object, const RuntimeMethod* method) 
{
	void *__d_args[1] = {0};
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___0_callback, (RuntimeObject*)___1_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Callback_EndInvoke_m4F442FA84FCCEAEC4C97B26FCC81CF151748AA42 (Callback_t7B47DD9AFA7435379B928DCAE34ED3B1F82AA377* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlaybackMgr__ctor_mEC819B47D03CDBA188F0758D4E8D045F2E823941 (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlaybackMgr__ctor_mEC819B47D03CDBA188F0758D4E8D045F2E823941_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlaybackMgr__ctor_mEC819B47D03CDBA188F0758D4E8D045F2E823941_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 349));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 350));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 351));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 352));
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 352));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 353));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 354));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 355));
		intptr_t L_0;
		L_0 = VideoPlaybackMgr_Internal_Create_mFF19AB8170FAD9831FAEB264CF668869A6292575(NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 355));
		__this->___m_Ptr = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 356));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlaybackMgr_Dispose_mA234FB2994C6DD6E729F8CE29032A36A6399090E (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GC_t920F9CF6EBB7C787E5010A4352E1B587F356DC58_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlaybackMgr_Dispose_mA234FB2994C6DD6E729F8CE29032A36A6399090E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, VideoPlaybackMgr_Dispose_mA234FB2994C6DD6E729F8CE29032A36A6399090E_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 357));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 358));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 359));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 360));
		intptr_t L_0 = __this->___m_Ptr;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 361));
		bool L_1;
		L_1 = IntPtr_op_Inequality_m90EFC9C4CAD9A33E309F2DDF98EE4E1DD253637B(L_0, 0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 361));
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 362));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_002e;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 363));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 364));
		intptr_t L_3 = __this->___m_Ptr;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 365));
		VideoPlaybackMgr_Internal_Destroy_m2C276421DA1094DE0154D07C63059A489B2C9886(L_3, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 365));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 366));
		__this->___m_Ptr = 0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 367));
	}

IL_002e:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 368));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 369));
		il2cpp_codegen_runtime_class_init_inline(GC_t920F9CF6EBB7C787E5010A4352E1B587F356DC58_il2cpp_TypeInfo_var);
		GC_SuppressFinalize_m71815DBD5A0CD2EA1BE43317B08B7A14949EDC65(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 369));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VideoModule + 370));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR intptr_t VideoPlaybackMgr_Internal_Create_mFF19AB8170FAD9831FAEB264CF668869A6292575 (const RuntimeMethod* method) 
{
	typedef intptr_t (*VideoPlaybackMgr_Internal_Create_mFF19AB8170FAD9831FAEB264CF668869A6292575_ftn) ();
	static VideoPlaybackMgr_Internal_Create_mFF19AB8170FAD9831FAEB264CF668869A6292575_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlaybackMgr_Internal_Create_mFF19AB8170FAD9831FAEB264CF668869A6292575_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlaybackMgr::Internal_Create()");
	intptr_t icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlaybackMgr_Internal_Destroy_m2C276421DA1094DE0154D07C63059A489B2C9886 (intptr_t ___0_ptr, const RuntimeMethod* method) 
{
	typedef void (*VideoPlaybackMgr_Internal_Destroy_m2C276421DA1094DE0154D07C63059A489B2C9886_ftn) (intptr_t);
	static VideoPlaybackMgr_Internal_Destroy_m2C276421DA1094DE0154D07C63059A489B2C9886_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlaybackMgr_Internal_Destroy_m2C276421DA1094DE0154D07C63059A489B2C9886_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlaybackMgr::Internal_Destroy(System.IntPtr)");
	_il2cpp_icall_func(___0_ptr);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* VideoPlaybackMgr_CreateVideoPlayback_mC835018658220293B0D63CC51943E92EECED0D35 (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65* __this, String_t* ___0_fileName, MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* ___1_errorCallback, Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* ___2_readyCallback, Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* ___3_reachedEndCallback, bool ___4_splitAlpha, const RuntimeMethod* method) 
{
	typedef VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* (*VideoPlaybackMgr_CreateVideoPlayback_mC835018658220293B0D63CC51943E92EECED0D35_ftn) (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65*, String_t*, MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256*, Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD*, Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD*, bool);
	static VideoPlaybackMgr_CreateVideoPlayback_mC835018658220293B0D63CC51943E92EECED0D35_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlaybackMgr_CreateVideoPlayback_mC835018658220293B0D63CC51943E92EECED0D35_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlaybackMgr::CreateVideoPlayback(System.String,UnityEngineInternal.Video.VideoPlaybackMgr/MessageCallback,UnityEngineInternal.Video.VideoPlaybackMgr/Callback,UnityEngineInternal.Video.VideoPlaybackMgr/Callback,System.Boolean)");
	VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* icallRetVal = _il2cpp_icall_func(__this, ___0_fileName, ___1_errorCallback, ___2_readyCallback, ___3_reachedEndCallback, ___4_splitAlpha);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlaybackMgr_ReleaseVideoPlayback_mEBB34B94811658D6DC537E129552EB6C914EA5DF (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65* __this, VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F* ___0_playback, const RuntimeMethod* method) 
{
	typedef void (*VideoPlaybackMgr_ReleaseVideoPlayback_mEBB34B94811658D6DC537E129552EB6C914EA5DF_ftn) (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65*, VideoPlayback_t9CD19EFA2E8EA339078DE748487782A86BB3A29F*);
	static VideoPlaybackMgr_ReleaseVideoPlayback_mEBB34B94811658D6DC537E129552EB6C914EA5DF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlaybackMgr_ReleaseVideoPlayback_mEBB34B94811658D6DC537E129552EB6C914EA5DF_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlaybackMgr::ReleaseVideoPlayback(UnityEngineInternal.Video.VideoPlayback)");
	_il2cpp_icall_func(__this, ___0_playback);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t VideoPlaybackMgr_get_videoPlaybackCount_mCEAD72B9FA6CA33D6B6FFB9D89FBE5951AF169C8 (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65* __this, const RuntimeMethod* method) 
{
	typedef uint64_t (*VideoPlaybackMgr_get_videoPlaybackCount_mCEAD72B9FA6CA33D6B6FFB9D89FBE5951AF169C8_ftn) (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65*);
	static VideoPlaybackMgr_get_videoPlaybackCount_mCEAD72B9FA6CA33D6B6FFB9D89FBE5951AF169C8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlaybackMgr_get_videoPlaybackCount_mCEAD72B9FA6CA33D6B6FFB9D89FBE5951AF169C8_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlaybackMgr::get_videoPlaybackCount()");
	uint64_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlaybackMgr_Update_mFF1DF25FA75198589940A53A201D16BE42A31BED (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65* __this, const RuntimeMethod* method) 
{
	typedef void (*VideoPlaybackMgr_Update_mFF1DF25FA75198589940A53A201D16BE42A31BED_ftn) (VideoPlaybackMgr_t81DEECDC547CBB85322C9F81047C3216EEFE1D65*);
	static VideoPlaybackMgr_Update_mFF1DF25FA75198589940A53A201D16BE42A31BED_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlaybackMgr_Update_mFF1DF25FA75198589940A53A201D16BE42A31BED_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlaybackMgr::Update()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VideoPlaybackMgr_ProcessOSMainLoopMessagesForTesting_m91AF3CE72692E047FE5D4A0C0569782A3E9C7BE2 (const RuntimeMethod* method) 
{
	typedef void (*VideoPlaybackMgr_ProcessOSMainLoopMessagesForTesting_m91AF3CE72692E047FE5D4A0C0569782A3E9C7BE2_ftn) ();
	static VideoPlaybackMgr_ProcessOSMainLoopMessagesForTesting_m91AF3CE72692E047FE5D4A0C0569782A3E9C7BE2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VideoPlaybackMgr_ProcessOSMainLoopMessagesForTesting_m91AF3CE72692E047FE5D4A0C0569782A3E9C7BE2_ftn)il2cpp_codegen_resolve_icall ("UnityEngineInternal.Video.VideoPlaybackMgr::ProcessOSMainLoopMessagesForTesting()");
	_il2cpp_icall_func();
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void Callback_Invoke_m7432E1AC997A780F60056FA9047E4E18F92AFAF0_Multicast(Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* __this, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* currentDelegate = reinterpret_cast<Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void Callback_Invoke_m7432E1AC997A780F60056FA9047E4E18F92AFAF0_OpenInst(Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* __this, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(method);
}
void Callback_Invoke_m7432E1AC997A780F60056FA9047E4E18F92AFAF0_OpenStatic(Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* __this, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(method);
}
IL2CPP_EXTERN_C  void DelegatePInvokeWrapper_Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD (Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* __this, const RuntimeMethod* method)
{
	typedef void (DEFAULT_CALL *PInvokeFunc)();
	PInvokeFunc il2cppPInvokeFunc = reinterpret_cast<PInvokeFunc>(il2cpp_codegen_get_reverse_pinvoke_function_ptr(__this));
	il2cppPInvokeFunc();

}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Callback__ctor_m44310EB97EB6728B3D57A9DE439402069EDE6077 (Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 0;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&Callback_Invoke_m7432E1AC997A780F60056FA9047E4E18F92AFAF0_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		if (___0_object == NULL)
			il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
		__this->___invoke_impl = __this->___method_ptr;
		__this->___method_code = (intptr_t)__this->___m_target;
	}
	__this->___extra_arg = (intptr_t)&Callback_Invoke_m7432E1AC997A780F60056FA9047E4E18F92AFAF0_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Callback_Invoke_m7432E1AC997A780F60056FA9047E4E18F92AFAF0 (Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Callback_BeginInvoke_m0E16B1170894C3EC3F5BC4A4860FF6ADB1AF9426 (Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* __this, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___0_callback, RuntimeObject* ___1_object, const RuntimeMethod* method) 
{
	void *__d_args[1] = {0};
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___0_callback, (RuntimeObject*)___1_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Callback_EndInvoke_mED251E3436D1846F2166D667869B08C993E6C797 (Callback_t8D2FFD24EBCCF4D722878F63D33E1A629EF05BAD* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void MessageCallback_Invoke_m391111B84BDE2A8B208D7B775015923DA8DF90DA_Multicast(MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* __this, String_t* ___0_message, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* currentDelegate = reinterpret_cast<MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, String_t*, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_message, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void MessageCallback_Invoke_m391111B84BDE2A8B208D7B775015923DA8DF90DA_OpenInst(MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* __this, String_t* ___0_message, const RuntimeMethod* method)
{
	NullCheck(___0_message);
	typedef void (*FunctionPointerType) (String_t*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_message, method);
}
void MessageCallback_Invoke_m391111B84BDE2A8B208D7B775015923DA8DF90DA_OpenStatic(MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* __this, String_t* ___0_message, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (String_t*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_message, method);
}
IL2CPP_EXTERN_C  void DelegatePInvokeWrapper_MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256 (MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* __this, String_t* ___0_message, const RuntimeMethod* method)
{
	typedef void (DEFAULT_CALL *PInvokeFunc)(char*);
	PInvokeFunc il2cppPInvokeFunc = reinterpret_cast<PInvokeFunc>(il2cpp_codegen_get_reverse_pinvoke_function_ptr(__this));
	char* ____0_message_marshaled = NULL;
	____0_message_marshaled = il2cpp_codegen_marshal_string(___0_message);

	il2cppPInvokeFunc(____0_message_marshaled);

	il2cpp_codegen_marshal_free(____0_message_marshaled);
	____0_message_marshaled = NULL;

}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MessageCallback__ctor_m0BC04D432B6209D16BDB6500D492C7A08B051EDD (MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 1;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&MessageCallback_Invoke_m391111B84BDE2A8B208D7B775015923DA8DF90DA_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		bool isOpen = parameterCount == 0;
		if (isOpen)
		{
			__this->___invoke_impl = (intptr_t)&MessageCallback_Invoke_m391111B84BDE2A8B208D7B775015923DA8DF90DA_OpenInst;
		}
		else
		{
			if (___0_object == NULL)
				il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
			__this->___invoke_impl = __this->___method_ptr;
			__this->___method_code = (intptr_t)__this->___m_target;
		}
	}
	__this->___extra_arg = (intptr_t)&MessageCallback_Invoke_m391111B84BDE2A8B208D7B775015923DA8DF90DA_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MessageCallback_Invoke_m391111B84BDE2A8B208D7B775015923DA8DF90DA (MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* __this, String_t* ___0_message, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, String_t*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_message, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* MessageCallback_BeginInvoke_m3530276506A82E06D6F4ED3428E0C4AB14FB6C6D (MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* __this, String_t* ___0_message, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___1_callback, RuntimeObject* ___2_object, const RuntimeMethod* method) 
{
	void *__d_args[2] = {0};
	__d_args[0] = ___0_message;
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___1_callback, (RuntimeObject*)___2_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MessageCallback_EndInvoke_m1D5047E371017CC22F34AD8F2B7674CF7222B81F (MessageCallback_t10023C86A35DB5F05E5AC8D3BAE93F425717B256* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* AudioSampleProvider_get_owner_mC8461FD790AEC7BACE00C8A3F1DE4D00E08EF1AC_inline (AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AudioSampleProvider_get_owner_mC8461FD790AEC7BACE00C8A3F1DE4D00E08EF1AC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, AudioSampleProvider_get_owner_mC8461FD790AEC7BACE00C8A3F1DE4D00E08EF1AC_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AudioModule + 865));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AudioModule + 866));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AudioModule + 867));
		Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* L_0 = __this->___U3CownerU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6_inline (AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AudioSampleProvider_t602353124A2F6F2AEC38E56C3C21932344F712E2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, AudioSampleProvider_get_trackIndex_m6F250872A4CFCFB1806466578BAFB9B0ED4F0BA6_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AudioModule + 859));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_AudioModule + 860));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_AudioModule + 861));
		uint16_t L_0 = __this->___U3CtrackIndexU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void EventHandler_Invoke_m8A3288B6DF1FE2699A58EA22D0B830BB20E94E15_inline (EventHandler_tE1FA51515FC895E3A02E317912FBDCDCF390A411* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_source, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FrameReadyEventHandler_Invoke_m73FF71F81A620194421226D8A98C64FB82E7B235_inline (FrameReadyEventHandler_tDEDADF060B051C2953122274EBF3896AF48735E8* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, int64_t ___1_frameIdx, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, int64_t, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_source, ___1_frameIdx, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ErrorEventHandler_Invoke_m0601D855B85F5053A0A10A0166A8CE469D4672C5_inline (ErrorEventHandler_t6DD292ED79129AF67D909EDA603DA9DC5C762462* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, String_t* ___1_message, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, String_t*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_source, ___1_message, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TimeEventHandler_Invoke_m2327761543815FB7181FFF73E87FC5EC0F84349D_inline (TimeEventHandler_t8F77A92148F1A0F37AE8DE9CA7E25D90D001DC1E* __this, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3* ___0_source, double ___1_seconds, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, VideoPlayer_t48EA4A8117B822BC59590150DED9AD46C62F65D3*, double, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_source, ___1_seconds, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
