﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* BurstCompilerHelper_IsBurstEnabled_m8F3C6D0129D14359B51860FBA51933C4FE92F131_RuntimeMethod_var;



extern void BurstCompileAttribute_set_FloatMode_mFC4C13A636FAF57510757D42EA1017C1A3FA6580 (void);
extern void BurstCompileAttribute_set_FloatPrecision_m62685CD0A4F258FB8779A37BC01DAABB83DFD465 (void);
extern void BurstCompileAttribute_set_CompileSynchronously_mED8B25B60ABC1EA5327DE691DCE129C9BD34BD58 (void);
extern void BurstCompileAttribute_set_Options_mEC956014E83B4671F431159FE7D1DDD6BE5BC91E (void);
extern void BurstCompileAttribute__ctor_mFCB7FEAFCE1A2CE6A5268A4EA062E33E5472ABBE (void);
extern void BurstCompileAttribute__ctor_mBA3EAC7C435927F67F10F2D185F5CBE8F88DC3C6 (void);
extern void BurstCompiler_get_IsEnabled_m55FDBCB2279A83AC8926260034F870E3A11116C7 (void);
extern void BurstCompiler_CompileILPPMethod2_m545A8FC57B460871C1715F32DD601F2C1CA9C7FA (void);
extern void BurstCompiler_GetILPPMethodFunctionPointer2_m8C671F61D031A10FC46911AC94B57C1E58D1F567 (void);
extern void BurstCompiler_Compile_m0038D8F2B6CB3915CB12F71E15B14C7355BFC8EF (void);
extern void BurstCompiler_DummyMethod_m44E6D413356022A0F6BA962A31026BA4EE5FE95F (void);
extern void BurstCompiler__cctor_mA29CF2918E31D89BB314B5CC1AF842BE93E9EE6B (void);
extern void StaticTypeReinitAttribute__ctor_m8642643889129E11741654F66EE77046A2A7D1CB (void);
extern void BurstCompilerHelper_IsBurstEnabled_m8F3C6D0129D14359B51860FBA51933C4FE92F131 (void);
extern void BurstCompilerHelper_DiscardedMethod_mE9B27FDCAB7B17C7B7496ADACFDBB72E3F155F6B (void);
extern void BurstCompilerHelper_IsCompiledByBurst_m0239AE7BCAF7076EE75C46D528F04AC34F3761DD (void);
extern void BurstCompilerHelper__cctor_m2B57C7C8A7B5F4CEE1E1DE05C5FC63C10AE37FD3 (void);
extern void IsBurstEnabledDelegate__ctor_m675CBAB9E803A7723AB3601DEB086E706E98A86E (void);
extern void IsBurstEnabledDelegate_Invoke_m9FA44E7FDC323DE7DF1232200DED8C89A67D7F65 (void);
extern void FakeDelegate__ctor_mC2654CB88A21F64F4C25E02A0C89854E2F74484B (void);
extern void FakeDelegate_get_Method_m36F3C3DAC1377B07AF18BAC6EDF28F3FAE5BA828 (void);
extern void U3CU3Ec__cctor_m8FF612FA8632F867C2CA577D7FF7A080320568BF (void);
extern void U3CU3Ec__ctor_mEC9179CC84E1FA4BB4AB4B39A87C134F481976C9 (void);
extern void U3CU3Ec_U3CCompileU3Eb__22_0_m2326454433F78E8E68A7EB9191933F393BDB0401 (void);
extern void BurstCompilerOptions__ctor_m644EA41CAFD4F89CE36074DBD77BAC761C122285 (void);
extern void BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4 (void);
extern void BurstCompilerOptions_get_EnableBurstCompilation_mE10DF1EAAF0A56906D9784498FD48EAC1B012CD0 (void);
extern void BurstCompilerOptions_set_EnableBurstCompilation_mEDD4E93926B3E03A8E38CA9D483D4E4FD649D849 (void);
extern void BurstCompilerOptions_set_EnableBurstSafetyChecks_m2AB857BC80AE1546031305C47F88ADB147A8BB83 (void);
extern void BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69 (void);
extern void BurstCompilerOptions_TryGetAttribute_m4EE3F62FAF2A482C444060E1CCB480711CC377F8 (void);
extern void BurstCompilerOptions_GetBurstCompileAttribute_m9383E7E419C41B6BD078452FC1E2EF94A9AD2972 (void);
extern void BurstCompilerOptions_HasBurstCompileAttribute_mC68CA53F4A77780A30D34E895B120188F31F7826 (void);
extern void BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7 (void);
extern void BurstCompilerOptions_MaybeTriggerRecompilation_mBE68BE4083665B2DE194184223A6BF14CA7F3821 (void);
extern void BurstCompilerOptions__cctor_m00F05309A6D0721099EBAF2FB553AD1A409815F6 (void);
extern void BurstCompilerOptions_CheckIsSecondaryUnityProcess_mAA7A85682C937E5FF55B6B9ADCF0F1789F889E67 (void);
extern void BurstRuntime_RuntimeLog_m01D9192CF1CE1F0113F51431413D5F002C82E12A (void);
extern void BurstRuntime_PreventRequiredAttributeStrip_mEB29E8C73D86AC18C902D6CA4B85C9D1DC0DB540 (void);
extern void BurstRuntime_Log_mACD9C0A258B393532ED8AE9DB127D494C14D0E88 (void);
extern void PreserveAttribute__ctor_m73E16FAB2119900D63EE60E6A868357D44E175F5 (void);
extern void BurstString_CopyFixedString_m5C7937A0D221B27A3D5FE9C1021B2210A7E72A16 (void);
extern void BurstString_Format_m5B430D57A65E74E0921325EC12E2920FACE2B684 (void);
extern void BurstString_Format_mEC129A0C1267C5438D13D9B8DA5BE80C9C6D3B8B (void);
extern void BurstString_Format_m77916B0A75CB28DF9F0BD6F32290D31BB24C7D4C (void);
extern void BurstString_Format_m8BCCCB4132CE427768D9A118E49B3F1F6C222102 (void);
extern void BurstString_Format_m19E81CEC5B4BA84C250AE5BAADC37D414E736730 (void);
extern void BurstString_Format_mF3FC2B176298B24C25FBF6DA92E700174C318000 (void);
extern void BurstString_Format_m234EB67007839F6D88BD31306502FB35A9F06FE1 (void);
extern void BurstString_Format_m69268960549C3B448843D0EB215B43DE6BFB75CE (void);
extern void BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844 (void);
extern void BurstString_Format_m23EDDB41EF95146DB17FED537050D7AC3A6901B6 (void);
extern void BurstString_Format_m4F5213B5469A6BDEEAB4B678F771A6F32CB952E7 (void);
extern void BurstString_Format_m2B7D17E527F80FA75BBE1D5B8C58C3B929B6664D (void);
extern void BurstString_Format_mEB0F69187C05D4543A5FF23A4E8E7A8DC27745A5 (void);
extern void BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33 (void);
extern void BurstString_GetLengthIntegerToString_m7C848D6F1F8062C53DDBCF15BC3C48492B1D6772 (void);
extern void BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8 (void);
extern void BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673 (void);
extern void BurstString_FormatDecimalOrHexadecimal_mA06BC7EC5DFAC150C462EBDD98CC067917E468AF (void);
extern void BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313 (void);
extern void BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37 (void);
extern void BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0 (void);
extern void BurstString_GetLengthForFormatGeneral_m8C803B634ACAA22001B49BEFBB5AB9CE7BD69766 (void);
extern void BurstString_FormatGeneral_m6BD2A28E369BBBF4444ED8D8A71EA6641A7DBCD1 (void);
extern void BurstString_RoundNumber_m524D9772E74FA38A0C43453F17AB2C7BAAB004E4 (void);
extern void BurstString_ShouldRoundUp_m409E5BBC77EF196F3CCAC9B2AFF01225E2119464 (void);
extern void BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54 (void);
extern void BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1 (void);
extern void BurstString_BigInt_Add_m4E1C5A27B4D6168D2967BF79174DA2A04A07669E (void);
extern void BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF (void);
extern void BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9 (void);
extern void BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3 (void);
extern void BurstString_BigInt_Multiply_m80C42811355207D0CD9E4E14BB916F0242D44FDF (void);
extern void BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C (void);
extern void BurstString_BigInt_Multiply2_m45D9B179615B4A6BAD47C2EAE92AEDE7A2406252 (void);
extern void BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E (void);
extern void BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43 (void);
extern void BurstString_BigInt_Pow10_mE53CE39D44AABA6924D6544F12E564EC2DCFE642 (void);
extern void BurstString_BigInt_MultiplyPow10_mA62F7C4D0BC220B200E0AF031CEA586C59E1EEBD (void);
extern void BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A (void);
extern void BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9 (void);
extern void BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583 (void);
extern void BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE (void);
extern void BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979 (void);
extern void BurstString_ConvertFloatToString_m31A31291376EE1C7AA2DFA26573312B25E0DDCDA (void);
extern void BurstString_ConvertDoubleToString_m5B4644F134166CA236077075A11108590892EDD0 (void);
extern void BurstString__cctor_m7DAF55C23F1F9D98FC9F78D057E3730166E28B78 (void);
extern void PreserveAttribute__ctor_mBA1653B32D31972033C043A55588458B03F262B1 (void);
extern void NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141 (void);
extern void NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846 (void);
extern void FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055 (void);
extern void FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3 (void);
extern void FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789 (void);
extern void FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488 (void);
extern void tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7 (void);
extern void tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2 (void);
extern void tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8 (void);
extern void tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14 (void);
extern void tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233 (void);
extern void tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388 (void);
extern void tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6 (void);
extern void tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF (void);
extern void tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B (void);
extern void tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60 (void);
extern void tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C (void);
extern void SharedStatic_GetOrCreateSharedStaticInternal_m9850783202F2E2DCA43597CD97C129C683D6FEBD (void);
extern void PreserveAttribute__ctor_m5C7C403F74E9EAEB24409A43B4EB60B4A161AB0F (void);
extern void Common_umul128_m6BE762AD1B87296A151EDD918A0802E9FB0846B7 (void);
extern void V64DebugView__ctor_m8B93FD41843CFE85E56FC106908748A512866D1A (void);
extern void V64DebugView_get_Byte_mE7321132F0F960DA5F4CC5F3C8C4D9590F36DE07 (void);
extern void V64DebugView_get_SByte_mA1DE53CFFD29D00D1A6528CE3E0B8F0B3F72D67A (void);
extern void V64DebugView_get_UShort_m4C3D69D63F19D810D57037A9995F13DA774AF073 (void);
extern void V64DebugView_get_SShort_mAE042049E9C8FE8E64B0A6137925E52B9B9F8C67 (void);
extern void V64DebugView_get_UInt_mE120B248349776B5E95E087918250943D7F37E78 (void);
extern void V64DebugView_get_SInt_m95264A7FEB41EE36E88D59B41F5E9883905BE0A9 (void);
extern void V64DebugView_get_Float_m188E7E0F9CB70BD06CA2CFCA7553925383BB4B57 (void);
extern void V64DebugView_get_SLong_m5AE3B8B83A826AB95DC13079E5F63733821C47F6 (void);
extern void V64DebugView_get_ULong_m6B56894B5D3051DD10F1032933E9AB33407FED9D (void);
extern void V64DebugView_get_Double_mA4EF50063B9C4EB7AE40EB8DED550BA69246B222 (void);
extern void V128DebugView__ctor_mC42F6FFDF54C60C5A059034BAB8BB5E10E038693 (void);
extern void V128DebugView_get_Byte_m41DAE52D58F88364298B4821CBD5F8553314D4DD (void);
extern void V128DebugView_get_SByte_mEF603AAD6E9C28351548B61DFA501711D093EB24 (void);
extern void V128DebugView_get_UShort_m08A2C3357E1D21BED05A9CD3792365A71ED87205 (void);
extern void V128DebugView_get_SShort_m0ECA44D738B5735389A60F057FE18E8197EDD22A (void);
extern void V128DebugView_get_UInt_m5E2CB21FBDD5A6DAF84D67E70F58235EC0FC6AF6 (void);
extern void V128DebugView_get_SInt_m80A4E5E0F0951876499F460205296B37A044DF92 (void);
extern void V128DebugView_get_Float_m4349267DC73E577EE2C0DA71DE0460082F8CFC7C (void);
extern void V128DebugView_get_SLong_m799B8A54511F2E4CCB22A0F8A4047997EF8A9B4B (void);
extern void V128DebugView_get_ULong_m712AFB4F0D411802F483802463DDED5FDB024A98 (void);
extern void V128DebugView_get_Double_m6608D5731324AACA6FFCB159D1ADD73C7476CFD8 (void);
extern void V256DebugView__ctor_mD72189AE846873932A2B7DA4A4ADB0F137A80719 (void);
extern void V256DebugView_get_Byte_m30250D6D408C21BBA598F32747B4E8920CCB1DE3 (void);
extern void V256DebugView_get_SByte_m2007C72F3A25CE7C9416516F0BD4095A248E861B (void);
extern void V256DebugView_get_UShort_m035F4653F8E38CB40257967EFEBFE1E9A48E8234 (void);
extern void V256DebugView_get_SShort_m85ADD78029A67E2352678C2FD4BE5274F4DFADE7 (void);
extern void V256DebugView_get_UInt_mB41C94D3A0BF8F26107008EC6EA33AA20ADF6141 (void);
extern void V256DebugView_get_SInt_mF7DEA4A87AA028E0C722CB32061827B9F3D6044B (void);
extern void V256DebugView_get_Float_m6BB84136BDD10A1AB3F758F448837BA8A013ECFF (void);
extern void V256DebugView_get_SLong_mEFC8C9382C072366371F2CA2D5EEB140A87AB963 (void);
extern void V256DebugView_get_ULong_m749636BFF56ED53852F7685BB69917D137061DA9 (void);
extern void V256DebugView_get_Double_m3232A8A1120069BC0D9FA04039D0C1431E7E6E72 (void);
extern void v256__ctor_m267402531F6CFFB7F6B60508FF475D5DF65B20EE (void);
extern void v256__ctor_mA9B9CC971837A7F271235EFCFFEA5D12E2E67E4A (void);
extern void Avx_mm256_load_ps_mDEC29DE0AC8C7A62975D63B512D5FD825D83E749 (void);
extern void Avx_mm256_store_ps_mAA874350740C462A1059A066894E57E93D7B6697 (void);
extern void Avx_mm256_loadu_si256_m63575B1FA1C174A5D442A2F53E7A1708AC7E7F31 (void);
extern void Avx_mm256_storeu_si256_m52989726D29436BCF95D32D4319C6CBDF31D11D4 (void);
extern void Avx_mm256_set1_epi32_m326DB72B5F59FC760340BFD50C022F573F032D3C (void);
extern void Avx2_get_IsAvx2Supported_mDE53DA491B5B96753F6558B1DF3C5D5A9BB1ADB4 (void);
extern void Avx2_mm256_xor_si256_mDD46C306F796DE4284677678DB751CC4AD998614 (void);
extern void Avx2_mm256_add_epi64_m0312FD16FB80EFA4C0B72A8FBB32C2735285F9B8 (void);
extern void Avx2_mm256_mul_epu32_mBA8AE42AD7D5F226187ECD9FD132F6EC138C6512 (void);
extern void Avx2_mm256_slli_epi64_m81DB24BED37FB2D8CBDDC0B6B655F920642BB559 (void);
extern void Avx2_mm256_srli_epi64_m629ADF7A5EBCAC5A84A2CA773EE8F18FE66DDD58 (void);
extern void Avx2_mm256_shuffle_epi32_m226985CA8797C3192874F3822295A40BDEEA850B (void);
extern void Sse_SHUFFLE_m80B322C7F945F0225AFA5E2995108547DA36391E (void);
extern void Sse2_add_epi64_m7F48D1953DDBDBF38CA494BABE0A3390BA6C86BF (void);
extern void Sse2_mul_epu32_mCD9FF71C6DA28E454D1EBE3249DC4F9E99DC11DB (void);
extern void Sse2_slli_epi64_mB984CF9AA56B47FDD860A6C0D9DCC5CFEC420B4D (void);
extern void Sse2_srli_epi64_m2B154276738045C80B2C8857CFDB232487262CFD (void);
extern void Sse2_xor_si128_m54213FFE7B2D891507E00B3395DB3AC25820A8A9 (void);
extern void Sse2_shuffle_epi32_m1EA2B8A82D93417EA3B3789381D8117DC635F35F (void);
extern void AssumeRangeAttribute__ctor_m079EDF7E5EEC31A177E1B9825784AB0C8192ECE7 (void);
static Il2CppMethodPointer s_methodPointers[169] = 
{
	BurstCompileAttribute_set_FloatMode_mFC4C13A636FAF57510757D42EA1017C1A3FA6580,
	BurstCompileAttribute_set_FloatPrecision_m62685CD0A4F258FB8779A37BC01DAABB83DFD465,
	BurstCompileAttribute_set_CompileSynchronously_mED8B25B60ABC1EA5327DE691DCE129C9BD34BD58,
	BurstCompileAttribute_set_Options_mEC956014E83B4671F431159FE7D1DDD6BE5BC91E,
	BurstCompileAttribute__ctor_mFCB7FEAFCE1A2CE6A5268A4EA062E33E5472ABBE,
	BurstCompileAttribute__ctor_mBA3EAC7C435927F67F10F2D185F5CBE8F88DC3C6,
	BurstCompiler_get_IsEnabled_m55FDBCB2279A83AC8926260034F870E3A11116C7,
	BurstCompiler_CompileILPPMethod2_m545A8FC57B460871C1715F32DD601F2C1CA9C7FA,
	BurstCompiler_GetILPPMethodFunctionPointer2_m8C671F61D031A10FC46911AC94B57C1E58D1F567,
	BurstCompiler_Compile_m0038D8F2B6CB3915CB12F71E15B14C7355BFC8EF,
	BurstCompiler_DummyMethod_m44E6D413356022A0F6BA962A31026BA4EE5FE95F,
	BurstCompiler__cctor_mA29CF2918E31D89BB314B5CC1AF842BE93E9EE6B,
	StaticTypeReinitAttribute__ctor_m8642643889129E11741654F66EE77046A2A7D1CB,
	BurstCompilerHelper_IsBurstEnabled_m8F3C6D0129D14359B51860FBA51933C4FE92F131,
	BurstCompilerHelper_DiscardedMethod_mE9B27FDCAB7B17C7B7496ADACFDBB72E3F155F6B,
	BurstCompilerHelper_IsCompiledByBurst_m0239AE7BCAF7076EE75C46D528F04AC34F3761DD,
	BurstCompilerHelper__cctor_m2B57C7C8A7B5F4CEE1E1DE05C5FC63C10AE37FD3,
	IsBurstEnabledDelegate__ctor_m675CBAB9E803A7723AB3601DEB086E706E98A86E,
	IsBurstEnabledDelegate_Invoke_m9FA44E7FDC323DE7DF1232200DED8C89A67D7F65,
	FakeDelegate__ctor_mC2654CB88A21F64F4C25E02A0C89854E2F74484B,
	FakeDelegate_get_Method_m36F3C3DAC1377B07AF18BAC6EDF28F3FAE5BA828,
	U3CU3Ec__cctor_m8FF612FA8632F867C2CA577D7FF7A080320568BF,
	U3CU3Ec__ctor_mEC9179CC84E1FA4BB4AB4B39A87C134F481976C9,
	U3CU3Ec_U3CCompileU3Eb__22_0_m2326454433F78E8E68A7EB9191933F393BDB0401,
	BurstCompilerOptions__ctor_m644EA41CAFD4F89CE36074DBD77BAC761C122285,
	BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4,
	BurstCompilerOptions_get_EnableBurstCompilation_mE10DF1EAAF0A56906D9784498FD48EAC1B012CD0,
	BurstCompilerOptions_set_EnableBurstCompilation_mEDD4E93926B3E03A8E38CA9D483D4E4FD649D849,
	BurstCompilerOptions_set_EnableBurstSafetyChecks_m2AB857BC80AE1546031305C47F88ADB147A8BB83,
	BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69,
	BurstCompilerOptions_TryGetAttribute_m4EE3F62FAF2A482C444060E1CCB480711CC377F8,
	BurstCompilerOptions_GetBurstCompileAttribute_m9383E7E419C41B6BD078452FC1E2EF94A9AD2972,
	BurstCompilerOptions_HasBurstCompileAttribute_mC68CA53F4A77780A30D34E895B120188F31F7826,
	BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7,
	BurstCompilerOptions_MaybeTriggerRecompilation_mBE68BE4083665B2DE194184223A6BF14CA7F3821,
	BurstCompilerOptions__cctor_m00F05309A6D0721099EBAF2FB553AD1A409815F6,
	BurstCompilerOptions_CheckIsSecondaryUnityProcess_mAA7A85682C937E5FF55B6B9ADCF0F1789F889E67,
	BurstRuntime_RuntimeLog_m01D9192CF1CE1F0113F51431413D5F002C82E12A,
	BurstRuntime_PreventRequiredAttributeStrip_mEB29E8C73D86AC18C902D6CA4B85C9D1DC0DB540,
	BurstRuntime_Log_mACD9C0A258B393532ED8AE9DB127D494C14D0E88,
	PreserveAttribute__ctor_m73E16FAB2119900D63EE60E6A868357D44E175F5,
	BurstString_CopyFixedString_m5C7937A0D221B27A3D5FE9C1021B2210A7E72A16,
	BurstString_Format_m5B430D57A65E74E0921325EC12E2920FACE2B684,
	BurstString_Format_mEC129A0C1267C5438D13D9B8DA5BE80C9C6D3B8B,
	BurstString_Format_m77916B0A75CB28DF9F0BD6F32290D31BB24C7D4C,
	BurstString_Format_m8BCCCB4132CE427768D9A118E49B3F1F6C222102,
	BurstString_Format_m19E81CEC5B4BA84C250AE5BAADC37D414E736730,
	BurstString_Format_mF3FC2B176298B24C25FBF6DA92E700174C318000,
	BurstString_Format_m234EB67007839F6D88BD31306502FB35A9F06FE1,
	BurstString_Format_m69268960549C3B448843D0EB215B43DE6BFB75CE,
	BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844,
	BurstString_Format_m23EDDB41EF95146DB17FED537050D7AC3A6901B6,
	BurstString_Format_m4F5213B5469A6BDEEAB4B678F771A6F32CB952E7,
	BurstString_Format_m2B7D17E527F80FA75BBE1D5B8C58C3B929B6664D,
	BurstString_Format_mEB0F69187C05D4543A5FF23A4E8E7A8DC27745A5,
	BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33,
	BurstString_GetLengthIntegerToString_m7C848D6F1F8062C53DDBCF15BC3C48492B1D6772,
	BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8,
	BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673,
	BurstString_FormatDecimalOrHexadecimal_mA06BC7EC5DFAC150C462EBDD98CC067917E468AF,
	BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313,
	BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37,
	BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0,
	BurstString_GetLengthForFormatGeneral_m8C803B634ACAA22001B49BEFBB5AB9CE7BD69766,
	BurstString_FormatGeneral_m6BD2A28E369BBBF4444ED8D8A71EA6641A7DBCD1,
	BurstString_RoundNumber_m524D9772E74FA38A0C43453F17AB2C7BAAB004E4,
	BurstString_ShouldRoundUp_m409E5BBC77EF196F3CCAC9B2AFF01225E2119464,
	BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54,
	BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1,
	BurstString_BigInt_Add_m4E1C5A27B4D6168D2967BF79174DA2A04A07669E,
	BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF,
	BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9,
	BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3,
	BurstString_BigInt_Multiply_m80C42811355207D0CD9E4E14BB916F0242D44FDF,
	BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C,
	BurstString_BigInt_Multiply2_m45D9B179615B4A6BAD47C2EAE92AEDE7A2406252,
	BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E,
	BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43,
	BurstString_BigInt_Pow10_mE53CE39D44AABA6924D6544F12E564EC2DCFE642,
	BurstString_BigInt_MultiplyPow10_mA62F7C4D0BC220B200E0AF031CEA586C59E1EEBD,
	BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A,
	BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9,
	BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583,
	BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE,
	BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979,
	BurstString_ConvertFloatToString_m31A31291376EE1C7AA2DFA26573312B25E0DDCDA,
	BurstString_ConvertDoubleToString_m5B4644F134166CA236077075A11108590892EDD0,
	BurstString__cctor_m7DAF55C23F1F9D98FC9F78D057E3730166E28B78,
	PreserveAttribute__ctor_mBA1653B32D31972033C043A55588458B03F262B1,
	NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141,
	NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846,
	FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055,
	FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3,
	FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789,
	FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488,
	tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7,
	tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2,
	tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8,
	tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14,
	tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233,
	tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388,
	tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6,
	tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF,
	tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B,
	tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60,
	tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SharedStatic_GetOrCreateSharedStaticInternal_m9850783202F2E2DCA43597CD97C129C683D6FEBD,
	PreserveAttribute__ctor_m5C7C403F74E9EAEB24409A43B4EB60B4A161AB0F,
	Common_umul128_m6BE762AD1B87296A151EDD918A0802E9FB0846B7,
	V64DebugView__ctor_m8B93FD41843CFE85E56FC106908748A512866D1A,
	V64DebugView_get_Byte_mE7321132F0F960DA5F4CC5F3C8C4D9590F36DE07,
	V64DebugView_get_SByte_mA1DE53CFFD29D00D1A6528CE3E0B8F0B3F72D67A,
	V64DebugView_get_UShort_m4C3D69D63F19D810D57037A9995F13DA774AF073,
	V64DebugView_get_SShort_mAE042049E9C8FE8E64B0A6137925E52B9B9F8C67,
	V64DebugView_get_UInt_mE120B248349776B5E95E087918250943D7F37E78,
	V64DebugView_get_SInt_m95264A7FEB41EE36E88D59B41F5E9883905BE0A9,
	V64DebugView_get_Float_m188E7E0F9CB70BD06CA2CFCA7553925383BB4B57,
	V64DebugView_get_SLong_m5AE3B8B83A826AB95DC13079E5F63733821C47F6,
	V64DebugView_get_ULong_m6B56894B5D3051DD10F1032933E9AB33407FED9D,
	V64DebugView_get_Double_mA4EF50063B9C4EB7AE40EB8DED550BA69246B222,
	V128DebugView__ctor_mC42F6FFDF54C60C5A059034BAB8BB5E10E038693,
	V128DebugView_get_Byte_m41DAE52D58F88364298B4821CBD5F8553314D4DD,
	V128DebugView_get_SByte_mEF603AAD6E9C28351548B61DFA501711D093EB24,
	V128DebugView_get_UShort_m08A2C3357E1D21BED05A9CD3792365A71ED87205,
	V128DebugView_get_SShort_m0ECA44D738B5735389A60F057FE18E8197EDD22A,
	V128DebugView_get_UInt_m5E2CB21FBDD5A6DAF84D67E70F58235EC0FC6AF6,
	V128DebugView_get_SInt_m80A4E5E0F0951876499F460205296B37A044DF92,
	V128DebugView_get_Float_m4349267DC73E577EE2C0DA71DE0460082F8CFC7C,
	V128DebugView_get_SLong_m799B8A54511F2E4CCB22A0F8A4047997EF8A9B4B,
	V128DebugView_get_ULong_m712AFB4F0D411802F483802463DDED5FDB024A98,
	V128DebugView_get_Double_m6608D5731324AACA6FFCB159D1ADD73C7476CFD8,
	V256DebugView__ctor_mD72189AE846873932A2B7DA4A4ADB0F137A80719,
	V256DebugView_get_Byte_m30250D6D408C21BBA598F32747B4E8920CCB1DE3,
	V256DebugView_get_SByte_m2007C72F3A25CE7C9416516F0BD4095A248E861B,
	V256DebugView_get_UShort_m035F4653F8E38CB40257967EFEBFE1E9A48E8234,
	V256DebugView_get_SShort_m85ADD78029A67E2352678C2FD4BE5274F4DFADE7,
	V256DebugView_get_UInt_mB41C94D3A0BF8F26107008EC6EA33AA20ADF6141,
	V256DebugView_get_SInt_mF7DEA4A87AA028E0C722CB32061827B9F3D6044B,
	V256DebugView_get_Float_m6BB84136BDD10A1AB3F758F448837BA8A013ECFF,
	V256DebugView_get_SLong_mEFC8C9382C072366371F2CA2D5EEB140A87AB963,
	V256DebugView_get_ULong_m749636BFF56ED53852F7685BB69917D137061DA9,
	V256DebugView_get_Double_m3232A8A1120069BC0D9FA04039D0C1431E7E6E72,
	v256__ctor_m267402531F6CFFB7F6B60508FF475D5DF65B20EE,
	v256__ctor_mA9B9CC971837A7F271235EFCFFEA5D12E2E67E4A,
	Avx_mm256_load_ps_mDEC29DE0AC8C7A62975D63B512D5FD825D83E749,
	Avx_mm256_store_ps_mAA874350740C462A1059A066894E57E93D7B6697,
	Avx_mm256_loadu_si256_m63575B1FA1C174A5D442A2F53E7A1708AC7E7F31,
	Avx_mm256_storeu_si256_m52989726D29436BCF95D32D4319C6CBDF31D11D4,
	Avx_mm256_set1_epi32_m326DB72B5F59FC760340BFD50C022F573F032D3C,
	Avx2_get_IsAvx2Supported_mDE53DA491B5B96753F6558B1DF3C5D5A9BB1ADB4,
	Avx2_mm256_xor_si256_mDD46C306F796DE4284677678DB751CC4AD998614,
	Avx2_mm256_add_epi64_m0312FD16FB80EFA4C0B72A8FBB32C2735285F9B8,
	Avx2_mm256_mul_epu32_mBA8AE42AD7D5F226187ECD9FD132F6EC138C6512,
	Avx2_mm256_slli_epi64_m81DB24BED37FB2D8CBDDC0B6B655F920642BB559,
	Avx2_mm256_srli_epi64_m629ADF7A5EBCAC5A84A2CA773EE8F18FE66DDD58,
	Avx2_mm256_shuffle_epi32_m226985CA8797C3192874F3822295A40BDEEA850B,
	Sse_SHUFFLE_m80B322C7F945F0225AFA5E2995108547DA36391E,
	Sse2_add_epi64_m7F48D1953DDBDBF38CA494BABE0A3390BA6C86BF,
	Sse2_mul_epu32_mCD9FF71C6DA28E454D1EBE3249DC4F9E99DC11DB,
	Sse2_slli_epi64_mB984CF9AA56B47FDD860A6C0D9DCC5CFEC420B4D,
	Sse2_srli_epi64_m2B154276738045C80B2C8857CFDB232487262CFD,
	Sse2_xor_si128_m54213FFE7B2D891507E00B3395DB3AC25820A8A9,
	Sse2_shuffle_epi32_m1EA2B8A82D93417EA3B3789381D8117DC635F35F,
	AssumeRangeAttribute__ctor_m079EDF7E5EEC31A177E1B9825784AB0C8192ECE7,
};
extern void NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141_AdjustorThunk (void);
extern void NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_AdjustorThunk (void);
extern void FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055_AdjustorThunk (void);
extern void FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3_AdjustorThunk (void);
extern void FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789_AdjustorThunk (void);
extern void FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488_AdjustorThunk (void);
extern void tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_AdjustorThunk (void);
extern void tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2_AdjustorThunk (void);
extern void tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8_AdjustorThunk (void);
extern void tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14_AdjustorThunk (void);
extern void tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233_AdjustorThunk (void);
extern void tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388_AdjustorThunk (void);
extern void tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6_AdjustorThunk (void);
extern void tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF_AdjustorThunk (void);
extern void tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B_AdjustorThunk (void);
extern void tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60_AdjustorThunk (void);
extern void tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C_AdjustorThunk (void);
extern void v256__ctor_m267402531F6CFFB7F6B60508FF475D5DF65B20EE_AdjustorThunk (void);
extern void v256__ctor_mA9B9CC971837A7F271235EFCFFEA5D12E2E67E4A_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[19] = 
{
	{ 0x0600005A, NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141_AdjustorThunk },
	{ 0x0600005B, NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_AdjustorThunk },
	{ 0x0600005C, FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055_AdjustorThunk },
	{ 0x0600005D, FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3_AdjustorThunk },
	{ 0x0600005E, FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789_AdjustorThunk },
	{ 0x0600005F, FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488_AdjustorThunk },
	{ 0x06000060, tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_AdjustorThunk },
	{ 0x06000061, tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2_AdjustorThunk },
	{ 0x06000062, tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8_AdjustorThunk },
	{ 0x06000063, tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14_AdjustorThunk },
	{ 0x06000064, tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233_AdjustorThunk },
	{ 0x06000065, tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388_AdjustorThunk },
	{ 0x06000066, tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6_AdjustorThunk },
	{ 0x06000067, tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF_AdjustorThunk },
	{ 0x06000068, tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B_AdjustorThunk },
	{ 0x06000069, tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60_AdjustorThunk },
	{ 0x0600006A, tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C_AdjustorThunk },
	{ 0x06000094, v256__ctor_m267402531F6CFFB7F6B60508FF475D5DF65B20EE_AdjustorThunk },
	{ 0x06000095, v256__ctor_mA9B9CC971837A7F271235EFCFFEA5D12E2E67E4A_AdjustorThunk },
};
static const int32_t s_InvokerIndices[169] = 
{
	3852,
	3852,
	3807,
	3881,
	4364,
	2734,
	8993,
	8446,
	6267,
	5616,
	9089,
	9089,
	3881,
	8993,
	8867,
	8220,
	9089,
	2798,
	4168,
	3881,
	4250,
	9089,
	4364,
	3185,
	3807,
	4168,
	4168,
	3807,
	3807,
	4250,
	7252,
	8505,
	8220,
	4364,
	4364,
	9089,
	8993,
	6079,
	9089,
	6079,
	4364,
	6079,
	5071,
	5472,
	5464,
	5463,
	5474,
	5463,
	5474,
	5475,
	5476,
	5471,
	5466,
	5467,
	5469,
	5477,
	6504,
	5470,
	5072,
	5070,
	7224,
	5164,
	5164,
	7462,
	5070,
	6860,
	6307,
	8791,
	7461,
	6845,
	6845,
	6845,
	6845,
	6852,
	7894,
	8867,
	8867,
	8942,
	7904,
	6852,
	7904,
	7806,
	7904,
	4569,
	5074,
	5473,
	5465,
	9089,
	4364,
	857,
	4151,
	1316,
	4168,
	4216,
	4250,
	4216,
	3632,
	4168,
	3974,
	3973,
	4168,
	4350,
	4350,
	4168,
	4350,
	4351,
	0,
	0,
	0,
	0,
	0,
	5615,
	4364,
	6817,
	3997,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	3995,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	3996,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	3852,
	2909,
	8938,
	7908,
	8938,
	7908,
	8939,
	8993,
	8074,
	8074,
	8074,
	8073,
	8073,
	8073,
	5792,
	8072,
	8072,
	8071,
	8071,
	8072,
	8071,
	2762,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[1] = 
{
	{ 0x0600000E, 28,  (void**)&BurstCompilerHelper_IsBurstEnabled_m8F3C6D0129D14359B51860FBA51933C4FE92F131_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x02000019, { 0, 3 } },
	{ 0x0200001A, { 3, 5 } },
};
extern const uint32_t g_rgctx_FunctionPointer_1_t0666C00338C9DBCF4C31C1B1326ED43190DE0F38;
extern const uint32_t g_rgctx_Marshal_GetDelegateForFunctionPointer_TisT_t9E37FA2330E4A886B47120B954AAD7D9426B8783_mEA087B9A129C0AB2D73817CF23AC8B3121787C3C;
extern const uint32_t g_rgctx_T_t9E37FA2330E4A886B47120B954AAD7D9426B8783;
extern const uint32_t g_rgctx_SharedStatic_1_tB929B3357445BF112CFE0DA3DACBBEEAC8749C22;
extern const uint32_t g_rgctx_Unsafe_AsRef_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_mEE7B8BA32C960B9A36668D1A55993B6596E7B11A;
extern const uint32_t g_rgctx_TU26_t2BAB852B77A3158AA79460B09AA152D26C15E11E;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_m2A4C0FE147D438A6E478B7DE3EFD0FA088D18428;
extern const uint32_t g_rgctx_SharedStatic_1__ctor_m8C24499DF79560507F2AFDB01D8E2DFE40C5A86C;
static const Il2CppRGCTXDefinition s_rgctxValues[8] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FunctionPointer_1_t0666C00338C9DBCF4C31C1B1326ED43190DE0F38 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Marshal_GetDelegateForFunctionPointer_TisT_t9E37FA2330E4A886B47120B954AAD7D9426B8783_mEA087B9A129C0AB2D73817CF23AC8B3121787C3C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9E37FA2330E4A886B47120B954AAD7D9426B8783 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SharedStatic_1_tB929B3357445BF112CFE0DA3DACBBEEAC8749C22 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unsafe_AsRef_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_mEE7B8BA32C960B9A36668D1A55993B6596E7B11A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t2BAB852B77A3158AA79460B09AA152D26C15E11E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_m2A4C0FE147D438A6E478B7DE3EFD0FA088D18428 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1__ctor_m8C24499DF79560507F2AFDB01D8E2DFE40C5A86C },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnity_Burst;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Burst_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Burst_CodeGenModule = 
{
	"Unity.Burst.dll",
	169,
	s_methodPointers,
	19,
	s_adjustorThunks,
	s_InvokerIndices,
	1,
	s_reversePInvokeIndices,
	2,
	s_rgctxIndices,
	8,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnity_Burst,
	NULL,
	NULL,
	NULL,
	NULL,
};
