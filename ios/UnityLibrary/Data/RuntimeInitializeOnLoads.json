{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "Lit<PERSON>son", "className": "UnityTypeBindings", "methodName": "Register", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__12756722525760122593", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp-firstpass", "nameSpace": "Crosstales.FB.Util", "className": "SetupProject", "methodName": "setup", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp-firstpass", "nameSpace": "Crosstales.Common.Util", "className": "BaseHelper", "methodName": "initialize", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp-firstpass", "nameSpace": "Crosstales.Common.Util", "className": "FileHelper", "methodName": "initialize", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp-firstpass", "nameSpace": "Crosstales.Common.Util", "className": "SingletonHelper", "methodName": "initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "R3.<PERSON>", "nameSpace": "R3", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "R3.<PERSON>", "nameSpace": "R3", "className": "UnityProviderInitializer", "methodName": "SetDefaultObservableSystem", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "UniTask", "nameSpace": "Cysharp.Threading.Tasks", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1652832624114795843", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.MemoryProfiler", "nameSpace": "Unity.MemoryProfiler", "className": "MetadataInjector", "methodName": "PlayerInitMetadata", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.VisualScripting.Core", "nameSpace": "Unity.VisualScripting", "className": "RuntimeVSUsageUtility", "methodName": "RuntimeInitializeOnLoadBeforeSceneLoad", "loadTypes": 1, "isUnityClass": true}]}