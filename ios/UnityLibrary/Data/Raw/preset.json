{"portraitParams": null, "adjustParams": {"allColorAdjustParams": [{"maskTextureID": null, "maskName": "全图", "maskKey": "full", "faceIndex": -1, "isActive": false, "filterKey": null, "filterIntensity": 0, "wb": null, "tint": null, "expose": 0, "contrast": 0, "brightness": 0, "hightLight": 0, "shadowLight": 0, "white": 0, "black": 0, "clear": 0, "colorful": 0, "saturation": 0, "curve": null, "hsl_hue_Red": 0, "hsl_hue_Orange": 0, "hsl_hue_Yellow": 0, "hsl_hue_Green": 0, "hsl_hue_Cyan": 0, "hsl_hue_Blue": 0, "hsl_hue_Purple": 0, "hsl_hue_Magenta": 0, "hsl_saturation_Red": 0, "hsl_saturation_Orange": 0, "hsl_saturation_Yellow": 0, "hsl_saturation_Green": 0, "hsl_saturation_Cyan": 0, "hsl_saturation_Blue": 0, "hsl_saturation_Purple": 0, "hsl_saturation_Magenta": 0, "hsl_lightness_Red": 0, "hsl_lightness_Orange": 0, "hsl_lightness_Yellow": 0, "hsl_lightness_Green": 0, "hsl_lightness_Cyan": 0, "hsl_lightness_Blue": 0, "hsl_lightness_Purple": 0, "hsl_lightness_Magenta": 0, "sharpen": 0, "sharpen_radius": 50, "sharpen_detail": 25, "noise_remove": 0, "noise_remove_detail": 0, "noise_remove_contrast": 0, "color_remove": 0, "color_remove_detail": 0, "color_remove_smooth": 0, "grain": 0, "grainShadow": 0, "grainMiddleTone": 0, "grainHighlight": 0, "grainSize": 0, "grainRoughness": 0, "grainColor": 0, "lensDistortion": 0, "lockClipping": 0, "darkCorner": 0, "darkMidpoint": 50, "fix_r_hue": 0, "fix_r_saturation": 0, "fix_g_hue": 0, "fix_g_saturation": 0, "fix_b_hue": 0, "fix_b_saturation": 0}, {"maskTextureID": null, "maskName": "所有面部", "maskKey": "face_all", "faceIndex": -1, "isActive": false, "filterKey": null, "filterIntensity": null, "wb": 0, "tint": 0, "expose": 61, "contrast": 0, "brightness": 0, "hightLight": 0, "shadowLight": 0, "white": 0, "black": 0, "clear": 0, "colorful": 0, "saturation": 0, "curve": null, "hsl_hue_Red": 0, "hsl_hue_Orange": 0, "hsl_hue_Yellow": 0, "hsl_hue_Green": 0, "hsl_hue_Cyan": 0, "hsl_hue_Blue": 0, "hsl_hue_Purple": 0, "hsl_hue_Magenta": 0, "hsl_saturation_Red": 0, "hsl_saturation_Orange": 0, "hsl_saturation_Yellow": 0, "hsl_saturation_Green": 0, "hsl_saturation_Cyan": 0, "hsl_saturation_Blue": 0, "hsl_saturation_Purple": 0, "hsl_saturation_Magenta": 0, "hsl_lightness_Red": 0, "hsl_lightness_Orange": 0, "hsl_lightness_Yellow": 0, "hsl_lightness_Green": 0, "hsl_lightness_Cyan": 0, "hsl_lightness_Blue": 0, "hsl_lightness_Purple": 0, "hsl_lightness_Magenta": 0, "sharpen": null, "sharpen_radius": null, "sharpen_detail": null, "noise_remove": null, "noise_remove_detail": null, "noise_remove_contrast": null, "color_remove": null, "color_remove_detail": null, "color_remove_smooth": null, "grain": null, "grainShadow": null, "grainMiddleTone": null, "grainHighlight": null, "grainSize": null, "grainRoughness": null, "grainColor": null, "lensDistortion": null, "lockClipping": null, "darkCorner": null, "darkMidpoint": null, "fix_r_hue": null, "fix_r_saturation": null, "fix_g_hue": null, "fix_g_saturation": null, "fix_b_hue": null, "fix_b_saturation": null}]}}