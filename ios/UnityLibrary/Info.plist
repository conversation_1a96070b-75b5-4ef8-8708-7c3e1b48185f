<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CADisableMinimumFrameDuration</key>
    <false />
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <false />
    <key>CFBundleAllowMixedLocalizations</key>
    <true />
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleDisplayName</key>
    <string>Sugoi-Retouch</string>
    <key>CFBundleExecutable</key>
    <string>${EXECUTABLE_NAME}</string>
    <key>CFBundleIdentifier</key>
    <string>${PRODUCT_BUNDLE_IDENTIFIER}</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>${PRODUCT_NAME}</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>0.1</string>
    <key>CFBundleVersion</key>
    <string>0</string>
    <key>LSRequiresIPhoneOS</key>
    <true />
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen-iPhone</string>
    <key>UILaunchStoryboardName~ipad</key>
    <string>LaunchScreen-iPad</string>
    <key>UILaunchStoryboardName~iphone</key>
    <string>LaunchScreen-iPhone</string>
    <key>UILaunchStoryboardName~ipod</key>
    <string>LaunchScreen-iPhone</string>
    <key>UIPrerenderedIcon</key>
    <false />
    <key>UIRequiredDeviceCapabilities</key>
    <array>
      <string>arm64</string>
      <string>metal</string>
    </array>
    <key>UIRequiresFullScreen</key>
    <true />
    <key>UIRequiresPersistentWiFi</key>
    <false />
    <key>UIStatusBarHidden</key>
    <true />
    <key>UIStatusBarStyle</key>
    <string>UIStatusBarStyleDefault</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
    </array>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <true />
    <key>Unity_LoadingActivityIndicatorStyle</key>
    <integer>-1</integer>
  </dict>
</plist>
