// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		00000000008063A1000160D3 /* libiPhone-lib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = D8A1C72A0E8063A1000160D3 /* libiPhone-lib.a */; };
		01F967D36F74C7C4205C1BD6 /* lib_burst_generated.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DAB61E49F0B27D71CDA3B02F /* lib_burst_generated.a */; };
		178C9ABCFE5BB1DE2BD16D2D /* NativeCallProxy.mm in Sources */ = {isa = PBXBuildFile; fileRef = CAE0DAFFEEF167F8D2A2BC72 /* NativeCallProxy.mm */; };
		1D45A5CCCCC4E76191AAF1D0 /* libil2cpp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = EE54DC7E56FA015DDC9659BD /* libil2cpp.a */; };
		2DBCA64A86BE8DEE679DD35A /* IngameDebugConsole.mm in Sources */ = {isa = PBXBuildFile; fileRef = 366D4A68D81F1245950129DE /* IngameDebugConsole.mm */; };
		31211B4C6E4D3A55C19BC2A2 /* RealityKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2695EADE8122A2B6567B40E7 /* RealityKit.framework */; };
		415674EFD2EC187355BC05B1 /* LaunchScreen-iPhoneLandscape.png in Resources */ = {isa = PBXBuildFile; fileRef = 1E24B93E9D27D99850693BAF /* LaunchScreen-iPhoneLandscape.png */; };
		475B26F38A3A652C6174529D /* IUnityInterface.h in Headers */ = {isa = PBXBuildFile; fileRef = 679ACDCF6D14CA6FE6F81479 /* IUnityInterface.h */; };
		5623C57617FDCB0800090B9E /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		5623C57717FDCB0800090B9E /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 830B5C100E5ED4C100C7819F /* UIKit.framework */; };
		5623C57D17FDCB0900090B9E /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 5623C57B17FDCB0900090B9E /* InfoPlist.strings */; };
		5623C57F17FDCB0900090B9E /* Unity_iPhone_Tests.m in Sources */ = {isa = PBXBuildFile; fileRef = 5623C57E17FDCB0900090B9E /* Unity_iPhone_Tests.m */; };
		56C56C9817D6015200616839 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 56C56C9717D6015100616839 /* Images.xcassets */; };
		574A8D1BFDF4103B13A6538A /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 21ECD27EDF8EB21168769097 /* GameController.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		63ACF7178776C648B96974EB /* baselib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7CD8D832DF5E49F3229DE515 /* baselib.a */; };
		763ED5D885EABF761B2FE262 /* LaunchScreen-iPad.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A6444ABDD2799CC877EBAF7E /* LaunchScreen-iPad.storyboard */; };
		7F4E05AB2717219200A2CBE4 /* libGameAssembly.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F4E05A02717216D00A2CBE4 /* libGameAssembly.a */; };
		826E40C67FAA509B29A0EFA3 /* IUnityGraphics.h in Headers */ = {isa = PBXBuildFile; fileRef = 30347B5D7991A325732A4F10 /* IUnityGraphics.h */; };
		862C245020AEC7AC006FB4AD /* UnityWebRequest.mm in Sources */ = {isa = PBXBuildFile; fileRef = 862C244F20AEC7AC006FB4AD /* UnityWebRequest.mm */; };
		8A20382D213D4B3C005E6C56 /* AVKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8A20382C213D4B3C005E6C56 /* AVKit.framework */; };
		8A215703245064AA00E582EB /* NoGraphicsHelper.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A2BC6DE245061EE00C7C97D /* NoGraphicsHelper.mm */; };
		8A3831B5246956AA00CD74FD /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8A3831B4246956AA00CD74FD /* Metal.framework */; };
		8BEDFA3929C88F86007F26D7 /* UnityViewControllerBase+visionOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 8BEDFA3729C88F86007F26D7 /* UnityViewControllerBase+visionOS.h */; };
		8BEDFA3A29C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8BEDFA3829C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm */; };
		91C1EB5F348833EAD02A7E9A /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F2E4BD281D8D2761198E3AA0 /* PrivacyInfo.xcprivacy */; };
		9BA70426A90DB33FFDF35A71 /* lib_burst_generated.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 17B8DA4C2189239C555CC0EC /* lib_burst_generated.cpp */; };
		9D0A618B21BFE7F30094DC33 /* libiconv.2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9D0A618A21BFE7F30094DC33 /* libiconv.2.tbd */; };
		9D25ABA1213FB47800354C27 /* UnityFramework.h in Headers */ = {isa = PBXBuildFile; fileRef = 9D25AB9F213FB47800354C27 /* UnityFramework.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9D25ABA5213FB47800354C27 /* UnityFramework.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 9D25AB9D213FB47800354C27 /* UnityFramework.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		9D25ABAD213FB6B700354C27 /* AppDelegateListener.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AF7755F17997D1300341121 /* AppDelegateListener.mm */; };
		9D25ABAE213FB6BA00354C27 /* LifeCycleListener.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A292A9817992CE100409BA4 /* LifeCycleListener.mm */; };
		9D25ABAF213FB6BE00354C27 /* RenderPluginDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A5C1491174E662D0006EB36 /* RenderPluginDelegate.mm */; };
		9D25ABB0213FB6C400354C27 /* UnityViewControllerListener.mm in Sources */ = {isa = PBXBuildFile; fileRef = AAFE69D119F187C200638316 /* UnityViewControllerListener.mm */; };
		9D25ABB1213FB6D600354C27 /* UnityView+Keyboard.mm in Sources */ = {isa = PBXBuildFile; fileRef = 85E5623620F4F4D1001DFEF6 /* UnityView+Keyboard.mm */; };
		9D25ABB2213FB6E300354C27 /* ActivityIndicator.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A9FCB121617295F00C05364 /* ActivityIndicator.mm */; };
		9D25ABB3213FB6E300354C27 /* Keyboard.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A142DC51636943E00DD87CA /* Keyboard.mm */; };
		9D25ABB4213FB6E300354C27 /* OrientationSupport.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AC71EC319E7FBA90027502F /* OrientationSupport.mm */; };
		9D25ABB6213FB6E300354C27 /* StoreReview.m in Sources */ = {isa = PBXBuildFile; fileRef = 4E090A331F27884B0077B28D /* StoreReview.m */; };
		9D25ABB7213FB6E300354C27 /* UnityAppController+ViewHandling.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A4815C017A287D2003FBFD5 /* UnityAppController+ViewHandling.mm */; };
		9D25ABB8213FB6E300354C27 /* UnityView.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A851BA616FB2F6D00E911DB /* UnityView.mm */; };
		9D25ABB9213FB6E300354C27 /* UnityView+iOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A7939FF1ED43EE100B44EF1 /* UnityView+iOS.mm */; };
		9D25ABBA213FB6E300354C27 /* UnityView+tvOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A793A011ED43EE100B44EF1 /* UnityView+tvOS.mm */; };
		9D25ABBB213FB6E300354C27 /* UnityViewControllerBase.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A7939FC1ED2F53200B44EF1 /* UnityViewControllerBase.mm */; };
		9D25ABBC213FB6E300354C27 /* UnityViewControllerBase+iOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A793A031ED43EE100B44EF1 /* UnityViewControllerBase+iOS.mm */; };
		9D25ABBD213FB6E300354C27 /* UnityViewControllerBase+tvOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A793A051ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.mm */; };
		9D25ABBE213FB6F800354C27 /* OnDemandResources.mm in Sources */ = {isa = PBXBuildFile; fileRef = FC0B20A11B7A4F0B00FDFC55 /* OnDemandResources.mm */; };
		9D25ABBF213FB6F800354C27 /* AVCapture.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AC74A9419B47FEF00019D38 /* AVCapture.mm */; };
		9D25ABC0213FB6F800354C27 /* CameraCapture.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8ADCE38A19C87177006F04F6 /* CameraCapture.mm */; };
		9D25ABC1213FB6F800354C27 /* CMVideoSampling.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A2AA93416E0978D001FB470 /* CMVideoSampling.mm */; };
		9D25ABC2213FB6F800354C27 /* CVTextureCache.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A367F5A16A6D36F0012ED11 /* CVTextureCache.mm */; };
		9D25ABC3213FB6F800354C27 /* DeviceSettings.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8ACB801B177081D4005D0019 /* DeviceSettings.mm */; };
		9D25ABC4213FB6F800354C27 /* DisplayManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A5E0B9016849D1800CBB6FE /* DisplayManager.mm */; };
		9D25ABC6213FB6F800354C27 /* Filesystem.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A25E6D118D767E20006A227 /* Filesystem.mm */; };
		9D25ABC8213FB6F800354C27 /* InternalProfiler.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 8A6720A319EEB905006C92E0 /* InternalProfiler.cpp */; };
		9D25ABC9213FB6F800354C27 /* MetalHelper.mm in Sources */ = {isa = PBXBuildFile; fileRef = 1859EA9A19214E7B0022C3D3 /* MetalHelper.mm */; };
		9D25ABCA213FB6F800354C27 /* FullScreenVideoPlayer.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A16150B1A8E4362006FA788 /* FullScreenVideoPlayer.mm */; };
		9D25ABCB213FB6F800354C27 /* UnityReplayKit.mm in Sources */ = {isa = PBXBuildFile; fileRef = 84DC28F51C5137FE00BC67D7 /* UnityReplayKit.mm */; };
		9D25ABCC213FB6F800354C27 /* UnityReplayKit_Scripting.mm in Sources */ = {isa = PBXBuildFile; fileRef = 848031E01C5160D700FCEAB7 /* UnityReplayKit_Scripting.mm */; };
		9D25ABCD213FB6F800354C27 /* VideoPlayer.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AB3CB3D16D390BB00697AD5 /* VideoPlayer.mm */; };
		9D25ABE0213FB76500354C27 /* CrashReporter.mm in Sources */ = {isa = PBXBuildFile; fileRef = FC85CCB916C3ED8000BAF7C7 /* CrashReporter.mm */; };
		9D25ABE1213FB76500354C27 /* iPhone_Sensors.mm in Sources */ = {isa = PBXBuildFile; fileRef = 56DBF99C15E3CDC9007A4A8D /* iPhone_Sensors.mm */; };
		9D25ABE3213FB76500354C27 /* UnityAppController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A851BA916FB3AD000E911DB /* UnityAppController.mm */; };
		9D25ABE4213FB76500354C27 /* UnityAppController+Rendering.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AA5D80117ABE9AF007B9910 /* UnityAppController+Rendering.mm */; };
		9D25ABE5213FB76500354C27 /* UnityAppController+UnityInterface.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A8D90D91A274A7800456C4E /* UnityAppController+UnityInterface.mm */; };
		9D25ABE6213FB7C100354C27 /* RegisterFeatures.cpp in Sources */ = {isa = PBXBuildFile; fileRef = AAC3E38B1A68945900F6174A /* RegisterFeatures.cpp */; };
		9D25ABE9213FB7CC00354C27 /* Il2CppOptions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 03F528621B447098000F4FB8 /* Il2CppOptions.cpp */; };
		9D690CCF21BFD341005026B1 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5BAD78601F2B5A59006103DE /* Security.framework */; };
		9D690CD021BFD349005026B1 /* MediaToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 960391211D6CE46E003BF157 /* MediaToolbox.framework */; };
		9D690CD221BFD36C005026B1 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AA5D99861AFAD3C800B27605 /* CoreText.framework */; };
		9D690CD321BFD376005026B1 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8358D1B70ED1CC3700E3A684 /* AudioToolbox.framework */; };
		9D690CD421BFD37E005026B1 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F36C11013C5C673007FBDD9 /* AVFoundation.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		9D690CD521BFD388005026B1 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 56FD43950ED4745200FE3770 /* CFNetwork.framework */; };
		9D690CD621BFD391005026B1 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 56B7959A1442E0F20026B3DD /* CoreGraphics.framework */; };
		9D690CD721BFD39D005026B1 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F36C10E13C5C673007FBDD9 /* CoreMedia.framework */; };
		9D690CD821BFD3A5005026B1 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 56B795C11442E1100026B3DD /* CoreMotion.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		9D690CD921BFD3AC005026B1 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F36C10F13C5C673007FBDD9 /* CoreVideo.framework */; };
		9D690CDA21BFD3B5005026B1 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		9D690CDB21BFD3BF005026B1 /* OpenAL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 83B2574B0E63022200468741 /* OpenAL.framework */; };
		9D690CDD21BFD3D0005026B1 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 83B2570A0E62FF8A00468741 /* QuartzCore.framework */; };
		9D690CDE21BFD3D9005026B1 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 56BCBA380FCF049A0030C3B2 /* SystemConfiguration.framework */; };
		9D690CDF21BFD3E3005026B1 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 830B5C100E5ED4C100C7819F /* UIKit.framework */; };
		9D9DE4EA221D84E60049D9A1 /* Data in Resources */ = {isa = PBXBuildFile; fileRef = AA31BF961B55660D0013FB1B /* Data */; };
		9DA3B0442174CB96001678C7 /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = 9DA3B0432174CB96001678C7 /* main.mm */; };
		9DC67E8521CBBEBB005F9FA1 /* UnityAppController.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A851BA816FB3AD000E911DB /* UnityAppController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DC67E8621CBBEC7005F9FA1 /* UndefinePlatforms.h in Headers */ = {isa = PBXBuildFile; fileRef = 9D16CD8021C938B300DD46C0 /* UndefinePlatforms.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DC67E8721CBBEC7005F9FA1 /* RedefinePlatforms.h in Headers */ = {isa = PBXBuildFile; fileRef = 9D16CD8121C938BB00DD46C0 /* RedefinePlatforms.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DC67E8821CBBED5005F9FA1 /* RenderPluginDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A5C1490174E662D0006EB36 /* RenderPluginDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DC67E8921CBBEDF005F9FA1 /* LifeCycleListener.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A292A9717992CE100409BA4 /* LifeCycleListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DFA7F9D21410F2E00C2880E /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = D82DCFBB0E8000A5005D6AD8 /* main.mm */; };
		9E277B346B541C2D072C5489 /* IUnityGraphicsMetal.h in Headers */ = {isa = PBXBuildFile; fileRef = 894C1B251937BE1BC2B4D36C /* IUnityGraphicsMetal.h */; };
		A0089F71519CB873CA1D2878 /* PGUnityPluginDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 1C4CC834EAE2BD1247708FA7 /* PGUnityPluginDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A0EBFD3072F58D940550863B /* PGUnityPlugin.mm in Sources */ = {isa = PBXBuildFile; fileRef = 28E173888AE846C65CF0DC84 /* PGUnityPlugin.mm */; };
		BEE3E21D322649C83B0DD4DC /* LaunchScreen-iPhone.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 5A3A1B4C3FB1FC90023B942E /* LaunchScreen-iPhone.storyboard */; };
		C52C4BAD9FBCA05274DA0EDC /* PGUnityPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 80CFB204F2EC78281EC6CF9E /* PGUnityPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D085C06443DB1B1B5C6637B6 /* LaunchScreen-iPad.png in Resources */ = {isa = PBXBuildFile; fileRef = D556450C3DBEECE838996A3D /* LaunchScreen-iPad.png */; };
		E22AFF0A3F5BDAC0D9C0871C /* NativeCallProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 51E0FA324635BD384CCB75D3 /* NativeCallProxy.h */; };
		F5C03E9DD3BF40D8EC0F4404 /* LaunchScreen-iPhonePortrait.png in Resources */ = {isa = PBXBuildFile; fileRef = 681AA63C19788ACA381FB8C1 /* LaunchScreen-iPhonePortrait.png */; };
		F9C34C618808157121DB9949 /* Data in Resources */ = {isa = PBXBuildFile; fileRef = AA31BF961B55660D0013FB1B /* Data */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		5623C58117FDCB0900090B9E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 29B97313FDCFA39411CA2CEA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1D6058900D05DD3D006BFB54;
			remoteInfo = "Unity-iPhone";
		};
		7F401A4C272AC77C005AF450 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 29B97313FDCFA39411CA2CEA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7F4E059F2717216D00A2CBE4;
			remoteInfo = GameAssembly;
		};
		9D25ABA2213FB47800354C27 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 29B97313FDCFA39411CA2CEA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9D25AB9C213FB47800354C27;
			remoteInfo = UnityFramework;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		7F4E059E2717216D00A2CBE4 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		83D0C1FD0E6C8D7700EBCE5D /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 0;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D25ABAB213FB47800354C27 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				9D25ABA5213FB47800354C27 /* UnityFramework.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		004B7ED377C245FA07EA4CA7 /* Unity.TextMeshPro_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.TextMeshPro_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro_CodeGen.c; sourceTree = SOURCE_ROOT; };
		007AE62FCE6018BA9A003B7F /* Assembly-CSharp__80.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__80.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__80.cpp"; sourceTree = SOURCE_ROOT; };
		0084A6F53FDC53D8C88D04BC /* mscorlib__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__14.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__14.cpp; sourceTree = SOURCE_ROOT; };
		01542E4A7351BF537D5F9A2D /* UnityEngine.TextCoreTextEngineModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreTextEngineModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp; sourceTree = SOURCE_ROOT; };
		0261C17A7919F2AE5BFC8D0A /* Assembly-CSharp__52.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__52.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__52.cpp"; sourceTree = SOURCE_ROOT; };
		0374DE58E2FECDF95FC29943 /* Unity.Burst.Unsafe.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Burst.Unsafe.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Burst.Unsafe.cpp; sourceTree = SOURCE_ROOT; };
		037E4726C630DA1E62A5CBDD /* UnityEngine.TilemapModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TilemapModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		03A6C04DBFCC9DD52544385D /* UnityEngine.InputModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.InputModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		03A87A26A63177A70A5AC67E /* Assembly-CSharp__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__15.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__15.cpp"; sourceTree = SOURCE_ROOT; };
		03F528621B447098000F4FB8 /* Il2CppOptions.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Il2CppOptions.cpp; sourceTree = "<group>"; };
		043632D66EB0B7E5F3272285 /* System.Xml__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__4.cpp; sourceTree = SOURCE_ROOT; };
		0464E0DCE4A0FC27BFF43BB4 /* System.Xml.Linq.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml.Linq.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq.cpp; sourceTree = SOURCE_ROOT; };
		04FAC3AAC831D75114B40DDB /* UnityEngine.UI_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UI_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_Debugger.c; sourceTree = SOURCE_ROOT; };
		0546416B09929F2E2CC06B90 /* UnityEngine.TLSModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TLSModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TLSModule.cpp; sourceTree = SOURCE_ROOT; };
		058D9CAFDB588D59044A2B25 /* System__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__4.cpp; sourceTree = SOURCE_ROOT; };
		05C25260E27291917CF35635 /* R3.Unity_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = R3.Unity_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/R3.Unity_CodeGen.c; sourceTree = SOURCE_ROOT; };
		0625BA89AA0F71807DF4285A /* Assembly-CSharp__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__2.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__2.cpp"; sourceTree = SOURCE_ROOT; };
		068A319EC6F63095C6A505D8 /* UnityEngine.WindModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.WindModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.WindModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		079A43E3F27C556AF34E8FDB /* Unity.VisualScripting.Core__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.VisualScripting.Core__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.VisualScripting.Core__1.cpp; sourceTree = SOURCE_ROOT; };
		07CA36577698941D4EE4BC70 /* Assembly-CSharp__34.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__34.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__34.cpp"; sourceTree = SOURCE_ROOT; };
		07F77C1BA780C0E1C591423E /* Unity.TextMeshPro__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__6.cpp; sourceTree = SOURCE_ROOT; };
		0843F8EB79BC66D9BA665116 /* Assembly-CSharp__55.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__55.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__55.cpp"; sourceTree = SOURCE_ROOT; };
		08B9B183EA7AC0D27F8A1A86 /* R3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = R3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/R3.cpp; sourceTree = SOURCE_ROOT; };
		08D218E34CD3080971C43706 /* System.Xml__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__6.cpp; sourceTree = SOURCE_ROOT; };
		08D9ECBDADDA4DD82C86134D /* Assembly-CSharp__42.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__42.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__42.cpp"; sourceTree = SOURCE_ROOT; };
		0A99F41A6E79CC9C6BE2D738 /* Assembly-CSharp__24.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__24.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__24.cpp"; sourceTree = SOURCE_ROOT; };
		0B1A110055A045B2CF537BDF /* System.Core_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Core_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Core_Debugger.c; sourceTree = SOURCE_ROOT; };
		0B698999CFCA23787EA976B2 /* Assembly-CSharp__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__8.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__8.cpp"; sourceTree = SOURCE_ROOT; };
		0C33FB3ADEC96C25F3C484DA /* UnityEngine.TextCoreFontEngineModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TextCoreFontEngineModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		0C361D905CC46C46E74E790D /* UnityEngine.VFXModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.VFXModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VFXModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		0D1FF11E9C4F5348B320B360 /* UnityEngine.InputModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.InputModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		0D31AAF3C6C0CC11D285C738 /* UnityEngine.CoreModule__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__2.cpp; sourceTree = SOURCE_ROOT; };
		0D74EFEA392C0E65155F7A96 /* UnityEngine.PropertiesModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.PropertiesModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		0D84C192128D6CD0E262978B /* UnityEngine.VFXModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.VFXModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VFXModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		0D877878793A1AFE06CA78FD /* UnityEngine.XRModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.XRModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.XRModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		0DB6966C8FE1F310309492B8 /* Assembly-CSharp__54.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__54.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__54.cpp"; sourceTree = SOURCE_ROOT; };
		0E3F3917340B2949C8B8E209 /* Il2CppMetadataUsage.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppMetadataUsage.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataUsage.c; sourceTree = SOURCE_ROOT; };
		0F1F382C4530E826AE63D9D6 /* UnityEngine.UIElementsModule__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__5.cpp; sourceTree = SOURCE_ROOT; };
		0F795415320EF5804B496AD2 /* Il2CppInvokerTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppInvokerTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppInvokerTable.cpp; sourceTree = SOURCE_ROOT; };
		0F892C1159E410FBFC2AC766 /* Assembly-CSharp__100.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__100.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__100.cpp"; sourceTree = SOURCE_ROOT; };
		0FA54142F83E19F3610B1991 /* Unity.TextMeshPro__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__1.cpp; sourceTree = SOURCE_ROOT; };
		0FF38D860993A4461BE3D1D6 /* UnityEngine.ImageConversionModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.ImageConversionModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule.cpp; sourceTree = SOURCE_ROOT; };
		11856605578FD8270FB7624A /* Generics__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__10.cpp; sourceTree = SOURCE_ROOT; };
		120999864790FD52FD3963EF /* UnityEngine.StreamingModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.StreamingModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.StreamingModule.cpp; sourceTree = SOURCE_ROOT; };
		135D0ADBD4A33357B22F37EC /* UnityEngine.CrashReportingModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CrashReportingModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CrashReportingModule.cpp; sourceTree = SOURCE_ROOT; };
		13D52F001D38A56FB76E88A5 /* UnityEngine.UIElementsModule__17.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__17.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__17.cpp; sourceTree = SOURCE_ROOT; };
		13D57270A9533024EAF53DA2 /* Assembly-CSharp__63.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__63.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__63.cpp"; sourceTree = SOURCE_ROOT; };
		1432D64D8E4E62A6A841AFB0 /* R3.Unity_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = R3.Unity_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/R3.Unity_Debugger.c; sourceTree = SOURCE_ROOT; };
		1492999617EA37FB6D4727C7 /* Assembly-CSharp__16.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__16.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__16.cpp"; sourceTree = SOURCE_ROOT; };
		1562360321C1A01A12D225BC /* UnityEngine.SharedInternalsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.SharedInternalsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule.cpp; sourceTree = SOURCE_ROOT; };
		15A19AFEA90DC0B5F336605F /* UnityEngine.UIElementsModule__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__7.cpp; sourceTree = SOURCE_ROOT; };
		16A72414295A2B23C5F331A7 /* Il2CppCCalculateTypeValues1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateTypeValues1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues1.cpp; sourceTree = SOURCE_ROOT; };
		16E144C6C0B3E8367F911BA8 /* System.Xml_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Xml_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml_CodeGen.c; sourceTree = SOURCE_ROOT; };
		1701016BB5DA32953B06ADB5 /* UnityEngine_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine_CodeGen.c; sourceTree = SOURCE_ROOT; };
		1791BB02AC6CAA800735D036 /* R3_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = R3_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/R3_Debugger.c; sourceTree = SOURCE_ROOT; };
		17A88A24B72C4E31367418A7 /* Assembly-CSharp__19.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__19.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__19.cpp"; sourceTree = SOURCE_ROOT; };
		17B8DA4C2189239C555CC0EC /* lib_burst_generated.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = lib_burst_generated.cpp; path = Libraries/lib_burst_generated.cpp; sourceTree = SOURCE_ROOT; };
		1859EA9A19214E7B0022C3D3 /* MetalHelper.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = MetalHelper.mm; sourceTree = "<group>"; };
		188198725C11AA1A9BB3A8BF /* Assembly-CSharp__47.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__47.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__47.cpp"; sourceTree = SOURCE_ROOT; };
		1887791D1EDB29E243E57E31 /* Assembly-CSharp__57.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__57.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__57.cpp"; sourceTree = SOURCE_ROOT; };
		196E9825E8305BAF49AC748B /* Unity.Mathematics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Mathematics.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Mathematics.cpp; sourceTree = SOURCE_ROOT; };
		19CC4FC0E5C5AFF6FF9E64FA /* System__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__11.cpp; sourceTree = SOURCE_ROOT; };
		1B92F201E0C11B4944DF0F0B /* System.Runtime.Serialization.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Runtime.Serialization.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization.cpp; sourceTree = SOURCE_ROOT; };
		1BB2FEF7E52AA544C2D86C49 /* UnityEngine.UnityAnalyticsCommonModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityAnalyticsCommonModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		1C09897E685B7A7D91056118 /* System.Xml_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Xml_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml_Debugger.c; sourceTree = SOURCE_ROOT; };
		1C28A4DFE57E236FAE30F7B7 /* System.Xml.Linq_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Xml.Linq_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq_Debugger.c; sourceTree = SOURCE_ROOT; };
		1C3F27E9009F948E3EC03458 /* System.Xml__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__5.cpp; sourceTree = SOURCE_ROOT; };
		1C473E48207C8E4741CF401C /* Assembly-CSharp__59.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__59.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__59.cpp"; sourceTree = SOURCE_ROOT; };
		1C4CC834EAE2BD1247708FA7 /* PGUnityPluginDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = PGUnityPluginDelegate.h; path = Libraries/Plugins/iOS/PGUnityPluginDelegate.h; sourceTree = SOURCE_ROOT; };
		1D0A122864C3A431BADD4B5D /* System.Transactions_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Transactions_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Transactions_Debugger.c; sourceTree = SOURCE_ROOT; };
		1D30AB110D05D00D00671497 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		1D6058910D05DD3D006BFB54 /* Unity-Target-New.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; name = "Unity-Target-New.app"; path = "Sugoi-Retouch.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		1DC31C363AA49E88C15788BB /* mscorlib__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__11.cpp; sourceTree = SOURCE_ROOT; };
		1DD696E89C6A943954768877 /* Il2CppGenericInstDefinitions.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericInstDefinitions.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericInstDefinitions.c; sourceTree = SOURCE_ROOT; };
		1E24B93E9D27D99850693BAF /* LaunchScreen-iPhoneLandscape.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "LaunchScreen-iPhoneLandscape.png"; sourceTree = SOURCE_ROOT; };
		1E2F8862C3F9970065521531 /* IngameDebugConsole.Runtime_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = IngameDebugConsole.Runtime_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/IngameDebugConsole.Runtime_Debugger.c; sourceTree = SOURCE_ROOT; };
		1EF6732A52CD49B602825AEB /* Il2CppCCalculateTypeValues.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateTypeValues.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues.cpp; sourceTree = SOURCE_ROOT; };
		1F13B5FB9DF58B79696EBF36 /* Assembly-CSharp_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = "Assembly-CSharp_Debugger.c"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp_Debugger.c"; sourceTree = SOURCE_ROOT; };
		1F8BFA2E5A3610BA64D7B9B0 /* UnityEngine.UIModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UIModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		1F951416510E376441DD5ABD /* Assembly-CSharp__18.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__18.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__18.cpp"; sourceTree = SOURCE_ROOT; };
		2030A7564DD6412E7C11EA97 /* UnityEngine.AndroidJNIModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AndroidJNIModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AndroidJNIModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		209F1A6FDF0F7CD4B19CFB7B /* UnityEngine.ClothModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ClothModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ClothModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		211F060733D8F95086408787 /* UnityEngine.CoreModule__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__6.cpp; sourceTree = SOURCE_ROOT; };
		212E41FE4D4EBAD53A5E0F6F /* UnityEngine.InputModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.InputModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputModule.cpp; sourceTree = SOURCE_ROOT; };
		218A5F5892B6E72316C9FFDA /* UnityEngine.PropertiesModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.PropertiesModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule.cpp; sourceTree = SOURCE_ROOT; };
		21D6D96509F70B8503A70C75 /* Assembly-CSharp__108.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__108.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__108.cpp"; sourceTree = SOURCE_ROOT; };
		21ECD27EDF8EB21168769097 /* GameController.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameController.framework; path = System/Library/Frameworks/GameController.framework; sourceTree = SDKROOT; };
		223EBC6A186ED13AA8BAEDBA /* Assembly-CSharp__23.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__23.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__23.cpp"; sourceTree = SOURCE_ROOT; };
		223F87790AF1C48E224ABBC8 /* Assembly-CSharp__98.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__98.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__98.cpp"; sourceTree = SOURCE_ROOT; };
		2274C9C05B1B4B06D30C0211 /* System.Data__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__3.cpp; sourceTree = SOURCE_ROOT; };
		23225D541B6C1620CBF548E2 /* UnityEngine.TextCoreTextEngineModule__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreTextEngineModule__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp; sourceTree = SOURCE_ROOT; };
		232710CEDCF91E0181CCF102 /* UnityEngine.IMGUIModule__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.IMGUIModule__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule__2.cpp; sourceTree = SOURCE_ROOT; };
		234DA00B7297246CAFE09A46 /* Assembly-CSharp__58.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__58.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__58.cpp"; sourceTree = SOURCE_ROOT; };
		238B504DA5717798C8149303 /* Il2CppRgctxTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppRgctxTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppRgctxTable.c; sourceTree = SOURCE_ROOT; };
		245383633D96F1EBF8071FE5 /* UnityEngine.UnityWebRequestWWWModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestWWWModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		2460523730B2F13867ED42C8 /* UnityEngine.ScreenCaptureModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ScreenCaptureModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ScreenCaptureModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		25711C69564281E1F27185F1 /* Mono.Security.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Mono.Security.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Security.cpp; sourceTree = SOURCE_ROOT; };
		25EDC3486A64365C2398DAB5 /* Assembly-CSharp__44.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__44.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__44.cpp"; sourceTree = SOURCE_ROOT; };
		265D6DDBC6C0E98AE8B87F62 /* IngameDebugConsole.Runtime__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = IngameDebugConsole.Runtime__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/IngameDebugConsole.Runtime__1.cpp; sourceTree = SOURCE_ROOT; };
		2687C070B6BABD52B518AC9D /* UnityEngine.ImageConversionModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ImageConversionModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		2695EADE8122A2B6567B40E7 /* RealityKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = RealityKit.framework; path = System/Library/Frameworks/RealityKit.framework; sourceTree = SDKROOT; };
		27754211CC9E10094FDB5B65 /* Assembly-CSharp__86.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__86.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__86.cpp"; sourceTree = SOURCE_ROOT; };
		282C69E0C3A9D7A01FA7BDE0 /* UnityEngine.PerformanceReportingModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.PerformanceReportingModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PerformanceReportingModule.cpp; sourceTree = SOURCE_ROOT; };
		28C3B8EF48C0EB6F619F326F /* System.Core.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core.cpp; sourceTree = SOURCE_ROOT; };
		28D10947512B25AFE0C9D0D1 /* UnityEngine.SubsystemsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.SubsystemsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubsystemsModule.cpp; sourceTree = SOURCE_ROOT; };
		28E173888AE846C65CF0DC84 /* PGUnityPlugin.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = PGUnityPlugin.mm; path = Libraries/Plugins/iOS/PGUnityPlugin.mm; sourceTree = SOURCE_ROOT; };
		29218B0B79CD49C8515B939E /* System.Xml__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__10.cpp; sourceTree = SOURCE_ROOT; };
		29C9A11F248A4A9690F9001D /* UnityEngine.AndroidJNIModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AndroidJNIModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AndroidJNIModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		29DB862887FD80F9544C0F27 /* UnityEngine.CoreModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.CoreModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		2A1756D56CDCBEB23C273602 /* UnityEngine.LocalizationModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.LocalizationModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.LocalizationModule.cpp; sourceTree = SOURCE_ROOT; };
		2A4E1F27CBC17CFBC9D90381 /* UnityEngine.UIElementsModule__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__11.cpp; sourceTree = SOURCE_ROOT; };
		2A64BC0FB139C78B893FDCAA /* UnityEngine.UnityAnalyticsCommonModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityAnalyticsCommonModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule.cpp; sourceTree = SOURCE_ROOT; };
		2A94588CB7D88ADC352647E9 /* UnityEngine.VehiclesModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.VehiclesModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VehiclesModule.cpp; sourceTree = SOURCE_ROOT; };
		2B3E91347CC233053DCB5A6A /* Unity.TextMeshPro__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__7.cpp; sourceTree = SOURCE_ROOT; };
		2B422D1DE14C2A8D3F21065A /* UnityEngine.UIElementsModule__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__1.cpp; sourceTree = SOURCE_ROOT; };
		2B4E5941CFE34B6535203334 /* UnityEngine.AccessibilityModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AccessibilityModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AccessibilityModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		2BCFF7AAB07F012FD2E34C5B /* mscorlib__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__2.cpp; sourceTree = SOURCE_ROOT; };
		2BEDAA3AFB931C1E1C406501 /* mscorlib__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__3.cpp; sourceTree = SOURCE_ROOT; };
		2D55E3A876DECECA9E2A124C /* System.Configuration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Configuration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Configuration.cpp; sourceTree = SOURCE_ROOT; };
		2D5AF8F8E52C508883AD72E2 /* System.Xml__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__7.cpp; sourceTree = SOURCE_ROOT; };
		2E38E8560CE5D41091E04C4B /* System.Numerics_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Numerics_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Numerics_CodeGen.c; sourceTree = SOURCE_ROOT; };
		2E8E2D02765123AF23FC2A10 /* System.Numerics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Numerics.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Numerics.cpp; sourceTree = SOURCE_ROOT; };
		2E9FEEBC77FE9D53FD71E76A /* UnityEngine.UI__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UI__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__3.cpp; sourceTree = SOURCE_ROOT; };
		30347B5D7991A325732A4F10 /* IUnityGraphics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IUnityGraphics.h; path = Classes/Unity/IUnityGraphics.h; sourceTree = SOURCE_ROOT; };
		3066866A0209B133D099A786 /* Assembly-CSharp__75.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__75.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__75.cpp"; sourceTree = SOURCE_ROOT; };
		30A72D51776289D79DEEDA61 /* UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		30E35F62D8A9A9617E95F23E /* UnityEngine.UnityWebRequestAudioModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestAudioModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAudioModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		31186A0E47C72F72C527829D /* __Generated_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = __Generated_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/__Generated_Debugger.c; sourceTree = SOURCE_ROOT; };
		317B44F3876EC140C859BDEB /* Generics__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__11.cpp; sourceTree = SOURCE_ROOT; };
		32CA8D88C5E1E8B5DDD1684B /* System.Transactions.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Transactions.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Transactions.cpp; sourceTree = SOURCE_ROOT; };
		3369FBBB1202A2D16B16FA7E /* mscorlib__21.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__21.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__21.cpp; sourceTree = SOURCE_ROOT; };
		33D9A9D1BA3BF2E1779DF0EA /* Assembly-CSharp__81.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__81.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__81.cpp"; sourceTree = SOURCE_ROOT; };
		347B08775B35751B1821B42A /* UnityEngine.UnityTestProtocolModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityTestProtocolModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityTestProtocolModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		34B2FF818AC0E4621CBC5379 /* UnityEngine.UIModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule.cpp; sourceTree = SOURCE_ROOT; };
		34CC05B10329BD755492E6F7 /* UnityEngine.StreamingModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.StreamingModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.StreamingModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		35446C8F021B4551882708E5 /* System__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__1.cpp; sourceTree = SOURCE_ROOT; };
		354C830C3FA3287CFC1D3844 /* UnityEngine.SpriteShapeModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.SpriteShapeModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule.cpp; sourceTree = SOURCE_ROOT; };
		359EA6DE6F0ADB889FC61B63 /* Assembly-CSharp_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = "Assembly-CSharp_CodeGen.c"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp_CodeGen.c"; sourceTree = SOURCE_ROOT; };
		35BA4E1F3D3749F3A77D35B2 /* UnityEngine.UIElementsModule__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__14.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__14.cpp; sourceTree = SOURCE_ROOT; };
		35E347F2DB0DFA23974DEB26 /* UnityEngine.ScreenCaptureModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.ScreenCaptureModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ScreenCaptureModule.cpp; sourceTree = SOURCE_ROOT; };
		366D4A68D81F1245950129DE /* IngameDebugConsole.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = IngameDebugConsole.mm; path = Libraries/Plugins/IngameDebugConsole/iOS/IngameDebugConsole.mm; sourceTree = SOURCE_ROOT; };
		375E310F9505DD2005F3438A /* UniTask.Linq.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UniTask.Linq.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UniTask.Linq.cpp; sourceTree = SOURCE_ROOT; };
		37AC7AC7977FAFF44E7DAA61 /* UnityEngine.TilemapModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TilemapModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		37D3FC745AFF2AAE4E3F0FC0 /* Assembly-CSharp__78.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__78.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__78.cpp"; sourceTree = SOURCE_ROOT; };
		37EAFBD23847FB208B8464AF /* UnityEngine.UnityAnalyticsModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityAnalyticsModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		37F6C712B26110AD2FC79825 /* System.Configuration_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Configuration_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Configuration_Debugger.c; sourceTree = SOURCE_ROOT; };
		3950BD72498ECBFF84CEBC47 /* Assembly-CSharp__77.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__77.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__77.cpp"; sourceTree = SOURCE_ROOT; };
		39A4F548F86A869BD5CA86C0 /* Assembly-CSharp__26.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__26.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__26.cpp"; sourceTree = SOURCE_ROOT; };
		3AE2C1E81BAF7A69DB8DD34C /* UnityEngine.TerrainPhysicsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TerrainPhysicsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TerrainPhysicsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		3B557CF9358F36A58A8F38FC /* UnityEngine.PhysicsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.PhysicsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		3BF0F548641EA67234EF4822 /* Assembly-CSharp__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__3.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__3.cpp"; sourceTree = SOURCE_ROOT; };
		3C54469E02AE026A630B4244 /* Assembly-CSharp__25.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__25.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__25.cpp"; sourceTree = SOURCE_ROOT; };
		3C604F94CAC079219AA8E603 /* UnityEngine.ProfilerModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ProfilerModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ProfilerModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		3CC546E182B89988089AC598 /* UnityEngine.UIElementsModule__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__3.cpp; sourceTree = SOURCE_ROOT; };
		3D4E0D172302BFF9751A2E53 /* Mono.Data.Sqlite__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Mono.Data.Sqlite__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Data.Sqlite__1.cpp; sourceTree = SOURCE_ROOT; };
		3E2067B7FF4DEA685E5DDD1A /* UnityEngine.VRModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.VRModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VRModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		3E6AB690991E5D5C419B99F5 /* Newtonsoft.Json__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__2.cpp; sourceTree = SOURCE_ROOT; };
		3E7E5CC83B71F26761C384D4 /* Assembly-CSharp__79.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__79.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__79.cpp"; sourceTree = SOURCE_ROOT; };
		3EAD2E0234EACBB68F7569A5 /* Assembly-CSharp__36.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__36.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__36.cpp"; sourceTree = SOURCE_ROOT; };
		3F6406DE79FB768A8C324E67 /* Assembly-CSharp__69.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__69.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__69.cpp"; sourceTree = SOURCE_ROOT; };
		4114445645D738D6E9CC35B8 /* Assembly-CSharp__17.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__17.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__17.cpp"; sourceTree = SOURCE_ROOT; };
		4117F67ACFF8CBA17CC65251 /* System.Xml__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__15.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__15.cpp; sourceTree = SOURCE_ROOT; };
		414991A97FF59CD6675FD9EB /* Newtonsoft.Json__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__4.cpp; sourceTree = SOURCE_ROOT; };
		41772366CC01AABF3A141A21 /* System.Xml__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__3.cpp; sourceTree = SOURCE_ROOT; };
		418A53639A9FDF6906C6BFBC /* UnityEngine.CoreModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule.cpp; sourceTree = SOURCE_ROOT; };
		43289C428F36A64040F01B14 /* mscorlib__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__8.cpp; sourceTree = SOURCE_ROOT; };
		435AD329270E76D06EF5AEBF /* Assembly-CSharp__102.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__102.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__102.cpp"; sourceTree = SOURCE_ROOT; };
		439D39F1FC43F7B0A6C04FFF /* Generics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics.cpp; sourceTree = SOURCE_ROOT; };
		43DCC1F9F099008314351765 /* Assembly-CSharp__49.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__49.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__49.cpp"; sourceTree = SOURCE_ROOT; };
		444A6E27F397365C367A0E17 /* Assembly-CSharp__21.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__21.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__21.cpp"; sourceTree = SOURCE_ROOT; };
		45EFF0285CC84ED02CB30A69 /* UnityEngine.UIElementsModule__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__9.cpp; sourceTree = SOURCE_ROOT; };
		465EB73EB7CAF8A291ADD317 /* Assembly-CSharp__27.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__27.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__27.cpp"; sourceTree = SOURCE_ROOT; };
		469A72CDD1B2FFEAA2E37FAC /* Assembly-CSharp__66.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__66.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__66.cpp"; sourceTree = SOURCE_ROOT; };
		46D7F0219796DB59D3B54049 /* UnityEngine.VFXModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.VFXModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VFXModule.cpp; sourceTree = SOURCE_ROOT; };
		470F4DBFC6CF22AFB4920379 /* mscorlib_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = mscorlib_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib_CodeGen.c; sourceTree = SOURCE_ROOT; };
		4731C5266AFA863C29647599 /* System__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__8.cpp; sourceTree = SOURCE_ROOT; };
		47A43E6546DABAEA781AD71C /* Unity.Mathematics_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Mathematics_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Mathematics_CodeGen.c; sourceTree = SOURCE_ROOT; };
		47FB88DA2B3F39732E795E6F /* Microsoft.Bcl.TimeProvider.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Microsoft.Bcl.TimeProvider.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Microsoft.Bcl.TimeProvider.cpp; sourceTree = SOURCE_ROOT; };
		48934EA8227C7DAAAECD158F /* Unity.Collections_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Collections_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Collections_Debugger.c; sourceTree = SOURCE_ROOT; };
		4A04E986DBE4161111452A21 /* UnityEngine.ProfilerModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ProfilerModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ProfilerModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		4A4901A84201EE0A3D97E0A0 /* UnityEngine_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine_Debugger.c; sourceTree = SOURCE_ROOT; };
		4A8BF4F505104D1B8AE63E79 /* Assembly-CSharp__48.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__48.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__48.cpp"; sourceTree = SOURCE_ROOT; };
		4AE6620E3ECDEE30CBB8DF48 /* UnityEngine.UnityWebRequestTextureModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityWebRequestTextureModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule.cpp; sourceTree = SOURCE_ROOT; };
		4B927A0205588C3B49CC5110 /* UnityEngine.SpriteShapeModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SpriteShapeModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		4BC1C33C24833176E207546D /* System.Data.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data.cpp; sourceTree = SOURCE_ROOT; };
		4C0C4B11A8BFDF671CA131CF /* mscorlib_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = mscorlib_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib_Debugger.c; sourceTree = SOURCE_ROOT; };
		4C5826BC3AF1776F7B20B4E2 /* UnityEngine.UnityTestProtocolModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityTestProtocolModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityTestProtocolModule.cpp; sourceTree = SOURCE_ROOT; };
		4CC55811FE82BEE3AC9B535A /* Microsoft.Bcl.TimeProvider_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Microsoft.Bcl.TimeProvider_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Microsoft.Bcl.TimeProvider_CodeGen.c; sourceTree = SOURCE_ROOT; };
		4CF8EE25016BB90ABFD83417 /* System.Core__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core__3.cpp; sourceTree = SOURCE_ROOT; };
		4DE81098A8E6BD7AD67B3EF4 /* UnityEngine.WindModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.WindModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.WindModule.cpp; sourceTree = SOURCE_ROOT; };
		4E090A331F27884B0077B28D /* StoreReview.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StoreReview.m; sourceTree = "<group>"; };
		4E33F765E8E51E7DFF5D52A6 /* UnityEngine.TextCoreFontEngineModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreFontEngineModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp; sourceTree = SOURCE_ROOT; };
		4E74E57C06D4EB7EF04FD8C6 /* Assembly-CSharp__92.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__92.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__92.cpp"; sourceTree = SOURCE_ROOT; };
		4ED0B579112CB1E12AEF01F5 /* mscorlib.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib.cpp; sourceTree = SOURCE_ROOT; };
		4EEAF035005D81C6A49A7C45 /* UnityEngine.TextCoreTextEngineModule__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreTextEngineModule__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp; sourceTree = SOURCE_ROOT; };
		4F220071BD34BF6BF75F1125 /* UnityEngine.TerrainModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TerrainModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TerrainModule.cpp; sourceTree = SOURCE_ROOT; };
		4F97D266790DB44CD01959EC /* Assembly-CSharp__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__4.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__4.cpp"; sourceTree = SOURCE_ROOT; };
		501D596216B1F282D990909B /* Assembly-CSharp__91.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__91.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__91.cpp"; sourceTree = SOURCE_ROOT; };
		5031982034D7F5B402FA49EA /* Assembly-CSharp__72.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__72.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__72.cpp"; sourceTree = SOURCE_ROOT; };
		5039AE8A9B052FA9A784E00E /* System.Drawing_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Drawing_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Drawing_Debugger.c; sourceTree = SOURCE_ROOT; };
		504F10354AA6664ACC10AE55 /* Assembly-CSharp__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__7.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__7.cpp"; sourceTree = SOURCE_ROOT; };
		50FA716FD26EC3318C60967C /* Il2CppCCalculateFieldValues4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateFieldValues4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues4.cpp; sourceTree = SOURCE_ROOT; };
		51E0FA324635BD384CCB75D3 /* NativeCallProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = NativeCallProxy.h; path = Libraries/FlutterUnityIntegration/Plugins/iOS/NativeCallProxy.h; sourceTree = SOURCE_ROOT; };
		525CDBA21FAADF221B8950A5 /* Generics__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__5.cpp; sourceTree = SOURCE_ROOT; };
		53204F470DA89B666DECF64E /* UnityEngine.UI__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UI__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__4.cpp; sourceTree = SOURCE_ROOT; };
		53D50B1E0F71DFEBC9CABD14 /* UnityEngine.UIElementsModule__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__2.cpp; sourceTree = SOURCE_ROOT; };
		53E271A00C9F9828E4C3AEEB /* UnityEngine.VRModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.VRModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VRModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		544BB825B8624DFAB4CA9C2C /* UnityEngine.JSONSerializeModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.JSONSerializeModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		54EE3B312E220CA8996C49C7 /* Mono.Security__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Mono.Security__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Security__1.cpp; sourceTree = SOURCE_ROOT; };
		56115212599D5E43FF019CAB /* System__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__5.cpp; sourceTree = SOURCE_ROOT; };
		561FE8E0DA0AF097A24473A9 /* System__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__10.cpp; sourceTree = SOURCE_ROOT; };
		5623C57317FDCB0800090B9E /* Unity-iPhone Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Unity-iPhone Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		5623C57A17FDCB0900090B9E /* Unity-iPhone Tests-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Unity-iPhone Tests-Info.plist"; sourceTree = "<group>"; };
		5623C57C17FDCB0900090B9E /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		5623C57E17FDCB0900090B9E /* Unity_iPhone_Tests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Unity_iPhone_Tests.m; sourceTree = "<group>"; };
		5623C58017FDCB0900090B9E /* Unity-iPhone Tests-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Unity-iPhone Tests-Prefix.pch"; sourceTree = "<group>"; };
		5692F3DC0FA9D8E500EBA2F1 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		56965297FFBB2280B06DD136 /* UnityEngine.SpriteMaskModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.SpriteMaskModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteMaskModule.cpp; sourceTree = SOURCE_ROOT; };
		56B7959A1442E0F20026B3DD /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		56B795C11442E1100026B3DD /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		56BCBA380FCF049A0030C3B2 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		56C56C9717D6015100616839 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = "Unity-iPhone/Images.xcassets"; sourceTree = "<group>"; };
		56DBF99C15E3CDC9007A4A8D /* iPhone_Sensors.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = iPhone_Sensors.mm; sourceTree = "<group>"; };
		56DBF99E15E3CE85007A4A8D /* iPhone_Sensors.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = iPhone_Sensors.h; sourceTree = "<group>"; };
		56FD43950ED4745200FE3770 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		581B5EF0511A520BC9497338 /* UnityEngine.UnityWebRequestAudioModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestAudioModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAudioModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		582018FF346E4A041A401DB2 /* Newtonsoft.Json_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Newtonsoft.Json_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json_Debugger.c; sourceTree = SOURCE_ROOT; };
		58C462906CFE1012884EAB16 /* System.Core__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core__1.cpp; sourceTree = SOURCE_ROOT; };
		58D0A5E12D87A4B766719265 /* mscorlib__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__4.cpp; sourceTree = SOURCE_ROOT; };
		58D3A7800BA0F8BFFDC7DDAA /* UnityEngine.GridModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.GridModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		5904DF798F0A0B60FDE28972 /* UnityEngine.UmbraModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UmbraModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UmbraModule.cpp; sourceTree = SOURCE_ROOT; };
		5975A90725D533A536509F55 /* Assembly-CSharp__88.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__88.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__88.cpp"; sourceTree = SOURCE_ROOT; };
		5A1480F12D5DBF237742A501 /* Unity.VisualScripting.Core.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.VisualScripting.Core.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.VisualScripting.Core.cpp; sourceTree = SOURCE_ROOT; };
		5A3A1B4C3FB1FC90023B942E /* LaunchScreen-iPhone.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "LaunchScreen-iPhone.storyboard"; sourceTree = SOURCE_ROOT; };
		5B2F54EB41B243A7C1793B5C /* UnityEngine.TextCoreTextEngineModule__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreTextEngineModule__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp; sourceTree = SOURCE_ROOT; };
		5B53B64A88569D3F8EB8A9F1 /* Assembly-CSharp-firstpass__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp-firstpass__1.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__1.cpp"; sourceTree = SOURCE_ROOT; };
		5BAD78601F2B5A59006103DE /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		5C3AA7AEC8BCC431E5FEEC04 /* Assembly-CSharp__37.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__37.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__37.cpp"; sourceTree = SOURCE_ROOT; };
		5CE747857641C3A4C4FD32A4 /* System.Xml__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__13.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__13.cpp; sourceTree = SOURCE_ROOT; };
		5D2CB0C1599CC73B675915AB /* Assembly-CSharp__43.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__43.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__43.cpp"; sourceTree = SOURCE_ROOT; };
		5E6EECA05C59F494EE5D3F45 /* System.Runtime.Serialization_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Runtime.Serialization_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization_CodeGen.c; sourceTree = SOURCE_ROOT; };
		5E93FF130E65841D72EE889D /* Assembly-CSharp__46.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__46.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__46.cpp"; sourceTree = SOURCE_ROOT; };
		5EA521C38706003E7FF399CD /* Il2CppMetadataRegistration.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppMetadataRegistration.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataRegistration.c; sourceTree = SOURCE_ROOT; };
		5EBDAF8BD2370611732140AE /* UnityEngine.HotReloadModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.HotReloadModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.HotReloadModule.cpp; sourceTree = SOURCE_ROOT; };
		5FA2C48F1E333FAD0D36009D /* UnityEngine.PropertiesModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.PropertiesModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		6032091DC775FCFCB4E4D89D /* Assembly-CSharp__71.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__71.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__71.cpp"; sourceTree = SOURCE_ROOT; };
		60A2A5634B079CBE8AA2AB50 /* UnityEngine.SharedInternalsModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SharedInternalsModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		60B9DB6A1EF87ABF75AA1505 /* UnityEngine.DirectorModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.DirectorModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.DirectorModule.cpp; sourceTree = SOURCE_ROOT; };
		60F8566A9938D8EEF754221A /* UnityEngine.WindModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.WindModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.WindModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		61644B28313FCE72AA0F7731 /* Il2CppReversePInvokeWrapperTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppReversePInvokeWrapperTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppReversePInvokeWrapperTable.cpp; sourceTree = SOURCE_ROOT; };
		61703DF54F612431C9B2CEC6 /* UnityEngine.IMGUIModule__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.IMGUIModule__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule__1.cpp; sourceTree = SOURCE_ROOT; };
		625C59B7F72E20CA1DAD37D1 /* Assembly-CSharp__29.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__29.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__29.cpp"; sourceTree = SOURCE_ROOT; };
		6268E9B44CD8602C2A9D5536 /* GenericMethods__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__4.cpp; sourceTree = SOURCE_ROOT; };
		626C4499FCB90C9951F0B3D0 /* UnityEngine.GridModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.GridModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		62B31F6AE6313940631A3B13 /* Il2CppInteropDataTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppInteropDataTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppInteropDataTable.cpp; sourceTree = SOURCE_ROOT; };
		62CBF79C3A505CA169C0A2F8 /* UnityEngine.InputLegacyModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.InputLegacyModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		64E2DA6CA86A91002CE87FE3 /* UnityEngine.UIElementsModule__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__6.cpp; sourceTree = SOURCE_ROOT; };
		652C3B4418CC4076B5B964F7 /* UnityEngine.XRModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.XRModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.XRModule.cpp; sourceTree = SOURCE_ROOT; };
		65A609DC99CBAA15044D5D88 /* mscorlib__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__7.cpp; sourceTree = SOURCE_ROOT; };
		65D16328C6C383697132B27B /* R3_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = R3_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/R3_CodeGen.c; sourceTree = SOURCE_ROOT; };
		6679B66625DA79D57B50501A /* UnityClassRegistration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityClassRegistration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityClassRegistration.cpp; sourceTree = SOURCE_ROOT; };
		672987B2BECEFF858A090C4A /* UnityEngine.UI__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UI__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__1.cpp; sourceTree = SOURCE_ROOT; };
		6762A34FEF91061AC451841F /* UnityEngine.UI_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UI_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c; sourceTree = SOURCE_ROOT; };
		677B3A5BE7B2CB7BD477BB0A /* UnityEngine.InputLegacyModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.InputLegacyModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule.cpp; sourceTree = SOURCE_ROOT; };
		6793EED162DFAA97FA2F9745 /* UnityEngine.XRModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.XRModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.XRModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		679ACDCF6D14CA6FE6F81479 /* IUnityInterface.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IUnityInterface.h; path = Classes/Unity/IUnityInterface.h; sourceTree = SOURCE_ROOT; };
		67B80E918530D8754535F3F9 /* Il2CppCodeRegistration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCodeRegistration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCodeRegistration.cpp; sourceTree = SOURCE_ROOT; };
		67F0DF2A0A095662168CF329 /* UnityEngine.UmbraModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UmbraModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UmbraModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		681AA63C19788ACA381FB8C1 /* LaunchScreen-iPhonePortrait.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "LaunchScreen-iPhonePortrait.png"; sourceTree = SOURCE_ROOT; };
		68D8C1664EA1B56E9061D907 /* UniTask_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UniTask_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UniTask_Debugger.c; sourceTree = SOURCE_ROOT; };
		6968D18C16085C2E74DA6431 /* Unity.VisualScripting.Core_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.VisualScripting.Core_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.VisualScripting.Core_Debugger.c; sourceTree = SOURCE_ROOT; };
		69878364FA46AF9AF01A1E2D /* UnityEngine.UnityConnectModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityConnectModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityConnectModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		69BE7C8970379AE244085D79 /* mscorlib__16.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__16.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__16.cpp; sourceTree = SOURCE_ROOT; };
		6A15947266FEEEC3FE52FF89 /* UnityEngine.AudioModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AudioModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		6A8E9CCC95DBFF719234E05A /* Assembly-CSharp__103.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__103.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__103.cpp"; sourceTree = SOURCE_ROOT; };
		6ACBE790647C380298DD6CF0 /* UnityEngine.UIElementsModule__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__13.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__13.cpp; sourceTree = SOURCE_ROOT; };
		6AF6E713D4C420DE4590CB06 /* Assembly-CSharp__51.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__51.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__51.cpp"; sourceTree = SOURCE_ROOT; };
		6BED2FDDD9A6A84C09DCE287 /* UnityEngine.ContentLoadModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.ContentLoadModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ContentLoadModule.cpp; sourceTree = SOURCE_ROOT; };
		6C99EF8B0B6349FDCBC9E290 /* UnityEngine.UnityTestProtocolModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityTestProtocolModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityTestProtocolModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		6D100D31A5258D5757D1271A /* UnityEngine.UIElementsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule.cpp; sourceTree = SOURCE_ROOT; };
		6DAB81F028AF6E963A2D1970 /* System_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System_Debugger.c; sourceTree = SOURCE_ROOT; };
		6DF9A4610A43B72DCECA9C9D /* Assembly-CSharp__73.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__73.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__73.cpp"; sourceTree = SOURCE_ROOT; };
		6ECF4D044F64234638E5FC65 /* UnityEngine.ClothModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.ClothModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ClothModule.cpp; sourceTree = SOURCE_ROOT; };
		6F192CC9204E890AC63A7D6E /* Mono.Data.Sqlite_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Mono.Data.Sqlite_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Data.Sqlite_Debugger.c; sourceTree = SOURCE_ROOT; };
		6F3766F996C276272977836B /* Unity.Burst.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Burst.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Burst.cpp; sourceTree = SOURCE_ROOT; };
		6F5AA23CDF74E46493B84C24 /* Assembly-CSharp__94.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__94.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__94.cpp"; sourceTree = SOURCE_ROOT; };
		6F9722D0330536FF815E94C5 /* System.Xml__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__1.cpp; sourceTree = SOURCE_ROOT; };
		6FAB350C926C6A86ECCBC783 /* UnityEngine.SubstanceModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SubstanceModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubstanceModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		703F80825404E22CCED85738 /* UnityEngine.UnityConnectModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityConnectModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityConnectModule.cpp; sourceTree = SOURCE_ROOT; };
		70DB5ACDDC37E7CA9E4EDC4C /* UnityEngine.GameCenterModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.GameCenterModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		71064449DE869E361D0759B6 /* Assembly-CSharp__104.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__104.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__104.cpp"; sourceTree = SOURCE_ROOT; };
		7143E69478DF47AA204760BF /* System.Data__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__10.cpp; sourceTree = SOURCE_ROOT; };
		71520C37DB84A0B4A8E0F46F /* System.Xml.Linq_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Xml.Linq_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq_CodeGen.c; sourceTree = SOURCE_ROOT; };
		715666142DF9AB95ECEA536D /* UnityEngine.CoreModule__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__4.cpp; sourceTree = SOURCE_ROOT; };
		71A76431EB6A5AAC9E16CE06 /* Assembly-CSharp__62.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__62.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__62.cpp"; sourceTree = SOURCE_ROOT; };
		7251EA772488E8D0B647AA11 /* UnityEngine.CoreModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.CoreModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		727353E0A17F3CD26C387C4B /* UnityEngine.UIModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UIModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		72CDAAB9A750C4C524BA0E01 /* UnityEngine.VehiclesModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.VehiclesModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VehiclesModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		732F023F742A46D0095260A9 /* Assembly-CSharp-firstpass_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = "Assembly-CSharp-firstpass_CodeGen.c"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass_CodeGen.c"; sourceTree = SOURCE_ROOT; };
		733D2142097013827A6FD7A8 /* Il2CppGenericClassTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericClassTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericClassTable.c; sourceTree = SOURCE_ROOT; };
		734DA6B71EC643623B6729C5 /* Assembly-CSharp__65.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__65.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__65.cpp"; sourceTree = SOURCE_ROOT; };
		73E1EB45A942E11178F3915F /* UnityEngine.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.cpp; sourceTree = SOURCE_ROOT; };
		73FD00F17EEAD9A00F37E623 /* Assembly-CSharp__90.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__90.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__90.cpp"; sourceTree = SOURCE_ROOT; };
		74EC73663D0BD731F26EF000 /* UnityEngine.HotReloadModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.HotReloadModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.HotReloadModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		74FECA58CE4743229729F562 /* Unity.TextMeshPro_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.TextMeshPro_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro_Debugger.c; sourceTree = SOURCE_ROOT; };
		750BF4D08D860C9A70ACE432 /* System.Xml__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__12.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__12.cpp; sourceTree = SOURCE_ROOT; };
		759AB2B41E34648F0B834DEA /* UnityEngine.LocalizationModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.LocalizationModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.LocalizationModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		76159C624A1386AC23D3E469 /* UnityEngine.UIElementsModule__18.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__18.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__18.cpp; sourceTree = SOURCE_ROOT; };
		76A06ED55BC26ABEC74D9B5E /* UnityEngine.UnityWebRequestTextureModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestTextureModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		76CD73C928A465C0F9B1FA48 /* UnityEngine.SubsystemsModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SubsystemsModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubsystemsModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		76E90196E5C6B8BA16A9EB4C /* System_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System_CodeGen.c; sourceTree = SOURCE_ROOT; };
		7771951A12B3A7284778E4C6 /* System__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__7.cpp; sourceTree = SOURCE_ROOT; };
		780EF42D0F810F6942D61886 /* Assembly-CSharp__50.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__50.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__50.cpp"; sourceTree = SOURCE_ROOT; };
		782F20A8C011459AB9E0A41E /* System__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__6.cpp; sourceTree = SOURCE_ROOT; };
		79278D851CECE04A1B143367 /* UnityEngine.ContentLoadModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ContentLoadModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ContentLoadModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		7931F0EB3287754DA068276D /* System.Xml__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__11.cpp; sourceTree = SOURCE_ROOT; };
		79AEFAA2999CF84161DD28DE /* UnityEngine.CoreModule__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__8.cpp; sourceTree = SOURCE_ROOT; };
		79B3CF3B88129FCFF7234C25 /* Assembly-CSharp__68.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__68.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__68.cpp"; sourceTree = SOURCE_ROOT; };
		7A0126D9A77C3A2BC78EAC19 /* UnityEngine.PhysicsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.PhysicsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule.cpp; sourceTree = SOURCE_ROOT; };
		7BCD68DC79BE5C86BB4574CB /* Unity.Burst_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Burst_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Burst_CodeGen.c; sourceTree = SOURCE_ROOT; };
		7C135DFA4622154078EFA2E6 /* System.Data_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Data_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Data_Debugger.c; sourceTree = SOURCE_ROOT; };
		7C2E380E3D2F92B20313DD1D /* Il2CppGenericMethodTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericMethodTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodTable.c; sourceTree = SOURCE_ROOT; };
		7C8909A9432588EF82DC1449 /* UnityEngine.UnityCurlModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityCurlModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityCurlModule.cpp; sourceTree = SOURCE_ROOT; };
		7CD8D832DF5E49F3229DE515 /* baselib.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = baselib.a; path = Libraries/baselib.a; sourceTree = SOURCE_ROOT; };
		7EEF5FB367689FB1ECEBB6A7 /* System.Xml__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__2.cpp; sourceTree = SOURCE_ROOT; };
		7F36C10E13C5C673007FBDD9 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		7F36C10F13C5C673007FBDD9 /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		7F36C11013C5C673007FBDD9 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		7F4E05A02717216D00A2CBE4 /* libGameAssembly.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libGameAssembly.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7FA39E9CC391DDDACEDDE1E9 /* Assembly-CSharp__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__9.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__9.cpp"; sourceTree = SOURCE_ROOT; };
		80CFB204F2EC78281EC6CF9E /* PGUnityPlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = PGUnityPlugin.h; path = Libraries/Plugins/iOS/PGUnityPlugin.h; sourceTree = SOURCE_ROOT; };
		811857DBA7707D83A80DEBA7 /* UniTask_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UniTask_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UniTask_CodeGen.c; sourceTree = SOURCE_ROOT; };
		817B6BBCBF9F078D00727259 /* System.Data_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Data_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Data_CodeGen.c; sourceTree = SOURCE_ROOT; };
		8239340CC04E228289A4A60F /* UnityEngine.ProfilerModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.ProfilerModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ProfilerModule.cpp; sourceTree = SOURCE_ROOT; };
		8288C1289036AC5BFE45E61D /* Unity.Burst_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Burst_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Burst_Debugger.c; sourceTree = SOURCE_ROOT; };
		830B5C100E5ED4C100C7819F /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		8358D1B70ED1CC3700E3A684 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		835C8F33E51F11E7A01A5885 /* System.Runtime.CompilerServices.Unsafe.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Runtime.CompilerServices.Unsafe.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Runtime.CompilerServices.Unsafe.cpp; sourceTree = SOURCE_ROOT; };
		83B2570A0E62FF8A00468741 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		83B2574B0E63022200468741 /* OpenAL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenAL.framework; path = System/Library/Frameworks/OpenAL.framework; sourceTree = SDKROOT; };
		83E06090B64757F3B9010574 /* Assembly-CSharp__64.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__64.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__64.cpp"; sourceTree = SOURCE_ROOT; };
		843F731F67294457F5D3CE6D /* UnityEngine.UmbraModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UmbraModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UmbraModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		848031E01C5160D700FCEAB7 /* UnityReplayKit_Scripting.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityReplayKit_Scripting.mm; sourceTree = "<group>"; };
		8482C9568E6A49FD58B18615 /* System__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__9.cpp; sourceTree = SOURCE_ROOT; };
		848BF6A7383F7523CFD9F1F2 /* Generics__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__13.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__13.cpp; sourceTree = SOURCE_ROOT; };
		84AB4AD7C79452A60789EBBD /* Assembly-CSharp__83.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__83.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__83.cpp"; sourceTree = SOURCE_ROOT; };
		84C6A84D75C32015E4995C33 /* System.Xml__16.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__16.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__16.cpp; sourceTree = SOURCE_ROOT; };
		84DC28F51C5137FE00BC67D7 /* UnityReplayKit.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityReplayKit.mm; sourceTree = "<group>"; };
		84DC28F71C51383500BC67D7 /* UnityReplayKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityReplayKit.h; sourceTree = "<group>"; };
		857AE3AA6D7217204F38E177 /* UnityEngine.GridModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.GridModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule.cpp; sourceTree = SOURCE_ROOT; };
		85D23FAC1867CA1EB72F971F /* UnityEngine.DSPGraphModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.DSPGraphModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.DSPGraphModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		85E5623620F4F4D1001DFEF6 /* UnityView+Keyboard.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityView+Keyboard.mm"; sourceTree = "<group>"; };
		85F9C154ED639637D65BCC5C /* UnityEngine.PerformanceReportingModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.PerformanceReportingModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PerformanceReportingModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		862C244F20AEC7AC006FB4AD /* UnityWebRequest.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityWebRequest.mm; sourceTree = "<group>"; };
		86D11D47EA7629E34BD50F12 /* Assembly-CSharp__107.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__107.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__107.cpp"; sourceTree = SOURCE_ROOT; };
		86D61A6AC37C64EC49B5261F /* System.Core__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core__2.cpp; sourceTree = SOURCE_ROOT; };
		87605856C2B447518B98EAF3 /* UnityICallRegistration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityICallRegistration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityICallRegistration.cpp; sourceTree = SOURCE_ROOT; };
		87E07729B348731BD1074CDF /* Assembly-CSharp__101.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__101.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__101.cpp"; sourceTree = SOURCE_ROOT; };
		88FC6E3CAED8A246BF94CBAC /* UnityEngine.DirectorModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.DirectorModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.DirectorModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		894C1B251937BE1BC2B4D36C /* IUnityGraphicsMetal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IUnityGraphicsMetal.h; path = Classes/Unity/IUnityGraphicsMetal.h; sourceTree = SOURCE_ROOT; };
		8992CFD7E59D57B6F6AA07C2 /* System.Data__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__9.cpp; sourceTree = SOURCE_ROOT; };
		89EF3E4A2B0003F14AAD225D /* UnityEngine.UI__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UI__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__2.cpp; sourceTree = SOURCE_ROOT; };
		8A142DC41636943E00DD87CA /* Keyboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Keyboard.h; sourceTree = "<group>"; };
		8A142DC51636943E00DD87CA /* Keyboard.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = Keyboard.mm; sourceTree = "<group>"; };
		8A16150B1A8E4362006FA788 /* FullScreenVideoPlayer.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = FullScreenVideoPlayer.mm; sourceTree = "<group>"; };
		8A20382C213D4B3C005E6C56 /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = System/Library/Frameworks/AVKit.framework; sourceTree = SDKROOT; };
		8A21AED21622F59300AF8007 /* UnityViewControllerBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityViewControllerBase.h; sourceTree = "<group>"; };
		8A25E6D118D767E20006A227 /* Filesystem.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = Filesystem.mm; sourceTree = "<group>"; };
		8A292A9717992CE100409BA4 /* LifeCycleListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LifeCycleListener.h; sourceTree = "<group>"; };
		8A292A9817992CE100409BA4 /* LifeCycleListener.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = LifeCycleListener.mm; sourceTree = "<group>"; };
		8A2AA93316E0978D001FB470 /* CMVideoSampling.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CMVideoSampling.h; sourceTree = "<group>"; };
		8A2AA93416E0978D001FB470 /* CMVideoSampling.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CMVideoSampling.mm; sourceTree = "<group>"; };
		8A2BC6DE245061EE00C7C97D /* NoGraphicsHelper.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = NoGraphicsHelper.mm; sourceTree = "<group>"; };
		8A367F5916A6D36F0012ED11 /* CVTextureCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CVTextureCache.h; sourceTree = "<group>"; };
		8A367F5A16A6D36F0012ED11 /* CVTextureCache.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CVTextureCache.mm; sourceTree = "<group>"; };
		8A3831B4246956AA00CD74FD /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		8A4815BF17A287D2003FBFD5 /* UnityAppController+ViewHandling.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UnityAppController+ViewHandling.h"; sourceTree = "<group>"; };
		8A4815C017A287D2003FBFD5 /* UnityAppController+ViewHandling.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityAppController+ViewHandling.mm"; sourceTree = "<group>"; };
		8A5C1490174E662D0006EB36 /* RenderPluginDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RenderPluginDelegate.h; sourceTree = "<group>"; };
		8A5C1491174E662D0006EB36 /* RenderPluginDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = RenderPluginDelegate.mm; sourceTree = "<group>"; };
		8A5E0B8F16849D1800CBB6FE /* DisplayManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DisplayManager.h; sourceTree = "<group>"; };
		8A5E0B9016849D1800CBB6FE /* DisplayManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = DisplayManager.mm; sourceTree = "<group>"; };
		8A6137121A10B57700059EDF /* ObjCRuntime.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ObjCRuntime.h; sourceTree = "<group>"; };
		8A6720A319EEB905006C92E0 /* InternalProfiler.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = InternalProfiler.cpp; sourceTree = "<group>"; };
		8A6720A419EEB905006C92E0 /* InternalProfiler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InternalProfiler.h; sourceTree = "<group>"; };
		8A6720A619EFAF25006C92E0 /* Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Prefix.pch; sourceTree = "<group>"; };
		8A7939FC1ED2F53200B44EF1 /* UnityViewControllerBase.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityViewControllerBase.mm; sourceTree = "<group>"; };
		8A7939FE1ED43EE100B44EF1 /* UnityView+iOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityView+iOS.h"; sourceTree = "<group>"; };
		8A7939FF1ED43EE100B44EF1 /* UnityView+iOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityView+iOS.mm"; sourceTree = "<group>"; };
		8A793A001ED43EE100B44EF1 /* UnityView+tvOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityView+tvOS.h"; sourceTree = "<group>"; };
		8A793A011ED43EE100B44EF1 /* UnityView+tvOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityView+tvOS.mm"; sourceTree = "<group>"; };
		8A793A021ED43EE100B44EF1 /* UnityViewControllerBase+iOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityViewControllerBase+iOS.h"; sourceTree = "<group>"; };
		8A793A031ED43EE100B44EF1 /* UnityViewControllerBase+iOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityViewControllerBase+iOS.mm"; sourceTree = "<group>"; };
		8A793A041ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityViewControllerBase+tvOS.h"; sourceTree = "<group>"; };
		8A793A051ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityViewControllerBase+tvOS.mm"; sourceTree = "<group>"; };
		8A851BA516FB2F6D00E911DB /* UnityView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityView.h; sourceTree = "<group>"; };
		8A851BA616FB2F6D00E911DB /* UnityView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityView.mm; sourceTree = "<group>"; };
		8A851BA816FB3AD000E911DB /* UnityAppController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityAppController.h; sourceTree = "<group>"; };
		8A851BA916FB3AD000E911DB /* UnityAppController.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityAppController.mm; sourceTree = "<group>"; };
		8A851BAB16FC875E00E911DB /* UnityInterface.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityInterface.h; sourceTree = "<group>"; };
		8A8D90D81A274A7800456C4E /* UnityAppController+UnityInterface.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityAppController+UnityInterface.h"; sourceTree = "<group>"; };
		8A8D90D91A274A7800456C4E /* UnityAppController+UnityInterface.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityAppController+UnityInterface.mm"; sourceTree = "<group>"; };
		8A90541019EE8843003D1039 /* UnityForwardDecls.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityForwardDecls.h; sourceTree = "<group>"; };
		8A9FCB111617295F00C05364 /* ActivityIndicator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ActivityIndicator.h; sourceTree = "<group>"; };
		8A9FCB121617295F00C05364 /* ActivityIndicator.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ActivityIndicator.mm; sourceTree = "<group>"; };
		8AA108C01948732900D0538B /* UnityRendering.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityRendering.h; sourceTree = "<group>"; };
		8AA5D80017ABE9AF007B9910 /* UnityAppController+Rendering.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityAppController+Rendering.h"; sourceTree = "<group>"; };
		8AA5D80117ABE9AF007B9910 /* UnityAppController+Rendering.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityAppController+Rendering.mm"; sourceTree = "<group>"; };
		8AA6ADDB17818CFD00A1C5F1 /* UnityTrampolineConfigure.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityTrampolineConfigure.h; sourceTree = "<group>"; };
		8AB3CB3C16D390BA00697AD5 /* VideoPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VideoPlayer.h; sourceTree = "<group>"; };
		8AB3CB3D16D390BB00697AD5 /* VideoPlayer.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = VideoPlayer.mm; sourceTree = "<group>"; };
		8ABDBCE019CAFCF700A842FF /* AVCapture.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AVCapture.h; sourceTree = "<group>"; };
		8AC71EC219E7FBA90027502F /* OrientationSupport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OrientationSupport.h; sourceTree = "<group>"; };
		8AC71EC319E7FBA90027502F /* OrientationSupport.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = OrientationSupport.mm; sourceTree = "<group>"; };
		8AC74A9419B47FEF00019D38 /* AVCapture.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AVCapture.mm; sourceTree = "<group>"; };
		8ACB801B177081D4005D0019 /* DeviceSettings.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = DeviceSettings.mm; sourceTree = "<group>"; };
		8ACB801D177081F7005D0019 /* Preprocessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Preprocessor.h; sourceTree = "<group>"; };
		8ADAC812E52229002239D46B /* UnityEngine.UnityWebRequestWWWModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityWebRequestWWWModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule.cpp; sourceTree = SOURCE_ROOT; };
		8ADCE38919C87177006F04F6 /* CameraCapture.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CameraCapture.h; sourceTree = "<group>"; };
		8ADCE38A19C87177006F04F6 /* CameraCapture.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CameraCapture.mm; sourceTree = "<group>"; };
		8AECDC781950835600CB29E8 /* UnityMetalSupport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityMetalSupport.h; sourceTree = "<group>"; };
		8AF7755E17997D1300341121 /* AppDelegateListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegateListener.h; sourceTree = "<group>"; };
		8AF7755F17997D1300341121 /* AppDelegateListener.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = AppDelegateListener.mm; sourceTree = "<group>"; };
		8B85E323FB282C9808D66C9A /* Assembly-CSharp__74.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__74.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__74.cpp"; sourceTree = SOURCE_ROOT; };
		8BEDFA3729C88F86007F26D7 /* UnityViewControllerBase+visionOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityViewControllerBase+visionOS.h"; sourceTree = "<group>"; };
		8BEDFA3829C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityViewControllerBase+visionOS.mm"; sourceTree = "<group>"; };
		8C939BCC03F2ED185E6B3E24 /* UnityEngine.TLSModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TLSModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TLSModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		8CAB5C6EF6FAC20D93CFF981 /* UnityEngine.UIElementsModule__16.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__16.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__16.cpp; sourceTree = SOURCE_ROOT; };
		8D1107310486CEB800E47090 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8DB79685079E7F8DE973185B /* UnityEngine.DSPGraphModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.DSPGraphModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.DSPGraphModule.cpp; sourceTree = SOURCE_ROOT; };
		8DF7D168881C3EF89850AD0A /* Generics__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__6.cpp; sourceTree = SOURCE_ROOT; };
		8E6728ADD56BA33B896627DA /* Generics__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__1.cpp; sourceTree = SOURCE_ROOT; };
		8EE47B422BDF2BE7441AA757 /* Newtonsoft.Json.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json.cpp; sourceTree = SOURCE_ROOT; };
		8F13D042492DBEB7CF96DC23 /* Generics__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__9.cpp; sourceTree = SOURCE_ROOT; };
		8FE3E92069BB36F8854D7277 /* __Generated_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = __Generated_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c; sourceTree = SOURCE_ROOT; };
		90114C270D8D46748478032F /* UnityEngine.CoreModule__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__1.cpp; sourceTree = SOURCE_ROOT; };
		901CB410390827075F68C913 /* UnityEngine.VideoModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.VideoModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VideoModule.cpp; sourceTree = SOURCE_ROOT; };
		90E69A22DD43A16E030D8110 /* UnityEngine.UnityAnalyticsCommonModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityAnalyticsCommonModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		90F170FC52D84C4EA00E7116 /* Assembly-CSharp__61.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__61.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__61.cpp"; sourceTree = SOURCE_ROOT; };
		9144340CC541DD77AADA21F3 /* System__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__2.cpp; sourceTree = SOURCE_ROOT; };
		91B3C31E164148A3C3C440BA /* Assembly-CSharp__31.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__31.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__31.cpp"; sourceTree = SOURCE_ROOT; };
		9295ED0F8281EADD6E244700 /* UnityEngine.UIElementsModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UIElementsModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		92E17166D3BDB3EA24239E77 /* mscorlib__20.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__20.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__20.cpp; sourceTree = SOURCE_ROOT; };
		93022D3C91405460FF1AFE7E /* Assembly-CSharp-firstpass_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = "Assembly-CSharp-firstpass_Debugger.c"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass_Debugger.c"; sourceTree = SOURCE_ROOT; };
		9315E97CF25DEC97ED24E7FE /* UnityEngine.ParticleSystemModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ParticleSystemModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		9379D2F4AB004FA422D73616 /* Assembly-CSharp__99.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__99.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__99.cpp"; sourceTree = SOURCE_ROOT; };
		93889A7E87D040174319E1BD /* UnityEngine.SubstanceModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SubstanceModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubstanceModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		93CB0776020C99B75942BABB /* Assembly-CSharp__28.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__28.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__28.cpp"; sourceTree = SOURCE_ROOT; };
		93E6825D15CDF948BB0DCB44 /* Il2CppCCalculateTypeValues2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateTypeValues2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues2.cpp; sourceTree = SOURCE_ROOT; };
		9432641D8FAD3395C648341C /* UnityEngine.Physics2DModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.Physics2DModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9497B750CCFB7578BDEF70E5 /* GenericMethods__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__2.cpp; sourceTree = SOURCE_ROOT; };
		960391211D6CE46E003BF157 /* MediaToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaToolbox.framework; path = System/Library/Frameworks/MediaToolbox.framework; sourceTree = SDKROOT; };
		960816A18612315549B46E03 /* Assembly-CSharp__70.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__70.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__70.cpp"; sourceTree = SOURCE_ROOT; };
		9664DF812E72471EB20F5443 /* Unity.TextMeshPro__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__2.cpp; sourceTree = SOURCE_ROOT; };
		979CE552E8632B2F59D7A75B /* UnityEngine.AssetBundleModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AssetBundleModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		97EFDE40B546C8DD238C8231 /* Il2CppCCalculateFieldValues3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateFieldValues3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues3.cpp; sourceTree = SOURCE_ROOT; };
		980A11C75E6594F47A4D0C4E /* System.Xml__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__8.cpp; sourceTree = SOURCE_ROOT; };
		996AF65B21117545805EDE7A /* UnityEngine.UnityWebRequestModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityWebRequestModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule.cpp; sourceTree = SOURCE_ROOT; };
		996B8B35A5892AE2820D8033 /* Il2CppTypeDefinitions.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppTypeDefinitions.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppTypeDefinitions.c; sourceTree = SOURCE_ROOT; };
		9A3270E8F2A4E1126343E8A4 /* UnityEngine.ARModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.ARModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ARModule.cpp; sourceTree = SOURCE_ROOT; };
		9B7E46021F9A4569A2F8B849 /* UnityEngine.GIModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.GIModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GIModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9C067E18382CE087FF58784D /* UnityEngine.ARModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ARModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ARModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9C5F4A52C023FBEBCB9B9C51 /* Assembly-CSharp__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__11.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__11.cpp"; sourceTree = SOURCE_ROOT; };
		9C6571465C64216B59151280 /* UnityEngine.JSONSerializeModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.JSONSerializeModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule.cpp; sourceTree = SOURCE_ROOT; };
		9CC7631EEE51DCCD5E2C1ABA /* UnityEngine.UIElementsModule__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__15.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__15.cpp; sourceTree = SOURCE_ROOT; };
		9CE38A0EB9BA95FB48A7537E /* UnityEngine.TextRenderingModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TextRenderingModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9D074ECA115BCF455EE2E8EF /* UnityEngine.GIModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.GIModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GIModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		9D0A618A21BFE7F30094DC33 /* libiconv.2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.2.tbd; path = usr/lib/libiconv.2.tbd; sourceTree = SDKROOT; };
		9D0A7564828238ED0C6F4AFD /* UnityEngine.AIModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AIModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AIModule.cpp; sourceTree = SOURCE_ROOT; };
		9D16CD8021C938B300DD46C0 /* UndefinePlatforms.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UndefinePlatforms.h; sourceTree = "<group>"; };
		9D16CD8121C938BB00DD46C0 /* RedefinePlatforms.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RedefinePlatforms.h; sourceTree = "<group>"; };
		9D25AB9D213FB47800354C27 /* UnityFramework.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = UnityFramework.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9D25AB9F213FB47800354C27 /* UnityFramework.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityFramework.h; sourceTree = "<group>"; };
		9D25ABA0213FB47800354C27 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9D3B639B6E4EB4B913EDABF2 /* Unity.TextMeshPro__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__3.cpp; sourceTree = SOURCE_ROOT; };
		9DA3B0432174CB96001678C7 /* main.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = main.mm; sourceTree = "<group>"; };
		9E43ABBC7220989A041EB4AE /* UnityEngine.PhysicsModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.PhysicsModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		9E71E7DCDFD803C6794F987C /* Mono.Data.Sqlite_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Mono.Data.Sqlite_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Data.Sqlite_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9E90AE8AEA3FE3DC0C6E0BED /* UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9EA3E57A19EDE7497E36F3DF /* UnityEngine.GameCenterModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.GameCenterModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9F0C5E0D39B881CEC215D850 /* Unity.Burst.Unsafe_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Burst.Unsafe_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Burst.Unsafe_Debugger.c; sourceTree = SOURCE_ROOT; };
		A0681946D39E04E92D886E8C /* Assembly-CSharp__56.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__56.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__56.cpp"; sourceTree = SOURCE_ROOT; };
		A1B3624D0AECCFE9A3417712 /* System.Drawing.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Drawing.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Drawing.cpp; sourceTree = SOURCE_ROOT; };
		A22FCCAA9050EDFB439D8723 /* Microsoft.Bcl.TimeProvider_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Microsoft.Bcl.TimeProvider_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Microsoft.Bcl.TimeProvider_Debugger.c; sourceTree = SOURCE_ROOT; };
		A239769F708DCF300ED81D67 /* Assembly-CSharp__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__14.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__14.cpp"; sourceTree = SOURCE_ROOT; };
		A2562FA24FCA1F0C707324C8 /* UnityEngine.UnityWebRequestWWWModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestWWWModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		A2B051349EB576E097B197CA /* Unity.Mathematics_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Mathematics_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Mathematics_Debugger.c; sourceTree = SOURCE_ROOT; };
		A34734E12320BFE93D77614B /* Assembly-CSharp__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__5.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__5.cpp"; sourceTree = SOURCE_ROOT; };
		A39A600A7B98C2458EAED5FA /* UnityEngine.VRModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.VRModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VRModule.cpp; sourceTree = SOURCE_ROOT; };
		A40145096953336761F9ECA1 /* UnityEngine.VehiclesModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.VehiclesModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VehiclesModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		A4591E5013E0219646DFFBD4 /* UnityEngine.GameCenterModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.GameCenterModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule.cpp; sourceTree = SOURCE_ROOT; };
		A45FF8EA2070EB8106DAC2EF /* System.Transactions_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Transactions_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Transactions_CodeGen.c; sourceTree = SOURCE_ROOT; };
		A504EDDC3C17119152F24F7F /* GenericMethods__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__3.cpp; sourceTree = SOURCE_ROOT; };
		A6444ABDD2799CC877EBAF7E /* LaunchScreen-iPad.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "LaunchScreen-iPad.storyboard"; sourceTree = SOURCE_ROOT; };
		A736D24CED71BDA3F309CC8E /* Il2CppCCalculateFieldValues2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateFieldValues2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues2.cpp; sourceTree = SOURCE_ROOT; };
		A7CC23DF498D4EFEEB0AF888 /* UnityEngine.AnimationModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AnimationModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		A849FEA549C242344B5B7C14 /* Assembly-CSharp__45.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__45.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__45.cpp"; sourceTree = SOURCE_ROOT; };
		A84D8F84F862A6F5608E5F7A /* UnityEngine.TerrainModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TerrainModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TerrainModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		A882C70BF6B0539484D63840 /* Assembly-CSharp__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__13.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__13.cpp"; sourceTree = SOURCE_ROOT; };
		A918C8B44A33797E60C31B4D /* UnityEngine.ImageConversionModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ImageConversionModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		A9CF3A2F17D70FECA132802A /* System.Runtime.CompilerServices.Unsafe_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Runtime.CompilerServices.Unsafe_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Runtime.CompilerServices.Unsafe_CodeGen.c; sourceTree = SOURCE_ROOT; };
		AA31BF961B55660D0013FB1B /* Data */ = {isa = PBXFileReference; lastKnownFileType = folder; path = Data; sourceTree = "<group>"; };
		AA5D99861AFAD3C800B27605 /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		AAC3E38B1A68945900F6174A /* RegisterFeatures.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = RegisterFeatures.cpp; sourceTree = "<group>"; };
		AAC3E38C1A68945900F6174A /* RegisterFeatures.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RegisterFeatures.h; sourceTree = "<group>"; };
		AAC85F52F8C010404109B18C /* Mono.Security_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Mono.Security_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Security_CodeGen.c; sourceTree = SOURCE_ROOT; };
		AAFE69D019F187C200638316 /* UnityViewControllerListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityViewControllerListener.h; sourceTree = "<group>"; };
		AAFE69D119F187C200638316 /* UnityViewControllerListener.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityViewControllerListener.mm; sourceTree = "<group>"; };
		AB2BF8F7BAFBE6886580905D /* mscorlib__22.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__22.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__22.cpp; sourceTree = SOURCE_ROOT; };
		ABF66CEE0A3ACFFBDA78140E /* UnityEngine.UIElementsModule__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__12.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__12.cpp; sourceTree = SOURCE_ROOT; };
		ACE173F91BF3866BEA121D66 /* UnityEngine.SharedInternalsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SharedInternalsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		AD3C154F80F47532952859E9 /* Assembly-CSharp__106.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__106.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__106.cpp"; sourceTree = SOURCE_ROOT; };
		AE25855F1DB729A61745C8CB /* Il2CppGenericAdjustorThunkTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericAdjustorThunkTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericAdjustorThunkTable.c; sourceTree = SOURCE_ROOT; };
		AED95C519544DEF0103B9C95 /* __Generated.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = __Generated.cpp; path = Il2CppOutputProject/Source/il2cppOutput/__Generated.cpp; sourceTree = SOURCE_ROOT; };
		AF0D68325B7E9D496A51734C /* UnityEngine.UnityWebRequestAssetBundleModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityWebRequestAssetBundleModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule.cpp; sourceTree = SOURCE_ROOT; };
		AFAC13389EEA2957CD9F0B97 /* UnityEngine.TerrainPhysicsModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TerrainPhysicsModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TerrainPhysicsModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		B0F046F0360DDF893A8A4FD2 /* Unity.VisualScripting.Core__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.VisualScripting.Core__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.VisualScripting.Core__2.cpp; sourceTree = SOURCE_ROOT; };
		B13B5CAA12B4643FE811ED64 /* UnityEngine.UnityWebRequestModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		B16FC38963BF1636A8CECA90 /* UnityEngine.TerrainPhysicsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TerrainPhysicsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TerrainPhysicsModule.cpp; sourceTree = SOURCE_ROOT; };
		B189DAEDDE23ED3D43BA6111 /* UnityEngine.AssetBundleModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AssetBundleModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		B1E720AFEEADE2716193EB8A /* Assembly-CSharp__84.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__84.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__84.cpp"; sourceTree = SOURCE_ROOT; };
		B2C5B578DC19408E99C7402B /* UnityEngine.ARModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ARModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ARModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		B308234A6A71A74A630CA80A /* UnityEngine.AIModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AIModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AIModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		B315EBF0BC65192DC23290CD /* Newtonsoft.Json__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__1.cpp; sourceTree = SOURCE_ROOT; };
		B322A23CD4D617B766B38780 /* Assembly-CSharp__60.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__60.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__60.cpp"; sourceTree = SOURCE_ROOT; };
		B39436E255FF5376259080BE /* mscorlib__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__6.cpp; sourceTree = SOURCE_ROOT; };
		B3B3B55ACB389C47124F63DC /* System.Xml__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__9.cpp; sourceTree = SOURCE_ROOT; };
		B3B78795CA5004C3F52079A6 /* UnityEngine.ContentLoadModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ContentLoadModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ContentLoadModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		B4A359AB8EAFEB14CB1C2FD1 /* Assembly-CSharp__85.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__85.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__85.cpp"; sourceTree = SOURCE_ROOT; };
		B5089176EA3ED5DF6BCDDF10 /* Assembly-CSharp__97.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__97.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__97.cpp"; sourceTree = SOURCE_ROOT; };
		B58EF12BF9B65BC74A92DC07 /* AppleRequiredReasonCSharpAPIs.txt */ = {isa = PBXFileReference; lastKnownFileType = text; name = AppleRequiredReasonCSharpAPIs.txt; path = UnityFramework/AppleRequiredReasonCSharpAPIs.txt; sourceTree = SOURCE_ROOT; };
		B637B07876CF12586D7C9CD5 /* UnityEngine.AnimationModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AnimationModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		B65026F4E0A439EAE4490865 /* UnityEngine.IMGUIModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.IMGUIModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		B662E7588FD9F79EDCF63D17 /* Il2CppCCalculateFieldValues.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateFieldValues.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues.cpp; sourceTree = SOURCE_ROOT; };
		B6B32C7D1EE6F0853DDDAA61 /* Assembly-CSharp__35.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__35.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__35.cpp"; sourceTree = SOURCE_ROOT; };
		B803F94314B18CB6B2655B90 /* UnityEngine.AIModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AIModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AIModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		B80FFAA110DC5159E206F4F5 /* UnityEngine.ClothModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ClothModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ClothModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		B977519F68E3B7F6C9CB761F /* Assembly-CSharp__93.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__93.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__93.cpp"; sourceTree = SOURCE_ROOT; };
		B9ABC75052207F54EEA14BA2 /* Assembly-CSharp__76.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__76.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__76.cpp"; sourceTree = SOURCE_ROOT; };
		BB7DCF84385C419D6C313CDF /* Assembly-CSharp__67.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__67.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__67.cpp"; sourceTree = SOURCE_ROOT; };
		BBA209A675B16140CD14E2A2 /* Mono.Security_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Mono.Security_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Security_Debugger.c; sourceTree = SOURCE_ROOT; };
		BD803104C2AE3F66699C2C08 /* UnityEngine.JSONSerializeModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.JSONSerializeModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		BDACB114054C760C9D862236 /* UnityEngine.SpriteMaskModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SpriteMaskModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteMaskModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		BE33F4A8ED55CC34EB5BDF80 /* System.Xml__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__14.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__14.cpp; sourceTree = SOURCE_ROOT; };
		BE89081E35159488B1CFEDD6 /* Assembly-CSharp__32.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__32.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__32.cpp"; sourceTree = SOURCE_ROOT; };
		BF026C43C97DF354A316F9F4 /* Generics__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__12.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__12.cpp; sourceTree = SOURCE_ROOT; };
		C00B25CE1E6F85C6F63C5984 /* UnityEngine.UnityConnectModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityConnectModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityConnectModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		C08898ABC22AC52604E841BD /* Assembly-CSharp__41.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__41.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__41.cpp"; sourceTree = SOURCE_ROOT; };
		C0D23F3F8BED210275372D69 /* Newtonsoft.Json__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__3.cpp; sourceTree = SOURCE_ROOT; };
		C0E20C4BEB0D765659856866 /* UnityEngine.CoreModule__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__7.cpp; sourceTree = SOURCE_ROOT; };
		C0FC069B611EACD4E344ED53 /* System.Data__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__5.cpp; sourceTree = SOURCE_ROOT; };
		C1D2C2900B1B1033C50FDC84 /* Assembly-CSharp__22.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__22.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__22.cpp"; sourceTree = SOURCE_ROOT; };
		C20B2680F0033B47C3BB8134 /* UnityEngine.UIElementsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UIElementsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C24D3A15A69D047679E1AF2F /* UnityEngine.AudioModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AudioModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule.cpp; sourceTree = SOURCE_ROOT; };
		C2BA20DA462F4D826FE30857 /* R3.Unity.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = R3.Unity.cpp; path = Il2CppOutputProject/Source/il2cppOutput/R3.Unity.cpp; sourceTree = SOURCE_ROOT; };
		C3C2D72757B1A8A28AE76822 /* mscorlib__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__10.cpp; sourceTree = SOURCE_ROOT; };
		C3D653AC6742EB0121722217 /* UnityEngine.ScreenCaptureModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ScreenCaptureModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ScreenCaptureModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C40C9D5FB5363330524518CB /* Assembly-CSharp.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp.cpp"; sourceTree = SOURCE_ROOT; };
		C47CCFA87796F12A1228E727 /* UnityEngine.UnityWebRequestAssetBundleModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestAssetBundleModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		C50B188E0276BA2E26358CED /* UnityEngine.PerformanceReportingModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.PerformanceReportingModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PerformanceReportingModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		C531584915BF8819C6496BE0 /* Newtonsoft.Json_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Newtonsoft.Json_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C5E03A93B2A56B97660E2F86 /* Assembly-CSharp__89.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__89.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__89.cpp"; sourceTree = SOURCE_ROOT; };
		C681789DEFADCD202ECA939C /* Unity.Collections_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Collections_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Collections_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C6B21A7CC65E934695520510 /* System.Core__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core__4.cpp; sourceTree = SOURCE_ROOT; };
		C74C563E02CE38C5DC9590E7 /* Generics__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__2.cpp; sourceTree = SOURCE_ROOT; };
		C781041026C372AE365E9C12 /* UniTask.Linq_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UniTask.Linq_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UniTask.Linq_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C7CB245619C3670C141BB501 /* UnityEngine.UnityWebRequestModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		C862D3A89A9BA3E4ED7205BD /* System.Configuration_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Configuration_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Configuration_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C865CD21D06B3F35209C5FA5 /* Generics__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__7.cpp; sourceTree = SOURCE_ROOT; };
		C86635207E96D385500A4A2C /* Assembly-CSharp__38.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__38.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__38.cpp"; sourceTree = SOURCE_ROOT; };
		C89119454D57EDC42A59D87C /* UnityEngine.TextRenderingModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextRenderingModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule.cpp; sourceTree = SOURCE_ROOT; };
		C922426E10576375DD5017D3 /* IngameDebugConsole.Runtime.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = IngameDebugConsole.Runtime.cpp; path = Il2CppOutputProject/Source/il2cppOutput/IngameDebugConsole.Runtime.cpp; sourceTree = SOURCE_ROOT; };
		CA2FE0E937B9F3EBF23597D5 /* Il2CppCCTypeValuesTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCTypeValuesTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCTypeValuesTable.cpp; sourceTree = SOURCE_ROOT; };
		CAB37C0B5E85683C4FB82A76 /* mscorlib__17.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__17.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__17.cpp; sourceTree = SOURCE_ROOT; };
		CABC68E3E532B24695A5FED6 /* Assembly-CSharp__95.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__95.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__95.cpp"; sourceTree = SOURCE_ROOT; };
		CAE0DAFFEEF167F8D2A2BC72 /* NativeCallProxy.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = NativeCallProxy.mm; path = Libraries/FlutterUnityIntegration/Plugins/iOS/NativeCallProxy.mm; sourceTree = SOURCE_ROOT; };
		CB6390412026FA54BD728F2E /* System__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__3.cpp; sourceTree = SOURCE_ROOT; };
		CD4079BF78197D083920D996 /* UnityEngine.UnityAnalyticsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityAnalyticsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		CD869F2F5BE944C6AF1DEE1B /* Assembly-CSharp__87.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__87.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__87.cpp"; sourceTree = SOURCE_ROOT; };
		CD891A8178C62435E30998FB /* System.Data__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__2.cpp; sourceTree = SOURCE_ROOT; };
		CDE326D9BC63A811E64F0402 /* System.Data__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__7.cpp; sourceTree = SOURCE_ROOT; };
		CDF3C3FBEC76E7F554BCAC7F /* UniTask.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UniTask.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UniTask.cpp; sourceTree = SOURCE_ROOT; };
		CE1CD281A8020E57A0495C3E /* mscorlib__18.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__18.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__18.cpp; sourceTree = SOURCE_ROOT; };
		CEEF9247F012D6069E397F57 /* Il2CppUnresolvedIndirectCallStubs.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppUnresolvedIndirectCallStubs.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp; sourceTree = SOURCE_ROOT; };
		CF08E8E4A81091C5FDB9F66D /* GenericMethods__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__1.cpp; sourceTree = SOURCE_ROOT; };
		CF27740112D78F14DED48EF7 /* System.Data__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__6.cpp; sourceTree = SOURCE_ROOT; };
		CF2D9CD79707BC66F0D724D2 /* UnityEngine.TextCoreTextEngineModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TextCoreTextEngineModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		D01680A0991BA8C95A4C44DE /* UnityEngine.SpriteMaskModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SpriteMaskModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteMaskModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		D0377ED2DD21A2FE426123F1 /* Assembly-CSharp__40.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__40.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__40.cpp"; sourceTree = SOURCE_ROOT; };
		D0609838D7B79343BB1B66CD /* mscorlib__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__12.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__12.cpp; sourceTree = SOURCE_ROOT; };
		D081C282527F806F47EB607B /* UnityEngine.DSPGraphModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.DSPGraphModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.DSPGraphModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		D1002655EF7EC77F763671BA /* System.Xml.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml.cpp; sourceTree = SOURCE_ROOT; };
		D11C507E6B790FF49D17D605 /* IngameDebugConsole.Runtime_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = IngameDebugConsole.Runtime_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/IngameDebugConsole.Runtime_CodeGen.c; sourceTree = SOURCE_ROOT; };
		D347FD89B300105FA57ABF90 /* UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		D36D733DA6A3052F599DC7CE /* mscorlib__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__5.cpp; sourceTree = SOURCE_ROOT; };
		D3BB5C24E04FA0D14EB9ECC3 /* UnityEngine.TLSModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TLSModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TLSModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		D3F41D4408A318CCAE49693A /* Il2CppGenericMethodDefinitions.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericMethodDefinitions.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodDefinitions.c; sourceTree = SOURCE_ROOT; };
		D4A00AF625F26E9AFA7D5EAA /* UnityEngine.AccessibilityModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AccessibilityModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AccessibilityModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		D556450C3DBEECE838996A3D /* LaunchScreen-iPad.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "LaunchScreen-iPad.png"; sourceTree = SOURCE_ROOT; };
		D5FA3667394130C0BCCF8B0E /* ApplePrivacyManifestsMerge.txt */ = {isa = PBXFileReference; lastKnownFileType = text; name = ApplePrivacyManifestsMerge.txt; path = UnityFramework/ApplePrivacyManifestsMerge.txt; sourceTree = SOURCE_ROOT; };
		D61973816F5E9F99E95C8C57 /* UnityEngine.IMGUIModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.IMGUIModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule.cpp; sourceTree = SOURCE_ROOT; };
		D638A35BC7B99FB83B2BC20E /* Mono.Data.Sqlite.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Mono.Data.Sqlite.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Data.Sqlite.cpp; sourceTree = SOURCE_ROOT; };
		D71208925BF18B7D39D077BE /* Unity.Collections.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Collections.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Collections.cpp; sourceTree = SOURCE_ROOT; };
		D740AE7756D71AACB0CCF7C7 /* Generics__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__8.cpp; sourceTree = SOURCE_ROOT; };
		D7503FB91502F4DD896CF714 /* UnityEngine.UIElementsModule__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__10.cpp; sourceTree = SOURCE_ROOT; };
		D82DCFBB0E8000A5005D6AD8 /* main.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = main.mm; path = Classes/main.mm; sourceTree = SOURCE_ROOT; };
		D8A1C72A0E8063A1000160D3 /* libiPhone-lib.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = "libiPhone-lib.a"; path = "Libraries/libiPhone-lib.a"; sourceTree = SOURCE_ROOT; };
		D917D7ECFD92AE0B95F7723A /* Unity.VisualScripting.Core_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.VisualScripting.Core_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.VisualScripting.Core_CodeGen.c; sourceTree = SOURCE_ROOT; };
		D9AC6836413CDFEB9D3D4DCA /* UnityEngine.UnityAnalyticsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityAnalyticsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule.cpp; sourceTree = SOURCE_ROOT; };
		D9BDB91C07511853A4B4A1F9 /* System.Numerics_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Numerics_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Numerics_Debugger.c; sourceTree = SOURCE_ROOT; };
		DA1731C322C733D4F9215478 /* UnityEngine.AnimationModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AnimationModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule.cpp; sourceTree = SOURCE_ROOT; };
		DAB61E49F0B27D71CDA3B02F /* lib_burst_generated.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = lib_burst_generated.a; path = Libraries/lib_burst_generated.a; sourceTree = SOURCE_ROOT; };
		DB88045514EBA3611759BAB0 /* Assembly-CSharp__39.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__39.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__39.cpp"; sourceTree = SOURCE_ROOT; };
		DB998B9FC449DC335718A76C /* UnityEngine.SubsystemsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SubsystemsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubsystemsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		DBF5A1414DA20E96D008D306 /* Unity.Burst.Unsafe_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Burst.Unsafe_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Burst.Unsafe_CodeGen.c; sourceTree = SOURCE_ROOT; };
		DC49E0D6482D5C0DB373D3F3 /* UnityEngine.CoreModule__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__5.cpp; sourceTree = SOURCE_ROOT; };
		DC641E85B09D087E3E294418 /* GenericMethods.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods.cpp; sourceTree = SOURCE_ROOT; };
		DC95A37E6F6893127CA8DA90 /* Assembly-CSharp__82.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__82.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__82.cpp"; sourceTree = SOURCE_ROOT; };
		DCC618ACC1C885764FCFF788 /* mscorlib__19.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__19.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__19.cpp; sourceTree = SOURCE_ROOT; };
		DCFF8C91896C4981029286E5 /* System.Core_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Core_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Core_CodeGen.c; sourceTree = SOURCE_ROOT; };
		DD952DDCAB0485953E3CE902 /* Il2CppCCFieldValuesTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCFieldValuesTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCFieldValuesTable.cpp; sourceTree = SOURCE_ROOT; };
		DDB2F8BB5DC4E5D804C3C134 /* Il2CppCCalculateFieldValues1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateFieldValues1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues1.cpp; sourceTree = SOURCE_ROOT; };
		DDEBC28914A53BD4F7CF1728 /* System.Runtime.Serialization_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Runtime.Serialization_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization_Debugger.c; sourceTree = SOURCE_ROOT; };
		DDFFC14FC5F2C34CB902E6FF /* UnityEngine.VideoModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.VideoModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VideoModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		DE35E560F61A1FC277AE3BDA /* Assembly-CSharp__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__12.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__12.cpp"; sourceTree = SOURCE_ROOT; };
		DE527102E9E1939013650259 /* Assembly-CSharp__53.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__53.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__53.cpp"; sourceTree = SOURCE_ROOT; };
		DE830B206631911313BD3EE8 /* UnityEngine.SubstanceModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.SubstanceModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubstanceModule.cpp; sourceTree = SOURCE_ROOT; };
		DE85ECC8173A198BB20AF9F1 /* Assembly-CSharp-firstpass.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp-firstpass.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass.cpp"; sourceTree = SOURCE_ROOT; };
		E0263673B7642BE0F04DE813 /* UnityEngine.UI.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UI.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI.cpp; sourceTree = SOURCE_ROOT; };
		E045302D92F3D175A9719406 /* UnityEngine.TextCoreTextEngineModule__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreTextEngineModule__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__4.cpp; sourceTree = SOURCE_ROOT; };
		E0994B957EB074EB69BD9ABA /* Assembly-CSharp__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__6.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__6.cpp"; sourceTree = SOURCE_ROOT; };
		E107302C2E0D6B6F367EFBB2 /* UnityEngine.CrashReportingModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.CrashReportingModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CrashReportingModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		E2566265D327EED99C2FE1A1 /* UniTask.Linq_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UniTask.Linq_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UniTask.Linq_Debugger.c; sourceTree = SOURCE_ROOT; };
		E28E0AD14FECA0BE6084E4CE /* UnityEngine.TilemapModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TilemapModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule.cpp; sourceTree = SOURCE_ROOT; };
		E2EF56E2797F5DBE1AFB6F3C /* UnityEngine.UnityWebRequestTextureModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestTextureModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		E44256EF68D3620E3BA7B3C5 /* UnityEngine.StreamingModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.StreamingModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.StreamingModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		E4C944039088C7D345B2460D /* UnityEngine.LocalizationModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.LocalizationModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.LocalizationModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		E4DF07DDFDEDBDD8E0275789 /* Assembly-CSharp__96.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__96.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__96.cpp"; sourceTree = SOURCE_ROOT; };
		E518660204D9E27F74306195 /* UnityEngine.TextCoreFontEngineModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TextCoreFontEngineModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		E63E22A2D76ACB6254C3A017 /* mscorlib__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__13.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__13.cpp; sourceTree = SOURCE_ROOT; };
		E66A9031D16170B89FB66341 /* UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.cpp; sourceTree = SOURCE_ROOT; };
		E6A068D0AE38570B96122437 /* Assembly-CSharp__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__10.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__10.cpp"; sourceTree = SOURCE_ROOT; };
		E79F4C19A708A497C9443FB8 /* Assembly-CSharp__20.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__20.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__20.cpp"; sourceTree = SOURCE_ROOT; };
		E8A3C67A913D9E240397EFB1 /* UnityEngine.UIElementsModule__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__4.cpp; sourceTree = SOURCE_ROOT; };
		E8C92A8C826C15791DB4D76F /* UnityEngine.TextCoreTextEngineModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TextCoreTextEngineModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		E8E6BB924BD5BB712770450E /* System.Data__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__1.cpp; sourceTree = SOURCE_ROOT; };
		E9A7659A3EF108621EF5E740 /* mscorlib__23.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__23.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__23.cpp; sourceTree = SOURCE_ROOT; };
		E9C72CBC632293D0FE2E3D56 /* System.Drawing_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Drawing_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Drawing_CodeGen.c; sourceTree = SOURCE_ROOT; };
		EA841D12341C9363E02EE00F /* Unity.TextMeshPro__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__4.cpp; sourceTree = SOURCE_ROOT; };
		EAFCEA713D2A37FF5D81C6CB /* mscorlib__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__9.cpp; sourceTree = SOURCE_ROOT; };
		EB4E970CF51960FC6EF12D91 /* UnityEngine.GIModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.GIModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GIModule.cpp; sourceTree = SOURCE_ROOT; };
		ECAF71504226E32235BC8E2F /* UnityEngine.CoreModule__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__3.cpp; sourceTree = SOURCE_ROOT; };
		EE54DC7E56FA015DDC9659BD /* libil2cpp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libil2cpp.a; path = Libraries/libil2cpp.a; sourceTree = SOURCE_ROOT; };
		F02A78402345158DB659E78E /* UnityEngine.AudioModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AudioModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F09919364E13EC87792FCBE3 /* Unity.TextMeshPro__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__5.cpp; sourceTree = SOURCE_ROOT; };
		F0EA4F626013C9E032D8A2AD /* UnityEngine.UnityCurlModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityCurlModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityCurlModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F103AF1CC7D2D96E788D51D7 /* Assembly-CSharp__30.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__30.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__30.cpp"; sourceTree = SOURCE_ROOT; };
		F1F28F611D1E7EE7CCC25F71 /* mscorlib__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__1.cpp; sourceTree = SOURCE_ROOT; };
		F24B06F17D255CA359EB4840 /* UnityEngine.SpriteShapeModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SpriteShapeModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F2E4BD281D8D2761198E3AA0 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = PrivacyInfo.xcprivacy; name = PrivacyInfo.xcprivacy; path = UnityFramework/PrivacyInfo.xcprivacy; sourceTree = SOURCE_ROOT; };
		F2E5D9A741FB7ADF7324BC5B /* System.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.cpp; sourceTree = SOURCE_ROOT; };
		F31761DC8785C18DED8DC01A /* UnityEngine.TextRenderingModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TextRenderingModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		F32E24B5FD8122F20E7308A7 /* UnityEngine.VideoModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.VideoModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VideoModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F3865E57914B553931A5D241 /* UnityEngine.HotReloadModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.HotReloadModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.HotReloadModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F4CEF9BD1988923C37044087 /* UnityEngine.CrashReportingModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.CrashReportingModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CrashReportingModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F4F4286D3BE59E565564055C /* UnityEngine.AssetBundleModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AssetBundleModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule.cpp; sourceTree = SOURCE_ROOT; };
		F55249155698357E094F40BE /* UnityEngine.UIElementsModule__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__8.cpp; sourceTree = SOURCE_ROOT; };
		F5C0D9C036EFC8EF66FE06C4 /* mscorlib__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__15.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__15.cpp; sourceTree = SOURCE_ROOT; };
		F6361BB52195CE5D00F61766 /* UnitySharedDecls.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnitySharedDecls.h; sourceTree = "<group>"; };
		F6CDB022FB8DC8CFB499BE10 /* Generics__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__3.cpp; sourceTree = SOURCE_ROOT; };
		F7B9CB2246993CC9923F6456 /* Assembly-CSharp__33.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__33.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__33.cpp"; sourceTree = SOURCE_ROOT; };
		F7F2599B7A76BDAC54FDCB60 /* UnityEngine.AndroidJNIModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AndroidJNIModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AndroidJNIModule.cpp; sourceTree = SOURCE_ROOT; };
		F7FB70D9617296AB7653F474 /* UnityEngine.TerrainModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TerrainModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TerrainModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		F828452E792C2569D9C775E5 /* Assembly-CSharp__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__1.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__1.cpp"; sourceTree = SOURCE_ROOT; };
		F8ECC93D90D85D74F4348AD7 /* System.Runtime.CompilerServices.Unsafe_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Runtime.CompilerServices.Unsafe_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Runtime.CompilerServices.Unsafe_Debugger.c; sourceTree = SOURCE_ROOT; };
		F939D2575DAA3E85196F5BB7 /* UnityEngine.Physics2DModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.Physics2DModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		F93B6EC9ADF6740B487EB7B0 /* UnityEngine.DirectorModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.DirectorModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.DirectorModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		F95D837157339CB183E2FC76 /* System.Data__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__8.cpp; sourceTree = SOURCE_ROOT; };
		F98883DE16B476EDC83A25E2 /* UnityEngine.ParticleSystemModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ParticleSystemModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F9E100714A248BEC8B9C285D /* UnityEngine.InputLegacyModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.InputLegacyModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		F9F1230E7B00F6B2FFC7A8EB /* UnityEngine.Physics2DModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.Physics2DModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule.cpp; sourceTree = SOURCE_ROOT; };
		FA0D72F8EDBD0798BCFC9916 /* UnityEngine.ParticleSystemModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.ParticleSystemModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule.cpp; sourceTree = SOURCE_ROOT; };
		FAA2E88DA984D6D226870B21 /* System.Data__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__4.cpp; sourceTree = SOURCE_ROOT; };
		FAB4D031E79E43D0CC876610 /* Il2CppGenericMethodPointerTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericMethodPointerTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodPointerTable.c; sourceTree = SOURCE_ROOT; };
		FAFE6F25F97E0825A99B77C2 /* Generics__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__4.cpp; sourceTree = SOURCE_ROOT; };
		FC0B20A11B7A4F0B00FDFC55 /* OnDemandResources.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = OnDemandResources.mm; sourceTree = "<group>"; };
		FC3D7EBE16D2621600D1BD0D /* CrashReporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CrashReporter.h; sourceTree = "<group>"; };
		FC3E8E87EC9D6CB4B64081C3 /* UnityEngine.AccessibilityModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AccessibilityModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AccessibilityModule.cpp; sourceTree = SOURCE_ROOT; };
		FC85CCB916C3ED8000BAF7C7 /* CrashReporter.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CrashReporter.mm; sourceTree = "<group>"; };
		FC85CCBA16C3ED8000BAF7C7 /* PLCrashReporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLCrashReporter.h; sourceTree = "<group>"; };
		FCA5FE8B92B32260C9A32E3F /* UnityEngine.UnityWebRequestAudioModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityWebRequestAudioModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAudioModule.cpp; sourceTree = SOURCE_ROOT; };
		FD55A1779C68E12BA6BB99A2 /* Assembly-CSharp__105.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__105.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__105.cpp"; sourceTree = SOURCE_ROOT; };
		FE9D71900DB30014C49FC674 /* UnityEngine.IMGUIModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.IMGUIModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		FF066C4423BD31959A6000C2 /* UnityEngine.UnityCurlModule_Debugger.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityCurlModule_Debugger.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityCurlModule_Debugger.c; sourceTree = SOURCE_ROOT; };
		FF80ED27C0BED9BC1BA9070D /* Unity.TextMeshPro.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro.cpp; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1D60588F0D05DD3D006BFB54 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5623C57017FDCB0800090B9E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5623C57717FDCB0800090B9E /* UIKit.framework in Frameworks */,
				5623C57617FDCB0800090B9E /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7F4E059D2717216D00A2CBE4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D25AB99213FB47800354C27 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00000000008063A1000160D3 /* libiPhone-lib.a in Frameworks */,
				8A3831B5246956AA00CD74FD /* Metal.framework in Frameworks */,
				9D690CCF21BFD341005026B1 /* Security.framework in Frameworks */,
				9D690CD021BFD349005026B1 /* MediaToolbox.framework in Frameworks */,
				9D690CD221BFD36C005026B1 /* CoreText.framework in Frameworks */,
				9D690CD321BFD376005026B1 /* AudioToolbox.framework in Frameworks */,
				9D690CD421BFD37E005026B1 /* AVFoundation.framework in Frameworks */,
				8A20382D213D4B3C005E6C56 /* AVKit.framework in Frameworks */,
				9D690CD521BFD388005026B1 /* CFNetwork.framework in Frameworks */,
				9D690CD621BFD391005026B1 /* CoreGraphics.framework in Frameworks */,
				9D690CD721BFD39D005026B1 /* CoreMedia.framework in Frameworks */,
				9D690CD821BFD3A5005026B1 /* CoreMotion.framework in Frameworks */,
				9D690CD921BFD3AC005026B1 /* CoreVideo.framework in Frameworks */,
				9D690CDA21BFD3B5005026B1 /* Foundation.framework in Frameworks */,
				9D690CDB21BFD3BF005026B1 /* OpenAL.framework in Frameworks */,
				9D690CDD21BFD3D0005026B1 /* QuartzCore.framework in Frameworks */,
				9D690CDE21BFD3D9005026B1 /* SystemConfiguration.framework in Frameworks */,
				9D690CDF21BFD3E3005026B1 /* UIKit.framework in Frameworks */,
				7F4E05AB2717219200A2CBE4 /* libGameAssembly.a in Frameworks */,
				9D0A618B21BFE7F30094DC33 /* libiconv.2.tbd in Frameworks */,
				01F967D36F74C7C4205C1BD6 /* lib_burst_generated.a in Frameworks */,
				1D45A5CCCCC4E76191AAF1D0 /* libil2cpp.a in Frameworks */,
				63ACF7178776C648B96974EB /* baselib.a in Frameworks */,
				31211B4C6E4D3A55C19BC2A2 /* RealityKit.framework in Frameworks */,
				574A8D1BFDF4103B13A6538A /* GameController.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		04B0C11757E9C7B105072285 /* Source */ = {
			isa = PBXGroup;
			children = (
				45C76ABEC14C6E10C9CFC809 /* il2cppOutput */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		08E06EF576D79C4643098BCC /* Il2CppOutputProject */ = {
			isa = PBXGroup;
			children = (
				04B0C11757E9C7B105072285 /* Source */,
			);
			path = Il2CppOutputProject;
			sourceTree = "<group>";
		};
		19C28FACFE9D520D11CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				1D6058910D05DD3D006BFB54 /* Unity-Target-New.app */,
				5623C57317FDCB0800090B9E /* Unity-iPhone Tests.xctest */,
				9D25AB9D213FB47800354C27 /* UnityFramework.framework */,
				7F4E05A02717216D00A2CBE4 /* libGameAssembly.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		29B97314FDCFA39411CA2CEA /* CustomTemplate */ = {
			isa = PBXGroup;
			children = (
				D82DCFB50E8000A5005D6AD8 /* Classes */,
				29B97323FDCFA39411CA2CEA /* Frameworks */,
				08E06EF576D79C4643098BCC /* Il2CppOutputProject */,
				D8A1C7220E80637F000160D3 /* Libraries */,
				9DA3B0422174CB96001678C7 /* MainApp */,
				19C28FACFE9D520D11CA2CBB /* Products */,
				5623C57817FDCB0800090B9E /* Unity-iPhone Tests */,
				9D25AB9E213FB47800354C27 /* UnityFramework */,
				AA31BF961B55660D0013FB1B /* Data */,
				56C56C9717D6015100616839 /* Images.xcassets */,
				8D1107310486CEB800E47090 /* Info.plist */,
				D556450C3DBEECE838996A3D /* LaunchScreen-iPad.png */,
				A6444ABDD2799CC877EBAF7E /* LaunchScreen-iPad.storyboard */,
				5A3A1B4C3FB1FC90023B942E /* LaunchScreen-iPhone.storyboard */,
				1E24B93E9D27D99850693BAF /* LaunchScreen-iPhoneLandscape.png */,
				681AA63C19788ACA381FB8C1 /* LaunchScreen-iPhonePortrait.png */,
			);
			name = CustomTemplate;
			sourceTree = "<group>";
		};
		29B97323FDCFA39411CA2CEA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				7F36C11013C5C673007FBDD9 /* AVFoundation.framework */,
				8A20382C213D4B3C005E6C56 /* AVKit.framework */,
				8358D1B70ED1CC3700E3A684 /* AudioToolbox.framework */,
				56FD43950ED4745200FE3770 /* CFNetwork.framework */,
				56B7959A1442E0F20026B3DD /* CoreGraphics.framework */,
				5692F3DC0FA9D8E500EBA2F1 /* CoreLocation.framework */,
				7F36C10E13C5C673007FBDD9 /* CoreMedia.framework */,
				56B795C11442E1100026B3DD /* CoreMotion.framework */,
				AA5D99861AFAD3C800B27605 /* CoreText.framework */,
				7F36C10F13C5C673007FBDD9 /* CoreVideo.framework */,
				1D30AB110D05D00D00671497 /* Foundation.framework */,
				21ECD27EDF8EB21168769097 /* GameController.framework */,
				960391211D6CE46E003BF157 /* MediaToolbox.framework */,
				8A3831B4246956AA00CD74FD /* Metal.framework */,
				83B2574B0E63022200468741 /* OpenAL.framework */,
				83B2570A0E62FF8A00468741 /* QuartzCore.framework */,
				2695EADE8122A2B6567B40E7 /* RealityKit.framework */,
				5BAD78601F2B5A59006103DE /* Security.framework */,
				56BCBA380FCF049A0030C3B2 /* SystemConfiguration.framework */,
				830B5C100E5ED4C100C7819F /* UIKit.framework */,
				9D0A618A21BFE7F30094DC33 /* libiconv.2.tbd */,
			);
			path = Frameworks;
			sourceTree = "<group>";
		};
		370000EAB67CF9F0667B6C5B /* Plugins */ = {
			isa = PBXGroup;
			children = (
				89D1F5FD607898AE36842EED /* iOS */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
		45C76ABEC14C6E10C9CFC809 /* il2cppOutput */ = {
			isa = PBXGroup;
			children = (
				DE85ECC8173A198BB20AF9F1 /* Assembly-CSharp-firstpass.cpp */,
				732F023F742A46D0095260A9 /* Assembly-CSharp-firstpass_CodeGen.c */,
				93022D3C91405460FF1AFE7E /* Assembly-CSharp-firstpass_Debugger.c */,
				5B53B64A88569D3F8EB8A9F1 /* Assembly-CSharp-firstpass__1.cpp */,
				C40C9D5FB5363330524518CB /* Assembly-CSharp.cpp */,
				359EA6DE6F0ADB889FC61B63 /* Assembly-CSharp_CodeGen.c */,
				1F13B5FB9DF58B79696EBF36 /* Assembly-CSharp_Debugger.c */,
				F828452E792C2569D9C775E5 /* Assembly-CSharp__1.cpp */,
				E6A068D0AE38570B96122437 /* Assembly-CSharp__10.cpp */,
				0F892C1159E410FBFC2AC766 /* Assembly-CSharp__100.cpp */,
				87E07729B348731BD1074CDF /* Assembly-CSharp__101.cpp */,
				435AD329270E76D06EF5AEBF /* Assembly-CSharp__102.cpp */,
				6A8E9CCC95DBFF719234E05A /* Assembly-CSharp__103.cpp */,
				71064449DE869E361D0759B6 /* Assembly-CSharp__104.cpp */,
				FD55A1779C68E12BA6BB99A2 /* Assembly-CSharp__105.cpp */,
				AD3C154F80F47532952859E9 /* Assembly-CSharp__106.cpp */,
				86D11D47EA7629E34BD50F12 /* Assembly-CSharp__107.cpp */,
				21D6D96509F70B8503A70C75 /* Assembly-CSharp__108.cpp */,
				9C5F4A52C023FBEBCB9B9C51 /* Assembly-CSharp__11.cpp */,
				DE35E560F61A1FC277AE3BDA /* Assembly-CSharp__12.cpp */,
				A882C70BF6B0539484D63840 /* Assembly-CSharp__13.cpp */,
				A239769F708DCF300ED81D67 /* Assembly-CSharp__14.cpp */,
				03A87A26A63177A70A5AC67E /* Assembly-CSharp__15.cpp */,
				1492999617EA37FB6D4727C7 /* Assembly-CSharp__16.cpp */,
				4114445645D738D6E9CC35B8 /* Assembly-CSharp__17.cpp */,
				1F951416510E376441DD5ABD /* Assembly-CSharp__18.cpp */,
				17A88A24B72C4E31367418A7 /* Assembly-CSharp__19.cpp */,
				0625BA89AA0F71807DF4285A /* Assembly-CSharp__2.cpp */,
				E79F4C19A708A497C9443FB8 /* Assembly-CSharp__20.cpp */,
				444A6E27F397365C367A0E17 /* Assembly-CSharp__21.cpp */,
				C1D2C2900B1B1033C50FDC84 /* Assembly-CSharp__22.cpp */,
				223EBC6A186ED13AA8BAEDBA /* Assembly-CSharp__23.cpp */,
				0A99F41A6E79CC9C6BE2D738 /* Assembly-CSharp__24.cpp */,
				3C54469E02AE026A630B4244 /* Assembly-CSharp__25.cpp */,
				39A4F548F86A869BD5CA86C0 /* Assembly-CSharp__26.cpp */,
				465EB73EB7CAF8A291ADD317 /* Assembly-CSharp__27.cpp */,
				93CB0776020C99B75942BABB /* Assembly-CSharp__28.cpp */,
				625C59B7F72E20CA1DAD37D1 /* Assembly-CSharp__29.cpp */,
				3BF0F548641EA67234EF4822 /* Assembly-CSharp__3.cpp */,
				F103AF1CC7D2D96E788D51D7 /* Assembly-CSharp__30.cpp */,
				91B3C31E164148A3C3C440BA /* Assembly-CSharp__31.cpp */,
				BE89081E35159488B1CFEDD6 /* Assembly-CSharp__32.cpp */,
				F7B9CB2246993CC9923F6456 /* Assembly-CSharp__33.cpp */,
				07CA36577698941D4EE4BC70 /* Assembly-CSharp__34.cpp */,
				B6B32C7D1EE6F0853DDDAA61 /* Assembly-CSharp__35.cpp */,
				3EAD2E0234EACBB68F7569A5 /* Assembly-CSharp__36.cpp */,
				5C3AA7AEC8BCC431E5FEEC04 /* Assembly-CSharp__37.cpp */,
				C86635207E96D385500A4A2C /* Assembly-CSharp__38.cpp */,
				DB88045514EBA3611759BAB0 /* Assembly-CSharp__39.cpp */,
				4F97D266790DB44CD01959EC /* Assembly-CSharp__4.cpp */,
				D0377ED2DD21A2FE426123F1 /* Assembly-CSharp__40.cpp */,
				C08898ABC22AC52604E841BD /* Assembly-CSharp__41.cpp */,
				08D9ECBDADDA4DD82C86134D /* Assembly-CSharp__42.cpp */,
				5D2CB0C1599CC73B675915AB /* Assembly-CSharp__43.cpp */,
				25EDC3486A64365C2398DAB5 /* Assembly-CSharp__44.cpp */,
				A849FEA549C242344B5B7C14 /* Assembly-CSharp__45.cpp */,
				5E93FF130E65841D72EE889D /* Assembly-CSharp__46.cpp */,
				188198725C11AA1A9BB3A8BF /* Assembly-CSharp__47.cpp */,
				4A8BF4F505104D1B8AE63E79 /* Assembly-CSharp__48.cpp */,
				43DCC1F9F099008314351765 /* Assembly-CSharp__49.cpp */,
				A34734E12320BFE93D77614B /* Assembly-CSharp__5.cpp */,
				780EF42D0F810F6942D61886 /* Assembly-CSharp__50.cpp */,
				6AF6E713D4C420DE4590CB06 /* Assembly-CSharp__51.cpp */,
				0261C17A7919F2AE5BFC8D0A /* Assembly-CSharp__52.cpp */,
				DE527102E9E1939013650259 /* Assembly-CSharp__53.cpp */,
				0DB6966C8FE1F310309492B8 /* Assembly-CSharp__54.cpp */,
				0843F8EB79BC66D9BA665116 /* Assembly-CSharp__55.cpp */,
				A0681946D39E04E92D886E8C /* Assembly-CSharp__56.cpp */,
				1887791D1EDB29E243E57E31 /* Assembly-CSharp__57.cpp */,
				234DA00B7297246CAFE09A46 /* Assembly-CSharp__58.cpp */,
				1C473E48207C8E4741CF401C /* Assembly-CSharp__59.cpp */,
				E0994B957EB074EB69BD9ABA /* Assembly-CSharp__6.cpp */,
				B322A23CD4D617B766B38780 /* Assembly-CSharp__60.cpp */,
				90F170FC52D84C4EA00E7116 /* Assembly-CSharp__61.cpp */,
				71A76431EB6A5AAC9E16CE06 /* Assembly-CSharp__62.cpp */,
				13D57270A9533024EAF53DA2 /* Assembly-CSharp__63.cpp */,
				83E06090B64757F3B9010574 /* Assembly-CSharp__64.cpp */,
				734DA6B71EC643623B6729C5 /* Assembly-CSharp__65.cpp */,
				469A72CDD1B2FFEAA2E37FAC /* Assembly-CSharp__66.cpp */,
				BB7DCF84385C419D6C313CDF /* Assembly-CSharp__67.cpp */,
				79B3CF3B88129FCFF7234C25 /* Assembly-CSharp__68.cpp */,
				3F6406DE79FB768A8C324E67 /* Assembly-CSharp__69.cpp */,
				504F10354AA6664ACC10AE55 /* Assembly-CSharp__7.cpp */,
				960816A18612315549B46E03 /* Assembly-CSharp__70.cpp */,
				6032091DC775FCFCB4E4D89D /* Assembly-CSharp__71.cpp */,
				5031982034D7F5B402FA49EA /* Assembly-CSharp__72.cpp */,
				6DF9A4610A43B72DCECA9C9D /* Assembly-CSharp__73.cpp */,
				8B85E323FB282C9808D66C9A /* Assembly-CSharp__74.cpp */,
				3066866A0209B133D099A786 /* Assembly-CSharp__75.cpp */,
				B9ABC75052207F54EEA14BA2 /* Assembly-CSharp__76.cpp */,
				3950BD72498ECBFF84CEBC47 /* Assembly-CSharp__77.cpp */,
				37D3FC745AFF2AAE4E3F0FC0 /* Assembly-CSharp__78.cpp */,
				3E7E5CC83B71F26761C384D4 /* Assembly-CSharp__79.cpp */,
				0B698999CFCA23787EA976B2 /* Assembly-CSharp__8.cpp */,
				007AE62FCE6018BA9A003B7F /* Assembly-CSharp__80.cpp */,
				33D9A9D1BA3BF2E1779DF0EA /* Assembly-CSharp__81.cpp */,
				DC95A37E6F6893127CA8DA90 /* Assembly-CSharp__82.cpp */,
				84AB4AD7C79452A60789EBBD /* Assembly-CSharp__83.cpp */,
				B1E720AFEEADE2716193EB8A /* Assembly-CSharp__84.cpp */,
				B4A359AB8EAFEB14CB1C2FD1 /* Assembly-CSharp__85.cpp */,
				27754211CC9E10094FDB5B65 /* Assembly-CSharp__86.cpp */,
				CD869F2F5BE944C6AF1DEE1B /* Assembly-CSharp__87.cpp */,
				5975A90725D533A536509F55 /* Assembly-CSharp__88.cpp */,
				C5E03A93B2A56B97660E2F86 /* Assembly-CSharp__89.cpp */,
				7FA39E9CC391DDDACEDDE1E9 /* Assembly-CSharp__9.cpp */,
				73FD00F17EEAD9A00F37E623 /* Assembly-CSharp__90.cpp */,
				501D596216B1F282D990909B /* Assembly-CSharp__91.cpp */,
				4E74E57C06D4EB7EF04FD8C6 /* Assembly-CSharp__92.cpp */,
				B977519F68E3B7F6C9CB761F /* Assembly-CSharp__93.cpp */,
				6F5AA23CDF74E46493B84C24 /* Assembly-CSharp__94.cpp */,
				CABC68E3E532B24695A5FED6 /* Assembly-CSharp__95.cpp */,
				E4DF07DDFDEDBDD8E0275789 /* Assembly-CSharp__96.cpp */,
				B5089176EA3ED5DF6BCDDF10 /* Assembly-CSharp__97.cpp */,
				223F87790AF1C48E224ABBC8 /* Assembly-CSharp__98.cpp */,
				9379D2F4AB004FA422D73616 /* Assembly-CSharp__99.cpp */,
				DC641E85B09D087E3E294418 /* GenericMethods.cpp */,
				CF08E8E4A81091C5FDB9F66D /* GenericMethods__1.cpp */,
				9497B750CCFB7578BDEF70E5 /* GenericMethods__2.cpp */,
				A504EDDC3C17119152F24F7F /* GenericMethods__3.cpp */,
				6268E9B44CD8602C2A9D5536 /* GenericMethods__4.cpp */,
				439D39F1FC43F7B0A6C04FFF /* Generics.cpp */,
				8E6728ADD56BA33B896627DA /* Generics__1.cpp */,
				11856605578FD8270FB7624A /* Generics__10.cpp */,
				317B44F3876EC140C859BDEB /* Generics__11.cpp */,
				BF026C43C97DF354A316F9F4 /* Generics__12.cpp */,
				848BF6A7383F7523CFD9F1F2 /* Generics__13.cpp */,
				C74C563E02CE38C5DC9590E7 /* Generics__2.cpp */,
				F6CDB022FB8DC8CFB499BE10 /* Generics__3.cpp */,
				FAFE6F25F97E0825A99B77C2 /* Generics__4.cpp */,
				525CDBA21FAADF221B8950A5 /* Generics__5.cpp */,
				8DF7D168881C3EF89850AD0A /* Generics__6.cpp */,
				C865CD21D06B3F35209C5FA5 /* Generics__7.cpp */,
				D740AE7756D71AACB0CCF7C7 /* Generics__8.cpp */,
				8F13D042492DBEB7CF96DC23 /* Generics__9.cpp */,
				DD952DDCAB0485953E3CE902 /* Il2CppCCFieldValuesTable.cpp */,
				CA2FE0E937B9F3EBF23597D5 /* Il2CppCCTypeValuesTable.cpp */,
				B662E7588FD9F79EDCF63D17 /* Il2CppCCalculateFieldValues.cpp */,
				DDB2F8BB5DC4E5D804C3C134 /* Il2CppCCalculateFieldValues1.cpp */,
				A736D24CED71BDA3F309CC8E /* Il2CppCCalculateFieldValues2.cpp */,
				97EFDE40B546C8DD238C8231 /* Il2CppCCalculateFieldValues3.cpp */,
				50FA716FD26EC3318C60967C /* Il2CppCCalculateFieldValues4.cpp */,
				1EF6732A52CD49B602825AEB /* Il2CppCCalculateTypeValues.cpp */,
				16A72414295A2B23C5F331A7 /* Il2CppCCalculateTypeValues1.cpp */,
				93E6825D15CDF948BB0DCB44 /* Il2CppCCalculateTypeValues2.cpp */,
				67B80E918530D8754535F3F9 /* Il2CppCodeRegistration.cpp */,
				AE25855F1DB729A61745C8CB /* Il2CppGenericAdjustorThunkTable.c */,
				733D2142097013827A6FD7A8 /* Il2CppGenericClassTable.c */,
				1DD696E89C6A943954768877 /* Il2CppGenericInstDefinitions.c */,
				D3F41D4408A318CCAE49693A /* Il2CppGenericMethodDefinitions.c */,
				FAB4D031E79E43D0CC876610 /* Il2CppGenericMethodPointerTable.c */,
				7C2E380E3D2F92B20313DD1D /* Il2CppGenericMethodTable.c */,
				62B31F6AE6313940631A3B13 /* Il2CppInteropDataTable.cpp */,
				0F795415320EF5804B496AD2 /* Il2CppInvokerTable.cpp */,
				5EA521C38706003E7FF399CD /* Il2CppMetadataRegistration.c */,
				0E3F3917340B2949C8B8E209 /* Il2CppMetadataUsage.c */,
				61644B28313FCE72AA0F7731 /* Il2CppReversePInvokeWrapperTable.cpp */,
				238B504DA5717798C8149303 /* Il2CppRgctxTable.c */,
				996B8B35A5892AE2820D8033 /* Il2CppTypeDefinitions.c */,
				CEEF9247F012D6069E397F57 /* Il2CppUnresolvedIndirectCallStubs.cpp */,
				C922426E10576375DD5017D3 /* IngameDebugConsole.Runtime.cpp */,
				D11C507E6B790FF49D17D605 /* IngameDebugConsole.Runtime_CodeGen.c */,
				1E2F8862C3F9970065521531 /* IngameDebugConsole.Runtime_Debugger.c */,
				265D6DDBC6C0E98AE8B87F62 /* IngameDebugConsole.Runtime__1.cpp */,
				47FB88DA2B3F39732E795E6F /* Microsoft.Bcl.TimeProvider.cpp */,
				4CC55811FE82BEE3AC9B535A /* Microsoft.Bcl.TimeProvider_CodeGen.c */,
				A22FCCAA9050EDFB439D8723 /* Microsoft.Bcl.TimeProvider_Debugger.c */,
				D638A35BC7B99FB83B2BC20E /* Mono.Data.Sqlite.cpp */,
				9E71E7DCDFD803C6794F987C /* Mono.Data.Sqlite_CodeGen.c */,
				6F192CC9204E890AC63A7D6E /* Mono.Data.Sqlite_Debugger.c */,
				3D4E0D172302BFF9751A2E53 /* Mono.Data.Sqlite__1.cpp */,
				25711C69564281E1F27185F1 /* Mono.Security.cpp */,
				AAC85F52F8C010404109B18C /* Mono.Security_CodeGen.c */,
				BBA209A675B16140CD14E2A2 /* Mono.Security_Debugger.c */,
				54EE3B312E220CA8996C49C7 /* Mono.Security__1.cpp */,
				8EE47B422BDF2BE7441AA757 /* Newtonsoft.Json.cpp */,
				C531584915BF8819C6496BE0 /* Newtonsoft.Json_CodeGen.c */,
				582018FF346E4A041A401DB2 /* Newtonsoft.Json_Debugger.c */,
				B315EBF0BC65192DC23290CD /* Newtonsoft.Json__1.cpp */,
				3E6AB690991E5D5C419B99F5 /* Newtonsoft.Json__2.cpp */,
				C0D23F3F8BED210275372D69 /* Newtonsoft.Json__3.cpp */,
				414991A97FF59CD6675FD9EB /* Newtonsoft.Json__4.cpp */,
				C2BA20DA462F4D826FE30857 /* R3.Unity.cpp */,
				05C25260E27291917CF35635 /* R3.Unity_CodeGen.c */,
				1432D64D8E4E62A6A841AFB0 /* R3.Unity_Debugger.c */,
				08B9B183EA7AC0D27F8A1A86 /* R3.cpp */,
				65D16328C6C383697132B27B /* R3_CodeGen.c */,
				1791BB02AC6CAA800735D036 /* R3_Debugger.c */,
				2D55E3A876DECECA9E2A124C /* System.Configuration.cpp */,
				C862D3A89A9BA3E4ED7205BD /* System.Configuration_CodeGen.c */,
				37F6C712B26110AD2FC79825 /* System.Configuration_Debugger.c */,
				28C3B8EF48C0EB6F619F326F /* System.Core.cpp */,
				DCFF8C91896C4981029286E5 /* System.Core_CodeGen.c */,
				0B1A110055A045B2CF537BDF /* System.Core_Debugger.c */,
				58C462906CFE1012884EAB16 /* System.Core__1.cpp */,
				86D61A6AC37C64EC49B5261F /* System.Core__2.cpp */,
				4CF8EE25016BB90ABFD83417 /* System.Core__3.cpp */,
				C6B21A7CC65E934695520510 /* System.Core__4.cpp */,
				4BC1C33C24833176E207546D /* System.Data.cpp */,
				817B6BBCBF9F078D00727259 /* System.Data_CodeGen.c */,
				7C135DFA4622154078EFA2E6 /* System.Data_Debugger.c */,
				E8E6BB924BD5BB712770450E /* System.Data__1.cpp */,
				7143E69478DF47AA204760BF /* System.Data__10.cpp */,
				CD891A8178C62435E30998FB /* System.Data__2.cpp */,
				2274C9C05B1B4B06D30C0211 /* System.Data__3.cpp */,
				FAA2E88DA984D6D226870B21 /* System.Data__4.cpp */,
				C0FC069B611EACD4E344ED53 /* System.Data__5.cpp */,
				CF27740112D78F14DED48EF7 /* System.Data__6.cpp */,
				CDE326D9BC63A811E64F0402 /* System.Data__7.cpp */,
				F95D837157339CB183E2FC76 /* System.Data__8.cpp */,
				8992CFD7E59D57B6F6AA07C2 /* System.Data__9.cpp */,
				A1B3624D0AECCFE9A3417712 /* System.Drawing.cpp */,
				E9C72CBC632293D0FE2E3D56 /* System.Drawing_CodeGen.c */,
				5039AE8A9B052FA9A784E00E /* System.Drawing_Debugger.c */,
				2E8E2D02765123AF23FC2A10 /* System.Numerics.cpp */,
				2E38E8560CE5D41091E04C4B /* System.Numerics_CodeGen.c */,
				D9BDB91C07511853A4B4A1F9 /* System.Numerics_Debugger.c */,
				835C8F33E51F11E7A01A5885 /* System.Runtime.CompilerServices.Unsafe.cpp */,
				A9CF3A2F17D70FECA132802A /* System.Runtime.CompilerServices.Unsafe_CodeGen.c */,
				F8ECC93D90D85D74F4348AD7 /* System.Runtime.CompilerServices.Unsafe_Debugger.c */,
				1B92F201E0C11B4944DF0F0B /* System.Runtime.Serialization.cpp */,
				5E6EECA05C59F494EE5D3F45 /* System.Runtime.Serialization_CodeGen.c */,
				DDEBC28914A53BD4F7CF1728 /* System.Runtime.Serialization_Debugger.c */,
				32CA8D88C5E1E8B5DDD1684B /* System.Transactions.cpp */,
				A45FF8EA2070EB8106DAC2EF /* System.Transactions_CodeGen.c */,
				1D0A122864C3A431BADD4B5D /* System.Transactions_Debugger.c */,
				0464E0DCE4A0FC27BFF43BB4 /* System.Xml.Linq.cpp */,
				71520C37DB84A0B4A8E0F46F /* System.Xml.Linq_CodeGen.c */,
				1C28A4DFE57E236FAE30F7B7 /* System.Xml.Linq_Debugger.c */,
				D1002655EF7EC77F763671BA /* System.Xml.cpp */,
				16E144C6C0B3E8367F911BA8 /* System.Xml_CodeGen.c */,
				1C09897E685B7A7D91056118 /* System.Xml_Debugger.c */,
				6F9722D0330536FF815E94C5 /* System.Xml__1.cpp */,
				29218B0B79CD49C8515B939E /* System.Xml__10.cpp */,
				7931F0EB3287754DA068276D /* System.Xml__11.cpp */,
				750BF4D08D860C9A70ACE432 /* System.Xml__12.cpp */,
				5CE747857641C3A4C4FD32A4 /* System.Xml__13.cpp */,
				BE33F4A8ED55CC34EB5BDF80 /* System.Xml__14.cpp */,
				4117F67ACFF8CBA17CC65251 /* System.Xml__15.cpp */,
				84C6A84D75C32015E4995C33 /* System.Xml__16.cpp */,
				7EEF5FB367689FB1ECEBB6A7 /* System.Xml__2.cpp */,
				41772366CC01AABF3A141A21 /* System.Xml__3.cpp */,
				043632D66EB0B7E5F3272285 /* System.Xml__4.cpp */,
				1C3F27E9009F948E3EC03458 /* System.Xml__5.cpp */,
				08D218E34CD3080971C43706 /* System.Xml__6.cpp */,
				2D5AF8F8E52C508883AD72E2 /* System.Xml__7.cpp */,
				980A11C75E6594F47A4D0C4E /* System.Xml__8.cpp */,
				B3B3B55ACB389C47124F63DC /* System.Xml__9.cpp */,
				F2E5D9A741FB7ADF7324BC5B /* System.cpp */,
				76E90196E5C6B8BA16A9EB4C /* System_CodeGen.c */,
				6DAB81F028AF6E963A2D1970 /* System_Debugger.c */,
				35446C8F021B4551882708E5 /* System__1.cpp */,
				561FE8E0DA0AF097A24473A9 /* System__10.cpp */,
				19CC4FC0E5C5AFF6FF9E64FA /* System__11.cpp */,
				9144340CC541DD77AADA21F3 /* System__2.cpp */,
				CB6390412026FA54BD728F2E /* System__3.cpp */,
				058D9CAFDB588D59044A2B25 /* System__4.cpp */,
				56115212599D5E43FF019CAB /* System__5.cpp */,
				782F20A8C011459AB9E0A41E /* System__6.cpp */,
				7771951A12B3A7284778E4C6 /* System__7.cpp */,
				4731C5266AFA863C29647599 /* System__8.cpp */,
				8482C9568E6A49FD58B18615 /* System__9.cpp */,
				375E310F9505DD2005F3438A /* UniTask.Linq.cpp */,
				C781041026C372AE365E9C12 /* UniTask.Linq_CodeGen.c */,
				E2566265D327EED99C2FE1A1 /* UniTask.Linq_Debugger.c */,
				CDF3C3FBEC76E7F554BCAC7F /* UniTask.cpp */,
				811857DBA7707D83A80DEBA7 /* UniTask_CodeGen.c */,
				68D8C1664EA1B56E9061D907 /* UniTask_Debugger.c */,
				0374DE58E2FECDF95FC29943 /* Unity.Burst.Unsafe.cpp */,
				DBF5A1414DA20E96D008D306 /* Unity.Burst.Unsafe_CodeGen.c */,
				9F0C5E0D39B881CEC215D850 /* Unity.Burst.Unsafe_Debugger.c */,
				6F3766F996C276272977836B /* Unity.Burst.cpp */,
				7BCD68DC79BE5C86BB4574CB /* Unity.Burst_CodeGen.c */,
				8288C1289036AC5BFE45E61D /* Unity.Burst_Debugger.c */,
				D71208925BF18B7D39D077BE /* Unity.Collections.cpp */,
				C681789DEFADCD202ECA939C /* Unity.Collections_CodeGen.c */,
				48934EA8227C7DAAAECD158F /* Unity.Collections_Debugger.c */,
				196E9825E8305BAF49AC748B /* Unity.Mathematics.cpp */,
				47A43E6546DABAEA781AD71C /* Unity.Mathematics_CodeGen.c */,
				A2B051349EB576E097B197CA /* Unity.Mathematics_Debugger.c */,
				FF80ED27C0BED9BC1BA9070D /* Unity.TextMeshPro.cpp */,
				004B7ED377C245FA07EA4CA7 /* Unity.TextMeshPro_CodeGen.c */,
				74FECA58CE4743229729F562 /* Unity.TextMeshPro_Debugger.c */,
				0FA54142F83E19F3610B1991 /* Unity.TextMeshPro__1.cpp */,
				9664DF812E72471EB20F5443 /* Unity.TextMeshPro__2.cpp */,
				9D3B639B6E4EB4B913EDABF2 /* Unity.TextMeshPro__3.cpp */,
				EA841D12341C9363E02EE00F /* Unity.TextMeshPro__4.cpp */,
				F09919364E13EC87792FCBE3 /* Unity.TextMeshPro__5.cpp */,
				07F77C1BA780C0E1C591423E /* Unity.TextMeshPro__6.cpp */,
				2B3E91347CC233053DCB5A6A /* Unity.TextMeshPro__7.cpp */,
				5A1480F12D5DBF237742A501 /* Unity.VisualScripting.Core.cpp */,
				D917D7ECFD92AE0B95F7723A /* Unity.VisualScripting.Core_CodeGen.c */,
				6968D18C16085C2E74DA6431 /* Unity.VisualScripting.Core_Debugger.c */,
				079A43E3F27C556AF34E8FDB /* Unity.VisualScripting.Core__1.cpp */,
				B0F046F0360DDF893A8A4FD2 /* Unity.VisualScripting.Core__2.cpp */,
				6679B66625DA79D57B50501A /* UnityClassRegistration.cpp */,
				9D0A7564828238ED0C6F4AFD /* UnityEngine.AIModule.cpp */,
				B803F94314B18CB6B2655B90 /* UnityEngine.AIModule_CodeGen.c */,
				B308234A6A71A74A630CA80A /* UnityEngine.AIModule_Debugger.c */,
				9A3270E8F2A4E1126343E8A4 /* UnityEngine.ARModule.cpp */,
				9C067E18382CE087FF58784D /* UnityEngine.ARModule_CodeGen.c */,
				B2C5B578DC19408E99C7402B /* UnityEngine.ARModule_Debugger.c */,
				FC3E8E87EC9D6CB4B64081C3 /* UnityEngine.AccessibilityModule.cpp */,
				D4A00AF625F26E9AFA7D5EAA /* UnityEngine.AccessibilityModule_CodeGen.c */,
				2B4E5941CFE34B6535203334 /* UnityEngine.AccessibilityModule_Debugger.c */,
				F7F2599B7A76BDAC54FDCB60 /* UnityEngine.AndroidJNIModule.cpp */,
				2030A7564DD6412E7C11EA97 /* UnityEngine.AndroidJNIModule_CodeGen.c */,
				29C9A11F248A4A9690F9001D /* UnityEngine.AndroidJNIModule_Debugger.c */,
				DA1731C322C733D4F9215478 /* UnityEngine.AnimationModule.cpp */,
				A7CC23DF498D4EFEEB0AF888 /* UnityEngine.AnimationModule_CodeGen.c */,
				B637B07876CF12586D7C9CD5 /* UnityEngine.AnimationModule_Debugger.c */,
				F4F4286D3BE59E565564055C /* UnityEngine.AssetBundleModule.cpp */,
				979CE552E8632B2F59D7A75B /* UnityEngine.AssetBundleModule_CodeGen.c */,
				B189DAEDDE23ED3D43BA6111 /* UnityEngine.AssetBundleModule_Debugger.c */,
				C24D3A15A69D047679E1AF2F /* UnityEngine.AudioModule.cpp */,
				F02A78402345158DB659E78E /* UnityEngine.AudioModule_CodeGen.c */,
				6A15947266FEEEC3FE52FF89 /* UnityEngine.AudioModule_Debugger.c */,
				6ECF4D044F64234638E5FC65 /* UnityEngine.ClothModule.cpp */,
				B80FFAA110DC5159E206F4F5 /* UnityEngine.ClothModule_CodeGen.c */,
				209F1A6FDF0F7CD4B19CFB7B /* UnityEngine.ClothModule_Debugger.c */,
				6BED2FDDD9A6A84C09DCE287 /* UnityEngine.ContentLoadModule.cpp */,
				B3B78795CA5004C3F52079A6 /* UnityEngine.ContentLoadModule_CodeGen.c */,
				79278D851CECE04A1B143367 /* UnityEngine.ContentLoadModule_Debugger.c */,
				418A53639A9FDF6906C6BFBC /* UnityEngine.CoreModule.cpp */,
				29DB862887FD80F9544C0F27 /* UnityEngine.CoreModule_CodeGen.c */,
				7251EA772488E8D0B647AA11 /* UnityEngine.CoreModule_Debugger.c */,
				90114C270D8D46748478032F /* UnityEngine.CoreModule__1.cpp */,
				0D31AAF3C6C0CC11D285C738 /* UnityEngine.CoreModule__2.cpp */,
				ECAF71504226E32235BC8E2F /* UnityEngine.CoreModule__3.cpp */,
				715666142DF9AB95ECEA536D /* UnityEngine.CoreModule__4.cpp */,
				DC49E0D6482D5C0DB373D3F3 /* UnityEngine.CoreModule__5.cpp */,
				211F060733D8F95086408787 /* UnityEngine.CoreModule__6.cpp */,
				C0E20C4BEB0D765659856866 /* UnityEngine.CoreModule__7.cpp */,
				79AEFAA2999CF84161DD28DE /* UnityEngine.CoreModule__8.cpp */,
				135D0ADBD4A33357B22F37EC /* UnityEngine.CrashReportingModule.cpp */,
				F4CEF9BD1988923C37044087 /* UnityEngine.CrashReportingModule_CodeGen.c */,
				E107302C2E0D6B6F367EFBB2 /* UnityEngine.CrashReportingModule_Debugger.c */,
				8DB79685079E7F8DE973185B /* UnityEngine.DSPGraphModule.cpp */,
				D081C282527F806F47EB607B /* UnityEngine.DSPGraphModule_CodeGen.c */,
				85D23FAC1867CA1EB72F971F /* UnityEngine.DSPGraphModule_Debugger.c */,
				60B9DB6A1EF87ABF75AA1505 /* UnityEngine.DirectorModule.cpp */,
				88FC6E3CAED8A246BF94CBAC /* UnityEngine.DirectorModule_CodeGen.c */,
				F93B6EC9ADF6740B487EB7B0 /* UnityEngine.DirectorModule_Debugger.c */,
				EB4E970CF51960FC6EF12D91 /* UnityEngine.GIModule.cpp */,
				9B7E46021F9A4569A2F8B849 /* UnityEngine.GIModule_CodeGen.c */,
				9D074ECA115BCF455EE2E8EF /* UnityEngine.GIModule_Debugger.c */,
				A4591E5013E0219646DFFBD4 /* UnityEngine.GameCenterModule.cpp */,
				9EA3E57A19EDE7497E36F3DF /* UnityEngine.GameCenterModule_CodeGen.c */,
				70DB5ACDDC37E7CA9E4EDC4C /* UnityEngine.GameCenterModule_Debugger.c */,
				857AE3AA6D7217204F38E177 /* UnityEngine.GridModule.cpp */,
				58D3A7800BA0F8BFFDC7DDAA /* UnityEngine.GridModule_CodeGen.c */,
				626C4499FCB90C9951F0B3D0 /* UnityEngine.GridModule_Debugger.c */,
				5EBDAF8BD2370611732140AE /* UnityEngine.HotReloadModule.cpp */,
				F3865E57914B553931A5D241 /* UnityEngine.HotReloadModule_CodeGen.c */,
				74EC73663D0BD731F26EF000 /* UnityEngine.HotReloadModule_Debugger.c */,
				D61973816F5E9F99E95C8C57 /* UnityEngine.IMGUIModule.cpp */,
				B65026F4E0A439EAE4490865 /* UnityEngine.IMGUIModule_CodeGen.c */,
				FE9D71900DB30014C49FC674 /* UnityEngine.IMGUIModule_Debugger.c */,
				61703DF54F612431C9B2CEC6 /* UnityEngine.IMGUIModule__1.cpp */,
				232710CEDCF91E0181CCF102 /* UnityEngine.IMGUIModule__2.cpp */,
				0FF38D860993A4461BE3D1D6 /* UnityEngine.ImageConversionModule.cpp */,
				A918C8B44A33797E60C31B4D /* UnityEngine.ImageConversionModule_CodeGen.c */,
				2687C070B6BABD52B518AC9D /* UnityEngine.ImageConversionModule_Debugger.c */,
				677B3A5BE7B2CB7BD477BB0A /* UnityEngine.InputLegacyModule.cpp */,
				62CBF79C3A505CA169C0A2F8 /* UnityEngine.InputLegacyModule_CodeGen.c */,
				F9E100714A248BEC8B9C285D /* UnityEngine.InputLegacyModule_Debugger.c */,
				212E41FE4D4EBAD53A5E0F6F /* UnityEngine.InputModule.cpp */,
				0D1FF11E9C4F5348B320B360 /* UnityEngine.InputModule_CodeGen.c */,
				03A6C04DBFCC9DD52544385D /* UnityEngine.InputModule_Debugger.c */,
				9C6571465C64216B59151280 /* UnityEngine.JSONSerializeModule.cpp */,
				544BB825B8624DFAB4CA9C2C /* UnityEngine.JSONSerializeModule_CodeGen.c */,
				BD803104C2AE3F66699C2C08 /* UnityEngine.JSONSerializeModule_Debugger.c */,
				2A1756D56CDCBEB23C273602 /* UnityEngine.LocalizationModule.cpp */,
				E4C944039088C7D345B2460D /* UnityEngine.LocalizationModule_CodeGen.c */,
				759AB2B41E34648F0B834DEA /* UnityEngine.LocalizationModule_Debugger.c */,
				FA0D72F8EDBD0798BCFC9916 /* UnityEngine.ParticleSystemModule.cpp */,
				F98883DE16B476EDC83A25E2 /* UnityEngine.ParticleSystemModule_CodeGen.c */,
				9315E97CF25DEC97ED24E7FE /* UnityEngine.ParticleSystemModule_Debugger.c */,
				282C69E0C3A9D7A01FA7BDE0 /* UnityEngine.PerformanceReportingModule.cpp */,
				85F9C154ED639637D65BCC5C /* UnityEngine.PerformanceReportingModule_CodeGen.c */,
				C50B188E0276BA2E26358CED /* UnityEngine.PerformanceReportingModule_Debugger.c */,
				F9F1230E7B00F6B2FFC7A8EB /* UnityEngine.Physics2DModule.cpp */,
				9432641D8FAD3395C648341C /* UnityEngine.Physics2DModule_CodeGen.c */,
				F939D2575DAA3E85196F5BB7 /* UnityEngine.Physics2DModule_Debugger.c */,
				7A0126D9A77C3A2BC78EAC19 /* UnityEngine.PhysicsModule.cpp */,
				3B557CF9358F36A58A8F38FC /* UnityEngine.PhysicsModule_CodeGen.c */,
				9E43ABBC7220989A041EB4AE /* UnityEngine.PhysicsModule_Debugger.c */,
				8239340CC04E228289A4A60F /* UnityEngine.ProfilerModule.cpp */,
				4A04E986DBE4161111452A21 /* UnityEngine.ProfilerModule_CodeGen.c */,
				3C604F94CAC079219AA8E603 /* UnityEngine.ProfilerModule_Debugger.c */,
				218A5F5892B6E72316C9FFDA /* UnityEngine.PropertiesModule.cpp */,
				0D74EFEA392C0E65155F7A96 /* UnityEngine.PropertiesModule_CodeGen.c */,
				5FA2C48F1E333FAD0D36009D /* UnityEngine.PropertiesModule_Debugger.c */,
				E66A9031D16170B89FB66341 /* UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.cpp */,
				D347FD89B300105FA57ABF90 /* UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule_CodeGen.c */,
				30A72D51776289D79DEEDA61 /* UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule_Debugger.c */,
				35E347F2DB0DFA23974DEB26 /* UnityEngine.ScreenCaptureModule.cpp */,
				C3D653AC6742EB0121722217 /* UnityEngine.ScreenCaptureModule_CodeGen.c */,
				2460523730B2F13867ED42C8 /* UnityEngine.ScreenCaptureModule_Debugger.c */,
				1562360321C1A01A12D225BC /* UnityEngine.SharedInternalsModule.cpp */,
				ACE173F91BF3866BEA121D66 /* UnityEngine.SharedInternalsModule_CodeGen.c */,
				60A2A5634B079CBE8AA2AB50 /* UnityEngine.SharedInternalsModule_Debugger.c */,
				56965297FFBB2280B06DD136 /* UnityEngine.SpriteMaskModule.cpp */,
				BDACB114054C760C9D862236 /* UnityEngine.SpriteMaskModule_CodeGen.c */,
				D01680A0991BA8C95A4C44DE /* UnityEngine.SpriteMaskModule_Debugger.c */,
				354C830C3FA3287CFC1D3844 /* UnityEngine.SpriteShapeModule.cpp */,
				F24B06F17D255CA359EB4840 /* UnityEngine.SpriteShapeModule_CodeGen.c */,
				4B927A0205588C3B49CC5110 /* UnityEngine.SpriteShapeModule_Debugger.c */,
				120999864790FD52FD3963EF /* UnityEngine.StreamingModule.cpp */,
				34CC05B10329BD755492E6F7 /* UnityEngine.StreamingModule_CodeGen.c */,
				E44256EF68D3620E3BA7B3C5 /* UnityEngine.StreamingModule_Debugger.c */,
				DE830B206631911313BD3EE8 /* UnityEngine.SubstanceModule.cpp */,
				6FAB350C926C6A86ECCBC783 /* UnityEngine.SubstanceModule_CodeGen.c */,
				93889A7E87D040174319E1BD /* UnityEngine.SubstanceModule_Debugger.c */,
				28D10947512B25AFE0C9D0D1 /* UnityEngine.SubsystemsModule.cpp */,
				DB998B9FC449DC335718A76C /* UnityEngine.SubsystemsModule_CodeGen.c */,
				76CD73C928A465C0F9B1FA48 /* UnityEngine.SubsystemsModule_Debugger.c */,
				0546416B09929F2E2CC06B90 /* UnityEngine.TLSModule.cpp */,
				8C939BCC03F2ED185E6B3E24 /* UnityEngine.TLSModule_CodeGen.c */,
				D3BB5C24E04FA0D14EB9ECC3 /* UnityEngine.TLSModule_Debugger.c */,
				4F220071BD34BF6BF75F1125 /* UnityEngine.TerrainModule.cpp */,
				A84D8F84F862A6F5608E5F7A /* UnityEngine.TerrainModule_CodeGen.c */,
				F7FB70D9617296AB7653F474 /* UnityEngine.TerrainModule_Debugger.c */,
				B16FC38963BF1636A8CECA90 /* UnityEngine.TerrainPhysicsModule.cpp */,
				3AE2C1E81BAF7A69DB8DD34C /* UnityEngine.TerrainPhysicsModule_CodeGen.c */,
				AFAC13389EEA2957CD9F0B97 /* UnityEngine.TerrainPhysicsModule_Debugger.c */,
				4E33F765E8E51E7DFF5D52A6 /* UnityEngine.TextCoreFontEngineModule.cpp */,
				E518660204D9E27F74306195 /* UnityEngine.TextCoreFontEngineModule_CodeGen.c */,
				0C33FB3ADEC96C25F3C484DA /* UnityEngine.TextCoreFontEngineModule_Debugger.c */,
				01542E4A7351BF537D5F9A2D /* UnityEngine.TextCoreTextEngineModule.cpp */,
				CF2D9CD79707BC66F0D724D2 /* UnityEngine.TextCoreTextEngineModule_CodeGen.c */,
				E8C92A8C826C15791DB4D76F /* UnityEngine.TextCoreTextEngineModule_Debugger.c */,
				23225D541B6C1620CBF548E2 /* UnityEngine.TextCoreTextEngineModule__1.cpp */,
				4EEAF035005D81C6A49A7C45 /* UnityEngine.TextCoreTextEngineModule__2.cpp */,
				5B2F54EB41B243A7C1793B5C /* UnityEngine.TextCoreTextEngineModule__3.cpp */,
				E045302D92F3D175A9719406 /* UnityEngine.TextCoreTextEngineModule__4.cpp */,
				C89119454D57EDC42A59D87C /* UnityEngine.TextRenderingModule.cpp */,
				9CE38A0EB9BA95FB48A7537E /* UnityEngine.TextRenderingModule_CodeGen.c */,
				F31761DC8785C18DED8DC01A /* UnityEngine.TextRenderingModule_Debugger.c */,
				E28E0AD14FECA0BE6084E4CE /* UnityEngine.TilemapModule.cpp */,
				37AC7AC7977FAFF44E7DAA61 /* UnityEngine.TilemapModule_CodeGen.c */,
				037E4726C630DA1E62A5CBDD /* UnityEngine.TilemapModule_Debugger.c */,
				E0263673B7642BE0F04DE813 /* UnityEngine.UI.cpp */,
				6D100D31A5258D5757D1271A /* UnityEngine.UIElementsModule.cpp */,
				C20B2680F0033B47C3BB8134 /* UnityEngine.UIElementsModule_CodeGen.c */,
				9295ED0F8281EADD6E244700 /* UnityEngine.UIElementsModule_Debugger.c */,
				2B422D1DE14C2A8D3F21065A /* UnityEngine.UIElementsModule__1.cpp */,
				D7503FB91502F4DD896CF714 /* UnityEngine.UIElementsModule__10.cpp */,
				2A4E1F27CBC17CFBC9D90381 /* UnityEngine.UIElementsModule__11.cpp */,
				ABF66CEE0A3ACFFBDA78140E /* UnityEngine.UIElementsModule__12.cpp */,
				6ACBE790647C380298DD6CF0 /* UnityEngine.UIElementsModule__13.cpp */,
				35BA4E1F3D3749F3A77D35B2 /* UnityEngine.UIElementsModule__14.cpp */,
				9CC7631EEE51DCCD5E2C1ABA /* UnityEngine.UIElementsModule__15.cpp */,
				8CAB5C6EF6FAC20D93CFF981 /* UnityEngine.UIElementsModule__16.cpp */,
				13D52F001D38A56FB76E88A5 /* UnityEngine.UIElementsModule__17.cpp */,
				76159C624A1386AC23D3E469 /* UnityEngine.UIElementsModule__18.cpp */,
				53D50B1E0F71DFEBC9CABD14 /* UnityEngine.UIElementsModule__2.cpp */,
				3CC546E182B89988089AC598 /* UnityEngine.UIElementsModule__3.cpp */,
				E8A3C67A913D9E240397EFB1 /* UnityEngine.UIElementsModule__4.cpp */,
				0F1F382C4530E826AE63D9D6 /* UnityEngine.UIElementsModule__5.cpp */,
				64E2DA6CA86A91002CE87FE3 /* UnityEngine.UIElementsModule__6.cpp */,
				15A19AFEA90DC0B5F336605F /* UnityEngine.UIElementsModule__7.cpp */,
				F55249155698357E094F40BE /* UnityEngine.UIElementsModule__8.cpp */,
				45EFF0285CC84ED02CB30A69 /* UnityEngine.UIElementsModule__9.cpp */,
				34B2FF818AC0E4621CBC5379 /* UnityEngine.UIModule.cpp */,
				727353E0A17F3CD26C387C4B /* UnityEngine.UIModule_CodeGen.c */,
				1F8BFA2E5A3610BA64D7B9B0 /* UnityEngine.UIModule_Debugger.c */,
				6762A34FEF91061AC451841F /* UnityEngine.UI_CodeGen.c */,
				04FAC3AAC831D75114B40DDB /* UnityEngine.UI_Debugger.c */,
				672987B2BECEFF858A090C4A /* UnityEngine.UI__1.cpp */,
				89EF3E4A2B0003F14AAD225D /* UnityEngine.UI__2.cpp */,
				2E9FEEBC77FE9D53FD71E76A /* UnityEngine.UI__3.cpp */,
				53204F470DA89B666DECF64E /* UnityEngine.UI__4.cpp */,
				5904DF798F0A0B60FDE28972 /* UnityEngine.UmbraModule.cpp */,
				843F731F67294457F5D3CE6D /* UnityEngine.UmbraModule_CodeGen.c */,
				67F0DF2A0A095662168CF329 /* UnityEngine.UmbraModule_Debugger.c */,
				2A64BC0FB139C78B893FDCAA /* UnityEngine.UnityAnalyticsCommonModule.cpp */,
				90E69A22DD43A16E030D8110 /* UnityEngine.UnityAnalyticsCommonModule_CodeGen.c */,
				1BB2FEF7E52AA544C2D86C49 /* UnityEngine.UnityAnalyticsCommonModule_Debugger.c */,
				D9AC6836413CDFEB9D3D4DCA /* UnityEngine.UnityAnalyticsModule.cpp */,
				CD4079BF78197D083920D996 /* UnityEngine.UnityAnalyticsModule_CodeGen.c */,
				37EAFBD23847FB208B8464AF /* UnityEngine.UnityAnalyticsModule_Debugger.c */,
				703F80825404E22CCED85738 /* UnityEngine.UnityConnectModule.cpp */,
				69878364FA46AF9AF01A1E2D /* UnityEngine.UnityConnectModule_CodeGen.c */,
				C00B25CE1E6F85C6F63C5984 /* UnityEngine.UnityConnectModule_Debugger.c */,
				7C8909A9432588EF82DC1449 /* UnityEngine.UnityCurlModule.cpp */,
				F0EA4F626013C9E032D8A2AD /* UnityEngine.UnityCurlModule_CodeGen.c */,
				FF066C4423BD31959A6000C2 /* UnityEngine.UnityCurlModule_Debugger.c */,
				4C5826BC3AF1776F7B20B4E2 /* UnityEngine.UnityTestProtocolModule.cpp */,
				6C99EF8B0B6349FDCBC9E290 /* UnityEngine.UnityTestProtocolModule_CodeGen.c */,
				347B08775B35751B1821B42A /* UnityEngine.UnityTestProtocolModule_Debugger.c */,
				AF0D68325B7E9D496A51734C /* UnityEngine.UnityWebRequestAssetBundleModule.cpp */,
				9E90AE8AEA3FE3DC0C6E0BED /* UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c */,
				C47CCFA87796F12A1228E727 /* UnityEngine.UnityWebRequestAssetBundleModule_Debugger.c */,
				FCA5FE8B92B32260C9A32E3F /* UnityEngine.UnityWebRequestAudioModule.cpp */,
				30E35F62D8A9A9617E95F23E /* UnityEngine.UnityWebRequestAudioModule_CodeGen.c */,
				581B5EF0511A520BC9497338 /* UnityEngine.UnityWebRequestAudioModule_Debugger.c */,
				996AF65B21117545805EDE7A /* UnityEngine.UnityWebRequestModule.cpp */,
				B13B5CAA12B4643FE811ED64 /* UnityEngine.UnityWebRequestModule_CodeGen.c */,
				C7CB245619C3670C141BB501 /* UnityEngine.UnityWebRequestModule_Debugger.c */,
				4AE6620E3ECDEE30CBB8DF48 /* UnityEngine.UnityWebRequestTextureModule.cpp */,
				76A06ED55BC26ABEC74D9B5E /* UnityEngine.UnityWebRequestTextureModule_CodeGen.c */,
				E2EF56E2797F5DBE1AFB6F3C /* UnityEngine.UnityWebRequestTextureModule_Debugger.c */,
				8ADAC812E52229002239D46B /* UnityEngine.UnityWebRequestWWWModule.cpp */,
				A2562FA24FCA1F0C707324C8 /* UnityEngine.UnityWebRequestWWWModule_CodeGen.c */,
				245383633D96F1EBF8071FE5 /* UnityEngine.UnityWebRequestWWWModule_Debugger.c */,
				46D7F0219796DB59D3B54049 /* UnityEngine.VFXModule.cpp */,
				0C361D905CC46C46E74E790D /* UnityEngine.VFXModule_CodeGen.c */,
				0D84C192128D6CD0E262978B /* UnityEngine.VFXModule_Debugger.c */,
				A39A600A7B98C2458EAED5FA /* UnityEngine.VRModule.cpp */,
				3E2067B7FF4DEA685E5DDD1A /* UnityEngine.VRModule_CodeGen.c */,
				53E271A00C9F9828E4C3AEEB /* UnityEngine.VRModule_Debugger.c */,
				2A94588CB7D88ADC352647E9 /* UnityEngine.VehiclesModule.cpp */,
				72CDAAB9A750C4C524BA0E01 /* UnityEngine.VehiclesModule_CodeGen.c */,
				A40145096953336761F9ECA1 /* UnityEngine.VehiclesModule_Debugger.c */,
				901CB410390827075F68C913 /* UnityEngine.VideoModule.cpp */,
				F32E24B5FD8122F20E7308A7 /* UnityEngine.VideoModule_CodeGen.c */,
				DDFFC14FC5F2C34CB902E6FF /* UnityEngine.VideoModule_Debugger.c */,
				4DE81098A8E6BD7AD67B3EF4 /* UnityEngine.WindModule.cpp */,
				60F8566A9938D8EEF754221A /* UnityEngine.WindModule_CodeGen.c */,
				068A319EC6F63095C6A505D8 /* UnityEngine.WindModule_Debugger.c */,
				652C3B4418CC4076B5B964F7 /* UnityEngine.XRModule.cpp */,
				0D877878793A1AFE06CA78FD /* UnityEngine.XRModule_CodeGen.c */,
				6793EED162DFAA97FA2F9745 /* UnityEngine.XRModule_Debugger.c */,
				73E1EB45A942E11178F3915F /* UnityEngine.cpp */,
				1701016BB5DA32953B06ADB5 /* UnityEngine_CodeGen.c */,
				4A4901A84201EE0A3D97E0A0 /* UnityEngine_Debugger.c */,
				87605856C2B447518B98EAF3 /* UnityICallRegistration.cpp */,
				AED95C519544DEF0103B9C95 /* __Generated.cpp */,
				8FE3E92069BB36F8854D7277 /* __Generated_CodeGen.c */,
				31186A0E47C72F72C527829D /* __Generated_Debugger.c */,
				4ED0B579112CB1E12AEF01F5 /* mscorlib.cpp */,
				470F4DBFC6CF22AFB4920379 /* mscorlib_CodeGen.c */,
				4C0C4B11A8BFDF671CA131CF /* mscorlib_Debugger.c */,
				F1F28F611D1E7EE7CCC25F71 /* mscorlib__1.cpp */,
				C3C2D72757B1A8A28AE76822 /* mscorlib__10.cpp */,
				1DC31C363AA49E88C15788BB /* mscorlib__11.cpp */,
				D0609838D7B79343BB1B66CD /* mscorlib__12.cpp */,
				E63E22A2D76ACB6254C3A017 /* mscorlib__13.cpp */,
				0084A6F53FDC53D8C88D04BC /* mscorlib__14.cpp */,
				F5C0D9C036EFC8EF66FE06C4 /* mscorlib__15.cpp */,
				69BE7C8970379AE244085D79 /* mscorlib__16.cpp */,
				CAB37C0B5E85683C4FB82A76 /* mscorlib__17.cpp */,
				CE1CD281A8020E57A0495C3E /* mscorlib__18.cpp */,
				DCC618ACC1C885764FCFF788 /* mscorlib__19.cpp */,
				2BCFF7AAB07F012FD2E34C5B /* mscorlib__2.cpp */,
				92E17166D3BDB3EA24239E77 /* mscorlib__20.cpp */,
				3369FBBB1202A2D16B16FA7E /* mscorlib__21.cpp */,
				AB2BF8F7BAFBE6886580905D /* mscorlib__22.cpp */,
				E9A7659A3EF108621EF5E740 /* mscorlib__23.cpp */,
				2BEDAA3AFB931C1E1C406501 /* mscorlib__3.cpp */,
				58D0A5E12D87A4B766719265 /* mscorlib__4.cpp */,
				D36D733DA6A3052F599DC7CE /* mscorlib__5.cpp */,
				B39436E255FF5376259080BE /* mscorlib__6.cpp */,
				65A609DC99CBAA15044D5D88 /* mscorlib__7.cpp */,
				43289C428F36A64040F01B14 /* mscorlib__8.cpp */,
				EAFCEA713D2A37FF5D81C6CB /* mscorlib__9.cpp */,
			);
			path = il2cppOutput;
			sourceTree = "<group>";
		};
		53D667C6AFCF5C846FB3B413 /* iOS */ = {
			isa = PBXGroup;
			children = (
				80CFB204F2EC78281EC6CF9E /* PGUnityPlugin.h */,
				28E173888AE846C65CF0DC84 /* PGUnityPlugin.mm */,
				1C4CC834EAE2BD1247708FA7 /* PGUnityPluginDelegate.h */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		5623C57817FDCB0800090B9E /* Unity-iPhone Tests */ = {
			isa = PBXGroup;
			children = (
				5623C57917FDCB0800090B9E /* Supporting Files */,
				5623C57E17FDCB0900090B9E /* Unity_iPhone_Tests.m */,
			);
			path = "Unity-iPhone Tests";
			sourceTree = "<group>";
		};
		5623C57917FDCB0800090B9E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				5623C57A17FDCB0900090B9E /* Unity-iPhone Tests-Info.plist */,
				5623C58017FDCB0900090B9E /* Unity-iPhone Tests-Prefix.pch */,
				5623C57B17FDCB0900090B9E /* InfoPlist.strings */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		5853089C430571CE0D5DC4B0 /* iOS */ = {
			isa = PBXGroup;
			children = (
				366D4A68D81F1245950129DE /* IngameDebugConsole.mm */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		67A8E0415A1307476AC7DEF1 /* FlutterUnityIntegration */ = {
			isa = PBXGroup;
			children = (
				370000EAB67CF9F0667B6C5B /* Plugins */,
			);
			path = FlutterUnityIntegration;
			sourceTree = "<group>";
		};
		89D1F5FD607898AE36842EED /* iOS */ = {
			isa = PBXGroup;
			children = (
				51E0FA324635BD384CCB75D3 /* NativeCallProxy.h */,
				CAE0DAFFEEF167F8D2A2BC72 /* NativeCallProxy.mm */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		8A3EDDC51615B7C1001839E9 /* UI */ = {
			isa = PBXGroup;
			children = (
				8A9FCB111617295F00C05364 /* ActivityIndicator.h */,
				8A9FCB121617295F00C05364 /* ActivityIndicator.mm */,
				8A142DC41636943E00DD87CA /* Keyboard.h */,
				8A142DC51636943E00DD87CA /* Keyboard.mm */,
				8AC71EC219E7FBA90027502F /* OrientationSupport.h */,
				8AC71EC319E7FBA90027502F /* OrientationSupport.mm */,
				4E090A331F27884B0077B28D /* StoreReview.m */,
				8A4815BF17A287D2003FBFD5 /* UnityAppController+ViewHandling.h */,
				8A4815C017A287D2003FBFD5 /* UnityAppController+ViewHandling.mm */,
				85E5623620F4F4D1001DFEF6 /* UnityView+Keyboard.mm */,
				8A7939FE1ED43EE100B44EF1 /* UnityView+iOS.h */,
				8A7939FF1ED43EE100B44EF1 /* UnityView+iOS.mm */,
				8A793A001ED43EE100B44EF1 /* UnityView+tvOS.h */,
				8A793A011ED43EE100B44EF1 /* UnityView+tvOS.mm */,
				8A851BA516FB2F6D00E911DB /* UnityView.h */,
				8A851BA616FB2F6D00E911DB /* UnityView.mm */,
				8A793A021ED43EE100B44EF1 /* UnityViewControllerBase+iOS.h */,
				8A793A031ED43EE100B44EF1 /* UnityViewControllerBase+iOS.mm */,
				8A793A041ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.h */,
				8A793A051ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.mm */,
				8BEDFA3729C88F86007F26D7 /* UnityViewControllerBase+visionOS.h */,
				8BEDFA3829C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm */,
				8A21AED21622F59300AF8007 /* UnityViewControllerBase.h */,
				8A7939FC1ED2F53200B44EF1 /* UnityViewControllerBase.mm */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		8A5C148F174E662D0006EB36 /* PluginBase */ = {
			isa = PBXGroup;
			children = (
				8AF7755E17997D1300341121 /* AppDelegateListener.h */,
				8AF7755F17997D1300341121 /* AppDelegateListener.mm */,
				8A292A9717992CE100409BA4 /* LifeCycleListener.h */,
				8A292A9817992CE100409BA4 /* LifeCycleListener.mm */,
				8A5C1490174E662D0006EB36 /* RenderPluginDelegate.h */,
				8A5C1491174E662D0006EB36 /* RenderPluginDelegate.mm */,
				AAFE69D019F187C200638316 /* UnityViewControllerListener.h */,
				AAFE69D119F187C200638316 /* UnityViewControllerListener.mm */,
			);
			path = PluginBase;
			sourceTree = "<group>";
		};
		8AF18FE316490981007B4420 /* Unity */ = {
			isa = PBXGroup;
			children = (
				8ABDBCE019CAFCF700A842FF /* AVCapture.h */,
				8AC74A9419B47FEF00019D38 /* AVCapture.mm */,
				8A2AA93316E0978D001FB470 /* CMVideoSampling.h */,
				8A2AA93416E0978D001FB470 /* CMVideoSampling.mm */,
				8A367F5916A6D36F0012ED11 /* CVTextureCache.h */,
				8A367F5A16A6D36F0012ED11 /* CVTextureCache.mm */,
				8ADCE38919C87177006F04F6 /* CameraCapture.h */,
				8ADCE38A19C87177006F04F6 /* CameraCapture.mm */,
				8ACB801B177081D4005D0019 /* DeviceSettings.mm */,
				8A5E0B8F16849D1800CBB6FE /* DisplayManager.h */,
				8A5E0B9016849D1800CBB6FE /* DisplayManager.mm */,
				8A25E6D118D767E20006A227 /* Filesystem.mm */,
				8A16150B1A8E4362006FA788 /* FullScreenVideoPlayer.mm */,
				30347B5D7991A325732A4F10 /* IUnityGraphics.h */,
				894C1B251937BE1BC2B4D36C /* IUnityGraphicsMetal.h */,
				679ACDCF6D14CA6FE6F81479 /* IUnityInterface.h */,
				8A6720A319EEB905006C92E0 /* InternalProfiler.cpp */,
				8A6720A419EEB905006C92E0 /* InternalProfiler.h */,
				1859EA9A19214E7B0022C3D3 /* MetalHelper.mm */,
				8A2BC6DE245061EE00C7C97D /* NoGraphicsHelper.mm */,
				8A6137121A10B57700059EDF /* ObjCRuntime.h */,
				FC0B20A11B7A4F0B00FDFC55 /* OnDemandResources.mm */,
				8A90541019EE8843003D1039 /* UnityForwardDecls.h */,
				8A851BAB16FC875E00E911DB /* UnityInterface.h */,
				8AECDC781950835600CB29E8 /* UnityMetalSupport.h */,
				8AA108C01948732900D0538B /* UnityRendering.h */,
				84DC28F71C51383500BC67D7 /* UnityReplayKit.h */,
				84DC28F51C5137FE00BC67D7 /* UnityReplayKit.mm */,
				848031E01C5160D700FCEAB7 /* UnityReplayKit_Scripting.mm */,
				F6361BB52195CE5D00F61766 /* UnitySharedDecls.h */,
				862C244F20AEC7AC006FB4AD /* UnityWebRequest.mm */,
				8AB3CB3C16D390BA00697AD5 /* VideoPlayer.h */,
				8AB3CB3D16D390BB00697AD5 /* VideoPlayer.mm */,
			);
			path = Unity;
			sourceTree = "<group>";
		};
		8B16182EBCF2731BF1F27111 /* IngameDebugConsole */ = {
			isa = PBXGroup;
			children = (
				5853089C430571CE0D5DC4B0 /* iOS */,
			);
			path = IngameDebugConsole;
			sourceTree = "<group>";
		};
		9D25AB9E213FB47800354C27 /* UnityFramework */ = {
			isa = PBXGroup;
			children = (
				D5FA3667394130C0BCCF8B0E /* ApplePrivacyManifestsMerge.txt */,
				B58EF12BF9B65BC74A92DC07 /* AppleRequiredReasonCSharpAPIs.txt */,
				9D25ABA0213FB47800354C27 /* Info.plist */,
				F2E4BD281D8D2761198E3AA0 /* PrivacyInfo.xcprivacy */,
				9D25AB9F213FB47800354C27 /* UnityFramework.h */,
			);
			path = UnityFramework;
			sourceTree = "<group>";
		};
		9DA3B0422174CB96001678C7 /* MainApp */ = {
			isa = PBXGroup;
			children = (
				9DA3B0432174CB96001678C7 /* main.mm */,
			);
			path = MainApp;
			sourceTree = SOURCE_ROOT;
		};
		D82DCFB50E8000A5005D6AD8 /* Classes */ = {
			isa = PBXGroup;
			children = (
				8A5C148F174E662D0006EB36 /* PluginBase */,
				8A3EDDC51615B7C1001839E9 /* UI */,
				8AF18FE316490981007B4420 /* Unity */,
				FC3D7EBE16D2621600D1BD0D /* CrashReporter.h */,
				FC85CCB916C3ED8000BAF7C7 /* CrashReporter.mm */,
				FC85CCBA16C3ED8000BAF7C7 /* PLCrashReporter.h */,
				8A6720A619EFAF25006C92E0 /* Prefix.pch */,
				8ACB801D177081F7005D0019 /* Preprocessor.h */,
				9D16CD8121C938BB00DD46C0 /* RedefinePlatforms.h */,
				9D16CD8021C938B300DD46C0 /* UndefinePlatforms.h */,
				8AA5D80017ABE9AF007B9910 /* UnityAppController+Rendering.h */,
				8AA5D80117ABE9AF007B9910 /* UnityAppController+Rendering.mm */,
				8A8D90D81A274A7800456C4E /* UnityAppController+UnityInterface.h */,
				8A8D90D91A274A7800456C4E /* UnityAppController+UnityInterface.mm */,
				8A851BA816FB3AD000E911DB /* UnityAppController.h */,
				8A851BA916FB3AD000E911DB /* UnityAppController.mm */,
				8AA6ADDB17818CFD00A1C5F1 /* UnityTrampolineConfigure.h */,
				56DBF99E15E3CE85007A4A8D /* iPhone_Sensors.h */,
				56DBF99C15E3CDC9007A4A8D /* iPhone_Sensors.mm */,
				D82DCFBB0E8000A5005D6AD8 /* main.mm */,
			);
			path = Classes;
			sourceTree = SOURCE_ROOT;
		};
		D8A1C7220E80637F000160D3 /* Libraries */ = {
			isa = PBXGroup;
			children = (
				67A8E0415A1307476AC7DEF1 /* FlutterUnityIntegration */,
				E65BCBDDB23DA191911454C2 /* Plugins */,
				03F528621B447098000F4FB8 /* Il2CppOptions.cpp */,
				AAC3E38B1A68945900F6174A /* RegisterFeatures.cpp */,
				AAC3E38C1A68945900F6174A /* RegisterFeatures.h */,
				7CD8D832DF5E49F3229DE515 /* baselib.a */,
				DAB61E49F0B27D71CDA3B02F /* lib_burst_generated.a */,
				17B8DA4C2189239C555CC0EC /* lib_burst_generated.cpp */,
				D8A1C72A0E8063A1000160D3 /* libiPhone-lib.a */,
				EE54DC7E56FA015DDC9659BD /* libil2cpp.a */,
			);
			path = Libraries;
			sourceTree = SOURCE_ROOT;
		};
		E65BCBDDB23DA191911454C2 /* Plugins */ = {
			isa = PBXGroup;
			children = (
				8B16182EBCF2731BF1F27111 /* IngameDebugConsole */,
				53D667C6AFCF5C846FB3B413 /* iOS */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		9D25AB9A213FB47800354C27 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9DC67E8821CBBED5005F9FA1 /* RenderPluginDelegate.h in Headers */,
				9DC67E8521CBBEBB005F9FA1 /* UnityAppController.h in Headers */,
				9D25ABA1213FB47800354C27 /* UnityFramework.h in Headers */,
				9DC67E8621CBBEC7005F9FA1 /* UndefinePlatforms.h in Headers */,
				9DC67E8721CBBEC7005F9FA1 /* RedefinePlatforms.h in Headers */,
				9DC67E8921CBBEDF005F9FA1 /* LifeCycleListener.h in Headers */,
				8BEDFA3929C88F86007F26D7 /* UnityViewControllerBase+visionOS.h in Headers */,
				A0089F71519CB873CA1D2878 /* PGUnityPluginDelegate.h in Headers */,
				E22AFF0A3F5BDAC0D9C0871C /* NativeCallProxy.h in Headers */,
				C52C4BAD9FBCA05274DA0EDC /* PGUnityPlugin.h in Headers */,
				475B26F38A3A652C6174529D /* IUnityInterface.h in Headers */,
				826E40C67FAA509B29A0EFA3 /* IUnityGraphics.h in Headers */,
				9E277B346B541C2D072C5489 /* IUnityGraphicsMetal.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		1D6058900D05DD3D006BFB54 /* Unity-iPhone */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "Unity-iPhone" */;
			buildPhases = (
				1D60588D0D05DD3D006BFB54 /* Resources */,
				83D0C1FD0E6C8D7700EBCE5D /* CopyFiles */,
				1D60588E0D05DD3D006BFB54 /* Sources */,
				1D60588F0D05DD3D006BFB54 /* Frameworks */,
				9D25ABAB213FB47800354C27 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				9D25ABA3213FB47800354C27 /* PBXTargetDependency */,
			);
			name = "Unity-iPhone";
			productName = "iPhone-target";
			productReference = 1D6058910D05DD3D006BFB54 /* Unity-Target-New.app */;
			productType = "com.apple.product-type.application";
		};
		5623C57217FDCB0800090B9E /* Unity-iPhone Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5623C58517FDCB0900090B9E /* Build configuration list for PBXNativeTarget "Unity-iPhone Tests" */;
			buildPhases = (
				5623C56F17FDCB0800090B9E /* Sources */,
				5623C57017FDCB0800090B9E /* Frameworks */,
				5623C57117FDCB0800090B9E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				5623C58217FDCB0900090B9E /* PBXTargetDependency */,
			);
			name = "Unity-iPhone Tests";
			productName = "Unity-iPhone Tests";
			productReference = 5623C57317FDCB0800090B9E /* Unity-iPhone Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7F4E059F2717216D00A2CBE4 /* GameAssembly */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7F4E05AA2717216D00A2CBE4 /* Build configuration list for PBXNativeTarget "GameAssembly" */;
			buildPhases = (
				7F4E059C2717216D00A2CBE4 /* Sources */,
				7F4E059D2717216D00A2CBE4 /* Frameworks */,
				7F4E059E2717216D00A2CBE4 /* CopyFiles */,
				C62A2A42F32E085EF849CF0B /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = GameAssembly;
			productName = GameAssembly;
			productReference = 7F4E05A02717216D00A2CBE4 /* libGameAssembly.a */;
			productType = "com.apple.product-type.library.static";
		};
		9D25AB9C213FB47800354C27 /* UnityFramework */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9D25ABA6213FB47800354C27 /* Build configuration list for PBXNativeTarget "UnityFramework" */;
			buildPhases = (
				9D25AB9A213FB47800354C27 /* Headers */,
				9D25AB98213FB47800354C27 /* Sources */,
				9D25AB99213FB47800354C27 /* Frameworks */,
				9D25AB9B213FB47800354C27 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7F401A4D272AC77C005AF450 /* PBXTargetDependency */,
			);
			name = UnityFramework;
			productName = UnityFramework;
			productReference = 9D25AB9D213FB47800354C27 /* UnityFramework.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		29B97313FDCFA39411CA2CEA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				TargetAttributes = {
					1D6058900D05DD3D006BFB54 = {
						ProvisioningStyle = Manual;
						SystemCapabilities = {
							com.apple.GameControllers.appletvos = {
								enabled = 1;
							};
						};
						UnityMainTarget = 1;
					};
					5623C57217FDCB0800090B9E = {
						ProvisioningStyle = Manual;
						TestTargetID = 1D6058900D05DD3D006BFB54;
					};
					7F4E059F2717216D00A2CBE4 = {
						CreatedOnToolsVersion = 13.0;
						ProvisioningStyle = Automatic;
					};
					9D25AB9C213FB47800354C27 = {
						CreatedOnToolsVersion = 9.2;
						ProvisioningStyle = Automatic;
						UnityFrameworkTarget = 1;
					};
				};
			};
			buildConfigurationList = C01FCF4E08A954540054247B /* Build configuration list for PBXProject "Unity-iPhone" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
				en,
			);
			mainGroup = 29B97314FDCFA39411CA2CEA /* CustomTemplate */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1D6058900D05DD3D006BFB54 /* Unity-iPhone */,
				5623C57217FDCB0800090B9E /* Unity-iPhone Tests */,
				9D25AB9C213FB47800354C27 /* UnityFramework */,
				7F4E059F2717216D00A2CBE4 /* GameAssembly */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1D60588D0D05DD3D006BFB54 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9D9DE4EA221D84E60049D9A1 /* Data in Resources */,
				56C56C9817D6015200616839 /* Images.xcassets in Resources */,
				BEE3E21D322649C83B0DD4DC /* LaunchScreen-iPhone.storyboard in Resources */,
				F5C03E9DD3BF40D8EC0F4404 /* LaunchScreen-iPhonePortrait.png in Resources */,
				415674EFD2EC187355BC05B1 /* LaunchScreen-iPhoneLandscape.png in Resources */,
				763ED5D885EABF761B2FE262 /* LaunchScreen-iPad.storyboard in Resources */,
				D085C06443DB1B1B5C6637B6 /* LaunchScreen-iPad.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5623C57117FDCB0800090B9E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5623C57D17FDCB0900090B9E /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D25AB9B213FB47800354C27 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				91C1EB5F348833EAD02A7E9A /* PrivacyInfo.xcprivacy in Resources */,
				F9C34C618808157121DB9949 /* Data in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		C62A2A42F32E085EF849CF0B /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [ \"$CONFIGURATION\" = \"Debug\" ];\nthen\n    IL2CPP_CONFIG=\"Debug\"\nelse\n    IL2CPP_CONFIG=\"Release\"\nfi\n\nHOST_ARCH=$(uname -m)\nif [ \"$HOST_ARCH\" = \"arm64\" ];\nthen\n    HOST_ARCH_BEE=\"arm64\"\nelse\n    HOST_ARCH_BEE=\"x64\"\nfi\n\nif [ \"$ARCHS\" = \"arm64\" ];\nthen\n    LIB_ARCH=\"arm64\"\nelif [ \"$ARCHS\" = \"x86_64\" ];\nthen\n   LIB_ARCH=\"x64\"\nelse\n   LIB_ARCH=\"$HOST_ARCH_BEE\"\nfi\n\necho \"Lib arch: $LIB_ARCH\"\necho \"Host arch: $HOST_ARCH\"\necho \"Bee arch: $HOST_ARCH_BEE\"\n\nIL2CPP_DIR=\"$PROJECT_DIR/Il2CppOutputProject/IL2CPP/build/deploy_$HOST_ARCH\"\nIL2CPP=\"$IL2CPP_DIR/il2cpp\"\nchmod +x \"$IL2CPP\"\nchmod +x \"$IL2CPP_DIR/bee_backend/mac-$HOST_ARCH_BEE/bee_backend\"\n\n\"$IL2CPP\" --compile-cpp --platform=iOS --baselib-directory=\"$PROJECT_DIR/Libraries\" --additional-defines=IL2CPP_DEBUG=0 --incremental-g-c-time-slice=3 --dotnetprofile=unityaot-macos --enable-debugger --profiler-report --print-command-line --external-lib-il2-cpp=\"$PROJECT_DIR/Libraries/libil2cpp.a\" --generatedcppdir=\"Il2CppOutputProject/Source/il2cppOutput\" --architecture=\"$LIB_ARCH\" --outputpath=\"$CONFIGURATION_BUILD_DIR/libGameAssembly.a\" --cachedirectory=\"$CONFIGURATION_TEMP_DIR/artifacts/$LIB_ARCH\" --configuration=\"$IL2CPP_CONFIG\" \n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1D60588E0D05DD3D006BFB54 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9DA3B0442174CB96001678C7 /* main.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5623C56F17FDCB0800090B9E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5623C57F17FDCB0900090B9E /* Unity_iPhone_Tests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7F4E059C2717216D00A2CBE4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D25AB98213FB47800354C27 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8BEDFA3A29C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm in Sources */,
				862C245020AEC7AC006FB4AD /* UnityWebRequest.mm in Sources */,
				9D25ABE9213FB7CC00354C27 /* Il2CppOptions.cpp in Sources */,
				9D25ABC1213FB6F800354C27 /* CMVideoSampling.mm in Sources */,
				9D25ABB9213FB6E300354C27 /* UnityView+iOS.mm in Sources */,
				9D25ABAD213FB6B700354C27 /* AppDelegateListener.mm in Sources */,
				9D25ABBD213FB6E300354C27 /* UnityViewControllerBase+tvOS.mm in Sources */,
				9D25ABB1213FB6D600354C27 /* UnityView+Keyboard.mm in Sources */,
				9D25ABCB213FB6F800354C27 /* UnityReplayKit.mm in Sources */,
				9D25ABBA213FB6E300354C27 /* UnityView+tvOS.mm in Sources */,
				9D25ABBB213FB6E300354C27 /* UnityViewControllerBase.mm in Sources */,
				9D25ABCC213FB6F800354C27 /* UnityReplayKit_Scripting.mm in Sources */,
				9D25ABB7213FB6E300354C27 /* UnityAppController+ViewHandling.mm in Sources */,
				9D25ABC2213FB6F800354C27 /* CVTextureCache.mm in Sources */,
				9D25ABC4213FB6F800354C27 /* DisplayManager.mm in Sources */,
				9D25ABCD213FB6F800354C27 /* VideoPlayer.mm in Sources */,
				9D25ABC3213FB6F800354C27 /* DeviceSettings.mm in Sources */,
				9D25ABCA213FB6F800354C27 /* FullScreenVideoPlayer.mm in Sources */,
				9D25ABC8213FB6F800354C27 /* InternalProfiler.cpp in Sources */,
				9D25ABB6213FB6E300354C27 /* StoreReview.m in Sources */,
				9D25ABB4213FB6E300354C27 /* OrientationSupport.mm in Sources */,
				9D25ABAF213FB6BE00354C27 /* RenderPluginDelegate.mm in Sources */,
				9D25ABE3213FB76500354C27 /* UnityAppController.mm in Sources */,
				9D25ABBF213FB6F800354C27 /* AVCapture.mm in Sources */,
				8A215703245064AA00E582EB /* NoGraphicsHelper.mm in Sources */,
				9D25ABE1213FB76500354C27 /* iPhone_Sensors.mm in Sources */,
				9D25ABC6213FB6F800354C27 /* Filesystem.mm in Sources */,
				9D25ABB8213FB6E300354C27 /* UnityView.mm in Sources */,
				9DFA7F9D21410F2E00C2880E /* main.mm in Sources */,
				9D25ABBE213FB6F800354C27 /* OnDemandResources.mm in Sources */,
				9D25ABE4213FB76500354C27 /* UnityAppController+Rendering.mm in Sources */,
				9D25ABB3213FB6E300354C27 /* Keyboard.mm in Sources */,
				9D25ABE0213FB76500354C27 /* CrashReporter.mm in Sources */,
				9D25ABE6213FB7C100354C27 /* RegisterFeatures.cpp in Sources */,
				9D25ABBC213FB6E300354C27 /* UnityViewControllerBase+iOS.mm in Sources */,
				9D25ABE5213FB76500354C27 /* UnityAppController+UnityInterface.mm in Sources */,
				9D25ABC0213FB6F800354C27 /* CameraCapture.mm in Sources */,
				9D25ABB0213FB6C400354C27 /* UnityViewControllerListener.mm in Sources */,
				9D25ABAE213FB6BA00354C27 /* LifeCycleListener.mm in Sources */,
				9D25ABB2213FB6E300354C27 /* ActivityIndicator.mm in Sources */,
				9D25ABC9213FB6F800354C27 /* MetalHelper.mm in Sources */,
				2DBCA64A86BE8DEE679DD35A /* IngameDebugConsole.mm in Sources */,
				178C9ABCFE5BB1DE2BD16D2D /* NativeCallProxy.mm in Sources */,
				A0EBFD3072F58D940550863B /* PGUnityPlugin.mm in Sources */,
				9BA70426A90DB33FFDF35A71 /* lib_burst_generated.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		5623C58217FDCB0900090B9E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1D6058900D05DD3D006BFB54 /* Unity-iPhone */;
			targetProxy = 5623C58117FDCB0900090B9E /* PBXContainerItemProxy */;
		};
		7F401A4D272AC77C005AF450 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7F4E059F2717216D00A2CBE4 /* GameAssembly */;
			targetProxy = 7F401A4C272AC77C005AF450 /* PBXContainerItemProxy */;
		};
		9D25ABA3213FB47800354C27 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9D25AB9C213FB47800354C27 /* UnityFramework */;
			targetProxy = 9D25ABA2213FB47800354C27 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		5623C57B17FDCB0900090B9E /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				5623C57C17FDCB0900090B9E /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		1D6058940D05DD3E006BFB54 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = "";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				INFOPLIST_FILE = Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"$(OTHER_CFLAGS)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.DefaultCompany.Sugoi-Retouch";
				PRODUCT_NAME = "$(PRODUCT_NAME_APP)";
				PROVISIONING_PROFILE = "$(PROVISIONING_PROFILE_APP)";
				PROVISIONING_PROFILE_APP = "";
				PROVISIONING_PROFILE_SPECIFIER = "$(PROVISIONING_PROFILE_SPECIFIER_APP)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
			};
			name = Debug;
		};
		1D6058950D05DD3E006BFB54 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				INFOPLIST_FILE = Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"$(OTHER_CFLAGS)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.DefaultCompany.Sugoi-Retouch";
				PRODUCT_NAME = "$(PRODUCT_NAME_APP)";
				PROVISIONING_PROFILE = "$(PROVISIONING_PROFILE_APP)";
				PROVISIONING_PROFILE_APP = "";
				PROVISIONING_PROFILE_SPECIFIER = "$(PROVISIONING_PROFILE_SPECIFIER_APP)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
			};
			name = Release;
		};
		5623C58317FDCB0900090B9E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Unity-iPhone Tests/Unity-iPhone Tests-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				INFOPLIST_FILE = "Unity-iPhone Tests/Unity-iPhone Tests-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.unity3d.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/$(PRODUCT_NAME_APP).app/$(PRODUCT_NAME_APP)";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = xctest;
			};
			name = Release;
		};
		5623C58417FDCB0900090B9E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Unity-iPhone Tests/Unity-iPhone Tests-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				INFOPLIST_FILE = "Unity-iPhone Tests/Unity-iPhone Tests-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.unity3d.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/$(PRODUCT_NAME_APP).app/$(PRODUCT_NAME_APP)";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WRAPPER_EXTENSION = xctest;
			};
			name = Debug;
		};
		56E860801D6757FF00A1AB2B /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_ENABLE_CPP_RTTI = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_THUMB_SUPPORT = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				PRODUCT_NAME_APP = "Sugoi-Retouch";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
			};
			name = ReleaseForRunning;
		};
		56E860811D6757FF00A1AB2B /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = "";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				INFOPLIST_FILE = Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"$(OTHER_CFLAGS)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.DefaultCompany.Sugoi-Retouch";
				PRODUCT_NAME = "$(PRODUCT_NAME_APP)";
				PROVISIONING_PROFILE = "$(PROVISIONING_PROFILE_APP)";
				PROVISIONING_PROFILE_APP = "";
				PROVISIONING_PROFILE_SPECIFIER = "$(PROVISIONING_PROFILE_SPECIFIER_APP)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
			};
			name = ReleaseForRunning;
		};
		56E860821D6757FF00A1AB2B /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Unity-iPhone Tests/Unity-iPhone Tests-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				INFOPLIST_FILE = "Unity-iPhone Tests/Unity-iPhone Tests-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.unity3d.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/$(PRODUCT_NAME_APP).app/$(PRODUCT_NAME_APP)";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = xctest;
			};
			name = ReleaseForRunning;
		};
		56E860831D67581C00A1AB2B /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_ENABLE_CPP_RTTI = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_THUMB_SUPPORT = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				PRODUCT_NAME_APP = "Sugoi-Retouch";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
			};
			name = ReleaseForProfiling;
		};
		56E860841D67581C00A1AB2B /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				INFOPLIST_FILE = Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"$(OTHER_CFLAGS)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.DefaultCompany.Sugoi-Retouch";
				PRODUCT_NAME = "$(PRODUCT_NAME_APP)";
				PROVISIONING_PROFILE = "$(PROVISIONING_PROFILE_APP)";
				PROVISIONING_PROFILE_APP = "";
				PROVISIONING_PROFILE_SPECIFIER = "$(PROVISIONING_PROFILE_SPECIFIER_APP)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
			};
			name = ReleaseForProfiling;
		};
		56E860851D67581C00A1AB2B /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Unity-iPhone Tests/Unity-iPhone Tests-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				INFOPLIST_FILE = "Unity-iPhone Tests/Unity-iPhone Tests-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.unity3d.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/$(PRODUCT_NAME_APP).app/$(PRODUCT_NAME_APP)";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = xctest;
			};
			name = ReleaseForProfiling;
		};
		7F4E05A62717216D00A2CBE4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7F4E05A72717216D00A2CBE4 /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseForProfiling;
		};
		7F4E05A82717216D00A2CBE4 /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseForRunning;
		};
		7F4E05A92717216D00A2CBE4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		9D25ABA7213FB47800354C27 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = 1.0;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Classes/Prefix.pch;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Classes",
					"$(SRCROOT)",
					"$(SRCROOT)/Classes/Native",
					"$(SRCROOT)/Libraries/bdwgc/include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/pch",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include",
					"$(SRCROOT)/Libraries/external/baselib/Platforms/IOS/Include",
				);
				INFOPLIST_FILE = UnityFramework/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Libraries",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fno-strict-overflow",
					"-DUNITY_VERSION_VER=2022",
					"-DUNITY_VERSION_MAJ=3",
					"-DUNITY_VERSION_MIN=55",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-weak_framework",
					CoreMotion,
					"-weak-lSystem",
					"$(OTHER_LDFLAGS_FRAMEWORK)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.unity3d.framework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WARNING_CFLAGS = "-Wno-missing-declarations";
			};
			name = Release;
		};
		9D25ABA8213FB47800354C27 /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = 1.0;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Classes/Prefix.pch;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Classes",
					"$(SRCROOT)",
					"$(SRCROOT)/Classes/Native",
					"$(SRCROOT)/Libraries/bdwgc/include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/pch",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include",
					"$(SRCROOT)/Libraries/external/baselib/Platforms/IOS/Include",
				);
				INFOPLIST_FILE = UnityFramework/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Libraries",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fno-strict-overflow",
					"-DUNITY_VERSION_VER=2022",
					"-DUNITY_VERSION_MAJ=3",
					"-DUNITY_VERSION_MIN=55",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-weak_framework",
					CoreMotion,
					"-weak-lSystem",
					"$(OTHER_LDFLAGS_FRAMEWORK)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.unity3d.framework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WARNING_CFLAGS = "-Wno-missing-declarations";
			};
			name = ReleaseForProfiling;
		};
		9D25ABA9213FB47800354C27 /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = 1.0;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Classes/Prefix.pch;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Classes",
					"$(SRCROOT)",
					"$(SRCROOT)/Classes/Native",
					"$(SRCROOT)/Libraries/bdwgc/include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/pch",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include",
					"$(SRCROOT)/Libraries/external/baselib/Platforms/IOS/Include",
				);
				INFOPLIST_FILE = UnityFramework/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Libraries",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fno-strict-overflow",
					"-DUNITY_VERSION_VER=2022",
					"-DUNITY_VERSION_MAJ=3",
					"-DUNITY_VERSION_MIN=55",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-weak_framework",
					CoreMotion,
					"-weak-lSystem",
					"$(OTHER_LDFLAGS_FRAMEWORK)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.unity3d.framework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WARNING_CFLAGS = "-Wno-missing-declarations";
			};
			name = ReleaseForRunning;
		};
		9D25ABAA213FB47800354C27 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1.0;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Classes/Prefix.pch;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Classes",
					"$(SRCROOT)",
					"$(SRCROOT)/Classes/Native",
					"$(SRCROOT)/Libraries/bdwgc/include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/pch",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include",
					"$(SRCROOT)/Libraries/external/baselib/Platforms/IOS/Include",
				);
				INFOPLIST_FILE = UnityFramework/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Libraries",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fno-strict-overflow",
					"-DUNITY_VERSION_VER=2022",
					"-DUNITY_VERSION_MAJ=3",
					"-DUNITY_VERSION_MIN=55",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-weak_framework",
					CoreMotion,
					"-weak-lSystem",
					"$(OTHER_LDFLAGS_FRAMEWORK)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.unity3d.framework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.55f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WARNING_CFLAGS = "-Wno-missing-declarations";
			};
			name = Debug;
		};
		C01FCF4F08A954540054247B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_ENABLE_CPP_RTTI = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_THUMB_SUPPORT = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME_APP = "Sugoi-Retouch";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
			};
			name = Debug;
		};
		C01FCF5008A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_ENABLE_CPP_RTTI = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_THUMB_SUPPORT = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				PRODUCT_NAME_APP = "Sugoi-Retouch";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "Unity-iPhone" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1D6058950D05DD3E006BFB54 /* Release */,
				56E860841D67581C00A1AB2B /* ReleaseForProfiling */,
				56E860811D6757FF00A1AB2B /* ReleaseForRunning */,
				1D6058940D05DD3E006BFB54 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5623C58517FDCB0900090B9E /* Build configuration list for PBXNativeTarget "Unity-iPhone Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5623C58317FDCB0900090B9E /* Release */,
				56E860851D67581C00A1AB2B /* ReleaseForProfiling */,
				56E860821D6757FF00A1AB2B /* ReleaseForRunning */,
				5623C58417FDCB0900090B9E /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7F4E05AA2717216D00A2CBE4 /* Build configuration list for PBXNativeTarget "GameAssembly" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7F4E05A62717216D00A2CBE4 /* Release */,
				7F4E05A72717216D00A2CBE4 /* ReleaseForProfiling */,
				7F4E05A82717216D00A2CBE4 /* ReleaseForRunning */,
				7F4E05A92717216D00A2CBE4 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9D25ABA6213FB47800354C27 /* Build configuration list for PBXNativeTarget "UnityFramework" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9D25ABA7213FB47800354C27 /* Release */,
				9D25ABA8213FB47800354C27 /* ReleaseForProfiling */,
				9D25ABA9213FB47800354C27 /* ReleaseForRunning */,
				9D25ABAA213FB47800354C27 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C01FCF4E08A954540054247B /* Build configuration list for PBXProject "Unity-iPhone" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF5008A954540054247B /* Release */,
				56E860831D67581C00A1AB2B /* ReleaseForProfiling */,
				56E860801D6757FF00A1AB2B /* ReleaseForRunning */,
				C01FCF4F08A954540054247B /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 29B97313FDCFA39411CA2CEA /* Project object */;
}
