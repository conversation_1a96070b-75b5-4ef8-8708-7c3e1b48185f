//
//  UnityPluginDelegate.h
//  UnityFramework
//
//  Created by MIG-Ultra on 2023/4/7.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
 接口示意参见文档，若有差异，以文档为准:
 https://pinguo.pingcode.com/wiki/spaces/MIG/pages/63f8622c80a234fa666bf6c0
 */


struct PGUVec3 {
    float x;
    float y;
    float z;
};

struct PGUVec2 {
    float x;
    float y;
};

//视频跳帧完成回调
typedef void (*PGNativeVideoDecoderSeekFrameCompletedHandle)(void* context,bool result);
typedef char* (*CalCurveValueByTimeFunc)(char* curve,char* valueType, float time);
typedef char* (*CombineCurveFunc)(char* curveListJson);
typedef char* (*ConvertPointsToCurveFunc)(char* curveListJson);
typedef char* (*ConvertCubeToLocalLut64Func)(char* cubeFilePath,char* savePath);
typedef char* (*CreateBounceCurveFunc)(char* mode,float startX,float startY,float endX,float endY,float a,float b, float smooth, bool clamp);
typedef char* (*GetTextSizeFunc)(char* textParameters);
typedef char* (*ConvertAeCurveToLocalCurveFunc)(char* curveList);
typedef void  (*PGMessageEntry)(char* msg);

// 数据迁移相关
typedef int (*PickEffectTypeFunc)(char *dataJsonPath);
typedef char *(*CurveTransferFunc)(char *parameterJson, char *effectDataJsonPath, char *textureSizeStr);
typedef int (*BlendModeTransferFunc)(int blendMode);
typedef int (*DataTypeEnumTransferFunc)(int paramType);

//坐标系转换

typedef struct PGUVec3 (*PGCoordinateConverterScreenToLocalHandle)(int converterID, struct PGUVec2 point);
typedef struct PGUVec2 (*PGCoordinateConverterLocalToScreenHandle)(int converterID, struct PGUVec3 point);


@protocol PGUnityPluginDelegate <NSObject>

@optional

- (char *)getRenderLayerJsonData:(char *)guid layerType:(int)layerType;
- (void)nativeVideoDecoderSeek:(char *)videoPath time:(double)time texture:(void *)texture textureWidth:(int)textureWidth textureHeight:(int)textureHeight context:(void *)context completed:(PGNativeVideoDecoderSeekFrameCompletedHandle)completed;
- (void)nativeVideoDecoderCleanup:(char *)videoPath;
- (void)onEngineInitialized;
- (void)onEngineError:(char *)errorMsg;
- (void)onMethodCompleted:(char *)completed result:(char *)result hasException:(bool)hasException;
- (void)onTextureRenderEnd:(void *)texture;
- (void)onTextureExported:(void *)bufferPtr textureWidth:(int)textureWidth textureHeight:(int)textureHeight bufferLength:(int)byteLength colorFormat:(int)colorFormat timestamp:(double)timestamp;
- (void)onParameterObserverCallback:(char *)parasJson;
- (void)onLayerBoundingObserverCallback:(char *)parasJson;
- (void)onCameraFrameObserverCallback:(char *)parasJson;
- (char *)getVideoSourceDescription:(char *)effectID param:(char*)param fileValue:(char*)fileValue;
- (void)onPGAnimationNativeHelperFunc:(CalCurveValueByTimeFunc) calTimeFunc combineListJson:(CombineCurveFunc)combineListJson curveListJson:(ConvertPointsToCurveFunc) curveListJson  bounceFunc:(CreateBounceCurveFunc) bounceFunc  aeCurveToLocalCurveFunc:(ConvertAeCurveToLocalCurveFunc) aeCurveToLocalCurveFunc;
- (void)onPGCubeHelperFunc:(ConvertCubeToLocalLut64Func)calTimeFunc;
- (void)onPGTextNativeHelperFunc:(GetTextSizeFunc) callBack;
- (void)onPGMessageHandlerInitialized:(PGMessageEntry) messageEntry;
- (void)onPGCoordinateConverterManagerInitialized:(PGCoordinateConverterScreenToLocalHandle) screenToLocalHandle localToScreenHandle:(PGCoordinateConverterLocalToScreenHandle) localToScreenHandle;

// 粒子形状接口
- (void)onShapeDataChanged:(char *)methodName result:(char *)result;

// 数据迁移接口
- (void)onPGBlurrrDataTransferHelperFunc:(PickEffectTypeFunc)pickerFunc curveFunc:(CurveTransferFunc)curveFunc blendFunc:(BlendModeTransferFunc)blendFunc dataEnumFunc:(DataTypeEnumTransferFunc)dataEnumFunc;


- (void)syncWorkspace:(char *)workspaceJson;
@end

NS_ASSUME_NONNULL_END

