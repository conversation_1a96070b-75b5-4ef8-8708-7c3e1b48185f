//
//  MyPlugin.m
//  UnityFramework
//
//  Created by MIG-Ultra on 2023/4/7.
//

#import "PGUnityPlugin.h"
#import "IUnityInterface.h"
#import "IUnityGraphics.h"

@implementation PGUnityPlugin

// 对外的委托
static __weak id<PGUnityPluginDelegate> _Nullable _unityDelegate;

+ (id<PGUnityPluginDelegate>)unityDelegate
{
    return _unityDelegate;
}

+ (void)setUnityDelegate:(id<PGUnityPluginDelegate>)unityDelegate
{
    _unityDelegate = unityDelegate;
}

@end

extern "C" {

char* GetRenderLayerJsonData(char* guid, int layerType)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(getRenderLayerJsonData:layerType:)]) {
        return [PGUnityPlugin.unityDelegate getRenderLayerJsonData:guid layerType:layerType];
    }
    return 0;
}

char* GetVideoSourceDescription(char* effectID,char* param,char* fileValue)
{  
     if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(getVideoSourceDescription:param:fileValue:)]) {
       
         return [PGUnityPlugin.unityDelegate getVideoSourceDescription:effectID param:param fileValue:fileValue];
     } 
     return 0;
}

void NativeVideoDecoderSeek(char* videoPath,double time,void* texture,int textureWidth, int textureHeight,void* context,PGNativeVideoDecoderSeekFrameCompletedHandle completed)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(nativeVideoDecoderSeek:time:texture:textureWidth:textureHeight:context:completed:)]) {
        [PGUnityPlugin.unityDelegate nativeVideoDecoderSeek:videoPath time:time texture:texture textureWidth:textureWidth textureHeight:textureHeight context:context completed:completed];
    }
}

void NativeVideoDecoderCleanup(char* videoPath){
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(nativeVideoDecoderCleanup:)]) {
        [PGUnityPlugin.unityDelegate nativeVideoDecoderCleanup:videoPath];
    }
}

void OnEngineInitialized()
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onEngineInitialized)]) {
        [PGUnityPlugin.unityDelegate onEngineInitialized];
    }
}

void OnEngineError(char* errorMsg)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onEngineError:)]) {
        [PGUnityPlugin.unityDelegate onEngineError:errorMsg];
    }
}

void OnMethodCompleted(char* guid, char* result,bool hasException)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onMethodCompleted:result:hasException:)]) {
        [PGUnityPlugin.unityDelegate onMethodCompleted:guid result:result hasException:hasException];
    }
}

void OnParameterObserverCallback(char* parasJson)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onParameterObserverCallback:)]) {
        [PGUnityPlugin.unityDelegate onParameterObserverCallback:parasJson];
    }
}

void OnLayerBoundingObserverCallback(char* parasJson)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onLayerBoundingObserverCallback:)]) {
        [PGUnityPlugin.unityDelegate onLayerBoundingObserverCallback:parasJson];
    }
}

void OnCameraFrameObserverCallback(char* parasJson)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onCameraFrameObserverCallback:)]) {
        [PGUnityPlugin.unityDelegate onCameraFrameObserverCallback:parasJson];
    }
}
 
void OnTextureExported(void* bytePtr,int tWidth,int tHeight, int byteLength, int colorFormat, double timestamp)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onTextureExported:textureWidth:textureHeight:bufferLength:colorFormat:timestamp:)]) {
            [PGUnityPlugin.unityDelegate onTextureExported:bytePtr textureWidth:tWidth textureHeight:tHeight bufferLength:byteLength colorFormat:colorFormat timestamp:timestamp];
        }
}

void UNITY_INTERFACE_API OnTextureRenderEnd(int eventID,void* texture)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onTextureRenderEnd:)]) {
            [PGUnityPlugin.unityDelegate onTextureRenderEnd:texture];
       }
}

UnityRenderingEventAndData GetOnTextureRenderEndFuncPtr()
{
    return OnTextureRenderEnd;
}


//本地消息入口函数注册
void OnPGMessageHandlerInitialized(PGMessageEntry entry)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onPGMessageHandlerInitialized:)]) {
            [PGUnityPlugin.unityDelegate onPGMessageHandlerInitialized:entry];
        }
}

void OnPGCoordinateConverterManagerInitialized(PGCoordinateConverterScreenToLocalHandle screenToLocalHandle,PGCoordinateConverterLocalToScreenHandle localToScreenHandle)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onPGCoordinateConverterManagerInitialized:localToScreenHandle:)]) {
        [PGUnityPlugin.unityDelegate onPGCoordinateConverterManagerInitialized:screenToLocalHandle localToScreenHandle:localToScreenHandle];
    }
}

#pragma mark 以下为AE项目需要的曲线部分，特殊逻辑 
// 将三个方法做缓存
void PGAnimationNativeHelperFunc(CalCurveValueByTimeFunc calTimeFunc, CombineCurveFunc combineListJson,ConvertPointsToCurveFunc curveListJson, CreateBounceCurveFunc bounceFunc, ConvertAeCurveToLocalCurveFunc aeCurveToLocalCurveFunc)
{ 
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onPGAnimationNativeHelperFunc:combineListJson:curveListJson:bounceFunc:aeCurveToLocalCurveFunc:)]) {
            [PGUnityPlugin.unityDelegate onPGAnimationNativeHelperFunc:calTimeFunc combineListJson:combineListJson curveListJson:curveListJson bounceFunc:bounceFunc aeCurveToLocalCurveFunc:aeCurveToLocalCurveFunc];
        }
}

#pragma mark 以下为AE项目Cube转png lut64部分，特殊逻辑 
// 将一个方法做缓存
void PGCubeFileHelperFunc(ConvertCubeToLocalLut64Func cubeToLutFileFunc)
{ 
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onPGCubeHelperFunc:)]) {
            [PGUnityPlugin.unityDelegate onPGCubeHelperFunc:cubeToLutFileFunc];
    }
}

#pragma mark 以下为AE项目需要的文字部分，特殊逻辑 
// 将三个方法做缓存
void PGTextNativeHelperFunc(GetTextSizeFunc callBack)
{ 
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onPGTextNativeHelperFunc:)]) {
            [PGUnityPlugin.unityDelegate onPGTextNativeHelperFunc:callBack ];
        }
}

#pragma mark 以下为项目迁移可能用到的帮助方法
void PGBlurrrDataTransferHelperFunc(PickEffectTypeFunc pickerFunc, CurveTransferFunc curveFunc, BlendModeTransferFunc blendFunc, DataTypeEnumTransferFunc dataEnumFunc)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onPGBlurrrDataTransferHelperFunc:curveFunc:blendFunc:blendFunc:)]) {
            [PGUnityPlugin.unityDelegate onPGBlurrrDataTransferHelperFunc:pickerFunc curveFunc:curveFunc blendFunc:blendFunc dataEnumFunc:dataEnumFunc];
        }
}

#pragma mark 粒子形状点位
void OnShapeDataChanged(char* methodName, char* result)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(onShapeDataChanged:result:)]) {
        [PGUnityPlugin.unityDelegate onShapeDataChanged:methodName result:result];
    }
}

#pragma mark 为工程编译成功临时实现以下接口，后续将会逐步删除。

void SyncWorkspace(char* workspaceJson)
{
    if ([PGUnityPlugin.unityDelegate respondsToSelector:@selector(syncWorkspace:)]) {
        [PGUnityPlugin.unityDelegate syncWorkspace:workspaceJson];
    }
}

void GetEditLiteMainResource(){}

void GetNativeFaceDataV2(){}

void NativeBodyPosture(){}

void NativeCornerDetect(){}

void NativeEntityRecognize(){}

void NativeFaceDetect(){}

void NativePersonMasking(){}

void NativeSegmentCommon(){}
 
void NativeSegmentHair(){}

void NativeSegmentSimilar(){}

void NativeSkySegmentation(){}

void NativeVideoDecoderInit(){}

void NativeVideoDecoderSeekFrame(){}

void OnDeepFaceRefineStart(){}

void OnDeepRetouchStart(){}

void OnEditLiteComponentMaskChanged(){}

void OnEditLiteFinalTexRequest(){}

void OnEditLiteMagnifierChanged(){}

void OnEditLitePrepareSuccess(){}

void OnEditLiteScreenColorAtPointRequest(){}

void OnEditLiteViewTransformChanged(){}

void OnEditLiteWillLoad(){}

void OnSpotRemove(){}

void PGBridgeTextureManagerInit(){}

void RepaintTransformObjectComplete(){}

void ReportLoadUnicodeFailInfo(){}

void ReportMagicEffectCapabilityStatus(){}

void ReportSelectTextItemInfos(){}

void ReportSelectTransformObjectInfo(){}

void ReportSkyChangeRenderComplete(){}

void ReportSkyChangeTransformPrepare(){}

void ReportTextBGNormalizeSize(){}

void ReportTextPrepareInfos(){}

void ReportTextboxInfos(){}

void onOralStart(){}

}
