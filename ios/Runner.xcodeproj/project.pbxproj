// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0234C48D1D2F89461C855093 /* Pods_RunnerTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8B6792BF68EE694F5F847EEA /* Pods_RunnerTests.framework */; };
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 331C807B294A618700263BE5 /* RunnerTests.swift */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		3C73AE832D18FB0400A26999 /* UnityEngineController.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C73AE822D18FB0400A26999 /* UnityEngineController.m */; };
		3C73AE872D19048F00A26999 /* UnityDispatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C73AE862D19048F00A26999 /* UnityDispatcher.m */; };
		3C73AE892D19091200A26999 /* AppDelegate+Unity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C73AE882D19091200A26999 /* AppDelegate+Unity.swift */; };
		3C73AE982D194BD600A26999 /* UnityEngineMessageSenderImpl.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C73AE962D194BD600A26999 /* UnityEngineMessageSenderImpl.m */; };
		3CBC53102D117ECE009174D6 /* FlutterMethodAbnormal.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CBC53052D117ECE009174D6 /* FlutterMethodAbnormal.swift */; };
		3CBC53112D117ECE009174D6 /* UnityMethodResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CBC53082D117ECE009174D6 /* UnityMethodResponder.swift */; };
		3CBC53122D117ECE009174D6 /* FlutterMethodApprover.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CBC53062D117ECE009174D6 /* FlutterMethodApprover.swift */; };
		3CBC53132D117ECE009174D6 /* FlutterMessageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CBC53042D117ECE009174D6 /* FlutterMessageManager.swift */; };
		3CBC53152D117ECE009174D6 /* FlutterMessageHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CBC53032D117ECE009174D6 /* FlutterMessageHandler.swift */; };
		3CBC53162D117ECE009174D6 /* UnityMessageHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CBC53072D117ECE009174D6 /* UnityMessageHandler.swift */; };
		3CBC53182D117EDB009174D6 /* AppDelegate+Flutter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CBC53172D117EDB009174D6 /* AppDelegate+Flutter.swift */; };
		3CF45FEF2D1A944A00953531 /* FlutterMessageSender.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CF45FEE2D1A944A00953531 /* FlutterMessageSender.swift */; };
		3CF45FF12D1A953F00953531 /* UnityMessageSender.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CF45FF02D1A953F00953531 /* UnityMessageSender.swift */; };
		3CF45FF42D1AA0B600953531 /* UnityMessage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CF45FF22D1AA0B600953531 /* UnityMessage.swift */; };
		3CF45FF52D1AA0B600953531 /* UnityMessageType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CF45FF32D1AA0B600953531 /* UnityMessageType.swift */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		AA47F97515E5D8B4E6C788B0 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EF69CDB8F638B59D5D32D003 /* Pods_Runner.framework */; };
		C26029242D15558400174DC3 /* UnityFramework.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C26029232D15558400174DC3 /* UnityFramework.framework */; };
		C26029252D15558400174DC3 /* UnityFramework.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = C26029232D15558400174DC3 /* UnityFramework.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		D03EF6EE2D19657B008D4585 /* PGNetworkSignPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = D03EF6ED2D19657B008D4585 /* PGNetworkSignPlugin.swift */; };
		D03EF7062D1D0055008D4585 /* pgnetworksign.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D03EF7042D1D004F008D4585 /* pgnetworksign.framework */; };
		D03EF7072D1D0055008D4585 /* pgnetworksign.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = D03EF7042D1D004F008D4585 /* pgnetworksign.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		D06B17892D2F5DB000E0C024 /* PGScreenshotPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = D06B17882D2F5DB000E0C024 /* PGScreenshotPlugin.swift */; };
		D06B178B2D2F5DD300E0C024 /* PGScreenshotView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D06B178A2D2F5DD300E0C024 /* PGScreenshotView.swift */; };
		D06B178F2D2FAB6100E0C024 /* PGSecureRootViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = D06B178E2D2FAB6100E0C024 /* PGSecureRootViewController.swift */; };
		D06B17912D2FC8F900E0C024 /* PGScreenShotManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = D06B17902D2FC8F900E0C024 /* PGScreenShotManager.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		331C8085294A63A400263BE5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97C146ED1CF9000F007C117D;
			remoteInfo = Runner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				C26029252D15558400174DC3 /* UnityFramework.framework in Embed Frameworks */,
				D03EF7072D1D0055008D4585 /* pgnetworksign.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		331C807B294A618700263BE5 /* RunnerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunnerTests.swift; sourceTree = "<group>"; };
		331C8081294A63A400263BE5 /* RunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3C6176BA2D1951160019F74C /* UnityEngineMessageHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityEngineMessageHandler.h; sourceTree = "<group>"; };
		3C73AE812D18FB0400A26999 /* UnityEngineController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityEngineController.h; sourceTree = "<group>"; };
		3C73AE822D18FB0400A26999 /* UnityEngineController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UnityEngineController.m; sourceTree = "<group>"; };
		3C73AE852D19048F00A26999 /* UnityDispatcher.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityDispatcher.h; sourceTree = "<group>"; };
		3C73AE862D19048F00A26999 /* UnityDispatcher.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UnityDispatcher.m; sourceTree = "<group>"; };
		3C73AE882D19091200A26999 /* AppDelegate+Unity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+Unity.swift"; sourceTree = "<group>"; };
		3C73AE942D194BD600A26999 /* UnityEngineMessageSender.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityEngineMessageSender.h; sourceTree = "<group>"; };
		3C73AE952D194BD600A26999 /* UnityEngineMessageSenderImpl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityEngineMessageSenderImpl.h; sourceTree = "<group>"; };
		3C73AE962D194BD600A26999 /* UnityEngineMessageSenderImpl.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UnityEngineMessageSenderImpl.m; sourceTree = "<group>"; };
		3CBC53032D117ECE009174D6 /* FlutterMessageHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlutterMessageHandler.swift; sourceTree = "<group>"; };
		3CBC53042D117ECE009174D6 /* FlutterMessageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlutterMessageManager.swift; sourceTree = "<group>"; };
		3CBC53052D117ECE009174D6 /* FlutterMethodAbnormal.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlutterMethodAbnormal.swift; sourceTree = "<group>"; };
		3CBC53062D117ECE009174D6 /* FlutterMethodApprover.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlutterMethodApprover.swift; sourceTree = "<group>"; };
		3CBC53072D117ECE009174D6 /* UnityMessageHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UnityMessageHandler.swift; sourceTree = "<group>"; };
		3CBC53082D117ECE009174D6 /* UnityMethodResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UnityMethodResponder.swift; sourceTree = "<group>"; };
		3CBC530E2D117ECE009174D6 /* UnityInterface.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityInterface.h; sourceTree = "<group>"; };
		3CBC53172D117EDB009174D6 /* AppDelegate+Flutter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+Flutter.swift"; sourceTree = "<group>"; };
		3CF45FEE2D1A944A00953531 /* FlutterMessageSender.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlutterMessageSender.swift; sourceTree = "<group>"; };
		3CF45FF02D1A953F00953531 /* UnityMessageSender.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UnityMessageSender.swift; sourceTree = "<group>"; };
		3CF45FF22D1AA0B600953531 /* UnityMessage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UnityMessage.swift; sourceTree = "<group>"; };
		3CF45FF32D1AA0B600953531 /* UnityMessageType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UnityMessageType.swift; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7786E88C264E45924E35AEA3 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		8B6792BF68EE694F5F847EEA /* Pods_RunnerTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		988E96DF0E69CED3E1DB8F18 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		9925CB3864537C87ED4DC540 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		C26029232D15558400174DC3 /* UnityFramework.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = UnityFramework.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D03EF6ED2D19657B008D4585 /* PGNetworkSignPlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PGNetworkSignPlugin.swift; sourceTree = "<group>"; };
		D03EF7042D1D004F008D4585 /* pgnetworksign.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = pgnetworksign.framework; sourceTree = "<group>"; };
		D06B17882D2F5DB000E0C024 /* PGScreenshotPlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PGScreenshotPlugin.swift; sourceTree = "<group>"; };
		D06B178A2D2F5DD300E0C024 /* PGScreenshotView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PGScreenshotView.swift; sourceTree = "<group>"; };
		D06B178E2D2FAB6100E0C024 /* PGSecureRootViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PGSecureRootViewController.swift; sourceTree = "<group>"; };
		D06B17902D2FC8F900E0C024 /* PGScreenShotManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PGScreenShotManager.swift; sourceTree = "<group>"; };
		D663208B82B94C6D46D171A7 /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		DFF4465C47F65778737862AC /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		E9DD24A3EA0BC0CFD58A62EF /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		EF69CDB8F638B59D5D32D003 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		330FD93F0DBE9B2CC2C022B3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0234C48D1D2F89461C855093 /* Pods_RunnerTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D03EF7062D1D0055008D4585 /* pgnetworksign.framework in Frameworks */,
				AA47F97515E5D8B4E6C788B0 /* Pods_Runner.framework in Frameworks */,
				C26029242D15558400174DC3 /* UnityFramework.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		011842E5AE399483BC36DCFD /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C26029232D15558400174DC3 /* UnityFramework.framework */,
				EF69CDB8F638B59D5D32D003 /* Pods_Runner.framework */,
				8B6792BF68EE694F5F847EEA /* Pods_RunnerTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		331C8082294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C807B294A618700263BE5 /* RunnerTests.swift */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		3C73AE802D18FADE00A26999 /* Controller */ = {
			isa = PBXGroup;
			children = (
				3C73AE812D18FB0400A26999 /* UnityEngineController.h */,
				3C73AE822D18FB0400A26999 /* UnityEngineController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		3C73AE842D19044600A26999 /* Dispatcher */ = {
			isa = PBXGroup;
			children = (
				3C73AE852D19048F00A26999 /* UnityDispatcher.h */,
				3C73AE862D19048F00A26999 /* UnityDispatcher.m */,
			);
			path = Dispatcher;
			sourceTree = "<group>";
		};
		3C73AE8A2D190C2800A26999 /* MessageSender */ = {
			isa = PBXGroup;
			children = (
				3CF45FEE2D1A944A00953531 /* FlutterMessageSender.swift */,
				3CF45FF02D1A953F00953531 /* UnityMessageSender.swift */,
				3CF45FF22D1AA0B600953531 /* UnityMessage.swift */,
				3CF45FF32D1AA0B600953531 /* UnityMessageType.swift */,
			);
			path = MessageSender;
			sourceTree = "<group>";
		};
		3C73AE8F2D19308900A26999 /* UnityEngineMessageHandler */ = {
			isa = PBXGroup;
			children = (
				3C6176BA2D1951160019F74C /* UnityEngineMessageHandler.h */,
			);
			path = UnityEngineMessageHandler;
			sourceTree = "<group>";
		};
		3C73AE972D194BD600A26999 /* UnityEngineMessageSender */ = {
			isa = PBXGroup;
			children = (
				3C73AE942D194BD600A26999 /* UnityEngineMessageSender.h */,
				3C73AE952D194BD600A26999 /* UnityEngineMessageSenderImpl.h */,
				3C73AE962D194BD600A26999 /* UnityEngineMessageSenderImpl.m */,
			);
			path = UnityEngineMessageSender;
			sourceTree = "<group>";
		};
		3CBC53092D117ECE009174D6 /* MessageHanlder */ = {
			isa = PBXGroup;
			children = (
				3CBC53032D117ECE009174D6 /* FlutterMessageHandler.swift */,
				3CBC53052D117ECE009174D6 /* FlutterMethodAbnormal.swift */,
				3CBC53062D117ECE009174D6 /* FlutterMethodApprover.swift */,
				3CBC53072D117ECE009174D6 /* UnityMessageHandler.swift */,
				3CBC53082D117ECE009174D6 /* UnityMethodResponder.swift */,
				3CBC53042D117ECE009174D6 /* FlutterMessageManager.swift */,
			);
			path = MessageHanlder;
			sourceTree = "<group>";
		};
		3CBC530A2D117ECE009174D6 /* Flurrer */ = {
			isa = PBXGroup;
			children = (
				3C73AE8A2D190C2800A26999 /* MessageSender */,
				3CBC53092D117ECE009174D6 /* MessageHanlder */,
			);
			path = Flurrer;
			sourceTree = "<group>";
		};
		3CBC530F2D117ECE009174D6 /* Unity */ = {
			isa = PBXGroup;
			children = (
				3C73AE972D194BD600A26999 /* UnityEngineMessageSender */,
				3C73AE8F2D19308900A26999 /* UnityEngineMessageHandler */,
				3C73AE842D19044600A26999 /* Dispatcher */,
				3C73AE802D18FADE00A26999 /* Controller */,
				3CBC530E2D117ECE009174D6 /* UnityInterface.h */,
			);
			path = Unity;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				331C8082294A63A400263BE5 /* RunnerTests */,
				D1C649AE569B80A0F371F71D /* Pods */,
				011842E5AE399483BC36DCFD /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				331C8081294A63A400263BE5 /* RunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				3CBC53172D117EDB009174D6 /* AppDelegate+Flutter.swift */,
				3C73AE882D19091200A26999 /* AppDelegate+Unity.swift */,
				D06B17872D2F5D7F00E0C024 /* Screenshot */,
				D03EF6E32D1962A2008D4585 /* Sign */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
				3CBC530A2D117ECE009174D6 /* Flurrer */,
				3CBC530F2D117ECE009174D6 /* Unity */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		D03EF6E32D1962A2008D4585 /* Sign */ = {
			isa = PBXGroup;
			children = (
				D03EF7042D1D004F008D4585 /* pgnetworksign.framework */,
				D03EF6ED2D19657B008D4585 /* PGNetworkSignPlugin.swift */,
			);
			path = Sign;
			sourceTree = "<group>";
		};
		D06B17872D2F5D7F00E0C024 /* Screenshot */ = {
			isa = PBXGroup;
			children = (
				D06B178E2D2FAB6100E0C024 /* PGSecureRootViewController.swift */,
				D06B17882D2F5DB000E0C024 /* PGScreenshotPlugin.swift */,
				D06B178A2D2F5DD300E0C024 /* PGScreenshotView.swift */,
				D06B17902D2FC8F900E0C024 /* PGScreenShotManager.swift */,
			);
			path = Screenshot;
			sourceTree = "<group>";
		};
		D1C649AE569B80A0F371F71D /* Pods */ = {
			isa = PBXGroup;
			children = (
				7786E88C264E45924E35AEA3 /* Pods-Runner.debug.xcconfig */,
				9925CB3864537C87ED4DC540 /* Pods-Runner.release.xcconfig */,
				988E96DF0E69CED3E1DB8F18 /* Pods-Runner.profile.xcconfig */,
				E9DD24A3EA0BC0CFD58A62EF /* Pods-RunnerTests.debug.xcconfig */,
				D663208B82B94C6D46D171A7 /* Pods-RunnerTests.release.xcconfig */,
				DFF4465C47F65778737862AC /* Pods-RunnerTests.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		331C8080294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */;
			buildPhases = (
				92CDF35169CAE04A7491322B /* [CP] Check Pods Manifest.lock */,
				331C807D294A63A400263BE5 /* Sources */,
				331C807F294A63A400263BE5 /* Resources */,
				330FD93F0DBE9B2CC2C022B3 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				331C8086294A63A400263BE5 /* PBXTargetDependency */,
			);
			name = RunnerTests;
			productName = RunnerTests;
			productReference = 331C8081294A63A400263BE5 /* RunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				CE55BA3FA698DC83B78051CD /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				E326DA70CEA5596A2D73D4FF /* [CP] Embed Pods Frameworks */,
				09E8EC74E7F547FA26206BE2 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					331C8080294A63A400263BE5 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 97C146ED1CF9000F007C117D;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				331C8080294A63A400263BE5 /* RunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		331C807F294A63A400263BE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		09E8EC74E7F547FA26206BE2 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		92CDF35169CAE04A7491322B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RunnerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build";
		};
		CE55BA3FA698DC83B78051CD /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E326DA70CEA5596A2D73D4FF /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		331C807D294A63A400263BE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3C73AE872D19048F00A26999 /* UnityDispatcher.m in Sources */,
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				3CF45FF12D1A953F00953531 /* UnityMessageSender.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
				3CBC53102D117ECE009174D6 /* FlutterMethodAbnormal.swift in Sources */,
				3CF45FEF2D1A944A00953531 /* FlutterMessageSender.swift in Sources */,
				3CBC53182D117EDB009174D6 /* AppDelegate+Flutter.swift in Sources */,
				3CBC53112D117ECE009174D6 /* UnityMethodResponder.swift in Sources */,
				D06B17912D2FC8F900E0C024 /* PGScreenShotManager.swift in Sources */,
				D06B178F2D2FAB6100E0C024 /* PGSecureRootViewController.swift in Sources */,
				D06B17892D2F5DB000E0C024 /* PGScreenshotPlugin.swift in Sources */,
				3C73AE982D194BD600A26999 /* UnityEngineMessageSenderImpl.m in Sources */,
				3CBC53122D117ECE009174D6 /* FlutterMethodApprover.swift in Sources */,
				3C73AE832D18FB0400A26999 /* UnityEngineController.m in Sources */,
				3C73AE892D19091200A26999 /* AppDelegate+Unity.swift in Sources */,
				D06B178B2D2F5DD300E0C024 /* PGScreenshotView.swift in Sources */,
				D03EF6EE2D19657B008D4585 /* PGNetworkSignPlugin.swift in Sources */,
				3CBC53132D117ECE009174D6 /* FlutterMessageManager.swift in Sources */,
				3CF45FF42D1AA0B600953531 /* UnityMessage.swift in Sources */,
				3CF45FF52D1AA0B600953531 /* UnityMessageType.swift in Sources */,
				3CBC53152D117ECE009174D6 /* FlutterMessageHandler.swift in Sources */,
				3CBC53162D117ECE009174D6 /* UnityMessageHandler.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		331C8086294A63A400263BE5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97C146ED1CF9000F007C117D /* Runner */;
			targetProxy = 331C8085294A63A400263BE5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = YFZ6GPTEHY;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/Sign",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/Sign",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pinguo.turing.art.ipad;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = chengdu_pinguo_development_turing_ipad;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		331C8088294A63A400263BE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E9DD24A3EA0BC0CFD58A62EF /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YFZ6GPTEHY;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.pinguo.turing.art.ipad.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Debug;
		};
		331C8089294A63A400263BE5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D663208B82B94C6D46D171A7 /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YFZ6GPTEHY;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.pinguo.turing.art.ipad.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Release;
		};
		331C808A294A63A400263BE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DFF4465C47F65778737862AC /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YFZ6GPTEHY;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.pinguo.turing.art.ipad.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = YFZ6GPTEHY;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/Sign",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/Sign",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pinguo.turing.art.ipad;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = chengdu_pinguo_development_turing_ipad;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = YFZ6GPTEHY;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/Sign",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/Sign",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pinguo.turing.art.ipad;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = chengdu_pinguo_development_turing_ipad;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				331C8088294A63A400263BE5 /* Debug */,
				331C8089294A63A400263BE5 /* Release */,
				331C808A294A63A400263BE5 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
