//
//  AppDelegate+Flutter.swift
//  Runner
//
//  Created by king on 2024/12/17.
//

import Foundation
extension AppDelegate {
    
    /// 创建 Flutter 消息处理器
    func registerFlutterMessageHandler() {
        if let controller = window?.rootViewController as? FlutterViewController {
            // 将处理器注册到 Flutter 通道
            FlutterMessageManager.shared.register(handler: UnityMessageHandler(), to: controller.binaryMessenger)
        }
    }
}
