import Flutter
import UIKit
import flutter_unity_widget

@main
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        changeRootVC()
        InitUnityIntegrationWithOptions(argc: CommandLine.argc, argv: CommandLine.unsafeArgv, launchOptions)
        
        GeneratedPluginRegistrant.register(with: self)
        registerUnityController()
        registerFlutterMessageHandler()
//        PGNetworkSignPlugin.register(with: self.registrar(forPlugin: "PGNetworkSignPlugin")!)
        PGScreenshotPlugin.register(with: self.registrar(forPlugin: "PGScreenshotPlugin")!)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func changeRootVC() {
        window = UIWindow.init(frame: UIScreen.main.bounds)
        window.backgroundColor = .white
        
        let screenShotView = PGScreenshotView(frame: UIScreen.main.bounds)
        PGScreenshotPlugin.shotView = screenShotView
        window.addSubview(screenShotView)
        
        let vc = PGSecureRootViewController()
        window.rootViewController = vc
    }
}

