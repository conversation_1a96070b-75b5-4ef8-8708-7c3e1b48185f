//
//  AppDelegate+Unity.swift
//  Runner
//
//  Created by king on 2024/12/23.
//

import Foundation
extension AppDelegate {
    
    /// 创建 Unity 消息处理器
    func registerUnityController() {
        
        if let controller = window?.rootViewController as? FlutterViewController {
            UnityEngineController.sharedInstance().prepar(with: UnityMessageSender(messenger: controller.binaryMessenger))
        }
    }
}
