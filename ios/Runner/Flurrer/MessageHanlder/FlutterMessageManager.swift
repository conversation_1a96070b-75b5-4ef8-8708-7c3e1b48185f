//
//  FlutterMessageManager.swift
//  Runner
//
//  Created by king on 2024/12/17.
//

import Foundation

/// 管理所有 FlutterMessageHandler 的单例类
class FlutterMessageManager {
    /// 单例实例
    static let shared = FlutterMessageManager()
    
    /// 存储所有已经注册的Channel
    private var handlers: [String] = [String]()
    
    private init() {}
    
    /// 将目标处理器注册到 Flutter 通道
    func register(handler: FlutterChannelHandler, to messenger: FlutterBinaryMessenger) {
        guard let item = handlers.first(where: { $0 ==  handler.channelName}) else {
            handlers.append(handler.channelName)
            let methodChannel = FlutterMethodChannel(name: handler.channelName, binaryMessenger: messenger)
            methodChannel.setMethodCallHandler {(call: FlutterMethodCall, result: @escaping FlutterResult) in
                handler.handle(methodName: call.method, arguments: call.arguments, result: result)
            }
            return
        }
        print("Do not register the same channel repeatedly." + handler.channelName)
    }
}
