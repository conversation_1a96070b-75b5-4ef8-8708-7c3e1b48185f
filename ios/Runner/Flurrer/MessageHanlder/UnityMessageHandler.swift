//
//  UnityMessageHandler.swift
//  Runner
//
//  Created by king on 2024/12/17.
//

import Foundation


/// 处理Flutter调用Unity消息
class UnityMessageHandler: FlutterChannelHandler {
    
    let channelName: String = "flutter_unity_message_channel" // 通道名称，和Flutter侧约定一致
    
    func handle(methodName: String, arguments: Any?, result: @escaping FlutterResult) {
        tryHandleMethodCall(methodName: methodName, arguments: arguments, result: result)
    }
    
    private func tryHandleMethodCall(methodName: String, arguments: Any?, result: @escaping FlutterResult) {
        guard let argumentsDict = arguments as? [String: Any] else {
            result(FlutterError(code: FlutterMethodAbnormal.invalidArguments.rawValue, message: "arguments must be a map", details: nil))
            return
        }

        var currentApprover: FlutterMethodApprover? = UnityMethodApproverFactory.createChain()
        while let approver = currentApprover {
            if approver.approve(methodName: methodName, arguments: argumentsDict, result: result) {
                return
            }
            currentApprover = approver.nextApprover
        }
        // 责任链中无人成功响应
        result(FlutterMethodNotImplemented)
    }
}

/// Flutter向Unity发送消息责任链工厂
class UnityMethodApproverFactory {
    static func createChain() -> FlutterMethodApprover {
        // 创建各个校验器
        let methodNameValidator = MethodNameValidator()
        let argumentMissingValidator = ArgumentMissingValidator()
        let UnityMethodResponder = UnityMethodResponder()
        
        // 链接责任链
        methodNameValidator.nextApprover = argumentMissingValidator
        argumentMissingValidator.nextApprover = UnityMethodResponder
        
        return methodNameValidator
    }
}
