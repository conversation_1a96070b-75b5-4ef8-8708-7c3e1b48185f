//
//  FlutterMetodhApprovers.swift
//  Runner
//
//  Created by king on 2024/12/17.
//

import Foundation

protocol FlutterMethodApprover {
    var nextApprover: FlutterMethodApprover? { get set }
    func approve(methodName: String, arguments: [String: Any], result: @escaping FlutterResult) -> <PERSON><PERSON>
}


/// 方法名称校验
class MethodNameValidator: FlutterMethodApprover {
    var nextApprover: FlutterMethodApprover?
            
    func approve(methodName: String, arguments: [String: Any], result: @escaping FlutterResult) -> <PERSON><PERSON> {
        // 如果参数类型校验通过，传递给下一个处理器
        return nextApprover?.approve(methodName: methodName, arguments: arguments, result: result) ?? false
    }
}

/// 方法参数校验
class ArgumentMissingValidator: FlutterMethodApprover {
    var nextApprover: FlutterMethodApprover?
    
    func approve(methodName: String, arguments: [String: Any], result: @escaping FlutterResult) -> <PERSON><PERSON> {
        // 如果参数类型校验通过，传递给下一个处理器
        return nextApprover?.approve(methodName: methodName, arguments: arguments, result: result) ?? false
    }
}
