//
//  UnityMethodResponder.swift
//  Runner
//
//  Created by king on 2024/12/17.
//

import Foundation

/// 响应Unity SendMessage
class UnityMethodResponder: FlutterMethodApprover {
    var nextApprover: FlutterMethodApprover?
    
    /// 判断是否可以处理该方法
    private func canApprove(methodName: String) -> <PERSON><PERSON> {
        return methodName == "sendMessage"
    }
    
    /// 处理方法请求
    func approve(methodName: String, arguments: [String: Any], result: @escaping FlutterResult) -> Bool {
        // 1. 判断是否是 sendMessage 方法
        guard canApprove(methodName: methodName) else {
            // 如果当前不能处理，传递给下一个处理者
            return nextApprover?.approve(methodName: methodName, arguments: arguments, result: result) ?? false
        }
        
        // 2. 提取参数，确保参数合法
        guard let message = arguments["message"] as? String else {
            let error = "Error: Missing or invalid argument 'message'. Expected a String."
            print(error)
            result(FlutterError(code: FlutterMethodAbnormal.invalidArguments.rawValue, message: error, details: nil))
            return false
        }
        
        // 3.实际调用发送消息
        print("Calling UnityMessageSender.sendMessage with message: \(message)")
        UnityEngineController.sharedInstance().sendMessage(message)

        // 4. 返回成功结果
        result("sendMessage invoked successfully with message: \(message)")
        
        // 5. 响应责任链终点
        return true
    }
}
