//
//  UnityMessageSender.swift
//  Runner
//
//  Created by king on 2024/12/24.
//

import Foundation

protocol MessageChannel {
    func send(_ message: UnityMessage)
}

class FlutterMessageChannel: MessageChannel {
    private let eventSink: FlutterEventSink?
    
    init(eventSink: FlutterEventSink?) {
        self.eventSink = eventSink
    }
    
    func send(_ message: UnityMessage) {
        eventSink?(message.toDictionary)
    }
}

class UnityMessageSender: NSObject, FlutterMessageSender {

    // MARK: - Constants
    private enum Constants {
        static let channelName = "unity_flutter_message_channel"
    }
    
    var channelName: String {
        return Constants.channelName
    }

    private let eventChannel: FlutterEventChannel
    private var messageChannel: MessageChannel?
    
    init(messenger: FlutterBinaryMessenger) {
        self.eventChannel = FlutterEventChannel(name: Constants.channelName, binaryMessenger: messenger)
        super.init()
        self.eventChannel.setStreamHandler(self)
    }
}

// MARK: - UnityEngineMessageHandler
extension UnityMessageSender: UnityEngineMessageHandler {
    func onEngineInitialized() {
        let message = UnityMessage(type: .engineInitialized, data: nil)
        messageChannel?.send(message)
    }
    
    func onEngineError(_ error: String) {
        let message = UnityMessage(type: .engineError, data: error)
        messageChannel?.send(message)
    }
}

// MARK: - FlutterStreamHandler
extension UnityMessageSender: FlutterStreamHandler {
    func onListen(withArguments arguments: Any?, 
                  eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.messageChannel = FlutterMessageChannel(eventSink: events)
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        self.messageChannel = nil
        return nil
    }
}
