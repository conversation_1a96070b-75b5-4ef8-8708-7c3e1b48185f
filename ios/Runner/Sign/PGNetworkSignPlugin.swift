//import Flutter
//import pgnetworksign
//
//class PGNetworkSignPlugin: NSObject, FlutterPlugin {
//    static func register(with registrar: FlutterPluginRegistrar) {
//        let channel = FlutterMethodChannel(
//            name: "com.turingart.pgsign",
//            binaryMessenger: registrar.messenger()
//        )
//        let instance = PGNetworkSignPlugin()
//        registrar.addMethodCallDelegate(instance, channel: channel)
//    }
//    
//    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
//        switch call.method {
//        case "getSign":
//            guard let args = call.arguments as? [String: Any],
//                  let host = args["host"] as? String,
//                  let path = args["path"] as? String,
//                  let method = args["method"] as? String,
//                  let headers = args["headers"] as? [String: String],
//                  let params = args["params"] as? [String: Any],
//                  let forBody = args["forBody"] as? Bool else {
//                result(FlutterError(code: "INVALID_ARGUMENTS",
//                                  message: "Invalid arguments",
//                                  details: nil))
//                return
//            }
//            
//            // 计算总参数数量
//            let totalCount = forBody ? headers.count : headers.count + params.count
//            
//            // 创建单个指针数组存储所有参数
//            let paramArray = UnsafeMutablePointer<UnsafeMutableRawPointer>.allocate(capacity: totalCount)
//            var paramCount = 0
//            
//            // 添加 headers 到参数数组
//            for (key, value) in headers {
//                let headerPointer = SignUtilsKt.addParam(key: key, value: value)
//                paramArray[paramCount] = headerPointer
//                paramCount += 1
//            }
//            
//            // 处理 JSON body 数据
//            var jsonBodyPointer: UnsafeMutablePointer<UInt8>?
//            var jsonBodySize: Int32 = 0
//            
//            if forBody {
//                do {
//                    // 使用 JSONSerialization 时设置选项以确保一致的输出
//                    let jsonData = try JSONSerialization.data(
//                        withJSONObject: params,
//                        options: [.sortedKeys] // 确保键按字母顺序排序
//                    )
//                    
//                    // 打印检查生成的 JSON 字符串
//                    if let jsonString = String(data: jsonData, encoding: .utf8) {
//                        print("Generated JSON string: \(jsonString)")
//                    }
//                    
//                    jsonBodySize = Int32(jsonData.count)
//                    if jsonBodySize > 0 {
//                        jsonBodyPointer = UnsafeMutablePointer<UInt8>.allocate(capacity: Int(jsonBodySize))
//                        jsonData.copyBytes(to: jsonBodyPointer!,
//                                         count: Int(jsonBodySize))
//                    }
//                } catch {
//                    // 清理已分配的内存
//                    paramArray.deallocate()
//                    
//                    result(FlutterError(code: "JSON_ERROR",
//                                      message: "Failed to serialize JSON body",
//                                      details: error.localizedDescription))
//                    return
//                }
//            } else {
//                // 处理参数
//                for (key, value) in params {
//                    let paramPointer = SignUtilsKt.addParam(key: key, value: value as? String ?? "")
//                    paramArray[paramCount] = paramPointer
//                    paramCount += 1
//                }
//            }
//            
//            // 调用签名方法
//            let sign = SignUtilsKt.genSign(
//                host: host,
//                urlPath: path,
//                method: method,
//                params: paramArray,
//                count: Int32(paramCount),
//                jsonBodyData: jsonBodyPointer,
//                bytes: jsonBodySize
//            )
//            
//            // 释放内存
//            paramArray.deallocate()
//            jsonBodyPointer?.deallocate()
//            
//            result(sign)
//            
//        default:
//            result(FlutterMethodNotImplemented)
//        }
//    }
//} 
