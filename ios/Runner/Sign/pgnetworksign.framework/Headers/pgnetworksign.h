#ifndef KONAN_LIBPGNETWORKSIGN_H
#define KONAN_LIBPGNETWORKSIGN_H
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
typedef bool            libpgnetworksign_KBoolean;
#else
typedef _Bool           libpgnetworksign_KBoolean;
#endif
typedef unsigned short     libpgnetworksign_KChar;
typedef signed char        libpgnetworksign_KByte;
typedef short              libpgnetworksign_KShort;
typedef int                libpgnetworksign_KInt;
typedef long long          libpgnetworksign_KLong;
typedef unsigned char      libpgnetworksign_KUByte;
typedef unsigned short     libpgnetworksign_KUShort;
typedef unsigned int       libpgnetworksign_KUInt;
typedef unsigned long long libpgnetworksign_KULong;
typedef float              libpgnetworksign_KFloat;
typedef double             libpgnetworksign_KDouble;
typedef float __attribute__ ((__vector_size__ (16))) libpgnetworksign_KVector128;
typedef void*              libpgnetworksign_KNativePtr;
struct libpgnetworksign_KType;
typedef struct libpgnetworksign_KType libpgnetworksign_KType;

typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Byte;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Short;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Int;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Long;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Float;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Double;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Char;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Boolean;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Unit;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_UByte;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_UShort;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_UInt;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_ULong;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_Param;
typedef struct {
  libpgnetworksign_KNativePtr pinned;
} libpgnetworksign_kref_kotlin_Any;

extern void* addParam(const char* key, const char* value);
extern const char* genSign(const char* host, const char* urlPath, const char* method, void* params, libpgnetworksign_KInt count, void* jsonBodyData, libpgnetworksign_KInt bytes);

typedef struct {
  /* Service functions. */
  void (*DisposeStablePointer)(libpgnetworksign_KNativePtr ptr);
  void (*DisposeString)(const char* string);
  libpgnetworksign_KBoolean (*IsInstance)(libpgnetworksign_KNativePtr ref, const libpgnetworksign_KType* type);
  libpgnetworksign_kref_kotlin_Byte (*createNullableByte)(libpgnetworksign_KByte);
  libpgnetworksign_KByte (*getNonNullValueOfByte)(libpgnetworksign_kref_kotlin_Byte);
  libpgnetworksign_kref_kotlin_Short (*createNullableShort)(libpgnetworksign_KShort);
  libpgnetworksign_KShort (*getNonNullValueOfShort)(libpgnetworksign_kref_kotlin_Short);
  libpgnetworksign_kref_kotlin_Int (*createNullableInt)(libpgnetworksign_KInt);
  libpgnetworksign_KInt (*getNonNullValueOfInt)(libpgnetworksign_kref_kotlin_Int);
  libpgnetworksign_kref_kotlin_Long (*createNullableLong)(libpgnetworksign_KLong);
  libpgnetworksign_KLong (*getNonNullValueOfLong)(libpgnetworksign_kref_kotlin_Long);
  libpgnetworksign_kref_kotlin_Float (*createNullableFloat)(libpgnetworksign_KFloat);
  libpgnetworksign_KFloat (*getNonNullValueOfFloat)(libpgnetworksign_kref_kotlin_Float);
  libpgnetworksign_kref_kotlin_Double (*createNullableDouble)(libpgnetworksign_KDouble);
  libpgnetworksign_KDouble (*getNonNullValueOfDouble)(libpgnetworksign_kref_kotlin_Double);
  libpgnetworksign_kref_kotlin_Char (*createNullableChar)(libpgnetworksign_KChar);
  libpgnetworksign_KChar (*getNonNullValueOfChar)(libpgnetworksign_kref_kotlin_Char);
  libpgnetworksign_kref_kotlin_Boolean (*createNullableBoolean)(libpgnetworksign_KBoolean);
  libpgnetworksign_KBoolean (*getNonNullValueOfBoolean)(libpgnetworksign_kref_kotlin_Boolean);
  libpgnetworksign_kref_kotlin_Unit (*createNullableUnit)(void);
  libpgnetworksign_kref_kotlin_UByte (*createNullableUByte)(libpgnetworksign_KUByte);
  libpgnetworksign_KUByte (*getNonNullValueOfUByte)(libpgnetworksign_kref_kotlin_UByte);
  libpgnetworksign_kref_kotlin_UShort (*createNullableUShort)(libpgnetworksign_KUShort);
  libpgnetworksign_KUShort (*getNonNullValueOfUShort)(libpgnetworksign_kref_kotlin_UShort);
  libpgnetworksign_kref_kotlin_UInt (*createNullableUInt)(libpgnetworksign_KUInt);
  libpgnetworksign_KUInt (*getNonNullValueOfUInt)(libpgnetworksign_kref_kotlin_UInt);
  libpgnetworksign_kref_kotlin_ULong (*createNullableULong)(libpgnetworksign_KULong);
  libpgnetworksign_KULong (*getNonNullValueOfULong)(libpgnetworksign_kref_kotlin_ULong);

  /* User functions. */
  struct {
    struct {
      struct {
        libpgnetworksign_KType* (*_type)(void);
        libpgnetworksign_kref_Param (*Param)(const char* key, const char* value);
        const char* (*get_key)(libpgnetworksign_kref_Param thiz);
        const char* (*get_value)(libpgnetworksign_kref_Param thiz);
        const char* (*component1)(libpgnetworksign_kref_Param thiz);
        const char* (*component2)(libpgnetworksign_kref_Param thiz);
        libpgnetworksign_kref_Param (*copy)(libpgnetworksign_kref_Param thiz, const char* key, const char* value);
        libpgnetworksign_KBoolean (*equals)(libpgnetworksign_kref_Param thiz, libpgnetworksign_kref_kotlin_Any other);
        libpgnetworksign_KInt (*hashCode)(libpgnetworksign_kref_Param thiz);
        const char* (*toString)(libpgnetworksign_kref_Param thiz);
      } Param;
      void (*main)();
      void* (*addParam_)(const char* key, const char* value);
      const char* (*genSign_)(const char* host, const char* urlPath, const char* method, void* params, libpgnetworksign_KInt count, void* jsonBodyData, libpgnetworksign_KInt bytes);
    } root;
  } kotlin;
} libpgnetworksign_ExportedSymbols;
extern libpgnetworksign_ExportedSymbols* libpgnetworksign_symbols(void);
#ifdef __cplusplus
}  /* extern "C" */
#endif
#endif  /* KONAN_LIBPGNETWORKSIGN_H */
