//
//  PGScreenShotManager.swift
//  Runner
//
//  Created by <PERSON> on 2025/1/9.
//

import Foundation
class PGScreenShotManager {
    private var displayLink: CADisplayLink?
    private var isCapturing = false
    private var frameCount = 0

    func startCapturing() {
        guard !isCapturing else { return }
        isCapturing = true
        
        // 创建 CADisplayLink，设置为每秒调用一次
        displayLink = CADisplayLink(target: self, selector: #selector(handleDisplayLink))
        displayLink?.preferredFramesPerSecond = 60  // 设置为60FPS
        displayLink?.add(to: .main, forMode: .common)
    }
    
    func stopCapturing() {
        displayLink?.invalidate()
        displayLink = nil
        isCapturing = false
        frameCount = 0
    }
    
    @objc private func handleDisplayLink() {
        // 每60帧（约1秒）截图一次
        if frameCount == 0 {
             captureScreen()
        }
        frameCount += 1
        if frameCount >= 60 {
            frameCount = 0
        }
    }
    
    private func captureScreen() {
        autoreleasepool {
            guard let rootVC = UIApplication.shared.keyWindow?.rootViewController as? PGSecureRootViewController else {
                return
            }
            rootVC.textField.isSecureTextEntry = false
            let renderer = UIGraphicsImageRenderer(bounds: rootVC.view.bounds)
            let screenshot = renderer.image { _ in
                rootVC.view.drawHierarchy(in: rootVC.view.bounds, afterScreenUpdates: false)
            }
            rootVC.textField.isSecureTextEntry = true
            PGScreenshotPlugin.shotView?.updateImage(image: screenshot)
        }
    }
}
