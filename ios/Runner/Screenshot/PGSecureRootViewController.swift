import UIKit
import Flutter

class PGSecureRootViewController: FlutterViewController {
    private(set) var textField: UITextField!
    private var blurView: PGScreenshotView = PGScreenshotView()
    
   override func viewDidLoad() {
        super.viewDidLoad()
        if let v = view { // 取出原view
            v.backgroundColor = .white // 背景色 - 截图的颜色
            v.frame = .zero // 不能设置为UIScreen.main.bounds
            
            view = makeSecureView() // 生成防截图view
            view.addSubview(v) // 必须将原view添加到当前view上
        }
    }
    
    /// 生成防截图view
    private func makeSecureView() -> UIView? {
        
        textField = UITextField()
        textField.isSecureTextEntry = false // 必须是true，否则无法禁止截图
        let fv = textField.subviews.first
        fv?.subviews.forEach { $0.removeFromSuperview() }
        fv?.isUserInteractionEnabled = true // 开启交互
        
        return fv
    }
} 
