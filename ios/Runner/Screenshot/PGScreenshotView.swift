//
//  PGScreenshotView.swift
//  Runner
//
//  Created by <PERSON> on 2025/1/9.
//

import UIKit

class PGScreenshotView: UIView {

    private lazy var imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var blurView: UIVisualEffectView = {
        let view = UIVisualEffectView()
        view.backgroundColor = .clear
        let blurEffect = UIBlurEffect(style: .dark)
        view.effect = blurEffect
        return view
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "欢迎使用图灵精修"
        label.textColor = .white
        label.textAlignment = .center
        label.font = UIFont(name: "PingFangSC-Semibold", size: 24)
        return label
    }()

    private lazy var detailLabel: UILabel = {
        let label = UILabel()
        label.text = "导出即可享受高清画质，分享图灵精修专属效果"
        label.textColor = UIColor.init(red: 235 / 255.0, green: 242 / 255.0, blue: 245 / 255.0, alpha: 0.6)
        label.textAlignment = .center
        label.font = UIFont(name: "PingFangSC-Regular", size: 14)
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        addSubview(imageView)
        imageView.frame = bounds
        addSubview(blurView)
        blurView.frame = bounds
        addSubview(titleLabel)
        titleLabel.frame = CGRect(x: 0, y: 0, width: bounds.width, height: 34)
        titleLabel.center = CGPoint(x: bounds.width / 2, y: bounds.height / 2 - 20)
        addSubview(detailLabel)
        detailLabel.frame = CGRect(x: 0, y: 0, width: bounds.width, height: 20)
        detailLabel.center = CGPoint(x: bounds.width / 2, y: bounds.height / 2 + 15)
    }

    func updateImage(image: UIImage) {
        imageView.image = image
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        imageView.frame = bounds
        blurView.frame = bounds
    }

}
