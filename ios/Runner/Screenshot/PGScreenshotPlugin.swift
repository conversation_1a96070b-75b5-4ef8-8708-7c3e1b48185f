//
//  PGScreenshotPlugin.swift
//  Runner
//
//  Created by <PERSON> on 2025/1/9.
//

import Foundation
import Flutter
import AVFoundation
class PGScreenshotPlugin: NSObject, FlutterPlugin {
    private var testView: PGScreenshotView = PGScreenshotView(frame: UIScreen.main.bounds)
    static weak var shotView: PGScreenshotView?
    static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "pg_screenshot", binaryMessenger: registrar.messenger())
        let instance = PGScreenshotPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        if call.method == "interceptionScreenshot" {
            if let enable = call.arguments as? Bool {
                handleInterceptionChange(value: enable)
                result(true)
            } else {
                result(FlutterError(code: "INVALID_ARGUMENTS",
                                  message: "Expected boolean argument",
                                  details: nil))
            }
        } else {
            result(FlutterMethodNotImplemented)
        }
    }
    
    func handleInterceptionChange(value: Bool) {
        if let rootVC = UIApplication.shared.delegate?.window??.rootViewController as? PGSecureRootViewController {
            rootVC.textField.isSecureTextEntry = value
            if value {
                let manager = PGScreenShotManager()
                manager.startCapturing()
            } else {
                let manager = PGScreenShotManager()
                manager.startCapturing()
            }
        }
        debugPrint("interceptionScreenshot: \(value)")
    }
}
