//
//  UnityEngineController.m
//  Runner
//
//  Created by king on 2024/12/23.
//

#import "UnityEngineController.h"
#import "UnityDispatcher.h"
#import "UnityEngineMessageSenderImpl.h"

@interface UnityEngineController() {
    UnityEngineMessageSenderImpl *_messageSender;
    UnityDispatcher *_dispatcher;
}
@end

@implementation UnityEngineController

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static UnityEngineController *controller = nil;
    dispatch_once(&onceToken, ^{
        controller = [[UnityEngineController alloc] init];
    });
    return controller;
}

- (UnityEngineMessageSenderImpl *)messageSender {
    if (!_messageSender) {
        _messageSender = [[UnityEngineMessageSenderImpl alloc] init];
    }
    return _messageSender;
}

- (void)prepar {
    [self preparWithMessageHandler: nil];
}

- (void)sendMessage:(NSString *)message {
    if (_messageSender == nil) {
        return;
    }
    [self.messageSender sendMessage:message];
}

- (void)preparWithMessageHandler:(id<UnityEngineMessageHandler> _Nullable)handler {
    _dispatcher = [[UnityDispatcher alloc] init];
    [_dispatcher registerUnityMessagerSender: self.messageSender];
    [_dispatcher registerUnityMessagerHandler: handler];
    [PGUnityPlugin setUnityDelegate: _dispatcher];
}

@end
