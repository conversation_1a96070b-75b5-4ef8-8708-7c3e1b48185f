//
//  UnityEngineController.h
//  Runner
//
//  Created by king on 2024/12/23.
//

#import <Foundation/Foundation.h>
#import "UnityEngineMessageHandler.h"

NS_ASSUME_NONNULL_BEGIN

@interface UnityEngineController : NSObject

+ (instancetype)sharedInstance;

/// 默认初始化方法
- (void)prepar;

/// 初始化UnityController 并初始化Unity消息处理者
- (void)preparWithMessageHandler:(id<UnityEngineMessageHandler> _Nullable)handler;


- (void)sendMessage:(NSString *)message;

@end

NS_ASSUME_NONNULL_END
