//
//  UnityDispatcher.h
//  Runner
//
//  Created by king on 2024/12/23.
//

#import <Foundation/Foundation.h>
#import "UnityFramework/UnityFramework.h"
#import "UnityEngineMessageSender.h"
#import "UnityEngineMessageHandler.h"

NS_ASSUME_NONNULL_BEGIN

@interface UnityDispatcher : NSObject<PGUnityPluginDelegate>


/// 注册Unity消息发送的实现
/// - Parameter sender: message sender
- (void)registerUnityMessagerSender: (id<UnityEngineMessageSender>)sender;

/// 注册向Unity发消息的实现
/// - Parameter handler:
- (void)registerUnityMessagerHandler: (id<UnityEngineMessageHandler>)handler;

@end

NS_ASSUME_NONNULL_END
