//
//  UnityDispatcher.m
//  Runner
//
//  Created by king on 2024/12/23.
//

#import "UnityDispatcher.h"
@interface UnityDispatcher()
@property(nonatomic, weak)id<UnityEngineMessageSender> messageSender;
@property(nonatomic, weak)id<UnityEngineMessageHandler> messageHandler;
@end
@implementation UnityDispatcher

- (void)onEngineInitialized {
    // Unity初始化完成的回调，需要通知Flutter
    NSLog(@"on engine initialized");
    if ([self.messageHandler respondsToSelector:@selector(onEngineInitialized)]) {
        [self.messageHandler onEngineInitialized];
    }
}

- (void)onEngineError:(char *)errorMsg {
    NSLog(@"on engine error");
    NSString *msg = [NSString stringWithCString: errorMsg encoding:(NSUTF8StringEncoding)];
    if ([self.messageHandler respondsToSelector:@selector(onEngineError:)]) {
        [self.messageHandler onEngineError:(msg)];
    }
}

- (void)onPGMessageHandlerInitialized:(PGMessageEntry) messageEntry {
    NSLog(@"on message handler initialized %p", messageEntry);
    if (messageEntry && [self.messageSender respondsToSelector:@selector(registerMessageEntry:)]) {
        [self.messageSender registerMessageEntry:messageEntry];
    }
}

- (void)registerUnityMessagerHandler:(nonnull id<UnityEngineMessageHandler>)handler {
    self.messageHandler = handler;
}

- (void)registerUnityMessagerSender:(nonnull id<UnityEngineMessageSender>)sender {
    self.messageSender = sender;
}

@end
