//
//  UnityEngineMessageSenderImpl.m
//  Runner
//
//  Created by king on 2024/12/23.
//

#import "UnityEngineMessageSenderImpl.h"

@interface UnityEngineMessageSenderImpl() {
    PGMessageEntry _messageEntry;
}

@end
@implementation UnityEngineMessageSenderImpl

-(instancetype)init {
    if (self = [super init]) {
        _messageEntry = nil;
    }
    return self;
}

/// 注册函数指针并在内部保存
/// - Parameter entry: Unity通讯的核心函数指针
-(void)registerMessageEntry:(PGMessageEntry)entry {
    _messageEntry = entry;
}


/// 通过函数指针向Unity发消息
/// - Parameter message: 消息内容
-(void)sendMessage:(NSString *)message {
    if (_messageEntry == nil) {
        NSLog(@"❌❌❌ unity entry is null, need to initialize");
        return;
    }
    if (!message.length || message == nil) {
        NSLog(@"❌❌❌ message error");
        return;
    }
    NSLog(@"send message to unity --- %@", message);
    _messageEntry((char *)message.UTF8String);
}

-(void)dealloc {
    _messageEntry = nil;
}

@end
