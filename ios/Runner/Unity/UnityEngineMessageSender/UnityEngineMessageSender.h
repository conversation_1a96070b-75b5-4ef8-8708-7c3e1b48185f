//
//  UnityEngineMessageSender.h
//  Runner
//
//  Created by king on 2024/12/23.
//

#import <Foundation/Foundation.h>
#import <UnityFramework/UnityFramework.h>
NS_ASSUME_NONNULL_BEGIN

@protocol UnityEngineMessageSender <NSObject>

/// 注册函数指针并在内部保存
/// - Parameter entry: Unity通讯的核心函数指针
-(void)registerMessageEntry:(PGMessageEntry)entry;

/// 向Unity发送消息
/// - Parameter message:
-(void)sendMessage:(NSString *)message;

@end

NS_ASSUME_NONNULL_END
