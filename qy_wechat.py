import requests
import logging
import json
import os
import subprocess
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class BuildNotificationSender:
    """
    构建通知发送器
    
    用于在构建成功后向企业微信群发送通知消息，包含Unity仓库和主仓库的构建信息。
    """
    
    def __init__(self, build_metadata_path: str, download_url: str, main_repo_path: str, isWin7: bool = False):
        """
        初始化构建通知发送器
        
        Args:
            build_metadata_path: build_metadata.json文件路径，包含Unity构建数据
            download_url: 构建产物的下载地址
            main_repo_path: 主仓库的git repo路径
            isWin7: 是否为Win7构建，True表示Win7，False表示Win10+
        """
        self.build_metadata_path = build_metadata_path
        self.download_url = download_url
        self.main_repo_path = main_repo_path
        self.isWin7 = isWin7
        self.webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9d8b0296-cb96-4761-93cb-b98ae271efd0"
        
        # 验证参数
        self._validate_inputs()
        
    def _validate_inputs(self):
        """验证输入参数的有效性"""
        if not os.path.exists(self.build_metadata_path):
            raise ValueError(f"build_metadata.json文件不存在: {self.build_metadata_path}")
            
        if not os.path.exists(self.main_repo_path):
            raise ValueError(f"主仓库路径不存在: {self.main_repo_path}")
            
        if not os.path.exists(os.path.join(self.main_repo_path, '.git')):
            raise ValueError(f"指定路径不是Git仓库: {self.main_repo_path}")
            
        if not self.download_url:
            raise ValueError("构建产物下载地址不能为空")
    
    def _load_unity_metadata(self) -> Dict[str, Any]:
        """
        从build_metadata.json加载Unity仓库的构建元数据
        
        Returns:
            Dict[str, Any]: Unity构建元数据
        """
        try:
            with open(self.build_metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            logger.info(f"成功加载Unity构建元数据: {self.build_metadata_path}")
            return metadata
        except Exception as e:
            logger.error(f"加载Unity构建元数据失败: {e}")
            return {}
    
    def _get_git_branch_name(self, repo_path: str) -> str:
        """
        获取Git仓库当前的分支名
        
        Args:
            repo_path: Git仓库路径
            
        Returns:
            str: Git仓库的分支名
        """
        try:
            original_dir = os.getcwd()
            os.chdir(repo_path)
            
            result = subprocess.run(
                ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                capture_output=True,
                text=True,
                check=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            branch_name = result.stdout.strip()
            os.chdir(original_dir)
            
            logger.info(f"获取Git分支名成功: {branch_name}")
            return branch_name
            
        except subprocess.CalledProcessError as e:
            logger.error(f"获取Git分支名失败: {e}")
            os.chdir(original_dir)
            return ""
        except Exception as e:
            logger.error(f"获取Git分支名时发生错误: {e}")
            os.chdir(original_dir)
            return ""
    
    def _get_recent_commits(self, repo_path: str, count: int = 3) -> List[Dict[str, str]]:
        """
        获取Git仓库最近的提交信息
        
        Args:
            repo_path: Git仓库路径
            count: 获取的提交数量，默认3条
            
        Returns:
            List[Dict[str, str]]: 提交信息列表，每个元素包含commit_id, message, author, date
        """
        try:
            original_dir = os.getcwd()
            os.chdir(repo_path)
            
            # 获取最近的提交信息
            result = subprocess.run([
                "git", "log", 
                f"--max-count={count}",
                "--pretty=format:%h|%s|%an|%ad",
                "--date=format:%Y/%m/%d %H:%M:%S"
            ], capture_output=True, text=True, check=True, encoding='utf-8', errors='ignore')
            
            commits = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    parts = line.split('|')
                    if len(parts) >= 4:
                        commits.append({
                            'commit_id': parts[0],
                            'message': parts[1],
                            'author': parts[2],
                            'date': parts[3]
                        })
            
            os.chdir(original_dir)
            logger.info(f"获取Git提交记录成功，共{len(commits)}条")
            return commits
            
        except subprocess.CalledProcessError as e:
            logger.error(f"获取Git提交记录失败: {e}")
            os.chdir(original_dir)
            return []
        except Exception as e:
            logger.error(f"获取Git提交记录时发生错误: {e}")
            os.chdir(original_dir)
            return []
    
    def _create_commit_summary_for_wechat(self, commits: List[Dict[str, str]]) -> str:
        """
        为企业微信消息创建提交摘要
        
        Args:
            commits: 提交信息列表
            
        Returns:
            str: 格式化的提交摘要
        """
        if not commits:
            return "无最近提交记录"
        
        summary_lines = []
        for i, commit in enumerate(commits[:3], 1):  # 只显示前3条
            commit_id = commit['commit_id']
            message = commit['message'][:50] + "..." if len(commit['message']) > 50 else commit['message']
            author = commit['author']
            date = commit['date']
            summary_lines.append(f"<font color=\"0x6c757d\">`[{commit_id}]`</font> {message} - {author} ({date})")
        
        return "\n".join(summary_lines)
    
    def _get_main_repo_metadata(self) -> Dict[str, Any]:
        """
        获取主仓库的Git元数据信息
        
        Returns:
            Dict[str, Any]: 主仓库元数据
        """
        try:
            branch_name = self._get_git_branch_name(self.main_repo_path)
            recent_commits = self._get_recent_commits(self.main_repo_path, 3)
            commit_summary = self._create_commit_summary_for_wechat(recent_commits)
            
            return {
                'git_branch_name': branch_name,
                'commit_summary': commit_summary,
                'recent_commits': recent_commits
            }
        except Exception as e:
            logger.error(f"获取主仓库元数据失败: {e}")
            return {
                'git_branch_name': 'N/A',
                'commit_summary': '无法获取提交信息',
                'recent_commits': []
            }
    
    def _make_markdown_message(self, markdown_content: str) -> Dict[str, Any]:
        """
        创建企业微信markdown格式消息
        
        Args:
            markdown_content: markdown内容
        
        Returns:
            dict: 格式化的企业微信markdown消息字典
        """
        return {
            "msgtype": "markdown",
            "markdown": {"content": markdown_content}
        }
    
    
    def _send_qy_wechat(self, message: Dict[str, Any]) -> bool:
        """
        向企业微信发送消息
        
        Args:
            message: 要发送的消息内容字典
        
        Returns:
            bool: 是否发送成功
        """
        try:
            response = requests.post(self.webhook_url, json=message)
            response.raise_for_status()
            logger.info("企业微信消息发送成功")
            return True
        except Exception as e:
            logger.error(f"发送企业微信消息失败: {e}")
            return False
    
    def send_build_success_notification(self) -> bool:
        """
        发送构建成功通知到企业微信
        
        Returns:
            bool: 是否发送成功
        """
        try:
            # 获取当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 加载Unity构建元数据
            unity_metadata = self._load_unity_metadata()
            unity_branch = unity_metadata.get('git_branch_name', 'N/A')
            unity_commits = unity_metadata.get('recent_commits', [])
            unity_commit_summary = self._create_commit_summary_for_wechat(unity_commits)
            
            # 获取主仓库元数据
            main_metadata = self._get_main_repo_metadata()
            main_branch = main_metadata.get('git_branch_name', 'N/A')
            main_commit_summary = main_metadata.get('commit_summary', '无法获取提交信息')
            
            # 根据isWin7参数确定系统版本显示
            system_version = "Win7" if self.isWin7 else "Win10+"
            
            # 构建markdown格式的成功消息
            markdown_content = f"""
## ✅ 构建成功通知 - {system_version}

--------------------------------
**主仓库信息**
构建分支: <font color="0x007bff">{main_branch}</font>
提交记录:
> <font color="comment">{main_commit_summary}</font>

--------------------------------
**Unity仓库信息**
构建分支: <font color="0x007bff">{unity_branch}</font>
提交记录:
> <font color="comment">{unity_commit_summary}</font>

--------------------------------
**构建信息**
构建时间: {current_time}
下载地址: [**点击下载构建产物**]({self.download_url})

🎉 构建完成，请及时下载测试！
"""

            # 创建并发送消息
            message = self._make_markdown_message(markdown_content)
            return self._send_qy_wechat(message)
            
        except Exception as e:
            logger.error(f"发送构建成功通知时出错: {e}")
            return False
    
    def send_build_failure_notification(self, error_message: str) -> bool:
        """
        发送构建失败通知到企业微信
        
        Args:
            error_message: 错误消息字符串
        
        Returns:
            bool: 是否发送成功
        """
        try:
            # 获取当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 加载Unity构建元数据（如果存在）
            unity_metadata = self._load_unity_metadata() if os.path.exists(self.build_metadata_path) else {}
            unity_branch = unity_metadata.get('git_branch_name', 'N/A')
            unity_commits = unity_metadata.get('recent_commits', [])
            unity_commit_summary = self._create_commit_summary_for_wechat(unity_commits)
            
            # 获取主仓库元数据
            main_metadata = self._get_main_repo_metadata()
            main_branch = main_metadata.get('git_branch_name', 'N/A')
            main_commit_summary = main_metadata.get('commit_summary', '无法获取提交信息')
            
            # 构建markdown格式的错误消息
            markdown_content = f"""## 🚨 构建失败通知
--------------------------------
**Unity仓库信息**
构建分支: <font color="warning">{unity_branch}</font>
提交记录:
> <font color="comment">{unity_commit_summary}</font>

--------------------------------
**主仓库信息**
构建分支: <font color="warning">{main_branch}</font>
提交记录:
> <font color="comment">{main_commit_summary}</font>

--------------------------------
**错误详情**
构建时间: {current_time}
```bash
{error_message}
```

❌ 构建失败，请检查错误信息并修复！
"""

            # 创建并发送消息
            message = self._make_markdown_message(markdown_content)
            return self._send_qy_wechat(message)
            
        except Exception as e:
            logger.error(f"发送构建失败通知时出错: {e}")
            return False


# 保持向后兼容的函数（可选）
def send_build_failure_message(error_message):
    """
    发送构建失败错误消息到企业微信（向后兼容函数）
    
    Args:
        error_message: 错误消息字符串
    
    Returns:
        bool: 是否发送成功
    """
    try:
        # 使用默认参数创建通知发送器
        current_dir = os.getcwd()
        build_metadata_path = os.path.join(current_dir, "build_metadata.json")
        download_url = "构建失败，无下载地址"
        main_repo_path = current_dir
        
        if not os.path.exists(build_metadata_path):
            # 创建一个空的元数据文件以避免错误
            empty_metadata = {
                "git_branch_name": "N/A",
                "recent_commits": []
            }
            with open(build_metadata_path, 'w', encoding='utf-8') as f:
                json.dump(empty_metadata, f, ensure_ascii=False, indent=2)
        
        sender = BuildNotificationSender(build_metadata_path, download_url, main_repo_path, isWin7=False)
        return sender.send_build_failure_notification(error_message)
        
    except Exception as e:
        logger.error(f"发送构建失败消息时出错: {e}")
        return False


if __name__ == "__main__":
    # 使用示例
    try:
        # 示例参数
        build_metadata_path = "build_metadata.json"
        download_url = "https://example.com/download/turing_art-1.0.0-win10-setup.exe"
        main_repo_path = os.getcwd()
        
        # 创建通知发送器（示例使用Win10+）
        sender = BuildNotificationSender(build_metadata_path, download_url, main_repo_path, isWin7=False)
        
        # 发送构建成功通知
        success = sender.send_build_success_notification()
        print(f"构建成功通知发送{'成功' if success else '失败'}")
        
    except Exception as e:
        print(f"示例运行失败: {e}")

