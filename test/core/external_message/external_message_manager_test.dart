import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:turing_art/core/external_message/external_message_manager.dart';
import 'package:turing_art/core/external_message/handler/external_message_base_handler.dart';
import 'package:turing_art/core/external_message/handler/external_message_result.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';

// 测试用的自定义消息处理器
class TestMessageHandler extends ExternalMessageHandler {
  final Function(BuildContext, ExternalMessage) onHandle;

  TestMessageHandler({required this.onHandle});

  @override
  String get handlerName => 'TestHandler';

  @override
  ExternalMessageType get supportedType => ExternalMessageType.importProject;

  @override
  Future<ExternalMessageResult> handle(
    BuildContext context,
    ExternalMessage message,
  ) async {
    onHandle(context, message);
    return const ExternalMessageSuccess('测试成功');
  }
}

void main() {
  group('ExternalMessageManager Tests', () {
    late ExternalMessageManager manager;

    setUp(() {
      manager = ExternalMessageManager();
      // 重置管理器状态
      manager.reset();
    });

    tearDown(() {
      manager.dispose();
    });

    test('单例模式测试', () {
      final manager1 = ExternalMessageManager();
      final manager2 = ExternalMessageManager();

      expect(manager1, equals(manager2));
    });

    test('初始化状态测试', () {
      expect(manager.hasPendingExternalMessages, false);
      expect(manager.pendingMessageCount, 0);
    });

    test('初始化和注册处理器', () {
      var messageReceived = false;
      ExternalMessageResult? lastResult;

      manager.initialize(
        onMessageResult: (result) => lastResult = result,
        onMessageReceived: () => messageReceived = true,
      );

      final handler = TestMessageHandler(
        onHandle: (context, message) {
          // 处理器被调用
        },
      );

      manager.registerHandler(handler);

      expect(manager.handlers.length, 1);
      expect(manager.handlers.first.handlerName, 'TestHandler');
    });

    test('重复初始化应该被忽略', () {
      var initCount = 0;

      manager.initialize(
        onMessageReceived: () => initCount++,
      );

      manager.initialize(
        onMessageReceived: () => initCount++,
      );

      // 只应该初始化一次
      expect(initCount, 0); // 因为没有实际的消息到达
    });

    testWidgets('消息处理流程测试', (WidgetTester tester) async {
      var handlerCalled = false;
      ExternalMessageResult? lastResult;

      manager.initialize(
        onMessageResult: (result) => lastResult = result,
      );

      final handler = TestMessageHandler(
        onHandle: (context, message) {
          handlerCalled = true;
        },
      );

      manager.registerHandler(handler);

      // 创建一个简单的widget来提供context
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () {
                  // 模拟处理待处理消息
                  manager.processPendingMessages(context);
                },
                child: const Text('Test'),
              );
            },
          ),
        ),
      );

      // 点击按钮触发消息处理
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // 验证没有待处理消息时不会出错
      expect(manager.hasPendingExternalMessages, false);
    });

    test('清空待处理消息', () {
      manager.initialize();

      // 此时没有实际的消息，所以待处理消息为0
      expect(manager.pendingMessageCount, 0);

      manager.clearPendingMessages();
      expect(manager.pendingMessageCount, 0);
    });

    test('dispose 清理资源', () {
      manager.initialize();

      final handler = TestMessageHandler(
        onHandle: (context, message) {},
      );
      manager.registerHandler(handler);

      expect(manager.handlers.length, 1);

      manager.dispose();

      // dispose后应该清理资源
      expect(manager.hasPendingExternalMessages, false);
    });

    test('重置管理器状态', () {
      manager.initialize();

      final handler = TestMessageHandler(
        onHandle: (context, message) {},
      );
      manager.registerHandler(handler);

      manager.reset();

      // 重置后应该可以重新初始化
      expect(() => manager.initialize(), returnsNormally);
    });
  });

  group('ExternalMessageResult Tests', () {
    test('ExternalMessageSuccess', () {
      const result = ExternalMessageSuccess('成功消息');
      expect(result.message, '成功消息');
    });

    test('ExternalMessageError', () {
      const result = ExternalMessageError('错误消息');
      expect(result.error, '错误消息');
    });

    test('ExternalMessageProcessing', () {
      const result = ExternalMessageProcessing('处理中消息');
      expect(result.message, '处理中消息');
    });
  });
}
