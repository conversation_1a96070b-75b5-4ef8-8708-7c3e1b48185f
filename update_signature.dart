import 'dart:convert';
import 'dart:io';

import 'package:yaml/yaml.dart';

Future<void> main(List<String> args) async {
  if (args.length < 3) {
    //'Usage: dart update_signature.dart <signature_file> <xml_file> <base_url>';
    exit(1);
  }

  final signatureFile = File(args[0]);
  final jsonFile = File(args[1]);

  if (!signatureFile.existsSync()) {
    // 'Error: Signature file not found'
    exit(1);
  }

  if (!jsonFile.existsSync()) {
    // 'Error: json file not found'
    exit(1);
  }

  // 读取pubspec.yaml获取版本号
  final pubspecFile = File('pubspec.yaml');
  if (!pubspecFile.existsSync()) {
    // 'Error: pubspec.yaml not found';
    exit(1);
  }

  final pubspecContent = pubspecFile.readAsStringSync();
  final pubspec = loadYaml(pubspecContent);
  final version = pubspec['version'] as String;

  // 读取签名文件内容
  final signatureContent = signatureFile.readAsStringSync().trim();
  final regex = RegExp(r'sparkle:dsaSignature="([^"]+)" length="(\d+)"');
  final match = regex.firstMatch(signatureContent);

  if (match == null) {
    // 'Error: Invalid signature format';
    exit(1);
  }

  final signature = match.group(1)!;
  final length = match.group(2)!;

  // 验证length值，如果为0则尝试获取实际文件大小
  final actualLength = length == '0' ? '0' : length;

  // 构建下载URL
  final baseUrl = args[2];
  // 获取纯版本号（去除可能的构建号）
  final cleanVersion = version.split('+').first;
  final fileName = 'turing_art-$version-windows-setup.exe';

  // Nas上根目录重新分享地址就会变（不操作不变），目前是"url": "http://192.168.1.210/share.cgi?ssid=ee253e67ce8e4b99a720acf043794408&path=%2F&filename=turing_art-1.0.0+1-windows-setup.exe&openfolder=forcedownload&ep=",
  final downloadUrl =
      '$baseUrl&path=%2F&filename=$fileName&openfolder=forcedownload&ep=';

  // 读取并解析JSON文件
  final jsonContent = jsonFile.readAsStringSync();
  final appcast = jsonDecode(jsonContent) as Map<String, dynamic>;

  // 验证JSON结构
  if (appcast['pkgInfo'] == null) {
    // 'Error: pkgInfo not found in JSON';
    exit(1);
  }

  // 更新pkgInfo字段
  final pkgInfo = appcast['pkgInfo'] as Map<String, dynamic>;
  pkgInfo
    ..['url'] = downloadUrl
    ..['dsaSignature'] = signature
    ..['version'] = cleanVersion // 去掉+1，更简洁
    ..['os'] = 'windows'
    ..['length'] = actualLength;

  // 更新标题
  appcast['title'] = '最新版本信息';

  // 保存更新后的JSON，使用格式化输出
  final formattedJson = const JsonEncoder.withIndent('  ').convert(appcast);
  jsonFile.writeAsStringSync(formattedJson);
}
