# PgFlutterLintsRules

该仓库包含一组自定义的 Flutter 项目 lint 规则。

`analysis_options.yaml` 文件中包含了一组自定义的 lint 规则，这些规则旨在帮助 Flutter 开发者编写更加一致、可维护的代码。

`lints.sh` 文件是一个脚本，用于在 CI/CD 环境中运行 `flutter analyze` 命令，以确保代码符合规范。 

### 作为子库导入
在项目根目录中执行以下命令：

```shell
# 添加子库
git <NAME_EMAIL>:vsd/foudation/flutter/pgflutterlintsrules.git pg_flutter_lints_rules
# 初始化子库
git submodule update --init --recursive
```


### 导入 `analysis_options.yaml`

要将该仓库中的 lint 规则集成到你的 Flutter 项目中，请按照以下步骤操作：

在仓库根目录下的 `analysis_options.yaml` 文件中添加 `include: ./pg_flutter_lints_rules/analysis_options.yaml`。

### 在 CI/CD 环境中配置Merge Request检查

要在 CI/CD 环境中配置 Merge Request 检查，请按照以下步骤操作：

1. 在工程根目录创建`.gitlab-ci.yml`文件
2. 在`.gitlab-ci.yml`文件中添加以下内容：

```yaml
stages:
  - lint

lint_flutter:
  stage: lint
  script:
    - chmod +x ./lints.sh
    - ./lints.sh a5WLEQHZy4xBKRmDxzvU https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=71613f3b-4de8-4dde-999d-200aacd2da93
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
      changes:
        - "**/*.dart"  # 只在 Dart 文件变更时触发
```