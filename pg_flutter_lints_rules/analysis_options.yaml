# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - [build/**]
    - "**/*.g.dart"
    - "**/databean/**"
    - "lib/album/save_picture_data.dart"
    - "lib/views/home/<USER>/**"
    - "lib/views/home/<USER>/model/**"
    - "lib/json/base/**"
    - "lib/views/order/model/**"
    - "packager_launcher.dart"
    - "prepare_unity.dart"
    - "unity_binary/prepare_unity.dart"
    - "windows/packaging/copy_dll.dart"
    - "windows/packaging/exe/generate_iss.dart"
    - "prepare_unity.dart"
    - "windows/packaging/exe/**"

  language:
    #    strict-casts: true
    strict-raw-types: true
  #    strict-inference: true

  #  enable-experiment:
  #    - extension-methods
  #   自定义规则提示等级： ignore，info，warning，error
  errors:
    # treat missing returns as a warning (not a hint)
    #    missing_return: ignore
    # allow having TODOs in the code
    todo: ignore
    always_declare_return_types: ignore
    #    always_use_package_imports: warning
    avoid_relative_lib_imports: error
    #文件需要下划线小写命名
    file_names: error
    library_names: error
    #需要使用文档注释
    comment_references: warning
    slash_for_doc_comments: warning
    avoid_print: error

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at
  # https://dart-lang.github.io/linter/lints/index.html.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    - package_api_docs #公共API提供文档注释
    - type_annotate_public_apis #公共api需要声明参数类型
    - always_declare_return_types #总是使用包引用
    - cancel_subscriptions
    - avoid_slow_async_io
    - always_put_control_body_on_new_line
    - avoid_multiple_declarations_per_line
    - avoid_positional_boolean_parameters
    - parameter_assignments
    - unnecessary_statements
    - sort_unnamed_constructors_first
    - comment_references
    - slash_for_doc_comments

  # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule
# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
