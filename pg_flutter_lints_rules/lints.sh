# 设置 PATH，避免使用 ~
export PATH="$PATH:$HOME/.pub-cache/bin:$HOME/fvm/default/bin"

# 设置 FVM 版本
fvm global 3.19.6

# 获取 Flutter 依赖
flutter pub get

max_length=3500
echo "CI_API_V4_URL=$CI_API_V4_URL"
echo "CI_PROJECT_ID=$CI_PROJECT_ID"
echo "CI_COMMIT_REF_NAME=$CI_COMMIT_REF_NAME"
#CI_PRIVATE_TOKEN="a5WLEQHZy4xBKRmDxzvU"
CI_PRIVATE_TOKEN=$1
NOTIFY_URL=$2
echo "Fetching changed files using git diff..."
MR_ID=$(curl --silent --header "PRIVATE-TOKEN: $CI_PRIVATE_TOKEN" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests?source_branch=$CI_COMMIT_REF_NAME" | jq -r '.[0].iid')
echo "Merge Request ID: $MR_ID"
CHANGED_FILES=$(curl --silent --header "PRIVATE-TOKEN: $CI_PRIVATE_TOKEN" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests/$MR_ID/changes" | jq -r '.changes[].new_path')
echo "Changed files:"
echo "$CHANGED_FILES"
fileList=""
while IFS= read -r file; do
  if [[ "$file" == lib/* && -f "$file" && "$file" == *".dart" ]]; then
    fileList="$fileList $file"
  fi
done <<<"$CHANGED_FILES"
if [[ "$fileList" == "" ]]; then
  echo "No Dart files found in the changed files. Exiting..."
  exit 0
fi
echo "Running flutter analyze on the following files: $fileList"

#执行 dart format 检查代码风格
format_output=$(dart format $fileList --output=none --set-exit-if-changed)
format_exit_status=$?
if [[ "$format_exit_status" -eq 1 ]]; then
  formatted_output=""
  total_lines=0
  analysed_lines=0
  while IFS= read -r line; do
    ((total_lines++))
  done <<<"$format_output"

  while IFS= read -r line; do
    if [[ "$line" == Changed* ]]; then
      formatted_output="<font color='red'>$formatted_output${line#Changed }</font>\n"
    else
      # 普通输出，不做格式化
      formatted_output="$formatted_output\n$line"
    fi
    if [[ ${#formatted_output} -ge "$max_length" ]]; then
      ((result = total_lines - analysed_lines))
      formatted_output="$formatted_output\n🚨 还有 '$result' 条问题，可以本地执行 dart format 查看"
      break
    fi
  done <<<"$format_output"

  echo $formatted_output
  json_payload=$(echo '{
        "msgtype": "markdown",
        "markdown": {
            "content": "## <font color='red'>代码风格检测不通过</font>\n'$formatted_output'\n<font color='red'>🚫 Merge Request已被阻止 请在本地运行`dart format`命令格式化代码后再提交代码！</font>"
        }
    }')
  echo $json_payload
#使用 curl 发送请求
      curl "$NOTIFY_URL" \
          -H 'Content-Type: application/json' \
          -d "$json_payload"
  exit $format_exit_status
fi

# 执行 flutter analyze 并将结果存储到变量
analyze_output=$(flutter analyze $fileList --no-fatal-infos --no-fatal-warnings)
# 获取 flutter analyze 命令的退出状态码
analyze_exit_status=$?
# 检查是否有分析问题
if [[ "$analyze_output" == *"No issues found!"* ]]; then
  # 如果没有问题，发送绿色字体消息
  json_payload=$(echo '{
        "msgtype": "markdown",
        "markdown": {
            "content": "## <font color='green'>Flutter Lints检测通过</font>"
        }
    }')
else
  # 初始化格式化输出变量
  formatted_output=""
  total_lines=0
  analysed_lines=0
  while IFS= read -r line; do
    ((total_lines++))
  done <<<"$analyze_output"

  # 处理每一行分析结果
  while IFS= read -r line; do
    ((analysed_lines++))
    if [[ "$line" =~ "Analyzing flutter_lints_ci_sample..." ]]; then
      formatted_output=""
    elif [[ "$line" =~ "warning" ]]; then
      # 黄色显示 warning
      formatted_output="$formatted_output\n<font color='orange'>$line</font>"
    elif [[ "$line" =~ "info" ]]; then
      # 正常显示 info
      formatted_output="$formatted_output\n$line"
    elif [[ "$line" =~ "error" ]]; then
      # 红色显示 error
      formatted_output="$formatted_output\n<font color='red'>$line</font>"
    else
      # 普通输出，不做格式化
      formatted_output="$formatted_output\n$line"
    fi
    if [[ ${#formatted_output} -ge "$max_length" ]]; then
      ((result = total_lines - analysed_lines))
      formatted_output="$formatted_output\n🚨 还有 $result 条问题，可以本地执行 flutter analyze 查看"
      break
    fi
  done <<<"$analyze_output"

  echo $formatted_output
  # 创建包含 Markdown 内容的 JSON
  if [[ "$analyze_exit_status" -eq 1 ]]; then
    json_payload=$(echo '{
            "msgtype": "markdown",
            "markdown": {
                "content": "## <font color='red'>Flutter Lints检测不通过</font>\n'$formatted_output'\n<font color='red'>🚫 Merge Request已被阻止 请修复Error级别问题后再提交代码！</font>"
            }
        }')
  else
    json_payload=$(echo '{
        "msgtype": "markdown",
        "markdown": {
            "content": "## <font color='orange'>Flutter Lints检测存在警告</font>\n'$formatted_output'\n🔧 请检查并修复这些警告，以保持代码质量！\n💡 可以尝试使用`dart fix --apply`命令一键修复部分问题 但是该命令影响范围较大，请谨慎使用！"
          }
      }')
  fi
fi
#使用 curl 发送请求
curl "$NOTIFY_URL" \
    -H 'Content-Type: application/json' \
    -d "$json_payload"

exit $analyze_exit_status
