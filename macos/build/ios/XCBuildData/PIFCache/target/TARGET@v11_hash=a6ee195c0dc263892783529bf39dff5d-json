{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c2138fdb091c986895d04636b52aebbb", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98970b0d6b9aad0f67447a5182f88da5aa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884465cafb74639ea2b1c407ad1b459af", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9803ca0786095a7d0d9211086c02f17a13", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884465cafb74639ea2b1c407ad1b459af", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9806a16e6f0c663a44af2ae5cd89fbc33a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dd04876447c957d4530713dfcc1c60ea", "guid": "bfdfe7dc352907fc980b868725387e98de58774f443efec449bcda72c01e80b1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98264503f5c772d7a60f09de1e52eb2a7f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e196bdd4fa9f86c25525c4bb402da06", "guid": "bfdfe7dc352907fc980b868725387e98384866e1d8476a997c3fa4b9045bc419"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0c1fa8d1d64c4a147828f1706830c6f", "guid": "bfdfe7dc352907fc980b868725387e987c15ac8b6180db57a02a8ad1e19f042a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833bab0fdfa3f3b118920dd5abe7e080e", "guid": "bfdfe7dc352907fc980b868725387e9897a55090d62be23f8c1bfe74de09b6a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe87f53416f29a018710df308353d11a", "guid": "bfdfe7dc352907fc980b868725387e98d52ae5f974068823998390de4471c1b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bcc3d50376d5e4492b7cee467baf343", "guid": "bfdfe7dc352907fc980b868725387e9806c765a1834631001a34064cba29d5eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7ccc46fdcccf575a564f694a34aa906", "guid": "bfdfe7dc352907fc980b868725387e989e5bf7fec4a8b29ec5d31f7fced33ac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a09484c0e98529a175a56ae637141d74", "guid": "bfdfe7dc352907fc980b868725387e98e819519f84ec1d10f660a42167e82457"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98962520d6437fddf0f6b3da15639b14c4", "guid": "bfdfe7dc352907fc980b868725387e98325b2bc51c54433f0b13eac305b1c14d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982feee90fe522aa3ef44879e02dcd8b96", "guid": "bfdfe7dc352907fc980b868725387e98c2a68c96e594259354731fd7005083c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98988b35e65f814db331632c1f2549b57c", "guid": "bfdfe7dc352907fc980b868725387e98c85c63d1442715f6e4805c7bb584a797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eebda73ceba97f8e200453b2df4eae3a", "guid": "bfdfe7dc352907fc980b868725387e98c4467278ffba9aac5f6421b1c0783b07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985758b78ed034cdb96623df7594cc862d", "guid": "bfdfe7dc352907fc980b868725387e9872d23e042b0b86b2ae2a099a9b8cd1a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983212c4947167f06233f4ec901bc006a2", "guid": "bfdfe7dc352907fc980b868725387e98d35283fc951a5101f8305e7cdf40bba3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827218fbdb2a3b9331bad40d9b145b411", "guid": "bfdfe7dc352907fc980b868725387e98bd4da5aa7a1b8ffdc5e7249d83e53323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982704ef47789d4b602c64fb84dd5d138f", "guid": "bfdfe7dc352907fc980b868725387e98a7c6e9581a12194d27b64df6f64ab181"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837161f27f03671bab6fdb32bed155595", "guid": "bfdfe7dc352907fc980b868725387e98bb28e3e3f96b1659797c4546c71a2a35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f99235ad792d0e28719132b32415e4", "guid": "bfdfe7dc352907fc980b868725387e984711c8bcec23fe509753234aeee19c6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b5ca072fc32c78bde141f760f444637", "guid": "bfdfe7dc352907fc980b868725387e986e61f2a87afff7a47952568f0f842543"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860a11968fdca5c76f98247f012b0cbe7", "guid": "bfdfe7dc352907fc980b868725387e98e40a19e74235a8b9f1d0874141e9eed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f24f849018af44d56ae808d8db163dc", "guid": "bfdfe7dc352907fc980b868725387e98ed5ee2a3d19ec568ec22ba1cba402ac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c7ef1e72243910064e78f9e5cd79df4", "guid": "bfdfe7dc352907fc980b868725387e985a8f73363a8f5268f31f6022b528907a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2be59f03d23553c30660b0323d7021", "guid": "bfdfe7dc352907fc980b868725387e98059f5e75108995c58eb7c9edbfabcd5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7cffaddac859c60ff5bc26994b826ef", "guid": "bfdfe7dc352907fc980b868725387e98ff689302085d858d2fd843d946938520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897cbe36bdcbd6e2d292574c644fce2c0", "guid": "bfdfe7dc352907fc980b868725387e98fda875c8ee43d3c4f949928c04c79b3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98539c55872266b917555ddc3a5b0493d5", "guid": "bfdfe7dc352907fc980b868725387e984dbcea220cf5fa89f4522f89b23da965"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981354af716d93e691088c59aec6da7406", "guid": "bfdfe7dc352907fc980b868725387e981780e3ad01af88ee72c20d05eaf68616"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98430697018c7af534307df3f541006b6e", "guid": "bfdfe7dc352907fc980b868725387e98490f382b3ac05e952dfaf2b3ca93e9ab"}], "guid": "bfdfe7dc352907fc980b868725387e98197416fd79412e696f352bd6d6c530fa", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9822fb8237664403c213acb093d8c8bacb", "guid": "bfdfe7dc352907fc980b868725387e980dd2b5cc8534efd8f387d73e2988f84d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869fb212e5b710f50a207fa33821e9d20", "guid": "bfdfe7dc352907fc980b868725387e98cd199ee3eab57e6903c3669c196987ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b946935a29c1086a5c8e667cd8d4345", "guid": "bfdfe7dc352907fc980b868725387e98cec3094c33e1d5cfb4723eee2fe1cced"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98584452894866f582ee1bef3ca4f1354c", "guid": "bfdfe7dc352907fc980b868725387e98cd2f9a0c4ad2672403a87e7ea672c731"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5678b7efb4c8d9136bbd7dded1bb395", "guid": "bfdfe7dc352907fc980b868725387e985f0c429ebf33c7900964e1f42f3cf993"}], "guid": "bfdfe7dc352907fc980b868725387e985ccad632265b554fa2da315572292087", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b1391e019199f84eb0896a38b37ee5f2", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e984e6256d91ccab934596d6503f198a2d7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}