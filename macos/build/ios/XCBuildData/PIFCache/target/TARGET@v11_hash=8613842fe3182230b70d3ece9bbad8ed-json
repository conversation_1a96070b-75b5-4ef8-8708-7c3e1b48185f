{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98281902f2a4292960f8d2f58c1b87982b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d3926596ad3cd9c175f9e28b372b54b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871d337753432e9816ec2c7751a172f40", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98288f36904c064e8ffee3307684f0adf4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871d337753432e9816ec2c7751a172f40", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ad21424d04da79e5b6b09fe13bb9d64", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823087e452329fa5285fe5e50ee21a895", "guid": "bfdfe7dc352907fc980b868725387e98c2ba2564738076858fe79d7046297cea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822074fa8479e249085cd9105c59fac88", "guid": "bfdfe7dc352907fc980b868725387e98853b0e87dffa71f5b58aa19e5d8456db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc74bf337b370d101f3459cd10413a6f", "guid": "bfdfe7dc352907fc980b868725387e98c0ffabfaa47270a182eb861dcb3c97ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825a4909eded838519cf7f877b709345b", "guid": "bfdfe7dc352907fc980b868725387e98e4280c00d9dacc0d029f2a76fae53742", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c99166c67a86d60a9790052db72806", "guid": "bfdfe7dc352907fc980b868725387e984ea3eecb0cd4d74cb5250b05948b72c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98130ded6020788eb66910e4249ea1b6c7", "guid": "bfdfe7dc352907fc980b868725387e98b7ca25109c8d0cd0e6ec9cf85e326f07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd3bd24f2f5c06f8e7dab96ce7ed133", "guid": "bfdfe7dc352907fc980b868725387e982b00b08c80673b7ac7763b5cca0c05c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e15b76dce26036eefe03a178423e8cd", "guid": "bfdfe7dc352907fc980b868725387e9824f69690574f55583d0ec1eac6446085", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5740a7dbc70a6a1a06f2a65880d452", "guid": "bfdfe7dc352907fc980b868725387e98a3d6ceca4889b1cc6dce1a9db4b8f3b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98013ae07a0b77f6ce7ab19d24cf6f79aa", "guid": "bfdfe7dc352907fc980b868725387e980dcd742a45bc2b581179f4f31e84335f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98910c783bc260be3eb371a358c1a5341f", "guid": "bfdfe7dc352907fc980b868725387e988afd726ea2439bce89d32737dd29861e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bc1024dbc8122813683dcdb72447db9", "guid": "bfdfe7dc352907fc980b868725387e9837dc2cb26d47f125c3bfc8a42987c140", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2bb97dae8dcf1a47d7208fc33a86bc0", "guid": "bfdfe7dc352907fc980b868725387e984f13f038c098389b2cc3a7970f1ecf21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b24ea562f96c15767e495cf180af05f", "guid": "bfdfe7dc352907fc980b868725387e98f57fd05847d719d2fed620540c65bc5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ee8f7369cfa8f4956d4ec476dc7634b", "guid": "bfdfe7dc352907fc980b868725387e989d5a9b3c658ee5b10e84d06e2bf55ef9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866d1a5e4b702ff6caa2b9e98998b3388", "guid": "bfdfe7dc352907fc980b868725387e984df56e010584a7b7a75ec6e43e8e094b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986401cc3b90769e13b7bf76d5b0ce42af", "guid": "bfdfe7dc352907fc980b868725387e98a581ec0675bf26a52652879cab6ccb16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8975b0cd43c7d2aa566da6360bf80f8", "guid": "bfdfe7dc352907fc980b868725387e987b9e081c1f47b9b5c71ff6b7a6741b5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b999bac4459d9ef6974981781642279b", "guid": "bfdfe7dc352907fc980b868725387e98001b4d9e2a68f9da26c9df0f8dd22711", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf1b79db3041b81d72c0986205bd0e79", "guid": "bfdfe7dc352907fc980b868725387e98fd50f07295abcf055b5e4b7b2cb093c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ca82a2ce8564d8bb20ba89404e56c5", "guid": "bfdfe7dc352907fc980b868725387e98ee7c0655d6fdf9bb0b15a2564e3001b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e051a311761cfc8cd22f22e5ed195407", "guid": "bfdfe7dc352907fc980b868725387e9879f560ef014af1116b607e66871f8a4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de7b6755f3546c0a105164347db327f1", "guid": "bfdfe7dc352907fc980b868725387e983f72bbb6c37b6e76a32b4a1b51eac00f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed4bf3d4d42cfcb892089c5e09ad80f3", "guid": "bfdfe7dc352907fc980b868725387e987a6b0f9f4cd20dab7e492387fd96c668", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981664dfc7329363764fdacd5d07a0c8e8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984dde65771bf8dd9666f9770522699460", "guid": "bfdfe7dc352907fc980b868725387e982d80efaa8cac804b67f5522a9e5f081e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98727eff6df6c11d4c6bc6603792316e40", "guid": "bfdfe7dc352907fc980b868725387e98edb3b316fe792e12b5f399ed436ec453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a18292c8a75b80657ead99b2b8eb2217", "guid": "bfdfe7dc352907fc980b868725387e98205a3f2f01abd99df7d670b1e4bc5b4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1aa32f6e5066565542c6fedfebc245d", "guid": "bfdfe7dc352907fc980b868725387e98762ff973933a5ef20806850da244cc91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a1c98915af26808c16b67cec09fec8", "guid": "bfdfe7dc352907fc980b868725387e9870291f334a3b4193c45c3ec4143223a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb9fff0c91929eedf482ea68feb577dd", "guid": "bfdfe7dc352907fc980b868725387e98d0fad16d3d7fdee4d59393a157398d1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870ff24453a6eab1786aa9961280ca03a", "guid": "bfdfe7dc352907fc980b868725387e98e0db079f9fbde5fd4c4b8540dc0da824"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f05d498a377b170744e9585bf4598ee", "guid": "bfdfe7dc352907fc980b868725387e98c8ce60cef79a7ce69fc2662a318d67f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abb968916a58cad61c45d611a1d5fadb", "guid": "bfdfe7dc352907fc980b868725387e9812895a73a0e03eaf508ddd3fb0d0c01f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51d42629e597b7fad6aa59344ab4ded", "guid": "bfdfe7dc352907fc980b868725387e989c97da061777c23cf2387296b895a4e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddd262c47c28b47c7916c32879d597fe", "guid": "bfdfe7dc352907fc980b868725387e98f02f600f64d58e0f7d4eecbc25f04c7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0ba1fbb1029333f645a6635a575db83", "guid": "bfdfe7dc352907fc980b868725387e984c6a003f82bd71c2daa58df7da0f74fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ca42b76e7a9df94b33ea78a6c138aa9", "guid": "bfdfe7dc352907fc980b868725387e9820a1666d38ed470527e26d3b96a3fec9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812874b7b433e1c8fdfedaaffd0c1983e", "guid": "bfdfe7dc352907fc980b868725387e98cd466a315222b40faa33fc6bdc1fae43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832608a23dad9e8580df85104c2870e87", "guid": "bfdfe7dc352907fc980b868725387e98950a4366924646dd7ea62e7a738fc23f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ba306192b1cd82a1c84be703ef7e111", "guid": "bfdfe7dc352907fc980b868725387e9830f1cedde3230afa0f52f0983ff82505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0113039bbb800bbe040908b4a4afe84", "guid": "bfdfe7dc352907fc980b868725387e988b75dd9cae97808bca73ac3a51cbcd92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce934cddbeb8df4452a93632550e099c", "guid": "bfdfe7dc352907fc980b868725387e98bc69122c6991dc655225143252842d7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98531356615478822b3ecc8b389169978f", "guid": "bfdfe7dc352907fc980b868725387e985ad6ea797f8a9f84792fc3790ec5062f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7eb960de5f07827197d1fd8c05e54e2", "guid": "bfdfe7dc352907fc980b868725387e98895c6bd97422a8fbaa2f027ff69d57eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15591d63f8232477e8b6a18e87b0b1d", "guid": "bfdfe7dc352907fc980b868725387e98634b2be6a9361bf9e9f27f008ca68dd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984937d722a247453ac491a1522d6a9e47", "guid": "bfdfe7dc352907fc980b868725387e9836e395285a4b0a2a22da5efef5807372"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec0f55855b27103b8f63e7bd57a593d1", "guid": "bfdfe7dc352907fc980b868725387e9830d50d3b8dada408fbd5ac3ca1c576d1"}], "guid": "bfdfe7dc352907fc980b868725387e98339f3c5c18cf261c37731f1ff9cd3c6b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b946935a29c1086a5c8e667cd8d4345", "guid": "bfdfe7dc352907fc980b868725387e98f76f428393514513ee220ee3cd45b4b7"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9e1d75d6fd474380329cbf47d24d45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98911b19d9ae38fc82e6a73bfd4cf29ba7", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ee52bb1ad32275d40a0a37ff2d7e9c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}