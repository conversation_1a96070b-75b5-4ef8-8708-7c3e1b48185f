{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d0d8a4d6fa86202a917c838d2b10ea2", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984dabebdf6f54c881cf6ce3f503dbafc8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b122ecd2ad4e44d9df65567d019ef154", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879370f9b6762ae3b167643fc360811ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b122ecd2ad4e44d9df65567d019ef154", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e020dca05c43366d73fdccc591333ac8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e2a91cacd49d3f5270912f2df316411c", "guid": "bfdfe7dc352907fc980b868725387e988d2f8bbf26c594c98b6f4928ae02efe6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ea361425b5ce6327840e4d955f5abb2", "guid": "bfdfe7dc352907fc980b868725387e9823c75426730e8833045f2965bb85e972", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c7bb1f0bb70c4a80fefeaadbaaed57a", "guid": "bfdfe7dc352907fc980b868725387e9840a175f210d8b2aa1a7faa7265e98e62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f2d7628348a5381a83efeaac3c24f75", "guid": "bfdfe7dc352907fc980b868725387e981829acc0f75fb1485833b4e7137f9acf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885fe431a64d6075b8dce3836d0cadd04", "guid": "bfdfe7dc352907fc980b868725387e988f310d5095c8de7ef073460e53445a95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982041b502a6f1e3ca4bfa3dff6f5d4a54", "guid": "bfdfe7dc352907fc980b868725387e9887f0c60e1ef68bf38dd2a564f13f9f93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb8e35d6261629c42aa2642fac7dc08e", "guid": "bfdfe7dc352907fc980b868725387e98776d177df37e4b0c3d25e27b68fc54bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b372afb8906a54ae10d73bebf968e3a2", "guid": "bfdfe7dc352907fc980b868725387e985abad7bd5ec93d1c93ec056d0cc4f3ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e14bd8e7fac5d5154e9d6789c400543", "guid": "bfdfe7dc352907fc980b868725387e98f4fa7160f67c647e683f77d7ec7ae8d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df02a5033e5453fd5ec9a2539c5815b1", "guid": "bfdfe7dc352907fc980b868725387e9846e086a74ea1013ada1b9ac7bf3b430c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819d0b17c36bc77e9072ade2ed17470b1", "guid": "bfdfe7dc352907fc980b868725387e98aca44c1cf6dbb1a2430f05fcbc9c5846", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989be5f7596f56e8275619bb1e13591969", "guid": "bfdfe7dc352907fc980b868725387e98650886e5b6e097ff53da993dd5a86dd8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec6f5b55f8866df8e54efac51495d285", "guid": "bfdfe7dc352907fc980b868725387e98b1788596d501426f90e971d15ee979af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f627185222c2ee79220c07bf5b276f7", "guid": "bfdfe7dc352907fc980b868725387e98e33b6befbabaa5277d19561aa67db0dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e4dabd057f9ff4f2b7b006a5d5389a", "guid": "bfdfe7dc352907fc980b868725387e984eac53ad5f16d0cecabab514d9175547", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a16eba2850942dd59c451882077f780", "guid": "bfdfe7dc352907fc980b868725387e98ea4d5989086c6c325ecd072bc93b62d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867eba63f6796c641af7190f388595108", "guid": "bfdfe7dc352907fc980b868725387e984d1b9f399dd6f4bf61f4cdb34808fe89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ed7acaa7df565ee5e96d2f5506a0c38", "guid": "bfdfe7dc352907fc980b868725387e988475e861e2463e4f90b410be7cf10ff6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98392e82895502a9411e1e9db27d25aaa2", "guid": "bfdfe7dc352907fc980b868725387e98a1471ae73dd890c59fba11dd407f7886", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa603eff901618230fd7f313d6d9af07", "guid": "bfdfe7dc352907fc980b868725387e984ee120bf6e73c6fcfc41ea4d696b9d9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e05faf1b2f0cd0cdf7bdcd729214ce1a", "guid": "bfdfe7dc352907fc980b868725387e98ac0c5a907d8552a13e4b5499ae36e203", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812a179afbbd05b20787937e30095255d", "guid": "bfdfe7dc352907fc980b868725387e98998271e00c5335804c5b1111c0dbbde7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98157c86ef397f35ac057dafbb8d9f1c7b", "guid": "bfdfe7dc352907fc980b868725387e985c652753b730d44ad5f9b45e352206e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a141d18bfd0103c21dedeb8347e45ef", "guid": "bfdfe7dc352907fc980b868725387e98671015b727f3b0f1f02a826107be2901", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2b9db72695f4feb55ab83a91df1c5e6", "guid": "bfdfe7dc352907fc980b868725387e988f85ef7491647985b1bdb26d4d21dd66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e5e6661ccad5243a9e364d87cb88029", "guid": "bfdfe7dc352907fc980b868725387e98d435f08b36413e43eeca253b081a1a49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888529c62fa0a9d7fe044ee039a4d83a2", "guid": "bfdfe7dc352907fc980b868725387e98326ad20f10ca54bd4bc945acd9d645a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841729d9e90cafeb283c1e5ea4b142e79", "guid": "bfdfe7dc352907fc980b868725387e98a80774e711bcf647668e64b28f34155b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c4eb01303aee8011b8981fe358507c", "guid": "bfdfe7dc352907fc980b868725387e98dbc926895fef88f0ae6877f3b37c9fd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebe58f77c600f8fcde9317db82c627c4", "guid": "bfdfe7dc352907fc980b868725387e982fac18b6a45ddabfa214d8406e1badb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880e1bbdf9164facc112fd21815cf56de", "guid": "bfdfe7dc352907fc980b868725387e98b8d8cbf8776d257d81628820e3053309", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9850d0827f95b51d4b1cdee62dc0ff2ebb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984387e2dc61e0efd861c07bdc1a4bc18b", "guid": "bfdfe7dc352907fc980b868725387e98d1abfc21e3adc3ce8da8f3915a2fdb99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98455cef5f030dc4ef39e8d2ea6c7ae93c", "guid": "bfdfe7dc352907fc980b868725387e98338e44eefcb6093ef2544bf28744f1a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c4725bdb00f24cc153cb96c7abdcbaf", "guid": "bfdfe7dc352907fc980b868725387e98e6cfa29c58fcd208653048985b267eab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ff4f1000a98d5af2a80b087b2cc3fa9", "guid": "bfdfe7dc352907fc980b868725387e98e24f019e5b1818bb37e5589d7553105f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891727d6a899422b0faaf7c3d87b9c16b", "guid": "bfdfe7dc352907fc980b868725387e98d728a902a18589514fa03bcb37ddbaf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a5b244333082d59d6fa06a497b89f66", "guid": "bfdfe7dc352907fc980b868725387e9848301141b51218205a2ff0abaef22a42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cbd23ecc829a25b432b000cf5f82f01", "guid": "bfdfe7dc352907fc980b868725387e98351d6b38a0f316d4129b7c96b514be06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9b8e1482383ece004fe81118de5d484", "guid": "bfdfe7dc352907fc980b868725387e9868d005f8962a02692e492e8e794025c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc6ab05c7efd6870d35b695ca3c75ba2", "guid": "bfdfe7dc352907fc980b868725387e98fa0d9c120af3dfa5456a60105332acc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98619f23b999b7e2ba91b18a0a618b528c", "guid": "bfdfe7dc352907fc980b868725387e98ed563f4000e39d0e2b6df29a30e68d4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832bf106f7e14452320a59f319f0e2258", "guid": "bfdfe7dc352907fc980b868725387e986a8b8d1c89d221dfd5d86085a3c0fd43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce88fcae7d4cc9d6da65eb0182b93b5c", "guid": "bfdfe7dc352907fc980b868725387e980b4be48345457771014751d80098a35c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f01e5db46bcc026690dd750e11234f95", "guid": "bfdfe7dc352907fc980b868725387e984964e9b1a279ee9b163227873c8079da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4e454a67228a7044f6701cc9b4c129b", "guid": "bfdfe7dc352907fc980b868725387e9843cb7b42438b7aa1df59890ddb9af281"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d2b65da2e9cdccd5c4ff35769a0cc50", "guid": "bfdfe7dc352907fc980b868725387e98c8d370dda397113b434a36fd3157b6b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d060076b33d9a015cb64cef03ef8280b", "guid": "bfdfe7dc352907fc980b868725387e9850ef062a65a92b20ef44b71f6fe47bf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98597d5699fb657b6ccc1beb708d8e85a4", "guid": "bfdfe7dc352907fc980b868725387e9885ac30ec12f50a28b1c955b8fac8cdc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d368538e1cb9b9883ae01154e93585a1", "guid": "bfdfe7dc352907fc980b868725387e98836fa7a360dcbb57fd56b17a10775571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840bb4467b8ba1da848d4630ebcfcc859", "guid": "bfdfe7dc352907fc980b868725387e98fa7e2e08b22320013dec284bef40aa05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecb3eaa13d49eb697c59b862efcbeb94", "guid": "bfdfe7dc352907fc980b868725387e98bffbe99767aac743ce854232b77a7218"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575a44dcef6af7389ff22d23f49b5805", "guid": "bfdfe7dc352907fc980b868725387e98688ea55b78d4ffe4ce64ce34e4d4ac41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98028258270fffbcb075898db59cae9b77", "guid": "bfdfe7dc352907fc980b868725387e980f2a8ffd2bb5fdf394f921c2bd015f13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98770c33f338e1f61319536ef4b2fa8a90", "guid": "bfdfe7dc352907fc980b868725387e98fc5ec96928813319eee300b8237568e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdd194c586434cc287ac9fc6ae1d84cb", "guid": "bfdfe7dc352907fc980b868725387e98f65094bbc502574f688c2ae6b3cf2a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982293c68ebc4820d966514b835d918906", "guid": "bfdfe7dc352907fc980b868725387e9814755a9a664ec7de6c4cf931d2326ba8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876c1ee851b6911e6cf3d5d620ca30f57", "guid": "bfdfe7dc352907fc980b868725387e98ef6cb51f0e559c57036ca596189e0d43"}], "guid": "bfdfe7dc352907fc980b868725387e98320e1412584a90fd41da063c83d2dd21", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b946935a29c1086a5c8e667cd8d4345", "guid": "bfdfe7dc352907fc980b868725387e98c66fe7d2ba435345befdf4d53ffe9324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e099f06c85de7041aadfd7f195afba5", "guid": "bfdfe7dc352907fc980b868725387e9837f82c7d5501ad000577f511c7066743"}], "guid": "bfdfe7dc352907fc980b868725387e986639f5be6da1c90ed3fe0682aa68698f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98569e89e49d03cc7e5bfa84b901e57021", "targetReference": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de"}], "guid": "bfdfe7dc352907fc980b868725387e9878595d290b1af8130556a3317b477895", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de", "name": "photo_manager-photo_manager_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cd1e97b2c7c0c2a96b4035a4c62b427d", "name": "photo_manager", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9841b881a585d538cf3da17a18e8b8ed12", "name": "photo_manager.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}