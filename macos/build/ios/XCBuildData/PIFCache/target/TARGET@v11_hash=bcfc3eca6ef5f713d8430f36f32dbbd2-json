{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989359292d998f09556311936e42a3c489", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982b843908da84eff1851a2ebcbf6e28f0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98253190664714c69fb461cb9e528a5313", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9874dcef30c4fcba687f854c09bc266b28", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98253190664714c69fb461cb9e528a5313", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHOTOS=1 PERMISSION_CAMERA=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98f122ee5ea69eeecc0d899863632459a7", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98dfe88eb8a86ca01f7542524c7e874dec", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983c6d3f96664beff2bd83575574af61ac", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982bb655663463bd7d0d5656a6e2e6773d", "guid": "bfdfe7dc352907fc980b868725387e98d3a850d53a1af79798c4a428a26a54b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98659b64c825e3ad829d860ee47b055f29", "guid": "bfdfe7dc352907fc980b868725387e984491e7d4a22bd196dd5df0d0e43e7369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c6dfe28f4493ad40345ff9a0824b196", "guid": "bfdfe7dc352907fc980b868725387e9865400a7fb5cbc2f06e3fd31f78586dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b69b910f871e70ac49cd5608e516055", "guid": "bfdfe7dc352907fc980b868725387e98db64d3481859fef4c6a21726273a0264"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed1ee3ccbf7e0da14b0b031e19ea8f11", "guid": "bfdfe7dc352907fc980b868725387e986def52951bccca77242458ea220df571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d48f1688de056527d4feff646c9c3e4", "guid": "bfdfe7dc352907fc980b868725387e98cf9b4c9ec6fd65071d1b74f127114995"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98187a156540d270257ca9f8cc15539d62", "guid": "bfdfe7dc352907fc980b868725387e98dee7564d53ce7de9f78a485bd4cd3543"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882270117474ea11f7353bba27c2400e3", "guid": "bfdfe7dc352907fc980b868725387e98c67f040ce0bb44bc90807a87b4205230"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa7b3e8d06c26d46fe1d1ecf1afd098c", "guid": "bfdfe7dc352907fc980b868725387e98d5e231e087aeb3faa151a92f039869d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98750ec6e2234ad3ed8be0ed4b469dffd2", "guid": "bfdfe7dc352907fc980b868725387e9833464aeb71408ad6e3c71cbca59893c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98714c353ebff5450eb0450d2eaa8a7698", "guid": "bfdfe7dc352907fc980b868725387e98d6c0a426e581ba00ae4e23f5263f0321"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891727fc31dc4e6b643e00a82abb85f7e", "guid": "bfdfe7dc352907fc980b868725387e980d93b0de03be224abaa6be7e38b1cb10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cef13ef3141a6fc508f1f3484e87c748", "guid": "bfdfe7dc352907fc980b868725387e98be3e2b648e39694af754bbf53fed9c45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd1eac9a7ecb79b8bc5033207d94d86", "guid": "bfdfe7dc352907fc980b868725387e98d0ae667e95205b1c337c62a41412ff24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98963ac7cc29f5aeda4f71ab1d433e3219", "guid": "bfdfe7dc352907fc980b868725387e98a941366175d581f9b500887796cbf19b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847780a3636627bff9a55be674c4dacbb", "guid": "bfdfe7dc352907fc980b868725387e98b606197b997ef951132d725a0c82cf87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd659c81be0675959c082d68d30f7646", "guid": "bfdfe7dc352907fc980b868725387e98280f68adeffb4ae4654d57567e5b9601"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec88a52e9f782903de17918515fc2145", "guid": "bfdfe7dc352907fc980b868725387e9841113b6e8b9f343d16f7bc5127ae8316"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b14cad006732fcb1956cc68bb17c4d5", "guid": "bfdfe7dc352907fc980b868725387e989346f31b155094d8a71d74df11fb1e6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98708cad8ef8c78cd5a3c1f365853056ab", "guid": "bfdfe7dc352907fc980b868725387e98f78fab820e7ece2b94f3c7a4911ed86d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ac32c16abe2977ba82e3ffc2cc93b3f", "guid": "bfdfe7dc352907fc980b868725387e987188c7aa0dd02a1996beb845572146a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894ad7093fbaaefcc06dd5310e772089d", "guid": "bfdfe7dc352907fc980b868725387e98135cfb658340587e75e97ad5be3e8edb"}], "guid": "bfdfe7dc352907fc980b868725387e9832cd95df8ddbab640f38a0e96a5193e3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}