platform :osx, '10.15'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'ephemeral', 'Flutter-Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure \"flutter pub get\" is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Flutter-Generated.xcconfig, then run \"flutter pub get\""
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_macos_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_macos_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_macos_build_settings(target)
  end
  
  # Copy SalientMatters library file for macOS
  salient_matters_lib = File.join(File.dirname(__FILE__), '..', 'lib', 'native', 'salient_matters', 'macos', 'PGSalientMattersLib.dylib')
  
  if File.exist?(salient_matters_lib)
    # Copy to different build directories
    ['Debug', 'Profile', 'Release'].each do |config|
      target_dir = File.join(File.dirname(__FILE__), 'build', 'macos', 'Build', 'Products', config)
      FileUtils.mkdir_p(target_dir)
      
      begin
        FileUtils.cp(salient_matters_lib, target_dir)
        puts "✅ SalientMatters library copied to #{target_dir}"
      rescue => e
        puts "⚠️  Failed to copy SalientMatters library to #{target_dir}: #{e.message}"
      end
    end
  else
    puts "⚠️  SalientMatters library not found at: #{salient_matters_lib}"
    puts "   Please add the PGSalientMattersLib.dylib file to lib/native/salient_matters/macos/"
  end
  
  # Copy RawConversion library file for macOS
  raw_conversion_lib = File.join(File.dirname(__FILE__), '..', 'lib', 'native', 'raw_conversion', 'macos', 'PGRawConversionLib.dylib')
  
  if File.exist?(raw_conversion_lib)
    # Copy to different build directories
    ['Debug', 'Profile', 'Release'].each do |config|
      target_dir = File.join(File.dirname(__FILE__), 'build', 'macos', 'Build', 'Products', config)
      FileUtils.mkdir_p(target_dir)
      
      begin
        FileUtils.cp(raw_conversion_lib, target_dir)
        puts "✅ RawConversion library copied to #{target_dir}"
      rescue => e
        puts "⚠️  Failed to copy RawConversion library to #{target_dir}: #{e.message}"
      end
    end
  else
    puts "⚠️  RawConversion library not found at: #{raw_conversion_lib}"
    puts "   Please add the PGRawConversionLib.dylib file to lib/native/raw_conversion/macos/"
  end
  
  # Copy RawDecoder library file for macOS
  raw_decoder_lib = File.join(File.dirname(__FILE__), '..', 'lib', 'native', 'raw_decoder', 'macos', 'libPGRawDecoder.dylib')
  
  if File.exist?(raw_decoder_lib)
    # Copy to different build directories
    ['Debug', 'Profile', 'Release'].each do |config|
      target_dir = File.join(File.dirname(__FILE__), 'build', 'macos', 'Build', 'Products', config)
      FileUtils.mkdir_p(target_dir)
      
      begin
        FileUtils.cp(raw_decoder_lib, target_dir)
        puts "✅ RawDecoder library copied to #{target_dir}"
      rescue => e
        puts "⚠️  Failed to copy RawDecoder library to #{target_dir}: #{e.message}"
      end
    end
  else
    puts "⚠️  RawDecoder library not found at: #{raw_decoder_lib}"
    puts "   Please add the libPGRawDecoder.dylib file to lib/native/raw_decoder/macos/"
  end
  
  # Copy ImageProcessor library file for macOS
  image_processor_lib = File.join(File.dirname(__FILE__), '..', 'lib', 'native', 'image_processor', 'macos', 'libPGImageProcessor.dylib')
  
  if File.exist?(image_processor_lib)
    # Copy to different build directories
    ['Debug', 'Profile', 'Release'].each do |config|
      target_dir = File.join(File.dirname(__FILE__), 'build', 'macos', 'Build', 'Products', config)
      FileUtils.mkdir_p(target_dir)
      
      begin
        FileUtils.cp(image_processor_lib, target_dir)
        puts "✅ ImageProcessor library copied to #{target_dir}"
      rescue => e
        puts "⚠️  Failed to copy ImageProcessor library to #{target_dir}: #{e.message}"
      end
    end
  else
    puts "⚠️  ImageProcessor library not found at: #{image_processor_lib}"
    puts "   Please add the libPGImageProcessor.dylib file to lib/native/image_processor/macos/"
  end
end
