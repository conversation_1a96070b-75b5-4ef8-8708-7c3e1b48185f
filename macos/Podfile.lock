PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - desktop_drop (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pg_desktop_multi_window (0.0.1):
    - FlutterMacOS
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.2.4)
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.2):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.49.2):
    - sqlite3/common (= 3.49.2)
  - sqlite3/common (3.49.2)
  - sqlite3/dbstatvtab (3.49.2):
    - sqlite3/common
  - sqlite3/fts5 (3.49.2):
    - sqlite3/common
  - sqlite3/math (3.49.2):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.49.2):
    - sqlite3/common
  - sqlite3/rtree (3.49.2):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.49.2)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/math
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - desktop_drop (from `Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - pg_desktop_multi_window (from `Flutter/ephemeral/.symlinks/plugins/pg_desktop_multi_window/macos`)
  - photo_manager (from `Flutter/ephemeral/.symlinks/plugins/photo_manager/macos`)
  - sentry_flutter (from `Flutter/ephemeral/.symlinks/plugins/sentry_flutter/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/darwin`)
  - sqlite3_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  trunk:
    - ReachabilitySwift
    - Sentry
    - sqlite3

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  desktop_drop:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  pg_desktop_multi_window:
    :path: Flutter/ephemeral/.symlinks/plugins/pg_desktop_multi_window/macos
  photo_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/photo_manager/macos
  sentry_flutter:
    :path: Flutter/ephemeral/.symlinks/plugins/sentry_flutter/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/darwin
  sqlite3_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin

SPEC CHECKSUMS:
  connectivity_plus: e74b9f74717d2d99d45751750e266e55912baeb5
  desktop_drop: e0b672a7d84c0a6cbc378595e82cdb15f2970a43
  device_info_plus: a56e6e74dbbd2bb92f2da12c64ddd4f67a749041
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  package_info_plus: 122abb51244f66eead59ce7c9c200d6b53111779
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  pg_desktop_multi_window: 7dac7c023c965a207e16555eccbbc2abe06adac9
  photo_manager: 1d80ae07a89a67dfbcae95953a1e5a24af7c3e62
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 27892878729f42701297c628eb90e7c6529f3684
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  sqlite3: 3c950dc86011117c307eb0b28c4a7bb449dce9f1
  sqlite3_flutter_libs: 74334e3ef2dbdb7d37e50859bb45da43935779c4
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b

PODFILE CHECKSUM: b9b6115961119d1a49a8bfd25fb9871e33764a4e

COCOAPODS: 1.16.2
