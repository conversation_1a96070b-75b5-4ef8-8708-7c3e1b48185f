stages:
  - lint
  - pg_llms_agent

lint_flutter:
  stage: lint
  before_script:
    - git submodule update --init --recursive pg_flutter_lints_rules
    - git submodule update --remote --recursive pg_flutter_lints_rules
  script:
    - chmod +x ./pg_flutter_lints_rules/lints.sh
    - ./pg_flutter_lints_rules/lints.sh a5WLEQHZy4xBKRmDxzvU https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=71613f3b-4de8-4dde-999d-200aacd2da93
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always

pg_llm_agent_job:
  stage: pg_llms_agent
  image:
    name: pg-llms-review:latest
    entrypoint: [""]
  script:
    - echo "Running PR Agent action step"
    - export MR_URL="$CI_MERGE_REQUEST_PROJECT_URL/merge_requests/$CI_MERGE_REQUEST_IID"
    - export gitlab__PERSONAL_ACCESS_TOKEN=$GITLAB_PERSONAL_ACCESS_TOKEN
    - export deepseek__key=$OPENAI_KEY
    - docker run --rm -e DEEPSEEK.KEY=$OPENAI_KEY -e GITLAB.PERSONAL_ACCESS_TOKEN=$GITLAB_PERSONAL_ACCESS_TOKEN pg-llms-review:latest --pr_url $MR_URL review
    - echo "Running PR Agent action Success"
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'